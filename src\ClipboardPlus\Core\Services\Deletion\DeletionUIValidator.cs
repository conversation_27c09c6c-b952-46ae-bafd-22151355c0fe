using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Implémentation du service de validation pour les opérations de suppression UI.
    /// Centralise toute la logique de validation pour réduire la complexité cyclomatique.
    /// </summary>
    public class DeletionUIValidator : IDeletionUIValidator
    {
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du validateur de suppression UI.
        /// </summary>
        /// <param name="loggingService">Service de logging optionnel</param>
        public DeletionUIValidator(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Valide qu'un élément peut être supprimé depuis l'interface utilisateur.
        /// </summary>
        /// <param name="item">L'élément à valider</param>
        /// <returns>Le résultat de la validation</returns>
        public UIValidationResult ValidateItem(ClipboardItem? item)
        {
            if (item == null)
            {
                _loggingService?.LogWarning("Validation échouée : élément null fourni pour suppression");
                return UIValidationResult.Failure("L'élément à supprimer ne peut pas être null", "NULL_ITEM");
            }

            if (item.Id <= 0)
            {
                _loggingService?.LogWarning($"Validation échouée : ID invalide {item.Id} pour suppression");
                return UIValidationResult.Failure($"ID d'élément invalide : {item.Id}", "INVALID_ID");
            }

            _loggingService?.LogDebug($"Validation réussie pour élément ID={item.Id}");
            return UIValidationResult.Success();
        }

        /// <summary>
        /// Valide que le gestionnaire d'historique est disponible et accessible.
        /// </summary>
        /// <param name="manager">Le gestionnaire à valider</param>
        /// <returns>Le résultat de la validation</returns>
        public UIValidationResult ValidateHistoryManager(IClipboardHistoryManager? manager)
        {
            if (manager == null)
            {
                _loggingService?.LogError("Validation échouée : ClipboardHistoryManager null");
                return UIValidationResult.Failure("Le gestionnaire d'historique n'est pas disponible", "NULL_MANAGER");
            }

            // Test d'accès basique au manager
            try
            {
                // Tentative d'accès à une propriété pour vérifier la disponibilité
                var testAccess = manager.GetType();
                if (testAccess == null)
                {
                    _loggingService?.LogError("Validation échouée : ClipboardHistoryManager inaccessible");
                    return UIValidationResult.Failure("Le gestionnaire d'historique est inaccessible", "INACCESSIBLE_MANAGER");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Validation échouée : erreur d'accès au ClipboardHistoryManager - {ex.Message}", ex);
                return UIValidationResult.Failure($"Erreur d'accès au gestionnaire : {ex.Message}", "MANAGER_ACCESS_ERROR");
            }

            _loggingService?.LogDebug("Validation réussie pour ClipboardHistoryManager");
            return UIValidationResult.Success();
        }

        /// <summary>
        /// Valide qu'aucune opération n'est en cours.
        /// </summary>
        /// <param name="isOperationInProgress">Indique si une opération est en cours</param>
        /// <returns>Le résultat de la validation</returns>
        public UIValidationResult ValidateOperationState(bool isOperationInProgress)
        {
            if (isOperationInProgress)
            {
                _loggingService?.LogInfo("Validation échouée : opération déjà en cours, suppression bloquée");
                return UIValidationResult.Failure("Une opération est déjà en cours", "OPERATION_IN_PROGRESS");
            }

            _loggingService?.LogDebug("Validation réussie : aucune opération en cours");
            return UIValidationResult.Success();
        }
    }
}
