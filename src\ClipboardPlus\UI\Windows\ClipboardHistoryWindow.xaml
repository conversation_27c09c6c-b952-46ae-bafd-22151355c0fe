<Window x:Class="ClipboardPlus.UI.Windows.ClipboardHistoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ClipboardPlus.UI.Windows"
        xmlns:controls="clr-namespace:ClipboardPlus.UI.Controls"
        xmlns:dd="clr-namespace:GongSolutions.Wpf.DragDrop;assembly=GongSolutions.Wpf.DragDrop"
        mc:Ignorable="d"
        Title="Historique du presse-papiers" 
        Height="450" 
        Width="350"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ShowInTaskbar="False"
        ResizeMode="NoResize"
        Loaded="Window_Loaded"
        Deactivated="Window_Deactivated">
    
    <Border Background="#F0F0F0"
            CornerRadius="1"
            BorderBrush="#AAAAAA"
            BorderThickness="1"
            Padding="10"
            Name="MainBorder">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Barre de recherche et boutons -->
            <Grid Grid.Row="0" Margin="0,0,0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Barre de recherche -->
                <Border Grid.Column="0" Background="#F0F0F0" Padding="10,10,10,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <!-- The settings button was moved out of this inner Grid -->
                        </Grid.ColumnDefinitions>

                        <TextBox x:Name="SearchFilterTextBox"
                                 Grid.Column="0"
                                 Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                 Padding="5"
                                 Background="White"
                                 BorderBrush="#CCCCCC"
                                 VerticalContentAlignment="Center"
                                 Margin="0,0,10,0"
                                 Height="30"/>
                    </Grid>
                </Border>

                <!-- Bouton Paramètres -->
                <Button Grid.Column="1"
                        Margin="5,0,0,0"
                        Width="32"
                        Height="32"
                        Style="{StaticResource FadeButtonStyle}"
                        Command="{Binding OpenSettingsCommand}"
                        ToolTip="Ouvrir les paramètres"
                        AutomationProperties.Name="Ouvrir les paramètres">
                    <TextBlock Text="&#xE713;"
                               FontFamily="Segoe MDL2 Assets"
                               FontSize="16"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"/>
                </Button>

                <!-- Bouton Créer Nouveau -->
                <Button Grid.Column="2"
                        Margin="5,0,0,0"
                        Width="32"
                        Height="32"
                        Style="{StaticResource FadeButtonStyle}"
                        Command="{Binding PrepareNewItemCommand}"
                        ToolTip="Créer un nouvel élément"
                        AutomationProperties.Name="Créer un nouvel élément">
                    <TextBlock Text="&#xE710;"
                               FontFamily="Segoe MDL2 Assets"
                               FontSize="16"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"/>
                </Button>
            </Grid>
            
            <!-- Liste des éléments du presse-papiers -->
            <ListBox x:Name="HistoryListBox" 
                     Grid.Row="1"
                     ItemsSource="{Binding HistoryItems}" 
                     SelectedItem="{Binding SelectedClipboardItem, Mode=TwoWay}"
                     Background="Transparent"
                     BorderThickness="0"
                     ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                     VirtualizingStackPanel.IsVirtualizing="True"
                     VirtualizingStackPanel.VirtualizationMode="Recycling"
                     SelectionMode="Single"
                     MouseDoubleClick="HistoryListBox_MouseDoubleClick"
                     HorizontalContentAlignment="Stretch"
                     FocusVisualStyle="{x:Null}"
                     dd:DragDrop.IsDragSource="True"
                     dd:DragDrop.IsDropTarget="True"
                     dd:DragDrop.DropHandler="{Binding}"
                     dd:DragDrop.UseDefaultDragAdorner="True"
                     dd:DragDrop.UseDefaultEffectDataTemplate="False"
                     dd:DragDrop.DragAdornerTemplate="{StaticResource DragAdornerTemplate}">
                <ListBox.Resources>
                    <!-- Override de la couleur du séparateur d'insertion -->
                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="#CCCCCC" Opacity="0.7"/>
                </ListBox.Resources>
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <controls:ClipboardItemControl/>
                    </DataTemplate>
                </ListBox.ItemTemplate>
                <ListBox.ItemContainerStyle>
                    <Style TargetType="ListBoxItem" BasedOn="{StaticResource ClipboardItemStyle}">
                        <!-- Aucune animation -->
                        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
                    </Style>
                </ListBox.ItemContainerStyle>
            </ListBox>
            
            <!-- Indicateur de chargement -->
            <Border Grid.Row="1" 
                    Background="#80FFFFFF" 
                    Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch">
                
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                    <!-- Icône de chargement statique -->
                    <TextBlock Text="&#xE895;" 
                               FontFamily="Segoe MDL2 Assets"
                               FontSize="36" 
                               HorizontalAlignment="Center"
                               Margin="0,0,0,15"
                               Foreground="#3281E6" />
                    
                    <!-- Message de chargement -->
                    <TextBlock Text="Chargement en cours..." 
                               HorizontalAlignment="Center"
                               FontSize="14"
                               Foreground="#333333" />
                </StackPanel>
            </Border>
        </Grid>
    </Border>
    
    <Window.Resources>
        <!-- Pas d'animations ni d'ombres -->
    </Window.Resources>
    
    <Window.Triggers>
        <!-- Pas d'animations au chargement -->
    </Window.Triggers>
</Window> 