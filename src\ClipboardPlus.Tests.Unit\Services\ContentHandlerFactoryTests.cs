using System;
using System.Linq;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using ClipboardPlus.Services.ContentHandlers;

namespace ClipboardPlus.Tests.Unit.Services
{
    /// <summary>
    /// Tests unitaires pour ContentHandlerFactory.
    /// Valide le pattern Factory pour la création des handlers appropriés.
    /// </summary>
    [TestFixture]
    public class ContentHandlerFactoryTests
    {
        private ContentHandlerFactory _factory;
        private IContentHandler[] _mockHandlers;

        [SetUp]
        public void Setup()
        {
            // Créer des handlers réels pour les tests
            _mockHandlers = new IContentHandler[]
            {
                new TextContentHandler(),
                new HtmlContentHandler(),
                new RtfContentHandler()
            };

            _factory = new ContentHandlerFactory(_mockHandlers);
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithNullHandlers_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ContentHandlerFactory(null!));
        }

        [Test]
        public void Constructor_WithValidHandlers_InitializesCorrectly()
        {
            // Act & Assert - Constructor should not throw
            Assert.DoesNotThrow(() => new ContentHandlerFactory(_mockHandlers));
        }

        #endregion

        #region CreateHandler by DataType Tests

        [Test]
        public void CreateHandler_WithTextDataType_ReturnsTextHandler()
        {
            // Act
            var result = _factory.CreateHandler(ClipboardDataType.Text);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsInstanceOf<TextContentHandler>(result);
            Assert.AreEqual(ClipboardDataType.Text, result.SupportedDataType);
        }

        [Test]
        public void CreateHandler_WithHtmlDataType_ReturnsHtmlHandler()
        {
            // Act
            var result = _factory.CreateHandler(ClipboardDataType.Html);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsInstanceOf<HtmlContentHandler>(result);
            Assert.AreEqual(ClipboardDataType.Html, result.SupportedDataType);
        }

        [Test]
        public void CreateHandler_WithRtfDataType_ReturnsRtfHandler()
        {
            // Act
            var result = _factory.CreateHandler(ClipboardDataType.Rtf);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsInstanceOf<RtfContentHandler>(result);
            Assert.AreEqual(ClipboardDataType.Rtf, result.SupportedDataType);
        }

        [Test]
        public void CreateHandler_WithUnsupportedDataType_ReturnsNull()
        {
            // Act
            var result = _factory.CreateHandler(ClipboardDataType.Image); // Pas dans nos handlers de test

            // Assert
            Assert.IsNull(result);
        }

        [Test]
        public void CreateHandler_WithUnknownDataType_ReturnsNull()
        {
            // Act
            var result = _factory.CreateHandler((ClipboardDataType)999);

            // Assert
            Assert.IsNull(result);
        }

        #endregion

        #region CreateHandler by ClipboardItem Tests

        [Test]
        public void CreateHandler_WithTextItem_ReturnsTextHandler()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                TextPreview = "Test text"
            };

            // Act
            var result = _factory.CreateHandler(item);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsInstanceOf<TextContentHandler>(result);
            Assert.IsTrue(result.CanHandle(item));
        }

        [Test]
        public void CreateHandler_WithHtmlItem_ReturnsHtmlHandler()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 2,
                DataType = ClipboardDataType.Html,
                TextPreview = "<html>Test</html>"
            };

            // Act
            var result = _factory.CreateHandler(item);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsInstanceOf<HtmlContentHandler>(result);
            Assert.IsTrue(result.CanHandle(item));
        }

        [Test]
        public void CreateHandler_WithUnsupportedItem_ReturnsNull()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 3,
                DataType = ClipboardDataType.Image,
                TextPreview = "Image preview"
            };

            // Act
            var result = _factory.CreateHandler(item);

            // Assert
            Assert.IsNull(result);
        }

        [Test]
        public void CreateHandler_WithNullItem_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _factory.CreateHandler((ClipboardItem)null!));
        }

        #endregion

        #region GetAllHandlers Tests

        [Test]
        public void GetAllHandlers_ReturnsAllProvidedHandlers()
        {
            // Act
            var result = _factory.GetAllHandlers().ToList();

            // Assert
            Assert.AreEqual(3, result.Count);
            Assert.IsTrue(result.Any(h => h is TextContentHandler));
            Assert.IsTrue(result.Any(h => h is HtmlContentHandler));
            Assert.IsTrue(result.Any(h => h is RtfContentHandler));
        }

        [Test]
        public void GetAllHandlers_ReturnsSameInstancesAsProvided()
        {
            // Act
            var result = _factory.GetAllHandlers().ToArray();

            // Assert
            foreach (var handler in _mockHandlers)
            {
                Assert.Contains(handler, result);
            }
        }

        #endregion

        #region HasHandler Tests

        [Test]
        public void HasHandler_WithSupportedType_ReturnsTrue()
        {
            // Act & Assert
            Assert.IsTrue(_factory.HasHandler(ClipboardDataType.Text));
            Assert.IsTrue(_factory.HasHandler(ClipboardDataType.Html));
            Assert.IsTrue(_factory.HasHandler(ClipboardDataType.Rtf));
        }

        [Test]
        public void HasHandler_WithUnsupportedType_ReturnsFalse()
        {
            // Act & Assert
            Assert.IsFalse(_factory.HasHandler(ClipboardDataType.Image));
            Assert.IsFalse(_factory.HasHandler(ClipboardDataType.FilePath));
        }

        [Test]
        public void HasHandler_WithUnknownType_ReturnsFalse()
        {
            // Act
            bool result = _factory.HasHandler((ClipboardDataType)999);

            // Assert
            Assert.IsFalse(result);
        }

        #endregion

        #region GetSupportedDataTypes Tests

        [Test]
        public void GetSupportedDataTypes_ReturnsCorrectTypes()
        {
            // Act
            var result = _factory.GetSupportedDataTypes().ToList();

            // Assert
            Assert.AreEqual(3, result.Count);
            Assert.Contains(ClipboardDataType.Text, result);
            Assert.Contains(ClipboardDataType.Html, result);
            Assert.Contains(ClipboardDataType.Rtf, result);
        }

        [Test]
        public void GetSupportedDataTypes_DoesNotReturnUnsupportedTypes()
        {
            // Act
            var result = _factory.GetSupportedDataTypes().ToList();

            // Assert
            Assert.IsFalse(result.Contains(ClipboardDataType.Image));
            Assert.IsFalse(result.Contains(ClipboardDataType.FilePath));
        }

        #endregion

        #region Edge Cases Tests

        [Test]
        public void Factory_WithEmptyHandlersList_WorksCorrectly()
        {
            // Arrange
            var emptyFactory = new ContentHandlerFactory(new IContentHandler[0]);

            // Act & Assert
            Assert.IsNull(emptyFactory.CreateHandler(ClipboardDataType.Text));
            Assert.IsFalse(emptyFactory.HasHandler(ClipboardDataType.Text));
            Assert.AreEqual(0, emptyFactory.GetSupportedDataTypes().Count());
            Assert.AreEqual(0, emptyFactory.GetAllHandlers().Count());
        }

        [Test]
        public void Factory_WithDuplicateHandlerTypes_UsesLastHandler()
        {
            // Arrange - Créer deux handlers pour le même type
            var handler1 = new TextContentHandler();
            var handler2 = new TextContentHandler();
            var duplicateHandlers = new IContentHandler[] { handler1, handler2 };
            var duplicateFactory = new ContentHandlerFactory(duplicateHandlers);

            // Act
            var result = duplicateFactory.CreateHandler(ClipboardDataType.Text);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreSame(handler2, result, "Le dernier handler doit être utilisé en cas de doublon");
        }

        [Test]
        public void Factory_CachesHandlersCorrectly()
        {
            // Act - Appeler plusieurs fois pour le même type
            var result1 = _factory.CreateHandler(ClipboardDataType.Text);
            var result2 = _factory.CreateHandler(ClipboardDataType.Text);

            // Assert - Doit retourner la même instance (cache)
            Assert.AreSame(result1, result2, "La factory doit utiliser un cache pour les handlers");
        }

        #endregion
    }
}
