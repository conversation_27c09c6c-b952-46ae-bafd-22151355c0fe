using System;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.WindowDeactivation;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.WindowDeactivation
{
    /// <summary>
    /// Tests unitaires pour WindowVisibilityDecisionService.
    /// </summary>
    [TestFixture]
    public class WindowVisibilityDecisionServiceTests
    {
        private Mock<ILoggingService>? _mockLoggingService;
        private WindowVisibilityDecisionService? _decisionService;
        private WindowVisibilityDecisionConfig? _config;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _config = new WindowVisibilityDecisionConfig
            {
                EnableDetailedLogging = true,
                EnablePerformanceMetrics = false
            };
            _decisionService = new WindowVisibilityDecisionService(_mockLoggingService.Object, _config);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new WindowVisibilityDecisionService(null!));
        }

        [Test]
        public void EvaluateVisibilityDecision_WithNullContext_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _decisionService!.EvaluateVisibilityDecision(null!));
        }

        [Test]
        public void EvaluateVisibilityDecision_WhenTargetWindowStillActive_ShouldReturnKeepVisible()
        {
            // Arrange
            var context = new WindowVisibilityDecisionContext
            {
                TargetWindowName = "TestWindow",
                IsTargetWindowActive = true,
                Config = _config
            };

            // Act
            var result = _decisionService!.EvaluateVisibilityDecision(context);

            // Assert
            Assert.That(result.ShouldHide, Is.False);
            Assert.That(result.DecisionType, Is.EqualTo(WindowVisibilityDecisionType.IgnoredStillActive));
            Assert.That(result.RecommendedAction, Is.EqualTo(WindowVisibilityAction.KeepVisible));
            Assert.That(result.ConfidenceLevel, Is.EqualTo(1.0));
            Assert.That(result.Reason, Is.EqualTo("La fenêtre est toujours considérée comme active"));
        }

        [Test]
        public void EvaluateVisibilityDecision_WhenApplicationWindowActive_ShouldReturnKeepVisible()
        {
            // Arrange
            var classification = new WindowClassificationResult
            {
                IsApplicationWindow = true,
                WindowTypeName = "AppSettingsWindow",
                RelationType = WindowRelationType.SettingsWindow
            };

            var context = new WindowVisibilityDecisionContext
            {
                TargetWindowName = "TestWindow",
                IsTargetWindowActive = false,
                Classification = classification,
                Config = _config
            };

            // Act
            var result = _decisionService!.EvaluateVisibilityDecision(context);

            // Assert
            Assert.That(result.ShouldHide, Is.False);
            Assert.That(result.DecisionType, Is.EqualTo(WindowVisibilityDecisionType.IgnoredApplicationWindow));
            Assert.That(result.RecommendedAction, Is.EqualTo(WindowVisibilityAction.KeepVisible));
            Assert.That(result.ConfidenceLevel, Is.EqualTo(0.95));
            Assert.That(result.Reason, Is.EqualTo("Fenêtre de l'application détectée"));
        }

        [Test]
        public void EvaluateVisibilityDecision_WhenNoActiveWindow_ShouldReturnHide()
        {
            // Arrange
            var diagnostic = new WindowDiagnosticResult
            {
                ActiveWindow = null,
                IsSuccessful = true
            };

            var context = new WindowVisibilityDecisionContext
            {
                TargetWindowName = "TestWindow",
                IsTargetWindowActive = false,
                Diagnostic = diagnostic,
                Config = _config
            };

            // Act
            var result = _decisionService!.EvaluateVisibilityDecision(context);

            // Assert
            Assert.That(result.ShouldHide, Is.True);
            Assert.That(result.DecisionType, Is.EqualTo(WindowVisibilityDecisionType.HideNoActiveWindow));
            Assert.That(result.RecommendedAction, Is.EqualTo(WindowVisibilityAction.HideImmediately));
            Assert.That(result.ConfidenceLevel, Is.EqualTo(0.8));
            Assert.That(result.Reason, Is.EqualTo("Aucune fenêtre active détectée"));
        }

        [Test]
        public void EvaluateVisibilityDecision_WhenExternalWindowActive_ShouldReturnHide()
        {
            // Arrange
            var classification = new WindowClassificationResult
            {
                IsApplicationWindow = false,
                WindowTypeName = "ExternalWindow",
                RelationType = WindowRelationType.External
            };

            var context = new WindowVisibilityDecisionContext
            {
                TargetWindowName = "TestWindow",
                IsTargetWindowActive = false,
                Classification = classification,
                Config = _config
            };

            // Act
            var result = _decisionService!.EvaluateVisibilityDecision(context);

            // Assert
            Assert.That(result.ShouldHide, Is.True);
            Assert.That(result.DecisionType, Is.EqualTo(WindowVisibilityDecisionType.HideExternalWindow));
            Assert.That(result.RecommendedAction, Is.EqualTo(WindowVisibilityAction.HideImmediately));
            Assert.That(result.ConfidenceLevel, Is.EqualTo(0.9));
            Assert.That(result.Reason, Is.EqualTo("Fenêtre externe active"));
        }

        [Test]
        public void EvaluateVisibilityDecision_WithDiagnosticButNoClassification_ShouldReturnHideNoActiveWindow()
        {
            // Arrange - Il y a un diagnostic mais pas de fenêtre active ni de classification
            var diagnostic = new WindowDiagnosticResult
            {
                ActiveWindow = null, // Pas de fenêtre active
                IsSuccessful = true,
                AllWindows = new List<WindowInfo>
                {
                    new WindowInfo { IsActive = false, WindowTypeName = "SomeWindow" }
                }.AsReadOnly()
            };

            var context = new WindowVisibilityDecisionContext
            {
                TargetWindowName = "TestWindow",
                IsTargetWindowActive = false,
                Diagnostic = diagnostic,
                Config = _config
                // Pas de classification
            };

            // Act
            var result = _decisionService!.EvaluateVisibilityDecision(context);

            // Assert - Sans fenêtre active, on devrait avoir HideNoActiveWindow
            Assert.That(result.ShouldHide, Is.True);
            Assert.That(result.DecisionType, Is.EqualTo(WindowVisibilityDecisionType.HideNoActiveWindow));
        }

        [Test]
        public void EvaluateVisibilityDecision_WithNoActiveWindow_ShouldReturnHideNoActiveWindow()
        {
            // Arrange
            var context = new WindowVisibilityDecisionContext
            {
                TargetWindowName = "TestWindow",
                IsTargetWindowActive = false,
                Config = _config
                // Pas de diagnostic ni de classification - donc aucune fenêtre active détectée
            };

            // Act
            var result = _decisionService!.EvaluateVisibilityDecision(context);

            // Assert
            Assert.That(result.ShouldHide, Is.True);
            Assert.That(result.DecisionType, Is.EqualTo(WindowVisibilityDecisionType.HideNoActiveWindow));
            Assert.That(result.RecommendedAction, Is.EqualTo(WindowVisibilityAction.HideImmediately));
            Assert.That(result.ConfidenceLevel, Is.EqualTo(0.8));
            Assert.That(result.Reason, Is.EqualTo("Aucune fenêtre active détectée"));
        }

        [Test]
        public void EvaluateVisibilityDecision_ShouldSetTimestamp()
        {
            // Arrange
            var context = new WindowVisibilityDecisionContext
            {
                TargetWindowName = "TestWindow",
                Config = _config
            };

            var beforeEvaluation = DateTime.Now;

            // Act
            var result = _decisionService!.EvaluateVisibilityDecision(context);

            // Assert
            Assert.That(result.Timestamp, Is.GreaterThanOrEqualTo(beforeEvaluation));
            Assert.That(result.Timestamp, Is.LessThanOrEqualTo(DateTime.Now.AddSeconds(1)));
        }

        [Test]
        public void EvaluateVisibilityDecision_ShouldMeasurePerformance()
        {
            // Arrange
            var context = new WindowVisibilityDecisionContext
            {
                TargetWindowName = "TestWindow",
                Config = _config
            };

            // Act
            var result = _decisionService!.EvaluateVisibilityDecision(context);

            // Assert
            Assert.That(result.EvaluationDurationMs, Is.GreaterThanOrEqualTo(0));
            Assert.That(result.EvaluationDurationMs, Is.LessThan(1000)); // Moins d'une seconde
        }

        [Test]
        public void EvaluateVisibilityDecision_WithActiveWindowPreventsHidingDisabled_ShouldIgnoreActiveState()
        {
            // Arrange
            var configWithDisabledRule = new WindowVisibilityDecisionConfig
            {
                ActiveWindowPreventsHiding = false,
                ApplicationWindowsPreventsHiding = false
            };

            var context = new WindowVisibilityDecisionContext
            {
                TargetWindowName = "TestWindow",
                IsTargetWindowActive = true, // Normalement empêcherait le masquage
                Config = configWithDisabledRule
                // Pas de diagnostic - donc aucune fenêtre active détectée
            };

            // Act
            var result = _decisionService!.EvaluateVisibilityDecision(context);

            // Assert
            // Avec la règle désactivée et pas de diagnostic, on devrait tomber dans HideNoActiveWindow
            Assert.That(result.DecisionType, Is.EqualTo(WindowVisibilityDecisionType.HideNoActiveWindow));
        }

        [Test]
        public void ConfigureDecisionRules_WithNullConfig_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _decisionService!.ConfigureDecisionRules(null!));
        }

        [Test]
        public void ConfigureDecisionRules_ShouldUpdateConfiguration()
        {
            // Arrange
            var newConfig = new WindowVisibilityDecisionConfig
            {
                ActiveWindowPreventsHiding = false,
                ApplicationWindowsPreventsHiding = false
            };

            // Act
            _decisionService!.ConfigureDecisionRules(newConfig);

            // Verify the configuration was applied by testing behavior
            var context = new WindowVisibilityDecisionContext
            {
                TargetWindowName = "TestWindow",
                IsTargetWindowActive = true
            };

            var result = _decisionService.EvaluateVisibilityDecision(context);

            // Assert
            // Avec la nouvelle configuration, la fenêtre active ne devrait plus empêcher le masquage
            Assert.That(result.DecisionType, Is.Not.EqualTo(WindowVisibilityDecisionType.IgnoredStillActive));
        }
    }
}
