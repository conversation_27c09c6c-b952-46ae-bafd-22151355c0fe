using System;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;
using WpfApplication = System.Windows.Application;
using WpfMessageBox = System.Windows.MessageBox;
using MessageBox = System.Windows.MessageBox;

namespace ClipboardPlus.UI.Windows
{
    /// <summary>
    /// Fenêtre de nettoyage avancé de l'historique du presse-papiers
    /// Permet de supprimer les éléments non épinglés selon des critères temporels
    /// </summary>
    public partial class AdvancedCleanupWindow : Window
    {
        #region Champs privés

        private readonly AdvancedCleanupViewModel _viewModel;
        private readonly ILoggingService? _loggingService;

        #endregion

        #region Propriétés publiques

        /// <summary>
        /// Obtient le ViewModel de la fenêtre de nettoyage avancé
        /// </summary>
        public AdvancedCleanupViewModel ViewModel => _viewModel;

        #endregion

        #region Constructeur

        /// <summary>
        /// Initialise une nouvelle instance de la fenêtre de nettoyage avancé
        /// </summary>
        /// <param name="historyManager">Service de gestion de l'historique</param>
        /// <param name="loggingService">Service de logging (peut être null)</param>
        public AdvancedCleanupWindow(IClipboardHistoryManager historyManager, ILoggingService? loggingService)
        {
            InitializeComponent();

            _loggingService = loggingService;
            _loggingService?.LogInfo("AdvancedCleanupWindow: DÉBUT du constructeur");

            // Créer et configurer le ViewModel
            _viewModel = new AdvancedCleanupViewModel(historyManager, loggingService);
            DataContext = _viewModel;

            // S'abonner aux événements du ViewModel
            _loggingService?.LogInfo("AdvancedCleanupWindow: Abonnement aux événements du ViewModel");
            _viewModel.OnCleanupCompleted += OnCleanupCompleted;
            _viewModel.OnCleanupError += OnCleanupError;
            _viewModel.OnCancelled += OnCancelled;
            _loggingService?.LogInfo("AdvancedCleanupWindow: Événements abonnés avec succès");

            // Configurer la fenêtre
            // Note: L'Owner sera défini par l'appelant pour maintenir la fenêtre d'historique active

            _loggingService?.LogInfo("AdvancedCleanupWindow: FIN du constructeur - fenêtre initialisée");
        }

        #endregion

        #region Gestionnaires d'événements du ViewModel

        /// <summary>
        /// Appelé quand le nettoyage est terminé avec succès
        /// </summary>
        /// <param name="deletedCount">Nombre d'éléments supprimés</param>
        private void OnCleanupCompleted(int deletedCount)
        {
            try
            {
                _loggingService?.LogInfo($"OnCleanupCompleted: DÉBUT - deletedCount={deletedCount}");

                string message;
                if (deletedCount == 0)
                {
                    message = "Aucun élément n'a été supprimé.";
                }
                else if (deletedCount == 1)
                {
                    message = "1 élément a été supprimé avec succès.";
                }
                else
                {
                    message = $"{deletedCount} éléments ont été supprimés avec succès.";
                }

                _loggingService?.LogInfo($"OnCleanupCompleted: Message préparé - '{message}'");
                _loggingService?.LogInfo($"OnCleanupCompleted: Tentative d'affichage de la MessageBox");

                // Utiliser WpfMessageBox avec Owner pour maintenir la fenêtre d'historique active
                WpfMessageBox.Show(this, message,
                              "Nettoyage Terminé",
                              MessageBoxButton.OK,
                              MessageBoxImage.Information);

                _loggingService?.LogInfo($"OnCleanupCompleted: MessageBox fermée, fermeture de la fenêtre");

                // Fermer la fenêtre avec succès (DialogResult = true)
                this.DialogResult = true;
                Close();

                _loggingService?.LogInfo($"OnCleanupCompleted: FIN - fenêtre fermée");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"OnCleanupCompleted: ERREUR - {ex.Message}", ex);
                OnCleanupError($"Erreur lors de l'affichage du résultat: {ex.Message}");
            }
        }

        /// <summary>
        /// Appelé en cas d'erreur pendant le nettoyage
        /// </summary>
        /// <param name="errorMessage">Message d'erreur</param>
        private void OnCleanupError(string errorMessage)
        {
            try
            {
                WpfMessageBox.Show(this, $"Erreur lors du nettoyage: {errorMessage}",
                              "Erreur",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                // En dernier recours, afficher une erreur générique
                WpfMessageBox.Show(this, $"Une erreur critique s'est produite: {ex.Message}",
                              "Erreur Critique",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Appelé quand l'utilisateur annule l'opération
        /// </summary>
        private void OnCancelled()
        {
            try
            {
                // Fermer la fenêtre avec annulation (DialogResult = false)
                this.DialogResult = false;
                Close();
            }
            catch (Exception)
            {
                // Forcer la fermeture en cas d'erreur
                try
                {
                    this.DialogResult = false;
                    Close();
                }
                catch
                {
                    // Ignorer les erreurs de fermeture
                }
            }
        }

        #endregion

        #region Gestionnaires d'événements de la fenêtre

        /// <summary>
        /// Appelé quand la fenêtre se ferme
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // Se désabonner des événements pour éviter les fuites mémoire
                if (_viewModel != null)
                {
                    _viewModel.OnCleanupCompleted -= OnCleanupCompleted;
                    _viewModel.OnCleanupError -= OnCleanupError;
                    _viewModel.OnCancelled -= OnCancelled;
                }
            }
            catch (Exception ex)
            {
                // Ignorer les erreurs de nettoyage
                System.Diagnostics.Debug.WriteLine($"Erreur lors du nettoyage de la fenêtre: {ex.Message}");
            }
            finally
            {
                base.OnClosed(e);
            }
        }

        /// <summary>
        /// Appelé quand la fenêtre est chargée
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Donner le focus au champ de saisie numérique
                TimeValueTextBox?.Focus();
                TimeValueTextBox?.SelectAll();
            }
            catch (Exception ex)
            {
                // Ignorer les erreurs de focus
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la définition du focus: {ex.Message}");
            }
        }

        #endregion
    }
}
