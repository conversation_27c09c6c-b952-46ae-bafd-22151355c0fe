using System;
using System.Threading;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.Core
{
    public class ApplicationLifetimeManager : IApplicationLifetimeManager
    {
        private int _isProcessingClipboard = 0;
        private readonly ILoggingService? _loggingService;
        private readonly IClipboardProcessorService? _clipboardProcessorService;
        private readonly IServiceProvider? _serviceProvider;
        private IClipboardListenerService? _clipboardListenerService;

        public ApplicationLifetimeManager(IServiceProvider? serviceProvider, ILoggingService? loggingService, IClipboardProcessorService? clipboardProcessorService)
        {
            _serviceProvider = serviceProvider;
            _loggingService = loggingService;
            _clipboardProcessorService = clipboardProcessorService;
        }

        public async Task<ISystemTrayService?> InitializeServices(IServiceProvider services, EventHandler shortcutHandler, EventHandler clipboardHandler)
        {
            _loggingService?.LogInfo("InitializeServices: Début de l'initialisation des services");
            
            try
            {
                // Récupérer les services nécessaires
                var systemTrayService = services.GetService<ISystemTrayService>();
                _clipboardListenerService = services.GetService<IClipboardListenerService>();

                // ClipboardMonitorService supprimé - remplacé par ClipboardListenerService uniquement

                // Démarrer le service d'écoute du presse-papiers
                if (_clipboardListenerService != null)
                {
                    _loggingService?.LogInfo("InitializeServices: Démarrage du ClipboardListenerService");
                    bool listenerStarted = _clipboardListenerService.StartListening();
                    
                    if (listenerStarted)
                    {
                        _loggingService?.LogInfo("InitializeServices: ClipboardListenerService démarré avec succès");
                        // S'abonner aux événements du nouveau service d'écoute
                        _clipboardListenerService.ClipboardContentChanged += ProcessClipboardContentEventAsync;
                    }
                    else
                    {
                        _loggingService?.LogError("InitializeServices: Échec du démarrage de ClipboardListenerService");
                    }
                }
                else
                {
                    _loggingService?.LogWarning("InitializeServices: ClipboardListenerService n'est pas disponible");
                }

                // Configurer le service de la barre d'état système
                if (systemTrayService != null)
                {
                    _loggingService?.LogInfo("InitializeServices: Initialisation du SystemTrayService");
                    systemTrayService.Initialize();
                }
                else
                {
                    _loggingService?.LogWarning("InitializeServices: SystemTrayService n'est pas disponible");
                }

                // Une opération asynchrone simple pour éviter l'avertissement CS1998
                await Task.Delay(1);

                _loggingService?.LogInfo("InitializeServices: Services initialisés avec succès");
                return systemTrayService;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"InitializeServices: Erreur lors de l'initialisation des services: {ex.Message}", ex);
                return null;
            }
        }

        public void Shutdown(IServiceProvider? services, ISystemTrayService? systemTrayService, Mutex? appMutex, bool ownsMutex)
        {
            _loggingService?.LogInfo("Shutdown: Début de la fermeture de l'application");
            
            try
            {
                // Se désabonner des événements du presse-papiers (ClipboardMonitorService supprimé)

                // Arrêter le service d'écoute du presse-papiers
                if (_clipboardListenerService != null)
                {
                    _loggingService?.LogInfo("Shutdown: Arrêt du ClipboardListenerService");
                    _clipboardListenerService.ClipboardContentChanged -= ProcessClipboardContentEventAsync;
                    _clipboardListenerService.StopListening();
                }

                // Libérer les ressources du service de la barre d'état système
                if (systemTrayService != null)
                {
                    _loggingService?.LogInfo("Shutdown: Libération des ressources du SystemTrayService");
                    systemTrayService.Dispose();
                }

                // Libérer le mutex si nécessaire
                if (appMutex != null && ownsMutex)
                {
                    _loggingService?.LogInfo("Shutdown: Libération du mutex de l'application");
                    appMutex.ReleaseMutex();
                    appMutex.Dispose();
                }

                _loggingService?.LogInfo("Shutdown: Fermeture de l'application terminée avec succès");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Shutdown: Erreur lors de la fermeture de l'application: {ex.Message}", ex);
            }
        }

        public async Task ProcessClipboardContentAsync()
        {
            // Utiliser un verrou pour éviter le traitement simultané du presse-papiers
            if (Interlocked.Exchange(ref _isProcessingClipboard, 1) == 1)
            {
                _loggingService?.LogInfo("Traitement du presse-papiers déjà en cours, ignoré");
                return;
            }

            try
            {
                _loggingService?.LogInfo("Traitement du contenu du presse-papiers...");

                // Vérifier si le service de traitement est disponible
                if (_clipboardProcessorService == null)
                {
                    _loggingService?.LogError("Le service de traitement du presse-papiers n'est pas disponible");
                    return;
                }

                // Traiter le contenu du presse-papiers
                await _clipboardProcessorService.ProcessCurrentClipboardContentAsync();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors du traitement du contenu du presse-papiers: {ex.Message}", ex);
            }
            finally
            {
                // Libérer le verrou
                Interlocked.Exchange(ref _isProcessingClipboard, 0);
            }
        }

        /// <summary>
        /// Traite le contenu du presse-papiers de manière asynchrone en réponse à un événement.
        /// </summary>
        /// <param name="sender">L'objet qui a déclenché l'événement.</param>
        /// <param name="e">Les arguments de l'événement.</param>
        private async void ProcessClipboardContentEventAsync(object? sender, EventArgs e)
        {
            _loggingService?.LogInfo($"Événement de changement du presse-papiers reçu de {sender?.GetType().Name ?? "source inconnue"}");
            await ProcessClipboardContentAsync();
        }
    }
} 