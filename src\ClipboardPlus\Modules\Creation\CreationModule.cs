using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Modules.Core;
using ClipboardPlus.Modules.Core.Events;
using Prism.Events;

namespace ClipboardPlus.Modules.Creation
{
    /// <summary>
    /// Implémentation du module de création de nouveaux éléments.
    /// 
    /// Ce module encapsule toute la logique de création de nouveaux éléments,
    /// incluant la validation, la préparation et la finalisation.
    /// </summary>
    public class CreationModule : ModuleBase, ICreationModule
    {
        private readonly IClipboardHistoryManager _historyManager;
        private readonly ILoggingService _loggingService;
        private readonly IEventAggregator _eventAggregator;

        private string? _newItemContent;
        private bool _isCreatingNewItem;
        private CreationState _currentState;
        private CreationMetadata? _creationMetadata;
        private readonly CreationStatistics _statistics;
        private DateTime _creationStartTime;

        /// <inheritdoc />
        public override string ModuleName => "CreationModule";

        /// <inheritdoc />
        public override Version ModuleVersion => new Version(1, 0, 0);

        /// <inheritdoc />
        public string? NewItemContent
        {
            get => _newItemContent;
            set
            {
                var previousContent = _newItemContent;
                _newItemContent = value;
                
                // Valider automatiquement le nouveau contenu
                var isValid = !string.IsNullOrWhiteSpace(value);
                OnContentChanged(new CreationContentChangedEventArgs(previousContent, value, isValid));
                
                // Mettre à jour l'état si nécessaire
                if (_currentState == CreationState.EditingContent && isValid)
                {
                    ChangeState(CreationState.ReadyToFinalize, "Content is valid");
                }
                else if (_currentState == CreationState.ReadyToFinalize && !isValid)
                {
                    ChangeState(CreationState.EditingContent, "Content is invalid");
                }
            }
        }

        /// <inheritdoc />
        public bool IsCreatingNewItem => _isCreatingNewItem;

        /// <inheritdoc />
        public bool IsContentValid => !string.IsNullOrWhiteSpace(_newItemContent);

        /// <inheritdoc />
        public CreationState CurrentState => _currentState;

        /// <inheritdoc />
        public CreationMetadata? CreationMetadata => _creationMetadata;

        /// <inheritdoc />
        public new event EventHandler<CreationStateChangedEventArgs>? StateChanged;

        /// <inheritdoc />
        public event EventHandler<CreationContentChangedEventArgs>? ContentChanged;

        /// <inheritdoc />
        public event EventHandler<ItemCreatedEventArgs>? ItemCreated;

        /// <inheritdoc />
        public event EventHandler<CreationCancelledEventArgs>? CreationCancelled;

        public CreationModule(
            IClipboardHistoryManager historyManager,
            ILoggingService loggingService,
            IEventAggregator eventAggregator)
        {
            _historyManager = historyManager ?? throw new ArgumentNullException(nameof(historyManager));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));

            _currentState = CreationState.Idle;
            _statistics = new CreationStatistics();
        }

        /// <inheritdoc />
        protected override Task OnInitializeAsync()
        {
            _loggingService.LogInfo($"Initializing {ModuleName}...");
            _loggingService.LogInfo($"{ModuleName} initialized successfully");
            return Task.CompletedTask;
        }

        /// <inheritdoc />
        protected override Task OnStartAsync()
        {
            _loggingService.LogInfo($"Starting {ModuleName}...");
            
            // Publier un événement de démarrage
            _eventAggregator.GetEvent<ModuleStateChangedPrismEvent>().Publish(
                new ModuleStateChangedEvent(ModuleName, ClipboardPlus.Modules.Core.ModuleState.Starting, ClipboardPlus.Modules.Core.ModuleState.Running));
            
            return Task.CompletedTask;
        }

        /// <inheritdoc />
        protected override Task OnStopAsync()
        {
            _loggingService.LogInfo($"Stopping {ModuleName}...");
            
            // Annuler toute création en cours
            if (_isCreatingNewItem)
            {
                _ = CancelCreationAsync("Module stopping");
            }
            
            // Publier un événement d'arrêt
            _eventAggregator.GetEvent<ModuleStateChangedPrismEvent>().Publish(
                new ModuleStateChangedEvent(ModuleName, ClipboardPlus.Modules.Core.ModuleState.Running, ClipboardPlus.Modules.Core.ModuleState.Stopping));
            
            return Task.CompletedTask;
        }

        /// <inheritdoc />
        protected override void OnDispose()
        {
            // Nettoyer les ressources
            _newItemContent = null;
            _creationMetadata = null;
            _isCreatingNewItem = false;
            _currentState = CreationState.Idle;
            
            _loggingService.LogInfo($"{ModuleName} disposed");
        }

        /// <inheritdoc />
        public async Task StartCreationAsync(string? initialContent = null)
        {
            if (_isCreatingNewItem)
            {
                throw new InvalidOperationException("Creation is already in progress");
            }

            try
            {
                _statistics.TotalAttempts++;
                _creationStartTime = DateTime.UtcNow;
                _isCreatingNewItem = true;

                ChangeState(CreationState.Initialized, "Creation started");

                _creationMetadata = new CreationMetadata
                {
                    StartTime = _creationStartTime,
                    Source = "Manual",
                    ContentLength = initialContent?.Length ?? 0,
                    ContentType = "Text" // Pour l'instant, on ne gère que le texte
                };

                if (!string.IsNullOrEmpty(initialContent))
                {
                    NewItemContent = initialContent;
                    ChangeState(CreationState.EditingContent, "Initial content provided");
                }
                else
                {
                    ChangeState(CreationState.EditingContent, "Waiting for content");
                }

                _loggingService.LogInfo("Creation started successfully");
            }
            catch (Exception ex)
            {
                _statistics.FailedCreations++;
                ChangeState(CreationState.Error, $"Failed to start creation: {ex.Message}");
                _loggingService.LogError("Failed to start creation", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task UpdateContentAsync(string? content)
        {
            if (!_isCreatingNewItem)
            {
                throw new InvalidOperationException("No creation in progress");
            }

            try
            {
                NewItemContent = content;
                
                if (_creationMetadata != null)
                {
                    _creationMetadata.ContentLength = content?.Length ?? 0;
                }

                _loggingService.LogInfo($"Content updated: {content?.Length ?? 0} characters");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Failed to update content", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<CreationValidationResult> ValidateContentAsync()
        {
            if (!_isCreatingNewItem)
            {
                return CreationValidationResult.Failure("No creation in progress");
            }

            try
            {
                ChangeState(CreationState.Validating, "Validating content");

                var errors = new List<string>();
                var warnings = new List<string>();

                if (string.IsNullOrWhiteSpace(_newItemContent))
                {
                    errors.Add("Content cannot be empty");
                }
                else
                {
                    if (_newItemContent.Length > 10000) // Limite arbitraire
                    {
                        warnings.Add("Content is very long and may impact performance");
                    }

                    if (_newItemContent.Trim().Length != _newItemContent.Length)
                    {
                        warnings.Add("Content has leading or trailing whitespace");
                    }
                }

                var isValid = errors.Count == 0;
                var result = new CreationValidationResult(isValid, null, errors, warnings);

                if (isValid)
                {
                    ChangeState(CreationState.ReadyToFinalize, "Content is valid");
                }
                else
                {
                    ChangeState(CreationState.EditingContent, "Content validation failed");
                }

                return result;
            }
            catch (Exception ex)
            {
                ChangeState(CreationState.Error, $"Validation failed: {ex.Message}");
                _loggingService.LogError("Failed to validate content", ex);
                return CreationValidationResult.Failure($"Validation error: {ex.Message}");
            }
        }

        /// <inheritdoc />
        public async Task<ClipboardItem> FinalizeAndSaveAsync()
        {
            if (!CanFinalize())
            {
                throw new InvalidOperationException("Cannot finalize creation in current state");
            }

            try
            {
                ChangeState(CreationState.Finalizing, "Finalizing creation");

                var newItem = new ClipboardItem
                {
                    DataType = ClipboardDataType.Text,
                    Timestamp = DateTime.Now,
                    RawData = System.Text.Encoding.UTF8.GetBytes(_newItemContent!),
                    TextPreview = _newItemContent!.Length > 100 
                        ? _newItemContent.Substring(0, 100) + "..." 
                        : _newItemContent,
                    CustomName = _newItemContent!.Length > 50 
                        ? _newItemContent.Substring(0, 50) + "..." 
                        : _newItemContent
                };

                await _historyManager.AddItemAsync(newItem);

                var creationTime = DateTime.UtcNow - _creationStartTime;
                _statistics.SuccessfulCreations++;
                _statistics.LastCreationTime = DateTime.UtcNow;
                UpdateAverageCreationTime(creationTime);

                ChangeState(CreationState.Completed, "Creation completed successfully");

                OnItemCreated(new ItemCreatedEventArgs(newItem, _creationMetadata!, creationTime));

                // Réinitialiser pour la prochaine création
                await ResetCreationAsync();

                _loggingService.LogInfo($"Item created and saved successfully: {newItem.Id}");
                return newItem;
            }
            catch (Exception ex)
            {
                _statistics.FailedCreations++;
                ChangeState(CreationState.Error, $"Failed to finalize: {ex.Message}");
                _loggingService.LogError("Failed to finalize and save item", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task CancelCreationAsync(string? reason = null)
        {
            if (!CanCancel())
            {
                return; // Rien à annuler
            }

            try
            {
                ChangeState(CreationState.Cancelling, $"Cancelling creation: {reason}");

                var elapsedTime = DateTime.UtcNow - _creationStartTime;
                _statistics.CancelledCreations++;

                OnCreationCancelled(new CreationCancelledEventArgs(reason, _newItemContent, elapsedTime));

                await ResetCreationAsync();

                _loggingService.LogInfo($"Creation cancelled: {reason}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Failed to cancel creation: {reason}", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task ResetCreationAsync()
        {
            _isCreatingNewItem = false;
            _newItemContent = null;
            _creationMetadata = null;
            ChangeState(CreationState.Idle, "Creation reset");
        }

        /// <inheritdoc />
        public bool CanFinalize()
        {
            return _isCreatingNewItem && 
                   _currentState == CreationState.ReadyToFinalize && 
                   IsContentValid;
        }

        /// <inheritdoc />
        public bool CanCancel()
        {
            return _isCreatingNewItem && 
                   _currentState != CreationState.Completed && 
                   _currentState != CreationState.Cancelled;
        }

        /// <inheritdoc />
        public CreationStatistics GetStatistics()
        {
            return _statistics;
        }

        private void ChangeState(CreationState newState, string? reason = null)
        {
            var previousState = _currentState;
            _currentState = newState;
            OnStateChanged(new CreationStateChangedEventArgs(previousState, newState, reason));
        }

        private void UpdateAverageCreationTime(TimeSpan creationTime)
        {
            if (_statistics.SuccessfulCreations == 1)
            {
                _statistics.AverageCreationTime = creationTime;
            }
            else
            {
                var totalTicks = _statistics.AverageCreationTime.Ticks * (_statistics.SuccessfulCreations - 1) + creationTime.Ticks;
                _statistics.AverageCreationTime = new TimeSpan(totalTicks / _statistics.SuccessfulCreations);
            }
        }

        protected virtual void OnStateChanged(CreationStateChangedEventArgs e)
        {
            StateChanged?.Invoke(this, e);
            
            // Publier aussi sur le bus d'événements
            _eventAggregator.GetEvent<CreationModulePrismEvent>().Publish(new CreationModuleEventData
            {
                ModuleName = ModuleName,
                CurrentState = e.NewState,
                PreviousState = e.PreviousState,
                Reason = e.Reason
            });
        }

        protected virtual void OnContentChanged(CreationContentChangedEventArgs e)
        {
            ContentChanged?.Invoke(this, e);
        }

        protected virtual void OnItemCreated(ItemCreatedEventArgs e)
        {
            ItemCreated?.Invoke(this, e);
        }

        protected virtual void OnCreationCancelled(CreationCancelledEventArgs e)
        {
            CreationCancelled?.Invoke(this, e);
        }
    }

    /// <summary>
    /// Événement spécifique au module de création.
    /// </summary>
    public class CreationModuleEvent : ModuleEventBase
    {
        public CreationState CurrentState { get; }
        public CreationState PreviousState { get; }
        public string? Reason { get; }

        public CreationModuleEvent(string sourceModule, CreationState currentState, 
            CreationState previousState, string? reason = null)
            : base(sourceModule)
        {
            CurrentState = currentState;
            PreviousState = previousState;
            Reason = reason;
        }
    }
}
