using System;
using System.Windows;
using System.Windows.Interop;
using ClipboardPlus.Core.Services.Shortcuts.Interfaces;
using WpfVisibility = System.Windows.Visibility;

namespace ClipboardPlus.Core.Services.Shortcuts.Implementations
{
    /// <summary>
    /// Implémentation de production pour IWindowFactory.
    /// Crée et gère les fenêtres pour l'enregistrement de raccourcis.
    /// </summary>
    public class WindowFactory : IWindowFactory
    {
        /// <inheritdoc />
        public Window CreateHiddenWindow()
        {
            return new Window
            {
                Title = "ClipboardPlus Hidden Window",
                Width = 1,
                Height = 1,
                Left = -10000,
                Top = -10000,
                WindowStyle = WindowStyle.None,
                ShowInTaskbar = false,
                Visibility = WpfVisibility.Hidden,
                AllowsTransparency = true,
                Background = System.Windows.Media.Brushes.Transparent
            };
        }

        /// <inheritdoc />
        public IntPtr GetWindowHandle(Window window)
        {
            if (window == null)
            {
                return IntPtr.Zero;
            }

            try
            {
                var helper = new WindowInteropHelper(window);
                return helper.Handle;
            }
            catch
            {
                return IntPtr.Zero;
            }
        }

        /// <inheritdoc />
        public void ShowAndHideWindow(Window window)
        {
            if (window == null)
            {
                return;
            }

            try
            {
                // Afficher la fenêtre pour qu'elle obtienne un handle
                window.Show();
                
                // La cacher immédiatement
                window.Hide();
            }
            catch
            {
                // Ignorer les erreurs d'affichage/masquage
            }
        }
    }
}
