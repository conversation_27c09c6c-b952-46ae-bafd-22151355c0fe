// ============================================================================
// HISTORY VIEWMODEL MANAGER IMPLEMENTATION - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Implémentation concrète de la gestion de l'historique
// 📋 RESPONSABILITÉ : Délégation pure vers HistoryModule existant
// 🏗️ ARCHITECTURE : Réutilisation complète de l'infrastructure existante
//
// ============================================================================

using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Modules.History;
using ClipboardPlus.UI.ViewModels.Managers.Interfaces;
using CommunityToolkit.Mvvm.ComponentModel;

namespace ClipboardPlus.UI.ViewModels.Managers.Implementations
{
    /// <summary>
    /// Implémentation concrète du manager de gestion de l'historique.
    /// 
    /// Cette classe délègue toutes les opérations vers le HistoryModule existant
    /// et maintient la synchronisation avec les collections observables pour l'UI.
    /// </summary>
    public class HistoryViewModelManager : ObservableObject, IHistoryViewModelManager
    {
        #region Champs Privés

        private readonly IHistoryModule _historyModule;
        private readonly ClipboardPlus.Core.Services.ILoggingService? _loggingService;
        private readonly Dispatcher _uiDispatcher;
        private ClipboardItem? _selectedClipboardItem;
        private string? _searchText;
        private bool _isLoading;
        private bool _isDisposed;

        #endregion

        #region Constructeur

        /// <summary>
        /// Initialise une nouvelle instance du HistoryViewModelManager.
        /// </summary>
        /// <param name="historyModule">Module d'historique à utiliser</param>
        /// <param name="loggingService">Service de logging pour diagnostic</param>
        public HistoryViewModelManager(IHistoryModule historyModule, ClipboardPlus.Core.Services.ILoggingService? loggingService = null)
        {
            _historyModule = historyModule ?? throw new ArgumentNullException(nameof(historyModule));
            _loggingService = loggingService; // ✅ CORRECTION CRITIQUE !

            // 🔧 CORRECTION CRITIQUE : Capturer le dispatcher UI thread
            _uiDispatcher = Dispatcher.CurrentDispatcher;

            _loggingService?.LogInfo("🏗️ [HISTORY_MANAGER] Constructeur - Initialisation");

            // Initialiser les collections observables
            HistoryItems = new ObservableCollection<ClipboardItem>();

            // S'abonner aux événements du module
            SubscribeToModuleEvents();

            _loggingService?.LogInfo("🏗️ [HISTORY_MANAGER] Constructeur terminé - Collections et événements initialisés");
        }

        #endregion

        #region Propriétés Observables (IHistoryViewModelManager)

        /// <summary>
        /// Collection observable des éléments d'historique affichés dans l'UI.
        /// </summary>
        public ObservableCollection<ClipboardItem> HistoryItems { get; }

        /// <summary>
        /// Élément actuellement sélectionné dans l'interface utilisateur.
        /// </summary>
        public ClipboardItem? SelectedClipboardItem
        {
            get => _selectedClipboardItem;
            set
            {
                if (SetProperty(ref _selectedClipboardItem, value))
                {
                    SelectionChanged?.Invoke(this, value);
                }
            }
        }

        /// <summary>
        /// Texte de recherche/filtrage actuel.
        /// </summary>
        public string? SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    SearchFilterChanged?.Invoke(this, value);
                    // Appliquer le filtre automatiquement
                    ApplySearchFilter(value);
                }
            }
        }

        /// <summary>
        /// Indique si une opération de chargement est en cours.
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            private set
            {
                if (SetProperty(ref _isLoading, value))
                {
                    LoadingStateChanged?.Invoke(this, value);
                }
            }
        }

        /// <summary>
        /// Nombre total d'éléments dans l'historique (avant filtrage).
        /// </summary>
        public int TotalItemCount => _historyModule.TotalItemCount;

        /// <summary>
        /// Nombre d'éléments visibles après filtrage.
        /// </summary>
        public int FilteredItemCount => _historyModule.FilteredItemCount;

        #endregion

        #region Événements (IHistoryViewModelManager)

        /// <summary>
        /// Événement déclenché lorsque l'historique change.
        /// </summary>
        public event EventHandler? HistoryChanged;

        /// <summary>
        /// Événement déclenché lorsque la sélection change.
        /// </summary>
        public event EventHandler<ClipboardItem?>? SelectionChanged;

        /// <summary>
        /// Événement déclenché lorsque le filtre de recherche change.
        /// </summary>
        public event EventHandler<string?>? SearchFilterChanged;

        /// <summary>
        /// Événement déclenché lorsque l'état de chargement change.
        /// </summary>
        public event EventHandler<bool>? LoadingStateChanged;

        #endregion

        #region Méthodes de Gestion de l'Historique (IHistoryViewModelManager)

        /// <summary>
        /// Charge l'historique depuis la source de données via le HistoryModule.
        /// </summary>
        /// <param name="callContext">Contexte de l'appel pour le debugging</param>
        /// <returns>Task représentant l'opération de chargement</returns>
        public async Task LoadHistoryAsync(string callContext = "HistoryViewModelManager")
        {
            if (_isDisposed)
            {
                _loggingService?.LogWarning($"🚨 [HISTORY_MANAGER] LoadHistoryAsync appelé sur instance disposée - Context: {callContext}");
                return;
            }

            _loggingService?.LogInfo($"📥 [HISTORY_MANAGER] LoadHistoryAsync DÉBUT - Context: {callContext}");

            try
            {
                IsLoading = true;
                _loggingService?.LogInfo($"📥 [HISTORY_MANAGER] IsLoading = true");

                // Déléguer vers le HistoryModule
                _loggingService?.LogInfo($"📥 [HISTORY_MANAGER] Délégation vers HistoryModule.LoadHistoryAsync");
                await _historyModule.LoadHistoryAsync(callContext);
                _loggingService?.LogInfo($"📥 [HISTORY_MANAGER] HistoryModule.LoadHistoryAsync terminé");

                // Synchroniser les collections
                _loggingService?.LogInfo($"📥 [HISTORY_MANAGER] Synchronisation des collections");
                SynchronizeCollections();
                _loggingService?.LogInfo($"📥 [HISTORY_MANAGER] Collections synchronisées - Items: {HistoryItems.Count}");

                // Notifier le changement
                _loggingService?.LogInfo($"📥 [HISTORY_MANAGER] Notification HistoryChanged");
                HistoryChanged?.Invoke(this, EventArgs.Empty);
                _loggingService?.LogInfo($"📥 [HISTORY_MANAGER] LoadHistoryAsync SUCCÈS - Context: {callContext}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"🚨 [HISTORY_MANAGER] LoadHistoryAsync ÉCHEC - Context: {callContext}, Erreur: {ex.Message}", ex);
                throw;
            }
            finally
            {
                IsLoading = false;
                _loggingService?.LogInfo($"📥 [HISTORY_MANAGER] IsLoading = false");
            }
        }

        /// <summary>
        /// Recharge l'historique en forçant une synchronisation.
        /// </summary>
        /// <param name="reason">Raison du rechargement</param>
        /// <returns>Task représentant l'opération de rechargement</returns>
        public async Task ReloadHistoryAsync(string reason = "Manual reload")
        {
            if (_isDisposed) return;

            try
            {
                IsLoading = true;

                // Forcer le rechargement via le module
                await _historyModule.ReloadHistoryAsync(reason);

                // Synchroniser les collections
                SynchronizeCollections();

                // Notifier le changement
                HistoryChanged?.Invoke(this, EventArgs.Empty);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Force une synchronisation des collections avec le HistoryModule.
        /// </summary>
        /// <param name="reason">Raison de la synchronisation forcée</param>
        /// <returns>Task représentant l'opération de synchronisation</returns>
        public async Task ForceSynchronizationAsync(string reason = "Manual sync")
        {
            if (_isDisposed) return;

            try
            {
                IsLoading = true;

                // Forcer la synchronisation via le module
                await _historyModule.ForceSynchronizationAsync(reason);

                // Synchroniser les collections
                SynchronizeCollections();

                // Notifier le changement
                HistoryChanged?.Invoke(this, EventArgs.Empty);
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Méthodes de Filtrage et Recherche (IHistoryViewModelManager)

        /// <summary>
        /// Applique un filtre de recherche sur les éléments d'historique.
        /// </summary>
        /// <param name="searchFilter">Filtre à appliquer (null pour effacer)</param>
        public void ApplySearchFilter(string? searchFilter)
        {
            if (_isDisposed) return;

            // Déléguer vers le HistoryModule
            _historyModule.ApplyFilter(searchFilter);
            
            // Synchroniser les collections
            SynchronizeCollections();
            
            // Notifier le changement
            HistoryChanged?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Efface le filtre de recherche actuel.
        /// </summary>
        public void ClearSearchFilter()
        {
            SearchText = null;
        }

        #endregion

        #region Méthodes Privées

        /// <summary>
        /// S'abonne aux événements du HistoryModule.
        /// </summary>
        private void SubscribeToModuleEvents()
        {
            if (_historyModule != null)
            {
                // 🔧 CORRECTION CRITIQUE : S'abonner aux événements du module
                _historyModule.HistoryChanged += OnHistoryModuleChanged;
                _historyModule.SelectionChanged += OnHistoryModuleSelectionChanged;
                _historyModule.FilterChanged += OnHistoryModuleFilterChanged;

                _loggingService?.LogInfo("🔗 [HISTORY_MANAGER] Abonnement aux événements du HistoryModule réussi");
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour les changements d'historique du module.
        /// </summary>
        private void OnHistoryModuleChanged(object? sender, ClipboardPlus.Modules.History.HistoryChangedEventArgs e)
        {
            var eventId = Guid.NewGuid().ToString("N")[..8];
            try
            {
                _loggingService?.LogInfo($"📡 [EVENT-{eventId}] HistoryChanged reçu: {e.ChangeType}, Context: {e.Context}, AffectedItems: {e.AffectedItems?.Count ?? 0}");

                // 📊 DIAGNOSTIC : Lister les éléments affectés
                if (e.AffectedItems != null && e.AffectedItems.Any())
                {
                    var affectedIds = string.Join(", ", e.AffectedItems.Take(5).Select(i => i.Id));
                    _loggingService?.LogInfo($"📡 [EVENT-{eventId}] Éléments affectés (5 premiers): [{affectedIds}]");
                }

                // État AVANT synchronisation
                var beforeCount = HistoryItems.Count;
                var beforeIds = HistoryItems.Take(5).Select(i => i.Id).ToList();
                _loggingService?.LogInfo($"📡 [EVENT-{eventId}] Manager AVANT sync - Count: {beforeCount}, IDs (5 premiers): [{string.Join(", ", beforeIds)}]");

                // Synchroniser la collection observable avec le module
                SynchronizeCollections();

                // État APRÈS synchronisation
                var afterCount = HistoryItems.Count;
                var afterIds = HistoryItems.Take(5).Select(i => i.Id).ToList();
                _loggingService?.LogInfo($"📡 [EVENT-{eventId}] Manager APRÈS sync - Count: {afterCount}, IDs (5 premiers): [{string.Join(", ", afterIds)}]");

                // Déclencher l'événement pour notifier les abonnés
                HistoryChanged?.Invoke(this, EventArgs.Empty);
                _loggingService?.LogInfo($"📡 [EVENT-{eventId}] Événement HistoryChanged propagé aux abonnés");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"🚨 [EVENT-{eventId}] Erreur lors du traitement de HistoryChanged: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour les changements de sélection du module.
        /// </summary>
        private void OnHistoryModuleSelectionChanged(object? sender, ClipboardPlus.Modules.History.HistorySelectionChangedEventArgs e)
        {
            try
            {
                _loggingService?.LogInfo($"📡 [HISTORY_MANAGER] Événement SelectionChanged reçu: {e.CurrentItem?.Id ?? 0}");

                // Synchroniser la sélection
                SelectedClipboardItem = e.CurrentItem;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"🚨 [HISTORY_MANAGER] Erreur lors du traitement de SelectionChanged: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour les changements de filtre du module.
        /// </summary>
        private void OnHistoryModuleFilterChanged(object? sender, ClipboardPlus.Modules.History.HistoryFilterChangedEventArgs e)
        {
            try
            {
                _loggingService?.LogInfo($"📡 [HISTORY_MANAGER] Événement FilterChanged reçu: '{e.CurrentFilter}'");

                // Synchroniser le filtre
                SearchText = e.CurrentFilter;

                // Synchroniser la collection filtrée
                SynchronizeCollections();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"🚨 [HISTORY_MANAGER] Erreur lors du traitement de FilterChanged: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Synchronise les collections observables avec le HistoryModule.
        /// </summary>
        private void SynchronizeCollections()
        {
            if (_isDisposed)
            {
                _loggingService?.LogWarning($"🚨 [HISTORY_MANAGER] SynchronizeCollections appelé sur instance disposée");
                return;
            }

            var syncId = Guid.NewGuid().ToString("N")[..8];
            _loggingService?.LogInfo($"🔄 [SYNC-{syncId}] SynchronizeCollections DÉBUT - Thread: {Thread.CurrentThread.ManagedThreadId}");

            // 🔧 CORRECTION CRITIQUE : Forcer l'exécution sur le thread UI
            if (!_uiDispatcher.CheckAccess())
            {
                _loggingService?.LogInfo($"🔄 [SYNC-{syncId}] Pas sur thread UI - Dispatch vers thread UI");
                _uiDispatcher.Invoke(() => SynchronizeCollections());
                return;
            }

            try
            {
                // 🔧 CORRECTION CRITIQUE : Synchroniser avec HistoryItems (TOUS les éléments) pas FilteredItems
                var moduleItems = _historyModule.HistoryItems;
                _loggingService?.LogInfo($"🔄 [SYNC-{syncId}] Module HistoryItems count: {moduleItems?.Count() ?? 0}");

                // 📊 DIAGNOSTIC DÉTAILLÉ : Lister les IDs des éléments du module
                if (moduleItems != null && moduleItems.Any())
                {
                    var moduleIds = string.Join(", ", moduleItems.Take(10).Select(i => i.Id));
                    _loggingService?.LogInfo($"🔄 [SYNC-{syncId}] Module IDs (10 premiers): [{moduleIds}]");
                }

                var oldCount = HistoryItems.Count;
                var oldIds = HistoryItems.Take(10).Select(i => i.Id).ToList();
                _loggingService?.LogInfo($"🔄 [SYNC-{syncId}] Manager AVANT Clear - Count: {oldCount}, IDs (10 premiers): [{string.Join(", ", oldIds)}]");

                // Mise à jour efficace de la collection observable
                HistoryItems.Clear();
                _loggingService?.LogInfo($"🔄 [SYNC-{syncId}] HistoryItems.Clear() TERMINÉ - Ancien count: {oldCount}, Nouveau count: {HistoryItems.Count}");

                if (moduleItems != null)
                {
                    var addedCount = 0;
                    foreach (var item in moduleItems)
                    {
                        HistoryItems.Add(item);
                        addedCount++;

                        // Log détaillé pour les 5 premiers éléments
                        if (addedCount <= 5)
                        {
                            _loggingService?.LogInfo($"🔄 [SYNC-{syncId}] Ajouté item #{addedCount}: ID={item.Id}, Title='{item.CustomName?.Substring(0, Math.Min(30, item.CustomName?.Length ?? 0))}...'");
                        }
                    }
                    _loggingService?.LogInfo($"🔄 [SYNC-{syncId}] Ajout terminé - {addedCount} éléments ajoutés");
                }

                var finalCount = HistoryItems.Count;
                var finalIds = HistoryItems.Take(10).Select(i => i.Id).ToList();
                _loggingService?.LogInfo($"🔄 [SYNC-{syncId}] Manager APRÈS Sync - Count: {finalCount}, IDs (10 premiers): [{string.Join(", ", finalIds)}]");

                _loggingService?.LogInfo($"🔄 [SYNC-{syncId}] SynchronizeCollections SUCCÈS - Ancien: {oldCount} → Nouveau: {finalCount}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"🚨 [SYNC-{syncId}] SynchronizeCollections ÉCHEC: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Se désabonne des événements du HistoryModule.
        /// </summary>
        private void UnsubscribeFromModuleEvents()
        {
            if (_historyModule != null)
            {
                // 🔧 CORRECTION CRITIQUE : Se désabonner des événements du module
                _historyModule.HistoryChanged -= OnHistoryModuleChanged;
                _historyModule.SelectionChanged -= OnHistoryModuleSelectionChanged;
                _historyModule.FilterChanged -= OnHistoryModuleFilterChanged;

                _loggingService?.LogInfo("🔗 [HISTORY_MANAGER] Désabonnement des événements du HistoryModule réussi");
            }
        }

        #endregion

        #region Méthodes d'Initialisation et Nettoyage (IHistoryViewModelManager)

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        /// <returns>Task représentant l'initialisation</returns>
        public async Task InitializeAsync()
        {
            if (_isDisposed) return;

            // Charger l'historique initial
            await LoadHistoryAsync("Initialization");
        }

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        public void Cleanup()
        {
            if (_isDisposed) return;

            UnsubscribeFromModuleEvents();
            HistoryItems.Clear();
            _selectedClipboardItem = null;
            _searchText = null;
        }

        /// <summary>
        /// Libère les ressources utilisées par le manager.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            Cleanup();
            _isDisposed = true;
        }

        #endregion

        #region Méthodes Non Implémentées (À compléter selon les besoins)

        // Les méthodes suivantes de l'interface seront implémentées dans une prochaine itération
        // selon les besoins spécifiques identifiés lors de l'intégration

        public void SelectItem(ClipboardItem? item) => SelectedClipboardItem = item;
        public void SelectNextItem() { /* À implémenter */ }
        public void SelectPreviousItem() { /* À implémenter */ }
        public void SelectFirstItem() { /* À implémenter */ }
        public void SelectLastItem() { /* À implémenter */ }
        public ClipboardItem? FindItemById(long id) => HistoryItems.FirstOrDefault(i => i.Id == id);
        public bool IsItemVisible(ClipboardItem item) => HistoryItems.Contains(item);
        public int GetItemIndex(ClipboardItem item) => HistoryItems.IndexOf(item);
        public HistoryStatistics GetStatistics() => new HistoryStatistics
        {
            TotalItems = TotalItemCount,
            FilteredItems = FilteredItemCount,
            CurrentFilter = SearchText
        };

        #endregion
    }
}
