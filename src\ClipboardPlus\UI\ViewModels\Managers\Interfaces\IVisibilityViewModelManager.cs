// ============================================================================
// INTERFACE VISIBILITY VIEWMODEL MANAGER - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Interface pour la gestion de la visibilité dans le ViewModel
// 📋 RESPONSABILITÉ : Gestion des propriétés ShowTitles, ShowTimestamps, etc.
// 🏗️ ARCHITECTURE : Extraction de la logique de visibilité dispersée
//
// ============================================================================

using System;

namespace ClipboardPlus.UI.ViewModels.Managers.Interfaces
{
    /// <summary>
    /// Interface pour le manager de gestion de la visibilité dans le ViewModel.
    /// 
    /// Ce manager est responsable de :
    /// - La gestion des propriétés de visibilité (ShowTitles, ShowTimestamps)
    /// - L'état de visibilité des composants UI
    /// - La synchronisation avec les paramètres utilisateur
    /// - La gestion des modes d'affichage
    /// </summary>
    public interface IVisibilityViewModelManager : IDisposable
    {
        #region Propriétés de Visibilité Principales

        /// <summary>
        /// Indique si les titres des éléments doivent être affichés.
        /// </summary>
        bool ShowTitles { get; set; }

        /// <summary>
        /// Indique si les timestamps des éléments doivent être affichés.
        /// </summary>
        bool ShowTimestamps { get; set; }

        /// <summary>
        /// Indique si les icônes de type de contenu doivent être affichées.
        /// </summary>
        bool ShowContentTypeIcons { get; set; }

        /// <summary>
        /// Indique si les indicateurs d'épinglage doivent être affichés.
        /// </summary>
        bool ShowPinIndicators { get; set; }

        /// <summary>
        /// Indique si les prévisualisations d'images doivent être affichées.
        /// </summary>
        bool ShowImagePreviews { get; set; }

        #endregion

        #region Propriétés d'État de l'Interface

        /// <summary>
        /// Indique si la barre de recherche est visible.
        /// </summary>
        bool IsSearchBarVisible { get; set; }

        /// <summary>
        /// Indique si la barre d'outils est visible.
        /// </summary>
        bool IsToolbarVisible { get; set; }

        /// <summary>
        /// Indique si la barre de statut est visible.
        /// </summary>
        bool IsStatusBarVisible { get; set; }

        /// <summary>
        /// Indique si le panneau de détails est visible.
        /// </summary>
        bool IsDetailsPanelVisible { get; set; }

        /// <summary>
        /// Indique si le mode compact est activé.
        /// </summary>
        bool IsCompactModeEnabled { get; set; }

        #endregion

        #region Propriétés de Mode d'Affichage

        /// <summary>
        /// Mode d'affichage actuel de la liste.
        /// </summary>
        ViewMode CurrentViewMode { get; set; }

        /// <summary>
        /// Taille des éléments dans la vue (Small, Medium, Large).
        /// </summary>
        ItemSize CurrentItemSize { get; set; }

        /// <summary>
        /// Nombre de colonnes pour l'affichage en grille.
        /// </summary>
        int GridColumnCount { get; set; }

        /// <summary>
        /// Indique si l'affichage automatique est activé.
        /// </summary>
        bool IsAutoDisplayEnabled { get; set; }

        #endregion

        #region Événements

        /// <summary>
        /// Événement déclenché lorsqu'une propriété de visibilité change.
        /// </summary>
        event EventHandler<VisibilityChangedEventArgs>? VisibilityChanged;

        /// <summary>
        /// Événement déclenché lorsque le mode d'affichage change.
        /// </summary>
        event EventHandler<ViewModeChangedEventArgs>? ViewModeChanged;

        /// <summary>
        /// Événement déclenché lorsque la taille des éléments change.
        /// </summary>
        event EventHandler<ItemSizeChangedEventArgs>? ItemSizeChanged;

        #endregion

        #region Méthodes de Gestion de la Visibilité

        /// <summary>
        /// Bascule l'affichage des titres.
        /// </summary>
        void ToggleShowTitles();

        /// <summary>
        /// Bascule l'affichage des timestamps.
        /// </summary>
        void ToggleShowTimestamps();

        /// <summary>
        /// Bascule l'affichage des icônes de type de contenu.
        /// </summary>
        void ToggleShowContentTypeIcons();

        /// <summary>
        /// Bascule l'affichage des indicateurs d'épinglage.
        /// </summary>
        void ToggleShowPinIndicators();

        /// <summary>
        /// Bascule l'affichage des prévisualisations d'images.
        /// </summary>
        void ToggleShowImagePreviews();

        /// <summary>
        /// Applique un profil de visibilité prédéfini.
        /// </summary>
        /// <param name="profile">Profil à appliquer</param>
        void ApplyVisibilityProfile(VisibilityProfile profile);

        #endregion

        #region Méthodes de Gestion des Modes

        /// <summary>
        /// Change le mode d'affichage.
        /// </summary>
        /// <param name="newMode">Nouveau mode d'affichage</param>
        void ChangeViewMode(ViewMode newMode);

        /// <summary>
        /// Change la taille des éléments.
        /// </summary>
        /// <param name="newSize">Nouvelle taille des éléments</param>
        void ChangeItemSize(ItemSize newSize);

        /// <summary>
        /// Bascule entre les modes d'affichage disponibles.
        /// </summary>
        void CycleViewModes();

        /// <summary>
        /// Bascule entre les tailles d'éléments disponibles.
        /// </summary>
        void CycleItemSizes();

        /// <summary>
        /// Active ou désactive le mode compact.
        /// </summary>
        /// <param name="enabled">True pour activer le mode compact</param>
        void SetCompactMode(bool enabled);

        #endregion

        #region Méthodes de Synchronisation

        /// <summary>
        /// Synchronise les paramètres de visibilité avec les paramètres utilisateur.
        /// </summary>
        void SynchronizeWithUserSettings();

        /// <summary>
        /// Sauvegarde les paramètres de visibilité actuels.
        /// </summary>
        void SaveVisibilitySettings();

        /// <summary>
        /// Restaure les paramètres de visibilité par défaut.
        /// </summary>
        void RestoreDefaultSettings();

        #endregion

        #region Méthodes Utilitaires

        /// <summary>
        /// Vérifie si un élément doit être visible selon les paramètres actuels.
        /// </summary>
        /// <param name="elementType">Type d'élément à vérifier</param>
        /// <returns>True si l'élément doit être visible</returns>
        bool ShouldShowElement(UIElementType elementType);

        /// <summary>
        /// Obtient la configuration de visibilité actuelle.
        /// </summary>
        /// <returns>Configuration de visibilité</returns>
        VisibilityConfiguration GetCurrentConfiguration();

        /// <summary>
        /// Applique une configuration de visibilité.
        /// </summary>
        /// <param name="configuration">Configuration à appliquer</param>
        void ApplyConfiguration(VisibilityConfiguration configuration);

        #endregion

        #region Méthodes d'Initialisation et Nettoyage

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        void Initialize();

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        void Cleanup();

        #endregion
    }

    #region Énumérations et Classes Auxiliaires

    /// <summary>
    /// Modes d'affichage disponibles.
    /// </summary>
    public enum ViewMode
    {
        List,
        Grid,
        Tiles,
        Details
    }

    /// <summary>
    /// Tailles d'éléments disponibles.
    /// </summary>
    public enum ItemSize
    {
        Small,
        Medium,
        Large,
        ExtraLarge
    }

    /// <summary>
    /// Types d'éléments UI.
    /// </summary>
    public enum UIElementType
    {
        Title,
        Timestamp,
        ContentTypeIcon,
        PinIndicator,
        ImagePreview,
        SearchBar,
        Toolbar,
        StatusBar,
        DetailsPanel
    }

    /// <summary>
    /// Profils de visibilité prédéfinis.
    /// </summary>
    public enum VisibilityProfile
    {
        Minimal,
        Standard,
        Detailed,
        Developer
    }

    /// <summary>
    /// Configuration de visibilité.
    /// </summary>
    public class VisibilityConfiguration
    {
        public bool ShowTitles { get; set; }
        public bool ShowTimestamps { get; set; }
        public bool ShowContentTypeIcons { get; set; }
        public bool ShowPinIndicators { get; set; }
        public bool ShowImagePreviews { get; set; }
        public ViewMode ViewMode { get; set; }
        public ItemSize ItemSize { get; set; }
        public bool IsCompactModeEnabled { get; set; }
    }

    /// <summary>
    /// Arguments d'événement pour un changement de visibilité.
    /// </summary>
    public class VisibilityChangedEventArgs : EventArgs
    {
        public string PropertyName { get; }
        public object? OldValue { get; }
        public object? NewValue { get; }

        public VisibilityChangedEventArgs(string propertyName, object? oldValue, object? newValue)
        {
            PropertyName = propertyName;
            OldValue = oldValue;
            NewValue = newValue;
        }
    }

    /// <summary>
    /// Arguments d'événement pour un changement de mode d'affichage.
    /// </summary>
    public class ViewModeChangedEventArgs : EventArgs
    {
        public ViewMode OldMode { get; }
        public ViewMode NewMode { get; }

        public ViewModeChangedEventArgs(ViewMode oldMode, ViewMode newMode)
        {
            OldMode = oldMode;
            NewMode = newMode;
        }
    }

    /// <summary>
    /// Arguments d'événement pour un changement de taille d'éléments.
    /// </summary>
    public class ItemSizeChangedEventArgs : EventArgs
    {
        public ItemSize OldSize { get; }
        public ItemSize NewSize { get; }

        public ItemSizeChangedEventArgs(ItemSize oldSize, ItemSize newSize)
        {
            OldSize = oldSize;
            NewSize = newSize;
        }
    }

    #endregion
}
