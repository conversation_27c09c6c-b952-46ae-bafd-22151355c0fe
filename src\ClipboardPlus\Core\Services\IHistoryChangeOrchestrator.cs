using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Orchestrateur principal pour le traitement des changements d'historique.
    /// 
    /// Ce service coordonne tous les services spécialisés pour traiter les événements
    /// de changement d'historique de manière structurée et testable.
    /// 
    /// Il remplace la logique monolithique de ClipboardHistoryManager_HistoryChanged
    /// par une architecture modulaire et maintenable.
    /// </summary>
    public interface IHistoryChangeOrchestrator
    {
        /// <summary>
        /// Traite un événement de changement d'historique en coordonnant
        /// tous les services nécessaires.
        /// 
        /// Cette méthode reproduit fidèlement la logique de la méthode originale
        /// tout en la décomposant en étapes claires et testables.
        /// </summary>
        /// <param name="args">Arguments contenant toutes les informations nécessaires</param>
        /// <returns>Résultat du traitement</returns>
        Task<HistoryChangeResult> HandleHistoryChangedAsync(HistoryChangedEventArgs args);

        /// <summary>
        /// Obtient les statistiques de traitement des événements.
        /// </summary>
        /// <returns>Statistiques actuelles</returns>
        HistoryChangeStatistics GetStatistics();

        /// <summary>
        /// Remet à zéro les statistiques de traitement.
        /// </summary>
        void ResetStatistics();
    }
}
