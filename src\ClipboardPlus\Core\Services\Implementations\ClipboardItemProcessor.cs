using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Implementations
{
    /// <summary>
    /// Service de traitement des éléments du presse-papiers.
    /// Responsabilité unique : Traiter les éléments (insertion/mise à jour en base de données).
    /// </summary>
    public class ClipboardItemProcessor : IClipboardItemProcessor
    {
        private readonly IPersistenceService _persistenceService;
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du processeur d'éléments.
        /// </summary>
        /// <param name="persistenceService">Service de persistance pour les opérations de base de données</param>
        /// <param name="loggingService">Service de logging pour traçabilité</param>
        public ClipboardItemProcessor(IPersistenceService persistenceService, ILoggingService? loggingService = null)
        {
            _persistenceService = persistenceService ?? throw new ArgumentNullException(nameof(persistenceService));
            _loggingService = loggingService;
        }

        /// <summary>
        /// Traite un nouvel élément en l'insérant dans la persistance.
        /// </summary>
        /// <param name="item">L'élément à insérer</param>
        /// <returns>L'ID généré pour l'élément</returns>
        public async Task<long> ProcessNewItemAsync(ClipboardItem item)
        {
            // Validation des paramètres d'entrée (ne pas capturer ces exceptions)
            if (item == null)
            {
                throw new ArgumentNullException(nameof(item));
            }

            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] ClipboardItemProcessor.ProcessNewItemAsync - Début traitement nouvel élément Type: {item.DataType}");

            try
            {
                _loggingService?.LogInfo($"[{operationId}] ClipboardItemProcessor.ProcessNewItemAsync - Insertion du nouvel élément dans la base de données");

                // Insérer l'élément dans la base de données
                var newId = await _persistenceService.InsertClipboardItemAsync(item);

                if (newId > 0)
                {
                    // Assigner l'ID généré à l'élément
                    item.Id = newId;
                    _loggingService?.LogInfo($"[{operationId}] ClipboardItemProcessor.ProcessNewItemAsync - Nouvel élément inséré avec succès. ID: {newId}");
                }
                else
                {
                    _loggingService?.LogWarning($"[{operationId}] ClipboardItemProcessor.ProcessNewItemAsync - Échec de l'insertion (ID retourné={newId})");
                }

                return newId;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] ClipboardItemProcessor.ProcessNewItemAsync - Erreur lors de l'insertion: {ex.Message}", ex);

                // En cas d'erreur, retourner 0 pour indiquer l'échec
                return 0;
            }
        }

        /// <summary>
        /// Traite un élément existant en le mettant à jour.
        /// </summary>
        /// <param name="existingItem">L'élément existant à mettre à jour</param>
        /// <param name="newTimestamp">Nouveau timestamp à appliquer</param>
        /// <returns>L'ID de l'élément mis à jour</returns>
        public async Task<long> ProcessExistingItemAsync(ClipboardItem existingItem, DateTime newTimestamp)
        {
            // Validation des paramètres d'entrée (ne pas capturer ces exceptions)
            if (existingItem == null)
            {
                throw new ArgumentNullException(nameof(existingItem));
            }

            if (existingItem.Id <= 0)
            {
                throw new ArgumentException("L'élément existant doit avoir un ID valide", nameof(existingItem));
            }

            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] ClipboardItemProcessor.ProcessExistingItemAsync - Début traitement élément existant ID: {existingItem.Id}");

            try
            {
                _loggingService?.LogInfo($"[{operationId}] ClipboardItemProcessor.ProcessExistingItemAsync - Mise à jour du timestamp de {existingItem.Timestamp:yyyy-MM-dd HH:mm:ss.fff} vers {newTimestamp:yyyy-MM-dd HH:mm:ss.fff}");

                // Mettre à jour le timestamp de l'élément existant
                var originalTimestamp = existingItem.Timestamp;
                existingItem.Timestamp = newTimestamp;

                // Mettre à jour l'élément dans la base de données
                await _persistenceService.UpdateClipboardItemAsync(existingItem);

                _loggingService?.LogInfo($"[{operationId}] ClipboardItemProcessor.ProcessExistingItemAsync - Élément ID={existingItem.Id} mis à jour avec succès");

                return existingItem.Id;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] ClipboardItemProcessor.ProcessExistingItemAsync - Erreur lors de la mise à jour: {ex.Message}", ex);

                // En cas d'erreur, retourner 0 pour indiquer l'échec
                return 0;
            }
        }
    }
}
