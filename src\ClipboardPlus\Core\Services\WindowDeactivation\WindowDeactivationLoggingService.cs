using System;
using System.Linq;
using System.Windows;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Implémentation du service de logging spécialisé pour la désactivation de fenêtres.
    /// </summary>
    public class WindowDeactivationLoggingService : IWindowDeactivationLoggingService
    {
        private readonly ILoggingService _loggingService;
        private WindowDeactivationLoggingConfig _config;

        /// <summary>
        /// Initialise une nouvelle instance du service de logging de désactivation.
        /// </summary>
        /// <param name="loggingService">Service de journalisation de base</param>
        /// <param name="config">Configuration du logging (optionnel)</param>
        public WindowDeactivationLoggingService(ILoggingService loggingService, WindowDeactivationLoggingConfig? config = null)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _config = config ?? new WindowDeactivationLoggingConfig();
        }

        /// <inheritdoc />
        public void LogDeactivationStart(Window window, string? operationId = null)
        {
            if (_config.LoggingLevel == WindowDeactivationLoggingLevel.Minimal) return;

            var windowName = window?.GetType().Name ?? "Unknown";
            var isClosing = GetWindowClosingState(window);
            var isActive = window?.IsActive ?? false;
            var opId = FormatOperationId(operationId);
            var emoji = _config.EnableEmojiLogging ? "🚨 " : "";

            _loggingService.LogInfo($"{emoji}{_config.LogPrefix}{opId} [DÉBUT] Window_Deactivated - Window: {windowName}, IsClosing: {isClosing}, IsActive: {isActive}");
        }

        /// <inheritdoc />
        public void LogDiagnosticResults(WindowDiagnosticResult diagnostic, string? operationId = null)
        {
            if (_config.LoggingLevel == WindowDeactivationLoggingLevel.Minimal) return;

            var opId = FormatOperationId(operationId);
            var emoji = _config.EnableEmojiLogging ? "🔍 " : "";

            if (diagnostic.IsSuccessful)
            {
                _loggingService.LogInfo($"{emoji}{_config.LogPrefix}{opId} DIAGNOSTIC: {diagnostic.TotalWindowCount} fenêtres trouvées en {diagnostic.DiagnosticDurationMs:F1}ms");

                if (_config.EnableWindowDetailsLogging && _config.LoggingLevel >= WindowDeactivationLoggingLevel.Detailed)
                {
                    foreach (var windowInfo in diagnostic.AllWindows)
                    {
                        _loggingService.LogInfo($"   - {windowInfo.WindowTypeName}: '{windowInfo.Title}' (IsActive: {windowInfo.IsActive}, Owner: {windowInfo.OwnerTypeName ?? "null"})");
                    }
                }

                var activeEmoji = _config.EnableEmojiLogging ? "🎯 " : "";
                if (diagnostic.ActiveWindow != null)
                {
                    _loggingService.LogInfo($"{activeEmoji}{_config.LogPrefix}{opId} Fenêtre active détectée: {diagnostic.ActiveWindowInfo?.WindowTypeName ?? "UNKNOWN"} - '{diagnostic.ActiveWindow.Title}'");
                }
                else
                {
                    _loggingService.LogInfo($"{activeEmoji}{_config.LogPrefix}{opId} Aucune fenêtre active détectée");
                }
            }
            else
            {
                var errorEmoji = _config.EnableEmojiLogging ? "❌ " : "";
                _loggingService.LogWarning($"{errorEmoji}{_config.LogPrefix}{opId} Échec du diagnostic: {diagnostic.ErrorMessage}");
            }
        }

        /// <inheritdoc />
        public void LogClassificationResults(WindowClassificationResult classification, string? operationId = null)
        {
            if (_config.LoggingLevel == WindowDeactivationLoggingLevel.Minimal) return;

            var opId = FormatOperationId(operationId);
            var emoji = _config.EnableEmojiLogging ? "🔍 " : "";

            _loggingService.LogInfo($"{emoji}{_config.LogPrefix}{opId} Analyse de la fenêtre active: {classification.WindowTypeFullName}");

            if (_config.LoggingLevel >= WindowDeactivationLoggingLevel.Detailed)
            {
                foreach (var test in classification.ClassificationTests)
                {
                    _loggingService.LogInfo($"   {test.Key}: {test.Value}");
                }
            }

            var resultEmoji = _config.EnableEmojiLogging ? "🎯 " : "";
            var friendlyName = !string.IsNullOrEmpty(classification.FriendlyName) ? $" ({classification.FriendlyName})" : "";
            _loggingService.LogInfo($"{resultEmoji}{_config.LogPrefix}{opId} isOurAppWindow: {classification.IsApplicationWindow}{friendlyName} - {classification.Reason}");

            if (_config.EnablePerformanceLogging)
            {
                var perfEmoji = _config.EnableEmojiLogging ? "⏱️ " : "";
                _loggingService.LogInfo($"{perfEmoji}{_config.LogPrefix}{opId} Classification terminée en {classification.ClassificationDurationMs:F1}ms");
            }
        }

        /// <inheritdoc />
        public void LogVisibilityDecision(WindowVisibilityDecision decision, string? operationId = null)
        {
            var opId = FormatOperationId(operationId);
            
            if (decision.ShouldHide)
            {
                var hideEmoji = _config.EnableEmojiLogging ? "🚨 " : "";
                _loggingService.LogInfo($"{hideEmoji}{_config.LogPrefix}{opId} MASQUAGE de la fenêtre autorisé! Raison: {decision.Reason}");
            }
            else
            {
                var keepEmoji = _config.EnableEmojiLogging ? "✅ " : "";
                _loggingService.LogInfo($"{keepEmoji}{_config.LogPrefix}{opId} Fenêtre conservée visible. Raison: {decision.Reason}");
            }

            if (_config.LoggingLevel >= WindowDeactivationLoggingLevel.Detailed)
            {
                _loggingService.LogInfo($"   Type de décision: {decision.DecisionType}");
                _loggingService.LogInfo($"   Action recommandée: {decision.RecommendedAction}");
                _loggingService.LogInfo($"   Niveau de confiance: {decision.ConfidenceLevel:P1}");
                
                if (!string.IsNullOrEmpty(decision.Details))
                {
                    _loggingService.LogInfo($"   Détails: {decision.Details}");
                }
            }

            if (_config.EnablePerformanceLogging)
            {
                var perfEmoji = _config.EnableEmojiLogging ? "⏱️ " : "";
                _loggingService.LogInfo($"{perfEmoji}{_config.LogPrefix}{opId} Décision prise en {decision.EvaluationDurationMs:F1}ms");
            }
        }

        /// <inheritdoc />
        public void LogDeactivationEnd(Window window, string? operationId = null)
        {
            if (_config.LoggingLevel == WindowDeactivationLoggingLevel.Minimal) return;

            var windowName = window?.GetType().Name ?? "Unknown";
            var opId = FormatOperationId(operationId);
            var emoji = _config.EnableEmojiLogging ? "🏁 " : "";

            _loggingService.LogInfo($"{emoji}{_config.LogPrefix}{opId} [FIN] Window_Deactivated - Window: {windowName}");
        }

        /// <inheritdoc />
        public void LogError(string operationId, Exception exception, string? context = null)
        {
            var opId = FormatOperationId(operationId);
            var emoji = _config.EnableEmojiLogging ? "❌ " : "";
            var contextInfo = !string.IsNullOrEmpty(context) ? $" Contexte: {context}" : "";

            _loggingService.LogError($"{emoji}{_config.LogPrefix}{opId} Erreur lors de la désactivation: {exception.Message}{contextInfo}", exception);
        }

        /// <inheritdoc />
        public void LogValidationResult(WindowStateValidationResult validationResult, string? operationId = null)
        {
            if (_config.LoggingLevel == WindowDeactivationLoggingLevel.Minimal && !validationResult.ShouldIgnore) return;

            var opId = FormatOperationId(operationId);
            var emoji = _config.EnableEmojiLogging ? "✅ " : "";

            if (validationResult.ShouldIgnore)
            {
                _loggingService.LogInfo($"{emoji}{_config.LogPrefix}{opId} Validation: IGNORÉ - {validationResult.Reason}");
            }
            else
            {
                var continueEmoji = _config.EnableEmojiLogging ? "🚀 " : "";
                _loggingService.LogInfo($"{continueEmoji}{_config.LogPrefix}{opId} Validation: CONTINUER - {validationResult.Reason}");
            }

            if (_config.LoggingLevel >= WindowDeactivationLoggingLevel.Detailed && !string.IsNullOrEmpty(validationResult.Details))
            {
                _loggingService.LogInfo($"   Détails: {validationResult.Details}");
            }
        }

        /// <inheritdoc />
        public void LogPerformanceMetrics(WindowDeactivationMetrics metrics, string? operationId = null)
        {
            if (!_config.EnablePerformanceLogging) return;

            var opId = FormatOperationId(operationId);
            var emoji = _config.EnableEmojiLogging ? "📊 " : "";

            _loggingService.LogInfo($"{emoji}{_config.LogPrefix}{opId} MÉTRIQUES DE PERFORMANCE:");
            _loggingService.LogInfo($"   Durée totale: {metrics.TotalDurationMs:F1}ms");
            _loggingService.LogInfo($"   Diagnostic: {metrics.DiagnosticDurationMs:F1}ms");
            _loggingService.LogInfo($"   Classification: {metrics.ClassificationDurationMs:F1}ms");
            _loggingService.LogInfo($"   Décision: {metrics.DecisionDurationMs:F1}ms");
            _loggingService.LogInfo($"   Fenêtres analysées: {metrics.WindowsAnalyzed}");
            
            if (metrics.MemoryUsageBytes > 0)
            {
                _loggingService.LogInfo($"   Utilisation mémoire: {metrics.MemoryUsageBytes:N0} octets");
            }
        }

        /// <inheritdoc />
        public void ConfigureLogging(WindowDeactivationLoggingConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _loggingService.LogInfo($"🔧 {_config.LogPrefix} Configuration du logging mise à jour - Niveau: {_config.LoggingLevel}");
        }

        /// <summary>
        /// Formate l'identifiant d'opération pour les logs.
        /// </summary>
        /// <param name="operationId">Identifiant d'opération</param>
        /// <returns>Identifiant formaté</returns>
        private string FormatOperationId(string? operationId)
        {
            if (!_config.IncludeOperationIds || string.IsNullOrEmpty(operationId))
            {
                return string.Empty;
            }

            return $" [{operationId}]";
        }

        /// <summary>
        /// Obtient l'état de fermeture d'une fenêtre via réflexion.
        /// </summary>
        /// <param name="window">Fenêtre à analyser</param>
        /// <returns>État de fermeture</returns>
        private bool GetWindowClosingState(Window? window)
        {
            if (window == null) return false;

            try
            {
                // Tentative d'accès au champ _isClosing via réflexion
                var isClosingField = window.GetType().GetField("_isClosing", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (isClosingField != null)
                {
                    return (bool)(isClosingField.GetValue(window) ?? false);
                }
            }
            catch
            {
                // Ignorer les erreurs de réflexion
            }

            return false;
        }
    }
}
