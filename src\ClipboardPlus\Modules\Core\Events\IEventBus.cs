using System;
using System.Threading.Tasks;

namespace ClipboardPlus.Modules.Core.Events
{
    /// <summary>
    /// Interface pour le bus d'événements inter-modules.
    /// 
    /// Le bus d'événements permet une communication découplée entre les modules
    /// en utilisant le pattern Publisher/Subscriber.
    /// </summary>
    public interface IEventBus
    {
        /// <summary>
        /// Publie un événement de manière asynchrone.
        /// </summary>
        /// <typeparam name="TEvent">Type de l'événement</typeparam>
        /// <param name="eventData">Données de l'événement</param>
        /// <returns>Task représentant l'opération de publication</returns>
        Task PublishAsync<TEvent>(TEvent eventData) where TEvent : class, IModuleEvent;

        /// <summary>
        /// Publie un événement de manière synchrone.
        /// </summary>
        /// <typeparam name="TEvent">Type de l'événement</typeparam>
        /// <param name="eventData">Données de l'événement</param>
        void Publish<TEvent>(TEvent eventData) where TEvent : class, IModuleEvent;

        /// <summary>
        /// S'abonne à un type d'événement avec un handler asynchrone.
        /// </summary>
        /// <typeparam name="TEvent">Type de l'événement</typeparam>
        /// <param name="handler">Handler pour traiter l'événement</param>
        /// <param name="keepSubscriberReferenceAlive">Si true, maintient une référence forte au handler pour éviter la collecte par le GC.
        /// Si false (défaut), utilise une WeakReference permettant la collecte automatique.</param>
        /// <returns>Token de désabonnement</returns>
        IEventSubscription Subscribe<TEvent>(Func<TEvent, Task> handler, bool keepSubscriberReferenceAlive = false) where TEvent : class, IModuleEvent;

        /// <summary>
        /// S'abonne à un type d'événement avec un handler synchrone.
        /// </summary>
        /// <typeparam name="TEvent">Type de l'événement</typeparam>
        /// <param name="handler">Handler pour traiter l'événement</param>
        /// <param name="keepSubscriberReferenceAlive">Si true, maintient une référence forte au handler pour éviter la collecte par le GC.
        /// Si false (défaut), utilise une WeakReference permettant la collecte automatique.</param>
        /// <returns>Token de désabonnement</returns>
        IEventSubscription Subscribe<TEvent>(Action<TEvent> handler, bool keepSubscriberReferenceAlive = false) where TEvent : class, IModuleEvent;

        /// <summary>
        /// Se désabonne d'un événement.
        /// </summary>
        /// <param name="subscription">Subscription à annuler</param>
        void Unsubscribe(IEventSubscription subscription);

        /// <summary>
        /// Se désabonne de tous les événements d'un abonné.
        /// </summary>
        /// <param name="subscriber">Abonné à désabonner</param>
        void UnsubscribeAll(object subscriber);

        /// <summary>
        /// Efface tous les abonnements.
        /// </summary>
        void Clear();
    }

    /// <summary>
    /// Interface de base pour tous les événements de modules.
    /// </summary>
    public interface IModuleEvent
    {
        /// <summary>
        /// Identifiant unique de l'événement.
        /// </summary>
        Guid EventId { get; }

        /// <summary>
        /// Horodatage de création de l'événement.
        /// </summary>
        DateTime Timestamp { get; }

        /// <summary>
        /// Nom du module source de l'événement.
        /// </summary>
        string SourceModule { get; }

        /// <summary>
        /// Nom du module cible (optionnel, null pour broadcast).
        /// </summary>
        string? TargetModule { get; }
    }

    /// <summary>
    /// Interface pour les tokens de désabonnement.
    /// </summary>
    public interface IEventSubscription : IDisposable
    {
        /// <summary>
        /// Identifiant unique de l'abonnement.
        /// </summary>
        Guid SubscriptionId { get; }

        /// <summary>
        /// Type de l'événement auquel on est abonné.
        /// </summary>
        Type EventType { get; }

        /// <summary>
        /// Abonné qui a créé cette subscription.
        /// </summary>
        object Subscriber { get; }

        /// <summary>
        /// Indique si l'abonnement est encore actif.
        /// </summary>
        bool IsActive { get; }

        /// <summary>
        /// Annule l'abonnement.
        /// </summary>
        void Cancel();
    }

    /// <summary>
    /// Classe de base pour tous les événements de modules.
    /// </summary>
    public abstract class ModuleEventBase : IModuleEvent
    {
        /// <inheritdoc />
        public Guid EventId { get; }

        /// <inheritdoc />
        public DateTime Timestamp { get; }

        /// <inheritdoc />
        public string SourceModule { get; }

        /// <inheritdoc />
        public string? TargetModule { get; }

        protected ModuleEventBase(string sourceModule, string? targetModule = null)
        {
            EventId = Guid.NewGuid();
            Timestamp = DateTime.UtcNow;
            SourceModule = sourceModule;
            TargetModule = targetModule;
        }
    }

    /// <summary>
    /// Événement générique pour les changements d'état de module.
    /// </summary>
    public class ModuleStateChangedEvent : ModuleEventBase
    {
        /// <summary>
        /// État précédent du module.
        /// </summary>
        public ModuleState PreviousState { get; }

        /// <summary>
        /// Nouvel état du module.
        /// </summary>
        public ModuleState NewState { get; }

        /// <summary>
        /// Raison du changement d'état.
        /// </summary>
        public string? Reason { get; }

        public ModuleStateChangedEvent(string sourceModule, ModuleState previousState, ModuleState newState, string? reason = null)
            : base(sourceModule)
        {
            PreviousState = previousState;
            NewState = newState;
            Reason = reason;
        }
    }

    /// <summary>
    /// Événement pour les erreurs de module.
    /// </summary>
    public class ModuleErrorEvent : ModuleEventBase
    {
        /// <summary>
        /// Exception qui a causé l'erreur.
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// Message d'erreur.
        /// </summary>
        public string ErrorMessage { get; }

        /// <summary>
        /// Contexte de l'erreur.
        /// </summary>
        public string? Context { get; }

        public ModuleErrorEvent(string sourceModule, Exception exception, string? context = null)
            : base(sourceModule)
        {
            Exception = exception;
            ErrorMessage = exception.Message;
            Context = context;
        }

        public ModuleErrorEvent(string sourceModule, string errorMessage, string? context = null)
            : base(sourceModule)
        {
            Exception = new Exception(errorMessage);
            ErrorMessage = errorMessage;
            Context = context;
        }
    }
}
