using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Visibility;

namespace ClipboardPlus.UI.ViewModels.Construction.Interfaces
{
    /// <summary>
    /// Service de configuration des événements et messages.
    /// Responsabilité : Configuration de tous les événements et messages WeakReference selon le principe SRP.
    /// </summary>
    public interface IEventConfigurationService
    {

        /// <summary>
        /// Configure les événements du gestionnaire de visibilité.
        /// Cette méthode centralise la configuration des événements de visibilité
        /// avec gestion appropriée des services optionnels et du logging.
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel à configurer</param>
        /// <param name="visibilityManager">Gestionnaire de visibilité (peut être null)</param>
        /// <param name="loggingService">Service de logging (peut être null)</param>
        void ConfigureVisibilityEvents(
            ClipboardHistoryViewModel viewModel,
            IVisibilityStateManager? visibilityManager,
            ILoggingService? loggingService);
    }
}
