using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service responsable de la validation des conditions préalables 
    /// pour le traitement des changements d'historique.
    /// 
    /// Ce service fait partie du refactoring de ClipboardHistoryManager_HistoryChanged
    /// pour réduire la complexité cyclomatique et améliorer la testabilité.
    /// </summary>
    public interface IHistoryChangeValidator
    {
        /// <summary>
        /// Valide si le changement d'historique peut être traité.
        /// </summary>
        /// <param name="context">Contexte de validation contenant l'état actuel</param>
        /// <returns>Résultat de validation avec raison si rejeté</returns>
        ValidationResult ValidateHistoryChange(HistoryChangeContext context);
    }

    /// <summary>
    /// Contexte contenant toutes les informations nécessaires pour valider
    /// si un changement d'historique peut être traité.
    /// </summary>
    public class HistoryChangeContext
    {
        /// <summary>
        /// Indique si la réaction aux changements d'historique est désactivée.
        /// </summary>
        public bool PreventReaction { get; set; }

        /// <summary>
        /// Indique si un élément est actuellement en cours de mise à jour.
        /// </summary>
        public bool IsUpdatingItem { get; set; }

        /// <summary>
        /// Indique si les éléments sont actuellement en cours de réorganisation.
        /// </summary>
        public bool IsReorderingItems { get; set; }

        /// <summary>
        /// Référence au gestionnaire d'historique (null si non disponible).
        /// </summary>
        public IClipboardHistoryManager? HistoryManager { get; set; }

        /// <summary>
        /// Indique si une opération est actuellement en cours.
        /// </summary>
        public bool IsOperationInProgress { get; set; }

        /// <summary>
        /// Indique si un collage d'élément est en cours.
        /// </summary>
        public bool IsItemPasteInProgress { get; set; }

        /// <summary>
        /// Indique si l'application s'exécute dans un environnement de test.
        /// </summary>
        public bool IsInTestEnvironment { get; set; }
    }

    /// <summary>
    /// Résultat de la validation d'un changement d'historique.
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// Indique si la validation a réussi.
        /// </summary>
        public bool IsValid { get; private set; }

        /// <summary>
        /// Raison du rejet si la validation a échoué.
        /// </summary>
        public string? Reason { get; private set; }

        private ValidationResult(bool isValid, string? reason = null)
        {
            IsValid = isValid;
            Reason = reason;
        }

        /// <summary>
        /// Crée un résultat de validation accepté.
        /// </summary>
        public static ValidationResult Accepted() => new ValidationResult(true);

        /// <summary>
        /// Crée un résultat de validation rejeté avec une raison.
        /// </summary>
        /// <param name="reason">Raison du rejet</param>
        public static ValidationResult Rejected(string reason) => new ValidationResult(false, reason);
    }
}
