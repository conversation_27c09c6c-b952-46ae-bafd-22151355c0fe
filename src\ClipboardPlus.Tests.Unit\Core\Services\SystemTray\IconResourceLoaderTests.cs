using System;
using System.Drawing;
using System.Threading;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;

namespace ClipboardPlus.Tests.Unit.Core.Services.SystemTray
{
    /// <summary>
    /// Tests unitaires pour IconResourceLoader.
    /// Vérifie la responsabilité unique : chargement des ressources d'icône avec fallback.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class IconResourceLoaderTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private IconResourceLoader _loader;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _loader = new IconResourceLoader(_mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new IconResourceLoader(null));
        }

        [Test]
        public void LoadIconFromResource_WithNullPath_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => _loader.LoadIconFromResource(null));
            Assert.That(ex.ParamName, Is.EqualTo("resourcePath"));
        }

        [Test]
        public void LoadIconFromResource_WithEmptyPath_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => _loader.LoadIconFromResource(""));
            Assert.That(ex.ParamName, Is.EqualTo("resourcePath"));
        }

        [Test]
        public void LoadIconFromResource_WithWhitespacePath_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => _loader.LoadIconFromResource("   "));
            Assert.That(ex.ParamName, Is.EqualTo("resourcePath"));
        }

        [Test]
        public void LoadIconFromResource_WithNonExistentResource_ReturnsFallbackIcon()
        {
            // Arrange
            const string nonExistentPath = "pack://application:,,,/Resources/nonexistent.ico";

            // Act
            var result = _loader.LoadIconFromResource(nonExistentPath);

            // Assert
            Assert.That(result, Is.Not.Null, "Should return an icon");
            Assert.That(result, Is.EqualTo(SystemIcons.Application), "Should return fallback icon for non-existent resource");

            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains($"Tentative de chargement de l'icône depuis: {nonExistentPath}"))),
                Times.Once,
                "Should log loading attempt");

            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("Ressource non trouvée") && s.Contains("Utilisation de l'icône de fallback"))),
                Times.Once,
                "Should log resource not found warning");
        }

        [Test]
        public void LoadIconFromResource_WithException_ReturnsFallbackIcon()
        {
            // Arrange
            const string invalidPath = "invalid://path";

            // Act
            var result = _loader.LoadIconFromResource(invalidPath);

            // Assert
            Assert.That(result, Is.Not.Null, "Should return an icon even when exception occurs");
            Assert.That(result, Is.EqualTo(SystemIcons.Application), "Should return fallback icon when exception occurs");

            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("Erreur lors de la vérification d'existence"))),
                Times.Once,
                "Should log warning when exception occurs during resource check");

            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("Ressource non trouvée") && s.Contains("Utilisation de l'icône de fallback"))),
                Times.Once,
                "Should log fallback usage");
        }

        [Test]
        public void GetFallbackIcon_ReturnsSystemApplicationIcon()
        {
            // Act
            var result = _loader.GetFallbackIcon();

            // Assert
            Assert.That(result, Is.Not.Null, "Should return an icon");
            Assert.That(result, Is.EqualTo(SystemIcons.Application), "Should return system application icon");

            _mockLoggingService.Verify(
                x => x.LogInfo("IconResourceLoader: Utilisation de l'icône système par défaut"),
                Times.Once,
                "Should log fallback icon usage");
        }

        [Test]
        public void ResourceExists_WithNullPath_ReturnsFalse()
        {
            // Act
            bool result = _loader.ResourceExists(null);

            // Assert
            Assert.That(result, Is.False, "Should return false for null path");
        }

        [Test]
        public void ResourceExists_WithEmptyPath_ReturnsFalse()
        {
            // Act
            bool result = _loader.ResourceExists("");

            // Assert
            Assert.That(result, Is.False, "Should return false for empty path");
        }

        [Test]
        public void ResourceExists_WithWhitespacePath_ReturnsFalse()
        {
            // Act
            bool result = _loader.ResourceExists("   ");

            // Assert
            Assert.That(result, Is.False, "Should return false for whitespace path");
        }

        [Test]
        public void ResourceExists_WithNonExistentResource_ReturnsFalse()
        {
            // Arrange
            const string nonExistentPath = "pack://application:,,,/Resources/nonexistent.ico";

            // Act
            bool result = _loader.ResourceExists(nonExistentPath);

            // Assert
            Assert.That(result, Is.False, "Should return false for non-existent resource");

            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("Erreur lors de la vérification d'existence de pack://application:,,,/Resources/nonexistent.ico"))),
                Times.Once,
                "Should log warning when resource doesn't exist");
        }

        [Test]
        public void ResourceExists_WithException_ReturnsFalseAndLogsWarning()
        {
            // Arrange
            const string invalidPath = "invalid://path";

            // Act
            bool result = _loader.ResourceExists(invalidPath);

            // Assert
            Assert.That(result, Is.False, "Should return false when exception occurs");

            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("Erreur lors de la vérification d'existence"))),
                Times.Once,
                "Should log warning when exception occurs");
        }

        [Test]
        public void GetFallbackIcon_WithException_ThrowsException()
        {
            // Arrange - Créer un mock qui lève une exception lors du logging
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo("IconResourceLoader: Utilisation de l'icône système par défaut"))
                             .Throws(new InvalidOperationException("Test exception"));

            var loader = new IconResourceLoader(mockLoggingService.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => loader.GetFallbackIcon());

            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de l'obtention de l'icône de fallback")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when fallback fails");
        }

        [Test]
        public void Loader_IsStateless()
        {
            // Act - Appeler plusieurs fois les méthodes
            var fallback1 = _loader.GetFallbackIcon();
            var fallback2 = _loader.GetFallbackIcon();

            bool exists1 = _loader.ResourceExists("pack://application:,,,/Resources/test1.ico");
            bool exists2 = _loader.ResourceExists("pack://application:,,,/Resources/test2.ico");

            // Assert - Les résultats doivent être cohérents
            Assert.That(fallback1, Is.EqualTo(fallback2), "GetFallbackIcon should return consistent results");
            Assert.That(exists1, Is.EqualTo(exists2), "ResourceExists should return consistent results for similar non-existent resources");
        }

        [Test]
        public void LoadIconFromResource_CallsResourceExists()
        {
            // Arrange
            const string testPath = "pack://application:,,,/Resources/test.ico";

            // Act
            _loader.LoadIconFromResource(testPath);

            // Assert - Vérifier que ResourceExists a été appelé via les logs
            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("Erreur lors de la vérification d'existence de pack://application:,,,/Resources/test.ico"))),
                Times.Once,
                "LoadIconFromResource should call ResourceExists internally");
        }

        [TearDown]
        public void TearDown()
        {
            // Nettoyer les ressources si nécessaire
        }
    }
}
