<Application x:Class="ClipboardPlus.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ClipboardPlus"
             xmlns:converters="clr-namespace:ClipboardPlus.UI.Converters"
             Startup="Application_Startup">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/ClipboardPlus;component/UI/Themes/Default.xaml"/>
                <ResourceDictionary Source="/ClipboardPlus;component/UI/Styles/DragDropStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Convertisseurs globaux - SYSTÈME SOLID + Utilitaires -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:BooleanToColorConverter x:Key="BooleanToColorConverter" />
            <converters:DataTypeToIconConverter x:Key="DataTypeToIconConverter" />
            
            <!-- ARCHITECTURE SOLID : Les règles de visibilité sont maintenant gérées par VisibilityStateManager -->
            
            <!-- Styles et ressources globales -->
            
            <!-- Animation pour les boutons -->
            <Style x:Key="FadeButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="3"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="border" Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="0">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E0E0E0"/>
                                    <Setter Property="BorderBrush" Value="#CCCCCC"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#CCCCCC"/>
                                    <Setter Property="BorderBrush" Value="#AAAAAA"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            
            <!-- Style pour les boutons transparents des éléments de liste -->
            <Style x:Key="TransparentButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="2"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Opacity" Value="0.7"/>
                <Setter Property="Foreground" Value="#333333"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="border" Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="0">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#20000000"/>
                                    <Setter Property="Opacity" Value="1"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#30000000"/>
                                    <Setter Property="Opacity" Value="1"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Style pour les éléments de la liste d'historique -->
            <Style x:Key="ClipboardItemStyle" TargetType="ListBoxItem">
                <Setter Property="Padding" Value="5" />
                <Setter Property="Margin" Value="2" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="VerticalContentAlignment" Value="Center" />
                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="BorderBrush" Value="Transparent" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ListBoxItem">
                            <Border x:Name="Border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="0">
                                <ContentPresenter x:Name="ContentPresenter"
                                                 Margin="{TemplateBinding Padding}"
                                                 HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                 VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                 SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                                <GradientStop Color="#E5F1FF" Offset="0.0" />
                                                <GradientStop Color="#D0E5FF" Offset="1.0" />
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter Property="BorderBrush" Value="#C0D8F0" />
                                    <Setter Property="Foreground" Value="#333333" />
                                </Trigger>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#F0F5FA" />
                                    <Setter Property="BorderBrush" Value="#E0E9F5" />
                                </Trigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsSelected" Value="True" />
                                        <Condition Property="IsMouseOver" Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                                <GradientStop Color="#D8E8FF" Offset="0.0" />
                                                <GradientStop Color="#C8E0FF" Offset="1.0" />
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter Property="BorderBrush" Value="#B0D0F0" />
                                    <Setter Property="Foreground" Value="#333333" />
                                </MultiTrigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application> 