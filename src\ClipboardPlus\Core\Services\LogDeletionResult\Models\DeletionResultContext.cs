using System;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Models
{
    /// <summary>
    /// Contexte complet d'une opération de suppression.
    /// Remplace les paramètres individuels de l'ancienne méthode LogDeletionResult.
    /// </summary>
    public class DeletionResultContext
    {
        /// <summary>
        /// Identifiant unique de l'opération.
        /// </summary>
        public Guid OperationId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Indique si la suppression a réussi.
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Élément concerné par la suppression (peut être null).
        /// </summary>
        public ClipboardItem? Item { get; set; }

        /// <summary>
        /// Message optionnel associé à l'opération.
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// Timestamp de l'opération.
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// ID du thread qui exécute l'opération.
        /// </summary>
        public int ThreadId { get; set; } = Environment.CurrentManagedThreadId;

        /// <summary>
        /// ViewModel associé à l'opération (pour validation).
        /// </summary>
        public ClipboardHistoryViewModel? ViewModel { get; set; }

        /// <summary>
        /// Informations sur l'appelant (pour traçabilité).
        /// </summary>
        public CallerInfo? CallerInfo { get; set; }

        /// <summary>
        /// Contexte d'exécution supplémentaire.
        /// </summary>
        public ExecutionContext? ExecutionContext { get; set; }

        /// <summary>
        /// Crée un contexte pour une suppression réussie.
        /// </summary>
        /// <param name="item">Élément supprimé</param>
        /// <param name="viewModel">ViewModel associé</param>
        /// <param name="message">Message optionnel</param>
        /// <returns>Contexte configuré</returns>
        public static DeletionResultContext CreateSuccess(
            ClipboardItem item, 
            ClipboardHistoryViewModel? viewModel = null, 
            string? message = null)
        {
            return new DeletionResultContext
            {
                Success = true,
                Item = item,
                ViewModel = viewModel,
                Message = message,
                CallerInfo = CallerInfo.Capture(),
                ExecutionContext = ExecutionContext.Capture()
            };
        }

        /// <summary>
        /// Crée un contexte pour une suppression échouée.
        /// </summary>
        /// <param name="item">Élément concerné (peut être null)</param>
        /// <param name="message">Message d'erreur</param>
        /// <param name="viewModel">ViewModel associé</param>
        /// <returns>Contexte configuré</returns>
        public static DeletionResultContext CreateFailure(
            ClipboardItem? item, 
            string message, 
            ClipboardHistoryViewModel? viewModel = null)
        {
            return new DeletionResultContext
            {
                Success = false,
                Item = item,
                ViewModel = viewModel,
                Message = message,
                CallerInfo = CallerInfo.Capture(),
                ExecutionContext = ExecutionContext.Capture()
            };
        }

        /// <summary>
        /// Crée un contexte à partir des paramètres de l'ancienne méthode.
        /// </summary>
        /// <param name="success">Succès de l'opération</param>
        /// <param name="item">Élément concerné</param>
        /// <param name="message">Message optionnel</param>
        /// <returns>Contexte configuré</returns>
        public static DeletionResultContext FromLegacyParameters(
            bool success, 
            ClipboardItem? item, 
            string? message = null)
        {
            return new DeletionResultContext
            {
                Success = success,
                Item = item,
                Message = message,
                CallerInfo = CallerInfo.Capture(),
                ExecutionContext = ExecutionContext.Capture()
            };
        }
    }

    /// <summary>
    /// Informations sur l'appelant de l'opération.
    /// </summary>
    public class CallerInfo
    {
        /// <summary>
        /// Nom de la méthode appelante.
        /// </summary>
        public string? MethodName { get; set; }

        /// <summary>
        /// Nom de la classe appelante.
        /// </summary>
        public string? ClassName { get; set; }

        /// <summary>
        /// Nom du fichier source.
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// Numéro de ligne dans le fichier source.
        /// </summary>
        public int LineNumber { get; set; }

        /// <summary>
        /// Capture les informations de l'appelant actuel.
        /// </summary>
        /// <returns>Informations de l'appelant</returns>
        public static CallerInfo Capture()
        {
            try
            {
                var stackTrace = new System.Diagnostics.StackTrace(true);
                var frame = stackTrace.GetFrame(2); // Remonter de 2 niveaux

                return new CallerInfo
                {
                    MethodName = frame?.GetMethod()?.Name,
                    ClassName = frame?.GetMethod()?.DeclaringType?.Name,
                    FileName = frame?.GetFileName(),
                    LineNumber = frame?.GetFileLineNumber() ?? 0
                };
            }
            catch
            {
                return new CallerInfo
                {
                    MethodName = "Unknown",
                    ClassName = "Unknown"
                };
            }
        }
    }

    /// <summary>
    /// Contexte d'exécution de l'opération.
    /// </summary>
    public class ExecutionContext
    {
        /// <summary>
        /// Utilisation mémoire au moment de l'opération.
        /// </summary>
        public long MemoryUsage { get; set; }

        /// <summary>
        /// ID du processus.
        /// </summary>
        public int ProcessId { get; set; }

        /// <summary>
        /// Nom de l'application.
        /// </summary>
        public string? ApplicationName { get; set; }

        /// <summary>
        /// Version de l'application.
        /// </summary>
        public string? ApplicationVersion { get; set; }

        /// <summary>
        /// Capture le contexte d'exécution actuel.
        /// </summary>
        /// <returns>Contexte d'exécution</returns>
        public static ExecutionContext Capture()
        {
            try
            {
                var process = System.Diagnostics.Process.GetCurrentProcess();
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();

                return new ExecutionContext
                {
                    MemoryUsage = GC.GetTotalMemory(false),
                    ProcessId = process.Id,
                    ApplicationName = process.ProcessName,
                    ApplicationVersion = assembly.GetName().Version?.ToString()
                };
            }
            catch
            {
                return new ExecutionContext
                {
                    MemoryUsage = 0,
                    ProcessId = 0,
                    ApplicationName = "Unknown"
                };
            }
        }
    }
}
