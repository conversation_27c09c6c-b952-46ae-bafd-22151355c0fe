using System;
using System.Collections.Generic;
using ClipboardPlus.Core.Services.Diagnostics;
using ClipboardPlus.Services.Diagnostics;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.Diagnostics
{
    [TestFixture]
    public class DiagnosticFormatterTests
    {
        private DiagnosticFormatter _formatter = null!;

        [SetUp]
        public void SetUp()
        {
            _formatter = new DiagnosticFormatter();
        }

        [Test]
        public void FormatDiagnosticHeader_ReturnsFormattedHeader()
        {
            // Arrange
            var message = "Test de suppression";
            var timestamp = new DateTime(2025, 7, 15, 14, 30, 0);

            // Act
            var result = _formatter.FormatDiagnosticHeader(message, timestamp);

            // Assert
            Assert.That(result, Is.Not.Null.And.Not.Empty);
            Assert.That(result, Does.Contain("DÉBUT DIAGNOSTIC DE SUPPRESSION"));
            Assert.That(result, Does.Contain("2025-07-15 14:30:00"));
            Assert.That(result, Does.Contain("Test de suppression"));
            Assert.That(result, Does.Contain("Thread ID:"));
            Assert.That(result, Does.Contain("Application Version:"));
        }

        [Test]
        public void FormatItemInformation_WithNullItem_ReturnsNullItemFormat()
        {
            // Arrange
            var itemData = new DiagnosticData(
                new Dictionary<string, object> { { "IsNull", true } },
                new List<string> { "L'élément à supprimer est null" },
                new List<string>(),
                DateTime.Now
            );

            // Act
            var result = _formatter.FormatItemInformation(itemData);

            // Assert
            Assert.That(result, Is.Not.Null.And.Not.Empty);
            Assert.That(result, Does.Contain("INFORMATIONS ÉLÉMENT"));
            Assert.That(result, Does.Contain("Élément: NULL"));
            Assert.That(result, Does.Contain("⚠️ Avertissements:"));
            Assert.That(result, Does.Contain("L'élément à supprimer est null"));
        }

        [Test]
        public void FormatItemInformation_WithValidItem_ReturnsCompleteFormat()
        {
            // Arrange
            var itemData = new DiagnosticData(
                new Dictionary<string, object>
                {
                    { "Id", 123 },
                    { "DataType", "Text" },
                    { "IsPinned", true },
                    { "CustomName", "Test Item" },
                    { "TextPreview", "Test content" },
                    { "HasRawData", false },
                    { "RawDataSize", 0 }
                },
                new List<string>(),
                new List<string>(),
                DateTime.Now
            );

            // Act
            var result = _formatter.FormatItemInformation(itemData);

            // Assert
            Assert.That(result, Is.Not.Null.And.Not.Empty);
            Assert.That(result, Does.Contain("ID: 123"));
            Assert.That(result, Does.Contain("Type: Text"));
            Assert.That(result, Does.Contain("Épinglé: True"));
            Assert.That(result, Does.Contain("Nom personnalisé: Test Item"));
            Assert.That(result, Does.Contain("Aperçu: Test content"));
            Assert.That(result, Does.Contain("Données brutes: Non (0 bytes)"));
        }

        [Test]
        public void FormatViewModelInformation_ReturnsFormattedViewModelInfo()
        {
            // Arrange
            var viewModelData = new DiagnosticData(
                new Dictionary<string, object>
                {
                    { "HistoryItemsCount", 5 },
                    { "HasSelectedItem", true },
                    { "SelectedItemId", 42 },
                    { "IsLoading", false },
                    { "PinnedItemsCount", 2 },
                    { "VisibleTitleCount", 3 },
                    { "HasDeleteCommand", true },
                    { "HasClearAllCommand", true },
                    { "HasPasteCommand", true }
                },
                new List<string> { "Test warning" },
                new List<string>(),
                DateTime.Now
            );

            // Act
            var result = _formatter.FormatViewModelInformation(viewModelData);

            // Assert
            Assert.That(result, Is.Not.Null.And.Not.Empty);
            Assert.That(result, Does.Contain("INFORMATIONS VIEWMODEL"));
            Assert.That(result, Does.Contain("Nombre d'éléments: 5"));
            Assert.That(result, Does.Contain("Élément sélectionné: Oui (ID: 42)"));
            Assert.That(result, Does.Contain("En cours de chargement: False"));
            Assert.That(result, Does.Contain("Éléments épinglés: 2"));
            Assert.That(result, Does.Contain("Supprimer élément: Disponible"));
            Assert.That(result, Does.Contain("⚠️ Avertissements:"));
            Assert.That(result, Does.Contain("Test warning"));
        }

        [Test]
        public void FormatSystemInformation_ReturnsFormattedSystemInfo()
        {
            // Arrange
            var systemData = new DiagnosticData(
                new Dictionary<string, object>
                {
                    { "ThreadId", 42 },
                    { "Timestamp", DateTime.Now },
                    { "ApplicationVersion", "1.0.0" },
                    { "WorkingSet", 100 * 1024 * 1024L }, // 100MB
                    { "ProcessorCount", 8 },
                    { "OSVersion", "Windows 11" },
                    { "Is64BitProcess", true }
                },
                new List<string>(),
                new List<string>(),
                DateTime.Now
            );

            // Act
            var result = _formatter.FormatSystemInformation(systemData);

            // Assert
            Assert.That(result, Is.Not.Null.And.Not.Empty);
            Assert.That(result, Does.Contain("INFORMATIONS SYSTÈME"));
            Assert.That(result, Does.Contain("Thread ID: 42"));
            Assert.That(result, Does.Contain("Version application: 1.0.0"));
            Assert.That(result, Does.Contain("Mémoire utilisée: 100,0 MB")); // Comportement réel : virgule française
            Assert.That(result, Does.Contain("Processeurs: 8"));
            Assert.That(result, Does.Contain("OS: Windows 11"));
            Assert.That(result, Does.Contain("64-bit: True"));
        }

        [Test]
        public void AssembleDiagnosticReport_CombinesAllSections()
        {
            // Arrange
            var header = "=== HEADER ===\n";
            var itemInfo = "=== ITEM ===\n";
            var viewModelInfo = "=== VIEWMODEL ===\n";
            var systemInfo = "=== SYSTEM ===\n";

            // Act
            var result = _formatter.AssembleDiagnosticReport(header, itemInfo, viewModelInfo, systemInfo);

            // Assert
            Assert.That(result, Is.Not.Null.And.Not.Empty);
            Assert.That(result, Does.Contain("=== HEADER ==="));
            Assert.That(result, Does.Contain("=== ITEM ==="));
            Assert.That(result, Does.Contain("=== VIEWMODEL ==="));
            Assert.That(result, Does.Contain("=== SYSTEM ==="));
            Assert.That(result, Does.Contain("FIN DIAGNOSTIC DE SUPPRESSION"));
        }
    }
}
