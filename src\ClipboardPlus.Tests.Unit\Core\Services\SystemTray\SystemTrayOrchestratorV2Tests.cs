using System;
using System.Drawing;
using System.Threading;
using System.Windows.Controls;
using System.Windows.Forms;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;
using ClipboardPlus.Core.Services.Windows;

namespace ClipboardPlus.Tests.Unit.Core.Services.SystemTray
{
    /// <summary>
    /// Tests unitaires pour les nouvelles méthodes V2 de SystemTrayOrchestrator.
    /// Ces tests vérifient que les nouvelles méthodes utilisent l'injection de dépendances
    /// au lieu du Service Locator pattern.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class SystemTrayOrchestratorV2Tests
    {
        private Mock<IThreadValidator> _mockThreadValidator = null!;
        private Mock<INotifyIconCleanupService> _mockCleanupService = null!;
        private Mock<INotifyIconFactory> _mockNotifyIconFactory = null!;
        private Mock<IIconResourceLoader> _mockIconLoader = null!;
        private Mock<IContextMenuBuilder> _mockMenuBuilder = null!;
        private Mock<IVisibilityManager> _mockVisibilityManager = null!;
        private Mock<IStartupNotificationService> _mockNotificationService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IServiceProvider> _mockServiceProvider = null!;
        private Mock<IHistoryWindowService> _mockHistoryWindowService = null!;
        private Mock<ISettingsWindowService> _mockSettingsWindowService = null!;
        private SystemTrayOrchestrator _orchestrator = null!;

        [SetUp]
        public void SetUp()
        {
            _mockThreadValidator = new Mock<IThreadValidator>();
            _mockCleanupService = new Mock<INotifyIconCleanupService>();
            _mockNotifyIconFactory = new Mock<INotifyIconFactory>();
            _mockIconLoader = new Mock<IIconResourceLoader>();
            _mockMenuBuilder = new Mock<IContextMenuBuilder>();
            _mockVisibilityManager = new Mock<IVisibilityManager>();
            _mockNotificationService = new Mock<IStartupNotificationService>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockHistoryWindowService = new Mock<IHistoryWindowService>();
            _mockSettingsWindowService = new Mock<ISettingsWindowService>();

            _orchestrator = new SystemTrayOrchestrator(
                _mockThreadValidator.Object,
                _mockCleanupService.Object,
                _mockNotifyIconFactory.Object,
                _mockIconLoader.Object,
                _mockMenuBuilder.Object,
                _mockVisibilityManager.Object,
                _mockNotificationService.Object,
                _mockLoggingService.Object,
                _mockServiceProvider.Object,
                _mockHistoryWindowService.Object,
                _mockSettingsWindowService.Object);
        }

        [Test]
        public void Initialize_V2_CallsAllRequiredServices()
        {
            // Arrange
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();

            _mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(false);
            _mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            _mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            _mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>())).Returns(mockContextMenu);
            _mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            // Act
            _orchestrator.Initialize_V2();

            // Assert
            _mockThreadValidator.Verify(x => x.ValidateUIThread(), Times.Once);
            _mockCleanupService.Verify(x => x.RequiresCleanup(It.IsAny<NotifyIcon>()), Times.Once);
            _mockNotifyIconFactory.Verify(x => x.CreateNotifyIcon("ClipboardPlus", true), Times.Once);
            _mockIconLoader.Verify(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico"), Times.Once);
            _mockMenuBuilder.Verify(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()), Times.Once);
            _mockVisibilityManager.Verify(x => x.ConfigureInitialVisibility(realNotifyIcon), Times.Once);

            // Cleanup
            realNotifyIcon.Dispose();
        }

        [Test]
        public void Initialize_V2_UsesInjectedServices_NotServiceLocator()
        {
            // Arrange
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();
            
            Action? capturedHistoryAction = null;
            Action? capturedSettingsAction = null;

            _mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(false);
            _mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            _mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            
            // CAPTURE des actions passées au menu builder
            _mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()))
                           .Callback<Action, Action, Action>((historyAction, settingsAction, exitAction) => 
                           {
                               capturedHistoryAction = historyAction;
                               capturedSettingsAction = settingsAction;
                           })
                           .Returns(mockContextMenu);
            
            _mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            // Act
            _orchestrator.Initialize_V2();

            // Simuler les clics sur les actions capturées
            capturedHistoryAction?.Invoke();
            capturedSettingsAction?.Invoke();

            // Assert - Vérifier que les services injectés sont utilisés
            _mockHistoryWindowService.Verify(x => x.ShowHistoryWindowAsync(), Times.Once, 
                "Initialize_V2 doit utiliser IHistoryWindowService injecté");
            _mockSettingsWindowService.Verify(x => x.OpenSettingsWindowAsync(), Times.Once, 
                "Initialize_V2 doit utiliser ISettingsWindowService injecté");

            // Vérifier que le Service Locator n'est PAS utilisé
            _mockServiceProvider.Verify(x => x.GetService(It.IsAny<Type>()), Times.Never, 
                "Initialize_V2 ne doit PAS utiliser le Service Locator");

            // Cleanup
            realNotifyIcon.Dispose();
        }

        [TearDown]
        public void TearDown()
        {
            _orchestrator?.Dispose();
        }
    }
}
