using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service responsable des tâches de maintenance périodique de l'historique.
    /// 
    /// Ce service extrait la logique de maintenance aléatoire de la méthode
    /// ClipboardHistoryManager_HistoryChanged originale pour la rendre testable et configurable.
    /// </summary>
    public interface IHistoryMaintenanceService
    {
        /// <summary>
        /// Déclenche les tâches de maintenance si nécessaire selon la probabilité configurée.
        /// 
        /// Reproduit la logique originale de déclenchement aléatoire de la purge (5% de chance).
        /// </summary>
        /// <param name="eventId">Identifiant de l'événement pour le logging</param>
        /// <returns>True si une tâche de maintenance a été déclenchée, False sinon</returns>
        Task<bool> TriggerMaintenanceIfNeededAsync(string eventId);

        /// <summary>
        /// Configure la probabilité de déclenchement de la maintenance.
        /// </summary>
        /// <param name="probabilityPercent">Probabilité en pourcentage (0-100)</param>
        void SetMaintenanceProbability(int probabilityPercent);

        /// <summary>
        /// Obtient la probabilité actuelle de déclenchement de la maintenance.
        /// </summary>
        /// <returns>Probabilité en pourcentage (0-100)</returns>
        int GetMaintenanceProbability();
    }
}
