using System;
using System.Runtime.InteropServices;

namespace ClipboardPlus.Utils
{
    /// <summary>
    /// Classe contenant les méthodes natives utilisées pour la communication entre instances
    /// </summary>
    internal static class AppNativeMethods
    {
        // Message personnalisé pour la communication entre instances
        public const int WM_SHOWME = 0x0400 + 0x042; // Message personnalisé WM_USER + 0x042
        public const int HWND_BROADCAST = 0xFFFF;
        
        [DllImport("user32.dll")]
        public static extern bool PostMessage(IntPtr hwnd, int msg, IntPtr wparam, IntPtr lparam);
    }
} 