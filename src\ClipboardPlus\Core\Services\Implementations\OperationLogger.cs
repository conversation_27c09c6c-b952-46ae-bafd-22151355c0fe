using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;
using System.Diagnostics;

namespace ClipboardPlus.Core.Services.Implementations
{
    /// <summary>
    /// Service de journalisation des opérations.
    /// Responsabilité unique : Enregistrer les logs des opérations.
    /// </summary>
    public class OperationLogger : IOperationLogger
    {
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du service de journalisation d'opérations.
        /// </summary>
        /// <param name="loggingService">Service de logging pour traçabilité</param>
        public OperationLogger(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Démarre une nouvelle opération et retourne un contexte de logging.
        /// </summary>
        /// <param name="operationName">Nom de l'opération</param>
        /// <param name="item">Élément concerné par l'opération</param>
        /// <returns>Contexte de logging pour cette opération</returns>
        public IOperationContext StartOperation(string operationName, ClipboardItem? item = null)
        {
            if (string.IsNullOrWhiteSpace(operationName))
            {
                throw new ArgumentException("Le nom de l'opération ne peut pas être vide", nameof(operationName));
            }

            var operationId = Guid.NewGuid().ToString()[..8];
            var context = new OperationContext(operationId, operationName, item, _loggingService);
            
            _loggingService?.LogInfo($"[{operationId}] OperationLogger.StartOperation - Début de l'opération '{operationName}'" + 
                                   (item != null ? $" pour l'élément ID={item.Id}" : ""));

            return context;
        }
    }

    /// <summary>
    /// Contexte d'une opération en cours.
    /// </summary>
    internal class OperationContext : IOperationContext
    {
        private readonly string _operationName;
        private readonly ClipboardItem? _item;
        private readonly ILoggingService? _loggingService;
        private readonly Stopwatch _stopwatch;
        private bool _isCompleted;
        private bool _disposed;

        /// <summary>
        /// ID unique de l'opération.
        /// </summary>
        public string OperationId { get; }

        /// <summary>
        /// Initialise un nouveau contexte d'opération.
        /// </summary>
        /// <param name="operationId">ID unique de l'opération</param>
        /// <param name="operationName">Nom de l'opération</param>
        /// <param name="item">Élément concerné par l'opération</param>
        /// <param name="loggingService">Service de logging</param>
        public OperationContext(string operationId, string operationName, ClipboardItem? item, ILoggingService? loggingService)
        {
            OperationId = operationId ?? throw new ArgumentNullException(nameof(operationId));
            _operationName = operationName ?? throw new ArgumentNullException(nameof(operationName));
            _item = item;
            _loggingService = loggingService;
            _stopwatch = Stopwatch.StartNew();
            _isCompleted = false;
            _disposed = false;
        }

        /// <summary>
        /// Enregistre une information pendant l'opération.
        /// </summary>
        /// <param name="message">Message à enregistrer</param>
        public void LogInfo(string message)
        {
            ThrowIfDisposed();
            if (string.IsNullOrWhiteSpace(message))
            {
                throw new ArgumentException("Le message ne peut pas être vide", nameof(message));
            }

            _loggingService?.LogInfo($"[{OperationId}] {_operationName} - {message}");
        }

        /// <summary>
        /// Enregistre un avertissement pendant l'opération.
        /// </summary>
        /// <param name="message">Message d'avertissement</param>
        public void LogWarning(string message)
        {
            ThrowIfDisposed();
            if (string.IsNullOrWhiteSpace(message))
            {
                throw new ArgumentException("Le message ne peut pas être vide", nameof(message));
            }

            _loggingService?.LogWarning($"[{OperationId}] {_operationName} - {message}");
        }

        /// <summary>
        /// Enregistre une erreur pendant l'opération.
        /// </summary>
        /// <param name="message">Message d'erreur</param>
        /// <param name="exception">Exception optionnelle</param>
        public void LogError(string message, Exception? exception = null)
        {
            ThrowIfDisposed();
            if (string.IsNullOrWhiteSpace(message))
            {
                throw new ArgumentException("Le message ne peut pas être vide", nameof(message));
            }

            _loggingService?.LogError($"[{OperationId}] {_operationName} - {message}", exception);
        }

        /// <summary>
        /// Marque l'opération comme terminée avec succès.
        /// </summary>
        /// <param name="result">Résultat de l'opération</param>
        public void Complete(object? result = null)
        {
            ThrowIfDisposed();
            if (_isCompleted)
            {
                return; // Déjà terminée
            }

            _isCompleted = true;
            _stopwatch.Stop();

            var resultInfo = result != null ? $" avec résultat: {result}" : "";
            _loggingService?.LogInfo($"[{OperationId}] {_operationName} - Opération terminée avec succès en {_stopwatch.ElapsedMilliseconds}ms{resultInfo}");
        }

        /// <summary>
        /// Marque l'opération comme échouée.
        /// </summary>
        /// <param name="error">Erreur qui a causé l'échec</param>
        public void Fail(string error)
        {
            ThrowIfDisposed();
            if (string.IsNullOrWhiteSpace(error))
            {
                throw new ArgumentException("L'erreur ne peut pas être vide", nameof(error));
            }

            if (_isCompleted)
            {
                return; // Déjà terminée
            }

            _isCompleted = true;
            _stopwatch.Stop();

            _loggingService?.LogError($"[{OperationId}] {_operationName} - Opération échouée après {_stopwatch.ElapsedMilliseconds}ms: {error}");
        }

        /// <summary>
        /// Libère les ressources utilisées par le contexte.
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            if (!_isCompleted)
            {
                // L'opération n'a pas été explicitement terminée, on la marque comme incomplète
                _stopwatch.Stop();
                _loggingService?.LogWarning($"[{OperationId}] {_operationName} - Opération non terminée explicitement après {_stopwatch.ElapsedMilliseconds}ms");
            }

            _stopwatch?.Stop();
            _disposed = true;
        }

        /// <summary>
        /// Vérifie si le contexte a été libéré et lève une exception si c'est le cas.
        /// </summary>
        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(OperationContext));
            }
        }
    }
}
