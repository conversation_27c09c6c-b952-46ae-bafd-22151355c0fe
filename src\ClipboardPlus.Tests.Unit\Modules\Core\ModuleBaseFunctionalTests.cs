using NUnit.Framework;
using ClipboardPlus.Modules.Core;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ModuleState = ClipboardPlus.Modules.Core.ModuleState;

namespace ClipboardPlus.Tests.Unit.Modules.Core
{
    /// <summary>
    /// Test fonctionnel pour ModuleBase - vérifie le comportement métier
    /// dans des scénarios d'usage réels incluant les cas d'erreur et chemins non couverts
    /// </summary>
    [TestFixture]
    public class ModuleBaseFunctionalTests
    {
        private TestModule _testModule;

        [SetUp]
        public void SetUp()
        {
            _testModule = new TestModule();
        }

        [TearDown]
        public void TearDown()
        {
            _testModule?.Dispose();
        }

        [Test]
        public async Task ModuleBase_CompleteErrorScenarioWorkflow_ShouldHandleAllErrorCasesCorrectly()
        {
            // Arrange - Scénario : Workflow complet avec tous les cas d'erreur et chemins non couverts

            // Phase 1: Test des propriétés de base
            Assert.That(_testModule.ModuleName, Is.EqualTo("TestModule"));
            Assert.That(_testModule.ModuleVersion, Is.EqualTo(new Version(1, 0, 0)));
            Assert.That(_testModule.State, Is.EqualTo(ModuleState.Created));
            Assert.That(_testModule.IsInitialized, Is.False);
            Assert.That(_testModule.IsRunning, Is.False);

            // Phase 2: Test workflow normal d'abord
            await _testModule.InitializeAsync();
            Assert.That(_testModule.State, Is.EqualTo(ModuleState.Initialized));
            Assert.That(_testModule.IsInitialized, Is.True);

            // Phase 3: Test CheckHealth avec custom health data
            _testModule.SetCustomHealthData(new Dictionary<string, object>
            {
                ["CustomMetric"] = 42,
                ["Status"] = "OK"
            });

            var healthResult = _testModule.CheckHealth();
            Assert.That(healthResult.IsHealthy, Is.True);
            Assert.That(healthResult.Details.ContainsKey("CustomMetric"), Is.True);
            Assert.That(healthResult.Details["CustomMetric"], Is.EqualTo(42));
            Assert.That(healthResult.Details["Status"], Is.EqualTo("OK"));

            // Phase 4: Test des erreurs d'initialisation - créer un nouveau module pour tester l'erreur d'init
            var failingModule = new TestModule();
            failingModule.ShouldFailOnInitialize = true;
            Assert.ThrowsAsync<ModuleInitializationException>(
                async () => await failingModule.InitializeAsync());

            Assert.That(failingModule.State, Is.EqualTo(ModuleState.Error));

            // Phase 5: Test CheckHealth en état d'erreur
            var errorHealthResult = failingModule.CheckHealth();
            Assert.That(errorHealthResult.IsHealthy, Is.False);
            Assert.That(errorHealthResult.Message, Contains.Substring("Error"));

            failingModule.Dispose();

            // Phase 6: Test workflow normal - démarrage
            await _testModule.StartAsync();
            Assert.That(_testModule.State, Is.EqualTo(ModuleState.Running));
            Assert.That(_testModule.IsRunning, Is.True);

            // Phase 7: Test CheckHealth avec exception
            _testModule.ShouldFailOnHealthCheck = true;
            var unhealthyResult = _testModule.CheckHealth();
            Assert.That(unhealthyResult.IsHealthy, Is.False);
            Assert.That(unhealthyResult.Message, Contains.Substring("Health check failed"));

            // Phase 8: Test Dispose avec erreur dans OnDispose
            _testModule.ShouldFailOnDispose = true;
            Assert.DoesNotThrow(() => _testModule.Dispose()); // Dispose ne doit pas lancer d'exception
            Assert.That(_testModule.State, Is.EqualTo(ModuleState.Disposed));

            // Phase 9: Test ThrowIfDisposed après dispose
            Assert.Throws<ObjectDisposedException>(() => _testModule.CheckHealth());
        }

        #region Classe de test pour ModuleBase

        /// <summary>
        /// Implémentation de test de ModuleBase pour les tests fonctionnels
        /// </summary>
        private class TestModule : ModuleBase
        {
            public override string ModuleName => "TestModule";
            public override Version ModuleVersion => new Version(1, 0, 0);

            public bool ShouldFailOnInitialize { get; set; }
            public bool ShouldFailOnStart { get; set; }
            public bool ShouldFailOnStop { get; set; }
            public bool ShouldFailOnReset { get; set; }
            public bool ShouldFailOnHealthCheck { get; set; }
            public bool ShouldFailOnDispose { get; set; }

            private Dictionary<string, object>? _customHealthData;

            public void SetCustomHealthData(Dictionary<string, object> data)
            {
                _customHealthData = data;
            }

            protected override Task OnInitializeAsync()
            {
                if (ShouldFailOnInitialize)
                    throw new InvalidOperationException("Test initialization failure");
                return Task.CompletedTask;
            }

            protected override Task OnStartAsync()
            {
                if (ShouldFailOnStart)
                    throw new InvalidOperationException("Test start failure");
                return Task.CompletedTask;
            }

            protected override Task OnStopAsync()
            {
                if (ShouldFailOnStop)
                    throw new InvalidOperationException("Test stop failure");
                return Task.CompletedTask;
            }

            protected override Task OnResetAsync()
            {
                if (ShouldFailOnReset)
                    throw new InvalidOperationException("Test reset failure");
                return Task.CompletedTask;
            }

            protected override Dictionary<string, object>? OnCheckHealth()
            {
                if (ShouldFailOnHealthCheck)
                    throw new InvalidOperationException("Test health check failure");
                return _customHealthData;
            }

            protected override void OnDispose()
            {
                if (ShouldFailOnDispose)
                    throw new InvalidOperationException("Test dispose failure");
            }
        }

        #endregion
    }
}
