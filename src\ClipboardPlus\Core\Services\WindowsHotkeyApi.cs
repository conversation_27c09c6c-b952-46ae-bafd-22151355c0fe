using System;
using System.Runtime.InteropServices;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Implémentation réelle utilisant l'API Windows native pour la gestion des raccourcis globaux.
    /// </summary>
    public class WindowsHotkeyApi : IWindowsHotkeyApi
    {
        private readonly ILoggingService? _loggingService;

        public WindowsHotkeyApi(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }
        /// <summary>
        /// Enregistre un raccourci clavier global avec Windows via l'API native.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre qui recevra les messages</param>
        /// <param name="id">Identifiant unique du raccourci</param>
        /// <param name="modifiers">Modificateurs (Ctrl, Alt, Shift, Win)</param>
        /// <param name="vk">Code de la touche virtuelle</param>
        /// <returns>True si l'enregistrement réussit, False sinon</returns>
        public bool RegisterHotKey(IntPtr hWnd, int id, uint modifiers, uint vk)
        {
            _loggingService?.LogInfo("[VERIFICATION_TEST_2025] WindowsHotkeyApi.RegisterHotKey APPELÉE");
            try
            {
                return NativeMethods.RegisterHotKey(hWnd, id, (int)modifiers, (int)vk);
            }
            catch (Exception)
            {
                // En cas d'exception lors de l'appel à l'API Windows, retourner false
                return false;
            }
        }

        /// <summary>
        /// Désenregistre un raccourci clavier global via l'API Windows native.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre</param>
        /// <param name="id">Identifiant du raccourci à désenregistrer</param>
        /// <returns>True si le désenregistrement réussit, False sinon</returns>
        public bool UnregisterHotKey(IntPtr hWnd, int id)
        {
            try
            {
                return NativeMethods.UnregisterHotKey(hWnd, id);
            }
            catch (Exception)
            {
                // En cas d'exception lors de l'appel à l'API Windows, retourner false
                return false;
            }
        }

        /// <summary>
        /// Méthodes natives pour l'enregistrement des raccourcis globaux.
        /// </summary>
        private static class NativeMethods
        {
            [DllImport("user32.dll", SetLastError = true)]
            public static extern bool RegisterHotKey(IntPtr hWnd, int id, int fsModifiers, int vk);

            [DllImport("user32.dll", SetLastError = true)]
            public static extern bool UnregisterHotKey(IntPtr hWnd, int id);
        }
    }
}
