using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels.Settings;

namespace ClipboardPlus.Core.Services.Settings
{
    /// <summary>
    /// Service orchestrateur responsable de la persistance des paramètres.
    /// Coordonne la sauvegarde de tous les types de paramètres.
    /// </summary>
    public interface ISettingsPersistenceOrchestrator
    {
        /// <summary>
        /// Sauvegarde tous les paramètres de manière orchestrée.
        /// </summary>
        /// <param name="settings">Données complètes des paramètres à sauvegarder</param>
        /// <returns>Résultat de la persistance</returns>
        Task<PersistenceResult> SaveAllSettingsAsync(CompleteSettingsData settings);

        /// <summary>
        /// Sauvegarde des paramètres spécifiques de manière sélective.
        /// </summary>
        /// <param name="settings">Données partielles des paramètres à sauvegarder</param>
        /// <returns>Résultat de la persistance</returns>
        Task<PersistenceResult> SaveSpecificSettingsAsync(PartialSettingsData settings);
    }
}
