using System;
using System.Drawing;
using System.Threading;
using System.Windows.Controls;
using System.Windows.Forms;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;
using ClipboardPlus.Core.Services.Windows;

namespace ClipboardPlus.Tests.Unit.Core.Services.SystemTray
{
    /// <summary>
    /// HARNAIS DE SÉCURITÉ pour le refactoring Service Locator de SystemTrayOrchestrator.
    /// Ces tests verrouillent le comportement actuel des méthodes HandleHistoryClick et HandleSettingsClick
    /// avant leur refactorisation pour éliminer l'anti-pattern Service Locator.
    /// 
    /// OBJECTIF: S'assurer qu'aucune régression fonctionnelle n'est introduite lors du refactoring.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class SystemTrayOrchestratorServiceLocatorHarnessTests
    {
        private Mock<IThreadValidator> _mockThreadValidator = null!;
        private Mock<INotifyIconCleanupService> _mockCleanupService = null!;
        private Mock<INotifyIconFactory> _mockNotifyIconFactory = null!;
        private Mock<IIconResourceLoader> _mockIconLoader = null!;
        private Mock<IContextMenuBuilder> _mockMenuBuilder = null!;
        private Mock<IVisibilityManager> _mockVisibilityManager = null!;
        private Mock<IStartupNotificationService> _mockNotificationService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IServiceProvider> _mockServiceProvider = null!;
        private Mock<ISystemTrayService> _mockSystemTrayService = null!;
        private Mock<IHistoryWindowService> _mockHistoryWindowService = null!;
        private Mock<ISettingsWindowService> _mockSettingsWindowService = null!;
        private SystemTrayOrchestrator _orchestrator = null!;

        [SetUp]
        public void SetUp()
        {
            _mockThreadValidator = new Mock<IThreadValidator>();
            _mockCleanupService = new Mock<INotifyIconCleanupService>();
            _mockNotifyIconFactory = new Mock<INotifyIconFactory>();
            _mockIconLoader = new Mock<IIconResourceLoader>();
            _mockMenuBuilder = new Mock<IContextMenuBuilder>();
            _mockVisibilityManager = new Mock<IVisibilityManager>();
            _mockNotificationService = new Mock<IStartupNotificationService>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockSystemTrayService = new Mock<ISystemTrayService>();
            _mockHistoryWindowService = new Mock<IHistoryWindowService>();
            _mockSettingsWindowService = new Mock<ISettingsWindowService>();

            // Configuration du Service Locator mock pour retourner le service SystemTray
            _mockServiceProvider
                .Setup(sp => sp.GetService(typeof(ISystemTrayService)))
                .Returns(_mockSystemTrayService.Object);

            _orchestrator = new SystemTrayOrchestrator(
                _mockThreadValidator.Object,
                _mockCleanupService.Object,
                _mockNotifyIconFactory.Object,
                _mockIconLoader.Object,
                _mockMenuBuilder.Object,
                _mockVisibilityManager.Object,
                _mockNotificationService.Object,
                _mockLoggingService.Object,
                _mockServiceProvider.Object,
                _mockHistoryWindowService.Object,
                _mockSettingsWindowService.Object);
        }

        /// <summary>
        /// HARNAIS DE SÉCURITÉ: Teste que le clic sur "Afficher l'historique" appelle bien ShowHistoryWindow().
        /// Ce test verrouille le comportement actuel avant refactoring.
        /// </summary>
        [Test]
        public void HandleHistoryClick_CallsSystemTrayServiceShowHistoryWindow()
        {
            // Arrange
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();
            
            Action capturedHistoryAction = null;

            // Configuration des mocks pour l'initialisation
            _mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(false);
            _mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            _mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            
            // CAPTURE de l'action HandleHistoryClick passée au menu builder
            _mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()))
                           .Callback<Action, Action, Action>((historyAction, settingsAction, exitAction) => 
                           {
                               capturedHistoryAction = historyAction;
                           })
                           .Returns(mockContextMenu);
            
            _mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            // Initialiser l'orchestrateur pour capturer les actions
            _orchestrator.Initialize();

            // Act - Simuler le clic sur "Afficher l'historique"
            capturedHistoryAction?.Invoke();

            // Assert - Vérifier que ShowHistoryWindow a été appelée
            _mockSystemTrayService.Verify(
                x => x.ShowHistoryWindow(), 
                Times.Once, 
                "HandleHistoryClick doit appeler ShowHistoryWindow() sur ISystemTrayService");

            // Vérifier que le logging approprié a eu lieu
            _mockLoggingService.Verify(
                x => x.LogInfo("SystemTrayOrchestrator: Gestion du clic sur 'Afficher l'historique'"),
                Times.Once,
                "HandleHistoryClick doit logger le début de l'opération");

            // Cleanup
            realNotifyIcon.Dispose();
        }

        /// <summary>
        /// HARNAIS DE SÉCURITÉ: Teste que le clic sur "Paramètres" appelle bien OpenSettingsWindow().
        /// Ce test verrouille le comportement actuel avant refactoring.
        /// </summary>
        [Test]
        public void HandleSettingsClick_CallsSystemTrayServiceOpenSettingsWindow()
        {
            // Arrange
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();
            
            Action capturedSettingsAction = null;

            // Configuration des mocks pour l'initialisation
            _mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(false);
            _mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            _mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            
            // CAPTURE de l'action HandleSettingsClick passée au menu builder
            _mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()))
                           .Callback<Action, Action, Action>((historyAction, settingsAction, exitAction) => 
                           {
                               capturedSettingsAction = settingsAction;
                           })
                           .Returns(mockContextMenu);
            
            _mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            // Initialiser l'orchestrateur pour capturer les actions
            _orchestrator.Initialize();

            // Act - Simuler le clic sur "Paramètres"
            capturedSettingsAction?.Invoke();

            // Assert - Vérifier que OpenSettingsWindow a été appelée
            _mockSystemTrayService.Verify(
                x => x.OpenSettingsWindow(), 
                Times.Once, 
                "HandleSettingsClick doit appeler OpenSettingsWindow() sur ISystemTrayService");

            // Vérifier que le logging approprié a eu lieu
            _mockLoggingService.Verify(
                x => x.LogInfo("SystemTrayOrchestrator: Gestion du clic sur 'Paramètres'"),
                Times.Once,
                "HandleSettingsClick doit logger le début de l'opération");

            // Cleanup
            realNotifyIcon.Dispose();
        }

        /// <summary>
        /// HARNAIS DE SÉCURITÉ: Teste la gestion d'erreur quand IServiceProvider.GetService() retourne null.
        /// Ce test verrouille le comportement de robustesse actuel.
        /// </summary>
        [Test]
        public void HandleHistoryClick_WhenServiceProviderReturnsNull_DoesNotThrow()
        {
            // Arrange
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();
            
            Action? capturedHistoryAction = null;

            // Configuration pour que GetService retourne null
            _mockServiceProvider
                .Setup(sp => sp.GetService(typeof(ISystemTrayService)))
                .Returns((object?)null);

            // Configuration des mocks pour l'initialisation
            _mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(false);
            _mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            _mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            
            _mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()))
                           .Callback<Action, Action, Action>((historyAction, settingsAction, exitAction) => 
                           {
                               capturedHistoryAction = historyAction;
                           })
                           .Returns(mockContextMenu);
            
            _mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            _orchestrator.Initialize();

            // Act & Assert - Ne doit pas lever d'exception
            Assert.DoesNotThrow(() => capturedHistoryAction?.Invoke(),
                "HandleHistoryClick doit gérer gracieusement le cas où GetService retourne null");

            // Cleanup
            realNotifyIcon.Dispose();
        }

        [TearDown]
        public void TearDown()
        {
            _orchestrator?.Dispose();
        }
    }
}
