using System.Windows.Media.Imaging;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services.ContentHandlers
{
    /// <summary>
    /// Handler pour le contenu image.
    /// Remplace la logique LoadImageContent() du ContentPreviewViewModel.
    /// </summary>
    public class ImageContentHandler : BaseContentHandler
    {
        private readonly IBitmapImageFactory _bitmapImageFactory;

        /// <summary>
        /// Initialise une nouvelle instance de ImageContentHandler.
        /// </summary>
        /// <param name="bitmapImageFactory">Factory pour créer les BitmapImage</param>
        /// <param name="loggingService">Service de logging optionnel</param>
        public ImageContentHandler(IBitmapImageFactory bitmapImageFactory, ILoggingService? loggingService = null) 
            : base(loggingService)
        {
            _bitmapImageFactory = bitmapImageFactory ?? throw new ArgumentNullException(nameof(bitmapImageFactory));
        }

        /// <inheritdoc />
        public override ClipboardDataType SupportedDataType => ClipboardDataType.Image;

        /// <inheritdoc />
        public override string GetDefaultUnavailableMessage()
        {
            return "Aperçu de l'image non disponible (pas de données).";
        }

        /// <inheritdoc />
        protected override object HandleContentInternal(ClipboardItem item)
        {
            _loggingService?.LogInfo($"ImageContentHandler.HandleContentInternal - Traitement du contenu image pour l'élément ID {item.Id}");

            // Priorité 1: Créer BitmapImage à partir de RawData si disponible
            if (item.RawData != null && item.RawData.Length > 0)
            {
                _loggingService?.LogInfo($"ImageContentHandler - Données brutes disponibles, taille: {item.RawData.Length} octets");
                
                // Validation des données d'image
                if (_bitmapImageFactory.IsValidImageData(item.RawData))
                {
                    _loggingService?.LogInfo("ImageContentHandler - Données d'image valides détectées");
                    
                    BitmapImage? bitmap = _bitmapImageFactory.CreateFromBytes(item.RawData);
                    if (bitmap != null)
                    {
                        _loggingService?.LogInfo($"ImageContentHandler - BitmapImage créée avec succès: {bitmap.PixelWidth}x{bitmap.PixelHeight}");
                        return bitmap;
                    }
                    else
                    {
                        _loggingService?.LogWarning("ImageContentHandler - Échec de la création de BitmapImage à partir des données brutes");
                    }
                }
                else
                {
                    _loggingService?.LogWarning("ImageContentHandler - Les données brutes ne sont pas des données d'image valides");
                }
            }
            else
            {
                _loggingService?.LogWarning("ImageContentHandler - Données brutes non disponibles");
            }

            // Priorité 2: Utiliser ThumbnailSource si disponible
            if (item.ThumbnailSource != null)
            {
                _loggingService?.LogInfo("ImageContentHandler - Utilisation de ThumbnailSource existant");
                return item.ThumbnailSource;
            }

            // Priorité 3: Message par défaut si aucune donnée d'image n'est disponible
            string defaultMessage = GetDefaultUnavailableMessage();
            _loggingService?.LogInfo($"ImageContentHandler - Aucune donnée d'image disponible, utilisation du message par défaut: '{defaultMessage}'");
            
            return defaultMessage;
        }

        /// <inheritdoc />
        public override bool CanHandle(ClipboardItem item)
        {
            if (!base.CanHandle(item))
            {
                return false;
            }

            // Vérification supplémentaire pour les images: au moins RawData ou ThumbnailSource doit être disponible
            bool hasImageData = (item.RawData != null && item.RawData.Length > 0) || item.ThumbnailSource != null;
            
            _loggingService?.LogInfo($"ImageContentHandler.CanHandle - Élément ID {item.Id} a des données d'image: {hasImageData}");
            
            return hasImageData;
        }
    }
}
