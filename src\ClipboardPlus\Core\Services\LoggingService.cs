using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Windows;
using System.Reflection;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using WpfApplication = System.Windows.Application;
using WinFormsApplication = System.Windows.Forms.Application;
using WpfMessageBox = System.Windows.MessageBox;
using ClipboardPlus.Core.Services.Logging;

namespace ClipboardPlus.Core.Services
{
    public interface ILoggingService
    {
        void LogDebug(string message);
        void LogInfo(string message);
        void LogWarning(string message);
        void LogError(string message, Exception? exception = null);
        void LogCritical(string message, Exception? exception = null);
        string GetLogFilePath();
        void ForceFlush();
        
        /// <summary>
        /// Active ou désactive l'affichage des logs dans la console
        /// </summary>
        /// <param name="enabled">True pour activer l'affichage dans la console, False pour désactiver</param>
        void EnableConsoleOutput(bool enabled);
        
        /// <summary>
        /// Indique si l'affichage des logs dans la console est activé
        /// </summary>
        bool IsConsoleOutputEnabled { get; }
        
        /// <summary>
        /// Enregistre un log spécifique à la fonction de suppression avec flush immédiat
        /// </summary>
        /// <param name="message">Message à logger</param>
        void LogDeletion(string message);
    }

    /// <summary>
    /// Service de journalisation pour l'application
    /// </summary>
    public class LoggingService : ILoggingService, IDisposable
    {
        private readonly string _logFilePath = string.Empty;
        private readonly object _lockObject = new object();
        private const int MAX_BUFFER_SIZE = 100;
        private const int MAX_LOG_SIZE_MB = 10;
        private DateTime _lastFlushTime = DateTime.Now;
        private bool _consoleOutputEnabled = false;
        private bool _isInitialized = false;
        private bool _disposed = false;

        // Nouvelle architecture refactorisée
        private ILogEntryFactory _logEntryFactory = null!;
        private List<ILogTarget> _logTargets = null!;
        private List<LogEntry> _logBuffer = null!;

        public bool IsConsoleOutputEnabled => _consoleOutputEnabled;
        
        /// <summary>
        /// Indique si le service de journalisation est correctement initialisé
        /// </summary>
        public bool IsInitialized => _isInitialized;

        public LoggingService()
        {
            try
            {
                // Définir le chemin du fichier log dans le dossier du projet
                var projectRoot = GetProjectRootDirectory();
                var logDirectory = Path.Combine(projectRoot, "logs");
                Directory.CreateDirectory(logDirectory);
                _logFilePath = Path.Combine(logDirectory, "clipboard_plus_temp.log");

                InitializeLoggingService();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Erreur lors de l'initialisation de LoggingService: {ex.Message}");
                Console.WriteLine($"Erreur lors de l'initialisation de LoggingService: {ex.Message}");
            }
        }



        /// <summary>
        /// Constructeur avec injection de dépendances complète.
        /// Respecte parfaitement le principe d'Inversion de Dépendances (DIP).
        /// </summary>
        /// <param name="logEntryFactory">Factory pour créer les entrées de log</param>
        /// <param name="logTargets">Collection des cibles de log</param>
        /// <param name="configuration">Configuration du système de logging</param>
        public LoggingService(
            ILogEntryFactory logEntryFactory,
            IEnumerable<ILogTarget> logTargets,
            ILoggingConfiguration configuration)
        {
            // Validation des paramètres
            _logEntryFactory = logEntryFactory ?? throw new ArgumentNullException(nameof(logEntryFactory));
            _logTargets = logTargets?.ToList() ?? throw new ArgumentNullException(nameof(logTargets));

            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            // Configuration depuis l'injection
            _logFilePath = configuration.LogFilePath;
            _consoleOutputEnabled = configuration.ConsoleOutputEnabled;

            try
            {
                Debug.WriteLine($"LoggingService initialisé avec DI - Chemin: {_logFilePath}");
                Console.WriteLine($"LoggingService initialisé avec DI - Chemin: {_logFilePath}");

                // Créer le répertoire si nécessaire
                var directory = Path.GetDirectoryName(_logFilePath);
                if (!string.IsNullOrEmpty(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Initialisation avec DI
                InitializeServiceWithDI();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Erreur lors de l'initialisation LoggingService avec DI: {ex.Message}");
                Console.WriteLine($"Erreur lors de l'initialisation LoggingService avec DI: {ex.Message}");
            }
        }

        /// <summary>
        /// Initialise le service avec injection de dépendances.
        /// Pas d'instanciation manuelle - toutes les dépendances sont injectées.
        /// </summary>
        private void InitializeServiceWithDI()
        {
            try
            {
                // Les dépendances sont déjà injectées via le constructeur
                // Pas d'instanciation manuelle ici !
                _logBuffer = new List<LogEntry>();

                Debug.WriteLine("Service de logging initialisé avec injection de dépendances");
                Console.WriteLine("Service de logging initialisé avec injection de dépendances");

                // Vérifier la rotation des logs
                RotateLogFileIfNeeded();

                // Marquer comme initialisé
                _isInitialized = true;

                // Log de confirmation
                LogInfo("Services configurés et initialisés avec succès (DI)");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Erreur lors de l'initialisation avec DI: {ex.Message}");
                Console.WriteLine($"Erreur lors de l'initialisation avec DI: {ex.Message}");
            }
        }

        /// <summary>
        /// Initialise les composants communs du service de logging.
        /// Méthode legacy avec instanciation manuelle (à terme obsolète).
        /// </summary>
        private void InitializeLoggingService()
        {
            try
            {
                // Activer immédiatement la sortie console pour toujours avoir des logs visibles
                _consoleOutputEnabled = true;
                Debug.WriteLine("Sortie console activée par défaut");
                Console.WriteLine("Sortie console activée par défaut");

                // Initialiser la nouvelle architecture refactorisée
                _logEntryFactory = new LogEntryFactory();
                _logTargets = new List<ILogTarget>
                {
                    new DebugLogTarget(),
                    new ConsoleLogTarget(_consoleOutputEnabled),
                    new FileLogTarget(_logFilePath)
                };
                _logBuffer = new List<LogEntry>();
                Debug.WriteLine("Nouvelle architecture de logging initialisée");
                Console.WriteLine("Nouvelle architecture de logging initialisée");
                
                // Vérifier la rotation des logs
            RotateLogFileIfNeeded();
            
                // Log dans le fichier
                LogInfo("Service de logging initialisé");
                LogInfo($"Fichier log: {_logFilePath}");
                
                // Forcer un flush immédiat
                ForceFlush();
                
                _isInitialized = true;
                
                // Logs directs pour debugging
                Debug.WriteLine("Initialisation de LoggingService - Terminée avec succès");
                Console.WriteLine("Initialisation de LoggingService - Terminée avec succès");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERREUR CRITIQUE lors de l'initialisation du service de logging: {ex.Message}");
                Debug.WriteLine($"StackTrace: {ex.StackTrace}");
                Console.WriteLine($"ERREUR CRITIQUE lors de l'initialisation du service de logging: {ex.Message}");
                Console.WriteLine($"StackTrace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Active ou désactive l'affichage des logs dans la console
        /// </summary>
        /// <param name="enabled">True pour activer l'affichage dans la console, False pour désactiver</param>
        public void EnableConsoleOutput(bool enabled)
        {
            lock (_lockObject)
            {
                if (_consoleOutputEnabled != enabled)
                {
                    _consoleOutputEnabled = enabled;

                    // CORRECTION 2025-01-13 : Mettre à jour le ConsoleLogTarget existant
                    // ConsoleLogTarget a un champ readonly, donc il faut le recréer
                    if (_logTargets != null)
                    {
                        for (int i = 0; i < _logTargets.Count; i++)
                        {
                            if (_logTargets[i] is ConsoleLogTarget)
                            {
                                // Remplacer par un nouveau ConsoleLogTarget avec la bonne configuration
                                _logTargets[i] = new ConsoleLogTarget(enabled);
                                break;
                            }
                        }
                    }

                    LogInfo($"Sortie console {(enabled ? "activée" : "désactivée")}");
                    ForceFlush(); // Forcer un flush immédiat pour s'assurer que ce message est écrit
                }
            }
        }
        
        public void LogDebug(
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            var callerInfo = $"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber}";
            WriteToLogRefactored("DEBUG", message, callerInfo);
        }

        public void LogInfo(
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            var callerInfo = $"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber}";
            WriteToLogRefactored("INFO", message, callerInfo);
        }

        public void LogWarning(
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            var callerInfo = $"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber}";
            WriteToLogRefactored("AVERTISSEMENT", message, callerInfo);
            ForceFlush(); // Forcer un flush pour les avertissements
        }
        
        public void LogError(
            string message,
            Exception? exception = null,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            string logMessage = message;
            if (exception != null)
            {
                logMessage += Environment.NewLine + FormatException(exception);
            }

            var callerInfo = $"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber}";
            WriteToLogRefactored("ERREUR", logMessage, callerInfo);
            ForceFlush(); // Forcer un flush pour les erreurs
        }

        public void LogCritical(
            string message,
            Exception? exception = null,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            string logMessage = message; // Le préfixe "!!! CRITIQUE !!!" sera ajouté par LogEntryFactory
            if (exception != null)
            {
                logMessage += Environment.NewLine + FormatException(exception);
            }

            var callerInfo = $"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber}";
            WriteToLogRefactored("CRITIQUE", logMessage, callerInfo);

            // Assurer que le message est immédiatement écrit sur le disque
            ForceFlush();

            // Afficher une boîte de dialogue pour les erreurs critiques si nous sommes dans un contexte UI
            ShowErrorMessageIfPossible(message + (exception != null ? $"\n\nErreur: {exception.Message}" : ""));
        }
        
        /// <summary>
        /// Enregistre un log spécifique à la fonction de suppression avec flush immédiat
        /// </summary>
        /// <param name="message">Message à logger</param>
        public void LogDeletion(
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            var callerInfo = $"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber}";
            WriteToLogRefactored("SUPPRESSION", message, callerInfo);
            ForceFlush(); // Forcer un flush immédiat pour les logs de suppression

            // Note: Debug et Console output sont maintenant gérés par les LogTargets
        }

        // Méthodes de compatibilité pour l'interface ILoggingService
        // Ces méthodes délèguent vers les nouvelles méthodes avec Caller Attributes
        void ILoggingService.LogDebug(string message) => LogDebug(message);
        void ILoggingService.LogInfo(string message) => LogInfo(message);
        void ILoggingService.LogWarning(string message) => LogWarning(message);
        void ILoggingService.LogError(string message, Exception? exception) => LogError(message, exception);
        void ILoggingService.LogCritical(string message, Exception? exception) => LogCritical(message, exception);
        void ILoggingService.LogDeletion(string message) => LogDeletion(message);
        
        public string GetLogFilePath()
        {
            return _logFilePath;
        }
        
        /// <summary>
        /// Force l'écriture immédiate des journaux en attente
        /// </summary>
        public void ForceFlush()
        {
            try
            {
                lock (_lockObject)
                {
                    // Flush du nouveau système (architecture refactorisée)
                    if (_logBuffer != null && _logBuffer.Count > 0)
                    {
                        FlushBufferRefactored();
                    }

                    // Flush des nouveaux targets
                    foreach (var target in _logTargets)
                    {
                        try
                        {
                            target.Flush();
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Erreur lors du flush de {target.GetType().Name}: {ex.Message}");
                        }
                    }
                    _lastFlushTime = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Erreur lors du flush des logs: {ex.Message}");
                if (_consoleOutputEnabled)
                {
                    Console.WriteLine($"Erreur lors du flush des logs: {ex.Message}");
                }
            }
        }
        
        private string FormatException(Exception exception)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"Exception: {exception.GetType().Name}");
            sb.AppendLine($"Message: {exception.Message}");
            sb.AppendLine($"Stack Trace: {exception.StackTrace}");
            
            // Informations supplémentaires selon le type d'exception
            if (exception is System.Windows.Markup.XamlParseException xamlEx)
            {
                sb.AppendLine($"Line Number: {xamlEx.LineNumber}, Position: {xamlEx.LinePosition}");
            }
            else if (exception is System.IO.IOException ioEx)
            {
                sb.AppendLine($"IO Error: HResult={ioEx.HResult}");
            }
            else if (exception is System.Net.WebException webEx)
            {
                sb.AppendLine($"Web Status: {webEx.Status}");
            }
            else if (exception is System.ComponentModel.Win32Exception win32Ex)
            {
                sb.AppendLine($"Win32 Error Code: {win32Ex.NativeErrorCode}");
            }
            
            // Analyser les propriétés supplémentaires de l'exception via la réflexion
            foreach (PropertyInfo prop in exception.GetType().GetProperties())
            {
                if (!IsStandardExceptionProperty(prop.Name))
                {
                    try
                    {
                        object? value = prop.GetValue(exception);
                        if (value != null)
                        {
                            sb.AppendLine($"{prop.Name}: {value}");
                        }
                    }
                    catch
                    {
                        // Ignorer les erreurs de réflexion
                    }
                }
            }
            
            // Traiter les exceptions internes récursivement
            if (exception.InnerException != null)
            {
                sb.AppendLine("--- Inner Exception ---");
                sb.Append(FormatException(exception.InnerException));
            }
            
            return sb.ToString();
        }
        
        private bool IsStandardExceptionProperty(string propertyName)
        {
            // Liste des propriétés standard d'Exception que nous avons déjà loggées
            return propertyName == "Message" || 
                   propertyName == "StackTrace" || 
                   propertyName == "InnerException" || 
                   propertyName == "Source" ||
                   propertyName == "HelpLink" ||
                   propertyName == "Data";
        }

        /// <summary>
        /// Méthode WriteToLog utilisant l'architecture SOLID avec Caller Information Attributes.
        /// </summary>
        /// <param name="level">Le niveau de log</param>
        /// <param name="message">Le message à logger</param>
        /// <param name="callerInfo">Informations sur l'appelant (fourni par Caller Attributes)</param>
        private void WriteToLogRefactored(string level, string message, string callerInfo)
        {
            try
            {
                // Vérifier si le service est initialisé (même logique que l'original)
                if (!_isInitialized && level != "CRITIQUE" && level != "ERREUR")
                {
                    Debug.WriteLine($"[{level}] {message} (Service non initialisé)");
                    return;
                }

                // 1. Créer une entrée de log structurée
                var logEntry = _logEntryFactory.Create(level, message, callerInfo);

                // 2. Traiter l'entrée de log (buffer, outputs)
                lock (_lockObject)
                {
                    // Ajouter au buffer interne
                    _logBuffer.Add(logEntry);

                    // Gérer le flush selon les mêmes conditions que l'original
                    if (ShouldFlushRefactored(logEntry))
                    {
                        FlushBufferRefactored();
                    }
                }
            }
            catch (Exception ex)
            {
                // Gestion d'erreur identique à l'original
                HandleLoggingErrorRefactored(ex, level, message);
            }
        }

        /// <summary>
        /// Détermine si un flush est nécessaire selon les conditions de l'original.
        /// </summary>
        private bool ShouldFlushRefactored(LogEntry entry)
        {
            // Condition 1: Buffer plein (même logique que l'original)
            if (_logBuffer.Count >= MAX_BUFFER_SIZE)
            {
                return true;
            }

            // Condition 2: Niveau critique nécessitant un flush immédiat
            if (entry.RequiresImmediateFlush)
            {
                return true;
            }

            // Condition 3: Timeout (5 secondes depuis le dernier flush)
            if ((DateTime.Now - _lastFlushTime).TotalSeconds >= 5)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Effectue le flush du buffer vers toutes les cibles.
        /// </summary>
        private void FlushBufferRefactored()
        {
            if (_logBuffer.Count == 0) return;

            try
            {
                // Copier les entrées à flusher
                var entriesToFlush = new List<LogEntry>(_logBuffer);
                _logBuffer.Clear();

                // Envoyer à toutes les cibles
                foreach (var entry in entriesToFlush)
                {
                    foreach (var target in _logTargets)
                    {
                        try
                        {
                            target.Write(entry);
                        }
                        catch (Exception ex)
                        {
                            // Gérer les erreurs de cible individuellement
                            Debug.WriteLine($"Erreur dans LogTarget {target.GetType().Name}: {ex.Message}");
                        }
                    }
                }

                // Forcer le flush sur toutes les cibles
                foreach (var target in _logTargets)
                {
                    try
                    {
                        target.Flush();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Erreur flush LogTarget {target.GetType().Name}: {ex.Message}");
                    }
                }

                _lastFlushTime = DateTime.Now;

                // Délai minimal comme dans l'original
                Thread.Sleep(5);
            }
            catch (Exception ex)
            {
                HandleLoggingErrorRefactored(ex, "FLUSH", "Erreur lors du flush du buffer");
            }
        }

        /// <summary>
        /// Gère les erreurs de logging avec fallback comme dans l'original.
        /// </summary>
        private void HandleLoggingErrorRefactored(Exception ex, string level, string message)
        {
            try
            {
                Debug.WriteLine($"Erreur WriteToLogRefactored: {ex.Message}");
                Console.WriteLine($"Erreur WriteToLogRefactored: {ex.Message}");

                // Écriture directe en cas d'erreur critique
                if (level == "CRITIQUE" || level == "ERREUR")
                {
                    try
                    {
                        Debug.WriteLine($"[ERREUR CRITIQUE] [{level}] {message}");
                        Console.WriteLine($"[ERREUR CRITIQUE] [{level}] {message}");
                    }
                    catch
                    {
                        // Ignorer silencieusement si même cela échoue
                    }
                }
            }
            catch
            {
                // Si même le fallback échoue, ignorer silencieusement
            }
        }






        
        private void RotateLogFileIfNeeded()
        {
            try
            {
                if (File.Exists(_logFilePath))
                {
                    var fileInfo = new FileInfo(_logFilePath);
                    if (fileInfo.Length > MAX_LOG_SIZE_MB * 1024 * 1024)
                    {
                        string? backupDir = Path.GetDirectoryName(_logFilePath);
                        
                        if (!string.IsNullOrEmpty(backupDir))
                        {
                            string archiveDir = Path.Combine(backupDir, "LogsArchive");
                            if (!Directory.Exists(archiveDir))
                            {
                                Directory.CreateDirectory(archiveDir);
                            }
                            
                            string backupPath = Path.Combine(
                                archiveDir, 
                                $"clipboard_plus_{DateTime.Now:yyyyMMdd_HHmmss}.log");
                                
                            File.Move(_logFilePath, backupPath);
                            
                            // Nettoyer les vieux fichiers d'archive (garder les 10 derniers)
                            CleanupOldLogFiles(archiveDir, 10);
                        }
                        else
                        {
                            // Cas où le répertoire est null ou vide, utiliser un nom de fichier avec timestamp
                            string backupPath = _logFilePath + $".{DateTime.Now:yyyyMMddHHmmss}.bak";
                            File.Move(_logFilePath, backupPath);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Erreur lors de la rotation du fichier journal: {ex.Message}");
            }
        }
        
        private void CleanupOldLogFiles(string directory, int keepCount)
        {
            try
            {
                DirectoryInfo di = new DirectoryInfo(directory);
                FileInfo[] logFiles = di.GetFiles("clipboard_plus_*.log")
                                        .OrderByDescending(f => f.CreationTime)
                                        .ToArray();
                
                for (int i = keepCount; i < logFiles.Length; i++)
                {
                    try
                    {
                        logFiles[i].Delete();
                    }
                    catch
                    {
                        // Ignorer les erreurs de suppression
                    }
                }
            }
            catch
            {
                // Ignorer les erreurs de nettoyage
            }
        }

        private void ShowErrorMessageIfPossible(string message, bool isModalDialog = false)
        {
            try
            {
                // Vérifier si nous sommes sur le thread UI et si l'application est encore active
                if (WpfApplication.Current != null)
                {
                    if (Thread.CurrentThread.GetApartmentState() == ApartmentState.STA && 
                        Thread.CurrentThread == WpfApplication.Current.Dispatcher.Thread)
                    {
                        // Nous sommes sur le thread UI, afficher directement
                        WpfMessageBox.Show(
                            message,
                            "Erreur Application",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                    else
                    {
                        // Nous ne sommes pas sur le thread UI, utiliser le Dispatcher
                        WpfApplication.Current.Dispatcher.BeginInvoke(new Action(() =>
                        {
                            WpfMessageBox.Show(
                                message,
                                "Erreur Application",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
                        }));
                    }
                }
            }
            catch (Exception ex)
            {
                // Si l'affichage échoue, écriture directe
                try
                {
                    Debug.WriteLine($"Impossible d'afficher le message d'erreur : {ex.Message}");
                    Console.WriteLine($"Impossible d'afficher le message d'erreur : {ex.Message}");
                }
                catch
                {
                    // Ignorer silencieusement
                }
            }
        }

        /// <summary>
        /// Trouve le répertoire racine du projet en remontant depuis le répertoire de l'exécutable.
        /// </summary>
        /// <returns>Le chemin du répertoire racine du projet.</returns>
        private string GetProjectRootDirectory()
        {
            try
            {
                // Commencer par le répertoire de l'assembly en cours
                var currentDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);

                if (string.IsNullOrEmpty(currentDirectory))
                {
                    currentDirectory = Directory.GetCurrentDirectory();
                }

                // Remonter jusqu'à trouver le fichier .sln ou le dossier src
                var directory = new DirectoryInfo(currentDirectory);
                while (directory != null && directory.Parent != null)
                {
                    // Chercher un fichier .sln ou un dossier src
                    if (directory.GetFiles("*.sln").Length > 0 ||
                        directory.GetDirectories("src").Length > 0)
                    {
                        return directory.FullName;
                    }
                    directory = directory.Parent;
                }

                // Fallback : utiliser le répertoire courant
                return Directory.GetCurrentDirectory();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Erreur lors de la recherche du répertoire racine du projet: {ex.Message}");
                // Fallback : utiliser le répertoire courant
                return Directory.GetCurrentDirectory();
            }
        }

        /// <summary>
        /// Libère les ressources utilisées par le service de logging
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Libère les ressources utilisées par le service de logging
        /// </summary>
        /// <param name="disposing">True si appelé depuis Dispose(), false si appelé depuis le finalizer</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    // Faire un dernier flush
                    ForceFlush();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Erreur lors du disposal du LoggingService: {ex.Message}");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// Finalizer pour s'assurer que les ressources sont libérées
        /// </summary>
        ~LoggingService()
        {
            Dispose(false);
        }
    }
}