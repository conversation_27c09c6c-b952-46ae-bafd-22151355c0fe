using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using ClipboardPlus.Core.DataModels;
using System.Diagnostics;
using ClipboardPlus.UI.Helpers;
using System.Linq;
using System.Windows.Media.Imaging;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service de persistance des données utilisant SQLite.
    /// </summary>
    public class PersistenceService : IPersistenceService
    {
        private readonly string _dbPath;
        private readonly string _connectionString;
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du service de persistance.
        /// </summary>
        public PersistenceService(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
            // Définir le chemin de la base de données dans le dossier de l'application
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "ClipboardPlus");
            
            // C<PERSON>er le répertoire s'il n'existe pas
            Directory.CreateDirectory(appDataPath);
            
            _dbPath = Path.Combine(appDataPath, "clipboard_data.db");
            _connectionString = $"Data Source={_dbPath}";
        }

        /// <summary>
        /// Initialise la base de données (création des tables si nécessaire).
        /// </summary>
        public async Task InitializeAsync()
        {
            using var connection = new SqliteConnection(_connectionString);
            await connection.OpenAsync();

            // Création de la table des éléments du presse-papiers
            string createClipboardItemsTable = @"
                CREATE TABLE IF NOT EXISTS ClipboardItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Timestamp TEXT NOT NULL,
                    DataType INTEGER NOT NULL,
                    CustomName TEXT,
                    IsPinned INTEGER NOT NULL,
                    OrderIndex INTEGER NOT NULL,
                    RawData BLOB,
                    SourceApplication TEXT,
                    TextPreview TEXT,
                    ThumbnailData BLOB
                )";

            // Création de la table des paramètres de l'application
            string createSettingsTable = @"
                CREATE TABLE IF NOT EXISTS ApplicationSettings (
                    Key TEXT PRIMARY KEY,
                    Value TEXT
                )";

            using (var command = connection.CreateCommand())
            {
                command.CommandText = createClipboardItemsTable;
                await command.ExecuteNonQueryAsync();
            }

            using (var command = connection.CreateCommand())
            {
                command.CommandText = createSettingsTable;
                await command.ExecuteNonQueryAsync();
            }
        }

        // Méthodes utilitaires pour la conversion ThumbnailSource <-> byte[]
        private byte[]? EncodeBitmapSourceToPng(BitmapSource? bitmapSource)
        {
            if (bitmapSource == null) return null;
            try
            {
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(bitmapSource));
                using (var stream = new MemoryStream())
                {
                    encoder.Save(stream);
                    return stream.ToArray();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("PersistenceService: Erreur lors de l'encodage de BitmapSource en PNG.", ex);
                return null;
            }
        }

        private BitmapImage? DecodeByteArrayToBitmapImage(byte[]? byteArray)
        {
            if (byteArray == null || byteArray.Length == 0) return null;
            try
            {
                using (var stream = new MemoryStream(byteArray))
                {
                    var image = new BitmapImage();
                    image.BeginInit();
                    image.CacheOption = BitmapCacheOption.OnLoad;
                    image.StreamSource = stream;
                    image.EndInit();
                    image.Freeze();
                    return image;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("PersistenceService: Erreur lors du décodage de byte[] en BitmapImage.", ex);
                return null;
            }
        }

        /// <summary>
        /// Récupère tous les éléments du presse-papiers.
        /// </summary>
        public async Task<List<ClipboardItem>> GetAllClipboardItemsAsync()
        {
            _loggingService?.LogInfo($"[DÉBUT] GetAllClipboardItemsAsync - ThreadID: {System.Environment.CurrentManagedThreadId}, Heure: {System.DateTime.Now:HH:mm:ss.fff}");
            var items = new List<ClipboardItem>();

            try
            {
            using var connection = new SqliteConnection(_connectionString);
            await connection.OpenAsync();

            string query = @"
                SELECT Id, Timestamp, DataType, CustomName, IsPinned, OrderIndex, 
                       RawData, SourceApplication, TextPreview, ThumbnailData
                FROM ClipboardItems
                ORDER BY OrderIndex DESC, Timestamp DESC";

            using var command = connection.CreateCommand();
            command.CommandText = query;

            using var reader = await command.ExecuteReaderAsync();
                int successfullyParsedCount = 0;
                int failedParseCount = 0;
            while (await reader.ReadAsync())
            {
                    DateTime timestamp;
                    if (!DateTime.TryParse(reader.GetString(1), out timestamp))
                    {
                        timestamp = DateTime.MinValue;
                        _loggingService?.LogWarning($"GetAllClipboardItemsAsync: Échec du parsing de la date pour l'élément ID {reader.GetInt64(0)}. Valeur d'origine: '{reader.GetString(1)}'. Utilisation de DateTime.MinValue.");
                        failedParseCount++;
                    }
                    else
                    {
                        successfullyParsedCount++;
                    }

                var item = new ClipboardItem
                {
                    Id = reader.GetInt64(0),
                        Timestamp = timestamp,
                    DataType = (ClipboardDataType)reader.GetInt32(2),
                    CustomName = reader.IsDBNull(3) ? null : reader.GetString(3),
                    IsPinned = reader.GetInt32(4) != 0,
                    OrderIndex = reader.GetInt32(5),
                    RawData = reader.IsDBNull(6) ? null : (byte[]?)reader.GetValue(6),
                    SourceApplication = reader.IsDBNull(7) ? null : reader.GetString(7),
                    TextPreview = reader.IsDBNull(8) ? null : reader.GetString(8),
                    // Ne pas initialiser ThumbnailData directement ici
                };
                byte[]? thumbnailBytes = reader.IsDBNull(9) ? null : (byte[]?)reader.GetValue(9);
                if (item.DataType == ClipboardDataType.Image)
                {
                    item.CustomName = null; // Forcer le CustomName à null pour les images
                    item.SourceApplication = null; // Forcer SourceApplication à null pour les images
                    item.TextPreview = null; // Forcer TextPreview à null pour les images
                    if (thumbnailBytes != null)
                    {
                        item.ThumbnailSource = DecodeByteArrayToBitmapImage(thumbnailBytes);
                    }
                }
                items.Add(item);
                }
                _loggingService?.LogInfo($"GetAllClipboardItemsAsync: {items.Count} éléments récupérés. Dates parsées avec succès: {successfullyParsedCount}, échecs: {failedParseCount}.");
            }
            catch (SqliteException ex)
            {
                _loggingService?.LogError($"GetAllClipboardItemsAsync: Erreur SQLite. Code: {ex.SqliteErrorCode}. Message: {ex.Message}", ex);
                // Selon la politique de gestion d'erreur, on pourrait relancer ou retourner une liste vide
                // Pour l'instant, on retourne la liste potentiellement vide et l'erreur est journalisée.
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"GetAllClipboardItemsAsync: Erreur générale inattendue. Message: {ex.Message}", ex);
            }
            finally
            {
                _loggingService?.LogInfo($"[FIN] GetAllClipboardItemsAsync - {items.Count} éléments retournés.");
            }
            return items;
        }

        /// <summary>
        /// Insère un nouvel élément dans le presse-papiers.
        /// </summary>
        public async Task<long> InsertClipboardItemAsync(ClipboardItem item)
        {
            if (item == null)
            {
                _loggingService?.LogError("InsertClipboardItemAsync: Tentative d'insérer un élément null.");
                throw new ArgumentNullException(nameof(item));
            }

            _loggingService?.LogInfo($"[DÉBUT] InsertClipboardItemAsync - Type: {item.DataType}, Nom: '{item.CustomName ?? "N/A"}', IsPinned: {item.IsPinned}, ThreadID: {System.Environment.CurrentManagedThreadId}, Heure: {System.DateTime.Now:HH:mm:ss.fff}");
            long id = 0;

            try
        {
            using var connection = new SqliteConnection(_connectionString);
            await connection.OpenAsync();

            string query = @"
                INSERT INTO ClipboardItems (
                    Timestamp, DataType, CustomName, IsPinned, OrderIndex, 
                    RawData, SourceApplication, TextPreview, ThumbnailData
                ) VALUES (
                    @Timestamp, @DataType, @CustomName, @IsPinned, @OrderIndex,
                    @RawData, @SourceApplication, @TextPreview, @ThumbnailData
                );
                SELECT last_insert_rowid();";

            using var command = connection.CreateCommand();
            command.CommandText = query;

            command.Parameters.AddWithValue("@Timestamp", item.Timestamp.ToString("o"));
            command.Parameters.AddWithValue("@DataType", (int)item.DataType);
            command.Parameters.AddWithValue("@CustomName", (object?)item.CustomName ?? DBNull.Value);
            command.Parameters.AddWithValue("@IsPinned", item.IsPinned ? 1 : 0);
            command.Parameters.AddWithValue("@OrderIndex", item.OrderIndex);
            command.Parameters.AddWithValue("@RawData", (object?)item.RawData ?? DBNull.Value);
            command.Parameters.AddWithValue("@SourceApplication", (object?)item.SourceApplication ?? DBNull.Value);
            command.Parameters.AddWithValue("@TextPreview", (object?)item.TextPreview ?? DBNull.Value);
            
            byte[]? thumbnailDataBytes = null;
            if (item.DataType == ClipboardDataType.Image && item.ThumbnailSource != null)
            {
                thumbnailDataBytes = EncodeBitmapSourceToPng(item.ThumbnailSource);
            }
            command.Parameters.AddWithValue("@ThumbnailData", (object?)thumbnailDataBytes ?? DBNull.Value);

            var result = await command.ExecuteScalarAsync();
                id = result != null ? Convert.ToInt64(result) : 0;
            item.Id = id;
                _loggingService?.LogInfo($"InsertClipboardItemAsync: Élément inséré avec succès. ID: {id}.");
            }
            catch (SqliteException ex)
            {
                _loggingService?.LogError($"InsertClipboardItemAsync: Erreur SQLite lors de l'insertion. Code: {ex.SqliteErrorCode}. Message: {ex.Message}", ex);
                // Retourner 0 en cas d'erreur pour indiquer l'échec de l'insertion
                id = 0;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"InsertClipboardItemAsync: Erreur générale inattendue lors de l'insertion. Message: {ex.Message}", ex);
                id = 0;
            }
            finally
            {
                _loggingService?.LogInfo($"[FIN] InsertClipboardItemAsync - ID retourné: {id}.");
            }
            return id;
        }

        /// <summary>
        /// Met à jour un élément existant du presse-papiers.
        /// </summary>
        public async Task UpdateClipboardItemAsync(ClipboardItem item)
        {
            if (item == null)
            {
                _loggingService?.LogError("UpdateClipboardItemAsync: Tentative de mettre à jour un élément null.");
                throw new ArgumentNullException(nameof(item));
            }

            _loggingService?.LogInfo($"[DÉBUT] UpdateClipboardItemAsync - ID: {item.Id}, Type: {item.DataType}, Nom: '{item.CustomName ?? "N/A"}', IsPinned: {item.IsPinned}, ThreadID: {System.Environment.CurrentManagedThreadId}, Heure: {System.DateTime.Now:HH:mm:ss.fff}");
            int rowsAffected = 0;

            try
        {
            using var connection = new SqliteConnection(_connectionString);
            await connection.OpenAsync();

            string query = @"
                UPDATE ClipboardItems SET
                    Timestamp = @Timestamp,
                    DataType = @DataType,
                    CustomName = @CustomName,
                    IsPinned = @IsPinned,
                    OrderIndex = @OrderIndex,
                    RawData = @RawData,
                    SourceApplication = @SourceApplication,
                    TextPreview = @TextPreview,
                    ThumbnailData = @ThumbnailData
                WHERE Id = @Id";

            using var command = connection.CreateCommand();
            command.CommandText = query;

            command.Parameters.AddWithValue("@Id", item.Id);
            command.Parameters.AddWithValue("@Timestamp", item.Timestamp.ToString("o"));
            command.Parameters.AddWithValue("@DataType", (int)item.DataType);
            command.Parameters.AddWithValue("@CustomName", (object?)item.CustomName ?? DBNull.Value);
            command.Parameters.AddWithValue("@IsPinned", item.IsPinned ? 1 : 0);
            command.Parameters.AddWithValue("@OrderIndex", item.OrderIndex);
            command.Parameters.AddWithValue("@RawData", (object?)item.RawData ?? DBNull.Value);
            command.Parameters.AddWithValue("@SourceApplication", (object?)item.SourceApplication ?? DBNull.Value);
            command.Parameters.AddWithValue("@TextPreview", (object?)item.TextPreview ?? DBNull.Value);

            byte[]? thumbnailDataBytes = null;
            if (item.DataType == ClipboardDataType.Image && item.ThumbnailSource != null)
            {
                thumbnailDataBytes = EncodeBitmapSourceToPng(item.ThumbnailSource);
            }
            command.Parameters.AddWithValue("@ThumbnailData", (object?)thumbnailDataBytes ?? DBNull.Value);

                rowsAffected = await command.ExecuteNonQueryAsync();
                _loggingService?.LogInfo($"UpdateClipboardItemAsync: Élément ID {item.Id} mis à jour. Lignes affectées: {rowsAffected}.");
            }
            catch (SqliteException ex)
            {
                _loggingService?.LogError($"UpdateClipboardItemAsync: Erreur SQLite lors de la mise à jour de l'élément ID {item.Id}. Code: {ex.SqliteErrorCode}. Message: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"UpdateClipboardItemAsync: Erreur générale inattendue lors de la mise à jour de l'élément ID {item.Id}. Message: {ex.Message}", ex);
            }
            finally
            {
                 _loggingService?.LogInfo($"[FIN] UpdateClipboardItemAsync - ID: {item.Id}, Lignes affectées: {rowsAffected}.");
            }
        }

        /// <summary>
        /// Supprime un élément du presse-papiers de la base de données.
        /// </summary>
        /// <param name="id">ID de l'élément à supprimer.</param>
        /// <returns>True si la suppression a réussi, False sinon.</returns>
        public async Task<bool> DeleteClipboardItemAsync(long id)
        {
            _loggingService?.LogInfo($"[DÉBUT] DeleteClipboardItemAsync - ID={id}, ThreadID: {System.Environment.CurrentManagedThreadId}, Heure: {System.DateTime.Now:HH:mm:ss.fff}");
            bool success = false; // Initialiser success à false
            // _loggingService?.ForceFlush(); // Envisager de supprimer ForceFlush ici si pas critique pour chaque log d'entrée
            
            if (id <= 0)
            {
                _loggingService?.LogWarning($"DeleteClipboardItemAsync: ID invalide ({id}), impossible de supprimer.");
                // _loggingService?.ForceFlush(); // Et ici
                return false;
            }
            
            try
            {
                using (var connection = new SqliteConnection(_connectionString))
                    {
                        await connection.OpenAsync();
                    _loggingService?.LogInfo($"DeleteClipboardItemAsync: Connexion à la base de données ouverte pour ID={id}.");

                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "DELETE FROM ClipboardItems WHERE Id = @Id";
                        command.Parameters.AddWithValue("@Id", id);

                        _loggingService?.LogInfo($"DeleteClipboardItemAsync: Exécution de la commande DELETE pour ID={id}.");
                        int rowsAffected = await command.ExecuteNonQueryAsync();
                        _loggingService?.LogInfo($"DeleteClipboardItemAsync: Commande DELETE exécutée pour ID={id}, Lignes affectées: {rowsAffected}.");
                                
                                if (rowsAffected > 0)
                                {
                            _loggingService?.LogInfo($"DeleteClipboardItemAsync: Élément ID={id} supprimé avec succès de la base de données.");
                            success = true;
                }
                else
                        {
                            _loggingService?.LogWarning($"DeleteClipboardItemAsync: Aucun élément avec ID={id} n'a été trouvé/supprimé de la base de données.");
                            success = false; // Explicitement false, même si déjà initialisé
                        }
                    }
                }
            }
            catch (SqliteException ex) 
            {
                _loggingService?.LogError($"DeleteClipboardItemAsync: Erreur SQLite lors de la suppression de l'élément ID={id}. Code: {ex.SqliteErrorCode}. Message: {ex.Message}", ex);
                success = false;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"DeleteClipboardItemAsync: Erreur générale inattendue lors de la suppression de l'élément ID={id}: {ex.Message}", ex);
                success = false;
            }
            finally
            {
                _loggingService?.LogInfo($"[FIN] DeleteClipboardItemAsync - ID={id}, Succès: {success}");
                // _loggingService?.ForceFlush(); // Et ici, sauf si la politique est de flusher après chaque bloc d'opération critique
            }
            return success; // Retourner la variable success
        }

        /// <summary>
        /// Supprime tous les éléments du presse-papiers, avec option de préserver les éléments épinglés.
        /// </summary>
        public async Task ClearClipboardItemsAsync(bool preservePinned = true)
        {
            _loggingService?.LogInfo($"[DÉBUT] ClearClipboardItemsAsync - PreservePinned: {preservePinned}, ThreadID: {System.Environment.CurrentManagedThreadId}, Heure: {System.DateTime.Now:HH:mm:ss.fff}");
            int rowsAffected = 0;
            try
        {
            using var connection = new SqliteConnection(_connectionString);
            await connection.OpenAsync();

            string query = preservePinned 
                ? "DELETE FROM ClipboardItems WHERE IsPinned = 0" 
                : "DELETE FROM ClipboardItems";
                _loggingService?.LogInfo($"ClearClipboardItemsAsync: Exécution de la requête: {query}");

            using var command = connection.CreateCommand();
            command.CommandText = query;

                rowsAffected = await command.ExecuteNonQueryAsync();
                _loggingService?.LogInfo($"ClearClipboardItemsAsync: Opération terminée. {rowsAffected} ligne(s) affectée(s).");
            }
            catch (SqliteException ex)
            {
                _loggingService?.LogError($"ClearClipboardItemsAsync: Erreur SQLite. PreservePinned: {preservePinned}. Code: {ex.SqliteErrorCode}. Message: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ClearClipboardItemsAsync: Erreur générale inattendue. PreservePinned: {preservePinned}. Message: {ex.Message}", ex);
            }
            finally
            {
                _loggingService?.LogInfo($"[FIN] ClearClipboardItemsAsync - PreservePinned: {preservePinned}, Lignes affectées: {rowsAffected}.");
            }
        }

        /// <summary>
        /// Récupère tous les paramètres de l'application.
        /// </summary>
        public async Task<Dictionary<string, string>> GetApplicationSettingsAsync()
        {
            _loggingService?.LogInfo($"[DÉBUT] GetApplicationSettingsAsync - ThreadID: {System.Environment.CurrentManagedThreadId}, Heure: {System.DateTime.Now:HH:mm:ss.fff}");
            var settings = new Dictionary<string, string>();

            try
            {
            using var connection = new SqliteConnection(_connectionString);
            await connection.OpenAsync();

            string query = "SELECT Key, Value FROM ApplicationSettings";

            using var command = connection.CreateCommand();
            command.CommandText = query;

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                string key = reader.GetString(0);
                string value = reader.GetString(1);
                settings[key] = value;
            }
                _loggingService?.LogInfo($"GetApplicationSettingsAsync: {settings.Count} paramètre(s) récupéré(s).");
            }
            catch (SqliteException ex)
            {
                _loggingService?.LogError($"GetApplicationSettingsAsync: Erreur SQLite. Code: {ex.SqliteErrorCode}. Message: {ex.Message}", ex);
                // Retourner un dictionnaire vide en cas d'erreur pour éviter de planter l'application
                return new Dictionary<string, string>(); 
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"GetApplicationSettingsAsync: Erreur générale inattendue. Message: {ex.Message}", ex);
                return new Dictionary<string, string>();
            }
            finally
            {
                _loggingService?.LogInfo($"[FIN] GetApplicationSettingsAsync - {settings.Count} paramètre(s) retourné(s).");
            }
            return settings;
        }

        /// <summary>
        /// Enregistre un paramètre spécifique de l'application.
        /// </summary>
        /// <param name="key">Clé du paramètre.</param>
        /// <param name="value">Valeur du paramètre.</param>
        public async Task SaveApplicationSettingAsync(string key, string value)
        {
            _loggingService?.LogInfo($"[PersistenceService] Sauvegarde du paramètre: Clé='{key}', Valeur='{value}'");
            using var connection = new SqliteConnection(_connectionString);
            await connection.OpenAsync();

            string query = @"
                INSERT OR REPLACE INTO ApplicationSettings (Key, Value)
                VALUES (@Key, @Value)";

            using var command = connection.CreateCommand();
            command.CommandText = query;
            command.Parameters.AddWithValue("@Key", key);
            command.Parameters.AddWithValue("@Value", value);

            try
            {
                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"SaveApplicationSettingAsync: Échec de la sauvegarde pour Clé='{key}'", ex);
                throw; // Relancer pour que l'appelant soit informé de l'échec
            }
        }
        
        /// <summary>
        /// Recherche un élément dupliqué dans la base de données.
        /// </summary>
        /// <param name="item">Élément à rechercher.</param>
        /// <returns>L'élément dupliqué s'il existe, sinon null.</returns>
        public async Task<ClipboardItem?> FindDuplicateAsync(ClipboardItem item)
        {
            if (item == null || item.RawData == null || item.RawData.Length == 0)
            {
                return null;
            }
            
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            _loggingService?.LogInfo($"[{operationId}][DÉBUT] FindDuplicateAsync - Type: {item.DataType}, Taille RawData: {item.RawData.Length}");
            
            try
            {
                // Récupérer tous les éléments du même type
                var allItems = await GetAllClipboardItemsAsync();
                var sameTypeItems = allItems.Where(i => i.DataType == item.DataType).ToList();
                
                _loggingService?.LogInfo($"[{operationId}] FindDuplicateAsync: {sameTypeItems.Count} éléments du même type trouvés.");
                
                // Rechercher un élément avec le même contenu
                foreach (var existingItem in sameTypeItems)
                {
                    if (existingItem.RawData != null && 
                        item.RawData.SequenceEqual(existingItem.RawData))
                    {
                        _loggingService?.LogInfo($"[{operationId}][FIN] FindDuplicateAsync - Doublon trouvé. ID existant: {existingItem.Id}");
                        return existingItem;
                    }
                }
                
                _loggingService?.LogInfo($"[{operationId}][FIN] FindDuplicateAsync - Aucun doublon trouvé.");
                return null;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] FindDuplicateAsync: Erreur lors de la recherche de doublons. Message: {ex.Message}", ex);
                return null; // En cas d'erreur, on considère qu'il n'y a pas de doublon
            }
        }
    }
} 