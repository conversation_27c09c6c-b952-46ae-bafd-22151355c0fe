using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Media.Imaging;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour la factory de création de BitmapImage.
    /// Abstrait la création directe de BitmapImage pour améliorer la testabilité.
    /// Respecte le principe d'inversion de dépendance (DIP).
    /// </summary>
    public interface IBitmapImageFactory
    {
        /// <summary>
        /// Crée une BitmapImage à partir de données binaires.
        /// </summary>
        /// <param name="imageData">Les données binaires de l'image</param>
        /// <returns>La BitmapImage créée, ou null si la création échoue</returns>
        /// <exception cref="ArgumentNullException">Levée si imageData est null</exception>
        /// <exception cref="ArgumentException">Levée si imageData est vide</exception>
        BitmapImage? CreateFromBytes(byte[] imageData);

        /// <summary>
        /// Crée une BitmapImage à partir d'un stream.
        /// </summary>
        /// <param name="stream">Le stream contenant les données de l'image</param>
        /// <returns>La BitmapImage créée, ou null si la création échoue</returns>
        /// <exception cref="ArgumentNullException">Levée si stream est null</exception>
        BitmapImage? CreateFromStream(Stream stream);

        /// <summary>
        /// Crée une BitmapImage à partir d'un URI.
        /// </summary>
        /// <param name="uri">L'URI de l'image</param>
        /// <returns>La BitmapImage créée, ou null si la création échoue</returns>
        /// <exception cref="ArgumentNullException">Levée si uri est null</exception>
        BitmapImage? CreateFromUri(Uri uri);

        /// <summary>
        /// Valide que les données binaires représentent une image valide.
        /// </summary>
        /// <param name="imageData">Les données à valider</param>
        /// <returns>True si les données représentent une image valide, false sinon</returns>
        bool IsValidImageData(byte[]? imageData);

        /// <summary>
        /// Obtient les dimensions d'une image sans la charger complètement.
        /// </summary>
        /// <param name="imageData">Les données de l'image</param>
        /// <returns>Les dimensions (largeur, hauteur) ou null si impossible à déterminer</returns>
        (int width, int height)? GetImageDimensions(byte[] imageData);
    }
}
