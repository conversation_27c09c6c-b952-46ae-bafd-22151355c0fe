using System;
using System.Text;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Services.ContentHandlers;

namespace ClipboardPlus.Tests.Unit.Services.ContentHandlers
{
    /// <summary>
    /// Tests unitaires pour TextContentHandler.
    /// Valide le traitement du contenu texte selon le comportement attendu.
    /// </summary>
    [TestFixture]
    public class TextContentHandlerTests
    {
        private TextContentHandler _handler;

        [SetUp]
        public void Setup()
        {
            _handler = new TextContentHandler();
        }

        #region Basic Properties Tests

        [Test]
        public void SupportedDataType_ReturnsText()
        {
            // Act
            var result = _handler.SupportedDataType;

            // Assert
            Assert.AreEqual(ClipboardDataType.Text, result);
        }

        [Test]
        public void GetDefaultUnavailableMessage_ReturnsExpectedMessage()
        {
            // Act
            string result = _handler.GetDefaultUnavailableMessage();

            // Assert
            Assert.AreEqual("Contenu texte non disponible.", result);
        }

        #endregion

        #region CanHandle Tests

        [Test]
        public void CanHandle_WithTextItem_ReturnsTrue()
        {
            // Arrange
            var item = new ClipboardItem { DataType = ClipboardDataType.Text };

            // Act
            bool result = _handler.CanHandle(item);

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public void CanHandle_WithNonTextItem_ReturnsFalse()
        {
            // Arrange
            var item = new ClipboardItem { DataType = ClipboardDataType.Image };

            // Act
            bool result = _handler.CanHandle(item);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void CanHandle_WithNullItem_ReturnsFalse()
        {
            // Act
            bool result = _handler.CanHandle(null);

            // Assert
            Assert.IsFalse(result);
        }

        #endregion

        #region HandleContent Tests

        [Test]
        public void HandleContent_WithRawData_ReturnsDecodedText()
        {
            // Arrange
            string originalText = "Texte avec accents: éàüñ€";
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(originalText),
                TextPreview = "Aperçu"
            };

            // Act
            var result = _handler.HandleContent(item);

            // Assert
            Assert.AreEqual(originalText, result);
        }

        [Test]
        public void HandleContent_WithoutRawDataButWithTextPreview_ReturnsTextPreview()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 2,
                DataType = ClipboardDataType.Text,
                RawData = null,
                TextPreview = "Texte de secours depuis TextPreview"
            };

            // Act
            var result = _handler.HandleContent(item);

            // Assert
            Assert.AreEqual("Texte de secours depuis TextPreview", result);
        }

        [Test]
        public void HandleContent_WithEmptyRawDataButWithTextPreview_ReturnsTextPreview()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 3,
                DataType = ClipboardDataType.Text,
                RawData = new byte[0],
                TextPreview = "Fallback pour RawData vide"
            };

            // Act
            var result = _handler.HandleContent(item);

            // Assert
            Assert.AreEqual("Fallback pour RawData vide", result);
        }

        [Test]
        public void HandleContent_WithoutDataAndWithoutTextPreview_ReturnsDefaultMessage()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 4,
                DataType = ClipboardDataType.Text,
                RawData = null,
                TextPreview = null
            };

            // Act
            var result = _handler.HandleContent(item);

            // Assert
            Assert.AreEqual("Contenu texte non disponible.", result);
        }

        [Test]
        public void HandleContent_WithEmptyTextPreview_ReturnsDefaultMessage()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 5,
                DataType = ClipboardDataType.Text,
                RawData = null,
                TextPreview = ""
            };

            // Act
            var result = _handler.HandleContent(item);

            // Assert
            Assert.AreEqual("Contenu texte non disponible.", result);
        }

        [Test]
        public void HandleContent_WithNullItem_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _handler.HandleContent(null!));
        }

        [Test]
        public void HandleContent_WithWrongDataType_ThrowsArgumentException()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 6,
                DataType = ClipboardDataType.Image,
                RawData = Encoding.UTF8.GetBytes("Some text")
            };

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => _handler.HandleContent(item));
            Assert.That(ex.Message, Contains.Substring("ne peut pas traiter le type Image"));
        }

        [Test]
        public void HandleContent_WithLargeText_HandlesCorrectly()
        {
            // Arrange
            string largeText = new string('A', 10000); // 10KB de texte
            var item = new ClipboardItem
            {
                Id = 7,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(largeText),
                TextPreview = "Aperçu"
            };

            // Act
            var result = _handler.HandleContent(item);

            // Assert
            Assert.AreEqual(largeText, result);
            Assert.AreEqual(10000, ((string)result).Length);
        }

        [Test]
        public void HandleContent_WithSpecialCharacters_PreservesEncoding()
        {
            // Arrange
            string specialText = "Émojis: 🎉🚀💻 Symboles: ©®™ Accents: àáâãäåæçèéêë";
            var item = new ClipboardItem
            {
                Id = 8,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(specialText),
                TextPreview = "Aperçu"
            };

            // Act
            var result = _handler.HandleContent(item);

            // Assert
            Assert.AreEqual(specialText, result);
        }

        [Test]
        public void HandleContent_WithMultilineText_PreservesLineBreaks()
        {
            // Arrange
            string multilineText = "Ligne 1\nLigne 2\r\nLigne 3\rLigne 4";
            var item = new ClipboardItem
            {
                Id = 9,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(multilineText),
                TextPreview = "Aperçu"
            };

            // Act
            var result = _handler.HandleContent(item);

            // Assert
            Assert.AreEqual(multilineText, result);
        }

        #endregion

        #region Edge Cases Tests

        [Test]
        public void HandleContent_WithWhitespaceOnlyTextPreview_ReturnsWhitespace()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 10,
                DataType = ClipboardDataType.Text,
                RawData = null,
                TextPreview = "   \t\n   "
            };

            // Act
            var result = _handler.HandleContent(item);

            // Assert
            Assert.AreEqual("   \t\n   ", result, "Les espaces doivent être préservés");
        }

        [Test]
        public void HandleContent_WithRawDataAndTextPreview_PrefersRawData()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 11,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes("Contenu depuis RawData"),
                TextPreview = "Contenu depuis TextPreview"
            };

            // Act
            var result = _handler.HandleContent(item);

            // Assert
            Assert.AreEqual("Contenu depuis RawData", result, "RawData doit avoir la priorité sur TextPreview");
        }

        #endregion
    }
}
