using System.Collections.Generic;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Interface pour la gestion de la suppression d'éléments en mémoire
    /// Responsabilité : Gérer la suppression des éléments de la collection en mémoire
    /// </summary>
    public interface IDeletionMemoryService
    {
        /// <summary>
        /// Vérifie si un élément existe en mémoire
        /// </summary>
        /// <param name="id">L'ID de l'élément à vérifier</param>
        /// <param name="historyItems">La collection d'éléments en mémoire</param>
        /// <returns>True si l'élément existe, False sinon</returns>
        bool ExistsInMemory(long id, IEnumerable<ClipboardItem> historyItems);

        /// <summary>
        /// Supprime un élément de la collection en mémoire
        /// </summary>
        /// <param name="id">L'ID de l'élément à supprimer</param>
        /// <param name="historyItems">La collection d'éléments en mémoire</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Résultat de la suppression mémoire</returns>
        Task<MemoryDeletionResult> RemoveFromMemoryAsync(long id, IList<ClipboardItem> historyItems, string operationId);

        /// <summary>
        /// Récupère un élément de la collection en mémoire par son ID
        /// </summary>
        /// <param name="id">L'ID de l'élément à récupérer</param>
        /// <param name="historyItems">La collection d'éléments en mémoire</param>
        /// <returns>L'élément trouvé ou null</returns>
        ClipboardItem? FindItemInMemory(long id, IEnumerable<ClipboardItem> historyItems);
    }

    /// <summary>
    /// Résultat de la suppression d'un élément en mémoire
    /// </summary>
    public class MemoryDeletionResult
    {
        /// <summary>
        /// Indique si la suppression en mémoire a réussi
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Indique si l'élément existait en mémoire avant la suppression
        /// </summary>
        public bool ExistedInMemory { get; set; }

        /// <summary>
        /// L'élément qui a été supprimé (pour rollback éventuel)
        /// </summary>
        public ClipboardItem? RemovedItem { get; set; }

        /// <summary>
        /// Message d'information sur l'opération
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// Crée un résultat de suppression mémoire réussie
        /// </summary>
        public static MemoryDeletionResult CreateSuccess(bool existedInMemory, ClipboardItem? removedItem = null, string? message = null) =>
            new() { Success = true, ExistedInMemory = existedInMemory, RemovedItem = removedItem, Message = message };

        /// <summary>
        /// Crée un résultat de suppression mémoire échouée
        /// </summary>
        public static MemoryDeletionResult CreateFailure(string message) =>
            new() { Success = false, ExistedInMemory = false, Message = message };
    }
}
