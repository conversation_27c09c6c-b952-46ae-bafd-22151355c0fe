using System;
using System.Windows.Forms;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Interface pour la gestion de la visibilité de l'icône système.
    /// Responsabilité unique : gérer l'affichage et la visibilité du NotifyIcon.
    /// </summary>
    public interface IVisibilityManager
    {
        /// <summary>
        /// Rend l'icône visible dans la zone de notification.
        /// </summary>
        /// <param name="notifyIcon">L'instance NotifyIcon à rendre visible.</param>
        void ShowIcon(NotifyIcon notifyIcon);

        /// <summary>
        /// Cache l'icône de la zone de notification.
        /// </summary>
        /// <param name="notifyIcon">L'instance NotifyIcon à cacher.</param>
        void HideIcon(NotifyIcon notifyIcon);

        /// <summary>
        /// Vérifie si l'icône est actuellement visible.
        /// </summary>
        /// <param name="notifyIcon">L'instance NotifyIcon à vérifier.</param>
        /// <returns>True si l'icône est visible, false sinon.</returns>
        bool IsIconVisible(NotifyIcon notifyIcon);

        /// <summary>
        /// Configure la visibilité initiale et vérifie l'état après configuration.
        /// </summary>
        /// <param name="notifyIcon">L'instance NotifyIcon à configurer.</param>
        /// <returns>True si la configuration a réussi, false sinon.</returns>
        bool ConfigureInitialVisibility(NotifyIcon notifyIcon);
    }
}
