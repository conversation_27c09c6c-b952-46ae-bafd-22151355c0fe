using System;
using System.Windows.Controls;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Implémentation du constructeur de menu contextuel.
    /// Responsabilité unique : construire le menu contextuel avec les éléments appropriés.
    /// </summary>
    public class ContextMenuBuilder : IContextMenuBuilder
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de ContextMenuBuilder.
        /// </summary>
        /// <param name="loggingService">Service de logging pour enregistrer les opérations de construction.</param>
        public ContextMenuBuilder(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <inheritdoc />
        public ContextMenu BuildContextMenu(Action onHistoryClick, Action onSettingsClick, Action onExitClick)
        {
            if (onHistoryClick == null) throw new ArgumentNullException(nameof(onHistoryClick));
            if (onSettingsClick == null) throw new ArgumentNullException(nameof(onSettingsClick));
            if (onExitClick == null) throw new ArgumentNullException(nameof(onExitClick));

            try
            {
                _loggingService.LogInfo("ContextMenuBuilder: Construction du menu contextuel...");

                var contextMenu = new ContextMenu();

                // Configuration WPF native pour fermeture automatique - COPIE DE LA FENÊTRE D'HISTORIQUE
                contextMenu.StaysOpen = false; // Se ferme automatiquement quand on clique ailleurs
                contextMenu.Focusable = true;   // Permet de recevoir le focus

                // ✅ SOLUTION FINALE : WPF native avec StaysOpen = false
                // Le menu se ferme automatiquement quand on clique ailleurs ou sur un élément

                // Logs essentiels pour le monitoring
                contextMenu.Opened += (sender, e) =>
                {
                    _loggingService.LogInfo("ContextMenuBuilder: Menu contextuel ouvert");
                };

                contextMenu.Closed += (sender, e) =>
                {
                    _loggingService.LogInfo("ContextMenuBuilder: Menu contextuel fermé");
                };



                // Créer les éléments du menu dans l'ordre spécifié
                var historyMenuItem = CreateMenuItem("Afficher l'historique", onHistoryClick);
                var settingsMenuItem = CreateMenuItem("Paramètres", onSettingsClick);
                var separator = CreateSeparator();
                var exitMenuItem = CreateMenuItem("Quitter", onExitClick);

                // Ajouter les éléments au menu
                contextMenu.Items.Add(historyMenuItem);
                contextMenu.Items.Add(settingsMenuItem);
                contextMenu.Items.Add(separator);
                contextMenu.Items.Add(exitMenuItem);

                _loggingService.LogInfo("ContextMenuBuilder: Menu contextuel créé avec succès (4 éléments: Historique, Paramètres, Séparateur, Quitter)");

                return contextMenu;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"ContextMenuBuilder: Erreur lors de la construction du menu contextuel: {ex.Message}", ex);
                throw; // Remonter l'exception car la création du menu est critique
            }
        }

        /// <inheritdoc />
        public MenuItem CreateMenuItem(string header, Action clickAction)
        {
            if (string.IsNullOrWhiteSpace(header))
            {
                throw new ArgumentException("Le texte de l'élément de menu ne peut pas être vide.", nameof(header));
            }

            if (clickAction == null)
            {
                throw new ArgumentNullException(nameof(clickAction));
            }

            try
            {
                _loggingService.LogInfo($"ContextMenuBuilder: Création de l'élément de menu: '{header}'");

                var menuItem = new MenuItem
                {
                    Header = header
                };

                // Configurer l'événement de clic
                menuItem.Click += (sender, e) =>
                {
                    try
                    {
                        _loggingService.LogInfo($"ContextMenuBuilder: Clic sur l'élément de menu: '{header}'");

                        // Fermer le menu contextuel parent avant d'exécuter l'action
                        if (menuItem.Parent is ContextMenu parentMenu)
                        {
                            parentMenu.IsOpen = false;
                            _loggingService.LogInfo("ContextMenuBuilder: Menu contextuel fermé après clic");
                        }

                        clickAction();
                    }
                    catch (Exception ex)
                    {
                        _loggingService.LogError($"ContextMenuBuilder: Erreur lors de l'exécution de l'action pour '{header}': {ex.Message}", ex);
                    }
                };

                _loggingService.LogInfo($"ContextMenuBuilder: Élément de menu '{header}' créé avec succès");

                return menuItem;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"ContextMenuBuilder: Erreur lors de la création de l'élément de menu '{header}': {ex.Message}", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public Separator CreateSeparator()
        {
            try
            {
                _loggingService.LogInfo("ContextMenuBuilder: Création d'un séparateur de menu");

                var separator = new Separator();

                _loggingService.LogInfo("ContextMenuBuilder: Séparateur créé avec succès");

                return separator;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"ContextMenuBuilder: Erreur lors de la création du séparateur: {ex.Message}", ex);
                throw;
            }
        }
    }
}
