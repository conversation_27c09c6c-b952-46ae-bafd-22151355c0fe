using System;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Interface pour la gestion des tentatives de suppression en base de données avec retry logic
    /// Responsabilité : Gérer la suppression en BDD avec logique de retry et gestion d'erreurs
    /// </summary>
    public interface IDeletionRetryService
    {
        /// <summary>
        /// Tente de supprimer un élément de la base de données avec logique de retry
        /// </summary>
        /// <param name="id">L'ID de l'élément à supprimer</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Résultat de la suppression avec détails des tentatives</returns>
        Task<DatabaseDeletionResult> DeleteFromDatabaseWithRetryAsync(long id, string operationId);

        /// <summary>
        /// Configure les paramètres de retry
        /// </summary>
        /// <param name="maxRetries">Nombre maximum de tentatives</param>
        /// <param name="baseDelayMs">D<PERSON><PERSON> de base entre les tentatives en millisecondes</param>
        void ConfigureRetry(int maxRetries, int baseDelayMs);
    }

    /// <summary>
    /// Résultat de la suppression en base de données avec retry
    /// </summary>
    public class DatabaseDeletionResult
    {
        /// <summary>
        /// Indique si la suppression en BDD a finalement réussi
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Nombre de tentatives effectuées
        /// </summary>
        public int AttemptsCount { get; set; }

        /// <summary>
        /// Indique s'il y a eu des erreurs pendant les tentatives
        /// </summary>
        public bool HadErrors { get; set; }

        /// <summary>
        /// Dernière exception rencontrée (si applicable)
        /// </summary>
        public Exception? LastException { get; set; }

        /// <summary>
        /// Messages de log des tentatives
        /// </summary>
        public string[] LogMessages { get; set; } = Array.Empty<string>();

        /// <summary>
        /// Temps total écoulé pour toutes les tentatives
        /// </summary>
        public TimeSpan TotalElapsed { get; set; }

        /// <summary>
        /// Crée un résultat de suppression BDD réussie
        /// </summary>
        public static DatabaseDeletionResult CreateSuccess(int attemptsCount, bool hadErrors = false, TimeSpan? totalElapsed = null, string[]? logMessages = null) =>
            new()
            {
                Success = true,
                AttemptsCount = attemptsCount,
                HadErrors = hadErrors,
                TotalElapsed = totalElapsed ?? TimeSpan.Zero,
                LogMessages = logMessages ?? Array.Empty<string>()
            };

        /// <summary>
        /// Crée un résultat de suppression BDD échouée
        /// </summary>
        public static DatabaseDeletionResult CreateFailure(int attemptsCount, Exception? lastException = null, TimeSpan? totalElapsed = null, string[]? logMessages = null) =>
            new()
            {
                Success = false,
                AttemptsCount = attemptsCount,
                HadErrors = true,
                LastException = lastException,
                TotalElapsed = totalElapsed ?? TimeSpan.Zero,
                LogMessages = logMessages ?? Array.Empty<string>()
            };
    }
}
