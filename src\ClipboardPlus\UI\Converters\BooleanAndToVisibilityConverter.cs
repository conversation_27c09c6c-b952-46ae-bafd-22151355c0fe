using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace ClipboardPlus.UI.Converters
{
    /// <summary>
    /// Convertisseur qui combine deux valeurs booléennes avec AND et convertit vers Visibility
    /// </summary>
    public class BooleanAndToVisibilityConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length != 2)
                return Visibility.Collapsed;

            bool value1 = values[0] is bool b1 && b1;
            bool value2 = values[1] is bool b2 && b2;

            return (value1 && value2) ? Visibility.Visible : Visibility.Collapsed;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
