using NUnit.Framework;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services.Configuration;
using ClipboardPlus.Services;
using System;

namespace ClipboardPlus.Tests.Unit
{
    [TestFixture]
    public class AppDependenciesTests
    {
        [Test]
        public void ConfigureServices_Should_Create_A_Valid_ServiceProvider()
        {
            // Arrange & Act
            // This test now focuses on the HostConfiguration which is the core
            // of the App's ConfigureServices method.
            var services = HostConfiguration.ConfigureServices();

            // Assert
            Assert.That(services, Is.Not.Null, "The service provider should not be null.");
            
            // Spot check a few key services to ensure they are registered correctly
            Assert.That(services.GetService<ILoggingService>(), Is.Not.Null, "ILoggingService should be registered.");
            Assert.That(services.GetService<ISingleInstanceService>(), Is.Not.Null, "ISingleInstanceService should be registered.");
            Assert.That(services.GetService<IApplicationLifetimeManager>(), Is.Not.Null, "IApplicationLifetimeManager should be registered.");
            Assert.That(services.GetService<IClipboardProcessorService>(), Is.Not.Null, "IClipboardProcessorService should be registered.");
            Assert.That(services.GetService<ISystemTrayService>(), Is.Not.Null, "ISystemTrayService should be registered.");
            Assert.That(services.GetService<IClipboardHistoryManager>(), Is.Not.Null, "IClipboardHistoryManager should be registered.");
        }

        [Test]
        public void SingleInstanceService_Constant_Configuration_Is_Correct()
        {
            // Arrange
            var serviceType = typeof(SingleInstanceService);
            const string expectedAppName = "ClipboardPlus";

            // Act
            var appMutexNameField = serviceType.GetField("APP_MUTEX_NAME", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            
            // Assert
            Assert.That(appMutexNameField, Is.Not.Null, "SingleInstanceService should have a defined mutex name constant.");

            var mutexName = appMutexNameField?.GetValue(null) as string;

            Assert.That(string.IsNullOrEmpty(mutexName), Is.False, "The mutex name should not be empty.");
            
            if (!string.IsNullOrEmpty(mutexName))
            {
                Assert.That(mutexName.Contains(expectedAppName), Is.True, 
                    "The mutex name should contain the application name for consistency.");
                Assert.That(mutexName.EndsWith("Mutex"), Is.True, "The mutex name should end with 'Mutex'.");
            }
        }
    }
}