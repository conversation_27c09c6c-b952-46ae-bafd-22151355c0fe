using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.Services.Windows;
using ClipboardPlus.UI.ViewModels.Construction.Models;

namespace ClipboardPlus.UI.ViewModels.Construction.Interfaces
{
    /// <summary>
    /// Interface principale pour la construction du ViewModel avec pattern Builder.
    /// Responsabilité : Orchestration de toutes les étapes de construction selon les principes SOLID.
    /// </summary>
    public interface IClipboardHistoryViewModelBuilder
    {
        /// <summary>
        /// Configure les dépendances obligatoires du ViewModel.
        /// </summary>
        /// <param name="historyManager">Gestionnaire de l'historique du presse-papiers</param>
        /// <param name="clipboardService">Service d'interaction avec le presse-papiers</param>
        /// <param name="settingsManager">Gestionnaire des paramètres de l'application</param>
        /// <param name="notificationService">Service de notifications utilisateur</param>
        /// <param name="userInteractionService">Service d'interaction utilisateur</param>
        /// <param name="serviceProvider">Fournisseur de services pour l'injection de dépendances</param>
        /// <param name="renameService">Service de renommage des éléments</param>
        /// <returns>Builder pour chaînage fluide</returns>
        IClipboardHistoryViewModelBuilder WithRequiredDependencies(
            IClipboardHistoryManager historyManager,
            IClipboardInteractionService clipboardService,
            ISettingsManager settingsManager,
            IUserNotificationService notificationService,
            IUserInteractionService userInteractionService,
            IServiceProvider serviceProvider,
            IRenameService renameService);

        /// <summary>
        /// Configure les dépendances optionnelles du ViewModel.
        /// </summary>
        /// <param name="deletionLogger">Logger pour les opérations de suppression (optionnel)</param>
        /// <param name="healthService">Service de santé des collections (optionnel)</param>
        /// <param name="visibilityManager">Gestionnaire de visibilité SOLID (optionnel)</param>
        /// <param name="newItemOrchestrator">Orchestrateur de création d'éléments (optionnel)</param>
        /// <param name="testDetector">Détecteur d'environnement de test (optionnel)</param>
        /// <param name="settingsWindowService">Service de gestion des fenêtres de paramètres (optionnel)</param>
        /// <returns>Builder pour chaînage fluide</returns>
        IClipboardHistoryViewModelBuilder WithOptionalDependencies(
            IDeletionResultLogger? deletionLogger = null,
            ICollectionHealthService? healthService = null,
            IVisibilityStateManager? visibilityManager = null,
            INewItemCreationOrchestrator? newItemOrchestrator = null,
            ITestEnvironmentDetector? testDetector = null,
            ClipboardPlus.Core.Services.Windows.ISettingsWindowService? settingsWindowService = null);

        /// <summary>
        /// Construit le ViewModel avec toutes les initialisations nécessaires de manière asynchrone.
        /// Cette méthode orchestre tous les services SOLID pour créer un ViewModel entièrement configuré.
        /// Version recommandée utilisant le nouveau constructeur simplifié + InitializeAsync().
        /// </summary>
        /// <returns>Instance de ClipboardHistoryViewModel entièrement initialisée</returns>
        /// <exception cref="InvalidOperationException">Si les dépendances obligatoires ne sont pas configurées</exception>
        Task<ClipboardHistoryViewModel> BuildAsync();

        /// <summary>
        /// Construit le ViewModel avec toutes les initialisations nécessaires de manière synchrone.
        /// Cette méthode est conservée pour la compatibilité ascendante mais utilise l'ancienne approche.
        /// Il est recommandé d'utiliser BuildAsync() pour la nouvelle architecture SOLID.
        /// </summary>
        /// <returns>Instance de ClipboardHistoryViewModel entièrement initialisée</returns>
        /// <exception cref="InvalidOperationException">Si les dépendances obligatoires ne sont pas configurées</exception>
        ClipboardHistoryViewModel Build();
    }
}
