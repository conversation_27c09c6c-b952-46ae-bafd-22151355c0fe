using System;
using System.IO;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Logging;
using ClipboardPlus.Services.Configuration;

namespace ClipboardPlus.Tests.Unit.Core.Services.Logging
{
    /// <summary>
    /// Tests pour valider l'injection de dépendances du LoggingService.
    /// Vérifie que le principe d'Inversion de Dépendances (DIP) est respecté.
    /// </summary>
    [TestFixture]
    public class LoggingServiceDependencyInjectionTests
    {
        private IServiceProvider _serviceProvider = null!;
        private string _tempLogFile = string.Empty;

        [SetUp]
        public void SetUp()
        {
            _tempLogFile = Path.GetTempFileName();
            
            // Configuration des services avec DI
            var services = new ServiceCollection();
            
            // Configuration manuelle pour les tests
            var loggingConfig = new LoggingConfiguration
            {
                LogFilePath = _tempLogFile,
                ConsoleOutputEnabled = true,
                DebugOutputEnabled = true,
                MinimumLevel = "DEBUG",
                MaxBufferSize = 50
            };
            
            services.AddSingleton<ILoggingConfiguration>(loggingConfig);
            services.AddSingleton<ILogEntryFactory, LogEntryFactory>();
            services.AddSingleton<ILogTarget, DebugLogTarget>();
            services.AddSingleton<ILogTarget>(provider =>
            {
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                return new ConsoleLogTarget(config.ConsoleOutputEnabled);
            });
            services.AddSingleton<ILogTarget>(provider =>
            {
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                return new FileLogTarget(config.LogFilePath);
            });
            
            services.AddSingleton<ILoggingService>(provider =>
            {
                var factory = provider.GetRequiredService<ILogEntryFactory>();
                var targets = provider.GetServices<ILogTarget>();
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                
                return new LoggingService(factory, targets, config);
            });
            
            _serviceProvider = services.BuildServiceProvider();
        }

        [TearDown]
        public void TearDown()
        {
            (_serviceProvider as IDisposable)?.Dispose();
            if (File.Exists(_tempLogFile))
            {
                try
                {
                    File.Delete(_tempLogFile);
                }
                catch
                {
                    // Ignorer les erreurs de suppression
                }
            }
        }

        [Test]
        public void LoggingService_WithDependencyInjection_ShouldResolveCorrectly()
        {
            // Arrange & Act
            var service = _serviceProvider.GetRequiredService<ILoggingService>();
            
            // Assert
            Assert.That(service, Is.Not.Null);
            Assert.That(service, Is.InstanceOf<LoggingService>());
        }

        [Test]
        public void LoggingService_WithInjectedConfiguration_ShouldUseCorrectConfig()
        {
            // Arrange
            var service = _serviceProvider.GetRequiredService<ILoggingService>();
            var config = _serviceProvider.GetRequiredService<ILoggingConfiguration>();
            
            // Act
            service.LogInfo("Test message with DI");
            service.ForceFlush();
            
            // Assert
            Assert.That(File.Exists(config.LogFilePath), Is.True);
            Assert.That(service.GetLogFilePath(), Is.EqualTo(config.LogFilePath));
        }

        [Test]
        public void LoggingService_WithInjectedTargets_ShouldUseAllTargets()
        {
            // Arrange
            var service = _serviceProvider.GetRequiredService<ILoggingService>();
            var targets = _serviceProvider.GetServices<ILogTarget>().ToList();
            
            // Act
            service.LogInfo("Test message for all targets");
            service.ForceFlush();
            
            // Assert
            Assert.That(targets.Count, Is.EqualTo(3)); // Debug, Console, File
            Assert.That(File.Exists(_tempLogFile), Is.True);
            
            var content = File.ReadAllText(_tempLogFile);
            Assert.That(content, Does.Contain("Test message for all targets"));
        }

        [Test]
        public void LoggingService_WithCallerInformationAttributes_ShouldWorkWithDI()
        {
            // Arrange
            var service = _serviceProvider.GetRequiredService<ILoggingService>();

            // Act
            service.LogInfo("DI test message");
            service.ForceFlush();

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            var content = File.ReadAllText(_tempLogFile);

            // Vérifier le format avec Caller Information Attributes
            // Note: Avec DI, les Caller Attributes pointent vers LoggingService.cs (comportement correct)
            Assert.That(content, Does.Contain("LoggingService.cs"));
            Assert.That(content, Does.Contain("LogInfo"));
            Assert.That(content, Does.Contain("DI test message"));

            // Vérifier le format complet du log
            Assert.That(content, Does.Match(@"\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\] \[INFO\] \[Non-UI\] \[Thread:\d+\] \[LoggingService\.cs:LogInfo:\d+\] DI test message"));
        }

        [Test]
        public void DependencyInjection_ShouldRespectSingletonLifetime()
        {
            // Arrange & Act
            var service1 = _serviceProvider.GetRequiredService<ILoggingService>();
            var service2 = _serviceProvider.GetRequiredService<ILoggingService>();
            
            // Assert
            Assert.That(ReferenceEquals(service1, service2), Is.True, 
                "LoggingService should be a singleton");
        }

        [Test]
        public void LoggingService_WithHostConfiguration_ShouldResolveCorrectly()
        {
            // Arrange - Utiliser la configuration réelle de l'application
            var realServiceProvider = HostConfiguration.ConfigureServices();
            
            // Act
            var service = realServiceProvider.GetRequiredService<ILoggingService>();
            
            // Assert
            Assert.That(service, Is.Not.Null);
            Assert.That(service, Is.InstanceOf<LoggingService>());
            
            // Test fonctionnel
            service.LogInfo("Test avec configuration réelle");
            service.ForceFlush();
            
            // Cleanup
            (realServiceProvider as IDisposable)?.Dispose();
        }
    }
}
