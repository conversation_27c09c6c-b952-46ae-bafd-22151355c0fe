using System;
using System.Drawing;
using System.Windows.Forms;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Interface pour la création et configuration de NotifyIcon.
    /// Responsabilité unique : créer et configurer les instances NotifyIcon.
    /// </summary>
    public interface INotifyIconFactory
    {
        /// <summary>
        /// Crée une nouvelle instance de NotifyIcon avec les paramètres de base.
        /// </summary>
        /// <param name="text">Le texte de l'info-bulle de l'icône.</param>
        /// <param name="visible">Indique si l'icône doit être visible initialement.</param>
        /// <returns>Une nouvelle instance de NotifyIcon configurée.</returns>
        NotifyIcon CreateNotifyIcon(string text, bool visible = false);

        /// <summary>
        /// Configure l'icône d'un NotifyIcon existant.
        /// </summary>
        /// <param name="notifyIcon">L'instance NotifyIcon à configurer.</param>
        /// <param name="icon">L'icône à assigner.</param>
        void ConfigureIcon(NotifyIcon notifyIcon, Icon icon);
    }
}
