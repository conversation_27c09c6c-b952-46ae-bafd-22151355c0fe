using System;
using System.Globalization;
using System.Windows.Data;

namespace ClipboardPlus.UI.Converters
{
    /// <summary>
    /// Convertit un entier en booléen. Retourne true si l'entier est supérieur à 0.
    /// </summary>
    public class IntToBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int intValue)
            {
                return intValue > 0;
            }
            else if (value is long longValue)
            {
                return longValue > 0;
            }
            
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 