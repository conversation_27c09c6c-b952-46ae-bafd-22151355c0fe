using System;
using System.Windows;
using WpfApplication = System.Windows.Application;

namespace ClipboardPlus.Core.Extensions
{
    /// <summary>
    /// Fournit des méthodes d'extension pour la classe Application
    /// </summary>
    public static class ApplicationExtensions
    {
        /// <summary>
        /// Méthode d'extension pour accéder au fournisseur de services depuis n'importe quelle classe
        /// </summary>
        /// <param name="application">L'instance de l'application</param>
        /// <returns>Le fournisseur de services de l'application</returns>
        public static IServiceProvider? Services(this WpfApplication application)
        {
            if (application is ClipboardPlus.App app)
            {
                return app.Services;
            }
            return null;
        }
    }
} 