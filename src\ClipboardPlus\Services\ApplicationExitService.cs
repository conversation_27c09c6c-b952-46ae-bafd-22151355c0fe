using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Service responsable de l'orchestration de la séquence de fermeture de l'application.
    /// Implémente la logique extraite de App.OnExit pour respecter les principes SOLID.
    /// </summary>
    public class ApplicationExitService : IApplicationExitService
    {
        /// <summary>
        /// Exécute la séquence complète de fermeture de l'application.
        /// LOGIQUE EXACTE MIGRÉE DE App.OnExit (lignes 117-164).
        /// </summary>
        /// <param name="services">Le fournisseur de services pour résoudre les dépendances</param>
        /// <returns>Une tâche représentant l'opération asynchrone</returns>
        public async Task ExecuteExitSequenceAsync(IServiceProvider? services)
        {
            // Récupérer le service de journalisation
            var loggingService = services?.GetService<ILoggingService>();
            loggingService?.LogInfo("OnExit: Début de la fermeture de l'application");
            
            try
            {
                // Sauvegarder tous les paramètres une dernière fois
                var settingsManager = services?.GetService<ISettingsManager>();
                if (settingsManager != null)
                {
                    loggingService?.LogInfo("[APP_EXIT] Sauvegarde finale des paramètres...");
                    loggingService?.LogInfo($"[APP_EXIT] -> Width: {settingsManager.SettingsWindowWidth}");
                    loggingService?.LogInfo($"[APP_EXIT] -> Height: {settingsManager.SettingsWindowHeight}");
                    loggingService?.LogInfo($"[APP_EXIT] -> Top: {settingsManager.SettingsWindowTop}");
                    loggingService?.LogInfo($"[APP_EXIT] -> Left: {settingsManager.SettingsWindowLeft}");
                    
                    try
                    {
                        // AMÉLIORATION : Utilisation correcte de l'asynchrone avec timeout de sécurité
                        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                        await settingsManager.SaveSettingsToPersistenceAsync().ConfigureAwait(false);
                        loggingService?.LogInfo("OnExit: Sauvegarde finale des paramètres terminée avec succès.");
                    }
                    catch (OperationCanceledException)
                    {
                        loggingService?.LogCritical("OnExit: TIMEOUT lors de la sauvegarde des paramètres (10s).");
                    }
                    catch (Exception ex)
                    {
                        loggingService?.LogCritical("OnExit: EXCEPTION CRITIQUE lors de la sauvegarde des paramètres.", ex);
                        // Dans un cas réel, on pourrait écrire dans un fichier de log d'urgence.
                        // System.IO.File.WriteAllText("settings_save_error.log", ex.ToString());
                    }
                }

                // Nettoyage des ressources via le service
                var lifetimeManager = services?.GetService<IApplicationLifetimeManager>();
                lifetimeManager?.Shutdown(services, null, null, false);
            }
            catch (Exception ex)
            {
                loggingService?.LogCritical($"OnExit: Exception non gérée lors de la fermeture: {ex.Message}", ex);

                // AMÉLIORATION : Tentative de sauvegarde d'urgence en cas d'échec critique
                try
                {
                    var emergencyPath = System.IO.Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                        "ClipboardPlus", "emergency_exit_error.log");

                    var errorInfo = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] CRITICAL EXIT ERROR: {ex}";
                    await System.IO.File.WriteAllTextAsync(emergencyPath, errorInfo).ConfigureAwait(false);
                }
                catch
                {
                    // Si même la sauvegarde d'urgence échoue, on ne peut rien faire de plus
                }
            }
            finally
            {
                loggingService?.LogInfo("OnExit: Fermeture de l'application terminée");
                loggingService?.ForceFlush(); // S'assurer que tous les logs sont écrits
            }
        }
    }
}
