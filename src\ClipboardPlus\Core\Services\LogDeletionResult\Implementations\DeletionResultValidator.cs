using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Implementations
{
    /// <summary>
    /// Implémentation du validateur de résultats de suppression.
    /// Vérifie l'état post-suppression et la cohérence des collections.
    /// </summary>
    public class DeletionResultValidator : IDeletionResultValidator
    {
        private readonly ILoggingService _loggingService;
        private readonly ICollectionStateAnalyzer _collectionAnalyzer;

        public DeletionResultValidator(
            ILoggingService loggingService,
            ICollectionStateAnalyzer collectionAnalyzer)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _collectionAnalyzer = collectionAnalyzer ?? throw new ArgumentNullException(nameof(collectionAnalyzer));
        }

        /// <summary>
        /// Valide l'état après une suppression d'élément.
        /// </summary>
        public DeletionValidationResult ValidatePostDeletion(ClipboardItem item, ClipboardHistoryViewModel viewModel)
        {
            _loggingService?.LogInfo($"[VALIDATOR] ValidatePostDeletion - Validation post-suppression pour l'élément {item.Id}");

            try
            {
                var result = new DeletionValidationResult
                {
                    IsValid = true,
                    ValidationTimestamp = DateTime.Now
                };

                // Vérifier que l'élément n'est plus dans la collection par référence
                result.ItemStillInCollection = viewModel.HistoryItems.Contains(item);
                result.FoundByReference = result.ItemStillInCollection ? item : null;

                // Vérifier par ID également
                result.FoundById = viewModel.HistoryItems.FirstOrDefault(i => i.Id == item.Id);
                result.ItemStillInManager = result.FoundById != null;

                // Vérifier si les instances correspondent
                result.InstancesMatch = result.FoundByReference != null && result.FoundById != null &&
                                       ReferenceEquals(result.FoundByReference, result.FoundById);

                // Déterminer si la validation est réussie
                result.IsValid = !result.ItemStillInCollection && !result.ItemStillInManager;

                // Ajouter des issues si nécessaire
                if (result.ItemStillInCollection)
                {
                    result.Issues.Add(new ValidationIssue
                    {
                        Severity = ValidationSeverity.Error,
                        Code = "ITEM_STILL_IN_COLLECTION",
                        Description = $"Élément ID {item.Id} toujours présent dans HistoryItems par référence"
                    });
                }

                if (result.ItemStillInManager)
                {
                    result.Issues.Add(new ValidationIssue
                    {
                        Severity = ValidationSeverity.Error,
                        Code = "ITEM_FOUND_BY_ID",
                        Description = $"Élément ID {item.Id} toujours trouvé par recherche ID"
                    });
                }

                _loggingService?.LogInfo($"[VALIDATOR] ValidatePostDeletion terminée - Résultat: {(result.IsValid ? "VALIDE" : "INVALIDE")} - Issues: {result.Issues.Count}");
                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[VALIDATOR] Erreur dans ValidatePostDeletion: {ex.Message}", ex);
                return new DeletionValidationResult
                {
                    IsValid = false,
                    ValidationTimestamp = DateTime.Now,
                    Issues = new List<ValidationIssue>
                    {
                        new ValidationIssue
                        {
                            Severity = ValidationSeverity.Critical,
                            Code = "VALIDATION_EXCEPTION",
                            Description = $"Erreur lors de la validation: {ex.Message}"
                        }
                    }
                };
            }
        }

        /// <summary>
        /// Valide l'état général des collections.
        /// </summary>
        public CollectionValidationResult ValidateCollectionState(ClipboardHistoryViewModel viewModel)
        {
            _loggingService?.LogInfo($"[VALIDATOR] ValidateCollectionState - Validation de l'état des collections");

            try
            {
                var result = new CollectionValidationResult
                {
                    IsValid = true
                };

                // Vérifications de base sur les collections
                if (viewModel.HistoryItems == null)
                {
                    result.IsValid = false;
                    _loggingService?.LogError("[VALIDATOR] La collection HistoryItems est null");
                }
                else
                {
                    result.ViewModelItemCount = viewModel.HistoryItems.Count;
                    _loggingService?.LogInfo($"[VALIDATOR] Collection HistoryItems contient {result.ViewModelItemCount} éléments");
                }

                _loggingService?.LogInfo($"[VALIDATOR] ValidateCollectionState terminée - Résultat: {(result.IsValid ? "VALIDE" : "INVALIDE")}");
                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[VALIDATOR] Erreur dans ValidateCollectionState: {ex.Message}", ex);
                return new CollectionValidationResult
                {
                    IsValid = false
                };
            }
        }

        /// <summary>
        /// Valide la cohérence entre le ViewModel et le gestionnaire d'historique.
        /// </summary>
        public ConsistencyValidationResult ValidateConsistency(ClipboardHistoryViewModel viewModel)
        {
            _loggingService?.LogInfo($"[VALIDATOR] ValidateConsistency - Validation de la cohérence");

            try
            {
                var result = new ConsistencyValidationResult
                {
                    IsConsistent = true,
                    ManagerAccessible = true,
                    ViewModelValid = true
                };

                // Validation de cohérence basique
                if (viewModel.HistoryItems != null)
                {
                    _loggingService?.LogInfo($"[VALIDATOR] ViewModel contient {viewModel.HistoryItems.Count} éléments");

                    // Vérifier les doublons par ID
                    var duplicateIds = viewModel.HistoryItems
                        .GroupBy(item => item.Id)
                        .Where(group => group.Count() > 1)
                        .Select(group => group.Key)
                        .ToList();

                    if (duplicateIds.Any())
                    {
                        result.IsConsistent = false;
                        _loggingService?.LogWarning($"[VALIDATOR] {duplicateIds.Count} IDs dupliqués détectés");
                    }
                }
                else
                {
                    result.ViewModelValid = false;
                    result.IsConsistent = false;
                }

                _loggingService?.LogInfo($"[VALIDATOR] ValidateConsistency terminée - Résultat: {(result.IsConsistent ? "COHÉRENT" : "INCOHÉRENT")}");
                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[VALIDATOR] Erreur dans ValidateConsistency: {ex.Message}", ex);
                return new ConsistencyValidationResult
                {
                    IsConsistent = false,
                    ManagerAccessible = false,
                    ViewModelValid = false
                };
            }
        }

        /// <summary>
        /// Effectue une validation complète.
        /// </summary>
        public ComprehensiveValidationResult ValidateComplete(DeletionResultContext context)
        {
            var result = new ComprehensiveValidationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _loggingService.LogDebug($"🔍 [VALIDATOR] Début validation complète - Operation ID: {context.OperationId}");

                // Validation post-suppression (si un élément est fourni)
                if (context.Item != null && context.ViewModel != null)
                {
                    result.PostDeletionValidation = ValidatePostDeletion(context.Item, context.ViewModel);
                }

                // Validation des collections (si ViewModel disponible)
                if (context.ViewModel != null)
                {
                    result.CollectionValidation = ValidateCollectionState(context.ViewModel);
                    result.ConsistencyValidation = ValidateConsistency(context.ViewModel);
                }

                result.MarkCompleted();

                _loggingService.LogDebug($"✅ [VALIDATOR] Validation complète terminée - Valide: {result.IsFullyValid}, Issues: {result.TotalIssuesCount}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [VALIDATOR] Erreur lors de la validation complète: {ex.Message}", ex);
                result.IsFullyValid = false;
            }
            finally
            {
                stopwatch.Stop();
                result.ValidationDuration = stopwatch.Elapsed;
                _loggingService.LogDebug($"⏱️ [VALIDATOR] Validation complète durée: {stopwatch.ElapsedMilliseconds}ms");
            }

            return result;
        }

        #region Méthodes privées

        private bool CheckItemInViewModelCollection(ClipboardItem item, ClipboardHistoryViewModel viewModel)
        {
            try
            {
                return viewModel.HistoryItems?.Contains(item) ?? false;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [VALIDATOR] Erreur lors de la vérification dans la collection ViewModel: {ex.Message}");
                return false;
            }
        }

        private bool CheckItemInHistoryManager(ClipboardItem item, ClipboardHistoryViewModel viewModel)
        {
            try
            {
                // CORRECTION: Utiliser la méthode publique au lieu de la réflexion
                var historyManager = viewModel.GetClipboardHistoryManager();

                if (historyManager == null)
                {
                    return false;
                }

                return historyManager.HistoryItems?.Contains(item) ?? false;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [VALIDATOR] Erreur lors de la vérification dans le gestionnaire: {ex.Message}");
                return false;
            }
        }

        private ClipboardItem? FindItemByReference(ClipboardItem item, ClipboardHistoryViewModel viewModel)
        {
            try
            {
                return viewModel.HistoryItems?.FirstOrDefault(i => ReferenceEquals(i, item));
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [VALIDATOR] Erreur lors de la recherche par référence: {ex.Message}");
                return null;
            }
        }

        private ClipboardItem? FindItemById(long itemId, ClipboardHistoryViewModel viewModel)
        {
            try
            {
                return viewModel.HistoryItems?.FirstOrDefault(i => i?.Id == itemId);
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [VALIDATOR] Erreur lors de la recherche par ID: {ex.Message}");
                return null;
            }
        }

        private bool CheckInstancesMatch(ClipboardItem? item1, ClipboardItem? item2)
        {
            if (item1 == null && item2 == null) return true;
            if (item1 == null || item2 == null) return false;
            return ReferenceEquals(item1, item2);
        }

        private void AnalyzeValidationResults(DeletionValidationResult result, ClipboardItem item)
        {
            // Si l'élément est encore présent après suppression, c'est un problème
            if (result.ItemStillInCollection)
            {
                result.Issues.Add(new ValidationIssue
                {
                    Severity = ValidationSeverity.Error,
                    Code = "ITEM_STILL_IN_COLLECTION",
                    Description = "L'élément est encore présent dans la collection du ViewModel après suppression",
                    RelatedItem = item
                });
            }

            if (result.ItemStillInManager)
            {
                result.Issues.Add(new ValidationIssue
                {
                    Severity = ValidationSeverity.Error,
                    Code = "ITEM_STILL_IN_MANAGER",
                    Description = "L'élément est encore présent dans le gestionnaire d'historique après suppression",
                    RelatedItem = item
                });
            }

            // Si les instances ne correspondent pas, c'est suspect
            if (result.FoundByReference != null && result.FoundById != null && !result.InstancesMatch)
            {
                result.Issues.Add(new ValidationIssue
                {
                    Severity = ValidationSeverity.Warning,
                    Code = "INSTANCE_MISMATCH",
                    Description = "Les instances trouvées par référence et par ID ne correspondent pas",
                    RelatedItem = item
                });
            }
        }

        private int CalculateDuplicates(ClipboardHistoryViewModel viewModel)
        {
            try
            {
                if (viewModel.HistoryItems == null) return 0;

                var itemIds = viewModel.HistoryItems.Where(i => i != null).Select(i => i!.Id).ToList();
                return itemIds.Count - itemIds.Distinct().Count();
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [VALIDATOR] Erreur lors du calcul des doublons: {ex.Message}");
                return 0;
            }
        }

        private bool CheckManagerAccessibility(ClipboardHistoryViewModel viewModel)
        {
            try
            {
                // CORRECTION: Utiliser la méthode publique au lieu de la réflexion fragile
                var historyManager = viewModel.GetClipboardHistoryManager();
                return historyManager != null;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [VALIDATOR] Erreur lors de la vérification d'accessibilité du gestionnaire: {ex.Message}");
                return false;
            }
        }

        private bool CheckViewModelValidity(ClipboardHistoryViewModel viewModel)
        {
            try
            {
                return viewModel.HistoryItems != null;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [VALIDATOR] Erreur lors de la vérification de validité du ViewModel: {ex.Message}");
                return false;
            }
        }

        private bool CheckUpdateInProgress(ClipboardHistoryViewModel viewModel)
        {
            try
            {
                // Vérifier si une propriété indique qu'une mise à jour est en cours
                var updateProperty = viewModel.GetType().GetProperty("IsOperationInProgress");
                if (updateProperty != null)
                {
                    return (bool)(updateProperty.GetValue(viewModel) ?? false);
                }
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [VALIDATOR] Erreur lors de la vérification de mise à jour en cours: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
