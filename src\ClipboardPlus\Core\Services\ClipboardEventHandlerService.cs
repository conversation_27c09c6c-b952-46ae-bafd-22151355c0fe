using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service de gestion des événements de changement du contenu du presse-papiers.
    /// Cette classe encapsule toute la logique qui était précédemment dans la méthode
    /// ClipboardListener_ClipboardContentChanged de la classe App.
    /// 
    /// Reproduit exactement le comportement existant :
    /// - Récupération lazy du LifetimeManager
    /// - Logique de fallback vers ClipboardProcessorService
    /// - Gestion d'erreurs avec GlobalExceptionManager
    /// - Messages de log identiques
    /// </summary>
    public class ClipboardEventHandlerService : IClipboardEventHandler
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILoggingService _loggingService;
        private readonly IGlobalExceptionManager _exceptionManager;
        
        // IMPORTANT: Reproduire EXACTEMENT la logique de mise en cache de App
        // Le _lifetimeManager peut être null et est récupéré de façon lazy
        private IApplicationLifetimeManager? _cachedLifetimeManager;

        /// <summary>
        /// Initialise une nouvelle instance du service de gestion des événements du presse-papiers.
        /// CORRECTION: Initialise immédiatement le LifetimeManager pour éviter les warnings récurrents.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services pour la résolution dynamique</param>
        /// <param name="loggingService">Service de journalisation</param>
        /// <param name="exceptionManager">Gestionnaire d'exceptions globales</param>
        public ClipboardEventHandlerService(
            IServiceProvider serviceProvider,
            ILoggingService loggingService,
            IGlobalExceptionManager exceptionManager)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _exceptionManager = exceptionManager ?? throw new ArgumentNullException(nameof(exceptionManager));

            // CORRECTION: Initialiser immédiatement le LifetimeManager au lieu d'une récupération lazy
            _cachedLifetimeManager = _serviceProvider.GetService<IApplicationLifetimeManager>();

            if (_cachedLifetimeManager == null)
            {
                _loggingService.LogWarning("ClipboardEventHandlerService: IApplicationLifetimeManager non disponible à l'initialisation");
            }
            else
            {
                _loggingService.LogInfo("ClipboardEventHandlerService: IApplicationLifetimeManager initialisé avec succès");
            }
        }

        /// <summary>
        /// Gère l'événement de changement du contenu du presse-papiers.
        /// Reproduit EXACTEMENT le comportement de la méthode ClipboardListener_ClipboardContentChanged
        /// de la classe App, y compris :
        /// - Les messages de log identiques
        /// - La logique de récupération lazy du LifetimeManager
        /// - Le fallback vers ClipboardProcessorService
        /// - La gestion d'erreurs avec GlobalExceptionManager
        /// </summary>
        /// <param name="sender">L'objet qui a déclenché l'événement</param>
        /// <param name="e">Les arguments de l'événement</param>
        /// <returns>Une tâche représentant l'opération asynchrone</returns>
        public async Task HandleClipboardContentChangedAsync(object? sender, EventArgs e)
        {
            // Message de log initial identique à l'original
            _loggingService.LogInfo($"ClipboardListener_ClipboardContentChanged: Contenu du presse-papiers modifié (source: {sender?.GetType().Name ?? "inconnue"})");

            try
            {
                // CORRECTION: Plus de récupération lazy - LifetimeManager initialisé dans le constructeur
                // Vérification de sécurité uniquement si le service n'était pas disponible à l'initialisation
                if (_cachedLifetimeManager == null)
                {
                    _loggingService.LogError("ClipboardListener_ClipboardContentChanged: LifetimeManager non disponible - service non initialisé");
                }

                // Traitement principal via LifetimeManager (si disponible)
                if (_cachedLifetimeManager != null)
                {
                    _loggingService.LogInfo("ClipboardListener_ClipboardContentChanged: Traitement du contenu du presse-papiers via _lifetimeManager");
                    await _cachedLifetimeManager.ProcessClipboardContentAsync();
                    _loggingService.LogInfo("ClipboardListener_ClipboardContentChanged: Traitement terminé avec succès");
                }
                else
                {
                    // Fallback vers ClipboardProcessorService (logique identique à l'original)
                    _loggingService.LogWarning("ClipboardListener_ClipboardContentChanged: LifetimeManager n'est pas disponible");

                    // Tentative de traitement direct si le service de traitement est disponible
                    var processorService = _serviceProvider.GetService<IClipboardProcessorService>();
                    if (processorService != null)
                    {
                        _loggingService.LogInfo("ClipboardListener_ClipboardContentChanged: Tentative de traitement direct via IClipboardProcessorService");
                        await processorService.ProcessCurrentClipboardContentAsync();
                        _loggingService.LogInfo("ClipboardListener_ClipboardContentChanged: Traitement direct terminé avec succès");
                    }
                    else
                    {
                        _loggingService.LogError("ClipboardListener_ClipboardContentChanged: Aucun moyen de traiter le contenu du presse-papiers");
                    }
                }
            }
            catch (Exception ex)
            {
                // Gestion d'erreurs identique à l'original
                _loggingService.LogError($"ClipboardListener_ClipboardContentChanged: Erreur lors du traitement du contenu du presse-papiers: {ex.Message}", ex);
                
                // Utiliser le service GlobalExceptionManager pour journaliser l'exception
                // (paramètres identiques à l'original)
                _exceptionManager.LogUnhandledException("ClipboardChanged", ex, false);
            }
        }
    }
}
