using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Reflection;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.Controls;
using ClipboardPlus.UI.ViewModels;
using System.Threading.Tasks;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.Input;

namespace ClipboardPlus.Tests.Unit.UI.Controls
{
    /// <summary>
    /// Tests de régression pour ClipboardItemControl
    /// Ces tests vérifient la structure du contrôle sans nécessiter d'environnement UI complet
    /// et sans instancier les contrôles WPF qui nécessitent un thread STA.
    /// </summary>
    [TestFixture]
    public class ClipboardItemControlTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<ClipboardHistoryViewModel> _mockViewModel = null!;
        private ClipboardItem _testItem = null!;

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockViewModel = new Mock<ClipboardHistoryViewModel>();
            _testItem = new ClipboardItem
            {
                Id = 1,
                CustomName = "Test Item",
                TextPreview = "Test Content",
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.Now
            };
        }

        /// <summary>
        /// Vérifie que la classe ClipboardItemControl existe et hérite de UserControl
        /// </summary>
        [Test]
        public void ClipboardItemControl_ClassExists()
        {
            // Arrange & Act
            Type type = typeof(ClipboardItemControl);

            // Assert
            Assert.That(type, Is.Not.Null, "La classe ClipboardItemControl devrait exister");
            Assert.That(typeof(UserControl).IsAssignableFrom(type), Is.True,
                "ClipboardItemControl devrait hériter de UserControl");
        }

        /// <summary>
        /// Vérifie que les champs privés nécessaires existent
        /// </summary>
        [Test]
        public void ClipboardItemControl_RequiredFields_Exist()
        {
            // Arrange & Act
            Type type = typeof(ClipboardItemControl);

            // Assert
            Assert.That(type.GetField("_currentItem", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _currentItem devrait exister");
            Assert.That(type.GetField("_viewModel", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _viewModel devrait exister");
            Assert.That(type.GetField("_propertyChangedHandlerRegistered", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _propertyChangedHandlerRegistered devrait exister");
            Assert.That(type.GetField("_viewModelSearchAttempted", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _viewModelSearchAttempted devrait exister");
            Assert.That(type.GetField("_loggingService", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _loggingService devrait exister");
        }

        /// <summary>
        /// Vérifie que les méthodes nécessaires existent
        /// </summary>
        [Test]
        public void ClipboardItemControl_RequiredMethods_Exist()
        {
            // Arrange & Act
            Type type = typeof(ClipboardItemControl);

            // Assert
            Assert.That(type.GetMethod("RenameMenuItem_Click", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode RenameMenuItem_Click devrait exister");
            Assert.That(type.GetMethod("PinMenuItem_Click", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode PinMenuItem_Click devrait exister");
            Assert.That(type.GetMethod("DeleteMenuItem_Click", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode DeleteMenuItem_Click devrait exister");
            Assert.That(type.GetMethod("PreviewMenuItem_Click", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode PreviewMenuItem_Click devrait exister");
            Assert.That(type.GetMethod("EditNameTextBox_KeyDown", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode EditNameTextBox_KeyDown devrait exister");
            Assert.That(type.GetMethod("EditNameTextBox_LostFocus", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode EditNameTextBox_LostFocus devrait exister");
            Assert.That(type.GetMethod("CheckRenameState", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode CheckRenameState devrait exister");
            Assert.That(type.GetMethod("FindViewModel", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode FindViewModel devrait exister");
            Assert.That(type.GetMethod("ClipboardItemControl_Loaded", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode ClipboardItemControl_Loaded devrait exister");
            Assert.That(type.GetMethod("ClipboardItemControl_DataContextChanged", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode ClipboardItemControl_DataContextChanged devrait exister");
            Assert.That(type.GetMethod("Item_PropertyChanged", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode Item_PropertyChanged devrait exister");
        }

        /// <summary>
        /// Vérifie que le contrôle contient les éléments visuels nécessaires dans le XAML
        /// </summary>
        [Test]
        public void ClipboardItemControl_RequiredUIElements_Exist()
        {
            // Cette méthode utilise la réflexion pour vérifier que les éléments d'interface
            // utilisateur définis dans le XAML existent dans la classe générée

            // Arrange & Act
            Type type = typeof(ClipboardItemControl);

            // Assert - Vérifier les propriétés qui seraient générées par le compilateur XAML
            Assert.That(type.GetField("EditNameTextBox", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "L'élément EditNameTextBox devrait exister dans le XAML");
            Assert.That(type.GetField("DisplayNameTextBlock", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "L'élément DisplayNameTextBlock devrait exister dans le XAML");
        }

        /// <summary>
        /// Vérifie que le contrôle gère correctement les opérations de renommage
        /// </summary>
        [Test]
        public void ClipboardItemControl_HandlesRenamingOperations()
        {
            // Arrange & Act
            Type type = typeof(ClipboardItemControl);

            // Assert
            Assert.That(type.GetMethod("CheckRenameState", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode CheckRenameState devrait exister");
            Assert.That(type.GetMethod("RenameMenuItem_Click", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode RenameMenuItem_Click devrait exister");
            Assert.That(type.GetMethod("EditNameTextBox_KeyDown", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode EditNameTextBox_KeyDown devrait exister");
            Assert.That(type.GetMethod("EditNameTextBox_LostFocus", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode EditNameTextBox_LostFocus devrait exister");
        }

        /// <summary>
        /// Vérifie que le contrôle gère correctement les opérations d'épinglage
        /// </summary>
        [Test]
        public void ClipboardItemControl_HandlesPinningOperations()
        {
            // Arrange & Act
            Type type = typeof(ClipboardItemControl);

            // Assert
            Assert.That(type.GetMethod("PinMenuItem_Click", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode PinMenuItem_Click devrait exister");
        }

        /// <summary>
        /// Vérifie que le contrôle gère correctement les opérations de prévisualisation
        /// </summary>
        [Test]
        public void ClipboardItemControl_HandlesPreviewOperations()
        {
            // Arrange & Act
            Type type = typeof(ClipboardItemControl);

            // Assert
            Assert.That(type.GetMethod("PreviewMenuItem_Click", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode PreviewMenuItem_Click devrait exister");
        }

        /// <summary>
        /// Vérifie que le contrôle gère correctement les opérations de suppression
        /// </summary>
        [Test]
        public void ClipboardItemControl_HandlesDeleteOperations()
        {
            // Arrange & Act
            Type type = typeof(ClipboardItemControl);

            // Assert
            Assert.That(type.GetMethod("DeleteMenuItem_Click", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode DeleteMenuItem_Click devrait exister");
        }

        // ANCIEN SYSTÈME TOTALEMENT SUPPRIMÉ
        // Tests des propriétés de dépendance obsolètes supprimés
        // Le système SOLID gère maintenant toute la visibilité

        /// <summary>
        /// Vérifie que la méthode d'extension FindParentOfType est implémentée
        /// </summary>
        [Test]
        public void ControlExtensions_FindParentOfType_ShouldBeImplemented()
        {
            // Arrange & Act
            Type extensionsType = typeof(ControlExtensions);
            var method = extensionsType.GetMethod("FindParentOfType", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.That(method, Is.Not.Null, "La méthode d'extension FindParentOfType devrait exister");
            Assert.That(method?.IsGenericMethod, Is.True, "La méthode FindParentOfType devrait être générique");
        }

        /// <summary>
        /// Vérifie que la méthode FindParentOfType a les bons paramètres
        /// </summary>
        [Test]
        public void ControlExtensions_FindParentOfType_ShouldHaveCorrectParameters()
        {
            // Arrange & Act
            var method = typeof(ControlExtensions).GetMethod("FindParentOfType", 
                BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.That(method, Is.Not.Null, "La méthode FindParentOfType devrait exister");
            
            var parameters = method?.GetParameters();
            Assert.That(parameters?.Length, Is.EqualTo(1), "La méthode devrait avoir 1 paramètre");
            Assert.That(parameters?[0].ParameterType.Name, Is.EqualTo("DependencyObject"),
                "Le paramètre de FindParentOfType devrait être de type DependencyObject");
        }
    }
}