using System;
using System.Windows;

namespace ClipboardPlus.Core.Services.Shortcuts.Interfaces
{
    /// <summary>
    /// Interface pour la création de fenêtres.
    /// Permet l'injection de dépendance et la testabilité.
    /// </summary>
    public interface IWindowFactory
    {
        /// <summary>
        /// Crée une fenêtre fantôme pour l'enregistrement de raccourcis.
        /// </summary>
        /// <returns>Une fenêtre configurée pour les raccourcis globaux.</returns>
        Window CreateHiddenWindow();

        /// <summary>
        /// Obtient le handle d'une fenêtre.
        /// </summary>
        /// <param name="window">La fenêtre dont obtenir le handle.</param>
        /// <returns>Le handle de la fenêtre.</returns>
        IntPtr GetWindowHandle(Window window);

        /// <summary>
        /// Affiche une fenêtre de manière cachée.
        /// </summary>
        /// <param name="window">La fenêtre à afficher/cacher.</param>
        void ShowAndHideWindow(Window window);
    }
}
