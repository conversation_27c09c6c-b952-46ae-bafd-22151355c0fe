using System;
using System.Windows;
using System.Windows.Controls;
using NUnit.Framework;
using ClipboardPlus.UI.Helpers;
using Moq;

namespace ClipboardPlus.Tests.Unit.UI.Helpers
{
    [TestFixture]
    public class AnimationHelperTests
    {
        [Test]
        public void AnimationHelper_HasExpectedMethods()
        {
            // Ce test vérifie simplement que les méthodes requises existent
            Type animationHelperType = typeof(AnimationHelper);
            
            // Vérifier que les méthodes existent
            var applyDropAnimationMethod = animationHelperType.GetMethod("ApplyDropAnimation");
            Assert.That(applyDropAnimationMethod, Is.Not.Null, "La méthode ApplyDropAnimation doit exister");

            var animateItemHeightMethod = animationHelperType.GetMethod("AnimateItemHeight");
            Assert.That(animateItemHeightMethod, Is.Not.Null, "La méthode AnimateItemHeight doit exister");

            var applyPulseAnimationMethod = animationHelperType.GetMethod("ApplyPulseAnimation");
            Assert.That(applyPulseAnimationMethod, Is.Not.Null, "La méthode ApplyPulseAnimation doit exister");
        }
        
        [Test]
        public void AnimationHelper_HasExpectedMethodSignatures()
        {
            // Ce test vérifie les signatures des méthodes
            Type animationHelperType = typeof(AnimationHelper);
            
            // Vérifier la signature de ApplyDropAnimation
            var applyDropAnimationMethod = animationHelperType.GetMethod("ApplyDropAnimation");
            Assert.That(applyDropAnimationMethod, Is.Not.Null, "La méthode ApplyDropAnimation doit exister");
            Assert.That(applyDropAnimationMethod!.ReturnType, Is.EqualTo(typeof(void)), "ApplyDropAnimation doit retourner void");
            var dropParams = applyDropAnimationMethod.GetParameters();
            Assert.That(dropParams.Length, Is.EqualTo(1), "ApplyDropAnimation doit avoir un paramètre");
            Assert.That(dropParams[0].ParameterType, Is.EqualTo(typeof(FrameworkElement)),
                "Le paramètre de ApplyDropAnimation doit être de type FrameworkElement");
            
            // Vérifier la signature de AnimateItemHeight
            var animateItemHeightMethod = animationHelperType.GetMethod("AnimateItemHeight");
            Assert.That(animateItemHeightMethod, Is.Not.Null, "La méthode AnimateItemHeight doit exister");
            Assert.That(animateItemHeightMethod!.ReturnType, Is.EqualTo(typeof(void)), "AnimateItemHeight doit retourner void");
            var heightParams = animateItemHeightMethod.GetParameters();
            Assert.That(heightParams.Length, Is.EqualTo(3), "AnimateItemHeight doit avoir trois paramètres");
            Assert.That(heightParams[0].ParameterType, Is.EqualTo(typeof(ItemsControl)),
                "Le premier paramètre de AnimateItemHeight doit être de type ItemsControl");
            Assert.That(heightParams[1].ParameterType, Is.EqualTo(typeof(int)),
                "Le deuxième paramètre de AnimateItemHeight doit être de type int");
            Assert.That(heightParams[2].ParameterType, Is.EqualTo(typeof(bool)),
                "Le troisième paramètre de AnimateItemHeight doit être de type bool");
            
            // Vérifier la signature de ApplyPulseAnimation
            var applyPulseAnimationMethod = animationHelperType.GetMethod("ApplyPulseAnimation");
            Assert.That(applyPulseAnimationMethod, Is.Not.Null, "La méthode ApplyPulseAnimation doit exister");
            Assert.That(applyPulseAnimationMethod!.ReturnType, Is.EqualTo(typeof(void)), "ApplyPulseAnimation doit retourner void");
            var pulseParams = applyPulseAnimationMethod.GetParameters();
            Assert.That(pulseParams.Length, Is.EqualTo(1), "ApplyPulseAnimation doit avoir un paramètre");
            Assert.That(pulseParams[0].ParameterType, Is.EqualTo(typeof(FrameworkElement)),
                "Le paramètre de ApplyPulseAnimation doit être de type FrameworkElement");
        }
        
        [Test]
        public void AnimationHelper_IsStaticClass()
        {
            // Vérifier que la classe AnimationHelper est statique (abstraite et scellée)
            Type animationHelperType = typeof(AnimationHelper);
            Assert.That(animationHelperType.IsAbstract && animationHelperType.IsSealed, Is.True,
                "AnimationHelper doit être une classe statique (abstraite et scellée)");
        }
    }
} 