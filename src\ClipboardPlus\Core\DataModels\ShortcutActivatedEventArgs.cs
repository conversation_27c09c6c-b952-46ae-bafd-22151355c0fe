using System;

namespace ClipboardPlus.Core.DataModels
{
    /// <summary>
    /// Arguments d'événement pour l'activation d'un raccourci global.
    /// Fournit des informations sur le raccourci qui a été activé.
    /// </summary>
    public class ShortcutActivatedEventArgs : EventArgs
    {
        /// <summary>
        /// Initialise une nouvelle instance de ShortcutActivatedEventArgs.
        /// </summary>
        /// <param name="activatedShortcut">Le raccourci qui a été activé.</param>
        public ShortcutActivatedEventArgs(KeyCombination activatedShortcut)
        {
            ActivatedShortcut = activatedShortcut ?? throw new ArgumentNullException(nameof(activatedShortcut));
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// Le raccourci qui a été activé.
        /// </summary>
        public KeyCombination ActivatedShortcut { get; }

        /// <summary>
        /// Timestamp de l'activation du raccourci.
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// Indique si l'événement a été traité.
        /// </summary>
        public bool Handled { get; set; }
    }
}
