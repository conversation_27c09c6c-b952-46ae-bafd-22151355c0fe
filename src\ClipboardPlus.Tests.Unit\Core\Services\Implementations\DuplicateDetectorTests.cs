using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Implementations;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.Implementations
{
    [TestFixture]
    public class DuplicateDetectorTests
    {
        private DuplicateDetector _duplicateDetector = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private List<ClipboardItem> _testHistoryItems = null!;

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _duplicateDetector = new DuplicateDetector(_mockLoggingService.Object);

            // Créer des éléments de test pour l'historique
            _testHistoryItems = new List<ClipboardItem>
            {
                new ClipboardItem
                {
                    Id = 1,
                    DataType = ClipboardDataType.Text,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Hello World"),
                    Timestamp = DateTime.Now.AddMinutes(-10)
                },
                new ClipboardItem
                {
                    Id = 2,
                    DataType = ClipboardDataType.Text,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Different Text"),
                    Timestamp = DateTime.Now.AddMinutes(-5)
                },
                new ClipboardItem
                {
                    Id = 3,
                    DataType = ClipboardDataType.Image,
                    RawData = new byte[] { 0x89, 0x50, 0x4E, 0x47 }, // PNG header
                    Timestamp = DateTime.Now.AddMinutes(-3)
                }
            };
        }

        #region Tests FindDuplicateAsync

        [Test]
        public async Task FindDuplicateAsync_WithNullItem_ReturnsNull()
        {
            // Act
            var result = await _duplicateDetector.FindDuplicateAsync(null!, _testHistoryItems);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task FindDuplicateAsync_WithNullRawData_ReturnsNull()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = null
            };

            // Act
            var result = await _duplicateDetector.FindDuplicateAsync(item, _testHistoryItems);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task FindDuplicateAsync_WithEmptyRawData_ReturnsNull()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = Array.Empty<byte>()
            };

            // Act
            var result = await _duplicateDetector.FindDuplicateAsync(item, _testHistoryItems);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task FindDuplicateAsync_WithNullHistoryItems_ReturnsNull()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test")
            };

            // Act
            var result = await _duplicateDetector.FindDuplicateAsync(item, null!);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task FindDuplicateAsync_WithEmptyHistoryItems_ReturnsNull()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test")
            };

            // Act
            var result = await _duplicateDetector.FindDuplicateAsync(item, new List<ClipboardItem>());

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task FindDuplicateAsync_WithExactDuplicate_ReturnsExistingItem()
        {
            // Arrange
            var existingItem = _testHistoryItems.First();
            var duplicateItem = new ClipboardItem
            {
                Id = 999, // ID différent
                DataType = existingItem.DataType,
                RawData = existingItem.RawData, // Même données
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _duplicateDetector.FindDuplicateAsync(duplicateItem, _testHistoryItems);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(existingItem.Id));
            Assert.That(result, Is.EqualTo(existingItem));
        }

        [Test]
        public async Task FindDuplicateAsync_WithDifferentDataType_ReturnsNull()
        {
            // Arrange
            var existingItem = _testHistoryItems.First(i => i.DataType == ClipboardDataType.Text);
            var differentTypeItem = new ClipboardItem
            {
                Id = 999,
                DataType = ClipboardDataType.Image, // Type différent
                RawData = existingItem.RawData, // Même données
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _duplicateDetector.FindDuplicateAsync(differentTypeItem, _testHistoryItems);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task FindDuplicateAsync_WithDifferentData_ReturnsNull()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Completely Different Text"),
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _duplicateDetector.FindDuplicateAsync(item, _testHistoryItems);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task FindDuplicateAsync_WithMultipleSameTypeItems_ReturnsCorrectDuplicate()
        {
            // Arrange
            var targetItem = _testHistoryItems.First(i => i.DataType == ClipboardDataType.Text && i.Id == 2);
            var duplicateItem = new ClipboardItem
            {
                Id = 999,
                DataType = targetItem.DataType,
                RawData = targetItem.RawData,
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _duplicateDetector.FindDuplicateAsync(duplicateItem, _testHistoryItems);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(targetItem.Id));
        }

        #endregion

        #region Tests AreDuplicates

        [Test]
        public void AreDuplicates_WithNullItems_ReturnsFalse()
        {
            // Act & Assert
            Assert.That(_duplicateDetector.AreDuplicates(null!, null!), Is.False);
            Assert.That(_duplicateDetector.AreDuplicates(_testHistoryItems.First(), null!), Is.False);
            Assert.That(_duplicateDetector.AreDuplicates(null!, _testHistoryItems.First()), Is.False);
        }

        [Test]
        public void AreDuplicates_WithDifferentDataTypes_ReturnsFalse()
        {
            // Arrange
            var textItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test")
            };
            var imageItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Image,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test") // Même données mais type différent
            };

            // Act
            var result = _duplicateDetector.AreDuplicates(textItem, imageItem);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void AreDuplicates_WithNullRawData_ReturnsFalse()
        {
            // Arrange
            var item1 = new ClipboardItem { DataType = ClipboardDataType.Text, RawData = null };
            var item2 = new ClipboardItem { DataType = ClipboardDataType.Text, RawData = null };

            // Act
            var result = _duplicateDetector.AreDuplicates(item1, item2);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void AreDuplicates_WithDifferentDataLength_ReturnsFalse()
        {
            // Arrange
            var item1 = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Short")
            };
            var item2 = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Much longer text")
            };

            // Act
            var result = _duplicateDetector.AreDuplicates(item1, item2);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void AreDuplicates_WithIdenticalData_ReturnsTrue()
        {
            // Arrange
            var data = System.Text.Encoding.UTF8.GetBytes("Identical content");
            var item1 = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = data
            };
            var item2 = new ClipboardItem
            {
                Id = 2, // ID différent
                DataType = ClipboardDataType.Text,
                RawData = data // Même données
            };

            // Act
            var result = _duplicateDetector.AreDuplicates(item1, item2);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void AreDuplicates_WithSimilarButDifferentData_ReturnsFalse()
        {
            // Arrange
            var item1 = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Hello World")
            };
            var item2 = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Hello world") // Différence de casse
            };

            // Act
            var result = _duplicateDetector.AreDuplicates(item1, item2);

            // Assert
            Assert.That(result, Is.False);
        }

        #endregion

        #region Tests de Logging

        [Test]
        public async Task FindDuplicateAsync_LogsOperationStart()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test")
            };

            // Act
            await _duplicateDetector.FindDuplicateAsync(item, _testHistoryItems);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("DuplicateDetector.FindDuplicateAsync - Début recherche"))),
                Times.Once);
        }

        [Test]
        public async Task FindDuplicateAsync_LogsWhenDuplicateFound()
        {
            // Arrange
            var existingItem = _testHistoryItems.First();
            var duplicateItem = new ClipboardItem
            {
                DataType = existingItem.DataType,
                RawData = existingItem.RawData
            };

            // Act
            await _duplicateDetector.FindDuplicateAsync(duplicateItem, _testHistoryItems);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Doublon trouvé"))),
                Times.Once);
        }

        [Test]
        public async Task FindDuplicateAsync_LogsWhenNoDuplicateFound()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Unique content")
            };

            // Act
            await _duplicateDetector.FindDuplicateAsync(item, _testHistoryItems);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Aucun doublon trouvé"))),
                Times.Once);
        }

        #endregion
    }
}
