using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service de surveillance de la santé des collections ViewModel-Manager
    /// </summary>
    public class CollectionHealthService : ICollectionHealthService, IDisposable
    {
        private readonly IClipboardHistoryManager _manager;
        private readonly ILoggingService _logger;
        private readonly System.Threading.Timer _healthCheckTimer;
        private readonly object _lockObject = new();
        
        private ClipboardHistoryViewModel? _viewModel;
        private bool _isMonitoring;
        private bool _lastHealthState = true;
        private bool _disposed;

        public event EventHandler<CollectionHealthEventArgs>? HealthChanged;

        public CollectionHealthService(
            IClipboardHistoryManager manager, 
            ILoggingService logger)
        {
            _manager = manager ?? throw new ArgumentNullException(nameof(manager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // Timer pour vérification périodique (toutes les 30 secondes)
            _healthCheckTimer = new System.Threading.Timer(PerformPeriodicHealthCheck, null, Timeout.Infinite, Timeout.Infinite);
            
            _logger.LogInfo("🏥 CollectionHealthService initialisé");
        }

        /// <summary>
        /// Enregistre le ViewModel à surveiller
        /// </summary>
        public void RegisterViewModel(ClipboardHistoryViewModel viewModel)
        {
            lock (_lockObject)
            {
                _viewModel = viewModel;
                _logger.LogInfo("🏥 ViewModel enregistré pour surveillance");
            }
        }

        public async Task<CollectionHealthReport> CheckHealthAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var operationId = Guid.NewGuid().ToString("N")[..8];
            
            var report = new CollectionHealthReport
            {
                OperationId = operationId,
                Timestamp = DateTime.Now
            };

            try
            {
                _logger.LogDebug($"🏥 [{operationId}] Début vérification santé");

                // 1. Vérifier l'accessibilité du Manager
                await CheckManagerAccessibility(report);

                // 2. Vérifier la cohérence des collections si ViewModel disponible
                if (_viewModel != null)
                {
                    await CheckCollectionConsistency(report);
                }
                else
                {
                    report.Issues.Add("ViewModel non enregistré pour surveillance");
                }

                // 3. Déterminer l'état de santé global
                report.IsHealthy = report.Issues.Count == 0 && report.ManagerAccessible;

                stopwatch.Stop();
                report.CheckDuration = stopwatch.Elapsed;

                _logger.LogDebug($"🏥 [{operationId}] Vérification terminée - Santé: {report.IsHealthy}, Durée: {report.CheckDuration.TotalMilliseconds}ms");

                // 4. Déclencher événement si changement d'état
                if (report.IsHealthy != _lastHealthState)
                {
                    var args = new CollectionHealthEventArgs(report, _lastHealthState);
                    HealthChanged?.Invoke(this, args);
                    _lastHealthState = report.IsHealthy;
                    
                    _logger.LogInfo($"🏥 [{operationId}] Changement d'état de santé: {!report.IsHealthy} → {report.IsHealthy}");
                }
            }
            catch (Exception ex)
            {
                report.IsHealthy = false;
                report.Issues.Add($"Erreur lors de la vérification: {ex.Message}");
                _logger.LogError($"🏥 [{operationId}] Erreur vérification santé: {ex.Message}", ex);
            }

            return report;
        }

        private Task CheckManagerAccessibility(CollectionHealthReport report)
        {
            try
            {
                if (_manager == null)
                {
                    report.ManagerAccessible = false;
                    report.Issues.Add("Manager est null");
                    return Task.CompletedTask;
                }

                // Test d'accès aux propriétés
                var items = _manager.HistoryItems;
                if (items == null)
                {
                    report.ManagerAccessible = false;
                    report.Issues.Add("Manager.HistoryItems est null");
                    return Task.CompletedTask;
                }

                report.ManagerItemCount = items.Count;
                report.ManagerAccessible = true;
                
                _logger.LogDebug($"🏥 Manager accessible - {report.ManagerItemCount} items");
            }
            catch (Exception ex)
            {
                report.ManagerAccessible = false;
                report.Issues.Add($"Manager inaccessible: {ex.Message}");
                _logger.LogWarning($"🏥 Manager inaccessible: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        private Task CheckCollectionConsistency(CollectionHealthReport report)
        {
            try
            {
                if (_viewModel?.HistoryItems == null)
                {
                    report.Issues.Add("ViewModel.HistoryItems est null");
                    return Task.CompletedTask;
                }

                report.ViewModelItemCount = _viewModel.HistoryItems.Count;
                
                // Vérifier la cohérence des comptes
                var difference = Math.Abs(report.ViewModelItemCount - report.ManagerItemCount);
                if (difference > 1) // Tolérance de 1 pour les opérations en cours
                {
                    report.Issues.Add($"Désynchronisation détectée: ViewModel={report.ViewModelItemCount}, Manager={report.ManagerItemCount}");
                    _logger.LogWarning($"🏥 Désynchronisation: VM={report.ViewModelItemCount}, Manager={report.ManagerItemCount}");
                }
                else
                {
                    _logger.LogDebug($"🏥 Collections synchronisées: {report.ViewModelItemCount} items");
                }
            }
            catch (Exception ex)
            {
                report.Issues.Add($"Erreur vérification cohérence: {ex.Message}");
                _logger.LogError($"🏥 Erreur vérification cohérence: {ex.Message}", ex);
            }

            return Task.CompletedTask;
        }

        public async Task<bool> RepairInconsistenciesAsync()
        {
            var operationId = Guid.NewGuid().ToString("N")[..8];
            _logger.LogInfo($"🔧 [{operationId}] Début réparation des incohérences");

            try
            {
                // Vérifier d'abord l'état actuel
                var healthReport = await CheckHealthAsync();
                if (healthReport.IsHealthy)
                {
                    _logger.LogInfo($"🔧 [{operationId}] Aucune réparation nécessaire - système sain");
                    return true;
                }

                // Tenter la synchronisation si ViewModel disponible
                if (_viewModel != null && healthReport.Issues.Any(i => i.Contains("Désynchronisation")))
                {
                    _logger.LogInfo($"🔧 [{operationId}] Tentative de synchronisation ViewModel-Manager");
                    
                    // Utiliser le service de synchronisation existant si disponible
                    var syncService = _viewModel.GetType().GetField("_historyCollectionSynchronizer", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.GetValue(_viewModel);
                    
                    if (syncService != null)
                    {
                        var loadMethod = syncService.GetType().GetMethod("LoadHistoryAsync");
                        if (loadMethod != null)
                        {
                            await (Task)loadMethod.Invoke(syncService, new object[] { "health_service_repair" })!;
                            _logger.LogInfo($"🔧 [{operationId}] Synchronisation effectuée");
                            return true;
                        }
                    }
                }

                _logger.LogWarning($"🔧 [{operationId}] Impossible de réparer automatiquement");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"🔧 [{operationId}] Erreur lors de la réparation: {ex.Message}", ex);
                return false;
            }
        }

        public void StartMonitoring()
        {
            lock (_lockObject)
            {
                if (_isMonitoring || _disposed) return;

                _healthCheckTimer.Change(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
                _isMonitoring = true;
                _logger.LogInfo("🏥 Surveillance automatique démarrée (30s)");
            }
        }

        public void StopMonitoring()
        {
            lock (_lockObject)
            {
                if (!_isMonitoring) return;

                _healthCheckTimer.Change(Timeout.Infinite, Timeout.Infinite);
                _isMonitoring = false;
                _logger.LogInfo("🏥 Surveillance automatique arrêtée");
            }
        }

        private async void PerformPeriodicHealthCheck(object? state)
        {
            if (_disposed) return;

            try
            {
                await CheckHealthAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"🏥 Erreur vérification périodique: {ex.Message}", ex);
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            lock (_lockObject)
            {
                _disposed = true;
                _isMonitoring = false;
                _healthCheckTimer?.Dispose();
                _logger.LogInfo("🏥 CollectionHealthService disposé");
            }
        }
    }
}
