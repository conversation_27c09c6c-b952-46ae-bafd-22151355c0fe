using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Reflection;
using ClipboardPlus.Core.DataModels;
using WpfApplication = System.Windows.Application;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Gestionnaire des thèmes utilisateur.
    /// </summary>
    public class UserThemeManager : IUserThemeManager
    {
        private List<ThemeInfo> _availableThemes;
        private ThemeInfo _activeTheme;
        private readonly string _assemblyName;
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de UserThemeManager.
        /// </summary>
        public UserThemeManager(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
            _availableThemes = new List<ThemeInfo>();
            _assemblyName = Assembly.GetExecutingAssembly().GetName().Name ?? "ClipboardPlus";
            _activeTheme = new ThemeInfo("Default", $"pack://application:,,,/{_assemblyName};component/UI/Themes/Default.xaml");
            
            // Charger les thèmes disponibles
            LoadAvailableThemes();
        }

        /// <summary>
        /// Récupère la liste des thèmes disponibles.
        /// </summary>
        /// <returns>Liste des thèmes disponibles.</returns>
        public IEnumerable<ThemeInfo> GetAvailableThemes()
        {
            return _availableThemes;
        }

        /// <summary>
        /// Applique un thème à l'application.
        /// </summary>
        /// <param name="theme">Le thème à appliquer.</param>
        /// <returns>Tâche asynchrone.</returns>
        public async Task ApplyThemeAsync(ThemeInfo theme)
        {
            if (theme == null)
                throw new ArgumentNullException(nameof(theme));

            try
            {
                // Utiliser le dispatcher de l'application pour modifier les ressources
                await WpfApplication.Current.Dispatcher.InvokeAsync(() =>
                {
                    var dictionaries = WpfApplication.Current.Resources.MergedDictionaries;
                    
                    // Supprimer les dictionnaires de thème existants
                    var themeDictionaries = dictionaries
                        .Where(d => d.Source?.ToString().Contains("/Themes/") == true)
                        .ToList();
                    
                    foreach (var dict in themeDictionaries)
                    {
                        dictionaries.Remove(dict);
                    }
                    
                    try
                    {
                        // Charger le ResourceDictionary du thème
                        var resourceDict = new ResourceDictionary { Source = new Uri(theme.FilePath, UriKind.Absolute) };
                        
                        // Mettre à jour les ressources de l'application
                        dictionaries.Add(resourceDict);
                        
                        // Mettre à jour le thème actif
                        _activeTheme = theme;
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"Erreur lors du chargement du thème {theme.Name} ({theme.FilePath}): {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors de l'application du thème: {ex.Message}", ex);
                throw; // Relancer l'exception pour informer l'appelant
            }
        }

        /// <summary>
        /// Récupère le thème actif.
        /// </summary>
        /// <returns>Le thème actif.</returns>
        public ThemeInfo GetActiveTheme()
        {
            return _activeTheme;
        }

        /// <summary>
        /// Charge le thème actif au démarrage de l'application.
        /// </summary>
        /// <param name="themePath">Chemin du thème à charger.</param>
        /// <returns>Tâche asynchrone.</returns>
        public async Task LoadActiveThemeAsync(string themePath)
        {
            // Trouver le thème correspondant au chemin
            var theme = _availableThemes.FirstOrDefault(t => 
                Path.GetFileName(new Uri(t.FilePath).LocalPath) == 
                Path.GetFileName(themePath)
            );
            
            // Si le thème n'est pas trouvé, utiliser le thème par défaut
            if (theme == null)
            {
                theme = _availableThemes.FirstOrDefault(t => t.Name == "Default") ?? _activeTheme;
            }
            
            // Appliquer le thème
            await ApplyThemeAsync(theme);
        }

        /// <summary>
        /// Charge la liste des thèmes disponibles.
        /// </summary>
        private void LoadAvailableThemes()
        {
            _availableThemes.Clear();
            
            // Ajouter les thèmes connus
            _availableThemes.Add(new ThemeInfo("Default", $"pack://application:,,,/{_assemblyName};component/UI/Themes/Default.xaml"));
            _availableThemes.Add(new ThemeInfo("Light", $"pack://application:,,,/{_assemblyName};component/UI/Themes/Light.xaml"));
            _availableThemes.Add(new ThemeInfo("Dark", $"pack://application:,,,/{_assemblyName};component/UI/Themes/Dark.xaml"));
        }
    }
} 