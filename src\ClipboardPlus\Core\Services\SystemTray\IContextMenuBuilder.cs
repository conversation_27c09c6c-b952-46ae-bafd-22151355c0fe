using System;
using System.Windows.Controls;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Interface pour la construction du menu contextuel.
    /// Responsabilité unique : construire le menu contextuel avec les éléments appropriés.
    /// </summary>
    public interface IContextMenuBuilder
    {
        /// <summary>
        /// Construit le menu contextuel complet avec tous les éléments.
        /// </summary>
        /// <param name="onHistoryClick">Action à exécuter lors du clic sur "Afficher l'historique".</param>
        /// <param name="onSettingsClick">Action à exécuter lors du clic sur "Paramètres".</param>
        /// <param name="onExitClick">Action à exécuter lors du clic sur "Quitter".</param>
        /// <returns>Le menu contextuel configuré avec tous les éléments.</returns>
        ContextMenu BuildContextMenu(
            Action onHistoryClick,
            Action onSettingsClick,
            Action onExitClick);

        /// <summary>
        /// Crée un élément de menu avec le texte et l'action spécifiés.
        /// </summary>
        /// <param name="header">Le texte de l'élément de menu.</param>
        /// <param name="clickAction">L'action à exécuter lors du clic.</param>
        /// <returns>L'élément de menu configuré.</returns>
        MenuItem CreateMenuItem(string header, Action clickAction);

        /// <summary>
        /// Crée un séparateur pour le menu.
        /// </summary>
        /// <returns>Un séparateur de menu.</returns>
        Separator CreateSeparator();
    }
}
