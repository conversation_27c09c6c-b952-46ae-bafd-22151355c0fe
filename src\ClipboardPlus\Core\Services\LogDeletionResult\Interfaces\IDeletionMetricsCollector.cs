using ClipboardPlus.Core.Services.LogDeletionResult.Models;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Interfaces
{
    /// <summary>
    /// Interface pour la collecte de métriques sur les opérations de suppression.
    /// Permet de surveiller les performances et la fiabilité du système.
    /// </summary>
    public interface IDeletionMetricsCollector
    {
        /// <summary>
        /// Enregistre une tentative de suppression.
        /// </summary>
        /// <param name="context">Contexte de la suppression</param>
        void RecordDeletionAttempt(DeletionResultContext context);

        /// <summary>
        /// Enregistre le résultat d'une opération de logging.
        /// </summary>
        /// <param name="result">Résultat de l'opération</param>
        void RecordLoggingResult(DeletionLoggingResult result);

        /// <summary>
        /// Enregistre les métriques de performance d'une opération.
        /// </summary>
        /// <param name="metrics">Métriques de performance</param>
        void RecordPerformanceMetrics(OperationPerformanceMetrics metrics);

        /// <summary>
        /// Enregistre une erreur survenue pendant le logging.
        /// </summary>
        /// <param name="error">Détails de l'erreur</param>
        void RecordError(DeletionLoggingError error);

        /// <summary>
        /// Obtient les métriques globales du système.
        /// </summary>
        /// <returns>Métriques globales</returns>
        DeletionMetrics GetMetrics();

        /// <summary>
        /// Obtient les métriques de performance.
        /// </summary>
        /// <returns>Métriques de performance</returns>
        DeletionLoggerMetrics GetPerformanceMetrics();

        /// <summary>
        /// Obtient les métriques d'erreurs.
        /// </summary>
        /// <returns>Métriques d'erreurs</returns>
        ErrorMetrics GetErrorMetrics();

        /// <summary>
        /// Réinitialise toutes les métriques.
        /// </summary>
        void ResetMetrics();

        /// <summary>
        /// Réinitialise uniquement les métriques de performance.
        /// </summary>
        void ResetPerformanceMetrics();

        /// <summary>
        /// Obtient un rapport détaillé des métriques.
        /// </summary>
        /// <returns>Rapport formaté des métriques</returns>
        string GenerateMetricsReport();
    }
}
