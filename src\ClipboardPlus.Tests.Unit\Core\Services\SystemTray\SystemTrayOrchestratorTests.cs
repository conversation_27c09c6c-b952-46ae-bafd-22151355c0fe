using System;
using System.Drawing;
using System.Threading;
using System.Windows.Controls;
using System.Windows.Forms;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;
using ClipboardPlus.Core.Services.Windows;

namespace ClipboardPlus.Tests.Unit.Core.Services.SystemTray
{
    /// <summary>
    /// Tests unitaires pour SystemTrayOrchestrator.
    /// Vérifie la responsabilité unique : orchestration de l'initialisation complète du système tray.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class SystemTrayOrchestratorTests
    {
        private Mock<IThreadValidator> _mockThreadValidator = null!;
        private Mock<INotifyIconCleanupService> _mockCleanupService = null!;
        private Mock<INotifyIconFactory> _mockNotifyIconFactory = null!;
        private Mock<IIconResourceLoader> _mockIconLoader = null!;
        private Mock<IContextMenuBuilder> _mockMenuBuilder = null!;
        private Mock<IVisibilityManager> _mockVisibilityManager = null!;
        private Mock<IStartupNotificationService> _mockNotificationService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IServiceProvider> _mockServiceProvider = null!;
        private Mock<IHistoryWindowService> _mockHistoryWindowService = null!;
        private Mock<ISettingsWindowService> _mockSettingsWindowService = null!;
        private SystemTrayOrchestrator _orchestrator = null!;

        [SetUp]
        public void SetUp()
        {
            _mockThreadValidator = new Mock<IThreadValidator>();
            _mockCleanupService = new Mock<INotifyIconCleanupService>();
            _mockNotifyIconFactory = new Mock<INotifyIconFactory>();
            _mockIconLoader = new Mock<IIconResourceLoader>();
            _mockMenuBuilder = new Mock<IContextMenuBuilder>();
            _mockVisibilityManager = new Mock<IVisibilityManager>();
            _mockNotificationService = new Mock<IStartupNotificationService>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockHistoryWindowService = new Mock<IHistoryWindowService>();
            _mockSettingsWindowService = new Mock<ISettingsWindowService>();

            _orchestrator = new SystemTrayOrchestrator(
                _mockThreadValidator.Object,
                _mockCleanupService.Object,
                _mockNotifyIconFactory.Object,
                _mockIconLoader.Object,
                _mockMenuBuilder.Object,
                _mockVisibilityManager.Object,
                _mockNotificationService.Object,
                _mockLoggingService.Object,
                _mockServiceProvider.Object,
                _mockHistoryWindowService.Object,
                _mockSettingsWindowService.Object);
        }

        [Test]
        public void Constructor_WithNullThreadValidator_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new SystemTrayOrchestrator(
                null, _mockCleanupService.Object, _mockNotifyIconFactory.Object, _mockIconLoader.Object,
                _mockMenuBuilder.Object, _mockVisibilityManager.Object, _mockNotificationService.Object,
                _mockLoggingService.Object, _mockServiceProvider.Object,
                _mockHistoryWindowService.Object, _mockSettingsWindowService.Object));
        }

        [Test]
        public void Constructor_WithNullCleanupService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new SystemTrayOrchestrator(
                _mockThreadValidator.Object, null, _mockNotifyIconFactory.Object, _mockIconLoader.Object,
                _mockMenuBuilder.Object, _mockVisibilityManager.Object, _mockNotificationService.Object,
                _mockLoggingService.Object, _mockServiceProvider.Object,
                _mockHistoryWindowService.Object, _mockSettingsWindowService.Object));
        }

        [Test]
        public void IsInitialized_BeforeInitialize_ReturnsFalse()
        {
            // Act & Assert
            Assert.That(_orchestrator.IsInitialized, Is.False, "Should return false before initialization");
        }

        [Test]
        public void Initialize_WithValidDependencies_InitializesSuccessfully()
        {
            // Arrange
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();

            _mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(false);
            _mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            _mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            _mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()))
                           .Returns(mockContextMenu);
            _mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            // Act
            _orchestrator.Initialize();

            // Assert
            Assert.That(_orchestrator.IsInitialized, Is.True, "Should be initialized after successful Initialize call");

            // Vérifier que toutes les étapes ont été appelées dans l'ordre
            _mockLoggingService.Verify(x => x.LogInfo("========== SystemTrayOrchestrator.Initialize: DÉBUT =========="), Times.Once);
            _mockThreadValidator.Verify(x => x.ValidateUIThread(), Times.Once);
            _mockCleanupService.Verify(x => x.RequiresCleanup(It.IsAny<NotifyIcon>()), Times.Once);
            _mockNotifyIconFactory.Verify(x => x.CreateNotifyIcon("ClipboardPlus", true), Times.Once);
            _mockIconLoader.Verify(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico"), Times.Once);
            _mockNotifyIconFactory.Verify(x => x.ConfigureIcon(realNotifyIcon, mockIcon), Times.Once);
            _mockMenuBuilder.Verify(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()), Times.Once);
            _mockVisibilityManager.Verify(x => x.ConfigureInitialVisibility(realNotifyIcon), Times.Once);
            _mockNotificationService.Verify(x => x.ShowStartupNotification(realNotifyIcon), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo("SystemTrayOrchestrator: Initialisation terminée avec succès"), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo("========== SystemTrayOrchestrator.Initialize: FIN =========="), Times.Once);

            // Cleanup
            realNotifyIcon.Dispose();
        }

        [Test]
        public void Initialize_WithCleanupRequired_CleansUpExistingIcon()
        {
            // Arrange
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();

            _mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(true);
            _mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            _mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            _mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()))
                           .Returns(mockContextMenu);
            _mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            // Act
            _orchestrator.Initialize();

            // Assert
            _mockCleanupService.Verify(x => x.CleanupExistingIcon(It.IsAny<NotifyIcon>()), Times.Once, "Should cleanup existing icon when required");
            Assert.That(_orchestrator.IsInitialized, Is.True, "Should be initialized after cleanup and recreation");

            // Cleanup
            realNotifyIcon.Dispose();
        }

        [Test]
        public void Initialize_WithException_CleansUpAndRethrows()
        {
            // Arrange
            _mockThreadValidator.Setup(x => x.ValidateUIThread()).Throws(new InvalidOperationException("Test exception"));

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => _orchestrator.Initialize());

            // Vérifier que l'erreur a été loggée
            _mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de l'initialisation")), It.IsAny<Exception>()),
                Times.Once,
                "Should log error when initialization fails");

            // Vérifier que l'état n'est pas initialisé
            Assert.That(_orchestrator.IsInitialized, Is.False, "Should not be initialized when exception occurs");
        }

        [Test]
        public void Initialize_AfterDispose_ThrowsObjectDisposedException()
        {
            // Arrange
            _orchestrator.Dispose();

            // Act & Assert
            Assert.Throws<ObjectDisposedException>(() => _orchestrator.Initialize());
        }

        [Test]
        public void ShowNotification_WhenNotInitialized_LogsErrorAndReturns()
        {
            // Act
            _orchestrator.ShowNotification("Title", "Message", ToolTipIcon.Info);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogError(It.IsAny<string>(), It.IsAny<Exception>()),
                Times.Once());

            _mockNotificationService.Verify(
                x => x.ShowCustomNotification(It.IsAny<NotifyIcon>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<ToolTipIcon>()),
                Times.Never());
        }

        [Test]
        public void ShowNotification_WhenInitialized_CallsNotificationService()
        {
            // Arrange
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();

            _mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(false);
            _mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            _mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            _mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()))
                           .Returns(mockContextMenu);
            _mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            _orchestrator.Initialize();

            // Act
            _orchestrator.ShowNotification("Test Title", "Test Message", ToolTipIcon.Warning);

            // Assert
            _mockNotificationService.Verify(
                x => x.ShowCustomNotification(realNotifyIcon, "Test Title", "Test Message", ToolTipIcon.Warning),
                Times.Once,
                "Should call notification service with correct parameters");

            // Cleanup
            realNotifyIcon.Dispose();
        }

        [Test]
        public void ShowNotification_AfterDispose_ThrowsObjectDisposedException()
        {
            // Arrange
            _orchestrator.Dispose();

            // Act & Assert
            Assert.Throws<ObjectDisposedException>(() => _orchestrator.ShowNotification("Title", "Message", ToolTipIcon.Info));
        }

        [Test]
        public void Dispose_CleansUpResources()
        {
            // Arrange
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();

            _mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(false);
            _mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            _mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            _mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()))
                           .Returns(mockContextMenu);
            _mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            _orchestrator.Initialize();

            // Act
            _orchestrator.Dispose();

            // Assert
            Assert.That(_orchestrator.IsInitialized, Is.False, "Should not be initialized after disposal");

            _mockLoggingService.Verify(x => x.LogInfo("SystemTrayOrchestrator: Disposition des ressources..."), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo("SystemTrayOrchestrator: Nettoyage des ressources..."), Times.Once);
            _mockCleanupService.Verify(x => x.CleanupExistingIcon(realNotifyIcon), Times.Once, "Should cleanup icon during disposal");
            _mockLoggingService.Verify(x => x.LogInfo("SystemTrayOrchestrator: Disposition terminée"), Times.Once);
        }

        [Test]
        public void Dispose_CalledMultipleTimes_OnlyDisposesOnce()
        {
            // Act
            _orchestrator.Dispose();
            _orchestrator.Dispose();

            // Assert
            _mockLoggingService.Verify(x => x.LogInfo("SystemTrayOrchestrator: Disposition des ressources..."), Times.Once,
                "Should only dispose once even when called multiple times");
        }

        [Test]
        public void IsInitialized_AfterDispose_ReturnsFalse()
        {
            // Arrange
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();

            _mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(false);
            _mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            _mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            _mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>()))
                           .Returns(mockContextMenu);
            _mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            _orchestrator.Initialize();
            Assert.That(_orchestrator.IsInitialized, Is.True, "Should be initialized before disposal");

            // Act
            _orchestrator.Dispose();

            // Assert
            Assert.That(_orchestrator.IsInitialized, Is.False, "Should not be initialized after disposal");

            // Cleanup
            realNotifyIcon.Dispose();
        }

        [TearDown]
        public void TearDown()
        {
            _orchestrator?.Dispose();
        }
    }
}
