using System;
using System.Threading;
using System.Windows;
using NUnit.Framework;
using Moq;
using ClipboardPlus;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using System.Threading.Tasks;

namespace ClipboardPlus.Tests.STA
{
    /// <summary>
    /// Tests pour le processus de démarrage de l'application ClipboardPlus
    /// Nous évitons d'instancier complètement l'application pour éviter les problèmes de configuration XAML
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    [NonParallelizable]
    public class ApplicationStartupTests
    {
        private Type _appType = null!;
        
        [SetUp]
        public void Setup()
        {
            _appType = typeof(App);
        }
        
        // ---------------------------------------------
        // ATTENTION :
        // Il est formellement déconseillé de tester App.xaml.cs (ou System.Windows.Application) en exécutant réellement
        // ses méthodes de cycle de vie dans un test automatisé. Cela provoque des plantages, des états globaux instables,
        // et des comportements non déterministes (voir @rapport_plantage_testsSTA-App.md).
        // La couverture de la logique de démarrage doit être obtenue via des tests sur StartupLogic et les services métiers.
        // ---------------------------------------------
        
        [Test]
        [Description("Vérifie que le gestionnaire d'événement Application_Startup existe dans l'application")]
        public void Application_Startup_MethodExists()
        {
            // Arrange
            var appType = typeof(App);
            
            // Act
            var startupMethod = appType.GetMethod("Application_Startup", BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Assert
            Assert.That(startupMethod, Is.Not.Null, "La méthode 'Application_Startup' doit exister sur App");
            Assert.That(startupMethod!.ReturnType, Is.EqualTo(typeof(void)), "La méthode 'Application_Startup' doit retourner void");
        }
        
        [Test]
        [Description("Analyse la structure de la méthode Application_Startup pour la refactorisation")]
        public void Application_Startup_StructureAnalysis()
        {
            // Cette méthode ne teste pas le comportement mais analyse la structure
            // pour assister la refactorisation
            
            // Récupérer les champs importants
            // REFACTORISATION: Le champ _lifetimeManager a été supprimé et remplacé par un service injecté
            // Vérifier que le champ _lifetimeManager n'existe plus (refactorisation réussie)
            var lifetimeManagerField = _appType.GetField("_lifetimeManager", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(lifetimeManagerField, Is.Null, "Le champ _lifetimeManager doit être supprimé après refactorisation");
            
            // Récupérer le code source de la méthode (nous ne pouvons pas le faire directement, mais on peut analyser le IL)
            var startupMethod = _appType.GetMethod("Application_Startup", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(startupMethod, Is.Not.Null, "La méthode Application_Startup doit exister");
            
            // Examiner les informations de la méthode
            var methodBody = startupMethod!.ToString() ?? string.Empty;
            
            // Vérifier que la méthode contient les blocs logiques identifiés dans le plan de refactorisation
            Console.WriteLine("\nStructure de la méthode Application_Startup pour refactorisation :");
            Console.WriteLine($"- Signature: {methodBody}");
            
            // Nous ne pouvons pas vérifier le contenu exact du corps de la méthode via réflexion
            // Mais nous pouvons vérifier la présence des paramètres et le type de retour
            var parameters = startupMethod.GetParameters();
            Assert.That(parameters.Length, Is.EqualTo(2), "La méthode devrait avoir 2 paramètres");
            Assert.That(parameters[0].ParameterType, Is.EqualTo(typeof(object)), "Le premier paramètre devrait être de type object");
            Assert.That(parameters[1].ParameterType, Is.EqualTo(typeof(StartupEventArgs)), "Le second paramètre devrait être de type StartupEventArgs");
            
            // Analyse de la structure interne
            Console.WriteLine("\nStructure interne identifiée pour la refactorisation:");
            Console.WriteLine("1. Initialisation des services et logging");
            Console.WriteLine("2. Vérification de l'instance unique (ISingleInstanceService)");
            Console.WriteLine("3. Traitement des arguments de ligne de commande");
            Console.WriteLine("4. Orchestration du démarrage de l'application");
            Console.WriteLine("5. Finalisation du démarrage (fenêtre cachée, gestionnaires d'événements)");
            
            // Identification des blocs logiques via les champs utilisés
            Console.WriteLine("\nBlocs logiques identifiés par l'utilisation des champs:");
            Console.WriteLine($"- Bloc ISingleInstanceService (notif instance unique): {false}");
            Console.WriteLine($"- Bloc ICommandLineParser (arguments): {false}");
            Console.WriteLine($"- Bloc IStartupOrchestrator (orchestration): {false}");
            Console.WriteLine($"- Bloc ISystemTrayService (UI): {false}");
            Console.WriteLine($"- Bloc IApplicationLifetimeManager (cycle de vie): {lifetimeManagerField != null}");
            
            // Succès - ce test est principalement pour l'analyse
            Assert.Pass("Analyse de structure complétée pour la refactorisation");
        }
        
        [Test]
        [Description("Vérifie que la méthode OnStartup existe dans l'application")]
        public void Application_OnStartup_MethodExists()
        {
            // Arrange
            var appType = typeof(App);
            
            // Act
            var onStartupMethod = appType.GetMethod("OnStartup", BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Assert
            Assert.That(onStartupMethod, Is.Not.Null, "La méthode 'OnStartup' doit exister sur App");
            Assert.That(onStartupMethod!.ReturnType, Is.EqualTo(typeof(void)), "La méthode 'OnStartup' doit retourner void");
        }
        
        [Test]
        [Description("Vérifie que la méthode OnExit existe dans l'application")]
        public void Application_OnExit_MethodExists()
        {
            // Arrange
            var appType = typeof(App);
            
            // Act
            var onExitMethod = appType.GetMethod("OnExit", BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Assert
            Assert.That(onExitMethod, Is.Not.Null, "La méthode 'OnExit' doit exister sur App");
            Assert.That(onExitMethod!.ReturnType, Is.EqualTo(typeof(void)), "La méthode 'OnExit' doit retourner void");
        }
        
        [Test]
        [Description("Vérifie que la méthode ShowHistoryWindowFromService existe dans l'application")]
        public void ShowHistoryWindowFromService_MethodExists()
        {
            // Arrange
            var appType = typeof(App);
            
            // Act
            var method = appType.GetMethod("ShowHistoryWindowFromService", BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Assert
            Assert.That(method, Is.Not.Null, "La méthode 'ShowHistoryWindowFromService' doit exister sur App");
            Assert.That(method!.ReturnType, Is.EqualTo(typeof(void)), "La méthode 'ShowHistoryWindowFromService' doit retourner void");
        }
        
        [Test]
        [Description("Vérifie que la méthode ShortcutService_ShortcutActivated existe dans l'application")]
        public void ShortcutService_ShortcutActivated_MethodExists()
        {
            // Arrange
            var appType = typeof(App);
            
            // Act
            var method = appType.GetMethod("ShortcutService_ShortcutActivated", BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Assert
            Assert.That(method, Is.Not.Null, "La méthode 'ShortcutService_ShortcutActivated' doit exister sur App");
            Assert.That(method!.ReturnType, Is.EqualTo(typeof(void)), "La méthode 'ShortcutService_ShortcutActivated' doit retourner void");
        }
        
        [Test]
        [Description("Vérifie que la méthode ClipboardListener_ClipboardContentChanged existe dans l'application")]
        public void ClipboardListener_ClipboardContentChanged_MethodExists()
        {
            // Arrange
            var appType = typeof(App);

            // Act
            var method = appType.GetMethod("ClipboardListener_ClipboardContentChanged", BindingFlags.NonPublic | BindingFlags.Instance);

            // Assert
            Assert.That(method, Is.Not.Null, "La méthode 'ClipboardListener_ClipboardContentChanged' doit exister sur App");
            Assert.That(method!.ReturnType, Is.EqualTo(typeof(void)), "La méthode 'ClipboardListener_ClipboardContentChanged' doit retourner void");
        }

        /// <summary>
        /// Tests de caractérisation pour Application_Startup() avant refactorisation
        /// Ces tests documentent le comportement actuel pour garantir qu'aucune régression n'est introduite
        /// </summary>
        [TestFixture]
        [Description("Tests de caractérisation pour Application_Startup() - Phase 0 du plan de refactorisation")]
        public class ApplicationStartupCharacterizationTests
        {
            [Test]
            [Description("CARACTÉRISATION: Vérifie que Application_Startup a la signature exacte attendue")]
            public void Application_Startup_HasCorrectSignature()
            {
                // Arrange
                var appType = typeof(App);

                // Act
                var method = appType.GetMethod("Application_Startup", BindingFlags.NonPublic | BindingFlags.Instance);

                // Assert
                Assert.That(method, Is.Not.Null, "Application_Startup doit exister");
                Assert.That(method!.ReturnType, Is.EqualTo(typeof(void)), "Doit retourner void");

                var parameters = method.GetParameters();
                Assert.That(parameters.Length, Is.EqualTo(2), "Doit avoir exactement 2 paramètres");
                Assert.That(parameters[0].ParameterType, Is.EqualTo(typeof(object)), "Premier paramètre: object sender");
                Assert.That(parameters[1].ParameterType, Is.EqualTo(typeof(StartupEventArgs)), "Second paramètre: StartupEventArgs e");

                // Vérifier que la méthode est async void
                var isAsync = method.GetCustomAttributes(typeof(System.Runtime.CompilerServices.AsyncStateMachineAttribute), false).Length > 0;
                Assert.That(isAsync, Is.True, "Application_Startup doit être async void");
            }

            [Test]
            [Description("CARACTÉRISATION: Vérifie que ConfigureServices existe et est appelable")]
            public void ConfigureServices_MethodExists()
            {
                // Arrange
                var appType = typeof(App);

                // Act
                var method = appType.GetMethod("ConfigureServices", BindingFlags.NonPublic | BindingFlags.Instance);

                // Assert
                Assert.That(method, Is.Not.Null, "ConfigureServices doit exister");
                Assert.That(method!.ReturnType, Is.EqualTo(typeof(void)), "ConfigureServices doit retourner void");
                Assert.That(method.GetParameters().Length, Is.EqualTo(0), "ConfigureServices ne doit pas avoir de paramètres");
            }

            [Test]
            [Description("CARACTÉRISATION: Vérifie que les handlers d'événements existent avec les bonnes signatures")]
            public void EventHandlers_ExistWithCorrectSignatures()
            {
                // Arrange
                var appType = typeof(App);

                // Act & Assert - ShortcutService_ShortcutActivated
                var shortcutHandler = appType.GetMethod("ShortcutService_ShortcutActivated", BindingFlags.NonPublic | BindingFlags.Instance);
                Assert.That(shortcutHandler, Is.Not.Null, "ShortcutService_ShortcutActivated doit exister");
                Assert.That(shortcutHandler!.ReturnType, Is.EqualTo(typeof(void)), "ShortcutService_ShortcutActivated doit retourner void");

                var shortcutParams = shortcutHandler.GetParameters();
                Assert.That(shortcutParams.Length, Is.EqualTo(2), "ShortcutService_ShortcutActivated doit avoir 2 paramètres");
                Assert.That(shortcutParams[0].ParameterType, Is.EqualTo(typeof(object)), "Premier paramètre: object? sender");
                Assert.That(shortcutParams[1].ParameterType, Is.EqualTo(typeof(EventArgs)), "Second paramètre: EventArgs e");

                // Act & Assert - ClipboardListener_ClipboardContentChanged
                var clipboardHandler = appType.GetMethod("ClipboardListener_ClipboardContentChanged", BindingFlags.NonPublic | BindingFlags.Instance);
                Assert.That(clipboardHandler, Is.Not.Null, "ClipboardListener_ClipboardContentChanged doit exister");
                Assert.That(clipboardHandler!.ReturnType, Is.EqualTo(typeof(void)), "ClipboardListener_ClipboardContentChanged doit retourner void");

                var clipboardParams = clipboardHandler.GetParameters();
                Assert.That(clipboardParams.Length, Is.EqualTo(2), "ClipboardListener_ClipboardContentChanged doit avoir 2 paramètres");
                Assert.That(clipboardParams[0].ParameterType, Is.EqualTo(typeof(object)), "Premier paramètre: object? sender");
                // Note: Le second paramètre pourrait être EventArgs ou un type dérivé - à vérifier
            }

            [Test]
            [Description("CARACTÉRISATION: Documente la complexité cyclomatique actuelle (6 branches)")]
            public void Application_Startup_ComplexityDocumentation()
            {
                // Ce test documente la complexité actuelle pour validation post-refactorisation
                // Complexité cyclomatique identifiée: 6 branches

                var expectedBranches = new[]
                {
                    "if (Services == null) - ligne 66",
                    "if (lifetimeManager == null) - ligne 76",
                    "if (startupLogic == null) - ligne 85",
                    "if (status == StartupStatus.Success) - ligne 92",
                    "else if (status == StartupStatus.ShutdownRequested) - ligne 96",
                    "catch (Exception ex) - ligne 106"
                };

                Console.WriteLine("CARACTÉRISATION - Complexité cyclomatique actuelle:");
                Console.WriteLine($"Nombre de branches: {expectedBranches.Length}");
                Console.WriteLine("Branches identifiées:");
                foreach (var branch in expectedBranches)
                {
                    Console.WriteLine($"  - {branch}");
                }

                // Validation: la complexité cible après refactorisation doit être < 3
                Assert.That(expectedBranches.Length, Is.EqualTo(6), "Complexité actuelle documentée: 6 branches");
                Assert.Pass($"Complexité cyclomatique actuelle documentée: {expectedBranches.Length} branches");
            }

            [Test]
            [Description("CARACTÉRISATION: Documente les 7 responsabilités identifiées")]
            public void Application_Startup_ResponsibilitiesDocumentation()
            {
                // Ce test documente les responsabilités actuelles pour validation post-refactorisation
                var expectedResponsibilities = new[]
                {
                    "Configuration des services (ConfigureServices si Services == null, ligne 66-69)",
                    "Résolution du logger (Services?.GetService<ILoggingService>, ligne 70)",
                    "Logging de début/fin (messages de délimitation, lignes 71 + 104)",
                    "Validation IApplicationLifetimeManager (résolution + vérification null, lignes 75-81)",
                    "Validation IStartupLogic (résolution + vérification null, lignes 84-90)",
                    "Exécution de la logique de démarrage (startupLogic.ExecuteAsync, ligne 91)",
                    "Gestion des statuts de retour (Success/ShutdownRequested/CriticalError, lignes 92-103)"
                };

                Console.WriteLine("CARACTÉRISATION - Responsabilités actuelles:");
                Console.WriteLine($"Nombre de responsabilités: {expectedResponsibilities.Length}");
                Console.WriteLine("Responsabilités identifiées:");
                foreach (var responsibility in expectedResponsibilities)
                {
                    Console.WriteLine($"  - {responsibility}");
                }

                // Validation: le nombre de responsabilités cible après refactorisation doit être < 4
                Assert.That(expectedResponsibilities.Length, Is.EqualTo(7), "Responsabilités actuelles documentées: 7");
                Assert.Pass($"Responsabilités actuelles documentées: {expectedResponsibilities.Length}");
            }
        }
    }
} 