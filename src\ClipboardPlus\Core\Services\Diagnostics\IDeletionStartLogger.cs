using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.Diagnostics
{
    /// <summary>
    /// Interface principale pour la journalisation du début des opérations de suppression
    /// Respecte le Single Responsibility Principle (SRP)
    /// </summary>
    public interface IDeletionStartLogger
    {
        /// <summary>
        /// Journalise le début d'une tentative de suppression d'un élément
        /// </summary>
        /// <param name="item">L'élément à supprimer (peut être null)</param>
        /// <param name="viewModel">Le ViewModel actuel</param>
        void LogDeletionStart(ClipboardItem? item, ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Journalise le début d'une tentative de suppression avec message personnalisé
        /// </summary>
        /// <param name="item">L'élément à supprimer (peut être null pour les opérations en lot)</param>
        /// <param name="viewModel">Le ViewModel actuel</param>
        /// <param name="message">Message personnalisé décrivant l'opération</param>
        void LogDeletionStart(ClipboardItem? item, ClipboardHistoryViewModel viewModel, string message);
    }
}
