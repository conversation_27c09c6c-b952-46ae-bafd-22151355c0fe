using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Core.DataModels;
using Microsoft.Extensions.DependencyInjection;
using WpfKeyEventArgs = System.Windows.Input.KeyEventArgs;

namespace ClipboardPlus.UI.Windows
{
    /// <summary>
    /// Logique d'interaction pour ShortcutCaptureWindow.xaml
    /// </summary>
    public partial class ShortcutCaptureWindow : Window
    {
        private ILoggingService? _loggingService;
        private ShortcutCaptureViewModel? _viewModel;
        private string? _capturedShortcut;

        /// <summary>
        /// Initialise une nouvelle instance de la fenêtre de capture de raccourci.
        /// </summary>
        public ShortcutCaptureWindow()
        {
            InitializeComponent();
            _loggingService = GetLoggingService();
            _loggingService?.LogInfo("ShortcutCaptureWindow - Constructeur par défaut initialisé.");
            SetupWindow();
        }

        /// <summary>
        /// Initialise une nouvelle instance de la fenêtre de capture de raccourci avec un ViewModel spécifié.
        /// </summary>
        /// <param name="viewModel">Le ViewModel à utiliser pour cette fenêtre.</param>
        public ShortcutCaptureWindow(ShortcutCaptureViewModel viewModel)
        {
            InitializeComponent();
            _loggingService = GetLoggingService();
            _viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
            DataContext = _viewModel;
            _loggingService?.LogInfo("ShortcutCaptureWindow - Constructeur avec ViewModel initialisé.");
            SetupWindow();
        }

        /// <summary>
        /// Crée une nouvelle instance de la fenêtre de capture de raccourci de manière simple.
        /// </summary>
        /// <param name="services">Le fournisseur de services pour l'injection de dépendances.</param>
        /// <param name="currentShortcut">Le raccourci actuel à modifier.</param>
        /// <returns>Une nouvelle instance de ShortcutCaptureWindow.</returns>
        public static Task<ShortcutCaptureWindow> CreateAsync(IServiceProvider services, string currentShortcut = "")
        {
            var loggingService = services.GetService<ILoggingService>();
            loggingService?.LogInfo($"ShortcutCaptureWindow.CreateAsync - Création simple pour capture directe.");

            try
            {
                // Créer la fenêtre simple sans ViewModel complexe
                var window = new ShortcutCaptureWindow();

                loggingService?.LogInfo("ShortcutCaptureWindow.CreateAsync - Fenêtre simple créée avec succès.");
                return Task.FromResult(window);
            }
            catch (Exception ex)
            {
                loggingService?.LogError("Erreur lors de la création de ShortcutCaptureWindow.", ex);
                throw;
            }
        }

        /// <summary>
        /// Configure les aspects communs de la fenêtre.
        /// </summary>
        private void SetupWindow()
        {
            // Gérer l'événement KeyDown pour permettre de fermer la fenêtre avec Échap
            KeyDown += (s, e) => {
                if (e.Key == Key.Escape)
                {
                    _loggingService?.LogInfo("ShortcutCaptureWindow: Touche Échap pressée, annulation.");
                    SafeSetDialogResult(false);
                    Close();
                }
            };

            // S'assurer que le DataContext est assigné
            if (DataContext == null && _viewModel != null)
            {
                DataContext = _viewModel;
            }
            else if (DataContext is ShortcutCaptureViewModel vm)
            {
                _viewModel = vm;
            }

            // Configurer les événements du ViewModel
            if (_viewModel != null)
            {
                _viewModel.CancelRequested += (s, e) => {
                    SafeSetDialogResult(false);
                    Close();
                };

                _viewModel.ShortcutValidated += (s, shortcut) => {
                    SafeSetDialogResult(true);
                    Close();
                };
            }
        }

        /// <summary>
        /// Gère l'événement Loaded de la fenêtre.
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            _loggingService?.LogInfo("ShortcutCaptureWindow: Fenêtre chargée - En écoute du raccourci.");

            // La fenêtre est prête à capturer immédiatement
            StatusText.Text = "En écoute...";
            Focus(); // Focus sur la fenêtre pour capturer les touches
        }

        /// <summary>
        /// Gère l'événement KeyDown de la fenêtre pour capturer directement.
        /// </summary>
        private void Window_KeyDown(object sender, WpfKeyEventArgs e)
        {
            // Échap pour annuler
            if (e.Key == Key.Escape)
            {
                _loggingService?.LogInfo("ShortcutCaptureWindow: Annulation par Échap.");
                SafeSetDialogResult(false);
                Close();
                return;
            }

            // Capturer la combinaison de touches
            var modifiers = Keyboard.Modifiers;

            // CORRECTION BUG : Utiliser SystemKey quand Key est System (pour Alt+touches)
            var key = e.Key == Key.System ? e.SystemKey : e.Key;

            // Ignorer les touches de modification seules
            if (key == Key.LeftCtrl || key == Key.RightCtrl ||
                key == Key.LeftAlt || key == Key.RightAlt ||
                key == Key.LeftShift || key == Key.RightShift ||
                key == Key.LWin || key == Key.RWin ||
                key == Key.System) // Ignorer aussi Key.System seul
            {
                return;
            }

            try
            {
                // LOGGING DEBUG : Afficher les détails de capture
                _loggingService?.LogInfo($"ShortcutCaptureWindow: e.Key={e.Key}, e.SystemKey={e.SystemKey}, key utilisée={key}, modifiers={modifiers}");

                // Créer la combinaison de touches
                var keyCombination = new KeyCombination((ModifierKeys)modifiers, key);
                _loggingService?.LogInfo($"ShortcutCaptureWindow: Raccourci capturé: {keyCombination}");

                // Afficher le raccourci capturé
                CapturedShortcut.Text = keyCombination.ToString();
                CapturedShortcut.Visibility = Visibility.Visible;
                StatusText.Text = "Raccourci capturé !";

                // Stocker le résultat et fermer automatiquement après un court délai
                _capturedShortcut = keyCombination.ToString();

                var timer = new System.Windows.Threading.DispatcherTimer();
                timer.Interval = TimeSpan.FromMilliseconds(800);
                timer.Tick += (s, args) => {
                    timer.Stop();
                    SafeSetDialogResult(true);
                    Close();
                };
                timer.Start();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Erreur lors de la capture du raccourci.", ex);
                StatusText.Text = "Erreur de capture";

                var timer = new System.Windows.Threading.DispatcherTimer();
                timer.Interval = TimeSpan.FromMilliseconds(1000);
                timer.Tick += (s, args) => {
                    timer.Stop();
                    SafeSetDialogResult(false);
                    Close();
                };
                timer.Start();
            }
        }

        /// <summary>
        /// Obtient le raccourci capturé.
        /// </summary>
        public string? GetCapturedShortcut()
        {
            return _capturedShortcut;
        }

        /// <summary>
        /// Définit DialogResult de manière sécurisée, uniquement si la fenêtre est ouverte en mode modal.
        /// </summary>
        /// <param name="result">Le résultat à définir.</param>
        private void SafeSetDialogResult(bool result)
        {
            try
            {
                // Vérifier si la fenêtre peut accepter un DialogResult
                if (IsLoaded && IsVisible)
                {
                    DialogResult = result;
                }
                else
                {
                    _loggingService?.LogWarning($"SafeSetDialogResult: Impossible de définir DialogResult={result} - Fenêtre non prête (IsLoaded={IsLoaded}, IsVisible={IsVisible})");
                }
            }
            catch (InvalidOperationException ex)
            {
                _loggingService?.LogWarning($"SafeSetDialogResult: DialogResult ne peut pas être défini - {ex.Message}");
                // En cas d'échec, fermer simplement la fenêtre
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Erreur lors de la définition de DialogResult.", ex);
            }
        }

        /// <summary>
        /// Obtient le service de journalisation de l'application.
        /// </summary>
        private ILoggingService? GetLoggingService()
        {
            try
            {
                var app = System.Windows.Application.Current;
                if (app != null)
                {
                    var services = app.GetType().GetProperty("Services")?.GetValue(app);
                    if (services != null)
                    {
                        var getServiceMethod = services.GetType().GetMethod("GetService", new Type[] { typeof(Type) });
                        if (getServiceMethod != null)
                        {
                            return getServiceMethod.Invoke(services, new object[] { typeof(ILoggingService) }) as ILoggingService;
                        }
                    }
                }
            }
            catch
            {
                // Ignorer les erreurs, retourner null en cas d'échec
            }
            return null;
        }
    }
}
