using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Interfaces
{
    /// <summary>
    /// Interface pour la détection de doublons dans l'historique du presse-papiers.
    /// Responsabilité unique : Détecter les éléments en double.
    /// </summary>
    public interface IDuplicateDetector
    {
        /// <summary>
        /// Recherche un doublon d'un élément dans l'historique.
        /// </summary>
        /// <param name="item">L'élément à rechercher</param>
        /// <param name="historyItems">Liste des éléments de l'historique</param>
        /// <returns>L'élément en double trouvé, ou null si aucun doublon</returns>
        Task<ClipboardItem?> FindDuplicateAsync(ClipboardItem item, IEnumerable<ClipboardItem> historyItems);

        /// <summary>
        /// Détermine si deux éléments sont considérés comme des doublons.
        /// </summary>
        /// <param name="item1">Premier élément</param>
        /// <param name="item2">Deuxième élément</param>
        /// <returns>True si les éléments sont des doublons</returns>
        bool AreDuplicates(ClipboardItem item1, ClipboardItem item2);
    }
}
