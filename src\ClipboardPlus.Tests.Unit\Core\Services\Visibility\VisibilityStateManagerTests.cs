using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using System;

namespace ClipboardPlus.Tests.Unit.Core.Services.Visibility
{
    /// <summary>
    /// Tests exhaustifs pour VisibilityStateManager - Orchestrateur central SOLID
    /// </summary>
    [TestFixture]
    public class VisibilityStateManagerTests
    {
        private Mock<IVisibilityRule<ClipboardItem>> _mockTitleRule = null!;
        private Mock<IVisibilityRule<ClipboardItem>> _mockTimestampRule = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private VisibilityStateManager _visibilityManager = null!;

        [SetUp]
        public void SetUp()
        {
            _mockTitleRule = new Mock<IVisibilityRule<ClipboardItem>>();
            _mockTimestampRule = new Mock<IVisibilityRule<ClipboardItem>>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockLoggingService = new Mock<ILoggingService>();

            // Configuration par défaut
            _mockSettingsManager.Setup(s => s.HideItemTitle).Returns(false);
            _mockSettingsManager.Setup(s => s.HideTimestamp).Returns(false);

            _visibilityManager = new VisibilityStateManager(
                _mockTitleRule.Object,
                _mockTimestampRule.Object,
                _mockSettingsManager.Object,
                _mockLoggingService.Object
            );
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithValidParameters_InitializesCorrectly()
        {
            // Assert
            Assert.IsNotNull(_visibilityManager);
            Assert.IsTrue(_visibilityManager.GlobalTitleVisibility);
            Assert.IsTrue(_visibilityManager.GlobalTimestampVisibility);
        }

        [Test]
        public void Constructor_WithNullTitleRule_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new VisibilityStateManager(
                null!,
                _mockTimestampRule.Object,
                _mockSettingsManager.Object,
                _mockLoggingService.Object
            ));
        }

        [Test]
        public void Constructor_WithNullTimestampRule_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new VisibilityStateManager(
                _mockTitleRule.Object,
                null!,
                _mockSettingsManager.Object,
                _mockLoggingService.Object
            ));
        }

        [Test]
        public void Constructor_WithNullSettingsManager_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new VisibilityStateManager(
                _mockTitleRule.Object,
                _mockTimestampRule.Object,
                null!,
                _mockLoggingService.Object
            ));
        }

        [Test]
        public void Constructor_WithNullLoggingService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new VisibilityStateManager(
                _mockTitleRule.Object,
                _mockTimestampRule.Object,
                _mockSettingsManager.Object,
                null!
            ));
        }

        [Test]
        public void Constructor_InitializesFromSettings()
        {
            // Arrange
            _mockSettingsManager.Setup(s => s.HideItemTitle).Returns(true);
            _mockSettingsManager.Setup(s => s.HideTimestamp).Returns(true);

            // Act
            var manager = new VisibilityStateManager(
                _mockTitleRule.Object,
                _mockTimestampRule.Object,
                _mockSettingsManager.Object,
                _mockLoggingService.Object
            );

            // Assert
            Assert.IsFalse(manager.GlobalTitleVisibility, "Devrait initialiser depuis HideItemTitle=true");
            Assert.IsFalse(manager.GlobalTimestampVisibility, "Devrait initialiser depuis HideTimestamp=true");
        }

        #endregion

        #region ShouldShowTitle Tests

        [Test]
        public void ShouldShowTitle_CallsTitleRule_WithCorrectParameters()
        {
            // Arrange
            var item = new ClipboardItem { CustomName = "Test" };
            _mockTitleRule.Setup(r => r.ShouldBeVisible(It.IsAny<ClipboardItem>(), It.IsAny<VisibilityContext>()))
                         .Returns(true);

            // Act
            _visibilityManager.ShouldShowTitle(item);

            // Assert
            _mockTitleRule.Verify(r => r.ShouldBeVisible(
                item,
                It.Is<VisibilityContext>(c => c.GlobalTitleVisibility == true)
            ), Times.Once);
        }

        [Test]
        public void ShouldShowTitle_ReturnsRuleResult()
        {
            // Arrange
            var item = new ClipboardItem { CustomName = "Test" };
            _mockTitleRule.Setup(r => r.ShouldBeVisible(It.IsAny<ClipboardItem>(), It.IsAny<VisibilityContext>()))
                         .Returns(false);

            // Act
            var result = _visibilityManager.ShouldShowTitle(item);

            // Assert
            Assert.IsFalse(result, "Devrait retourner le résultat de la règle");
        }

        [Test]
        public void ShouldShowTitle_WithNullItem_ThrowsNullReferenceException()
        {
            // Act & Assert
            Assert.Throws<NullReferenceException>(() => _visibilityManager.ShouldShowTitle(null!));
        }

        #endregion

        #region ShouldShowTimestamp Tests

        [Test]
        public void ShouldShowTimestamp_CallsTimestampRule_WithCorrectParameters()
        {
            // Arrange
            var item = new ClipboardItem { Timestamp = DateTime.Now };
            _mockTimestampRule.Setup(r => r.ShouldBeVisible(It.IsAny<ClipboardItem>(), It.IsAny<VisibilityContext>()))
                             .Returns(true);

            // Act
            _visibilityManager.ShouldShowTimestamp(item);

            // Assert
            _mockTimestampRule.Verify(r => r.ShouldBeVisible(
                item,
                It.Is<VisibilityContext>(c => c.GlobalTimestampVisibility == true)
            ), Times.Once);
        }

        [Test]
        public void ShouldShowTimestamp_ReturnsRuleResult()
        {
            // Arrange
            var item = new ClipboardItem { Timestamp = DateTime.Now };
            _mockTimestampRule.Setup(r => r.ShouldBeVisible(It.IsAny<ClipboardItem>(), It.IsAny<VisibilityContext>()))
                             .Returns(false);

            // Act
            var result = _visibilityManager.ShouldShowTimestamp(item);

            // Assert
            Assert.IsFalse(result, "Devrait retourner le résultat de la règle");
        }

        [Test]
        public void ShouldShowTimestamp_WithNullItem_ThrowsNullReferenceException()
        {
            // Act & Assert
            Assert.Throws<NullReferenceException>(() => _visibilityManager.ShouldShowTimestamp(null!));
        }

        #endregion

        #region UpdateGlobalTitleVisibility Tests

        [Test]
        public void UpdateGlobalTitleVisibility_ChangesProperty()
        {
            // Act
            _visibilityManager.UpdateGlobalTitleVisibility(false);

            // Assert
            Assert.IsFalse(_visibilityManager.GlobalTitleVisibility);
        }

        [Test]
        public void UpdateGlobalTitleVisibility_UpdatesSettingsManager()
        {
            // Act
            _visibilityManager.UpdateGlobalTitleVisibility(false);

            // Assert
            _mockSettingsManager.VerifySet(s => s.HideItemTitle = true, Times.Once);
        }

        [Test]
        public void UpdateGlobalTitleVisibility_TriggersEvent_WhenValueChanges()
        {
            // Arrange
            bool eventTriggered = false;
            VisibilityChangedEventArgs? eventArgs = null;
            
            _visibilityManager.VisibilityChanged += (sender, args) =>
            {
                eventTriggered = true;
                eventArgs = args;
            };

            // Act
            _visibilityManager.UpdateGlobalTitleVisibility(false);

            // Assert
            Assert.IsTrue(eventTriggered, "Événement devrait être déclenché");
            Assert.IsNotNull(eventArgs);
            Assert.AreEqual(VisibilityType.Title, eventArgs!.Type);
            Assert.IsFalse(eventArgs.IsVisible);
        }

        [Test]
        public void UpdateGlobalTitleVisibility_DoesNotTriggerEvent_WhenValueUnchanged()
        {
            // Arrange
            bool eventTriggered = false;
            _visibilityManager.VisibilityChanged += (sender, args) => eventTriggered = true;

            // Act - Même valeur que l'état initial (true)
            _visibilityManager.UpdateGlobalTitleVisibility(true);

            // Assert
            Assert.IsFalse(eventTriggered, "Événement ne devrait pas être déclenché");
        }

        #endregion

        #region UpdateGlobalTimestampVisibility Tests

        [Test]
        public void UpdateGlobalTimestampVisibility_ChangesProperty()
        {
            // Act
            _visibilityManager.UpdateGlobalTimestampVisibility(false);

            // Assert
            Assert.IsFalse(_visibilityManager.GlobalTimestampVisibility);
        }

        [Test]
        public void UpdateGlobalTimestampVisibility_UpdatesSettingsManager()
        {
            // Act
            _visibilityManager.UpdateGlobalTimestampVisibility(false);

            // Assert
            _mockSettingsManager.VerifySet(s => s.HideTimestamp = true, Times.Once);
        }

        [Test]
        public void UpdateGlobalTimestampVisibility_TriggersEvent_WhenValueChanges()
        {
            // Arrange
            bool eventTriggered = false;
            VisibilityChangedEventArgs? eventArgs = null;
            
            _visibilityManager.VisibilityChanged += (sender, args) =>
            {
                eventTriggered = true;
                eventArgs = args;
            };

            // Act
            _visibilityManager.UpdateGlobalTimestampVisibility(false);

            // Assert
            Assert.IsTrue(eventTriggered, "Événement devrait être déclenché");
            Assert.IsNotNull(eventArgs);
            Assert.AreEqual(VisibilityType.Timestamp, eventArgs!.Type);
            Assert.IsFalse(eventArgs.IsVisible);
        }

        #endregion

        #region UpdateFromSettings Tests

        [Test]
        public void UpdateTitleVisibilityFromSettings_UpdatesCorrectly()
        {
            // Act
            _visibilityManager.UpdateTitleVisibilityFromSettings(true); // Hide = true

            // Assert
            Assert.IsFalse(_visibilityManager.GlobalTitleVisibility, "Hide=true devrait donner GlobalVisibility=false");
        }

        [Test]
        public void UpdateTimestampVisibilityFromSettings_UpdatesCorrectly()
        {
            // Act
            _visibilityManager.UpdateTimestampVisibilityFromSettings(true); // Hide = true

            // Assert
            Assert.IsFalse(_visibilityManager.GlobalTimestampVisibility, "Hide=true devrait donner GlobalVisibility=false");
        }

        [Test]
        public void UpdateTitleVisibilityFromSettings_TriggersEvent_WhenValueChanges()
        {
            // Arrange
            bool eventTriggered = false;
            _visibilityManager.VisibilityChanged += (sender, args) => eventTriggered = true;

            // Act
            _visibilityManager.UpdateTitleVisibilityFromSettings(true); // Changement de false à true

            // Assert
            Assert.IsTrue(eventTriggered, "Événement devrait être déclenché lors du changement");
        }

        #endregion

        #region Integration Tests

        [Test]
        public void Integration_TitleVisibility_CompleteFlow()
        {
            // Arrange
            var item = new ClipboardItem { CustomName = "Test Title" };
            _mockTitleRule.Setup(r => r.ShouldBeVisible(It.IsAny<ClipboardItem>(), It.IsAny<VisibilityContext>()))
                         .Returns((ClipboardItem i, VisibilityContext c) => c.GlobalTitleVisibility && !string.IsNullOrWhiteSpace(i.CustomName));

            // Act & Assert - État initial (visible)
            Assert.IsTrue(_visibilityManager.ShouldShowTitle(item));

            // Act & Assert - Cacher globalement
            _visibilityManager.UpdateGlobalTitleVisibility(false);
            Assert.IsFalse(_visibilityManager.ShouldShowTitle(item));

            // Act & Assert - Réafficher
            _visibilityManager.UpdateGlobalTitleVisibility(true);
            Assert.IsTrue(_visibilityManager.ShouldShowTitle(item));
        }

        [Test]
        public void Integration_RenameWithHiddenTitle_StaysHidden()
        {
            // Arrange - Simule le scénario de régression
            var item = new ClipboardItem { CustomName = "Ancien nom" };
            _mockTitleRule.Setup(r => r.ShouldBeVisible(It.IsAny<ClipboardItem>(), It.IsAny<VisibilityContext>()))
                         .Returns((ClipboardItem i, VisibilityContext c) => c.GlobalTitleVisibility && !string.IsNullOrWhiteSpace(i.CustomName));

            // Cacher les titres globalement
            _visibilityManager.UpdateGlobalTitleVisibility(false);

            // Act - Simuler renommage
            item.CustomName = "Nouveau nom après renommage";
            var result = _visibilityManager.ShouldShowTitle(item);

            // Assert
            Assert.IsFalse(result, "RÉGRESSION: Le titre doit rester caché après renommage si l'utilisateur a choisi de cacher les titres");
        }

        #endregion

        #region Error Handling Tests

        [Test]
        public void ShouldShowTitle_HandlesRuleException_Gracefully()
        {
            // Arrange
            var item = new ClipboardItem { CustomName = "Test" };
            _mockTitleRule.Setup(r => r.ShouldBeVisible(It.IsAny<ClipboardItem>(), It.IsAny<VisibilityContext>()))
                         .Throws(new Exception("Rule error"));

            // Act & Assert
            Assert.DoesNotThrow(() => _visibilityManager.ShouldShowTitle(item));
            var result = _visibilityManager.ShouldShowTitle(item);
            Assert.IsTrue(result, "Devrait retourner true en cas d'exception de règle");
        }

        [Test]
        public void UpdateGlobalTitleVisibility_HandlesSettingsException_Gracefully()
        {
            // Arrange
            _mockSettingsManager.SetupSet(s => s.HideItemTitle = It.IsAny<bool>())
                               .Throws(new Exception("Settings error"));

            // Act & Assert
            Assert.DoesNotThrow(() => _visibilityManager.UpdateGlobalTitleVisibility(false));
        }

        #endregion

        #region Performance Tests

        [Test]
        public void ShouldShowTitle_PerformanceTest_CompletesQuickly()
        {
            // Arrange
            var item = new ClipboardItem { CustomName = "Test" };
            _mockTitleRule.Setup(r => r.ShouldBeVisible(It.IsAny<ClipboardItem>(), It.IsAny<VisibilityContext>()))
                         .Returns(true);
            
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act - 1000 appels
            for (int i = 0; i < 1000; i++)
            {
                _visibilityManager.ShouldShowTitle(item);
            }

            // Assert
            stopwatch.Stop();
            Assert.Less(stopwatch.ElapsedMilliseconds, 100, 
                "1000 appels devraient prendre moins de 100ms");
        }

        #endregion
    }
}
