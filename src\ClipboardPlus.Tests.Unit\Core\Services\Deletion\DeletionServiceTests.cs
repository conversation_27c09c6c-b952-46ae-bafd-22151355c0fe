using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Diagnostics;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services.Deletion
{
    [TestFixture]
    public class DeletionServiceTests
    {
        private Mock<IClipboardHistoryManager> _mockHistoryManager = null!;
        private Mock<IDeletionUIValidator> _mockValidator = null!;
        private Mock<IDeletionUIHandler> _mockUIHandler = null!;
        private Mock<IDeletionUINotificationService> _mockNotificationService = null!;
        private Mock<IDeletionDiagnostic> _mockDiagnosticService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IViewModelOperationState> _mockViewModelState = null!;
        private DeletionService _deletionService = null!;
        private ObservableCollection<ClipboardItem> _testCollection = null!;
        private ClipboardItem _testItem = null!;

        [SetUp]
        public void SetUp()
        {
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockValidator = new Mock<IDeletionUIValidator>();
            _mockUIHandler = new Mock<IDeletionUIHandler>();
            _mockNotificationService = new Mock<IDeletionUINotificationService>();
            _mockDiagnosticService = new Mock<IDeletionDiagnostic>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockViewModelState = new Mock<IViewModelOperationState>();

            _deletionService = new DeletionService(
                _mockHistoryManager.Object,
                _mockValidator.Object,
                _mockUIHandler.Object,
                _mockNotificationService.Object,
                _mockDiagnosticService.Object,
                _mockLoggingService.Object
            );

            _testCollection = new ObservableCollection<ClipboardItem>();
            _testItem = new ClipboardItem { Id = 123, TextPreview = "Test item", CustomName = "Test" };
            _testCollection.Add(_testItem);
        }

        [Test]
        public async Task DeleteItemAsync_ValidItem_Success()
        {
            // Arrange
            var operationId = "test-op-123";
            
            _mockValidator.Setup(v => v.ValidateItem(_testItem))
                         .Returns(UIValidationResult.Success());
            _mockValidator.Setup(v => v.ValidateOperationState(false))
                         .Returns(UIValidationResult.Success());
            _mockValidator.Setup(v => v.ValidateHistoryManager(_mockHistoryManager.Object))
                         .Returns(UIValidationResult.Success());

            _mockUIHandler.Setup(h => h.RemoveFromUI(_testCollection, _testItem))
                         .Returns(UIRemovalInfo.CreateSuccess(_testItem, 0));

            _mockHistoryManager.Setup(m => m.DeleteItemAsync(_testItem.Id))
                              .ReturnsAsync(true);

            // Act
            var result = await _deletionService.DeleteItemAsync(_testItem, operationId, _testCollection, _mockViewModelState.Object);

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(_testItem.Id, result.ItemId);
            Assert.AreEqual(operationId, result.OperationId);
            Assert.AreEqual("Test", result.ItemName);

            // Vérifier les appels
            _mockValidator.Verify(v => v.ValidateItem(_testItem), Times.Once);
            _mockUIHandler.Verify(h => h.RemoveFromUI(_testCollection, _testItem), Times.Once);
            _mockHistoryManager.Verify(m => m.DeleteItemAsync(_testItem.Id), Times.Once);
            _mockNotificationService.Verify(n => n.NotifySuccess("Test", operationId), Times.Once);
        }

        [Test]
        public async Task DeleteItemAsync_ValidationFails_ReturnsFailure()
        {
            // Arrange
            var operationId = "test-op-456";
            var validationError = "Item is null";
            
            _mockValidator.Setup(v => v.ValidateItem(_testItem))
                         .Returns(UIValidationResult.Failure(validationError, "NULL_ITEM"));

            // Act
            var result = await _deletionService.DeleteItemAsync(_testItem, operationId, _testCollection, _mockViewModelState.Object);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.AreEqual(validationError, result.ErrorMessage);
            Assert.AreEqual(operationId, result.OperationId);

            // Vérifier qu'aucune suppression n'a eu lieu
            _mockUIHandler.Verify(h => h.RemoveFromUI(It.IsAny<ObservableCollection<ClipboardItem>>(), It.IsAny<ClipboardItem>()), Times.Never);
            _mockHistoryManager.Verify(m => m.DeleteItemAsync(It.IsAny<long>()), Times.Never);
        }

        [Test]
        public async Task DeleteItemAsync_PersistenceFailure_PerformsRollback()
        {
            // Arrange
            var operationId = "test-op-789";
            var uiRemovalInfo = UIRemovalInfo.CreateSuccess(_testItem, 0);
            
            _mockValidator.Setup(v => v.ValidateItem(_testItem))
                         .Returns(UIValidationResult.Success());
            _mockValidator.Setup(v => v.ValidateOperationState(false))
                         .Returns(UIValidationResult.Success());
            _mockValidator.Setup(v => v.ValidateHistoryManager(_mockHistoryManager.Object))
                         .Returns(UIValidationResult.Success());

            _mockUIHandler.Setup(h => h.RemoveFromUI(_testCollection, _testItem))
                         .Returns(uiRemovalInfo);

            _mockHistoryManager.Setup(m => m.DeleteItemAsync(_testItem.Id))
                              .ReturnsAsync(false); // Échec de persistance

            // Act
            var result = await _deletionService.DeleteItemAsync(_testItem, operationId, _testCollection, _mockViewModelState.Object);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.IsTrue(result.RollbackPerformed);
            Assert.AreEqual(operationId, result.OperationId);

            // Vérifier le rollback
            _mockUIHandler.Verify(h => h.RollbackUI(_testCollection, uiRemovalInfo), Times.Once);
            _mockNotificationService.Verify(n => n.NotifyError("Test", It.IsAny<string>(), operationId), Times.Once);
            _mockNotificationService.Verify(n => n.NotifyRollback("Test", operationId), Times.Once);
        }

        [Test]
        public async Task DeleteItemAsync_PersistenceException_HandlesDiagnostic()
        {
            // Arrange
            var operationId = "test-op-exception";
            var exception = new Exception("Database error");
            
            _mockValidator.Setup(v => v.ValidateItem(_testItem))
                         .Returns(UIValidationResult.Success());
            _mockValidator.Setup(v => v.ValidateOperationState(false))
                         .Returns(UIValidationResult.Success());
            _mockValidator.Setup(v => v.ValidateHistoryManager(_mockHistoryManager.Object))
                         .Returns(UIValidationResult.Success());

            _mockUIHandler.Setup(h => h.RemoveFromUI(_testCollection, _testItem))
                         .Returns(UIRemovalInfo.CreateSuccess(_testItem, 0));

            _mockHistoryManager.Setup(m => m.DeleteItemAsync(_testItem.Id))
                              .ThrowsAsync(exception);

            // Act
            var result = await _deletionService.DeleteItemAsync(_testItem, operationId, _testCollection, _mockViewModelState.Object);

            // Assert
            Assert.IsFalse(result.Success);
            Assert.IsTrue(result.ErrorMessage.Contains("Database error"));
            Assert.AreEqual(exception, result.OriginalException);

            // Vérifier le diagnostic d'urgence
            _mockDiagnosticService.Verify(d => d.LogDeletionException(exception, "DeletionService.TryDeleteFromPersistenceAsync"), Times.Once);
        }

        [Test]
        public void Constructor_NullDependencies_ThrowsArgumentNullException()
        {
            // Test pour chaque dépendance null
            Assert.Throws<ArgumentNullException>(() => new DeletionService(null!, _mockValidator.Object, _mockUIHandler.Object, _mockNotificationService.Object, _mockDiagnosticService.Object));
            Assert.Throws<ArgumentNullException>(() => new DeletionService(_mockHistoryManager.Object, null!, _mockUIHandler.Object, _mockNotificationService.Object, _mockDiagnosticService.Object));
            Assert.Throws<ArgumentNullException>(() => new DeletionService(_mockHistoryManager.Object, _mockValidator.Object, null!, _mockNotificationService.Object, _mockDiagnosticService.Object));
            Assert.Throws<ArgumentNullException>(() => new DeletionService(_mockHistoryManager.Object, _mockValidator.Object, _mockUIHandler.Object, null!, _mockDiagnosticService.Object));
            Assert.Throws<ArgumentNullException>(() => new DeletionService(_mockHistoryManager.Object, _mockValidator.Object, _mockUIHandler.Object, _mockNotificationService.Object, null!));
        }
    }
}
