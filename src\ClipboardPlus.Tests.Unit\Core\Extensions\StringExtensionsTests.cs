using System;
using NUnit.Framework;
using ClipboardPlus.Core.Extensions;

namespace ClipboardPlus.Tests.Unit.Core.Extensions
{
    [TestFixture]
    public class StringExtensionsTests
    {
        [Test]
        public void Truncate_NullString_ReturnsEmptyString()
        {
            // Arrange
            string? value = null;
            int maxLength = 10;

            // Act
            string result = value.Truncate(maxLength);

            // Assert
            Assert.That(result, Is.EqualTo(string.Empty));
        }

        [Test]
        public void Truncate_EmptyString_ReturnsEmptyString()
        {
            // Arrange
            string value = string.Empty;
            int maxLength = 10;

            // Act
            string result = value.Truncate(maxLength);

            // Assert
            Assert.That(result, Is.EqualTo(string.Empty));
        }

        [Test]
        public void Truncate_NegativeMaxLength_ReturnsEmptyString()
        {
            // Arrange
            string value = "Test String";
            int maxLength = -5;

            // Act
            string result = value.Truncate(maxLength);

            // Assert
            Assert.That(result, Is.EqualTo(string.Empty));
        }

        [Test]
        public void Truncate_ZeroMaxLength_ReturnsEmptyString()
        {
            // Arrange
            string value = "Test String";
            int maxLength = 0;

            // Act
            string result = value.Truncate(maxLength);

            // Assert
            Assert.That(result, Is.EqualTo(string.Empty));
        }

        [Test]
        public void Truncate_MaxLengthLessThanThree_ReturnsDotsOrOriginal()
        {
            // Arrange
            string value = "Test String";

            // Test with maxLength = 1, 2, 3
            for (int maxLength = 1; maxLength <= 3; maxLength++)
            {
                // Act
                string result = value.Truncate(maxLength);

                // Assert
                if (value.Length > maxLength)
                {
                    // Doit retourner des points
                    Assert.That(result, Is.EqualTo(new string('.', maxLength)));
                }
                else
                {
                    // Doit retourner la chaîne originale
                    Assert.That(result, Is.EqualTo(value));
                }
            }
        }

        [Test]
        public void Truncate_StringLongerThanMaxLength_ReturnsTruncatedStringWithEllipsis()
        {
            // Arrange
            string value = "This is a long test string";
            int maxLength = 10;

            // Act
            string result = value.Truncate(maxLength);

            // Assert
            Assert.That(result, Is.EqualTo("This is..."));
            Assert.That(result.Length, Is.EqualTo(maxLength));
        }

        [Test]
        public void Truncate_StringEqualToMaxLength_ReturnsOriginalString()
        {
            // Arrange
            string value = "Test";
            int maxLength = 4;

            // Act
            string result = value.Truncate(maxLength);

            // Assert
            Assert.That(result, Is.EqualTo(value));
        }

        [Test]
        public void Truncate_StringShorterThanMaxLength_ReturnsOriginalString()
        {
            // Arrange
            string value = "Test";
            int maxLength = 10;

            // Act
            string result = value.Truncate(maxLength);

            // Assert
            Assert.That(result, Is.EqualTo(value));
        }

        [Test]
        public void Truncate_ExactBoundaryCase_HandlesCorrectly()
        {
            // Arrange
            string value = "12345678901234567890"; // 20 caractères
            int maxLength = 10;

            // Act
            string result = value.Truncate(maxLength);

            // Assert
            Assert.That(result, Is.EqualTo("1234567..."));
            Assert.That(result.Length, Is.EqualTo(maxLength));
        }
    }
} 