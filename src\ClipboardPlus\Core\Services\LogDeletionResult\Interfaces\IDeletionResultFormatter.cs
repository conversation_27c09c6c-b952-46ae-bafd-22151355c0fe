using ClipboardPlus.Core.Services.LogDeletionResult.Models;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Interfaces
{
    /// <summary>
    /// Interface pour le formatage des logs de suppression.
    /// Sépare la logique de formatage de la logique métier.
    /// </summary>
    public interface IDeletionResultFormatter
    {
        /// <summary>
        /// Formate le résultat d'une suppression pour le logging.
        /// </summary>
        /// <param name="context">Contexte de la suppression</param>
        /// <returns>Texte formaté prêt pour le log</returns>
        string FormatDeletionResult(DeletionResultContext context);

        /// <summary>
        /// Formate les résultats de validation pour le logging.
        /// </summary>
        /// <param name="validation">Résultat de validation</param>
        /// <returns>Texte formaté des validations</returns>
        string FormatValidationResult(ComprehensiveValidationResult validation);

        /// <summary>
        /// Formate l'état des collections pour le logging.
        /// </summary>
        /// <param name="state">État des collections</param>
        /// <returns>Texte formaté de l'état des collections</returns>
        string FormatCollectionState(CollectionStateInfo state);

        /// <summary>
        /// Formate les détails d'un élément pour le logging.
        /// </summary>
        /// <param name="item">Élément à formater</param>
        /// <returns>Texte formaté des détails de l'élément</returns>
        string FormatItemDetails(ClipboardPlus.Core.DataModels.ClipboardItem? item);

        /// <summary>
        /// Formate l'en-tête du log avec timestamp et informations système.
        /// </summary>
        /// <param name="context">Contexte de la suppression</param>
        /// <returns>En-tête formaté</returns>
        string FormatLogHeader(DeletionResultContext context);

        /// <summary>
        /// Formate le pied de page du log.
        /// </summary>
        /// <param name="context">Contexte de la suppression</param>
        /// <returns>Pied de page formaté</returns>
        string FormatLogFooter(DeletionResultContext context);

        /// <summary>
        /// Formate un log complet (en-tête + contenu + pied de page).
        /// </summary>
        /// <param name="context">Contexte de la suppression</param>
        /// <param name="validation">Résultat de validation</param>
        /// <param name="collectionState">État des collections</param>
        /// <returns>Log complet formaté</returns>
        string FormatCompleteLog(
            DeletionResultContext context, 
            ComprehensiveValidationResult validation, 
            CollectionStateInfo collectionState);
    }
}
