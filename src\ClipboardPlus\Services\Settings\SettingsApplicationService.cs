using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Settings;
using ClipboardPlus.Core.DataModels.Settings;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Services.Settings
{
    /// <summary>
    /// Implémentation du service d'application des paramètres.
    /// Responsable de l'application effective des paramètres de base et avancés.
    /// </summary>
    public class SettingsApplicationService : ISettingsApplicationService
    {
        private readonly ISettingsManager _settingsManager;
        private readonly IGlobalShortcutService _globalShortcutService;
        private readonly IUserNotificationService _userNotificationService;
        private readonly ILoggingService _loggingService;

        public SettingsApplicationService(
            ISettingsManager settingsManager,
            IGlobalShortcutService globalShortcutService,
            IUserNotificationService userNotificationService,
            ILoggingService loggingService)
        {
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
            _globalShortcutService = globalShortcutService ?? throw new ArgumentNullException(nameof(globalShortcutService));
            _userNotificationService = userNotificationService ?? throw new ArgumentNullException(nameof(userNotificationService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        public async Task<SettingsApplicationResult> ApplyBasicSettingsAsync(BasicSettingsData settings)
        {
            _loggingService?.LogInfo("[SettingsApplicationService] ApplyBasicSettingsAsync DÉBUT");
            
            var appliedSettings = new Dictionary<string, object>();
            var errors = new List<string>();

            try
            {
                // Application des paramètres de base au SettingsManager
                _settingsManager.MaxHistoryItems = settings.MaxHistoryItems;
                _settingsManager.StartWithWindows = settings.StartWithWindows;
                _settingsManager.MaxImageDimensionForThumbnail = settings.MaxImageDimensionForThumbnail;
                _settingsManager.MaxStorableItemSizeBytes = settings.MaxStorableItemSizeBytes;

                appliedSettings["MaxHistoryItems"] = settings.MaxHistoryItems;
                appliedSettings["StartWithWindows"] = settings.StartWithWindows;
                appliedSettings["MaxImageDimensionForThumbnail"] = settings.MaxImageDimensionForThumbnail;
                appliedSettings["MaxStorableItemSizeBytes"] = settings.MaxStorableItemSizeBytes;

                // Gestion spéciale du raccourci clavier
                var shortcutResult = await ApplyShortcutSettingAsync(settings.ShortcutKeyCombination);
                if (shortcutResult.Success)
                {
                    _settingsManager.ShortcutKeyCombination = settings.ShortcutKeyCombination;
                    appliedSettings["ShortcutKeyCombination"] = settings.ShortcutKeyCombination;
                }
                else
                {
                    errors.AddRange(shortcutResult.Errors);
                }

                // Gestion du démarrage automatique
                var startupResult = ApplyStartupSetting(settings.StartWithWindows);
                if (!startupResult.Success)
                {
                    errors.AddRange(startupResult.Errors);
                }

                _loggingService?.LogInfo($"[SettingsApplicationService] Paramètres de base appliqués: {appliedSettings.Count} éléments");

                return new SettingsApplicationResult(
                    Success: errors.Count == 0,
                    Message: errors.Count == 0 ? "Paramètres de base appliqués avec succès" : "Paramètres de base appliqués avec des erreurs",
                    AppliedSettings: appliedSettings,
                    Errors: errors
                );
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[SettingsApplicationService] ApplyBasicSettingsAsync ERREUR", ex);
                errors.Add($"Erreur lors de l'application des paramètres de base: {ex.Message}");
                
                return new SettingsApplicationResult(
                    Success: false,
                    Message: "Échec de l'application des paramètres de base",
                    AppliedSettings: appliedSettings,
                    Errors: errors
                );
            }
        }

        public Task<SettingsApplicationResult> ApplyAdvancedSettingsAsync(AdvancedSettingsData settings)
        {
            _loggingService?.LogInfo("[SettingsApplicationService] ApplyAdvancedSettingsAsync DÉBUT");
            
            var appliedSettings = new Dictionary<string, object>();
            var errors = new List<string>();

            try
            {
                // Application des paramètres de visibilité
                _settingsManager.HideTimestamp = settings.HideTimestamp;
                _settingsManager.HideItemTitle = settings.HideItemTitle;

                appliedSettings["HideTimestamp"] = settings.HideTimestamp;
                appliedSettings["HideItemTitle"] = settings.HideItemTitle;

                // Application du thème si spécifié
                if (settings.SelectedTheme != null)
                {
                    _settingsManager.ActiveThemePath = settings.SelectedTheme.FilePath;
                    appliedSettings["ActiveThemePath"] = settings.SelectedTheme.FilePath;
                }

                _loggingService?.LogInfo($"[SettingsApplicationService] Paramètres avancés appliqués: {appliedSettings.Count} éléments");

                return Task.FromResult(new SettingsApplicationResult(
                    Success: true,
                    Message: "Paramètres avancés appliqués avec succès",
                    AppliedSettings: appliedSettings,
                    Errors: errors
                ));
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[SettingsApplicationService] ApplyAdvancedSettingsAsync ERREUR", ex);
                errors.Add($"Erreur lors de l'application des paramètres avancés: {ex.Message}");

                return Task.FromResult(new SettingsApplicationResult(
                    Success: false,
                    Message: "Échec de l'application des paramètres avancés",
                    AppliedSettings: appliedSettings,
                    Errors: errors
                ));
            }
        }

        private async Task<SettingsApplicationResult> ApplyShortcutSettingAsync(string newShortcut)
        {
            var errors = new List<string>();
            var appliedSettings = new Dictionary<string, object>();

            try
            {
                // Vérifier si le raccourci a changé
                if (_settingsManager.ShortcutKeyCombination == newShortcut)
                {
                    return new SettingsApplicationResult(true, "Raccourci inchangé", appliedSettings, errors);
                }

                // Tenter de parser le nouveau raccourci
                if (!KeyCombination.TryParse(newShortcut, out var newKeyCombination))
                {
                    errors.Add("Format de raccourci clavier invalide");
                    return new SettingsApplicationResult(false, "Raccourci invalide", appliedSettings, errors);
                }

                // Désenregistrer l'ancien raccourci
                _globalShortcutService.UnregisterShortcut();

                // Tenter d'enregistrer le nouveau raccourci
                if (await _globalShortcutService.TryRegisterShortcutAsync(newKeyCombination))
                {
                    // Mettre à jour le SettingsManager avec le nouveau raccourci
                    _settingsManager.ShortcutKeyCombination = newShortcut;
                    appliedSettings["ShortcutKeyCombination"] = newShortcut;
                    return new SettingsApplicationResult(true, "Raccourci mis à jour avec succès", appliedSettings, errors);
                }
                else
                {
                    // Échec - restaurer l'ancien raccourci
                    var originalShortcut = _settingsManager.ShortcutKeyCombination;
                    if (KeyCombination.TryParse(originalShortcut, out var originalKeyCombination))
                    {
                        await _globalShortcutService.TryRegisterShortcutAsync(originalKeyCombination);
                    }

                    errors.Add("Le raccourci clavier n'a pas pu être mis à jour.");
                    _userNotificationService.ShowError("Erreur raccourci", "Le raccourci clavier n'a pas pu être mis à jour.");
                    
                    return new SettingsApplicationResult(false, "Échec de mise à jour du raccourci", appliedSettings, errors);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[SettingsApplicationService] ApplyShortcutSettingAsync ERREUR", ex);
                errors.Add($"Erreur lors de la mise à jour du raccourci: {ex.Message}");
                return new SettingsApplicationResult(false, "Erreur raccourci", appliedSettings, errors);
            }
        }

        private SettingsApplicationResult ApplyStartupSetting(bool startWithWindows)
        {
            var errors = new List<string>();
            var appliedSettings = new Dictionary<string, object>();

            try
            {
                // Vérifier si le paramètre a changé
                if (_settingsManager.StartWithWindows == startWithWindows)
                {
                    return new SettingsApplicationResult(true, "Démarrage automatique inchangé", appliedSettings, errors);
                }

                // Accéder au service de démarrage via Application.Current
                var app = System.Windows.Application.Current as ClipboardPlus.App;
                var startupManager = app?.Services?.GetService(typeof(ISystemStartupManager)) as ISystemStartupManager;

                if (startupManager != null)
                {
                    var success = startupManager.SetStartupEnabled(startWithWindows);
                    if (success)
                    {
                        appliedSettings["StartWithWindows"] = startWithWindows;
                        return new SettingsApplicationResult(true, "Démarrage automatique mis à jour", appliedSettings, errors);
                    }
                    else
                    {
                        errors.Add("Échec de la mise à jour du démarrage automatique");
                        _userNotificationService.ShowError("Erreur démarrage", "Le paramètre de démarrage automatique n'a pas pu être mis à jour.");
                        return new SettingsApplicationResult(false, "Échec démarrage automatique", appliedSettings, errors);
                    }
                }
                else
                {
                    errors.Add("Service de démarrage automatique non disponible");
                    _userNotificationService.ShowError("Erreur démarrage", "Le paramètre de démarrage automatique n'a pas pu être mis à jour.");
                    return new SettingsApplicationResult(false, "Service démarrage indisponible", appliedSettings, errors);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[SettingsApplicationService] ApplyStartupSettingAsync ERREUR", ex);
                errors.Add($"Erreur lors de la mise à jour du démarrage automatique: {ex.Message}");
                _userNotificationService.ShowError("Erreur démarrage", "Le paramètre de démarrage automatique n'a pas pu être mis à jour.");
                return new SettingsApplicationResult(false, "Erreur démarrage automatique", appliedSettings, errors);
            }
        }
    }
}
