using System;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Diagnostics;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Services.Diagnostics
{
    /// <summary>
    /// Analyseur de ViewModel qui évite l'utilisation de la réflexion
    /// </summary>
    public class ViewModelAnalyzer : IViewModelAnalyzer
    {
        private readonly ILoggingService _loggingService;

        public ViewModelAnalyzer(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        public ViewModelAnalysisResult AnalyzeState(ClipboardHistoryViewModel viewModel)
        {
            var issues = new List<string>();

            try
            {
                if (viewModel == null)
                {
                    issues.Add("ViewModel est null");
                    return new ViewModelAnalysisResult(0, false, false, null, issues);
                }

                var itemCount = viewModel.HistoryItems?.Count ?? 0;
                var isLoading = viewModel.IsLoading;
                var hasSelectedItem = viewModel.SelectedClipboardItem != null;
                var selectedItemPreview = viewModel.SelectedClipboardItem?.TextPreview;

                // Analyse des problèmes potentiels
                if (itemCount == 0)
                    issues.Add("Aucun élément dans l'historique");

                if (isLoading)
                    issues.Add("ViewModel en cours de chargement");

                if (viewModel.HistoryItems == null)
                    issues.Add("Collection HistoryItems est null");

                // Vérification de la cohérence
                if (hasSelectedItem && viewModel.HistoryItems != null)
                {
                    var selectedExists = viewModel.HistoryItems.Contains(viewModel.SelectedClipboardItem!);
                    if (!selectedExists)
                        issues.Add("L'élément sélectionné n'existe pas dans la collection");
                }

                return new ViewModelAnalysisResult(
                    itemCount,
                    isLoading,
                    hasSelectedItem,
                    selectedItemPreview,
                    issues
                );
            }
            catch (Exception ex)
            {
                issues.Add($"Erreur lors de l'analyse du ViewModel: {ex.Message}");
                _loggingService?.LogError($"Erreur ViewModelAnalyzer.AnalyzeState: {ex.Message}", ex);
                return new ViewModelAnalysisResult(0, false, false, null, issues);
            }
        }

        public CollectionHealthInfo AnalyzeCollectionHealth(ClipboardHistoryViewModel viewModel)
        {
            var healthWarnings = new List<string>();

            try
            {
                if (viewModel?.HistoryItems == null)
                {
                    healthWarnings.Add("Collection HistoryItems est null");
                    return new CollectionHealthInfo(0, 0, 0, false, healthWarnings);
                }

                var totalItems = viewModel.HistoryItems.Count;
                var pinnedItems = viewModel.HistoryItems.Count(item => item.IsPinned);
                var visibleItems = viewModel.HistoryItems.Count(item => item.IsTitleVisible);

                // Détection des doublons (basée sur TextPreview et Timestamp)
                var duplicateGroups = viewModel.HistoryItems
                    .Where(item => !string.IsNullOrEmpty(item.TextPreview))
                    .GroupBy(item => new { item.TextPreview, Date = item.Timestamp.Date })
                    .Where(group => group.Count() > 1)
                    .ToList();

                var hasDuplicates = duplicateGroups.Any();

                // Analyse de santé
                if (totalItems == 0)
                    healthWarnings.Add("Aucun élément dans la collection");

                if (pinnedItems > totalItems / 2)
                    healthWarnings.Add($"Trop d'éléments épinglés: {pinnedItems}/{totalItems}");

                if (visibleItems < totalItems / 3)
                    healthWarnings.Add($"Peu d'éléments avec titre visible: {visibleItems}/{totalItems}");

                if (hasDuplicates)
                    healthWarnings.Add($"Doublons détectés: {duplicateGroups.Count} groupes");

                // Vérification de l'ordre chronologique
                var items = viewModel.HistoryItems.ToList();
                for (int i = 1; i < items.Count; i++)
                {
                    if (items[i].Timestamp > items[i - 1].Timestamp)
                    {
                        healthWarnings.Add("Ordre chronologique incorrect détecté");
                        break;
                    }
                }

                return new CollectionHealthInfo(
                    totalItems,
                    pinnedItems,
                    visibleItems,
                    hasDuplicates,
                    healthWarnings
                );
            }
            catch (Exception ex)
            {
                healthWarnings.Add($"Erreur lors de l'analyse de santé: {ex.Message}");
                _loggingService?.LogError($"Erreur ViewModelAnalyzer.AnalyzeCollectionHealth: {ex.Message}", ex);
                return new CollectionHealthInfo(0, 0, 0, false, healthWarnings);
            }
        }
    }
}
