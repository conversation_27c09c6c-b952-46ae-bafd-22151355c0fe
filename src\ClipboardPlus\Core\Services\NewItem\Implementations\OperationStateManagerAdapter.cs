using System;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.NewItem.Implementations
{
    /// <summary>
    /// Adaptateur pour IOperationStateManager qui fait le pont avec ClipboardHistoryViewModel.
    /// Implémente le principe d'Adapter Pattern pour découpler l'orchestrateur du ViewModel.
    /// </summary>
    public class OperationStateManagerAdapter : IOperationStateManager
    {
        private readonly ClipboardHistoryViewModel _viewModel;

        public OperationStateManagerAdapter(ClipboardHistoryViewModel viewModel)
        {
            _viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
        }

        public bool IsOperationInProgress
        {
            get => _viewModel.IsOperationInProgress;
            set => _viewModel.IsOperationInProgress = value;
        }

        public bool IsItemCreationActive
        {
            get => _viewModel.IsItemCreationActive;
            set => _viewModel.IsItemCreationActive = value;
        }

        public string NewItemTextContent
        {
            get => _viewModel.NewItemTextContent;
            set => _viewModel.NewItemTextContent = value;
        }

        public void RefreshItemCreationCommands()
        {
            _viewModel.RefreshItemCreationCommands();
        }
    }
}
