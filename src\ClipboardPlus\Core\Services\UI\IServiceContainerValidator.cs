using System;

namespace ClipboardPlus.Core.Services.UI
{
    /// <summary>
    /// Service pour la validation du conteneur de services.
    /// Responsabilité unique : Valider l'état et la disponibilité du conteneur de services.
    /// </summary>
    public interface IServiceContainerValidator
    {
        /// <summary>
        /// Valide que le conteneur de services est correctement initialisé.
        /// </summary>
        /// <returns>Résultat de la validation</returns>
        ValidationResult ValidateServiceContainer();

        /// <summary>
        /// Obtient le fournisseur de services validé.
        /// </summary>
        /// <returns>Le fournisseur de services ou null si non disponible</returns>
        IServiceProvider? GetValidatedServiceProvider();
    }

    /// <summary>
    /// Résultat de la validation du conteneur de services.
    /// </summary>
    public record ValidationResult(
        bool IsValid,
        string Message,
        IServiceProvider? ServiceProvider = null
    )
    {
        /// <summary>
        /// Crée un résultat de validation réussie.
        /// </summary>
        public static ValidationResult CreateSuccess(IServiceProvider serviceProvider) =>
            new(true, "Conteneur de services valide", serviceProvider);

        /// <summary>
        /// Crée un résultat de validation échouée.
        /// </summary>
        public static ValidationResult CreateFailure(string message) =>
            new(false, message);
    }
}
