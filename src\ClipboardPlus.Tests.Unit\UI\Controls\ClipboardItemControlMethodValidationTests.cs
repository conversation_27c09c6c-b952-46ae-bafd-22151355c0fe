using NUnit.Framework;
using System;
using System.Linq;
using System.Reflection;
using ClipboardPlus.UI.Controls;

namespace ClipboardPlus.Tests.Unit.UI.Controls
{
    /// <summary>
    /// Tests de validation des méthodes dans ClipboardItemControl sans créer d'instances WPF.
    /// </summary>
    [TestFixture]
    public class ClipboardItemControlMethodValidationTests
    {
        [Test]
        public void ClipboardItemControl_ShouldHaveAdvancedCleanupMenuItem_ClickMethod()
        {
            // Arrange
            var controlType = typeof(ClipboardItemControl);
            
            // Act - Chercher la méthode AdvancedCleanupMenuItem_Click
            var method = controlType.GetMethod("AdvancedCleanupMenuItem_Click", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Assert
            Assert.IsNotNull(method, "La méthode AdvancedCleanupMenuItem_Click devrait exister");
            Assert.AreEqual(typeof(void), method!.ReturnType, "La méthode devrait retourner void");
            
            // Vérifier les paramètres
            var parameters = method!.GetParameters();
            Assert.AreEqual(2, parameters.Length, "La méthode devrait avoir 2 paramètres");
            Assert.AreEqual(typeof(object), parameters[0].ParameterType, "Premier paramètre devrait être object (sender)");
            Assert.AreEqual("sender", parameters[0].Name, "Premier paramètre devrait s'appeler 'sender'");
            Assert.AreEqual("e", parameters[1].Name, "Deuxième paramètre devrait s'appeler 'e'");
        }

        [Test]
        public void ClipboardItemControl_ShouldHaveDeleteMenuItem_ClickMethod()
        {
            // Arrange
            var controlType = typeof(ClipboardItemControl);
            
            // Act - Vérifier que la méthode DeleteMenuItem_Click existe toujours
            var method = controlType.GetMethod("DeleteMenuItem_Click", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Assert
            Assert.IsNotNull(method, "La méthode DeleteMenuItem_Click devrait toujours exister (pas de régression)");
        }

        [Test]
        public void ClipboardItemControl_ShouldHaveAllRequiredEventHandlers()
        {
            // Arrange
            var controlType = typeof(ClipboardItemControl);
            var requiredMethods = new[]
            {
                "RenameMenuItem_Click",
                "PinMenuItem_Click",
                "PreviewMenuItem_Click",
                "AdvancedCleanupMenuItem_Click", // Notre nouvelle méthode
                "DeleteMenuItem_Click"
            };
            
            // Act & Assert
            foreach (var methodName in requiredMethods)
            {
                var method = controlType.GetMethod(methodName, 
                    BindingFlags.NonPublic | BindingFlags.Instance);
                
                Assert.IsNotNull(method, $"La méthode {methodName} devrait exister");
                Assert.AreEqual(typeof(void), method!.ReturnType, $"La méthode {methodName} devrait retourner void");

                var parameters = method!.GetParameters();
                Assert.AreEqual(2, parameters.Length, $"La méthode {methodName} devrait avoir 2 paramètres");
            }
        }

        [Test]
        public void ClipboardItemControl_Type_ShouldBePublic()
        {
            // Arrange
            var controlType = typeof(ClipboardItemControl);
            
            // Assert
            Assert.IsTrue(controlType.IsPublic, "ClipboardItemControl devrait être public");
            Assert.IsTrue(controlType.IsClass, "ClipboardItemControl devrait être une classe");
        }

        [Test]
        public void ClipboardItemControl_ShouldInheritFromUserControl()
        {
            // Arrange
            var controlType = typeof(ClipboardItemControl);

            // Assert
            Assert.IsTrue(controlType.IsSubclassOf(typeof(System.Windows.Controls.UserControl)),
                "ClipboardItemControl devrait hériter de UserControl");
        }

        [Test]
        public void UserInteractionService_Interface_ShouldExist()
        {
            // Arrange
            var serviceType = typeof(ClipboardPlus.Core.Services.IUserInteractionService);

            // Assert
            Assert.IsNotNull(serviceType, "L'interface IUserInteractionService devrait exister");
            Assert.IsTrue(serviceType.IsInterface, "IUserInteractionService devrait être une interface");

            // Vérifier les méthodes essentielles
            var openMethod = serviceType.GetMethod("OpenAdvancedCleanupWindowAsync");
            Assert.IsNotNull(openMethod, "La méthode OpenAdvancedCleanupWindowAsync devrait exister");
            Assert.AreEqual(typeof(System.Threading.Tasks.Task), openMethod!.ReturnType,
                "OpenAdvancedCleanupWindowAsync devrait retourner Task");
        }

        [Test]
        public void UserInteractionService_Implementation_ShouldExist()
        {
            // Arrange
            var implementationType = typeof(ClipboardPlus.UI.Services.UserInteractionService);
            var interfaceType = typeof(ClipboardPlus.Core.Services.IUserInteractionService);

            // Assert
            Assert.IsNotNull(implementationType, "L'implémentation UserInteractionService devrait exister");
            Assert.IsTrue(implementationType.IsClass, "UserInteractionService devrait être une classe");
            Assert.IsTrue(interfaceType.IsAssignableFrom(implementationType),
                "UserInteractionService devrait implémenter IUserInteractionService");
        }

        [Test]
        public void ClipboardHistoryViewModel_ShouldHaveOpenAdvancedCleanupCommand()
        {
            // Arrange
            var viewModelType = typeof(ClipboardPlus.UI.ViewModels.ClipboardHistoryViewModel);

            // Act
            var property = viewModelType.GetProperty("OpenAdvancedCleanupCommand");

            // Assert
            Assert.IsNotNull(property, "La propriété OpenAdvancedCleanupCommand devrait exister");
            Assert.IsTrue(property!.CanRead, "OpenAdvancedCleanupCommand devrait être lisible");

            // Vérifier le type de la commande
            var commandInterface = typeof(CommunityToolkit.Mvvm.Input.IAsyncRelayCommand);
            Assert.IsTrue(commandInterface.IsAssignableFrom(property!.PropertyType),
                "OpenAdvancedCleanupCommand devrait être de type IAsyncRelayCommand");
        }
    }
}
