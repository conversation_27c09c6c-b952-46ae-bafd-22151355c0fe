using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.UI;

namespace ClipboardPlus.Services.UI
{
    /// <summary>
    /// Implémentation du service d'orchestration des fenêtres modales.
    /// Responsabilité unique : G<PERSON>rer la création, configuration et affichage des fenêtres modales.
    /// </summary>
    public class ModalWindowOrchestrator : IModalWindowOrchestrator, IDisposable
    {
        private readonly ILoggingService? _loggingService;
        private readonly SemaphoreSlim _semaphore;

        public ModalWindowOrchestrator(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
            _semaphore = new SemaphoreSlim(1, 1);
        }

        /// <summary>
        /// Affiche une fenêtre modale de manière asynchrone.
        /// </summary>
        public async Task<bool?> ShowModalWindowAsync<T>(IServiceProvider services, Window? owner = null) 
            where T : Window
        {
            try
            {
                // Utiliser la réflexion pour trouver une méthode CreateAsync
                var createAsyncMethod = typeof(T).GetMethod("CreateAsync", 
                    new[] { typeof(IServiceProvider) });

                if (createAsyncMethod == null)
                {
                    throw new InvalidOperationException(
                        $"La fenêtre {typeof(T).Name} doit avoir une méthode CreateAsync(IServiceProvider)");
                }

                // Factory pour créer la fenêtre via CreateAsync
                async Task<Window> WindowFactory()
                {
                    var result = createAsyncMethod.Invoke(null, new object[] { services });
                    if (result is Task<T> task)
                    {
                        return await task;
                    }
                    throw new InvalidOperationException(
                        $"CreateAsync de {typeof(T).Name} doit retourner Task<{typeof(T).Name}>");
                }

                return await ShowModalWindowAsync(WindowFactory, owner);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ModalWindowOrchestrator: Erreur lors de l'affichage de {typeof(T).Name}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Affiche une fenêtre modale avec configuration personnalisée.
        /// </summary>
        public async Task<bool?> ShowModalWindowAsync(
            Func<Task<Window>> windowFactory,
            Window? owner = null,
            Action<Window>? configureWindow = null)
        {
            await _semaphore.WaitAsync();
            try
            {
                _loggingService?.LogInfo("ModalWindowOrchestrator: Création de la fenêtre modale");
                var window = await windowFactory();

                // Configuration du propriétaire
                if (owner != null)
                {
                    window.Owner = owner;
                    _loggingService?.LogInfo($"ModalWindowOrchestrator: Owner défini: {owner.GetType().Name}");
                }

                // Configuration personnalisée
                configureWindow?.Invoke(window);

                // Affichage modal
                _loggingService?.LogInfo("ModalWindowOrchestrator: Affichage de la fenêtre en mode modal");
                var result = window.ShowDialog();

                _loggingService?.LogInfo($"ModalWindowOrchestrator: Résultat du dialogue: {result}");
                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ModalWindowOrchestrator: Erreur lors de l'affichage modal: {ex.Message}", ex);
                throw;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Libère les ressources utilisées par l'orchestrateur.
        /// </summary>
        public void Dispose()
        {
            _semaphore?.Dispose();
        }
    }
}
