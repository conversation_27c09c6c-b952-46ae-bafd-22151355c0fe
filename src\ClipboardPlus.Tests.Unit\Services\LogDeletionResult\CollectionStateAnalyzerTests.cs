using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Implementations;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Tests.Unit.Helpers;

namespace ClipboardPlus.Tests.Unit.Services.LogDeletionResult
{
    [TestFixture]
    public class CollectionStateAnalyzerTests
    {
        private CollectionStateAnalyzer _analyzer = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private ClipboardHistoryViewModel _viewModel = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _analyzer = new CollectionStateAnalyzer(_mockLoggingService.Object);
            _viewModel = ClipboardHistoryViewModelTestHelper.CreateViewModel();
        }

        [TearDown]
        public void TearDown()
        {
            _viewModel?.Dispose();
        }

        #region AnalyzeCollectionState Tests

        [Test]
        public void AnalyzeCollectionState_WithValidViewModel_ReturnsSuccessfulAnalysis()
        {
            // Arrange
            var testItems = CreateTestClipboardItems(5);
            _viewModel.HistoryItems.Clear();
            foreach (var item in testItems)
            {
                _viewModel.HistoryItems.Add(item);
            }

            // Act
            var result = _analyzer.AnalyzeCollectionState(_viewModel);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.AnalysisSuccessful, Is.True);
            Assert.That(result.ViewModelItemCount, Is.EqualTo(5));
            Assert.That(result.AnalysisId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(result.AnalysisTimestamp, Is.LessThanOrEqualTo(DateTime.Now));
        }

        [Test]
        public void AnalyzeCollectionState_WithNullViewModel_ReturnsFailedAnalysis()
        {
            // Act
            var result = _analyzer.AnalyzeCollectionState(null!);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.AnalysisSuccessful, Is.False);
            Assert.That(result.ErrorMessage, Is.Not.Null.And.Not.Empty);
            Assert.That(result.ViewModelItemCount, Is.EqualTo(0));
        }

        [Test]
        public void AnalyzeCollectionState_WithEmptyCollection_ReturnsValidAnalysis()
        {
            // Arrange
            _viewModel.HistoryItems.Clear();

            // Act
            var result = _analyzer.AnalyzeCollectionState(_viewModel);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.AnalysisSuccessful, Is.True);
            Assert.That(result.ViewModelItemCount, Is.EqualTo(0));
            Assert.That(result.ManagerItemCount, Is.EqualTo(0));
            Assert.That(result.CollectionsInSync, Is.True);
        }

        [Test]
        public void AnalyzeCollectionState_WithNullItems_DetectsNullCount()
        {
            // Arrange
            var testItems = CreateTestClipboardItems(3);
            testItems.Add(null!); // Ajouter un élément null
            _viewModel.HistoryItems.Clear();
            foreach (var item in testItems)
            {
                _viewModel.HistoryItems.Add(item);
            }

            // Act
            var result = _analyzer.AnalyzeCollectionState(_viewModel);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.AnalysisSuccessful, Is.True);
            Assert.That(result.NullItemCount, Is.EqualTo(1));
            Assert.That(result.ViewModelItemCount, Is.EqualTo(4)); // Total incluant null
        }

        [Test]
        public void AnalyzeCollectionState_WithDuplicates_DetectsDuplicateCount()
        {
            // Arrange
            var item1 = CreateTestClipboardItem("Item 1");
            var item2 = CreateTestClipboardItem("Item 2");
            var item3 = CreateTestClipboardItem("Item 3");

            // Créer des doublons en créant des éléments avec le même ID
            var duplicateItem1 = CreateTestClipboardItem("Duplicate 1");
            duplicateItem1.Id = item1.Id; // Même ID que item1

            var duplicateItem2 = CreateTestClipboardItem("Duplicate 2");
            duplicateItem2.Id = item2.Id; // Même ID que item2

            _viewModel.HistoryItems.Clear();
            _viewModel.HistoryItems.Add(item1);
            _viewModel.HistoryItems.Add(item2);
            _viewModel.HistoryItems.Add(item3);
            _viewModel.HistoryItems.Add(duplicateItem1); // Doublon d'ID
            _viewModel.HistoryItems.Add(duplicateItem2); // Doublon d'ID

            // Act
            var result = _analyzer.AnalyzeCollectionState(_viewModel);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.AnalysisSuccessful, Is.True);
            Assert.That(result.DuplicateCount, Is.GreaterThan(0));
            Assert.That(result.DuplicateCount, Is.EqualTo(2)); // 2 doublons d'ID
        }

        [Test]
        public void AnalyzeCollectionState_GeneratesStatistics()
        {
            // Arrange
            var testItems = CreateMixedTypeClipboardItems();
            _viewModel.HistoryItems.Clear();
            foreach (var item in testItems)
            {
                _viewModel.HistoryItems.Add(item);
            }

            // Act
            var result = _analyzer.AnalyzeCollectionState(_viewModel);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Statistics, Is.Not.Null);
            Assert.That(result.Statistics.TotalItems, Is.EqualTo(testItems.Count));
            Assert.That(result.Statistics.TypeDistribution, Is.Not.Null);
            Assert.That(result.Statistics.TypeDistribution.Count, Is.GreaterThan(0));
        }

        [Test]
        public void AnalyzeCollectionState_GeneratesFirstTenItemsSample()
        {
            // Arrange
            var testItems = CreateTestClipboardItems(15); // Plus de 10 éléments
            _viewModel.HistoryItems.Clear();
            foreach (var item in testItems)
            {
                _viewModel.HistoryItems.Add(item);
            }

            // Act
            var result = _analyzer.AnalyzeCollectionState(_viewModel);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FirstTenItems, Is.Not.Null);
            Assert.That(result.FirstTenItems.Count, Is.EqualTo(10));
        }

        #endregion

        #region FindItemInCollections Tests

        [Test]
        public void FindItemInCollections_WithExistingItem_FindsItem()
        {
            // Arrange
            var targetItem = CreateTestClipboardItem("Target item");
            var otherItems = CreateTestClipboardItems(3);

            _viewModel.HistoryItems.Clear();
            foreach (var item in otherItems)
            {
                _viewModel.HistoryItems.Add(item);
            }
            _viewModel.HistoryItems.Add(targetItem);

            // Act
            var result = _analyzer.FindItemInCollections(targetItem.Id, _viewModel);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Found, Is.True);
            Assert.That(result.FoundInViewModel, Is.EqualTo(targetItem));
        }

        [Test]
        public void FindItemInCollections_WithNonExistingItem_DoesNotFindItem()
        {
            // Arrange
            var targetItem = CreateTestClipboardItem("Non-existing item");
            var otherItems = CreateTestClipboardItems(3);

            _viewModel.HistoryItems.Clear();
            foreach (var item in otherItems)
            {
                _viewModel.HistoryItems.Add(item);
            }

            // Act
            var result = _analyzer.FindItemInCollections(targetItem.Id, _viewModel);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Found, Is.False);
            Assert.That(result.FoundInViewModel, Is.Null);
        }

        [Test]
        public void FindItemInCollections_WithInvalidId_HandlesGracefully()
        {
            // Act
            var result = _analyzer.FindItemInCollections(-1, _viewModel);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Found, Is.False);
            Assert.That(result.FoundInViewModel, Is.Null);
            Assert.That(result.FoundInManager, Is.Null);
        }

        [Test]
        public void FindItemInCollections_WithNullViewModel_HandlesGracefully()
        {
            // Arrange
            var targetItem = CreateTestClipboardItem("Test item");

            // Act
            var result = _analyzer.FindItemInCollections(targetItem.Id, null!);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Found, Is.False);
            Assert.That(result.FoundInViewModel, Is.Null);
        }

        #endregion

        // Note: ValidateConsistency n'est pas implémenté dans CollectionStateAnalyzer
        // Ces tests seraient pour DeletionResultValidator

        #region Helper Methods

        private List<ClipboardItem> CreateTestClipboardItems(int count)
        {
            var items = new List<ClipboardItem>();
            for (int i = 0; i < count; i++)
            {
                items.Add(CreateTestClipboardItem($"Test item {i + 1}"));
            }
            return items;
        }

        private ClipboardItem CreateTestClipboardItem(string content)
        {
            return new ClipboardItem
            {
                Id = DateTime.Now.Ticks + Random.Shared.Next(),
                TextPreview = content,
                CustomName = $"Custom {content}",
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes(content),
                Timestamp = DateTime.Now,
                IsPinned = false,
                OrderIndex = 0
            };
        }

        private List<ClipboardItem> CreateMixedTypeClipboardItems()
        {
            return new List<ClipboardItem>
            {
                new ClipboardItem
                {
                    Id = 1,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Text item",
                    RawData = System.Text.Encoding.UTF8.GetBytes("Text content"),
                    IsPinned = false
                },
                new ClipboardItem
                {
                    Id = 2,
                    DataType = ClipboardDataType.Image,
                    TextPreview = "Image item",
                    RawData = new byte[1024], // Fake image data
                    IsPinned = true
                },
                new ClipboardItem
                {
                    Id = 3,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "File item",
                    RawData = System.Text.Encoding.UTF8.GetBytes("C:\\test.txt"),
                    IsPinned = false
                }
            };
        }

        #endregion
    }
}
