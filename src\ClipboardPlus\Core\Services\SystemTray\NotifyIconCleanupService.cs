using System;
using System.Windows.Forms;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Implémentation du service de nettoyage des instances NotifyIcon.
    /// Responsabilité unique : gérer le nettoyage sécurisé des instances NotifyIcon existantes.
    /// </summary>
    public class NotifyIconCleanupService : INotifyIconCleanupService
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de NotifyIconCleanupService.
        /// </summary>
        /// <param name="loggingService">Service de logging pour enregistrer les opérations de nettoyage.</param>
        public NotifyIconCleanupService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <inheritdoc />
        public void CleanupExistingIcon(NotifyIcon? existingIcon)
        {
            if (existingIcon == null)
            {
                _loggingService.LogInfo("NotifyIconCleanupService: Aucune instance existante à nettoyer.");
                return;
            }

            try
            {
                _loggingService.LogWarning("NotifyIconCleanupService: Instance NotifyIcon existante détectée - Nettoyage en cours...");

                // Cacher l'icône avant de la disposer pour éviter les artefacts visuels
                if (existingIcon.Visible)
                {
                    _loggingService.LogInfo("NotifyIconCleanupService: Masquage de l'icône existante...");
                    existingIcon.Visible = false;
                }

                // Nettoyer les événements pour éviter les fuites mémoire
                _loggingService.LogInfo("NotifyIconCleanupService: Nettoyage des gestionnaires d'événements...");
                // Note: Les événements seront automatiquement nettoyés lors du Dispose()

                // Disposer de l'instance
                _loggingService.LogInfo("NotifyIconCleanupService: Disposition de l'instance NotifyIcon...");
                existingIcon.Dispose();

                _loggingService.LogInfo("NotifyIconCleanupService: Nettoyage terminé avec succès.");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"NotifyIconCleanupService: Erreur lors du nettoyage de l'instance existante: {ex.Message}", ex);
                // Ne pas remonter l'exception car le nettoyage ne doit pas empêcher la création d'une nouvelle instance
            }
        }

        /// <inheritdoc />
        public bool RequiresCleanup(NotifyIcon? icon)
        {
            if (icon == null)
            {
                return false;
            }

            try
            {
                // Vérifier si l'instance est encore valide et nécessite un nettoyage
                // Une instance non-disposée nécessite toujours un nettoyage
                bool requiresCleanup = true;

                _loggingService.LogInfo($"NotifyIconCleanupService: Vérification du besoin de nettoyage - Résultat: {requiresCleanup}");

                return requiresCleanup;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"NotifyIconCleanupService: Erreur lors de la vérification du besoin de nettoyage: {ex.Message}", ex);
                // En cas d'erreur, considérer qu'un nettoyage est nécessaire par sécurité
                return true;
            }
        }
    }
}
