using System;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.Messaging;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Settings;
using ClipboardPlus.Core.DataModels.Settings;
using ClipboardPlus.UI.Messages;

namespace ClipboardPlus.Services.Settings
{
    /// <summary>
    /// Implémentation du service de notification des paramètres.
    /// Centralise l'envoi des messages WeakReference et notifications utilisateur.
    /// </summary>
    public class SettingsNotificationService : ISettingsNotificationService
    {
        private readonly ILoggingService _loggingService;

        public SettingsNotificationService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        public async Task SendVisibilityChangedMessagesAsync(VisibilitySettings settings)
        {
            _loggingService?.LogInfo("[SettingsNotificationService] SendVisibilityChangedMessagesAsync DÉBUT");

            try
            {
                // Envoi du message HideTimestampChangedMessage
                _loggingService?.LogInfo($"[SettingsNotificationService] Envoi du message HideTimestampChangedMessage: {settings.HideTimestamp}");
                WeakReferenceMessenger.Default.Send(new HideTimestampChangedMessage(settings.HideTimestamp));
                _loggingService?.LogInfo("[SettingsNotificationService] Message HideTimestampChangedMessage envoyé avec succès");

                // Envoi du message HideItemTitleChangedMessage
                _loggingService?.LogInfo($"[SettingsNotificationService] DIAGNOSTIC CRITIQUE - Envoi du message HideItemTitleChangedMessage: {settings.HideItemTitle}");
                WeakReferenceMessenger.Default.Send(new HideItemTitleChangedMessage(settings.HideItemTitle));
                _loggingService?.LogInfo("[SettingsNotificationService] DIAGNOSTIC CRITIQUE - Message HideItemTitleChangedMessage envoyé avec succès");

                await Task.CompletedTask; // Pour respecter la signature async
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[SettingsNotificationService] SendVisibilityChangedMessagesAsync ERREUR", ex);
                // Ne pas re-lancer l'exception - l'envoi de messages ne doit pas faire échouer l'application des paramètres
            }
        }

        public async Task NotifySettingsAppliedAsync(SettingsApplicationResult result)
        {
            _loggingService?.LogInfo("[SettingsNotificationService] NotifySettingsAppliedAsync DÉBUT");

            try
            {
                if (result.Success)
                {
                    _loggingService?.LogInfo($"[SettingsNotificationService] Paramètres appliqués avec succès: {result.Message}");
                    _loggingService?.LogInfo($"[SettingsNotificationService] Éléments appliqués: {result.AppliedSettings.Count}");
                    
                    foreach (var setting in result.AppliedSettings)
                    {
                        _loggingService?.LogInfo($"[SettingsNotificationService] -> {setting.Key}: {setting.Value}");
                    }
                }
                else
                {
                    _loggingService?.LogInfo($"[SettingsNotificationService] Application des paramètres avec erreurs: {result.Message}");
                    _loggingService?.LogInfo($"[SettingsNotificationService] Nombre d'erreurs: {result.Errors.Count}");
                    
                    foreach (var error in result.Errors)
                    {
                        _loggingService?.LogInfo($"[SettingsNotificationService] -> Erreur: {error}");
                    }
                }

                await Task.CompletedTask; // Pour respecter la signature async
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[SettingsNotificationService] NotifySettingsAppliedAsync ERREUR", ex);
                throw; // Re-lancer l'exception pour que l'appelant puisse la gérer
            }
        }
    }
}
