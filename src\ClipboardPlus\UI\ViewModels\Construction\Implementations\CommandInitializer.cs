using ClipboardPlus.UI.ViewModels.Construction.Interfaces;

namespace ClipboardPlus.UI.ViewModels.Construction.Implementations
{
    /// <summary>
    /// Implémentation du service d'initialisation de toutes les commandes du ViewModel.
    /// Responsabilité unique : Initialisation centralisée de toutes les commandes selon le principe SRP.
    /// </summary>
    public class CommandInitializer : ICommandInitializer
    {
        /// <summary>
        /// Initialise toutes les commandes du ViewModel.
        /// Cette méthode centralise l'initialisation de toutes les commandes qui étaient
        /// précédemment dispersées dans plusieurs méthodes du constructeur (lignes 284-286) :
        /// - InitializeRenamingCommands() (ligne 284)
        /// - InitializeNewItemCommands() (ligne 285)
        /// - InitializeRemainingCommands() (ligne 286)
        ///
        /// IMPLÉMENTATION COMPLÈTE : Délègue aux méthodes publiques du ViewModel
        /// pour préserver l'ordre exact d'initialisation identifié dans la Pré-phase.
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel dont les commandes doivent être initialisées</param>
        /// <exception cref="ArgumentNullException">Si le ViewModel est null</exception>
        public void InitializeAllCommands(ClipboardHistoryViewModel viewModel)
        {
            if (viewModel == null) throw new ArgumentNullException(nameof(viewModel));

            // Initialisation dans l'ordre exact du constructeur original (lignes 284-286)
            // Cet ordre est critique pour le bon fonctionnement des commandes

            // 1. Initialiser les commandes de renommage (ligne 284)
            viewModel.InitializeRenamingCommandsPublic();

            // 2. Initialiser les commandes de nouvel élément (ligne 285)
            viewModel.InitializeNewItemCommandsPublic();

            // 3. Initialiser les commandes restantes (ligne 286)
            viewModel.InitializeRemainingCommandsPublic();
        }
    }
}
