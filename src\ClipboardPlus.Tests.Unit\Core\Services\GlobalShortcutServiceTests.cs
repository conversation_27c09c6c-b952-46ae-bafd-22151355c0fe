using System;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    [TestFixture]
    public class GlobalShortcutServiceTests
    {
        private GlobalShortcutService _shortcutService = null!;
        private bool _shortcutActivated;

        [SetUp]
        public void Initialize()
        {
            _shortcutService = new GlobalShortcutService();
            _shortcutActivated = false;
            _shortcutService.ShortcutActivated += (sender, args) => _shortcutActivated = true;
        }

        [Test]
        public void Constructor_InitializesDefaultValues()
        {
            // Utiliser la réflexion pour accéder aux champs privés
            var currentShortcutField = typeof(GlobalShortcutService).GetField("_currentShortcut", BindingFlags.NonPublic | BindingFlags.Instance);
            var isRegisteredField = typeof(GlobalShortcutService).GetField("_isRegistered", BindingFlags.NonPublic | BindingFlags.Instance);

            Assert.That(currentShortcutField, Is.Not.Null, "Le champ _currentShortcut n'a pas été trouvé via réflexion.");
            Assert.That(isRegisteredField, Is.Not.Null, "Le champ _isRegistered n'a pas été trouvé via réflexion.");

            var currentShortcut = (KeyCombination)currentShortcutField!.GetValue(_shortcutService)!;
            var isRegistered = (bool)isRegisteredField!.GetValue(_shortcutService)!;

            // Assert
            Assert.That(currentShortcut, Is.Not.Null);
            Assert.That(isRegistered, Is.False);
        }

        [Test]
        public void InitializeAsync_WithNullShortcut_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(() => _shortcutService.InitializeAsync(null));
        }

        [Test]
        public void GetCurrentRegisteredShortcut_ReturnsCurrentShortcut()
        {
            // Arrange - Définir un raccourci via réflexion
            var keyCombination = new KeyCombination(ModifierKeys.Control | ModifierKeys.Alt, Key.V);
            var currentShortcutField = typeof(GlobalShortcutService).GetField("_currentShortcut", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(currentShortcutField, Is.Not.Null, "Le champ _currentShortcut n'a pas été trouvé via réflexion.");
            currentShortcutField!.SetValue(_shortcutService, keyCombination);

            // Act
            var result = _shortcutService.GetCurrentRegisteredShortcut();

            // Assert
            Assert.That(result, Is.Not.Null, "GetCurrentRegisteredShortcut ne devrait pas retourner null si un raccourci est défini.");
            Assert.That(result.Modifiers, Is.EqualTo(keyCombination.Modifiers));
            Assert.That(result.Key, Is.EqualTo(keyCombination.Key));
        }

        [Test]
        public void UnregisterShortcut_WhenNotRegistered_DoesNotThrowException()
        {
            // Act & Assert - Ne devrait pas lancer d'exception
            _shortcutService.UnregisterShortcut();
        }

        [Test]
        public void UnregisterShortcut_WhenRegistered_SetsIsRegisteredToFalse()
        {
            // Arrange - Simuler un raccourci enregistré
            var isRegisteredField = typeof(GlobalShortcutService).GetField("_isRegistered", BindingFlags.NonPublic | BindingFlags.Instance);
            var windowHandleField = typeof(GlobalShortcutService).GetField("_windowHandle", BindingFlags.NonPublic | BindingFlags.Instance);

            Assert.That(isRegisteredField, Is.Not.Null, "Le champ _isRegistered n'a pas été trouvé via réflexion.");
            Assert.That(windowHandleField, Is.Not.Null, "Le champ _windowHandle n'a pas été trouvé via réflexion.");

            isRegisteredField!.SetValue(_shortcutService, true);
            windowHandleField!.SetValue(_shortcutService, new IntPtr(1)); // Valeur non nulle

            // Remplacer la méthode native UnregisterHotKey par un mock qui retourne toujours true
            // Note: Nous ne pouvons pas vraiment faire cela dans ce test, donc nous allons juste vérifier
            // que la méthode UnregisterShortcut est appelée sans exception

            // Act
            _shortcutService.UnregisterShortcut();

            // Assert
            var isRegisteredValue = isRegisteredField!.GetValue(_shortcutService);
            Assert.That(isRegisteredValue, Is.Not.Null, "_isRegistered ne devrait pas être null après GetValue.");
            Assert.That((bool)isRegisteredValue!, Is.False);
        }

        [Test]
        public void TryRegisterShortcutAsync_WithNullKeyCombination_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(() => _shortcutService.TryRegisterShortcutAsync(null));
        }

        [Test]
        public void OnShortcutActivated_TriggersEvent()
        {
            // Arrange - Accéder à la méthode privée OnShortcutActivated via réflexion
            var method = typeof(GlobalShortcutService).GetMethod("OnShortcutActivated", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(method, Is.Not.Null, "La méthode OnShortcutActivated n'a pas été trouvée via réflexion.");

            // Act
            method!.Invoke(_shortcutService, new object[] { });

            // Assert
            Assert.That(_shortcutActivated, Is.True);
        }

        [Test]
        public void WndProc_WithHotkeyMessage_TriggersShortcutActivated()
        {
            // Arrange
            const int WM_HOTKEY = 0x0312;
            var hotkeyId = 12345;

            // Définir l'ID du raccourci actuel via réflexion
            var currentHotkeyIdField = typeof(GlobalShortcutService).GetField("_currentHotkeyId", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(currentHotkeyIdField, Is.Not.Null, "Le champ _currentHotkeyId n'a pas été trouvé via réflexion.");
            currentHotkeyIdField!.SetValue(_shortcutService, hotkeyId);

            // Accéder à la méthode privée WndProc via réflexion
            var method = typeof(GlobalShortcutService).GetMethod("WndProc", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(method, Is.Not.Null, "La méthode WndProc n'a pas été trouvée via réflexion.");

            // Act
            var handled = false;
            object[] parameters = { IntPtr.Zero, WM_HOTKEY, (IntPtr)hotkeyId, IntPtr.Zero, handled };
            method!.Invoke(_shortcutService, parameters);

            // Assert
            Assert.That(_shortcutActivated, Is.True);
        }

        [Test]
        public void WndProc_WithNonHotkeyMessage_DoesNotTriggerShortcutActivated()
        {
            // Arrange
            const int WM_OTHER = 0x0100; // Un autre message Windows
            var hotkeyId = 12345;

            // Définir l'ID du raccourci actuel via réflexion
            var currentHotkeyIdField = typeof(GlobalShortcutService).GetField("_currentHotkeyId", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(currentHotkeyIdField, Is.Not.Null, "Le champ _currentHotkeyId n'a pas été trouvé via réflexion.");
            currentHotkeyIdField!.SetValue(_shortcutService, hotkeyId);

            // Accéder à la méthode privée WndProc via réflexion
            var method = typeof(GlobalShortcutService).GetMethod("WndProc", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(method, Is.Not.Null, "La méthode WndProc n'a pas été trouvée via réflexion.");

            // Act
            var handled = false;
            object[] parameters = { IntPtr.Zero, WM_OTHER, (IntPtr)hotkeyId, IntPtr.Zero, handled };
            method!.Invoke(_shortcutService, parameters);

            // Assert
            Assert.That(_shortcutActivated, Is.False);
        }

        [Test]
        public void WndProc_WithWrongHotkeyId_DoesNotTriggerShortcutActivated()
        {
            // Arrange
            const int WM_HOTKEY = 0x0312;
            var currentHotkeyId = 12345;
            var wrongHotkeyId = 54321;

            // Définir l'ID du raccourci actuel via réflexion
            var currentHotkeyIdField = typeof(GlobalShortcutService).GetField("_currentHotkeyId", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(currentHotkeyIdField, Is.Not.Null, "Le champ _currentHotkeyId n'a pas été trouvé via réflexion.");
            currentHotkeyIdField!.SetValue(_shortcutService, currentHotkeyId);

            // Accéder à la méthode privée WndProc via réflexion
            var method = typeof(GlobalShortcutService).GetMethod("WndProc", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(method, Is.Not.Null, "La méthode WndProc n'a pas été trouvée via réflexion.");

            // Act
            var handled = false;
            object[] parameters = { IntPtr.Zero, WM_HOTKEY, (IntPtr)wrongHotkeyId, IntPtr.Zero, handled };
            method!.Invoke(_shortcutService, parameters);

            // Assert
            Assert.That(_shortcutActivated, Is.False);
        }

        #region IsValidShortcut Tests

        [Test]
        [Description("Vérifie que IsValidShortcut retourne false avec une KeyCombination null")]
        public void IsValidShortcut_WithNullKeyCombination_ReturnsFalse()
        {
            // Arrange
            KeyCombination? nullKeyCombination = null;

            // Act
            var result = _shortcutService.IsValidShortcut(nullKeyCombination!);

            // Assert
            Assert.That(result, Is.False, "IsValidShortcut devrait retourner false avec une KeyCombination null");
        }

        [Test]
        [Description("Vérifie que IsValidShortcut retourne false avec aucun modificateur")]
        public void IsValidShortcut_WithNoModifiers_ReturnsFalse()
        {
            // Arrange
            var keyCombination = new KeyCombination
            {
                Modifiers = ModifierKeys.None,
                Key = Key.A
            };

            // Act
            var result = _shortcutService.IsValidShortcut(keyCombination);

            // Assert
            Assert.That(result, Is.False, "IsValidShortcut devrait retourner false sans modificateurs");
        }

        [Test]
        [Description("Vérifie que IsValidShortcut retourne false avec une touche None")]
        public void IsValidShortcut_WithKeyNone_ReturnsFalse()
        {
            // Arrange
            var keyCombination = new KeyCombination
            {
                Modifiers = ModifierKeys.Control,
                Key = Key.None
            };

            // Act
            var result = _shortcutService.IsValidShortcut(keyCombination);

            // Assert
            Assert.That(result, Is.False, "IsValidShortcut devrait retourner false avec Key.None");
        }

        [Test]
        [Description("Vérifie que IsValidShortcut retourne false avec modificateurs None et touche None")]
        public void IsValidShortcut_WithBothNone_ReturnsFalse()
        {
            // Arrange
            var keyCombination = new KeyCombination
            {
                Modifiers = ModifierKeys.None,
                Key = Key.None
            };

            // Act
            var result = _shortcutService.IsValidShortcut(keyCombination);

            // Assert
            Assert.That(result, Is.False, "IsValidShortcut devrait retourner false avec les deux None");
        }

        [Test]
        [Description("Vérifie que IsValidShortcut retourne true avec une combinaison valide Ctrl+A")]
        public void IsValidShortcut_WithValidCtrlA_ReturnsTrue()
        {
            // Arrange
            var keyCombination = new KeyCombination
            {
                Modifiers = ModifierKeys.Control,
                Key = Key.A
            };

            // Act
            var result = _shortcutService.IsValidShortcut(keyCombination);

            // Assert
            Assert.That(result, Is.True, "IsValidShortcut devrait retourner true avec Ctrl+A");
        }

        [Test]
        [Description("Vérifie que IsValidShortcut retourne true avec une combinaison valide Alt+F4")]
        public void IsValidShortcut_WithValidAltF4_ReturnsTrue()
        {
            // Arrange
            var keyCombination = new KeyCombination
            {
                Modifiers = ModifierKeys.Alt,
                Key = Key.F4
            };

            // Act
            var result = _shortcutService.IsValidShortcut(keyCombination);

            // Assert
            Assert.That(result, Is.True, "IsValidShortcut devrait retourner true avec Alt+F4");
        }

        [Test]
        [Description("Vérifie que IsValidShortcut retourne true avec plusieurs modificateurs")]
        public void IsValidShortcut_WithMultipleModifiers_ReturnsTrue()
        {
            // Arrange
            var keyCombination = new KeyCombination
            {
                Modifiers = ModifierKeys.Control | ModifierKeys.Shift,
                Key = Key.Escape
            };

            // Act
            var result = _shortcutService.IsValidShortcut(keyCombination);

            // Assert
            Assert.That(result, Is.True, "IsValidShortcut devrait retourner true avec Ctrl+Shift+Escape");
        }

        [Test]
        [Description("Vérifie que IsValidShortcut retourne true avec tous les modificateurs")]
        public void IsValidShortcut_WithAllModifiers_ReturnsTrue()
        {
            // Arrange
            var keyCombination = new KeyCombination
            {
                Modifiers = ModifierKeys.Control | ModifierKeys.Alt | ModifierKeys.Shift | ModifierKeys.Windows,
                Key = Key.Delete
            };

            // Act
            var result = _shortcutService.IsValidShortcut(keyCombination);

            // Assert
            Assert.That(result, Is.True, "IsValidShortcut devrait retourner true avec tous les modificateurs");
        }

        [Test]
        [Description("Vérifie que IsValidShortcut retourne true avec des touches de fonction")]
        public void IsValidShortcut_WithFunctionKeys_ReturnsTrue()
        {
            // Arrange
            var keyCombination = new KeyCombination
            {
                Modifiers = ModifierKeys.Control,
                Key = Key.F12
            };

            // Act
            var result = _shortcutService.IsValidShortcut(keyCombination);

            // Assert
            Assert.That(result, Is.True, "IsValidShortcut devrait retourner true avec Ctrl+F12");
        }

        [Test]
        [Description("Vérifie que IsValidShortcut retourne true avec des touches numériques")]
        public void IsValidShortcut_WithNumericKeys_ReturnsTrue()
        {
            // Arrange
            var keyCombination = new KeyCombination
            {
                Modifiers = ModifierKeys.Alt,
                Key = Key.D5
            };

            // Act
            var result = _shortcutService.IsValidShortcut(keyCombination);

            // Assert
            Assert.That(result, Is.True, "IsValidShortcut devrait retourner true avec Alt+5");
        }



        [Test]
        public void UnregisterShortcut_WithNoRegisteredShortcut_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _shortcutService.UnregisterShortcut());
        }

        #endregion
    }
}