using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Implementations
{
    /// <summary>
    /// Service de gestion de l'historique en mémoire.
    /// Responsabilité unique : Gérer la liste des éléments en mémoire.
    /// </summary>
    public class HistoryManager : IHistoryManager
    {
        private readonly List<ClipboardItem> _historyItems;
        private readonly IPersistenceService _persistenceService;
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du gestionnaire d'historique.
        /// </summary>
        /// <param name="persistenceService">Service de persistance pour les opérations de base de données</param>
        /// <param name="loggingService">Service de logging pour traçabilité</param>
        public HistoryManager(IPersistenceService persistenceService, ILoggingService? loggingService = null)
        {
            _persistenceService = persistenceService ?? throw new ArgumentNullException(nameof(persistenceService));
            _loggingService = loggingService;
            _historyItems = new List<ClipboardItem>();
        }

        /// <summary>
        /// Ajoute un élément à l'historique en mémoire.
        /// </summary>
        /// <param name="item">L'élément à ajouter</param>
        public void AddToHistory(ClipboardItem item)
        {
            if (item == null)
            {
                throw new ArgumentNullException(nameof(item));
            }

            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] HistoryManager.AddToHistory - Ajout élément ID={item.Id} en position 0");

            try
            {
                // Ajouter l'élément en première position (index 0)
                _historyItems.Insert(0, item);
                _loggingService?.LogInfo($"[{operationId}] HistoryManager.AddToHistory - Élément ID={item.Id} ajouté avec succès. Total éléments: {_historyItems.Count}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] HistoryManager.AddToHistory - Erreur lors de l'ajout: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Met à jour la position d'un élément existant dans l'historique.
        /// </summary>
        /// <param name="item">L'élément à déplacer en première position</param>
        public void MoveToTop(ClipboardItem item)
        {
            if (item == null)
            {
                throw new ArgumentNullException(nameof(item));
            }

            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] HistoryManager.MoveToTop - Déplacement élément ID={item.Id} vers le haut");

            try
            {
                // Trouver l'élément dans la liste
                var existingIndex = _historyItems.FindIndex(i => i.Id == item.Id);
                
                if (existingIndex == -1)
                {
                    _loggingService?.LogWarning($"[{operationId}] HistoryManager.MoveToTop - Élément ID={item.Id} non trouvé dans l'historique");
                    return;
                }

                if (existingIndex == 0)
                {
                    _loggingService?.LogInfo($"[{operationId}] HistoryManager.MoveToTop - Élément ID={item.Id} déjà en première position");
                    return;
                }

                // Supprimer l'élément de sa position actuelle
                _historyItems.RemoveAt(existingIndex);
                
                // L'ajouter en première position
                _historyItems.Insert(0, item);
                
                _loggingService?.LogInfo($"[{operationId}] HistoryManager.MoveToTop - Élément ID={item.Id} déplacé de la position {existingIndex} vers la position 0");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] HistoryManager.MoveToTop - Erreur lors du déplacement: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Met à jour un élément existant dans la collection et le déplace en première position.
        /// Utilisé quand un duplicata est détecté pour mettre à jour le timestamp et repositionner l'élément.
        /// </summary>
        /// <param name="existingItem">L'élément existant à mettre à jour et repositionner</param>
        public void UpdateExistingItem(ClipboardItem existingItem)
        {
            if (existingItem == null)
            {
                throw new ArgumentNullException(nameof(existingItem));
            }

            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] HistoryManager.UpdateExistingItem - Mise à jour élément ID={existingItem.Id}");

            try
            {
                // Trouver l'élément dans la collection
                var index = _historyItems.FindIndex(item => item.Id == existingItem.Id);
                if (index >= 0)
                {
                    // Déplacer l'élément en première position (plus récent)
                    _historyItems.RemoveAt(index);
                    _historyItems.Insert(0, existingItem);

                    _loggingService?.LogInfo($"[{operationId}] HistoryManager.UpdateExistingItem - Élément ID={existingItem.Id} déplacé en première position");
                }
                else
                {
                    _loggingService?.LogWarning($"[{operationId}] HistoryManager.UpdateExistingItem - Élément ID={existingItem.Id} non trouvé dans la collection");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] HistoryManager.UpdateExistingItem - Erreur inattendue: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Applique la limite maximale d'éléments dans l'historique.
        /// </summary>
        /// <param name="maxItems">Nombre maximum d'éléments autorisés</param>
        /// <returns>Nombre d'éléments supprimés</returns>
        public async Task<int> EnforceMaxHistoryItemsAsync(int maxItems)
        {
            if (maxItems < 0)
            {
                throw new ArgumentException("Le nombre maximum d'éléments ne peut pas être négatif", nameof(maxItems));
            }

            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] HistoryManager.EnforceMaxHistoryItemsAsync - Limite: {maxItems}, Actuel: {_historyItems.Count}");

            try
            {
                if (_historyItems.Count <= maxItems)
                {
                    _loggingService?.LogInfo($"[{operationId}] HistoryManager.EnforceMaxHistoryItemsAsync - Aucune suppression nécessaire");
                    return 0;
                }

                // Identifier les éléments à supprimer (non épinglés, les plus anciens)
                var itemsToRemove = _historyItems
                    .Where(i => !i.IsPinned)
                    .OrderBy(i => i.Timestamp)
                    .Take(_historyItems.Count - maxItems)
                    .ToList();

                if (!itemsToRemove.Any())
                {
                    _loggingService?.LogInfo($"[{operationId}] HistoryManager.EnforceMaxHistoryItemsAsync - Tous les éléments sont épinglés, aucune suppression possible");
                    return 0;
                }

                _loggingService?.LogInfo($"[{operationId}] HistoryManager.EnforceMaxHistoryItemsAsync - Suppression de {itemsToRemove.Count} élément(s) non épinglé(s)");

                int removedCount = 0;
                foreach (var item in itemsToRemove)
                {
                    try
                    {
                        // Supprimer de la base de données
                        await _persistenceService.DeleteClipboardItemAsync(item.Id);
                        
                        // Supprimer de la mémoire
                        _historyItems.Remove(item);
                        removedCount++;
                        
                        _loggingService?.LogInfo($"[{operationId}] HistoryManager.EnforceMaxHistoryItemsAsync - Élément ID={item.Id} supprimé");
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError($"[{operationId}] HistoryManager.EnforceMaxHistoryItemsAsync - Erreur lors de la suppression de l'élément ID={item.Id}: {ex.Message}", ex);
                        // Continuer avec les autres éléments
                    }
                }

                _loggingService?.LogInfo($"[{operationId}] HistoryManager.EnforceMaxHistoryItemsAsync - {removedCount} élément(s) supprimé(s) avec succès");
                return removedCount;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] HistoryManager.EnforceMaxHistoryItemsAsync - Erreur lors de l'application de la limite: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Obtient la liste actuelle des éléments de l'historique.
        /// </summary>
        public IReadOnlyList<ClipboardItem> GetHistoryItems()
        {
            return _historyItems.AsReadOnly();
        }
    }
}
