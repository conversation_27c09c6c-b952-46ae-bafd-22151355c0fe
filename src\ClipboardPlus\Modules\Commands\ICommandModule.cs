using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Modules.Core;

namespace ClipboardPlus.Modules.Commands
{
    /// <summary>
    /// Interface pour le module de gestion des commandes.
    /// 
    /// Ce module est responsable de la création, de l'exécution et de la gestion
    /// du cycle de vie de toutes les commandes de l'application.
    /// </summary>
    public interface ICommandModule : ClipboardPlus.Modules.Core.IModule
    {
        /// <summary>
        /// Commande pour supprimer l'élément sélectionné.
        /// </summary>
        ICommand DeleteSelectedItemCommand { get; }

        /// <summary>
        /// Commande pour effacer tout l'historique.
        /// </summary>
        ICommand ClearHistoryCommand { get; }

        /// <summary>
        /// Commande pour renommer un élément.
        /// </summary>
        ICommand RenameItemCommand { get; }

        /// <summary>
        /// Commande pour copier un élément vers le presse-papiers.
        /// </summary>
        ICommand CopyToClipboardCommand { get; }

        /// <summary>
        /// Commande pour coller l'élément sélectionné.
        /// </summary>
        ICommand PasteSelectedItemCommand { get; }

        /// <summary>
        /// Commande pour basculer l'épinglage d'un élément.
        /// </summary>
        ICommand TogglePinCommand { get; }

        /// <summary>
        /// Commande pour finaliser et sauvegarder un nouvel élément.
        /// </summary>
        ICommand FinalizeAndSaveNewItemCommand { get; }

        /// <summary>
        /// Commande pour annuler la création d'un nouvel élément.
        /// </summary>
        ICommand CancelNewItemCommand { get; }

        /// <summary>
        /// Commande pour confirmer le renommage d'un élément.
        /// </summary>
        ICommand ConfirmRenameCommand { get; }

        /// <summary>
        /// Commande pour annuler le renommage d'un élément.
        /// </summary>
        ICommand CancelRenameCommand { get; }

        /// <summary>
        /// Registre de toutes les commandes disponibles.
        /// </summary>
        ICommandRegistry CommandRegistry { get; }

        /// <summary>
        /// Contexte d'exécution des commandes.
        /// </summary>
        ICommandContext CommandContext { get; set; }

        /// <summary>
        /// Événement déclenché avant l'exécution d'une commande.
        /// </summary>
        event EventHandler<CommandExecutingEventArgs> CommandExecuting;

        /// <summary>
        /// Événement déclenché après l'exécution d'une commande.
        /// </summary>
        event EventHandler<CommandExecutedEventArgs> CommandExecuted;

        /// <summary>
        /// Événement déclenché lorsqu'une commande échoue.
        /// </summary>
        event EventHandler<CommandFailedEventArgs> CommandFailed;

        /// <summary>
        /// Enregistre une nouvelle commande dans le module.
        /// </summary>
        /// <param name="name">Nom de la commande</param>
        /// <param name="command">Instance de la commande</param>
        void RegisterCommand(string name, ICommand command);

        /// <summary>
        /// Désenregistre une commande du module.
        /// </summary>
        /// <param name="name">Nom de la commande</param>
        void UnregisterCommand(string name);

        /// <summary>
        /// Obtient une commande par son nom.
        /// </summary>
        /// <param name="name">Nom de la commande</param>
        /// <returns>Commande ou null si non trouvée</returns>
        ICommand? GetCommand(string name);

        /// <summary>
        /// Exécute une commande par son nom.
        /// </summary>
        /// <param name="commandName">Nom de la commande</param>
        /// <param name="parameter">Paramètre de la commande</param>
        /// <returns>Task représentant l'exécution</returns>
        Task ExecuteCommandAsync(string commandName, object? parameter = null);

        /// <summary>
        /// Vérifie si une commande peut être exécutée.
        /// </summary>
        /// <param name="commandName">Nom de la commande</param>
        /// <param name="parameter">Paramètre de la commande</param>
        /// <returns>True si la commande peut être exécutée</returns>
        bool CanExecuteCommand(string commandName, object? parameter = null);

        /// <summary>
        /// Obtient la liste de toutes les commandes enregistrées.
        /// </summary>
        /// <returns>Noms des commandes enregistrées</returns>
        IEnumerable<string> GetRegisteredCommands();

        /// <summary>
        /// Invalide toutes les commandes pour forcer une réévaluation de CanExecute.
        /// </summary>
        void InvalidateAllCommands();

        /// <summary>
        /// Invalide une commande spécifique.
        /// </summary>
        /// <param name="commandName">Nom de la commande à invalider</param>
        void InvalidateCommand(string commandName);
    }

    /// <summary>
    /// Interface pour le registre des commandes.
    /// </summary>
    public interface ICommandRegistry
    {
        /// <summary>
        /// Toutes les commandes enregistrées.
        /// </summary>
        IReadOnlyDictionary<string, ICommand> Commands { get; }

        /// <summary>
        /// Enregistre une commande.
        /// </summary>
        /// <param name="name">Nom de la commande</param>
        /// <param name="command">Instance de la commande</param>
        void Register(string name, ICommand command);

        /// <summary>
        /// Désenregistre une commande.
        /// </summary>
        /// <param name="name">Nom de la commande</param>
        /// <returns>True si la commande a été supprimée</returns>
        bool Unregister(string name);

        /// <summary>
        /// Obtient une commande par son nom.
        /// </summary>
        /// <param name="name">Nom de la commande</param>
        /// <returns>Commande ou null</returns>
        ICommand? Get(string name);

        /// <summary>
        /// Vérifie si une commande est enregistrée.
        /// </summary>
        /// <param name="name">Nom de la commande</param>
        /// <returns>True si la commande existe</returns>
        bool Contains(string name);

        /// <summary>
        /// Efface toutes les commandes.
        /// </summary>
        void Clear();
    }

    /// <summary>
    /// Interface pour le contexte d'exécution des commandes.
    /// </summary>
    public interface ICommandContext
    {
        /// <summary>
        /// Élément actuellement sélectionné.
        /// </summary>
        ClipboardItem? SelectedItem { get; set; }

        /// <summary>
        /// Éléments sélectionnés (pour les sélections multiples).
        /// </summary>
        IEnumerable<ClipboardItem> SelectedItems { get; set; }

        /// <summary>
        /// Contenu du nouvel élément en cours de création.
        /// </summary>
        string? NewItemContent { get; set; }

        /// <summary>
        /// Indique si un élément est en cours de création.
        /// </summary>
        bool IsCreatingNewItem { get; set; }

        /// <summary>
        /// Indique si un élément est en cours de renommage.
        /// </summary>
        bool IsRenamingItem { get; set; }

        /// <summary>
        /// ID de l'élément en cours de renommage.
        /// </summary>
        long? RenamingItemId { get; set; }

        /// <summary>
        /// Propriétés additionnelles du contexte.
        /// </summary>
        IDictionary<string, object> Properties { get; }

        /// <summary>
        /// Obtient une propriété du contexte.
        /// </summary>
        /// <typeparam name="T">Type de la propriété</typeparam>
        /// <param name="key">Clé de la propriété</param>
        /// <returns>Valeur de la propriété ou default(T)</returns>
        T? GetProperty<T>(string key);

        /// <summary>
        /// Définit une propriété du contexte.
        /// </summary>
        /// <typeparam name="T">Type de la propriété</typeparam>
        /// <param name="key">Clé de la propriété</param>
        /// <param name="value">Valeur de la propriété</param>
        void SetProperty<T>(string key, T value);

        /// <summary>
        /// Supprime une propriété du contexte.
        /// </summary>
        /// <param name="key">Clé de la propriété</param>
        /// <returns>True si la propriété a été supprimée</returns>
        bool RemoveProperty(string key);
    }

    /// <summary>
    /// Arguments d'événement pour l'exécution d'une commande.
    /// </summary>
    public class CommandExecutingEventArgs : EventArgs
    {
        /// <summary>
        /// Nom de la commande.
        /// </summary>
        public string CommandName { get; }

        /// <summary>
        /// Paramètre de la commande.
        /// </summary>
        public object? Parameter { get; }

        /// <summary>
        /// Contexte d'exécution.
        /// </summary>
        public ICommandContext Context { get; }

        /// <summary>
        /// Indique si l'exécution doit être annulée.
        /// </summary>
        public bool Cancel { get; set; }

        public CommandExecutingEventArgs(string commandName, object? parameter, ICommandContext context)
        {
            CommandName = commandName;
            Parameter = parameter;
            Context = context;
        }
    }

    /// <summary>
    /// Arguments d'événement pour une commande exécutée.
    /// </summary>
    public class CommandExecutedEventArgs : EventArgs
    {
        /// <summary>
        /// Nom de la commande.
        /// </summary>
        public string CommandName { get; }

        /// <summary>
        /// Paramètre de la commande.
        /// </summary>
        public object? Parameter { get; }

        /// <summary>
        /// Contexte d'exécution.
        /// </summary>
        public ICommandContext Context { get; }

        /// <summary>
        /// Résultat de l'exécution.
        /// </summary>
        public object? Result { get; }

        /// <summary>
        /// Durée d'exécution.
        /// </summary>
        public TimeSpan ExecutionTime { get; }

        public CommandExecutedEventArgs(string commandName, object? parameter, ICommandContext context, object? result, TimeSpan executionTime)
        {
            CommandName = commandName;
            Parameter = parameter;
            Context = context;
            Result = result;
            ExecutionTime = executionTime;
        }
    }

    /// <summary>
    /// Arguments d'événement pour une commande qui a échoué.
    /// </summary>
    public class CommandFailedEventArgs : EventArgs
    {
        /// <summary>
        /// Nom de la commande.
        /// </summary>
        public string CommandName { get; }

        /// <summary>
        /// Paramètre de la commande.
        /// </summary>
        public object? Parameter { get; }

        /// <summary>
        /// Contexte d'exécution.
        /// </summary>
        public ICommandContext Context { get; }

        /// <summary>
        /// Exception qui a causé l'échec.
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// Durée avant l'échec.
        /// </summary>
        public TimeSpan ExecutionTime { get; }

        public CommandFailedEventArgs(string commandName, object? parameter, ICommandContext context, Exception exception, TimeSpan executionTime)
        {
            CommandName = commandName;
            Parameter = parameter;
            Context = context;
            Exception = exception;
            ExecutionTime = executionTime;
        }
    }
}
