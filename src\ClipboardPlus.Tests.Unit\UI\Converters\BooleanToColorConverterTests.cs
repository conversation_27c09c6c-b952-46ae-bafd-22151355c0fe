using System;
using System.Globalization;
using System.Windows.Media;
using NUnit.Framework;
using ClipboardPlus.UI.Converters;

namespace ClipboardPlus.Tests.Unit.UI.Converters
{
    [TestFixture]
    public class BooleanToColorConverterTests
    {
        private BooleanToColorConverter? _converter;

        [SetUp]
        public void Setup()
        {
            _converter = new BooleanToColorConverter();
        }

        [Test]
        public void Convert_True_ReturnsGreenBrush()
        {
            // Arrange & Act
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            var result = _converter.Convert(true, typeof(Brush), parameter: new object(), culture: CultureInfo.InvariantCulture) as SolidColorBrush;

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être un SolidColorBrush");
            Assert.That(result!.Color, Is.EqualTo(Colors.Green), "La couleur devrait être verte pour true");
        }

        [Test]
        public void Convert_False_ReturnsTransparentBrush()
        {
            // Arrange & Act
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            var result = _converter.Convert(false, typeof(Brush), parameter: new object(), culture: CultureInfo.InvariantCulture) as SolidColorBrush;

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être un SolidColorBrush");
            Assert.That(result!.Color, Is.EqualTo(Colors.Transparent), "La couleur devrait être transparente pour false");
        }

        [Test]
        public void Convert_CustomColors_ReturnsCustomBrushes()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            string parameter = "Red,Blue";

            // Act
            var trueResult = _converter.Convert(true, typeof(Brush), parameter, culture: CultureInfo.InvariantCulture) as SolidColorBrush;
            var falseResult = _converter.Convert(false, typeof(Brush), parameter, culture: CultureInfo.InvariantCulture) as SolidColorBrush;

            // Assert
            Assert.That(trueResult, Is.Not.Null, "Le résultat pour true devrait être un SolidColorBrush");
            Assert.That(falseResult, Is.Not.Null, "Le résultat pour false devrait être un SolidColorBrush");
            Assert.That(trueResult!.Color, Is.EqualTo(Colors.Red), "La couleur pour true devrait être rouge");
            Assert.That(falseResult!.Color, Is.EqualTo(Colors.Blue), "La couleur pour false devrait être bleue");
        }

        [Test]
        public void Convert_InvalidColorParameter_ReturnsDefaultBrushes()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            string parameter = "InvalidColor,AnotherInvalidColor";

            // Act
            var trueResult = _converter.Convert(true, typeof(Brush), parameter, culture: CultureInfo.InvariantCulture) as SolidColorBrush;
            var falseResult = _converter.Convert(false, typeof(Brush), parameter, culture: CultureInfo.InvariantCulture) as SolidColorBrush;

            // Assert
            Assert.That(trueResult, Is.Not.Null, "Le résultat pour true devrait être un SolidColorBrush");
            Assert.That(falseResult, Is.Not.Null, "Le résultat pour false devrait être un SolidColorBrush");
            Assert.That(trueResult!.Color, Is.EqualTo(Colors.Green), "La couleur pour true devrait être verte (défaut)");
            Assert.That(falseResult!.Color, Is.EqualTo(Colors.Transparent), "La couleur pour false devrait être transparente (défaut)");
        }

        // --- NOUVEAUX TESTS POUR AMÉLIORER LA COUVERTURE ---

        [Test]
        public void Convert_WithNullValue_ReturnsFalseBrush()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            // Act - Tester avec null (VRAI CODE EXÉCUTÉ - branche non-bool)
            #pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            var result = _converter.Convert(null, typeof(Brush), null, CultureInfo.InvariantCulture) as SolidColorBrush;
            #pragma warning restore CS8625

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être un SolidColorBrush");
            Assert.That(result!.Color, Is.EqualTo(Colors.Transparent), "La couleur devrait être transparente pour null");
        }

        [Test]
        public void Convert_WithStringValue_ReturnsFalseBrush()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            // Act - Tester avec string (VRAI CODE EXÉCUTÉ - branche non-bool)
            #pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            var result = _converter.Convert("not a boolean", typeof(Brush), null, CultureInfo.InvariantCulture) as SolidColorBrush;
            #pragma warning restore CS8625

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être un SolidColorBrush");
            Assert.That(result!.Color, Is.EqualTo(Colors.Transparent), "La couleur devrait être transparente pour string");
        }

        [Test]
        public void Convert_WithIntValue_ReturnsFalseBrush()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            // Act - Tester avec int (VRAI CODE EXÉCUTÉ - branche non-bool)
            #pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            var result = _converter.Convert(42, typeof(Brush), null, CultureInfo.InvariantCulture) as SolidColorBrush;
            #pragma warning restore CS8625

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être un SolidColorBrush");
            Assert.That(result!.Color, Is.EqualTo(Colors.Transparent), "La couleur devrait être transparente pour int");
        }

        [Test]
        public void Convert_WithObjectValue_ReturnsFalseBrush()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            // Act - Tester avec object (VRAI CODE EXÉCUTÉ - branche non-bool)
            #pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            var result = _converter.Convert(new object(), typeof(Brush), null, CultureInfo.InvariantCulture) as SolidColorBrush;
            #pragma warning restore CS8625

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être un SolidColorBrush");
            Assert.That(result!.Color, Is.EqualTo(Colors.Transparent), "La couleur devrait être transparente pour object");
        }

        [Test]
        public void Convert_WithMalformedColorParameter_ReturnsDefaultBrushes()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            // Act - Tester avec paramètre malformé (VRAI CODE EXÉCUTÉ - branche catch)
            var result1 = _converter.Convert(true, typeof(Brush), "OnlyOneColor", CultureInfo.InvariantCulture) as SolidColorBrush;
            var result2 = _converter.Convert(true, typeof(Brush), "", CultureInfo.InvariantCulture) as SolidColorBrush;
            var result3 = _converter.Convert(true, typeof(Brush), "Color1,", CultureInfo.InvariantCulture) as SolidColorBrush;

            // Assert
            Assert.That(result1, Is.Not.Null, "Le résultat1 devrait être un SolidColorBrush");
            Assert.That(result2, Is.Not.Null, "Le résultat2 devrait être un SolidColorBrush");
            Assert.That(result3, Is.Not.Null, "Le résultat3 devrait être un SolidColorBrush");

            // Tous devraient utiliser les couleurs par défaut
            Assert.That(result1!.Color, Is.EqualTo(Colors.Green), "La couleur devrait être verte (défaut) pour paramètre incomplet");
            Assert.That(result2!.Color, Is.EqualTo(Colors.Green), "La couleur devrait être verte (défaut) pour paramètre vide");
            Assert.That(result3!.Color, Is.EqualTo(Colors.Green), "La couleur devrait être verte (défaut) pour paramètre avec virgule finale");
        }

        [Test]
        public void Convert_WithPartiallyValidColorParameter_UsesDefaultColorsOnError()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            // Act - Tester avec un paramètre partiellement valide (VRAI CODE EXÉCUTÉ - branches if dans try)
            var result = _converter.Convert(true, typeof(Brush), "Red,InvalidColor", CultureInfo.InvariantCulture) as SolidColorBrush;

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être un SolidColorBrush");
            // Quand une couleur est invalide, le convertisseur utilise les couleurs par défaut (comportement observé)
            Assert.That(result!.Color, Is.EqualTo(Colors.Green), "La couleur devrait être verte (défaut) quand une couleur est invalide");
        }

        [Test]
        public void Convert_WithNonStringParameter_IgnoresParameter()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            // Act - Tester avec paramètre non-string (VRAI CODE EXÉCUTÉ - branche parameter is string)
            var result = _converter.Convert(true, typeof(Brush), 123, CultureInfo.InvariantCulture) as SolidColorBrush;

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être un SolidColorBrush");
            Assert.That(result!.Color, Is.EqualTo(Colors.Green), "La couleur devrait être verte (défaut) quand le paramètre n'est pas string");
        }

        [Test]
        public void Convert_EdgeCases_HandleGracefully()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            // Act & Assert - Tester plusieurs cas limites (VRAI CODE EXÉCUTÉ)
            #pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            var result1 = _converter.Convert(true, null, null, null) as SolidColorBrush;
            var result2 = _converter.Convert(false, typeof(object), "Red,Blue", null) as SolidColorBrush;
            #pragma warning restore CS8625
            var result3 = _converter.Convert(true, typeof(Brush), "  Red  ,  Blue  ", CultureInfo.InvariantCulture) as SolidColorBrush;

            Assert.That(result1, Is.Not.Null, "Devrait gérer les paramètres null");
            Assert.That(result2, Is.Not.Null, "Devrait gérer les types cibles différents");
            Assert.That(result3, Is.Not.Null, "Devrait gérer les espaces dans les paramètres");

            Assert.That(result1!.Color, Is.EqualTo(Colors.Green), "Couleur par défaut pour true");
            Assert.That(result2!.Color, Is.EqualTo(Colors.Blue), "Couleur bleue pour false avec paramètre");
            Assert.That(result3!.Color, Is.EqualTo(Colors.Red), "Couleur rouge malgré les espaces");
        }
    }
}