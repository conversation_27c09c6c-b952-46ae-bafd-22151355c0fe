using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using NUnit.Framework;
using ClipboardPlus.Core.Services.Logging;

namespace ClipboardPlus.Tests.Unit.Core.Services.Logging
{
    /// <summary>
    /// Tests pour la classe FileLogTarget.
    /// Valide l'écriture dans les fichiers, le buffering et la gestion d'erreurs.
    /// </summary>
    [TestFixture]
    public class FileLogTargetTests
    {
        private string _tempLogFile = string.Empty;
        private FileLogTarget _target = null!;

        [SetUp]
        public void SetUp()
        {
            _tempLogFile = Path.GetTempFileName();
            _target = new FileLogTarget(_tempLogFile);
        }

        [TearDown]
        public void TearDown()
        {
            _target?.Dispose();
            if (File.Exists(_tempLogFile))
            {
                File.Delete(_tempLogFile);
            }
        }

        [Test]
        public void Write_WithSingleEntry_ShouldBufferEntry()
        {
            // Arrange
            var entry = new LogEntry("INFO", "Test message", DateTime.Now, "1", "UI", "TestMethod");

            // Vérifier d'abord que l'entrée ne nécessite pas de flush immédiat
            Assert.That(entry.RequiresImmediateFlush, Is.False, "L'entrée INFO ne devrait pas nécessiter de flush immédiat");

            // Act
            _target.Write(entry);

            // Assert - Le fichier ne devrait pas encore exister car c'est bufferisé
            // Note: On vérifie que le fichier est vide ou n'existe pas
            if (File.Exists(_tempLogFile))
            {
                var content = File.ReadAllText(_tempLogFile);
                Assert.That(content.Trim(), Is.Empty, "Le fichier devrait être vide car l'entrée devrait être bufferisée");
            }
        }

        [Test]
        public void Write_WithCriticalEntry_ShouldFlushImmediately()
        {
            // Arrange
            var entry = new LogEntry("CRITIQUE", "Critical error", DateTime.Now, "1", "UI", "TestMethod");

            // Act
            _target.Write(entry);

            // Assert - Le fichier devrait exister car les entrées critiques forcent un flush
            Assert.That(File.Exists(_tempLogFile), Is.True);
            
            var content = File.ReadAllText(_tempLogFile, Encoding.UTF8);
            Assert.That(content, Does.Contain("Critical error"));
            Assert.That(content, Does.Contain("[CRITIQUE]"));
        }

        [Test]
        public void Write_WithManyEntries_ShouldFlushWhenBufferFull()
        {
            // Arrange - Créer plus de 100 entrées pour dépasser MAX_BUFFER_SIZE
            var entries = Enumerable.Range(1, 105)
                .Select(i => new LogEntry("INFO", $"Message {i}", DateTime.Now, "1", "UI", "TestMethod"))
                .ToArray();

            // Act
            foreach (var entry in entries)
            {
                _target.Write(entry);
            }

            // Assert - Le fichier devrait exister car le buffer a été flushé
            Assert.That(File.Exists(_tempLogFile), Is.True);
            
            var content = File.ReadAllText(_tempLogFile, Encoding.UTF8);
            Assert.That(content, Does.Contain("Message 1"));
            Assert.That(content, Does.Contain("Message 100"));
        }

        [Test]
        public void Flush_WithBufferedEntries_ShouldWriteToFile()
        {
            // Arrange
            var entry1 = new LogEntry("INFO", "First message", DateTime.Now, "1", "UI", "TestMethod");
            var entry2 = new LogEntry("DEBUG", "Second message", DateTime.Now, "2", "Background", "TestMethod");
            
            _target.Write(entry1);
            _target.Write(entry2);

            // Act
            _target.Flush();

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            
            var content = File.ReadAllText(_tempLogFile, Encoding.UTF8);
            Assert.That(content, Does.Contain("First message"));
            Assert.That(content, Does.Contain("Second message"));
            Assert.That(content, Does.Contain("[INFO]"));
            Assert.That(content, Does.Contain("[DEBUG]"));
        }

        [Test]
        public void Write_WithMultiLineMessage_ShouldFormatCorrectly()
        {
            // Arrange
            var multiLineMessage = "Line 1\r\nLine 2\nLine 3";
            var entry = new LogEntry("ERROR", multiLineMessage, DateTime.Now, "1", "UI", "TestMethod");

            // Act
            _target.Write(entry);
            _target.Flush();

            // Assert
            var content = File.ReadAllText(_tempLogFile, Encoding.UTF8);
            var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            
            Assert.That(lines.Length, Is.EqualTo(3));
            Assert.That(lines[0], Does.Contain("[ERROR]").And.Contain("Line 1"));
            Assert.That(lines[1], Does.StartWith("    Line 2")); // Indentation
            Assert.That(lines[2], Does.StartWith("    Line 3")); // Indentation
        }

        [Test]
        public void WriteBatch_WithMultipleEntries_ShouldWriteAllEntries()
        {
            // Arrange
            var entries = new[]
            {
                new LogEntry("INFO", "Batch message 1", DateTime.Now, "1", "UI", "TestMethod"),
                new LogEntry("WARNING", "Batch message 2", DateTime.Now, "2", "Background", "TestMethod"),
                new LogEntry("ERROR", "Batch message 3", DateTime.Now, "3", "UI", "TestMethod")
            };

            // Act
            _target.WriteBatch(entries);

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            
            var content = File.ReadAllText(_tempLogFile, Encoding.UTF8);
            Assert.That(content, Does.Contain("Batch message 1"));
            Assert.That(content, Does.Contain("Batch message 2"));
            Assert.That(content, Does.Contain("Batch message 3"));
        }

        [Test]
        public void Write_WithInvalidPath_ShouldCreateRecoveryFile()
        {
            // Arrange
            var invalidPath = Path.Combine("Z:\\NonExistentDrive", "invalid.log");
            var invalidTarget = new FileLogTarget(invalidPath);
            var entry = new LogEntry("ERROR", "Test error", DateTime.Now, "1", "UI", "TestMethod");

            // Act
            invalidTarget.Write(entry);
            invalidTarget.Flush();

            // Assert - Vérifier qu'un fichier de récupération a été créé dans le dossier temp
            var tempFiles = Directory.GetFiles(Path.GetTempPath(), "clipboard_plus_recovery_*.log");
            Assert.That(tempFiles.Length, Is.GreaterThan(0));

            // Nettoyer
            foreach (var file in tempFiles)
            {
                try { File.Delete(file); } catch { }
            }
            invalidTarget.Dispose();
        }

        [Test]
        public void Dispose_WithPendingEntries_ShouldFlushBeforeDisposing()
        {
            // Arrange
            var entry = new LogEntry("INFO", "Final message", DateTime.Now, "1", "UI", "TestMethod");
            _target.Write(entry);

            // Act
            _target.Dispose();

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            
            var content = File.ReadAllText(_tempLogFile, Encoding.UTF8);
            Assert.That(content, Does.Contain("Final message"));
        }

        [Test]
        public void Write_AfterDispose_ShouldNotThrow()
        {
            // Arrange
            var entry = new LogEntry("INFO", "After dispose", DateTime.Now, "1", "UI", "TestMethod");
            _target.Dispose();

            // Act & Assert
            Assert.DoesNotThrow(() => _target.Write(entry));
        }
    }
}
