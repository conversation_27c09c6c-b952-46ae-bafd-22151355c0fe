using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.Diagnostics
{
    /// <summary>
    /// Interface pour la validation des commandes disponibles
    /// Respecte le Single Responsibility Principle (SRP)
    /// </summary>
    public interface ICommandValidator
    {
        /// <summary>
        /// Valide les commandes disponibles pour un élément et un ViewModel donnés
        /// </summary>
        /// <param name="viewModel">Le ViewModel contenant les commandes</param>
        /// <param name="item">L'élément pour lequel valider les commandes (peut être null)</param>
        /// <returns>Résultat de la validation des commandes</returns>
        CommandValidationResult ValidateCommands(ClipboardHistoryViewModel viewModel, ClipboardItem? item);
    }

    /// <summary>
    /// Résultat de la validation des commandes
    /// </summary>
    public record CommandValidationResult(
        Dictionary<string, bool> CommandStates,
        List<string> ValidationErrors,
        List<string> ValidationWarnings
    );
}
