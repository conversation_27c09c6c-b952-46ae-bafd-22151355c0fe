using System;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Services.Configuration;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.UI.ViewModels.Construction;

namespace ClipboardPlus.Tests
{
    /// <summary>
    /// Test simple pour vérifier l'activation de l'architecture managériale
    /// </summary>
    public class ManagerActivationTest
    {
        public static void TestManagerActivation()
        {
            Console.WriteLine("🔍 Test d'activation de l'architecture managériale...");
            
            try
            {
                // 1. Configuration des services
                var serviceProvider = HostConfiguration.ConfigureServices();
                Console.WriteLine("✅ Services configurés");

                // 2. Résolution des managers
                var historyManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IHistoryViewModelManager>();
                var commandManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.ICommandViewModelManager>();
                var itemCreationManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IItemCreationManager>();
                var eventManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IEventViewModelManager>();
                var visibilityManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IVisibilityViewModelManager>();
                var dragDropManager = serviceProvider.GetService<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IDragDropViewModelManager>();

                Console.WriteLine($"🔍 HistoryViewModelManager: {(historyManager != null ? "✅ RÉSOLU" : "❌ NULL")}");
                Console.WriteLine($"🔍 CommandViewModelManager: {(commandManager != null ? "✅ RÉSOLU" : "❌ NULL")}");
                Console.WriteLine($"🔍 ItemCreationManager: {(itemCreationManager != null ? "✅ RÉSOLU" : "❌ NULL")}");
                Console.WriteLine($"🔍 EventViewModelManager: {(eventManager != null ? "✅ RÉSOLU" : "❌ NULL")}");
                Console.WriteLine($"🔍 VisibilityViewModelManager: {(visibilityManager != null ? "✅ RÉSOLU" : "❌ NULL")}");
                Console.WriteLine($"🔍 DragDropViewModelManager: {(dragDropManager != null ? "✅ RÉSOLU" : "❌ NULL")}");

                // 3. Test de création du ViewModel
                var clipboardHistoryManager = serviceProvider.GetRequiredService<ClipboardPlus.Core.Services.IClipboardHistoryManager>();
                var clipboardService = serviceProvider.GetRequiredService<ClipboardPlus.Core.Services.IClipboardInteractionService>();
                var settingsManager = serviceProvider.GetRequiredService<ClipboardPlus.Core.Services.ISettingsManager>();
                var notificationService = serviceProvider.GetRequiredService<ClipboardPlus.Core.Services.IUserNotificationService>();
                var userInteractionService = serviceProvider.GetRequiredService<ClipboardPlus.Core.Services.IUserInteractionService>();
                var renameService = serviceProvider.GetRequiredService<ClipboardPlus.Core.Services.IRenameService>();
                var deletionLogger = serviceProvider.GetRequiredService<ClipboardPlus.Core.Services.LogDeletionResult.Interfaces.IDeletionResultLogger>();

                Console.WriteLine("✅ Services principaux résolus");

                // 4. Création du ViewModel via Factory
                var viewModel = ClipboardHistoryViewModelFactory.CreateWithDependencyInjection(
                    serviceProvider,
                    clipboardHistoryManager,
                    clipboardService,
                    settingsManager,
                    notificationService,
                    userInteractionService,
                    renameService,
                    deletionLogger
                );

                Console.WriteLine("✅ ViewModel créé");

                // 5. Test de l'activation des managers
                bool isManagerArchitectureActive = viewModel.IsManagerArchitectureActive;
                Console.WriteLine($"🎯 Architecture managériale active: {(isManagerArchitectureActive ? "✅ OUI" : "❌ NON")}");

                if (isManagerArchitectureActive)
                {
                    Console.WriteLine("🎉 SUCCÈS: L'architecture managériale est activée !");
                }
                else
                {
                    Console.WriteLine("⚠️ ATTENTION: L'application utilise les fallbacks (architecture legacy)");
                }

                // 6. Test d'une propriété déléguée
                try
                {
                    var historyItems = viewModel.HistoryItems;
                    Console.WriteLine($"✅ HistoryItems accessible: {historyItems != null}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Erreur lors de l'accès à HistoryItems: {ex.Message}");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERREUR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
