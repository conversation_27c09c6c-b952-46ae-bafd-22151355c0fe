using System;
using ClipboardPlus.Modules.Core;
using NUnit.Framework;
using ModuleState = ClipboardPlus.Modules.Core.ModuleState;

namespace ClipboardPlus.Tests.Unit.Modules.Core
{
    /// <summary>
    /// Tests unitaires pour les exceptions des modules
    /// </summary>
    [TestFixture]
    public class ModuleExceptionsTests
    {
        #region Tests de ModuleInitializationException

        [Test]
        public void ModuleInitializationException_WithMessage_ShouldSetProperties()
        {
            // Arrange
            var moduleName = "TestModule";
            var moduleState = ModuleState.Created;
            var message = "Initialization failed";

            // Act
            var exception = new ModuleInitializationException(moduleName, moduleState, message);

            // Assert
            Assert.That(exception.ModuleName, Is.EqualTo(moduleName));
            Assert.That(exception.ModuleState, Is.EqualTo(moduleState));
            Assert.That(exception.Message, Is.EqualTo(message));
            Assert.That(exception.InnerException, Is.Null);
        }

        [Test]
        public void ModuleInitializationException_WithInnerException_ShouldSetProperties()
        {
            // Arrange
            var moduleName = "TestModule";
            var moduleState = ModuleState.Created;
            var message = "Initialization failed";
            var innerException = new InvalidOperationException("Inner error");

            // Act
            var exception = new ModuleInitializationException(moduleName, moduleState, message, innerException);

            // Assert
            Assert.That(exception.ModuleName, Is.EqualTo(moduleName));
            Assert.That(exception.ModuleState, Is.EqualTo(moduleState));
            Assert.That(exception.Message, Is.EqualTo(message));
            Assert.That(exception.InnerException, Is.EqualTo(innerException));
        }

        #endregion



        #region Tests de ModuleOperationException

        [Test]
        public void ModuleOperationException_WithMessage_ShouldSetProperties()
        {
            // Arrange
            var moduleName = "TestModule";
            var moduleState = ModuleState.Running;
            var operationName = "TestOperation";
            var message = "Operation failed";

            // Act
            var exception = new ModuleOperationException(moduleName, moduleState, operationName, message);

            // Assert
            Assert.That(exception.ModuleName, Is.EqualTo(moduleName));
            Assert.That(exception.ModuleState, Is.EqualTo(moduleState));
            Assert.That(exception.Operation, Is.EqualTo(operationName));
            Assert.That(exception.Message, Is.EqualTo(message));
            Assert.That(exception.InnerException, Is.Null);
        }

        [Test]
        public void ModuleOperationException_WithInnerException_ShouldSetProperties()
        {
            // Arrange
            var moduleName = "TestModule";
            var moduleState = ModuleState.Running;
            var operationName = "TestOperation";
            var message = "Operation failed";
            var innerException = new InvalidOperationException("Inner error");

            // Act
            var exception = new ModuleOperationException(moduleName, moduleState, operationName, message, innerException);

            // Assert
            Assert.That(exception.ModuleName, Is.EqualTo(moduleName));
            Assert.That(exception.ModuleState, Is.EqualTo(moduleState));
            Assert.That(exception.Operation, Is.EqualTo(operationName));
            Assert.That(exception.Message, Is.EqualTo(message));
            Assert.That(exception.InnerException, Is.EqualTo(innerException));
        }

        #endregion

        #region Tests d'héritage

        [Test]
        public void ModuleInitializationException_ShouldInheritFromModuleException()
        {
            // Arrange & Act
            var exception = new ModuleInitializationException("Test", ModuleState.Created, "Test message");

            // Assert
            Assert.That(exception, Is.InstanceOf<ModuleException>());
            Assert.That(exception, Is.InstanceOf<Exception>());
        }



        [Test]
        public void ModuleOperationException_ShouldInheritFromModuleException()
        {
            // Arrange & Act
            var exception = new ModuleOperationException("Test", ModuleState.Running, "TestOp", "Test message");

            // Assert
            Assert.That(exception, Is.InstanceOf<ModuleException>());
            Assert.That(exception, Is.InstanceOf<Exception>());
        }

        #endregion

        #region Tests de sérialisation

        [Test]
        public void ModuleException_ShouldBeSerializable()
        {
            // Arrange
            var exception = new ModuleInitializationException("TestModule", ModuleState.Created, "Test message");

            // Act & Assert - Vérifier que l'exception peut être sérialisée
            Assert.DoesNotThrow(() =>
            {
                var message = exception.ToString();
                Assert.That(message, Is.Not.Null);
                Assert.That(message, Does.Contain("Test message"));

                // Vérifier les propriétés spécifiques
                Assert.That(exception.ModuleName, Is.EqualTo("TestModule"));
                Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Created));
            });
        }

        #endregion
    }
}
