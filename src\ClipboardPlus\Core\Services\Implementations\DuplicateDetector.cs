using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Implementations
{
    /// <summary>
    /// Service de détection de doublons dans l'historique du presse-papiers.
    /// Responsabilité unique : Détecter les éléments en double basé sur la comparaison des données brutes.
    /// </summary>
    public class DuplicateDetector : IDuplicateDetector
    {
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du détecteur de doublons.
        /// </summary>
        /// <param name="loggingService">Service de logging pour traçabilité</param>
        public DuplicateDetector(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Recherche un doublon d'un élément dans l'historique.
        /// </summary>
        /// <param name="item">L'élément à rechercher</param>
        /// <param name="historyItems">Liste des éléments de l'historique</param>
        /// <returns>L'élément en double trouvé, ou null si aucun doublon</returns>
        public async Task<ClipboardItem?> FindDuplicateAsync(ClipboardItem item, IEnumerable<ClipboardItem> historyItems)
        {
            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] DuplicateDetector.FindDuplicateAsync - Début recherche pour Type: {item?.DataType}, Taille RawData: {item?.RawData?.Length ?? 0}");

            try
            {
                // Validation des paramètres d'entrée
                if (item?.RawData == null || item.RawData.Length == 0)
                {
                    _loggingService?.LogInfo($"[{operationId}] DuplicateDetector.FindDuplicateAsync - itemToTest ou RawData est null/vide. Non considéré comme doublon.");
                    return null; // Ne peut pas être un doublon si les données brutes sont vides
                }

                if (historyItems == null)
                {
                    _loggingService?.LogWarning($"[{operationId}] DuplicateDetector.FindDuplicateAsync - historyItems est null");
                    return null;
                }

                var historyList = historyItems.ToList();
                _loggingService?.LogInfo($"[{operationId}] DuplicateDetector.FindDuplicateAsync - {historyList.Count} éléments à analyser pour comparaison.");

                // Optimisation : filtrer d'abord par type de données
                var sameTypeItems = historyList.Where(i => i.DataType == item.DataType).ToList();
                _loggingService?.LogDebug($"[{operationId}] DuplicateDetector.FindDuplicateAsync - {sameTypeItems.Count} éléments du même type ({item.DataType})");

                // Rechercher un élément avec des données identiques
                foreach (var existingItem in sameTypeItems)
                {
                    if (AreDuplicates(item, existingItem))
                    {
                        _loggingService?.LogInfo($"[{operationId}] DuplicateDetector.FindDuplicateAsync - Doublon trouvé. ID existant: {existingItem.Id}");
                        return existingItem; // Retourne l'objet existant
                    }
                }

                _loggingService?.LogInfo($"[{operationId}] DuplicateDetector.FindDuplicateAsync - Aucun doublon trouvé.");
                return null;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] DuplicateDetector.FindDuplicateAsync - Erreur inattendue: {ex.Message}", ex);
                return null; // En cas d'erreur, considérer qu'il n'y a pas de doublon
            }
        }

        /// <summary>
        /// Détermine si deux éléments sont considérés comme des doublons.
        /// </summary>
        /// <param name="item1">Premier élément</param>
        /// <param name="item2">Deuxième élément</param>
        /// <returns>True si les éléments sont des doublons</returns>
        public bool AreDuplicates(ClipboardItem item1, ClipboardItem item2)
        {
            // Validation des paramètres
            if (item1 == null || item2 == null)
            {
                return false;
            }

            // Les éléments doivent avoir le même type de données
            if (item1.DataType != item2.DataType)
            {
                return false;
            }

            // Les deux éléments doivent avoir des données brutes
            if (item1.RawData == null || item2.RawData == null)
            {
                return false;
            }

            // Optimisation : vérifier d'abord la longueur
            if (item1.RawData.Length != item2.RawData.Length)
            {
                return false;
            }

            // Comparaison byte par byte des données brutes
            return item1.RawData.SequenceEqual(item2.RawData);
        }
    }
}
