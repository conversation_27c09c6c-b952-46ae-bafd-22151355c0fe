using System;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour la gestion des événements de changement du contenu du presse-papiers.
    /// Ce service encapsule toute la logique de traitement des événements du presse-papiers
    /// qui était précédemment dans la classe App.
    /// </summary>
    public interface IClipboardEventHandler
    {
        /// <summary>
        /// Gère l'événement de changement du contenu du presse-papiers.
        /// Reproduit exactement le comportement de ClipboardListener_ClipboardContentChanged
        /// de la classe App, avec la même logique de fallback et gestion d'erreurs.
        /// </summary>
        /// <param name="sender">L'objet qui a déclenché l'événement</param>
        /// <param name="e">Les arguments de l'événement</param>
        /// <returns>Une tâche représentant l'opération asynchrone</returns>
        Task HandleClipboardContentChangedAsync(object? sender, EventArgs e);
    }
}
