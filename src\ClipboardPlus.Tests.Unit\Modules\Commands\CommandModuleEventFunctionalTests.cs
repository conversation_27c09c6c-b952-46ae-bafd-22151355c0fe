using NUnit.Framework;
using ClipboardPlus.Modules.Commands;
using ClipboardPlus.Modules.Core.Events;
using System;

namespace ClipboardPlus.Tests.Unit.Modules.Commands
{
    /// <summary>
    /// Tests fonctionnels pour CommandModuleEvent - vérifient le comportement métier
    /// dans des scénarios d'usage réels de communication inter-modules
    /// </summary>
    [TestFixture]
    public class CommandModuleEventFunctionalTests
    {
        #region Scénarios métier de communication inter-modules

        [Test]
        public void CommandModuleEvent_SuccessfulDataOperation_ShouldCommunicateBusinessResults()
        {
            // Arrange - Scénario : Opération de données réussie avec résultats métier
            var sourceModule = "ClipboardHistoryModule";
            var commandName = "ExportHistoryToFile";
            var parameter = new { FilePath = @"C:\exports\history.json", Format = "JSON", IncludeMetadata = true };
            var result = new { ExportedItems = 150, FileSize = "2.5MB", Duration = "3.2s" };
            var executionTime = TimeSpan.FromSeconds(3.2);

            // Act
            var moduleEvent = new CommandModuleEvent(sourceModule, commandName, parameter, result, executionTime);

            // Assert - Vérifier que les résultats métier sont communiqués
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("ClipboardHistoryModule"));
            Assert.That(moduleEvent.CommandName, Is.EqualTo("ExportHistoryToFile"));
            Assert.That(moduleEvent.Parameter, Is.EqualTo(parameter));
            Assert.That(moduleEvent.Result, Is.EqualTo(result));
            Assert.That(moduleEvent.ExecutionTime, Is.EqualTo(executionTime));
            
            // Vérifier les propriétés héritées de ModuleEventBase
            Assert.That(moduleEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(moduleEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
            Assert.That(moduleEvent.TargetModule, Is.Null); // Broadcast par défaut
        }

        [Test]
        public void CommandModuleEvent_UserInteractionCommand_ShouldTrackUserBehavior()
        {
            // Arrange - Scénario : Commande d'interaction utilisateur pour analytics
            var sourceModule = "UICommandModule";
            var commandName = "PinClipboardItem";
            var parameter = new { ItemId = "item-456", UserId = "user-123", Source = "ContextMenu" };
            var result = new { Success = true, PreviousState = "Unpinned", NewState = "Pinned" };
            var executionTime = TimeSpan.FromMilliseconds(85);

            // Act
            var moduleEvent = new CommandModuleEvent(sourceModule, commandName, parameter, result, executionTime);

            // Assert - Vérifier que le comportement utilisateur est tracké
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("UICommandModule"));
            Assert.That(moduleEvent.CommandName, Is.EqualTo("PinClipboardItem"));
            
            // Vérifier que les données utilisateur sont capturées
            var parameterData = moduleEvent.Parameter as dynamic;
            Assert.That(parameterData, Is.Not.Null);
            
            var resultData = moduleEvent.Result as dynamic;
            Assert.That(resultData, Is.Not.Null);
            
            Assert.That(moduleEvent.ExecutionTime.TotalMilliseconds, Is.LessThan(100)); // Interaction rapide
        }

        [Test]
        public void CommandModuleEvent_SystemMaintenanceOperation_ShouldReportMaintenanceMetrics()
        {
            // Arrange - Scénario : Opération de maintenance système
            var sourceModule = "MaintenanceModule";
            var commandName = "CleanupExpiredItems";
            var parameter = new { 
                MaxAge = TimeSpan.FromDays(30), 
                BatchSize = 100, 
                PreservePinned = true 
            };
            var result = new { 
                ItemsDeleted = 245, 
                SpaceFreed = "15.7MB", 
                DatabaseOptimized = true,
                MaintenanceLevel = "Standard"
            };
            var executionTime = TimeSpan.FromMinutes(2.8);

            // Act
            var moduleEvent = new CommandModuleEvent(sourceModule, commandName, parameter, result, executionTime);

            // Assert - Vérifier que les métriques de maintenance sont reportées
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("MaintenanceModule"));
            Assert.That(moduleEvent.CommandName, Is.EqualTo("CleanupExpiredItems"));
            Assert.That(moduleEvent.ExecutionTime.TotalMinutes, Is.GreaterThan(2));
            
            // Vérifier que l'événement peut être utilisé pour monitoring
            Assert.That(moduleEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(moduleEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void CommandModuleEvent_SecurityAuditCommand_ShouldLogSecurityEvents()
        {
            // Arrange - Scénario : Commande d'audit de sécurité
            var sourceModule = "SecurityModule";
            var commandName = "AuditUserAccess";
            var parameter = new { 
                UserId = "admin-789", 
                AuditType = "PermissionCheck", 
                ResourceId = "sensitive-data-001" 
            };
            var result = new { 
                AccessGranted = false, 
                Reason = "InsufficientPrivileges", 
                SecurityLevel = "High",
                AuditTrailId = "audit-2024-001"
            };
            var executionTime = TimeSpan.FromMilliseconds(150);

            // Act
            var moduleEvent = new CommandModuleEvent(sourceModule, commandName, parameter, result, executionTime);

            // Assert - Vérifier que les événements de sécurité sont loggés
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("SecurityModule"));
            Assert.That(moduleEvent.CommandName, Is.EqualTo("AuditUserAccess"));
            Assert.That(moduleEvent.ExecutionTime.TotalMilliseconds, Is.GreaterThan(100)); // Vérification sécurité prend du temps
            
            // Vérifier que l'événement contient les informations d'audit
            Assert.That(moduleEvent.Parameter, Is.Not.Null);
            Assert.That(moduleEvent.Result, Is.Not.Null);
        }

        #endregion

        #region Scénarios de performance et monitoring

        [Test]
        public void CommandModuleEvent_PerformanceCriticalOperation_ShouldTrackPerformanceMetrics()
        {
            // Arrange - Scénario : Opération critique en performance
            var sourceModule = "DataProcessingModule";
            var commandName = "ProcessLargeClipboardBatch";
            var parameter = new { 
                BatchSize = 10000, 
                ProcessingMode = "Parallel", 
                MaxConcurrency = 8 
            };
            var result = new { 
                ProcessedItems = 10000, 
                FailedItems = 3, 
                AverageProcessingTimeMs = 2.5,
                TotalMemoryUsedMB = 256,
                CpuUsagePercent = 85
            };
            var executionTime = TimeSpan.FromMinutes(5.2);

            // Act
            var moduleEvent = new CommandModuleEvent(sourceModule, commandName, parameter, result, executionTime);

            // Assert - Vérifier que les métriques de performance sont trackées
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("DataProcessingModule"));
            Assert.That(moduleEvent.CommandName, Is.EqualTo("ProcessLargeClipboardBatch"));
            Assert.That(moduleEvent.ExecutionTime.TotalMinutes, Is.GreaterThan(5));
            
            // Vérifier que l'événement peut être utilisé pour optimisation
            Assert.That(moduleEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(moduleEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void CommandModuleEvent_RealTimeOperation_ShouldSupportLowLatencyMonitoring()
        {
            // Arrange - Scénario : Opération temps réel avec monitoring faible latence
            var sourceModule = "RealTimeModule";
            var commandName = "CaptureClipboardChange";
            var parameter = new { 
                CaptureMode = "Immediate", 
                FilterEnabled = true, 
                Priority = "High" 
            };
            var result = new { 
                CaptureSuccess = true, 
                DataSize = 1024, 
                FilterApplied = "TextOnly",
                QueuePosition = 1
            };
            var executionTime = TimeSpan.FromMilliseconds(5);

            // Act
            var moduleEvent = new CommandModuleEvent(sourceModule, commandName, parameter, result, executionTime);

            // Assert - Vérifier que le monitoring faible latence est supporté
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("RealTimeModule"));
            Assert.That(moduleEvent.CommandName, Is.EqualTo("CaptureClipboardChange"));
            Assert.That(moduleEvent.ExecutionTime.TotalMilliseconds, Is.LessThan(10)); // Très rapide
            
            // Vérifier que l'événement est créé rapidement
            Assert.That(moduleEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(moduleEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        #endregion

        #region Scénarios de gestion des données nulles

        [Test]
        public void CommandModuleEvent_NullParameterCommand_ShouldHandleNullInputsGracefully()
        {
            // Arrange - Scénario : Commande sans paramètres (cas métier valide)
            var sourceModule = "SystemModule";
            var commandName = "GetSystemStatus";
            object? nullParameter = null;
            var result = new { 
                Status = "Healthy", 
                Uptime = TimeSpan.FromHours(24.5), 
                MemoryUsage = "45%" 
            };
            var executionTime = TimeSpan.FromMilliseconds(25);

            // Act
            var moduleEvent = new CommandModuleEvent(sourceModule, commandName, nullParameter, result, executionTime);

            // Assert - Vérifier que les paramètres null sont gérés
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("SystemModule"));
            Assert.That(moduleEvent.CommandName, Is.EqualTo("GetSystemStatus"));
            Assert.That(moduleEvent.Parameter, Is.Null);
            Assert.That(moduleEvent.Result, Is.Not.Null);
            Assert.That(moduleEvent.ExecutionTime, Is.EqualTo(TimeSpan.FromMilliseconds(25)));
        }

        [Test]
        public void CommandModuleEvent_NullResultCommand_ShouldHandleVoidOperations()
        {
            // Arrange - Scénario : Commande sans résultat (opération void)
            var sourceModule = "NotificationModule";
            var commandName = "ShowNotification";
            var parameter = new { 
                Message = "Operation completed", 
                Type = "Info", 
                Duration = 3000 
            };
            object? nullResult = null;
            var executionTime = TimeSpan.FromMilliseconds(100);

            // Act
            var moduleEvent = new CommandModuleEvent(sourceModule, commandName, parameter, nullResult, executionTime);

            // Assert - Vérifier que les résultats null sont gérés
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("NotificationModule"));
            Assert.That(moduleEvent.CommandName, Is.EqualTo("ShowNotification"));
            Assert.That(moduleEvent.Parameter, Is.Not.Null);
            Assert.That(moduleEvent.Result, Is.Null);
            Assert.That(moduleEvent.ExecutionTime, Is.EqualTo(TimeSpan.FromMilliseconds(100)));
        }

        #endregion

        #region Scénarios de communication ciblée

        [Test]
        public void CommandModuleEvent_InheritanceFromModuleEventBase_ShouldSupportTargetedCommunication()
        {
            // Arrange - Scénario : Communication ciblée vers un module spécifique
            var sourceModule = "CommandModule";
            var commandName = "RequestDataRefresh";
            var parameter = new { DataType = "ClipboardHistory", Force = true };
            var result = new { RefreshRequested = true };
            var executionTime = TimeSpan.FromMilliseconds(50);

            // Act
            var moduleEvent = new CommandModuleEvent(sourceModule, commandName, parameter, result, executionTime);

            // Assert - Vérifier que l'héritage de ModuleEventBase fonctionne
            Assert.That(moduleEvent, Is.InstanceOf<ModuleEventBase>());
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CommandModule"));
            Assert.That(moduleEvent.TargetModule, Is.Null); // Par défaut null pour broadcast
            Assert.That(moduleEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(moduleEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
            
            // Vérifier les propriétés spécifiques à CommandModuleEvent
            Assert.That(moduleEvent.CommandName, Is.EqualTo("RequestDataRefresh"));
            Assert.That(moduleEvent.Parameter, Is.Not.Null);
            Assert.That(moduleEvent.Result, Is.Not.Null);
            Assert.That(moduleEvent.ExecutionTime, Is.EqualTo(TimeSpan.FromMilliseconds(50)));
        }

        #endregion
    }
}
