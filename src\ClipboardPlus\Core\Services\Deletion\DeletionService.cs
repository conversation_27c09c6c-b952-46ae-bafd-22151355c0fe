using System.Collections.ObjectModel;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Diagnostics;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Service orchestrateur principal pour la suppression d'éléments du presse-papiers.
    /// Coordonne toutes les étapes de la suppression avec une complexité cyclomatique cible de 3.
    /// </summary>
    public class DeletionService : IDeletionService
    {
        private readonly IClipboardHistoryManager _historyManager;
        private readonly IDeletionUIValidator _validator;
        private readonly IDeletionUIHandler _uiHandler;
        private readonly IDeletionUINotificationService _notificationService;
        private readonly IDeletionDiagnostic _diagnosticService;
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du service de suppression.
        /// </summary>
        public DeletionService(
            IClipboardHistoryManager historyManager,
            IDeletionUIValidator validator,
            IDeletionUIHandler uiHandler,
            IDeletionUINotificationService notificationService,
            IDeletionDiagnostic diagnosticService,
            ILoggingService? loggingService = null)
        {
            _historyManager = historyManager ?? throw new ArgumentNullException(nameof(historyManager));
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _uiHandler = uiHandler ?? throw new ArgumentNullException(nameof(uiHandler));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _diagnosticService = diagnosticService ?? throw new ArgumentNullException(nameof(diagnosticService));
            _loggingService = loggingService;
        }

        /// <summary>
        /// Supprime un élément du presse-papiers de manière asynchrone.
        /// Gère TOUTE la logique y compris IsOperationInProgress.
        /// Complexité cyclomatique : 1 (VRAIE architecture SOLID)
        /// </summary>
        public async Task<DeletionResult> DeleteItemAsync(ClipboardItem? item, string operationId, ObservableCollection<ClipboardItem> uiCollection, IViewModelOperationState viewModelState)
        {
            _loggingService?.LogDebug($"[{operationId}] Début suppression élément");

            try
            {
                // Validation complète AVANT de définir l'état d'opération
                var validationResult = ValidateAll(item, viewModelState.IsOperationInProgress, operationId);
                if (!validationResult.IsValid)
                {
                    return DeletionResult.CreateFailure(validationResult.ErrorMessage, operationId, item);
                }

                // Gestion complète de l'état d'opération APRÈS validation réussie
                viewModelState.IsOperationInProgress = true;

                // Diagnostic de début (cast sécurisé vers ClipboardHistoryViewModel si nécessaire)
                if (_diagnosticService != null && viewModelState is ClipboardHistoryViewModel viewModel)
                {
                    _diagnosticService.LogDeletionStart_V2(item, viewModel, $"SupprimerElement [{operationId}]");
                }

                // Suppression UI et persistance
                var uiRemovalInfo = _uiHandler.RemoveFromUI(uiCollection, item!);
                var persistenceResult = await TryDeleteFromPersistenceAsync(item!, operationId);

                // Gestion du résultat final
                if (persistenceResult.Success)
                {
                    _notificationService.NotifySuccess(persistenceResult.ItemName, operationId);
                    return persistenceResult;
                }
                else
                {
                    // Rollback UI en cas d'échec de persistance
                    _uiHandler.RollbackUI(uiCollection, uiRemovalInfo);
                    _notificationService.NotifyError(persistenceResult.ItemName, persistenceResult.ErrorMessage, operationId);
                    _notificationService.NotifyRollback(persistenceResult.ItemName, operationId);
                    return DeletionResult.CreateFailure(persistenceResult.ErrorMessage, operationId, item, persistenceResult.OriginalException, true);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Exception dans DeletionService : {ex.Message}", ex);
                _diagnosticService?.LogDeletionException(ex, "DeletionService");
                return DeletionResult.CreateFailure($"Erreur lors de la suppression : {ex.Message}", operationId, item, ex);
            }
            finally
            {
                // Toujours remettre IsOperationInProgress à false
                viewModelState.IsOperationInProgress = false;
            }

            // Complexité cyclomatique totale : 1 ✅ VRAIE ARCHITECTURE SOLID
        }

        /// <summary>
        /// Valide tous les aspects nécessaires pour la suppression.
        /// Méthode privée pour encapsuler la logique de validation.
        /// </summary>
        private UIValidationResult ValidateAll(ClipboardItem? item, bool isOperationInProgress, string operationId)
        {
            // Validation de l'élément
            var itemValidation = _validator.ValidateItem(item);
            if (!itemValidation.IsValid)
            {
                return itemValidation;
            }

            // Validation de l'état d'opération
            var operationValidation = _validator.ValidateOperationState(isOperationInProgress);
            if (!operationValidation.IsValid)
            {
                return operationValidation;
            }

            // Validation du gestionnaire d'historique
            var managerValidation = _validator.ValidateHistoryManager(_historyManager);
            if (!managerValidation.IsValid)
            {
                return managerValidation;
            }

            return UIValidationResult.Success();
        }

        /// <summary>
        /// Tente de supprimer l'élément de la persistance avec gestion d'erreurs.
        /// Méthode privée pour encapsuler la logique de persistance.
        /// </summary>
        private async Task<DeletionResult> TryDeleteFromPersistenceAsync(ClipboardItem item, string operationId)
        {
            try
            {
                _loggingService?.LogInfo($"[{operationId}] Suppression persistance élément ID={item.Id}");
                
                var success = await _historyManager.DeleteItemAsync(item.Id);
                
                if (success)
                {
                    _loggingService?.LogInfo($"[{operationId}] Suppression persistance réussie ID={item.Id}");
                    return DeletionResult.CreateSuccess(item, operationId);
                }
                else
                {
                    _loggingService?.LogWarning($"[{operationId}] Échec suppression persistance ID={item.Id}");
                    return DeletionResult.CreateFailure("Échec de la suppression en base de données", operationId, item);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Exception suppression persistance ID={item.Id} : {ex.Message}", ex);
                
                // Diagnostic d'urgence
                try
                {
                    _diagnosticService.LogDeletionException(ex, "DeletionService.TryDeleteFromPersistenceAsync");
                }
                catch (Exception diagnosticEx)
                {
                    _loggingService?.LogError($"[{operationId}] Erreur diagnostic d'urgence : {diagnosticEx.Message}", diagnosticEx);
                }
                
                return DeletionResult.CreateFailure($"Erreur lors de la suppression : {ex.Message}", operationId, item, ex);
            }
        }
    }
}
