using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service responsable de la logique métier de renommage des éléments du presse-papiers.
    /// </summary>
    public interface IRenameService
    {
        /// <summary>
        /// Renomme un élément du presse-papiers de manière asynchrone.
        /// </summary>
        /// <param name="item">L'élément à renommer</param>
        /// <param name="newName">Le nouveau nom à attribuer</param>
        /// <returns>Le résultat de l'opération de renommage</returns>
        Task<RenameResult> RenameItemAsync(ClipboardItem item, string newName);
    }

    /// <summary>
    /// Résultat d'une opération de renommage.
    /// </summary>
    public class RenameResult
    {
        /// <summary>
        /// Indique si l'opération de renommage a réussi.
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Message d'erreur en cas d'échec (null si succès).
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// L'élément mis à jour après renommage (null si échec).
        /// </summary>
        public ClipboardItem? UpdatedItem { get; set; }

        /// <summary>
        /// L'ancien nom de l'élément avant renommage.
        /// </summary>
        public string? OldName { get; set; }

        /// <summary>
        /// Le nouveau nom attribué à l'élément.
        /// </summary>
        public string? NewName { get; set; }

        /// <summary>
        /// Crée un résultat de succès.
        /// </summary>
        public static RenameResult CreateSuccess(ClipboardItem updatedItem, string? oldName, string? newName)
        {
            return new RenameResult
            {
                Success = true,
                UpdatedItem = updatedItem,
                OldName = oldName,
                NewName = newName
            };
        }

        /// <summary>
        /// Crée un résultat d'échec.
        /// </summary>
        public static RenameResult CreateFailure(string errorMessage, string? oldName = null, string? newName = null)
        {
            return new RenameResult
            {
                Success = false,
                ErrorMessage = errorMessage,
                OldName = oldName,
                NewName = newName
            };
        }
    }
}
