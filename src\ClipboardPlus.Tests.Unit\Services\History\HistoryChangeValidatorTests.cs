#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.

using System;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.History
{
    /// <summary>
    /// Tests unitaires pour HistoryChangeValidator.
    /// </summary>
    [TestFixture]
    public class HistoryChangeValidatorTests
    {
        private Mock<ILoggingService>? _mockLoggingService;
        private Mock<IClipboardHistoryManager>? _mockHistoryManager;
        private HistoryChangeValidator? _validator;
        private HistoryChangeContext? _validContext;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _validator = new HistoryChangeValidator(_mockLoggingService.Object);

            // Créer un contexte valide par défaut
            _validContext = new HistoryChangeContext
            {
                PreventReaction = false,
                IsUpdatingItem = false,
                IsReorderingItems = false,
                HistoryManager = _mockHistoryManager.Object,
                IsOperationInProgress = false,
                IsItemPasteInProgress = false
            };

            // Configurer le mock pour retourner une collection valide
            _mockHistoryManager.Setup(x => x.HistoryItems).Returns(new List<ClipboardItem>());
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new HistoryChangeValidator(null!));
        }

        [Test]
        public void Constructor_WithValidLoggingService_ShouldCreateInstance()
        {
            // Act
            var validator = new HistoryChangeValidator(_mockLoggingService!.Object);

            // Assert
            Assert.That(validator, Is.Not.Null);
        }

        [Test]
        public void ValidateHistoryChange_WithNullContext_ShouldReturnRejected()
        {
            // Act
            var result = _validator!.ValidateHistoryChange(null);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.Reason, Is.EqualTo("Le contexte de validation est null"));
            _mockLoggingService!.Verify(x => x.LogWarning(It.Is<string>(s => 
                s.Contains("Le contexte de validation est null"))), Times.Once);
        }

        [Test]
        public void ValidateHistoryChange_WithValidContext_ShouldReturnAccepted()
        {
            // Act
            var result = _validator!.ValidateHistoryChange(_validContext!);

            // Assert
            Assert.That(result.IsValid, Is.True);
            Assert.That(result.Reason, Is.Null);
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("Validation réussie"))), Times.Once);
        }

        [Test]
        public void ValidateHistoryChange_WithPreventReactionTrue_ShouldReturnRejected()
        {
            // Arrange
            _validContext!.PreventReaction = true;

            // Act
            var result = _validator!.ValidateHistoryChange(_validContext);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.Reason, Is.EqualTo("_preventHistoryChangedReaction est true"));
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("_preventHistoryChangedReaction est true"))), Times.Once);
        }

        [Test]
        public void ValidateHistoryChange_WithIsUpdatingItemTrue_ShouldReturnRejected()
        {
            // Arrange
            _validContext!.IsUpdatingItem = true;

            // Act
            var result = _validator!.ValidateHistoryChange(_validContext);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.Reason, Is.EqualTo("_isUpdatingItem est true"));
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("_isUpdatingItem est true"))), Times.Once);
        }

        [Test]
        public void ValidateHistoryChange_WithIsReorderingItemsTrue_ShouldReturnRejected()
        {
            // Arrange
            _validContext!.IsReorderingItems = true;

            // Act
            var result = _validator!.ValidateHistoryChange(_validContext);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.Reason, Is.EqualTo("_isReorderingItems est true"));
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("_isReorderingItems est true"))), Times.Once);
        }

        [Test]
        public void ValidateHistoryChange_WithNullHistoryManager_ShouldReturnRejected()
        {
            // Arrange
            _validContext!.HistoryManager = null;

            // Act
            var result = _validator!.ValidateHistoryChange(_validContext);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.Reason, Is.EqualTo("_clipboardHistoryManager est null"));
            _mockLoggingService!.Verify(x => x.LogWarning(It.Is<string>(s => 
                s.Contains("_clipboardHistoryManager est null"))), Times.Once);
        }

        [Test]
        public void ValidateHistoryChange_WithIsOperationInProgressTrue_ShouldReturnRejected()
        {
            // Arrange
            _validContext!.IsOperationInProgress = true;

            // Act
            var result = _validator!.ValidateHistoryChange(_validContext);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.Reason, Is.EqualTo("Une opération est en cours (IsOperationInProgress)"));
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("Une opération est en cours (IsOperationInProgress)"))), Times.Once);
        }

        [Test]
        public void ValidateHistoryChange_WithIsItemPasteInProgressTrue_ShouldReturnRejected()
        {
            // Arrange
            _validContext!.IsItemPasteInProgress = true;

            // Act
            var result = _validator!.ValidateHistoryChange(_validContext);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.Reason, Is.EqualTo("Une opération est en cours (_isItemPasteInProgress)"));
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("Une opération est en cours (_isItemPasteInProgress)"))), Times.Once);
        }

        [Test]
        public void ValidateHistoryChange_WithNullHistoryItems_ShouldReturnRejected()
        {
            // Arrange
            _mockHistoryManager!.Setup(x => x.HistoryItems).Returns((List<ClipboardItem>?)null!);

            // Act
            var result = _validator!.ValidateHistoryChange(_validContext!);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.Reason, Is.EqualTo("_clipboardHistoryManager.HistoryItems est null"));
            _mockLoggingService!.Verify(x => x.LogWarning(It.Is<string>(s => 
                s.Contains("_clipboardHistoryManager.HistoryItems est null"))), Times.Once);
        }

        [Test]
        public void ValidateHistoryChange_WithMultipleInvalidConditions_ShouldReturnFirstRejection()
        {
            // Arrange - Plusieurs conditions invalides
            _validContext!.PreventReaction = true;
            _validContext.IsUpdatingItem = true;
            _validContext.HistoryManager = null;

            // Act
            var result = _validator!.ValidateHistoryChange(_validContext);

            // Assert - Devrait retourner la première condition échouée
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.Reason, Is.EqualTo("_preventHistoryChangedReaction est true"));
        }

        [Test]
        public void ValidateHistoryChange_ShouldValidateInCorrectOrder()
        {
            // Test que les validations sont effectuées dans l'ordre correct
            // 1. PreventReaction
            // 2. IsUpdatingItem/IsReorderingItems  
            // 3. HistoryManager null
            // 4. Operations in progress
            // 5. HistoryItems null

            // Test ordre 1 vs 2
            _validContext!.PreventReaction = true;
            _validContext.IsUpdatingItem = true;
            var result = _validator!.ValidateHistoryChange(_validContext);
            Assert.That(result.Reason, Is.EqualTo("_preventHistoryChangedReaction est true"));

            // Reset et test ordre 2 vs 3
            _validContext.PreventReaction = false;
            _validContext.HistoryManager = null;
            result = _validator!.ValidateHistoryChange(_validContext);
            Assert.That(result.Reason, Is.EqualTo("_isUpdatingItem est true"));
        }

        [Test]
        public void ValidateHistoryChange_WithEdgeCaseContext_ShouldHandleGracefully()
        {
            // Arrange - Contexte avec valeurs par défaut
            var edgeCaseContext = new HistoryChangeContext
            {
                HistoryManager = _mockHistoryManager!.Object
            };

            // Act
            var result = _validator!.ValidateHistoryChange(edgeCaseContext);

            // Assert
            Assert.That(result.IsValid, Is.True);
        }

        [Test]
        public void ValidateHistoryChange_ShouldLogAppropriateLevel()
        {
            // Test que les logs Warning sont utilisés pour les erreurs critiques
            // et Info pour les rejections normales

            // Test Warning pour contexte null
            _validator!.ValidateHistoryChange(null);
            _mockLoggingService!.Verify(x => x.LogWarning(It.IsAny<string>()), Times.Once);

            // Reset mock
            _mockLoggingService.Reset();

            // Test Info pour condition normale
            _validContext!.PreventReaction = true;
            _validator.ValidateHistoryChange(_validContext);
            _mockLoggingService.Verify(x => x.LogInfo(It.IsAny<string>()), Times.Once);
            _mockLoggingService.Verify(x => x.LogWarning(It.IsAny<string>()), Times.Never);
        }

        [Test]
        public void ValidationResult_ShouldHaveCorrectProperties()
        {
            // Test ValidationResult.Accepted
            var acceptedResult = ValidationResult.Accepted();
            Assert.That(acceptedResult.IsValid, Is.True);
            Assert.That(acceptedResult.Reason, Is.Null);

            // Test ValidationResult.Rejected
            var rejectedResult = ValidationResult.Rejected("Test reason");
            Assert.That(rejectedResult.IsValid, Is.False);
            Assert.That(rejectedResult.Reason, Is.EqualTo("Test reason"));
        }
    }
}
