using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services.ContentHandlers
{
    /// <summary>
    /// Handler pour le contenu HTML.
    /// Remplace la logique LoadHtmlContent() du ContentPreviewViewModel.
    /// </summary>
    public class HtmlContentHandler : BaseContentHandler
    {
        /// <summary>
        /// Initialise une nouvelle instance de HtmlContentHandler.
        /// </summary>
        /// <param name="loggingService">Service de logging optionnel</param>
        public HtmlContentHandler(ILoggingService? loggingService = null) 
            : base(loggingService)
        {
        }

        /// <inheritdoc />
        public override ClipboardDataType SupportedDataType => ClipboardDataType.Html;

        /// <inheritdoc />
        public override string GetDefaultUnavailableMessage()
        {
            return "Contenu HTML non disponible.";
        }

        /// <inheritdoc />
        protected override object HandleContentInternal(ClipboardItem item)
        {
            _loggingService?.LogInfo($"HtmlContentHandler.HandleContentInternal - Traitement du contenu HTML pour l'élément ID {item.Id}");

            // Priorité 1: Utiliser RawData si disponible
            if (item.RawData != null && item.RawData.Length > 0)
            {
                _loggingService?.LogInfo($"HtmlContentHandler - Données brutes disponibles, taille: {item.RawData.Length} octets");
                
                string? decodedHtml = DecodeRawDataAsText(item.RawData);
                if (decodedHtml != null)
                {
                    _loggingService?.LogInfo($"HtmlContentHandler - HTML décodé avec succès, longueur: {decodedHtml.Length} caractères");
                    
                    // Log d'un aperçu du HTML pour le débogage (limité pour éviter les logs trop longs)
                    string htmlPreview = decodedHtml.Length <= 200 ? decodedHtml : decodedHtml.Substring(0, 200) + "...";
                    _loggingService?.LogInfo($"HtmlContentHandler - Aperçu du HTML: '{htmlPreview}'");
                    
                    return decodedHtml;
                }
                else
                {
                    _loggingService?.LogWarning("HtmlContentHandler - Échec du décodage des données brutes, utilisation du TextPreview");
                }
            }
            else
            {
                _loggingService?.LogWarning("HtmlContentHandler - Données brutes non disponibles, utilisation du TextPreview");
            }

            // Priorité 2: Utiliser TextPreview si RawData n'est pas disponible
            if (!string.IsNullOrEmpty(item.TextPreview))
            {
                _loggingService?.LogInfo($"HtmlContentHandler - Utilisation du TextPreview: '{item.TextPreview}'");
                return item.TextPreview;
            }

            // Priorité 3: Message par défaut si aucune donnée n'est disponible
            string defaultMessage = GetDefaultUnavailableMessage();
            _loggingService?.LogInfo($"HtmlContentHandler - Aucune donnée disponible, utilisation du message par défaut: '{defaultMessage}'");
            
            return defaultMessage;
        }
    }
}
