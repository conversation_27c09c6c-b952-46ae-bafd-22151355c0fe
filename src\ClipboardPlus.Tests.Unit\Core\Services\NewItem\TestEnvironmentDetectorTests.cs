using System;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.NewItem.Implementations;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services.NewItem
{
    /// <summary>
    /// Tests unitaires pour TestEnvironmentDetector.
    /// Ces tests valident que la nouvelle implémentation fonctionne correctement
    /// et maintient la compatibilité avec l'ancienne logique.
    /// </summary>
    [TestFixture]
    [Category("Unit")]
    [Category("TestEnvironmentDetector")]
    public class TestEnvironmentDetectorTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private TestEnvironmentDetector _detector = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _detector = new TestEnvironmentDetector(_mockLoggingService.Object);
        }

        [Test]
        [Description("TestEnvironmentDetector retourne true dans un contexte de test NUnit")]
        public void IsInTestEnvironment_InNUnitContext_ReturnsTrue()
        {
            // Act
            var result = _detector.IsInTestEnvironment();

            // Assert
            Assert.That(result, Is.True, "TestEnvironmentDetector devrait retourner true dans un contexte de test NUnit");
        }

        [Test]
        [Description("TestEnvironmentDetector est cohérent entre plusieurs appels")]
        public void IsInTestEnvironment_MultipleCallsInSameContext_ReturnsConsistentResult()
        {
            // Act
            var result1 = _detector.IsInTestEnvironment();
            var result2 = _detector.IsInTestEnvironment();
            var result3 = _detector.IsInTestEnvironment();

            // Assert
            Assert.That(result1, Is.EqualTo(result2), "Les appels multiples devraient retourner le même résultat");
            Assert.That(result2, Is.EqualTo(result3), "Les appels multiples devraient retourner le même résultat");
        }

        [Test]
        [Description("TestEnvironmentDetector fonctionne sans service de logging")]
        public void IsInTestEnvironment_WithoutLoggingService_Works()
        {
            // Arrange
            var detectorWithoutLogging = new TestEnvironmentDetector(null);

            // Act
            var result = detectorWithoutLogging.IsInTestEnvironment();

            // Assert
            Assert.That(result, Is.TypeOf<bool>(), "Devrait retourner un booléen même sans logging");
            // Dans un contexte de test, on s'attend à true
            Assert.That(result, Is.True, "Devrait détecter l'environnement de test même sans logging");
        }

        [Test]
        [Description("TestEnvironmentDetector gère les exceptions gracieusement")]
        public void IsInTestEnvironment_WithExceptionInLogging_ReturnsResult()
        {
            // Arrange
            _mockLoggingService.Setup(x => x.LogInfo(It.IsAny<string>()))
                              .Throws(new Exception("Logging error"));

            // Act & Assert - Ne devrait pas lever d'exception
            Assert.DoesNotThrow(() => _detector.IsInTestEnvironment());
        }

        [Test]
        [Description("TestEnvironmentDetector performance acceptable")]
        public void IsInTestEnvironment_PerformanceTest()
        {
            // Arrange
            const int iterations = 100;

            // Act
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            for (int i = 0; i < iterations; i++)
            {
                _detector.IsInTestEnvironment();
            }
            stopwatch.Stop();

            // Assert
            var averageMs = stopwatch.ElapsedMilliseconds / (double)iterations;
            TestContext.WriteLine($"Performance TestEnvironmentDetector: {averageMs:F2}ms par appel sur {iterations} itérations");
            
            // Devrait être rapide
            Assert.That(averageMs, Is.LessThan(10.0), "TestEnvironmentDetector devrait être rapide");
        }

        [Test]
        [Description("Interface ITestEnvironmentDetector est correctement implémentée")]
        public void TestEnvironmentDetector_ImplementsInterface_Correctly()
        {
            // Act & Assert
            Assert.That(_detector, Is.InstanceOf<ITestEnvironmentDetector>(), 
                "TestEnvironmentDetector devrait implémenter ITestEnvironmentDetector");
        }

        [Test]
        [Description("TestEnvironmentDetector avec différentes instances")]
        public void IsInTestEnvironment_WithDifferentInstances_ConsistentBehavior()
        {
            // Arrange
            var detector2 = new TestEnvironmentDetector(_mockLoggingService.Object);
            var detector3 = new TestEnvironmentDetector(null);

            // Act
            var result1 = _detector.IsInTestEnvironment();
            var result2 = detector2.IsInTestEnvironment();
            var result3 = detector3.IsInTestEnvironment();

            // Assert
            Assert.That(result1, Is.EqualTo(result2), "Différentes instances avec logging devraient avoir le même comportement");
            Assert.That(result2, Is.EqualTo(result3), "Instances avec et sans logging devraient avoir le même comportement");
        }
    }
}
