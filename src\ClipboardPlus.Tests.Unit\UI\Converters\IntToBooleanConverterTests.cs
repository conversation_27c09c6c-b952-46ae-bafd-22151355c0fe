using System;
using System.Globalization;
using NUnit.Framework;
using ClipboardPlus.UI.Converters;

namespace ClipboardPlus.Tests.Unit.UI.Converters
{
    [TestFixture]
    public class IntToBooleanConverterTests
    {
        private IntToBooleanConverter _converter = null!;

        [SetUp]
        public void Initialize()
        {
            _converter = new IntToBooleanConverter();
        }

        [Test]
        public void Convert_WithPositiveIntValue_ReturnsTrue()
        {
            // Arrange
            object value = 1;
            Type targetType = typeof(bool);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            #pragma warning disable CS8604 // Possible null reference argument
            var result = _converter.Convert(value, targetType, parameter, culture);
            #pragma warning restore CS8604

            // Assert
            Assert.That(result, Is.EqualTo(true));
        }

        [Test]
        public void Convert_WithZeroIntValue_ReturnsFalse()
        {
            // Arrange
            object value = 0;
            Type targetType = typeof(bool);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            #pragma warning disable CS8604 // Possible null reference argument
            var result = _converter.Convert(value, targetType, parameter, culture);
            #pragma warning restore CS8604

            // Assert
            Assert.That(result, Is.EqualTo(false));
        }

        [Test]
        public void Convert_WithNegativeIntValue_ReturnsFalse()
        {
            // Arrange
            object value = -1;
            Type targetType = typeof(bool);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            #pragma warning disable CS8604 // Possible null reference argument
            var result = _converter.Convert(value, targetType, parameter, culture);
            #pragma warning restore CS8604

            // Assert
            Assert.That(result, Is.EqualTo(false));
        }

        [Test]
        public void Convert_WithPositiveLongValue_ReturnsTrue()
        {
            // Arrange
            object value = (long)100;
            Type targetType = typeof(bool);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            #pragma warning disable CS8604 // Possible null reference argument
            var result = _converter.Convert(value, targetType, parameter, culture);
            #pragma warning restore CS8604

            // Assert
            Assert.That(result, Is.EqualTo(true));
        }

        [Test]
        public void Convert_WithZeroLongValue_ReturnsFalse()
        {
            // Arrange
            object value = (long)0;
            Type targetType = typeof(bool);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            #pragma warning disable CS8604 // Possible null reference argument
            var result = _converter.Convert(value, targetType, parameter, culture);
            #pragma warning restore CS8604

            // Assert
            Assert.That(result, Is.EqualTo(false));
        }

        [Test]
        public void Convert_WithNegativeLongValue_ReturnsFalse()
        {
            // Arrange
            object value = (long)-100;
            Type targetType = typeof(bool);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            #pragma warning disable CS8604 // Possible null reference argument
            var result = _converter.Convert(value, targetType, parameter, culture);
            #pragma warning restore CS8604

            // Assert
            Assert.That(result, Is.EqualTo(false));
        }

        [Test]
        public void Convert_WithNonNumericValue_ReturnsFalse()
        {
            // Arrange
            object value = "NotAnInteger";
            Type targetType = typeof(bool);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            #pragma warning disable CS8604 // Possible null reference argument
            var result = _converter.Convert(value, targetType, parameter, culture);
            #pragma warning restore CS8604

            // Assert
            Assert.That(result, Is.EqualTo(false));
        }

        [Test]
        public void ConvertBack_ThrowsNotImplementedException()
        {
            // Arrange
            object value = true;
            Type targetType = typeof(int);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act & Assert
            #pragma warning disable CS8604 // Possible null reference argument
            Assert.Throws<NotImplementedException>(() => _converter.ConvertBack(value, targetType, parameter, culture));
            #pragma warning restore CS8604
        }
    }
} 