using System;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Core.Services.Deletion
{
    /// <summary>
    /// Tests unitaires pour DeletionNotificationService basés sur le VRAI code source
    /// </summary>
    [TestFixture]
    public class DeletionNotificationServiceTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<Action> _mockHistoryChangedCallback = null!;
        private DeletionNotificationService _notificationService = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockHistoryChangedCallback = new Mock<Action>();
            _notificationService = new DeletionNotificationService(_mockLoggingService.Object, _mockHistoryChangedCallback.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new DeletionNotificationService(null!, _mockHistoryChangedCallback.Object));
        }

        [Test]
        public void Constructor_WithNullCallback_ShouldNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new DeletionNotificationService(_mockLoggingService.Object, null));
        }

        [Test]
        public async Task NotifyAsync_WithSuccessfulGlobalResult_ShouldReturnSuccessAndTriggerNotifications()
        {
            // Arrange
            var testItem = new ClipboardItem { Id = 123, TextPreview = "Test" };
            var memoryResult = MemoryDeletionResult.CreateSuccess(true, testItem, "Supprimé de la mémoire");
            var databaseResult = DatabaseDeletionResult.CreateSuccess(1, false, TimeSpan.FromMilliseconds(100), new[] { "Succès BDD" });
            var consistency = new ConsistencyAnalysis { IsConsistent = true, Message = "Cohérent" };
            var globalResult = GlobalDeletionResult.CreateSuccess(memoryResult, databaseResult, consistency, "Test succès", true);
            string operationId = "notify-test";

            // Act
            var result = await _notificationService.NotifyAsync(globalResult, operationId);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.HistoryChangedTriggered, Is.True);
            Assert.That(result.DetailsLogged, Is.True);
            Assert.That(result.Messages.Length, Is.GreaterThan(0));
            Assert.That(result.Errors.Length, Is.EqualTo(0));

            // Verify callback was invoked
            _mockHistoryChangedCallback.Verify(x => x.Invoke(), Times.Once);

            // Verify logging
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(operationId) && s.Contains("Début des notifications"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(operationId) && s.Contains("Notifications terminées"))), Times.Once);
        }

        [Test]
        public async Task NotifyAsync_WithShouldNotifyFalse_ShouldNotTriggerHistoryChanged()
        {
            // Arrange
            var memoryResult = MemoryDeletionResult.CreateFailure("Échec mémoire");
            var databaseResult = DatabaseDeletionResult.CreateFailure(3, null, TimeSpan.FromMilliseconds(300), new[] { "Échec" });
            var consistency = new ConsistencyAnalysis { IsConsistent = true };
            var globalResult = GlobalDeletionResult.CreateFailure(memoryResult, databaseResult, consistency, "Test échec", false);
            string operationId = "no-notify-test";

            // Act
            var result = await _notificationService.NotifyAsync(globalResult, operationId);

            // Assert
            Assert.That(result.Success, Is.True); // Succès car pas d'erreur, juste pas de notification
            Assert.That(result.HistoryChangedTriggered, Is.False);
            Assert.That(result.DetailsLogged, Is.True);

            // Verify callback was NOT invoked
            _mockHistoryChangedCallback.Verify(x => x.Invoke(), Times.Never);

            // Verify appropriate logging
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(operationId) && s.Contains("shouldNotify = false"))), Times.Once);
        }

        [Test]
        public async Task TriggerHistoryChangedEventAsync_WithShouldNotifyTrue_ShouldInvokeCallback()
        {
            // Arrange
            string operationId = "trigger-test";

            // Act
            var result = await _notificationService.TriggerHistoryChangedEventAsync(true, operationId);

            // Assert
            Assert.That(result, Is.True);
            _mockHistoryChangedCallback.Verify(x => x.Invoke(), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("déclenché avec succès"))), Times.Once);
        }

        [Test]
        public async Task TriggerHistoryChangedEventAsync_WithShouldNotifyFalse_ShouldNotInvokeCallback()
        {
            // Arrange
            string operationId = "no-trigger-test";

            // Act
            var result = await _notificationService.TriggerHistoryChangedEventAsync(false, operationId);

            // Assert
            Assert.That(result, Is.False);
            _mockHistoryChangedCallback.Verify(x => x.Invoke(), Times.Never);
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(operationId) && s.Contains("shouldNotify = false"))), Times.Once);
        }

        [Test]
        public async Task TriggerHistoryChangedEventAsync_WithNullCallback_ShouldReturnFalse()
        {
            // Arrange
            var serviceWithNullCallback = new DeletionNotificationService(_mockLoggingService.Object, null);
            string operationId = "null-callback-test";

            // Act
            var result = await serviceWithNullCallback.TriggerHistoryChangedEventAsync(true, operationId);

            // Assert
            Assert.That(result, Is.False);
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(operationId) && s.Contains("callback null"))), Times.Once);
        }

        [Test]
        public void TriggerHistoryChangedEventAsync_WithCallbackException_ShouldThrowAndLog()
        {
            // Arrange
            var exception = new InvalidOperationException("Test callback error");
            _mockHistoryChangedCallback.Setup(x => x.Invoke()).Throws(exception);
            string operationId = "exception-test";

            // Act & Assert
            var thrownException = Assert.ThrowsAsync<InvalidOperationException>(
                async () => await _notificationService.TriggerHistoryChangedEventAsync(true, operationId));

            Assert.That(thrownException, Is.EqualTo(exception));
            _mockLoggingService.Verify(x => x.LogError(It.Is<string>(s => s.Contains(operationId) && s.Contains("Erreur lors du déclenchement")), exception), Times.Once);
        }

        [Test]
        public async Task LogDeletionDetailsAsync_WithSuccessfulResult_ShouldLogInfoMessages()
        {
            // Arrange
            var testItem = new ClipboardItem { Id = 789, TextPreview = "Test Item" };
            var memoryResult = MemoryDeletionResult.CreateSuccess(true, testItem, "Mémoire OK");
            var databaseResult = DatabaseDeletionResult.CreateSuccess(2, true, TimeSpan.FromMilliseconds(250), new[] { "BDD OK" });
            var consistency = new ConsistencyAnalysis { IsConsistent = true, Message = "Tout cohérent" };
            var globalResult = GlobalDeletionResult.CreateSuccess(memoryResult, databaseResult, consistency, "Succès global", true);
            string operationId = "log-success-test";

            // Act
            await _notificationService.LogDeletionDetailsAsync(globalResult, operationId);

            // Assert
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("=== DÉTAILS SUPPRESSION ==="))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("Résultat global: SUCCÈS"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("Message: Succès global"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("Mémoire - Succès: True"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("BDD - Succès: True"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("Cohérence: OK"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("Notification requise: True"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("=== FIN DÉTAILS ==="))), Times.Once);
        }

        [Test]
        public async Task LogDeletionDetailsAsync_WithFailedResult_ShouldLogWarningMessages()
        {
            // Arrange
            var memoryResult = MemoryDeletionResult.CreateFailure("Échec mémoire");
            var databaseResult = DatabaseDeletionResult.CreateFailure(3, new Exception("Erreur BDD"), TimeSpan.FromMilliseconds(500), new[] { "Échec BDD" });
            var consistency = new ConsistencyAnalysis { IsConsistent = false, Message = "Incohérent" };
            var globalResult = GlobalDeletionResult.CreateFailure(memoryResult, databaseResult, consistency, "Échec global", false);
            string operationId = "log-failure-test";

            // Act
            await _notificationService.LogDeletionDetailsAsync(globalResult, operationId);

            // Assert
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(operationId) && s.Contains("=== DÉTAILS SUPPRESSION ==="))), Times.Once);
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(operationId) && s.Contains("Résultat global: ÉCHEC"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(operationId) && s.Contains("Message: Échec global"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(operationId) && s.Contains("Mémoire - Succès: False"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(operationId) && s.Contains("BDD - Succès: False"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(operationId) && s.Contains("Cohérence: PROBLÈME"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(operationId) && s.Contains("Notification requise: False"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(operationId) && s.Contains("=== FIN DÉTAILS ==="))), Times.Once);
        }

        [Test]
        public async Task NotifyAsync_WithExceptionInLogging_ShouldHandleGracefully()
        {
            // Arrange
            var exception = new InvalidOperationException("Logging error");
            _mockLoggingService.Setup(x => x.LogInfo(It.IsAny<string>())).Throws(exception);

            var testItem = new ClipboardItem { Id = 111, TextPreview = "Test" };
            var memoryResult = MemoryDeletionResult.CreateSuccess(true, testItem);
            var databaseResult = DatabaseDeletionResult.CreateSuccess(1, false, TimeSpan.FromMilliseconds(100), new[] { "OK" });
            var consistency = new ConsistencyAnalysis { IsConsistent = true };
            var globalResult = GlobalDeletionResult.CreateSuccess(memoryResult, databaseResult, consistency, "Test", true);
            string operationId = "exception-logging-test";

            // Act
            var result = await _notificationService.NotifyAsync(globalResult, operationId);

            // Assert
            Assert.That(result.Success, Is.False); // Échec à cause de l'exception
            Assert.That(result.Errors.Length, Is.GreaterThan(0));
            Assert.That(result.Errors[0], Does.Contain("Erreur lors du logging des détails"));

            // Verify error was logged
            _mockLoggingService.Verify(x => x.LogError(It.Is<string>(s => s.Contains(operationId) && s.Contains("Erreur logging détails")), exception), Times.Once);
        }

        [Test]
        public async Task NotifyAsync_WithExceptionInHistoryChanged_ShouldHandleGracefully()
        {
            // Arrange
            var exception = new InvalidOperationException("HistoryChanged error");
            _mockHistoryChangedCallback.Setup(x => x.Invoke()).Throws(exception);

            var testItem = new ClipboardItem { Id = 222, TextPreview = "Test2" };
            var memoryResult = MemoryDeletionResult.CreateSuccess(true, testItem);
            var databaseResult = DatabaseDeletionResult.CreateSuccess(1, false, TimeSpan.FromMilliseconds(100), new[] { "OK" });
            var consistency = new ConsistencyAnalysis { IsConsistent = true };
            var globalResult = GlobalDeletionResult.CreateSuccess(memoryResult, databaseResult, consistency, "Test", true);
            string operationId = "exception-history-test";

            // Act
            var result = await _notificationService.NotifyAsync(globalResult, operationId);

            // Assert
            Assert.That(result.Success, Is.False); // Échec à cause de l'exception
            Assert.That(result.Errors.Length, Is.GreaterThan(0));
            Assert.That(result.Errors[0], Does.Contain("Erreur lors du déclenchement HistoryChanged"));

            // Verify error was logged
            _mockLoggingService.Verify(x => x.LogError(It.Is<string>(s => s.Contains(operationId) && s.Contains("Erreur HistoryChanged")), exception), Times.Once);
        }
    }
}
