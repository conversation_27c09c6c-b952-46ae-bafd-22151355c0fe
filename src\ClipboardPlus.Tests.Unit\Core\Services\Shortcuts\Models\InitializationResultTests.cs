using System;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Shortcuts.Models;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services.Shortcuts.Models
{
    [TestFixture]
    public class InitializationResultTests
    {
        private KeyCombination _testShortcut = null!;
        private IntPtr _testHandle;
        private const string TestStrategy = "TestStrategy";

        [SetUp]
        public void SetUp()
        {
            _testShortcut = new KeyCombination(ModifierKeys.Control | ModifierKeys.Alt, Key.V);
            _testHandle = new IntPtr(12345);
        }

        [Test]
        public void Constructor_ForSuccess_SetsPropertiesCorrectly()
        {
            // Act
            var result = new InitializationResult(_testShortcut, _testHandle, TestStrategy);

            // Assert
            Assert.That(result.IsSuccess, Is.True);
            Assert.That(result.RegisteredShortcut, Is.EqualTo(_testShortcut));
            Assert.That(result.WindowHandle, Is.EqualTo(_testHandle));
            Assert.That(result.Strategy, Is.EqualTo(TestStrategy));
            Assert.That(result.ErrorMessage, Is.Null);
            Assert.That(result.Exception, Is.Null);
            Assert.That(result.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
            Assert.That(result.Timestamp, Is.GreaterThan(DateTime.UtcNow.AddSeconds(-1)));
        }

        [Test]
        public void Constructor_ForFailure_SetsPropertiesCorrectly()
        {
            // Arrange
            const string errorMessage = "Test error message";
            var exception = new InvalidOperationException("Test exception");

            // Act
            var result = new InitializationResult(errorMessage, exception);

            // Assert
            Assert.That(result.IsSuccess, Is.False);
            Assert.That(result.ErrorMessage, Is.EqualTo(errorMessage));
            Assert.That(result.Exception, Is.EqualTo(exception));
            Assert.That(result.RegisteredShortcut, Is.Null);
            Assert.That(result.WindowHandle, Is.EqualTo(IntPtr.Zero));
            Assert.That(result.Strategy, Is.EqualTo("Failed"));
            Assert.That(result.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
            Assert.That(result.Timestamp, Is.GreaterThan(DateTime.UtcNow.AddSeconds(-1)));
        }

        [Test]
        public void Constructor_ForFailureWithoutException_SetsPropertiesCorrectly()
        {
            // Arrange
            const string errorMessage = "Test error without exception";

            // Act
            var result = new InitializationResult(errorMessage, null);

            // Assert
            Assert.That(result.IsSuccess, Is.False);
            Assert.That(result.ErrorMessage, Is.EqualTo(errorMessage));
            Assert.That(result.Exception, Is.Null);
            Assert.That(result.RegisteredShortcut, Is.Null);
            Assert.That(result.WindowHandle, Is.EqualTo(IntPtr.Zero));
            Assert.That(result.Strategy, Is.EqualTo("Failed"));
        }

        [Test]
        public void Success_StaticMethod_CreatesSuccessResult()
        {
            // Act
            var result = InitializationResult.Success(_testShortcut, _testHandle, TestStrategy);

            // Assert
            Assert.That(result.IsSuccess, Is.True);
            Assert.That(result.RegisteredShortcut, Is.EqualTo(_testShortcut));
            Assert.That(result.WindowHandle, Is.EqualTo(_testHandle));
            Assert.That(result.Strategy, Is.EqualTo(TestStrategy));
            Assert.That(result.ErrorMessage, Is.Null);
            Assert.That(result.Exception, Is.Null);
        }

        [Test]
        public void Failure_StaticMethod_CreatesFailureResult()
        {
            // Arrange
            const string errorMessage = "Static failure test";
            var exception = new ArgumentException("Static test exception");

            // Act
            var result = InitializationResult.Failure(errorMessage, exception);

            // Assert
            Assert.That(result.IsSuccess, Is.False);
            Assert.That(result.ErrorMessage, Is.EqualTo(errorMessage));
            Assert.That(result.Exception, Is.EqualTo(exception));
            Assert.That(result.RegisteredShortcut, Is.Null);
            Assert.That(result.WindowHandle, Is.EqualTo(IntPtr.Zero));
            Assert.That(result.Strategy, Is.EqualTo("Failed"));
        }

        [Test]
        public void Failure_StaticMethodWithoutException_CreatesFailureResult()
        {
            // Arrange
            const string errorMessage = "Static failure without exception";

            // Act
            var result = InitializationResult.Failure(errorMessage);

            // Assert
            Assert.That(result.IsSuccess, Is.False);
            Assert.That(result.ErrorMessage, Is.EqualTo(errorMessage));
            Assert.That(result.Exception, Is.Null);
        }

        [Test]
        public void Constructor_WithNullShortcut_HandlesGracefully()
        {
            // Act & Assert
            // L'implémentation actuelle ne valide pas les paramètres null
            Assert.DoesNotThrow(() =>
                new InitializationResult(null!, _testHandle, TestStrategy));
        }

        [Test]
        public void Constructor_WithNullStrategy_HandlesGracefully()
        {
            // Act & Assert
            // L'implémentation actuelle ne valide pas les paramètres null
            Assert.DoesNotThrow(() =>
                new InitializationResult(_testShortcut, _testHandle, null!));
        }

        [Test]
        public void Constructor_WithEmptyStrategy_HandlesGracefully()
        {
            // Act & Assert
            // L'implémentation actuelle ne valide pas les chaînes vides
            Assert.DoesNotThrow(() =>
                new InitializationResult(_testShortcut, _testHandle, ""));
        }

        [Test]
        public void Constructor_WithWhitespaceStrategy_HandlesGracefully()
        {
            // Act & Assert
            // L'implémentation actuelle ne valide pas les espaces
            Assert.DoesNotThrow(() =>
                new InitializationResult(_testShortcut, _testHandle, "   "));
        }

        [Test]
        public void Constructor_WithNullErrorMessage_HandlesGracefully()
        {
            // Act & Assert
            // L'implémentation actuelle ne valide pas les paramètres null
            Assert.DoesNotThrow(() =>
                new InitializationResult(null!, new Exception()));
        }

        [Test]
        public void Constructor_WithEmptyErrorMessage_HandlesGracefully()
        {
            // Act & Assert
            // L'implémentation actuelle ne valide pas les chaînes vides
            Assert.DoesNotThrow(() =>
                new InitializationResult("", new Exception()));
        }

        [Test]
        public void Constructor_WithWhitespaceErrorMessage_HandlesGracefully()
        {
            // Act & Assert
            // L'implémentation actuelle ne valide pas les espaces
            Assert.DoesNotThrow(() =>
                new InitializationResult("   ", new Exception()));
        }

        [Test]
        public void Timestamp_IsSetToCurrentTime()
        {
            // Arrange
            var beforeCreation = DateTime.UtcNow;

            // Act
            var result = InitializationResult.Success(_testShortcut, _testHandle, TestStrategy);
            var afterCreation = DateTime.UtcNow;

            // Assert
            Assert.That(result.Timestamp, Is.GreaterThanOrEqualTo(beforeCreation));
            Assert.That(result.Timestamp, Is.LessThanOrEqualTo(afterCreation));
        }

        [Test]
        public void MultipleResults_HaveDifferentTimestamps()
        {
            // Act
            var result1 = InitializationResult.Success(_testShortcut, _testHandle, TestStrategy);
            System.Threading.Thread.Sleep(1); // Assurer une différence de temps
            var result2 = InitializationResult.Success(_testShortcut, _testHandle, TestStrategy);

            // Assert
            Assert.That(result2.Timestamp, Is.GreaterThanOrEqualTo(result1.Timestamp));
        }

        [Test]
        public void WindowHandle_CanBeZero()
        {
            // Act
            var result = new InitializationResult(_testShortcut, IntPtr.Zero, TestStrategy);

            // Assert
            Assert.That(result.WindowHandle, Is.EqualTo(IntPtr.Zero));
            Assert.That(result.IsSuccess, Is.True);
        }
    }
}
