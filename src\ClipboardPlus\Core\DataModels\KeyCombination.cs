using System;
using System.Windows.Input;

namespace ClipboardPlus.Core.DataModels
{
    /// <summary>
    /// Représente une combinaison de touches (raccourci clavier).
    /// </summary>
    public class KeyCombination
    {
        /// <summary>
        /// Modificateurs (Ctrl, Alt, Shift, Win).
        /// </summary>
        public ModifierKeys Modifiers { get; set; }

        /// <summary>
        /// Touche principale.
        /// </summary>
        public Key Key { get; set; }

        /// <summary>
        /// Constructeur par défaut.
        /// </summary>
        public KeyCombination()
        {
            Modifiers = ModifierKeys.Windows;
            Key = Key.V;
        }

        /// <summary>
        /// Constructeur avec paramètres.
        /// </summary>
        /// <param name="modifiers">Modificateurs de touches (Ctrl, Alt, Shift, Win).</param>
        /// <param name="key">Touche principale.</param>
        public KeyCombination(ModifierKeys modifiers, Key key)
        {
            Modifiers = modifiers;
            Key = key;
        }

        /// <summary>
        /// Convertit la combinaison de touches en chaîne de caractères.
        /// </summary>
        /// <returns>Représentation textuelle de la combinaison de touches (ex: "Win+V").</returns>
        public override string ToString()
        {
            var modifiersText = "";

            if ((Modifiers & ModifierKeys.Windows) == ModifierKeys.Windows)
                modifiersText += "Win+";
            if ((Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
                modifiersText += "Ctrl+";
            if ((Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                modifiersText += "Alt+";
            if ((Modifiers & ModifierKeys.Shift) == ModifierKeys.Shift)
                modifiersText += "Shift+";

            return modifiersText + Key.ToString();
        }

        /// <summary>
        /// Analyse une chaîne de caractères et la convertit en KeyCombination.
        /// </summary>
        /// <param name="combinationString">Chaîne à analyser (ex: "Win+V").</param>
        /// <returns>KeyCombination correspondant à la chaîne analysée.</returns>
        /// <exception cref="ArgumentException">Si la chaîne est invalide.</exception>
        public static KeyCombination Parse(string combinationString)
        {
            if (string.IsNullOrWhiteSpace(combinationString))
                throw new ArgumentException("La chaîne de combinaison de touches ne peut pas être vide.", nameof(combinationString));

            var parts = combinationString.Split(new[] { '+' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length < 1)
                throw new ArgumentException("Format de combinaison de touches invalide.", nameof(combinationString));

            var modifiers = ModifierKeys.None;
            string keyName = parts[parts.Length - 1].Trim();

            for (int i = 0; i < parts.Length - 1; i++)
            {
                string part = parts[i].Trim().ToLowerInvariant();
                switch (part)
                {
                    case "win":
                    case "windows":
                        modifiers |= ModifierKeys.Windows;
                        break;
                    case "ctrl":
                    case "control":
                        modifiers |= ModifierKeys.Control;
                        break;
                    case "alt":
                        modifiers |= ModifierKeys.Alt;
                        break;
                    case "shift":
                        modifiers |= ModifierKeys.Shift;
                        break;
                    default:
                        throw new ArgumentException($"Modificateur de touche inconnu: {part}", nameof(combinationString));
                }
            }

            if (!Enum.TryParse(keyName, true, out Key key))
                throw new ArgumentException($"Nom de touche inconnu: {keyName}", nameof(combinationString));

            return new KeyCombination(modifiers, key);
        }

        /// <summary>
        /// Tente d'analyser une chaîne en KeyCombination sans lever d'exception en cas d'échec.
        /// </summary>
        /// <param name="combinationString">Chaîne à analyser.</param>
        /// <param name="result">KeyCombination résultant si l'analyse réussit.</param>
        /// <returns>True si l'analyse a réussi, False sinon.</returns>
        public static bool TryParse(string combinationString, out KeyCombination result)
        {
            result = new KeyCombination();

            try
            {
                result = Parse(combinationString);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 