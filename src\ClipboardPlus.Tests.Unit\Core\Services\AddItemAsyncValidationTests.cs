using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Interfaces;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests de validation pour la méthode AddItemAsync utilisant l'architecture SOLID.
    /// Ces tests vérifient que l'implémentation SOLID fonctionne correctement.
    /// </summary>
    [TestFixture]
    public class AddItemAsyncValidationTests
    {
        private Mock<IPersistenceService> _mockPersistenceService;
        private Mock<ISettingsManager> _mockSettingsManager;
        private Mock<IClipboardInteractionService> _mockClipboardInteractionService;
        private Mock<ILoggingService> _mockLoggingService;
        private ClipboardHistoryManager _historyManager;

        [SetUp]
        public void SetUp()
        {
            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockClipboardInteractionService = new Mock<IClipboardInteractionService>();
            _mockLoggingService = new Mock<ILoggingService>();

            // Configuration par défaut des mocks
            _mockSettingsManager.Setup(x => x.MaxStorableItemSizeBytes).Returns(10 * 1024 * 1024); // 10 MB
            _mockSettingsManager.Setup(x => x.MaxHistoryItems).Returns(100);

            _historyManager = new ClipboardHistoryManager(
                _mockPersistenceService.Object,
                _mockSettingsManager.Object,
                _mockClipboardInteractionService.Object);
        }

        [Test]
        public async Task AddItemAsync_WithValidItem_ReturnsValidId()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.UtcNow
            };

            _mockPersistenceService.Setup(x => x.InsertClipboardItemAsync(It.IsAny<ClipboardItem>()))
                .ReturnsAsync(123);

            // Act
            var result = await _historyManager.AddItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(123));
            Assert.That(item.Id, Is.EqualTo(123));
        }

        [Test]
        public void AddItemAsync_WithNullItem_ThrowsArgumentNullException()
        {
            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentNullException>(
                async () => await _historyManager.AddItemAsync(null));

            Assert.That(exception.ParamName, Is.EqualTo("item"));
        }

        [Test]
        public async Task AddItemAsync_WithOversizedItem_ThrowsArgumentException()
        {
            // Arrange
            var oversizedData = new byte[11 * 1024 * 1024]; // 11 MB > limite de 10 MB
            var oversizedItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = oversizedData,
                Timestamp = DateTime.UtcNow
            };

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(
                async () => await _historyManager.AddItemAsync(oversizedItem));

            Assert.That(exception.ParamName, Is.EqualTo("item"));
            Assert.That(exception.Message, Does.Contain("taille maximale autorisée"));
        }

        [Test]
        [Ignore("Test temporairement ignoré - nécessite une refactorisation pour gérer les doublons avec l'orchestrateur")]
        public async Task AddItemAsync_WithDuplicateItem_UpdatesExistingItem()
        {
            // Arrange
            var existingItem = new ClipboardItem
            {
                Id = 456,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Duplicate content"),
                Timestamp = DateTime.UtcNow.AddMinutes(-5)
            };

            var newItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Duplicate content"),
                Timestamp = DateTime.UtcNow
            };

            // Simuler qu'il y a déjà un élément en historique via le service de persistance
            _mockPersistenceService.Setup(x => x.GetAllClipboardItemsAsync())
                .ReturnsAsync(new List<ClipboardItem> { existingItem });

            _mockPersistenceService.Setup(x => x.UpdateClipboardItemAsync(It.IsAny<ClipboardItem>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _historyManager.AddItemAsync(newItem);

            // Assert
            Assert.That(result, Is.EqualTo(456)); // ID de l'élément existant
            _mockPersistenceService.Verify(x => x.UpdateClipboardItemAsync(It.IsAny<ClipboardItem>()), Times.Once);
        }
    }
}
