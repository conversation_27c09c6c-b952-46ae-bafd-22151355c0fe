using System;
using System.Collections.Generic;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.STA.Mocks
{
    /// <summary>
    /// Implémentation mock de IWindowsHotkeyApi pour les tests unitaires.
    /// Permet de simuler le comportement de l'API Windows sans dépendance système.
    /// </summary>
    public class MockWindowsHotkeyApi : IWindowsHotkeyApi
    {
        /// <summary>
        /// Contrôle si RegisterHotKey doit réussir ou échouer.
        /// </summary>
        public bool ShouldRegisterSucceed { get; set; } = true;

        /// <summary>
        /// Contrôle si UnregisterHotKey doit réussir ou échouer.
        /// </summary>
        public bool ShouldUnregisterSucceed { get; set; } = true;

        /// <summary>
        /// Liste des raccourcis enregistrés via RegisterHotKey.
        /// Utile pour vérifier les paramètres passés dans les tests.
        /// </summary>
        public List<RegisteredHotkey> RegisteredHotkeys { get; } = new();

        /// <summary>
        /// Liste des IDs de raccourcis désenregistrés via UnregisterHotKey.
        /// </summary>
        public List<int> UnregisteredHotkeyIds { get; } = new();

        /// <summary>
        /// Simule l'enregistrement d'un raccourci clavier global.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre</param>
        /// <param name="id">Identifiant du raccourci</param>
        /// <param name="modifiers">Modificateurs</param>
        /// <param name="vk">Code de la touche virtuelle</param>
        /// <returns>True si ShouldRegisterSucceed est true, False sinon</returns>
        public bool RegisterHotKey(IntPtr hWnd, int id, uint modifiers, uint vk)
        {
            if (ShouldRegisterSucceed)
            {
                RegisteredHotkeys.Add(new RegisteredHotkey(hWnd, id, modifiers, vk));
                return true;
            }
            return false;
        }

        /// <summary>
        /// Simule le désenregistrement d'un raccourci clavier global.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre</param>
        /// <param name="id">Identifiant du raccourci</param>
        /// <returns>True si ShouldUnregisterSucceed est true, False sinon</returns>
        public bool UnregisterHotKey(IntPtr hWnd, int id)
        {
            if (ShouldUnregisterSucceed)
            {
                UnregisteredHotkeyIds.Add(id);
                return true;
            }
            return false;
        }

        /// <summary>
        /// Remet à zéro l'état du mock pour un nouveau test.
        /// </summary>
        public void Reset()
        {
            ShouldRegisterSucceed = true;
            ShouldUnregisterSucceed = true;
            RegisteredHotkeys.Clear();
            UnregisteredHotkeyIds.Clear();
        }
    }

    /// <summary>
    /// Représente un raccourci enregistré pour les tests.
    /// </summary>
    /// <param name="HWnd">Handle de la fenêtre</param>
    /// <param name="Id">Identifiant du raccourci</param>
    /// <param name="Modifiers">Modificateurs (Ctrl, Alt, etc.)</param>
    /// <param name="VirtualKey">Code de la touche virtuelle</param>
    public record RegisteredHotkey(IntPtr HWnd, int Id, uint Modifiers, uint VirtualKey);
}
