using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;

namespace ClipboardPlus.Tests.Unit.Core.Services.Deletion
{
    /// <summary>
    /// Tests unitaires pour DeletionValidator
    /// </summary>
    [TestFixture]
    public class DeletionValidatorTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private DeletionValidator _validator = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _validator = new DeletionValidator(_mockLoggingService.Object);
        }

        [TestCase(1, true)]
        [TestCase(100, true)]
        [TestCase(long.MaxValue, true)]
        [TestCase(0, false)]
        [TestCase(-1, false)]
        [TestCase(long.MinValue, false)]
        public void IsValidId_ShouldReturnExpectedResult(long id, bool expected)
        {
            // Act
            var result = _validator.IsValidId(id);

            // Assert
            Assert.That(result, Is.EqualTo(expected));
        }

        [Test]
        public async Task ValidateRequestAsync_WithValidParameters_ShouldReturnSuccess()
        {
            // Arrange
            long validId = 123;
            string validOperationId = "test-operation-123";

            // Act
            var result = await _validator.ValidateRequestAsync(validId, validOperationId);

            // Assert
            Assert.That(result.IsValid, Is.True);
            Assert.That(result.ErrorMessage, Is.Null);
            Assert.That(result.ErrorCode, Is.Null);

            // Verify logging calls
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(validOperationId) && s.Contains("Validation de la requête"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(validOperationId) && s.Contains("Validation réussie"))), Times.Once);
        }

        [TestCase(0)]
        [TestCase(-1)]
        [TestCase(-999)]
        public async Task ValidateRequestAsync_WithInvalidId_ShouldReturnFailure(long invalidId)
        {
            // Arrange
            string validOperationId = "test-operation-123";

            // Act
            var result = await _validator.ValidateRequestAsync(invalidId, validOperationId);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.ErrorMessage, Is.Not.Null);
            Assert.That(result.ErrorCode, Is.EqualTo("INVALID_ID"));
            Assert.That(result.ErrorMessage, Does.Contain("ID invalide"));

            // Verify logging calls
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(validOperationId) && s.Contains("Validation de la requête"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(validOperationId) && s.Contains("ID invalide"))), Times.Once);
        }

        [TestCase(null)]
        [TestCase("")]
        [TestCase("   ")]
        public async Task ValidateRequestAsync_WithInvalidOperationId_ShouldReturnFailure(string invalidOperationId)
        {
            // Arrange
            long validId = 123;

            // Act
            var result = await _validator.ValidateRequestAsync(validId, invalidOperationId);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.ErrorMessage, Is.Not.Null);
            Assert.That(result.ErrorCode, Is.EqualTo("INVALID_OPERATION_ID"));
            Assert.That(result.ErrorMessage, Does.Contain("OperationId ne peut pas être null ou vide"));

            // Verify logging calls
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains("Validation échouée") && s.Contains("OperationId"))), Times.Once);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<System.ArgumentNullException>(() => new DeletionValidator(null!));
        }

        [Test]
        public async Task ValidateRequestAsync_ShouldLogCorrectMessages()
        {
            // Arrange
            long validId = 456;
            string validOperationId = "test-op-456";

            // Act
            await _validator.ValidateRequestAsync(validId, validOperationId);

            // Assert - Verify specific log messages
            _mockLoggingService.Verify(x => x.LogDebug($"[{validOperationId}] Validation de la requête de suppression pour ID: {validId}"), Times.Once);
            _mockLoggingService.Verify(x => x.LogDebug($"[{validOperationId}] Validation réussie pour ID: {validId}"), Times.Once);
        }

        [Test]
        public async Task ValidateRequestAsync_WithBoundaryValues_ShouldWork()
        {
            // Arrange
            string operationId = "boundary-test";

            // Test with ID = 1 (minimum valid)
            var result1 = await _validator.ValidateRequestAsync(1, operationId);
            Assert.That(result1.IsValid, Is.True);

            // Test with ID = long.MaxValue (maximum valid)
            var result2 = await _validator.ValidateRequestAsync(long.MaxValue, operationId);
            Assert.That(result2.IsValid, Is.True);
        }
    }

    /// <summary>
    /// Tests pour les méthodes statiques de DeletionValidationResult
    /// </summary>
    [TestFixture]
    public class DeletionValidationResultTests
    {
        [Test]
        public void CreateSuccess_ShouldReturnValidSuccessResult()
        {
            // Act
            var result = DeletionValidationResult.CreateSuccess();

            // Assert
            Assert.That(result.IsValid, Is.True);
            Assert.That(result.ErrorMessage, Is.Null);
            Assert.That(result.ErrorCode, Is.Null);
        }

        [Test]
        public void CreateFailure_WithMessage_ShouldReturnValidFailureResult()
        {
            // Arrange
            string errorMessage = "Test error message";

            // Act
            var result = DeletionValidationResult.CreateFailure(errorMessage);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.ErrorMessage, Is.EqualTo(errorMessage));
            Assert.That(result.ErrorCode, Is.Null);
        }

        [Test]
        public void CreateFailure_WithMessageAndCode_ShouldReturnValidFailureResult()
        {
            // Arrange
            string errorMessage = "Test error message";
            string errorCode = "TEST_ERROR";

            // Act
            var result = DeletionValidationResult.CreateFailure(errorMessage, errorCode);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.ErrorMessage, Is.EqualTo(errorMessage));
            Assert.That(result.ErrorCode, Is.EqualTo(errorCode));
        }
    }
}
