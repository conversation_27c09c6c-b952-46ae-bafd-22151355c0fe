using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services.Shortcuts.Interfaces;
using ClipboardPlus.Core.Services.Shortcuts.Models;

namespace ClipboardPlus.Core.Services.Shortcuts.Strategies
{
    /// <summary>
    /// Stratégie qui utilise la fenêtre principale de l'application pour l'enregistrement de raccourcis.
    /// </summary>
    public class MainWindowStrategy : IHandleProvisionStrategy
    {
        private readonly IWpfApplicationProvider _wpfProvider;
        private readonly IDispatcherProvider _dispatcherProvider;
        private readonly IWindowFactory _windowFactory;

        public MainWindowStrategy(
            IWpfApplicationProvider wpfProvider,
            IDispatcherProvider dispatcherProvider,
            IWindowFactory windowFactory)
        {
            _wpfProvider = wpfProvider ?? throw new ArgumentNullException(nameof(wpfProvider));
            _dispatcherProvider = dispatcherProvider ?? throw new ArgumentNullException(nameof(dispatcherProvider));
            _windowFactory = windowFactory ?? throw new ArgumentNullException(nameof(windowFactory));
        }

        /// <inheritdoc />
        public string StrategyName => "MainWindow";

        /// <inheritdoc />
        public bool CanProvideHandle()
        {
            return _wpfProvider.IsWpfApplicationAvailable() && 
                   _wpfProvider.IsMainWindowLoaded() &&
                   _dispatcherProvider.IsDispatcherAvailable();
        }

        /// <inheritdoc />
        public async Task<IntPtr> ProvideHandleAsync(InitializationContext context)
        {
            if (!CanProvideHandle())
            {
                return IntPtr.Zero;
            }

            try
            {
                return await _dispatcherProvider.InvokeAsync(() =>
                {
                    var mainWindow = _wpfProvider.GetMainWindow();
                    if (mainWindow == null)
                    {
                        return IntPtr.Zero;
                    }

                    return _windowFactory.GetWindowHandle(mainWindow);
                });
            }
            catch (Exception)
            {
                // En cas d'erreur, retourner IntPtr.Zero pour permettre le fallback
                return IntPtr.Zero;
            }
        }

        /// <inheritdoc />
        public void Cleanup()
        {
            // Rien à nettoyer pour la fenêtre principale
            // Elle est gérée par l'application
        }
    }
}
