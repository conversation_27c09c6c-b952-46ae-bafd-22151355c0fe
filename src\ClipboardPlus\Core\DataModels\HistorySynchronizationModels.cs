using System.Collections.Generic;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.DataModels
{
    /// <summary>
    /// Contexte contenant toutes les informations nécessaires pour la synchronisation
    /// des collections d'historique entre le ViewModel et le Manager.
    /// </summary>
    public class HistorySynchronizationContext
    {
        /// <summary>
        /// Collection d'éléments dans l'interface utilisateur.
        /// </summary>
        public IList<ClipboardItem> UIItems { get; set; } = new List<ClipboardItem>();

        /// <summary>
        /// Collection d'éléments dans le gestionnaire d'historique.
        /// </summary>
        public IList<ClipboardItem>? ManagerItems { get; set; }

        /// <summary>
        /// Synchroniseur de collections pour effectuer la synchronisation.
        /// </summary>
        public HistoryCollectionSynchronizer? Synchronizer { get; set; }

        /// <summary>
        /// Identifiant de l'événement pour le logging.
        /// </summary>
        public string EventId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Résultat d'une opération de synchronisation des collections.
    /// </summary>
    public class SynchronizationResult
    {
        /// <summary>
        /// Type de résultat de la synchronisation.
        /// </summary>
        public SynchronizationResultType Type { get; private set; }

        /// <summary>
        /// Message descriptif du résultat.
        /// </summary>
        public string Message { get; private set; }

        /// <summary>
        /// Indique si un rechargement complet est nécessaire.
        /// </summary>
        public bool RequiresFullReload => Type == SynchronizationResultType.RequiresFullReload;

        /// <summary>
        /// Indique si une nouvelle tentative de synchronisation est nécessaire.
        /// </summary>
        public bool RequiresRetry => Type == SynchronizationResultType.RequiresRetry;

        private SynchronizationResult(SynchronizationResultType type, string message)
        {
            Type = type;
            Message = message;
        }

        /// <summary>
        /// Crée un résultat indiquant que les collections étaient déjà synchronisées.
        /// </summary>
        public static SynchronizationResult AlreadySynchronized() =>
            new SynchronizationResult(SynchronizationResultType.AlreadySynchronized, "Collections déjà synchronisées");

        /// <summary>
        /// Crée un résultat indiquant qu'une synchronisation a été effectuée.
        /// </summary>
        public static SynchronizationResult Synchronized() =>
            new SynchronizationResult(SynchronizationResultType.Synchronized, "Synchronisation effectuée");

        /// <summary>
        /// Crée un résultat indiquant qu'une nouvelle tentative de synchronisation est nécessaire.
        /// </summary>
        public static SynchronizationResult RequiresRetryResult() =>
            new SynchronizationResult(SynchronizationResultType.RequiresRetry, "Nouvelle tentative de synchronisation nécessaire");

        /// <summary>
        /// Crée un résultat indiquant qu'un rechargement complet est nécessaire.
        /// </summary>
        public static SynchronizationResult RequiresFullReloadResult() =>
            new SynchronizationResult(SynchronizationResultType.RequiresFullReload, "Rechargement complet nécessaire");

        /// <summary>
        /// Crée un résultat d'erreur.
        /// </summary>
        /// <param name="errorMessage">Message d'erreur</param>
        public static SynchronizationResult Error(string errorMessage) =>
            new SynchronizationResult(SynchronizationResultType.Error, errorMessage);
    }

    /// <summary>
    /// Types de résultats possibles pour une synchronisation.
    /// </summary>
    public enum SynchronizationResultType
    {
        /// <summary>
        /// Les collections étaient déjà synchronisées.
        /// </summary>
        AlreadySynchronized,

        /// <summary>
        /// Une synchronisation a été effectuée avec succès.
        /// </summary>
        Synchronized,

        /// <summary>
        /// La synchronisation a échoué mais peut être retentée.
        /// </summary>
        RequiresRetry,

        /// <summary>
        /// Un rechargement complet est nécessaire.
        /// </summary>
        RequiresFullReload,

        /// <summary>
        /// Une erreur s'est produite pendant la synchronisation.
        /// </summary>
        Error
    }

    /// <summary>
    /// Résultat de la comparaison entre deux collections.
    /// </summary>
    public class CollectionComparison
    {
        /// <summary>
        /// Nombre d'éléments dans la collection UI.
        /// </summary>
        public int UICount { get; }

        /// <summary>
        /// Nombre d'éléments dans la collection Manager.
        /// </summary>
        public int ManagerCount { get; }

        /// <summary>
        /// Indique si les collections sont synchronisées.
        /// </summary>
        public bool AreSynchronized { get; }

        /// <summary>
        /// Initialise une nouvelle instance de comparaison de collections.
        /// </summary>
        /// <param name="uiCount">Nombre d'éléments UI</param>
        /// <param name="managerCount">Nombre d'éléments Manager</param>
        /// <param name="areSynchronized">Indique si les collections sont synchronisées</param>
        public CollectionComparison(int uiCount, int managerCount, bool areSynchronized)
        {
            UICount = uiCount;
            ManagerCount = managerCount;
            AreSynchronized = areSynchronized;
        }
    }
}
