using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Settings;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.DataModels.Settings;

namespace ClipboardPlus.Services.Settings
{
    /// <summary>
    /// Implémentation du service de validation des paramètres.
    /// Centralise toute la logique de validation avant application.
    /// </summary>
    public class SettingsValidationService : ISettingsValidationService
    {
        private readonly ILoggingService _loggingService;

        public SettingsValidationService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        public SettingsValidationResult ValidateShortcutChange(string currentShortcut, string newShortcut)
        {
            _loggingService?.LogInfo($"[SettingsValidationService] Validation changement raccourci: '{currentShortcut}' -> '{newShortcut}'");

            var errors = new List<string>();

            // Vérifier si le raccourci a réellement changé
            if (currentShortcut == newShortcut)
            {
                return new SettingsValidationResult(true, "Raccourci inchangé", errors);
            }

            // Valider le format du nouveau raccourci
            if (string.IsNullOrWhiteSpace(newShortcut))
            {
                errors.Add("Le raccourci clavier ne peut pas être vide");
                return new SettingsValidationResult(false, "Raccourci invalide", errors);
            }

            // Tenter de parser le raccourci
            if (!KeyCombination.TryParse(newShortcut, out _))
            {
                errors.Add($"Format de raccourci clavier invalide: '{newShortcut}'");
                return new SettingsValidationResult(false, "Format de raccourci invalide", errors);
            }

            return new SettingsValidationResult(true, "Raccourci valide", errors);
        }

        public SettingsValidationResult ValidateStartupChange(bool currentStartup, bool newStartup)
        {
            _loggingService?.LogInfo($"[SettingsValidationService] Validation changement démarrage: {currentStartup} -> {newStartup}");

            var errors = new List<string>();

            // Pas de validation spéciale nécessaire pour un booléen
            // La validation se fait au niveau de l'application (disponibilité du service)

            return new SettingsValidationResult(true, "Paramètre de démarrage valide", errors);
        }

        public SettingsValidationResult ValidateThemeChange(ThemeInfo? currentTheme, ThemeInfo? newTheme)
        {
            _loggingService?.LogInfo($"[SettingsValidationService] Validation changement thème: '{currentTheme?.FilePath}' -> '{newTheme?.FilePath}'");

            var errors = new List<string>();

            // Vérifier si le thème a réellement changé
            if (currentTheme?.FilePath == newTheme?.FilePath)
            {
                return new SettingsValidationResult(true, "Thème inchangé", errors);
            }

            // Valider le nouveau thème si spécifié
            if (newTheme != null)
            {
                if (string.IsNullOrWhiteSpace(newTheme.FilePath))
                {
                    errors.Add("Le chemin du thème ne peut pas être vide");
                    return new SettingsValidationResult(false, "Chemin de thème invalide", errors);
                }

                if (string.IsNullOrWhiteSpace(newTheme.Name))
                {
                    errors.Add("Le nom du thème ne peut pas être vide");
                    return new SettingsValidationResult(false, "Nom de thème invalide", errors);
                }
            }

            return new SettingsValidationResult(true, "Thème valide", errors);
        }

        public Task<SettingsValidationResult> ValidateAllChangesAsync(CompleteSettingsData currentSettings, CompleteSettingsData newSettings)
        {
            _loggingService?.LogInfo("[SettingsValidationService] Validation globale des changements");

            var allErrors = new List<string>();

            try
            {
                // Validation des paramètres de base
                var shortcutValidation = ValidateShortcutChange(
                    currentSettings.BasicSettings.ShortcutKeyCombination,
                    newSettings.BasicSettings.ShortcutKeyCombination);
                if (!shortcutValidation.IsValid)
                {
                    allErrors.AddRange(shortcutValidation.ValidationErrors);
                }

                var startupValidation = ValidateStartupChange(
                    currentSettings.BasicSettings.StartWithWindows,
                    newSettings.BasicSettings.StartWithWindows);
                if (!startupValidation.IsValid)
                {
                    allErrors.AddRange(startupValidation.ValidationErrors);
                }

                // Validation des paramètres avancés
                var themeValidation = ValidateThemeChange(
                    currentSettings.AdvancedSettings.SelectedTheme,
                    newSettings.AdvancedSettings.SelectedTheme);
                if (!themeValidation.IsValid)
                {
                    allErrors.AddRange(themeValidation.ValidationErrors);
                }

                // Validations métier supplémentaires
                if (newSettings.BasicSettings.MaxHistoryItems <= 0)
                {
                    allErrors.Add("Le nombre maximum d'éléments d'historique doit être positif");
                }

                if (newSettings.BasicSettings.MaxHistoryItems > 10000)
                {
                    allErrors.Add("Le nombre maximum d'éléments d'historique ne peut pas dépasser 10000");
                }

                if (newSettings.BasicSettings.MaxStorableItemSizeBytes <= 0)
                {
                    allErrors.Add("La taille maximale des éléments doit être positive");
                }

                if (newSettings.BasicSettings.MaxImageDimensionForThumbnail <= 0)
                {
                    allErrors.Add("La dimension maximale des miniatures doit être positive");
                }

                var isValid = allErrors.Count == 0;
                var message = isValid ? "Tous les changements sont valides" : $"Validation échouée: {allErrors.Count} erreur(s)";

                _loggingService?.LogInfo($"[SettingsValidationService] Validation globale terminée: {(isValid ? "SUCCÈS" : "ÉCHEC")}");

                return Task.FromResult(new SettingsValidationResult(isValid, message, allErrors));
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[SettingsValidationService] ValidateAllChangesAsync ERREUR", ex);
                allErrors.Add($"Erreur lors de la validation: {ex.Message}");
                return Task.FromResult(new SettingsValidationResult(false, "Erreur de validation", allErrors));
            }
        }
    }
}
