using System;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service responsable de la gestion du threading UI pour les changements d'historique.
    /// 
    /// Ce service extrait la logique complexe de gestion des threads de la méthode
    /// ClipboardHistoryManager_HistoryChanged pour améliorer la testabilité et la clarté.
    /// </summary>
    public interface IHistoryChangeThreadingService
    {
        /// <summary>
        /// Assure que l'exécution se fait sur le thread UI.
        /// Si l'appel n'est pas sur le thread UI, redirige l'action vers le thread UI.
        /// </summary>
        /// <param name="action">Action à exécuter sur le thread UI</param>
        /// <param name="eventId">Identifiant de l'événement pour le logging</param>
        /// <returns>True si l'action a été exécutée directement, False si elle a été redirigée</returns>
        bool EnsureUIThread(Action action, string eventId);

        /// <summary>
        /// Vérifie si le thread actuel est le thread UI.
        /// </summary>
        /// <returns>True si on est sur le thread UI, False sinon</returns>
        bool IsOnUIThread();
    }
}
