using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ClipboardPlus.Modules.Core
{
    /// <summary>
    /// Interface de base pour tous les modules de l'architecture modulaire.
    /// 
    /// Cette interface définit le contrat commun que tous les modules doivent respecter
    /// pour garantir une intégration cohérente et une gestion uniforme du cycle de vie.
    /// </summary>
    public interface IModule : IDisposable
    {
        /// <summary>
        /// Nom unique du module pour identification et logging.
        /// </summary>
        string ModuleName { get; }

        /// <summary>
        /// Version du module pour la compatibilité et le debugging.
        /// </summary>
        Version ModuleVersion { get; }

        /// <summary>
        /// État actuel du module.
        /// </summary>
        ModuleState State { get; }

        /// <summary>
        /// Indique si le module est initialisé et prêt à être utilisé.
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Indique si le module est en cours de fonctionnement.
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// Événement déclenché lorsque l'état du module change.
        /// </summary>
        event EventHandler<ModuleStateChangedEventArgs> StateChanged;

        /// <summary>
        /// Initialise le module de manière asynchrone.
        /// 
        /// Cette méthode doit être appelée avant toute utilisation du module.
        /// Elle configure les dépendances, initialise l'état interne et prépare
        /// le module pour le fonctionnement.
        /// </summary>
        /// <returns>Task représentant l'opération d'initialisation</returns>
        /// <exception cref="ModuleInitializationException">
        /// Levée si l'initialisation échoue
        /// </exception>
        Task InitializeAsync();

        /// <summary>
        /// Démarre le module et ses opérations.
        /// 
        /// Cette méthode active le module après l'initialisation.
        /// Elle démarre les processus en arrière-plan, les listeners d'événements
        /// et toute autre logique opérationnelle.
        /// </summary>
        /// <returns>Task représentant l'opération de démarrage</returns>
        /// <exception cref="ModuleOperationException">
        /// Levée si le démarrage échoue
        /// </exception>
        Task StartAsync();

        /// <summary>
        /// Arrête le module de manière gracieuse.
        /// 
        /// Cette méthode suspend les opérations du module sans le détruire.
        /// Le module peut être redémarré après un arrêt.
        /// </summary>
        /// <returns>Task représentant l'opération d'arrêt</returns>
        /// <exception cref="ModuleOperationException">
        /// Levée si l'arrêt échoue
        /// </exception>
        Task StopAsync();

        /// <summary>
        /// Réinitialise le module à son état initial.
        /// 
        /// Cette méthode remet le module dans un état propre,
        /// équivalent à un redémarrage complet.
        /// </summary>
        /// <returns>Task représentant l'opération de réinitialisation</returns>
        /// <exception cref="ModuleOperationException">
        /// Levée si la réinitialisation échoue
        /// </exception>
        Task ResetAsync();

        /// <summary>
        /// Valide l'état de santé du module.
        /// 
        /// Cette méthode vérifie que le module fonctionne correctement
        /// et retourne des informations de diagnostic.
        /// </summary>
        /// <returns>Résultat de la vérification de santé</returns>
        ModuleHealthResult CheckHealth();
    }

    /// <summary>
    /// États possibles d'un module.
    /// </summary>
    public enum ModuleState
    {
        /// <summary>
        /// Module créé mais pas encore initialisé.
        /// </summary>
        Created,

        /// <summary>
        /// Module en cours d'initialisation.
        /// </summary>
        Initializing,

        /// <summary>
        /// Module initialisé mais pas encore démarré.
        /// </summary>
        Initialized,

        /// <summary>
        /// Module en cours de démarrage.
        /// </summary>
        Starting,

        /// <summary>
        /// Module en fonctionnement normal.
        /// </summary>
        Running,

        /// <summary>
        /// Module en cours d'arrêt.
        /// </summary>
        Stopping,

        /// <summary>
        /// Module arrêté.
        /// </summary>
        Stopped,

        /// <summary>
        /// Module en erreur.
        /// </summary>
        Error,

        /// <summary>
        /// Module en cours de nettoyage/destruction.
        /// </summary>
        Disposing,

        /// <summary>
        /// Module détruit.
        /// </summary>
        Disposed
    }

    /// <summary>
    /// Arguments d'événement pour les changements d'état de module.
    /// </summary>
    public class ModuleStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// État précédent du module.
        /// </summary>
        public ModuleState PreviousState { get; }

        /// <summary>
        /// Nouvel état du module.
        /// </summary>
        public ModuleState NewState { get; }

        /// <summary>
        /// Horodatage du changement d'état.
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// Raison du changement d'état (optionnelle).
        /// </summary>
        public string? Reason { get; }

        public ModuleStateChangedEventArgs(ModuleState previousState, ModuleState newState, string? reason = null)
        {
            PreviousState = previousState;
            NewState = newState;
            Timestamp = DateTime.UtcNow;
            Reason = reason;
        }
    }

    /// <summary>
    /// Résultat d'une vérification de santé de module.
    /// </summary>
    public class ModuleHealthResult
    {
        /// <summary>
        /// Indique si le module est en bonne santé.
        /// </summary>
        public bool IsHealthy { get; }

        /// <summary>
        /// Message descriptif de l'état de santé.
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// Détails additionnels sur l'état de santé.
        /// </summary>
        public Dictionary<string, object> Details { get; }

        /// <summary>
        /// Horodatage de la vérification.
        /// </summary>
        public DateTime Timestamp { get; }

        public ModuleHealthResult(bool isHealthy, string message, Dictionary<string, object>? details = null)
        {
            IsHealthy = isHealthy;
            Message = message;
            Details = details ?? new Dictionary<string, object>();
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// Crée un résultat de santé positif.
        /// </summary>
        public static ModuleHealthResult Healthy(string message = "Module is healthy") =>
            new ModuleHealthResult(true, message);

        /// <summary>
        /// Crée un résultat de santé négatif.
        /// </summary>
        public static ModuleHealthResult Unhealthy(string message, Dictionary<string, object>? details = null) =>
            new ModuleHealthResult(false, message, details);
    }
}
