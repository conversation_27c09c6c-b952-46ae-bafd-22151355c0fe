<?xml version="1.0" encoding="utf-8" ?>
<RunSettings>
  <RunConfiguration>
    <!-- Forcer l'utilisation d'un seul thread pour éviter les problèmes avec les tests STA -->
    <MaxCpuCount>1</MaxCpuCount>
    <!-- Désactiver l'isolation des domaines d'application -->
    <DisableAppDomain>false</DisableAppDomain>
    <!-- Désactiver la parallélisation -->
    <DisableParallelization>true</DisableParallelization>
    <!-- Timeout pour les tests -->
    <TestSessionTimeout>300000</TestSessionTimeout>
  </RunConfiguration>

  <MSTest>
    <Parallelize>
      <Workers>1</Workers>
      <Scope>ClassLevel</Scope>
    </Parallelize>
    <!-- Timeout pour les méthodes de test individuelles -->
    <TestTimeout>30000</TestTimeout>
    <!-- Désactiver l'exécution forcée STA pour éviter les conflits avec le script de couverture -->
    <!-- <ExecutionThreadApartmentState>STA</ExecutionThreadApartmentState> -->
  </MSTest>

  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="XPlat code coverage">
        <Configuration>
          <Format>cobertura</Format>
          <ResultsDirectory>../CodeCoverage</ResultsDirectory>
          <SingleHit>false</SingleHit>
          <UseSourceLink>false</UseSourceLink>
          <IncludeTestAssembly>false</IncludeTestAssembly>
          <SkipAutoProps>true</SkipAutoProps>
          <DeterministicReport>false</DeterministicReport>
          <ExcludeByFile>**/obj/**/*.cs,**/bin/**/*.cs</ExcludeByFile>
          <ExcludeByAttribute>Obsolete,GeneratedCodeAttribute,CompilerGeneratedAttribute</ExcludeByAttribute>
          <IncludeDirectory>../ClipboardPlus</IncludeDirectory>
        </Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>
</RunSettings> 