using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Implementations;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Tests.Unit.Helpers;

namespace ClipboardPlus.Tests.Unit.Services.LogDeletionResult
{
    [TestFixture]
    public class LogDeletionResultIntegrationTests
    {
        private DeletionResultLogger _logger = null!;
        private CollectionStateAnalyzer _analyzer = null!;
        private DeletionResultValidator _validator = null!;
        private DeletionResultFormatter _formatter = null!;
        private DeletionMetricsCollector _metricsCollector = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private ClipboardHistoryViewModel _viewModel = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();

            // Créer les instances réelles pour les tests d'intégration
            _analyzer = new CollectionStateAnalyzer(_mockLoggingService.Object);
            _metricsCollector = new DeletionMetricsCollector(_mockLoggingService.Object);
            _formatter = new DeletionResultFormatter(_mockLoggingService.Object);
            _validator = new DeletionResultValidator(_mockLoggingService.Object, _analyzer);

            _logger = new DeletionResultLogger(
                _mockLoggingService.Object,
                _validator,
                _formatter,
                _analyzer,
                _metricsCollector);

            _viewModel = ClipboardHistoryViewModelTestHelper.CreateViewModel();
        }

        [TearDown]
        public void TearDown()
        {
            _viewModel?.Dispose();
            _metricsCollector?.ResetMetrics();
        }

        #region End-to-End Integration Tests

        [Test]
        public void CompleteWorkflow_WithSuccessfulDeletion_WorksEndToEnd()
        {
            // Arrange
            var item = CreateTestClipboardItem("Item to delete");
            var otherItems = CreateTestClipboardItems(3);

            // Ajouter les autres éléments mais pas l'élément à supprimer (simulation d'une suppression réussie)
            _viewModel.HistoryItems.Clear();
            foreach (var otherItem in otherItems)
            {
                _viewModel.HistoryItems.Add(otherItem);
            }

            var context = DeletionResultContext.CreateSuccess(item, _viewModel, "Item successfully deleted");

            // Act
            _logger.LogDeletionResult(context);

            // Assert
            var metrics = _metricsCollector.GetMetrics();
            Assert.That(metrics.TotalDeletions, Is.EqualTo(1));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(1));
            Assert.That(metrics.FailedDeletions, Is.EqualTo(0));

            // Vérifier que les logs ont été générés - le logger utilise LogDebug, pas LogInfo
            _mockLoggingService.Verify(
                x => x.LogDebug(It.Is<string>(s => s.Contains("Logging terminé"))),
                Times.Once);
        }

        [Test]
        public void CompleteWorkflow_WithFailedDeletion_DetectsItemStillPresent()
        {
            // Arrange
            var item = CreateTestClipboardItem("Item that should be deleted");
            var otherItems = CreateTestClipboardItems(2);

            // Ajouter tous les éléments y compris celui qui devrait être supprimé (simulation d'échec)
            _viewModel.HistoryItems.Clear();
            foreach (var otherItem in otherItems)
            {
                _viewModel.HistoryItems.Add(otherItem);
            }
            _viewModel.HistoryItems.Add(item); // L'élément est encore présent

            var context = DeletionResultContext.CreateFailure(item, "Deletion failed", _viewModel);

            // Act
            _logger.LogDeletionResult(context);

            // Assert
            var metrics = _metricsCollector.GetMetrics();
            Assert.That(metrics.TotalDeletions, Is.EqualTo(1));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(0));
            Assert.That(metrics.FailedDeletions, Is.EqualTo(1));

            // Vérifier que l'échec a été détecté - le logger utilise LogDebug, pas LogInfo
            _mockLoggingService.Verify(
                x => x.LogDebug(It.Is<string>(s => s.Contains("Logging terminé"))),
                Times.Once);
        }

        [Test]
        public async Task CompleteWorkflowAsync_WithMultipleOperations_AccumulatesMetricsCorrectly()
        {
            // Arrange
            var operations = new List<DeletionResultContext>();

            // Créer plusieurs opérations : 3 succès, 2 échecs
            for (int i = 0; i < 3; i++)
            {
                var item = CreateTestClipboardItem($"Success item {i}");
                operations.Add(DeletionResultContext.CreateSuccess(item, _viewModel));
            }

            for (int i = 0; i < 2; i++)
            {
                var item = CreateTestClipboardItem($"Failed item {i}");
                operations.Add(DeletionResultContext.CreateFailure(item, "Failed", _viewModel));
            }

            // Act
            foreach (var operation in operations)
            {
                await _logger.LogDeletionResultAsync(operation);
            }

            // Assert
            var metrics = _metricsCollector.GetMetrics();
            Assert.That(metrics.TotalDeletions, Is.EqualTo(5));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(3));
            Assert.That(metrics.FailedDeletions, Is.EqualTo(2));

            var performanceMetrics = _metricsCollector.GetPerformanceMetrics();
            Assert.That(performanceMetrics.TotalLoggingOperations, Is.EqualTo(5));
            Assert.That(performanceMetrics.AverageDuration, Is.GreaterThan(TimeSpan.Zero));
        }

        [Test]
        public void CompleteWorkflow_WithCollectionInconsistencies_DetectsAnomalies()
        {
            // Arrange
            var item = CreateTestClipboardItem("Test item");

            // Créer une collection avec des anomalies
            _viewModel.HistoryItems.Clear();
            _viewModel.HistoryItems.Add(CreateTestClipboardItem("Valid item 1"));
            _viewModel.HistoryItems.Add(null!); // Élément null - anomalie
            _viewModel.HistoryItems.Add(CreateTestClipboardItem("Valid item 2"));
            _viewModel.HistoryItems.Add(CreateTestClipboardItem("Duplicate content")); // Contenu dupliqué
            _viewModel.HistoryItems.Add(CreateTestClipboardItem("Duplicate content")); // Contenu dupliqué

            var context = DeletionResultContext.CreateSuccess(item, _viewModel);

            // Act
            _logger.LogDeletionResult(context);

            // Assert
            var metrics = _metricsCollector.GetMetrics();
            Assert.That(metrics.TotalDeletions, Is.EqualTo(1));

            // Vérifier que les anomalies ont été détectées - le formatter génère "ÉTAT DES COLLECTIONS" dans le contenu, pas dans les logs
            // Le logger utilise LogDebug pour ses propres messages
            _mockLoggingService.Verify(
                x => x.LogDebug(It.Is<string>(s => s.Contains("Logging terminé"))),
                Times.Once);
        }

        [Test]
        public void CompleteWorkflow_WithExceptionInValidation_HandlesGracefullyAndRecordsError()
        {
            // Arrange
            var item = CreateTestClipboardItem("Test item");

            // Créer un ViewModel qui va causer une exception
            _viewModel.Dispose(); // Dispose pour causer des problèmes

            var context = DeletionResultContext.CreateSuccess(item, _viewModel);

            // Act & Assert - Ne doit pas lever d'exception
            Assert.DoesNotThrow(() => _logger.LogDeletionResult(context));

            // Vérifier qu'une erreur a été enregistrée
            var errorMetrics = _metricsCollector.GetErrorMetrics();
            Assert.That(errorMetrics.TotalErrors, Is.GreaterThanOrEqualTo(0)); // Peut être 0 si l'exception est gérée différemment
        }

        #endregion

        #region Performance Tests

        [Test]
        public void CompleteWorkflow_MeasuresPerformanceCorrectly()
        {
            // Arrange
            var item = CreateTestClipboardItem("Performance test item");
            var context = DeletionResultContext.CreateSuccess(item, _viewModel);

            // Act
            var startTime = DateTime.Now;
            _logger.LogDeletionResult(context);
            var endTime = DateTime.Now;

            // Assert
            var performanceMetrics = _metricsCollector.GetPerformanceMetrics();
            Assert.That(performanceMetrics.TotalLoggingOperations, Is.EqualTo(1));
            Assert.That(performanceMetrics.AverageDuration, Is.GreaterThan(TimeSpan.Zero));
            Assert.That(performanceMetrics.AverageDuration, Is.LessThan(endTime - startTime));
        }

        [Test]
        public async Task CompleteWorkflowAsync_PerformanceIsReasonable()
        {
            // Arrange
            var item = CreateTestClipboardItem("Async performance test");
            var context = DeletionResultContext.CreateSuccess(item, _viewModel);

            // Act
            var startTime = DateTime.Now;
            await _logger.LogDeletionResultAsync(context);
            var endTime = DateTime.Now;

            // Assert
            var totalTime = endTime - startTime;
            Assert.That(totalTime, Is.LessThan(TimeSpan.FromSeconds(1)),
                "Le logging ne devrait pas prendre plus d'une seconde");

            var performanceMetrics = _metricsCollector.GetPerformanceMetrics();
            Assert.That(performanceMetrics.AverageDuration, Is.LessThan(totalTime));
        }

        #endregion

        #region Stress Tests

        [Test]
        public async Task CompleteWorkflow_WithManyOperations_RemainsStable()
        {
            // Arrange
            const int operationCount = 50;
            var operations = new List<Task>();

            // Act
            for (int i = 0; i < operationCount; i++)
            {
                var item = CreateTestClipboardItem($"Stress test item {i}");
                var context = DeletionResultContext.CreateSuccess(item, _viewModel);
                operations.Add(_logger.LogDeletionResultAsync(context));
            }

            await Task.WhenAll(operations);

            // Assert
            var metrics = _metricsCollector.GetMetrics();
            Assert.That(metrics.TotalDeletions, Is.EqualTo(operationCount));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(operationCount));

            var performanceMetrics = _metricsCollector.GetPerformanceMetrics();
            Assert.That(performanceMetrics.TotalLoggingOperations, Is.EqualTo(operationCount));
            Assert.That(performanceMetrics.AverageDuration, Is.GreaterThan(TimeSpan.Zero));
        }

        #endregion

        #region Metrics Report Tests

        [Test]
        public void CompleteWorkflow_GeneratesComprehensiveMetricsReport()
        {
            // Arrange
            var successItem = CreateTestClipboardItem("Success item");
            var failureItem = CreateTestClipboardItem("Failure item");

            var successContext = DeletionResultContext.CreateSuccess(successItem, _viewModel);
            var failureContext = DeletionResultContext.CreateFailure(failureItem, "Test failure", _viewModel);

            // Act
            _logger.LogDeletionResult(successContext);
            _logger.LogDeletionResult(failureContext);

            // Assert - MODIFIÉ : GenerateMetricsReport supprimée, on teste les métriques directement
            var metrics = _metricsCollector.GetMetrics();
            Assert.That(metrics, Is.Not.Null);
            Assert.That(metrics.TotalDeletions, Is.EqualTo(2));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(1));
            Assert.That(metrics.FailedDeletions, Is.EqualTo(1));
        }

        #endregion

        #region Helper Methods

        private List<ClipboardItem> CreateTestClipboardItems(int count)
        {
            var items = new List<ClipboardItem>();
            for (int i = 0; i < count; i++)
            {
                items.Add(CreateTestClipboardItem($"Test item {i + 1}"));
            }
            return items;
        }

        private ClipboardItem CreateTestClipboardItem(string content)
        {
            return new ClipboardItem
            {
                Id = DateTime.Now.Ticks + Random.Shared.Next(),
                TextPreview = content,
                CustomName = $"Custom {content}",
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes(content),
                Timestamp = DateTime.Now,
                IsPinned = false,
                OrderIndex = 0
            };
        }

        #endregion
    }
}
