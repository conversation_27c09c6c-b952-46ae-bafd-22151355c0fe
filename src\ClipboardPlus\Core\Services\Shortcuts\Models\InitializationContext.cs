using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Shortcuts.Models
{
    /// <summary>
    /// Contexte d'initialisation pour les raccourcis globaux.
    /// Encapsule tous les paramètres nécessaires à l'initialisation.
    /// </summary>
    public class InitializationContext
    {
        /// <summary>
        /// Initialise une nouvelle instance de InitializationContext.
        /// </summary>
        /// <param name="defaultShortcut">Le raccourci par défaut à enregistrer.</param>
        public InitializationContext(KeyCombination defaultShortcut)
        {
            DefaultShortcut = defaultShortcut ?? throw new ArgumentNullException(nameof(defaultShortcut));
        }

        /// <summary>
        /// Le raccourci par défaut à enregistrer.
        /// </summary>
        public KeyCombination DefaultShortcut { get; }

        /// <summary>
        /// Indique si l'initialisation doit être forcée même en cas d'erreur.
        /// </summary>
        public bool ForceInitialization { get; init; } = false;

        /// <summary>
        /// Timeout pour l'initialisation en millisecondes.
        /// </summary>
        public int TimeoutMs { get; init; } = 5000;

        /// <summary>
        /// Indique si les logs détaillés doivent être activés.
        /// </summary>
        public bool EnableDetailedLogging { get; init; } = true;
    }
}
