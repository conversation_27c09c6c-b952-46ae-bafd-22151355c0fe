using System;
using System.IO;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour les opérations sur le système de fichiers.
    /// Abstrait les appels directs à File.* pour améliorer la testabilité.
    /// Respecte le principe d'inversion de dépendance (DIP).
    /// </summary>
    public interface IFileSystemService
    {
        /// <summary>
        /// Vérifie si un fichier existe.
        /// </summary>
        /// <param name="path">Le chemin du fichier à vérifier</param>
        /// <returns>True si le fichier existe, false sinon</returns>
        /// <exception cref="ArgumentNullException">Levée si path est null</exception>
        /// <exception cref="ArgumentException">Levée si path est vide ou contient des caractères invalides</exception>
        bool FileExists(string path);

        /// <summary>
        /// Vérifie si un répertoire existe.
        /// </summary>
        /// <param name="path">Le chemin du répertoire à vérifier</param>
        /// <returns>True si le répertoire existe, false sinon</returns>
        /// <exception cref="ArgumentNullException">Levée si path est null</exception>
        /// <exception cref="ArgumentException">Levée si path est vide ou contient des caractères invalides</exception>
        bool DirectoryExists(string path);

        /// <summary>
        /// Obtient les informations sur un fichier.
        /// </summary>
        /// <param name="path">Le chemin du fichier</param>
        /// <returns>Les informations sur le fichier, ou null si le fichier n'existe pas</returns>
        /// <exception cref="ArgumentNullException">Levée si path est null</exception>
        FileInfo? GetFileInfo(string path);

        /// <summary>
        /// Obtient la taille d'un fichier en octets.
        /// </summary>
        /// <param name="path">Le chemin du fichier</param>
        /// <returns>La taille du fichier en octets, ou null si le fichier n'existe pas</returns>
        /// <exception cref="ArgumentNullException">Levée si path est null</exception>
        long? GetFileSize(string path);

        /// <summary>
        /// Obtient la date de dernière modification d'un fichier.
        /// </summary>
        /// <param name="path">Le chemin du fichier</param>
        /// <returns>La date de dernière modification, ou null si le fichier n'existe pas</returns>
        /// <exception cref="ArgumentNullException">Levée si path est null</exception>
        DateTime? GetLastWriteTime(string path);

        /// <summary>
        /// Valide qu'un chemin de fichier est syntaxiquement correct.
        /// </summary>
        /// <param name="path">Le chemin à valider</param>
        /// <returns>True si le chemin est valide, false sinon</returns>
        bool IsValidPath(string? path);
    }
}
