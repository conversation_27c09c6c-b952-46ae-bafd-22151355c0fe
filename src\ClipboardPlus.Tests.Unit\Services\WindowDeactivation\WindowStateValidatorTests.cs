using System;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.WindowDeactivation;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.WindowDeactivation
{
    /// <summary>
    /// Tests unitaires pour WindowStateValidator.
    /// </summary>
    [TestFixture]
    public class WindowStateValidatorTests
    {
        private Mock<ILoggingService>? _mockLoggingService;
        private WindowStateValidator? _validator;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _validator = new WindowStateValidator(_mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new WindowStateValidator(null!));
        }

        [Test]
        public void ShouldIgnoreDeactivation_WhenIsClosingTrue_ShouldReturnTrue()
        {
            // Act
            var result = _validator!.ShouldIgnoreDeactivation(isClosing: true, isOperationInProgress: false);

            // Assert
            Assert.That(result, Is.True);
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains("Fenêtre en cours de fermeture"))), Times.Once);
        }

        [Test]
        public void ShouldIgnoreDeactivation_WhenOperationInProgressTrue_ShouldReturnTrue()
        {
            // Act
            var result = _validator!.ShouldIgnoreDeactivation(isClosing: false, isOperationInProgress: true);

            // Assert
            Assert.That(result, Is.True);
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains("Opération ViewModel en cours"))), Times.Once);
        }

        [Test]
        public void ShouldIgnoreDeactivation_WhenBothFalse_ShouldReturnFalse()
        {
            // Act
            var result = _validator!.ShouldIgnoreDeactivation(isClosing: false, isOperationInProgress: false);

            // Assert
            Assert.That(result, Is.False);
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains("Aucune condition d'arrêt précoce"))), Times.Once);
        }

        [Test]
        public void ValidateWindowState_WithNullContext_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _validator!.ValidateWindowState(null!));
        }

        [Test]
        public void ValidateWindowState_WhenIsClosingTrue_ShouldReturnWindowClosingResult()
        {
            // Arrange
            var context = new WindowStateValidationContext
            {
                IsClosing = true,
                IsOperationInProgress = false,
                WindowName = "TestWindow"
            };

            // Act
            var result = _validator!.ValidateWindowState(context);

            // Assert
            Assert.That(result.ShouldIgnore, Is.True);
            Assert.That(result.ValidationType, Is.EqualTo(WindowStateValidationType.WindowClosing));
            Assert.That(result.Reason, Is.EqualTo("Fenêtre en cours de fermeture"));
            Assert.That(result.Details, Contains.Substring("TestWindow"));
        }

        [Test]
        public void ValidateWindowState_WhenOperationInProgressTrue_ShouldReturnOperationInProgressResult()
        {
            // Arrange
            var context = new WindowStateValidationContext
            {
                IsClosing = false,
                IsOperationInProgress = true,
                WindowName = "TestWindow"
            };

            // Act
            var result = _validator!.ValidateWindowState(context);

            // Assert
            Assert.That(result.ShouldIgnore, Is.True);
            Assert.That(result.ValidationType, Is.EqualTo(WindowStateValidationType.OperationInProgress));
            Assert.That(result.Reason, Is.EqualTo("Opération ViewModel en cours"));
            Assert.That(result.Details, Contains.Substring("TestWindow"));
        }

        [Test]
        public void ValidateWindowState_WhenBothFalse_ShouldReturnNormalResult()
        {
            // Arrange
            var context = new WindowStateValidationContext
            {
                IsClosing = false,
                IsOperationInProgress = false,
                WindowName = "TestWindow",
                IsActive = true
            };

            // Act
            var result = _validator!.ValidateWindowState(context);

            // Assert
            Assert.That(result.ShouldIgnore, Is.False);
            Assert.That(result.ValidationType, Is.EqualTo(WindowStateValidationType.Normal));
            Assert.That(result.Reason, Is.EqualTo("Aucune condition d'arrêt précoce"));
            Assert.That(result.Details, Contains.Substring("TestWindow"));
        }

        [Test]
        public void ValidateWindowState_ShouldLogValidationStart()
        {
            // Arrange
            var context = new WindowStateValidationContext
            {
                IsClosing = false,
                IsOperationInProgress = false,
                WindowName = "TestWindow"
            };

            // Act
            _validator!.ValidateWindowState(context);

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s =>
                s.Contains("[WindowStateValidator]") &&
                s.Contains("Validation pour 'TestWindow'"))), Times.Once);
        }

        [Test]
        public void ValidateWindowState_ShouldSetTimestamp()
        {
            // Arrange
            var context = new WindowStateValidationContext
            {
                IsClosing = false,
                IsOperationInProgress = false,
                WindowName = "TestWindow"
            };

            var beforeValidation = DateTime.Now.AddMilliseconds(-10); // Marge de sécurité

            // Act
            var result = _validator!.ValidateWindowState(context);

            // Assert
            Assert.That(result.Details, Is.Not.Null);
            // Le timestamp du contexte devrait être proche de maintenant
            Assert.That(context.Timestamp, Is.GreaterThanOrEqualTo(beforeValidation));
            Assert.That(context.Timestamp, Is.LessThanOrEqualTo(DateTime.Now.AddSeconds(1)));
        }

        [Test]
        public void ValidateWindowState_WithPriorityIsClosing_ShouldIgnoreOperationInProgress()
        {
            // Arrange - Les deux conditions sont vraies, mais IsClosing a la priorité
            var context = new WindowStateValidationContext
            {
                IsClosing = true,
                IsOperationInProgress = true,
                WindowName = "TestWindow"
            };

            // Act
            var result = _validator!.ValidateWindowState(context);

            // Assert
            Assert.That(result.ShouldIgnore, Is.True);
            Assert.That(result.ValidationType, Is.EqualTo(WindowStateValidationType.WindowClosing));
            Assert.That(result.Reason, Is.EqualTo("Fenêtre en cours de fermeture"));
        }
    }
}
