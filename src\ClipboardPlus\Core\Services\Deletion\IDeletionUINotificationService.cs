namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Service de notifications simplifié spécifique aux opérations de suppression UI.
    /// Encapsule la logique de notification pour réduire la complexité cyclomatique de SupprimerElement().
    /// </summary>
    public interface IDeletionUINotificationService
    {
        /// <summary>
        /// Notifie le succès d'une suppression.
        /// </summary>
        /// <param name="itemName">Nom de l'élément supprimé</param>
        /// <param name="operationId">ID de l'opération pour le tracking</param>
        void NotifySuccess(string itemName, string operationId);

        /// <summary>
        /// Notifie l'échec d'une suppression.
        /// </summary>
        /// <param name="itemName">Nom de l'élément qui n'a pas pu être supprimé</param>
        /// <param name="errorMessage">Message d'erreur</param>
        /// <param name="operationId">ID de l'opération pour le tracking</param>
        void NotifyError(string itemName, string errorMessage, string operationId);

        /// <summary>
        /// Notifie qu'un rollback UI a été effectué.
        /// </summary>
        /// <param name="itemName">Nom de l'élément restauré</param>
        /// <param name="operationId">ID de l'opération pour le tracking</param>
        void NotifyRollback(string itemName, string operationId);
    }
}
