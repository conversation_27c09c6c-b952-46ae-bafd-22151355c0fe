using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Windows
{
    /// <summary>
    /// Service responsable de l'affichage et de la gestion de la fenêtre d'historique du presse-papiers.
    /// Cette interface encapsule la logique d'ouverture de la fenêtre d'historique qui était précédemment
    /// dans SystemTrayService.ShowHistoryWindow().
    /// </summary>
    public interface IHistoryWindowService
    {
        /// <summary>
        /// Affiche la fenêtre d'historique du presse-papiers.
        /// Cette méthode gère l'activation de la fenêtre existante ou la création d'une nouvelle instance.
        /// </summary>
        /// <returns>Une tâche représentant l'opération asynchrone d'affichage de la fenêtre.</returns>
        Task ShowHistoryWindowAsync();
    }
}
