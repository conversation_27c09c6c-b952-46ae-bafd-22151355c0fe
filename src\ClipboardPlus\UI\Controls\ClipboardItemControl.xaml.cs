using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.UI.Windows;
using System.Windows.Forms;
using MessageBox = System.Windows.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;
using WpfListBox = System.Windows.Controls.ListBox;
using WpfKeyEventArgs = System.Windows.Input.KeyEventArgs;
using WpfApplication = System.Windows.Application;
using WpfMessageBox = System.Windows.MessageBox;
using WpfMessageBoxResult = System.Windows.MessageBoxResult;

namespace ClipboardPlus.UI.Controls
{
    /// <summary>
    /// Logique d'interaction pour ClipboardItemControl.xaml
    /// </summary>
    public partial class ClipboardItemControl : System.Windows.Controls.UserControl
    {
        private ClipboardItem? _currentItem;
        private ClipboardHistoryViewModel? _viewModel;
        private bool _propertyChangedHandlerRegistered = false;
        private bool _viewModelSearchAttempted = false; // Pour éviter des recherches répétées qui échouent
        private readonly ILoggingService? _loggingService;

        // ANCIEN SYSTÈME SUPPRIMÉ - Plus de propriétés de dépendance obsolètes
        // Le système SOLID gère maintenant toute la visibilité

        // ANCIEN SYSTÈME SUPPRIMÉ - Plus de propriétés CLR obsolètes
        // Le système SOLID gère maintenant toute la visibilité

        /// <summary>
        /// Initialise une nouvelle instance du contrôle d'élément du presse-papiers.
        /// </summary>
        public ClipboardItemControl() : this(null)
        {
        }

        /// <summary>
        /// Initialise une nouvelle instance du contrôle d'élément du presse-papiers avec injection de dépendances.
        /// </summary>
        /// <param name="loggingService">Service de logging injecté (optionnel)</param>
        public ClipboardItemControl(ILoggingService? loggingService)
        {
            InitializeComponent();

            // Injection de dépendances : utiliser le service fourni ou tenter de le résoudre
            _loggingService = loggingService ?? GetLoggingService();

            // VÉRIFIER LES RESSOURCES XAML
            try
            {
                var titleConverter = this.FindResource("TitleVisibilityConverter");
            }
            catch (Exception)
            {
                // Ignorer les erreurs de ressources
            }

            try
            {
                var timestampConverter = this.FindResource("TimestampVisibilityConverter");
            }
            catch (Exception)
            {
                // Ignorer les erreurs de ressources
            }

            // Ajouter un événement pour le chargement du contrôle
            this.Loaded += ClipboardItemControl_Loaded;

            // S'abonner aux changements de DataContext pour mettre à jour l'état de renommage
            DataContextChanged += ClipboardItemControl_DataContextChanged;
            this.Unloaded += ClipboardItemControl_Unloaded;
        }

        private void ClipboardItemControl_Loaded(object sender, RoutedEventArgs e)
        {
            RegisterPropertyChangedHandler();
            CheckRenameState();

            // Réinitialiser le flag de recherche de ViewModel lors du chargement
            _viewModelSearchAttempted = false;

            // Diagnostics additionnels
            var item = DataContext as ClipboardItem;
            if (item != null)
            {
                _currentItem = item;
                _loggingService?.LogInfo($"[CONTRÔLE] ClipboardItemControl chargé pour l'élément {item.Id} - Système SOLID actif");

                // VÉRIFIER LES BINDINGS ET CONVERTISSEURS
                var titleBlock = DisplayNameTextBlock;
                if (titleBlock != null)
                {
                    // VÉRIFIER LE BINDING
                    var binding = System.Windows.Data.BindingOperations.GetBinding(titleBlock, System.Windows.Controls.TextBlock.VisibilityProperty);
                    if (binding != null)
                    {
                        // Binding trouvé
                    }
                    else
                    {
                        // Aucun binding trouvé
                    }

                    _loggingService?.LogInfo($"[CONTRÔLE] TextBlock titre - Visibilité={titleBlock.Visibility}, Texte='{titleBlock.Text}'");
                }
                else
                {
                    // DisplayNameTextBlock est NULL
                }
            }
            else
            {
                // DataContext n'est pas un ClipboardItem
            }
        }

        /// <summary>
        /// Gère le changement de DataContext.
        /// </summary>
        private void ClipboardItemControl_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            Debug.WriteLine("DataContextChanged: " + (e.NewValue?.GetType().Name ?? "null"));

            // DIAGNOSTIC CRITIQUE : Vérifier le DataContext et IsTitleVisible
            System.Diagnostics.Debug.WriteLine($"[DATACONTEXT_DIAGNOSTIC] ClipboardItemControl DataContext changed - Old: {e.OldValue?.GetType().Name}, New: {e.NewValue?.GetType().Name}");
            if (e.NewValue is ClipboardItem item)
            {
                System.Diagnostics.Debug.WriteLine($"[DATACONTEXT_DIAGNOSTIC] ClipboardItem received - ID: {item.Id}, CustomName: '{item.CustomName}', IsTitleVisible: {item.IsTitleVisible}");
            }
            
            // Désabonner de l'ancien item si nécessaire
            if (_currentItem != null && _currentItem is INotifyPropertyChanged oldNotifier)
            {
                oldNotifier.PropertyChanged -= Item_PropertyChanged;
                Debug.WriteLine($"Désabonnement de l'élément {_currentItem.Id}");
            }
            
            // Désabonner de l'ancien ViewModel si nécessaire
            if (_viewModel != null && _viewModel is INotifyPropertyChanged oldVmNotifier)
            {
                oldVmNotifier.PropertyChanged -= ViewModel_PropertyChanged;
                Debug.WriteLine("Désabonnement de l'ancien ViewModel");
                _propertyChangedHandlerRegistered = false; // Réinitialiser pour permettre un nouvel abonnement
            }
            
            // Mettre à jour la référence à l'élément actuel
            _currentItem = DataContext as ClipboardItem;
            
            // Réinitialiser le flag de recherche de ViewModel lors du changement de DataContext
            _viewModelSearchAttempted = false;
            
            // Vérifier si l'élément est en cours de renommage
            RegisterPropertyChangedHandler();
            CheckRenameState();
        }
        
        /// <summary>
        /// Gère le déchargement du contrôle.
        /// </summary>
        private void ClipboardItemControl_Unloaded(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("ClipboardItemControl_Unloaded: " + (_currentItem?.Id.ToString() ?? "null"));
            // Désabonner des événements pour éviter les fuites de mémoire
            if (_currentItem != null && _currentItem is INotifyPropertyChanged itemNotifier)
            {
                itemNotifier.PropertyChanged -= Item_PropertyChanged;
                Debug.WriteLine($"Désabonnement de l'élément {_currentItem.Id} lors de Unloaded");
            }
            if (_viewModel != null && _viewModel is INotifyPropertyChanged vmNotifier)
            {
                vmNotifier.PropertyChanged -= ViewModel_PropertyChanged;
                Debug.WriteLine("Désabonnement du ViewModel lors de Unloaded");
            }
            _propertyChangedHandlerRegistered = false;
            _viewModel = null; // Libérer la référence au ViewModel
            _currentItem = null; // Libérer la référence à l'item
        }
        
        /// <summary>
        /// S'abonne aux événements PropertyChanged de l'élément actuel et du ViewModel
        /// </summary>
        private void RegisterPropertyChangedHandler()
        {
            // Éviter les abonnements multiples
            if (_propertyChangedHandlerRegistered)
                return;
                
            // S'abonner à l'élément actuel si possible
            if (_currentItem != null && _currentItem is INotifyPropertyChanged itemNotifier)
            {
                itemNotifier.PropertyChanged += Item_PropertyChanged;
                Debug.WriteLine($"Abonnement à l'élément {_currentItem.Id}");
            }
            
            // Trouver et s'abonner au ViewModel
            _viewModel = FindViewModel();
            if (_viewModel != null && _viewModel is INotifyPropertyChanged vmNotifier)
            {
                vmNotifier.PropertyChanged += ViewModel_PropertyChanged;
                Debug.WriteLine("Abonnement au ViewModel");
                _propertyChangedHandlerRegistered = true;
            }
        }
        
        /// <summary>
        /// Gère les changements de propriété de l'élément
        /// </summary>
        private void Item_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            Debug.WriteLine($"Item_PropertyChanged: {e.PropertyName}");
            // Si le nom personnalisé change, la liaison de données devrait automatiquement mettre à jour l'affichage
            // La mise à jour manuelle via UpdateTarget() est généralement redondante si INotifyPropertyChanged est correctement implémenté.
            if (e.PropertyName == nameof(ClipboardItem.CustomName))
            {
                // Dispatcher.InvokeAsync(() => {
                // Debug.WriteLine("Mise à jour de l'affichage du nom personnalisé via UpdateTarget (supprimé)");
                // DisplayNameTextBlock.GetBindingExpression(TextBlock.TextProperty)?.UpdateTarget();
                // });
            }
        }
        
        /// <summary>
        /// Gère les changements de propriété du ViewModel
        /// </summary>
        private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            Debug.WriteLine($"ViewModel_PropertyChanged: {e.PropertyName}");
            
            try
            {
                // Si l'élément en cours de renommage change, vérifier l'état de renommage
                if (e.PropertyName == nameof(ClipboardHistoryViewModel.ItemEnRenommage))
                {
                    Dispatcher.InvokeAsync(CheckRenameState);
                }
                // Si la collection d'éléments change, vérifier si notre élément est toujours là
                else if (e.PropertyName == "HistoryItems" && _currentItem != null && _viewModel != null)
                {
                    if (!_viewModel.HistoryItems.Contains(_currentItem))
                    {
                        Debug.WriteLine("L'élément actuel n'est plus dans la collection");
                        // L'élément a été supprimé, rien à faire de spécial ici
                        // Le contrôle sera probablement recyclé/détruit
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ViewModel_PropertyChanged exception: {ex.Message}");
            }
        }

        /// <summary>
        /// Vérifie si l'élément est en cours de renommage et met à jour l'interface en conséquence.
        /// CORRECTION : Évite la boucle infinie en utilisant un cache local.
        /// </summary>
        private void CheckRenameState()
        {
            try
            {
                // Vérifier que nous avons un élément valide
                if (DataContext is not ClipboardItem item)
                {
                    Debug.WriteLine("CheckRenameState: DataContext is not ClipboardItem");
                    return;
                }

                Debug.WriteLine($"CheckRenameState: Item ID={item.Id}, CustomName={item.CustomName ?? "null"}");

                // Obtenir le ViewModel si nécessaire
                if (_viewModel == null)
                {
                    _viewModel = FindViewModel();
                    if (_viewModel == null)
                    {
                        Debug.WriteLine("CheckRenameState: ViewModel not found");
                        return;
                    }
                }

                // 🔧 CORRECTION BOUCLE INFINIE : Utiliser une variable locale pour éviter l'appel répétitif au getter
                // Au lieu d'appeler _viewModel.ItemEnRenommage (qui déclenche PropertyChanged),
                // on utilise une logique basée sur l'état des contrôles UI
                bool isCurrentlyInEditMode = EditNameTextBox.Visibility == Visibility.Visible;

                // Déterminer si on devrait être en mode édition basé sur l'état du ViewModel
                // On ne lit ItemEnRenommage que si on n'est pas déjà en mode édition pour éviter la boucle
                bool shouldBeInEditMode = false;
                if (!isCurrentlyInEditMode)
                {
                    // Seulement lire ItemEnRenommage si on n'est pas déjà en mode édition
                    var itemEnRenommage = _viewModel.ItemEnRenommage;
                    shouldBeInEditMode = itemEnRenommage != null && itemEnRenommage.Id == item.Id;
                    Debug.WriteLine($"CheckRenameState: ItemEnRenommage={itemEnRenommage?.Id.ToString() ?? "null"}, shouldBeInEditMode={shouldBeInEditMode}");
                }
                else
                {
                    // Si on est déjà en mode édition, on reste en mode édition
                    shouldBeInEditMode = true;
                    Debug.WriteLine("CheckRenameState: Déjà en mode édition, maintien de l'état");
                }

                // Mettre à jour la visibilité seulement si nécessaire
                if (isCurrentlyInEditMode != shouldBeInEditMode)
                {
                    EditNameTextBox.Visibility = shouldBeInEditMode ? Visibility.Visible : Visibility.Collapsed;
                    Debug.WriteLine($"CheckRenameState: Visibilité mise à jour - EditMode: {shouldBeInEditMode}");
                }

                // === CORRECTION FINALE : NE PAS TOUCHER AU DISPLAYNAME TEXTBLOCK ===
                // On laisse le binding XAML sur DisplayNameTextBlock.Visibility faire TOUT le travail.
                // On ne le force plus à Collapsed ou Visible ici.
                // La seule chose qui le masquera est le fait que le TextBox d'édition,
                // lorsqu'il est visible, se superposera au TextBlock.
                // Ceci est géré par la disposition des contrôles dans le XAML.

                if (shouldBeInEditMode && !isCurrentlyInEditMode)
                {
                    // Seulement configurer le TextBox si on entre en mode édition
                    EditNameTextBox.Text = _viewModel.NouveauNom ?? item.CustomName ?? "";
                    Dispatcher.BeginInvoke(new Action(() => {
                        try
                        {
                            EditNameTextBox.Focus();
                            EditNameTextBox.SelectAll();
                            Debug.WriteLine("CheckRenameState: Focus and selection set");
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"CheckRenameState focus exception: {ex.Message}");
                        }
                    }), System.Windows.Threading.DispatcherPriority.Input);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"CheckRenameState exception: {ex.Message}");
            }
        }

        /// <summary>
        /// Trouve le ViewModel parent avec une mise en cache pour éviter des recherches répétées.
        /// </summary>
        private ClipboardHistoryViewModel? FindViewModel()
        {
            try
            {
                // Si nous avons déjà un ViewModel valide, le retourner directement
                if (_viewModel != null)
                {
                    return _viewModel;
                }
                
                // Si nous avons déjà essayé de trouver le ViewModel sans succès, ne pas réessayer
                // sauf si explicitement demandé (en réinitialisant _viewModelSearchAttempted)
                if (_viewModelSearchAttempted)
                {
                    Debug.WriteLine("FindViewModel: Recherche déjà tentée sans succès, abandon");
                    return null;
                }
                
                Debug.WriteLine("FindViewModel: Recherche du ViewModel...");
                
                // Méthode 1: Chercher le ListBox parent qui contient le contrôle (méthode la plus fiable)
                var listBox = this.FindParentOfType<WpfListBox>();
                if (listBox != null)
                {
                    var viewModel = listBox.DataContext as ClipboardHistoryViewModel;
                    if (viewModel != null)
                    {
                        Debug.WriteLine("FindViewModel: ViewModel trouvé via ListBox parent");
                        _viewModel = viewModel;
                        return viewModel;
                    }
                }
                
                // Méthode 2: Chercher dans les ancêtres visuels
                DependencyObject? current = this;
                int searchDepth = 0;
                const int MAX_SEARCH_DEPTH = 20; // Éviter les recherches infinies
                
                while (current != null && searchDepth < MAX_SEARCH_DEPTH)
                {
                    searchDepth++;
                    
                    if (current is FrameworkElement fe && fe.DataContext is ClipboardHistoryViewModel feVm)
                    {
                        Debug.WriteLine($"FindViewModel: ViewModel trouvé via ancêtre visuel (profondeur: {searchDepth})");
                        _viewModel = feVm;
                        return feVm;
                    }
                    
                    // Essayer de trouver le ListBox dans l'arbre visuel
                    if (current is WpfListBox lb && lb.DataContext is ClipboardHistoryViewModel lbVm)
                    {
                        Debug.WriteLine($"FindViewModel: ViewModel trouvé via ListBox dans l'arbre visuel (profondeur: {searchDepth})");
                        _viewModel = lbVm;
                        return lbVm;
                    }
                    
                    current = VisualTreeHelper.GetParent(current);
                }
                
                if (searchDepth >= MAX_SEARCH_DEPTH)
                {
                    Debug.WriteLine("FindViewModel: Profondeur de recherche maximale atteinte");
                }
                
                // Méthode 3: Chercher via la fenêtre principale
                if (WpfApplication.Current?.MainWindow?.DataContext is ClipboardHistoryViewModel mainVm)
                {
                    Debug.WriteLine("FindViewModel: ViewModel trouvé via MainWindow");
                    _viewModel = mainVm;
                    return mainVm;
                }
                
                // Méthode 4: Chercher via le service provider (si disponible)
                try {
                    if (WpfApplication.Current != null)
                    {
                        var serviceProvider = WpfApplication.Current.GetType().GetProperty("Services")?.GetValue(WpfApplication.Current) as IServiceProvider;
                        if (serviceProvider != null)
                        {
                            var vm = serviceProvider.GetService(typeof(ClipboardHistoryViewModel)) as ClipboardHistoryViewModel;
                            if (vm != null)
                            {
                                Debug.WriteLine("FindViewModel: ViewModel trouvé via ServiceProvider");
                                _viewModel = vm;
                                return vm;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"FindViewModel: Erreur lors de la recherche via ServiceProvider: {ex.Message}");
                }
                
                // Marquer que nous avons essayé de trouver le ViewModel
                _viewModelSearchAttempted = true;
                Debug.WriteLine("FindViewModel: Aucun ViewModel trouvé");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"FindViewModel exception: {ex.Message}");
                _viewModelSearchAttempted = true;
                return null;
            }
        }

        /// <summary>
        /// Gère les touches pressées dans le TextBox d'édition.
        /// </summary>
        private void EditNameTextBox_KeyDown(object sender, WpfKeyEventArgs e)
        {
            try
            {
                Debug.WriteLine($"EditNameTextBox_KeyDown: Key={e.Key}");
                
                // Obtenir le ViewModel si nécessaire
                if (_viewModel == null)
                {
                    _viewModel = FindViewModel();
                    if (_viewModel == null)
                    {
                        Debug.WriteLine("EditNameTextBox_KeyDown: ViewModel not found");
                        RenamingDiagnostic.LogRenameResult(false, "ViewModel non trouvé dans EditNameTextBox_KeyDown");
                        return;
                    }
                }
                
                // Gérer les touches spéciales
                if (e.Key == Key.Enter)
                {
                    // Journaliser l'état avant confirmation
                    RenamingDiagnostic.LogRenameConfirmAttempt(_viewModel, EditNameTextBox);
                    
                    // Confirmer le renommage
                    Debug.WriteLine("EditNameTextBox_KeyDown: Executing ConfirmerRenommageCommand");
                    if (_viewModel.ConfirmerRenommageCommand.CanExecute(null))
                    {
                        try
                        {
                            _viewModel.ConfirmerRenommageCommand.Execute(null);
                            RenamingDiagnostic.LogRenameResult(true, "Commande exécutée avec succès via Enter");
                            
                            // Déplacer le focus pour éviter le rectangle en pointillés
                            try
                            {
                                Debug.WriteLine("EditNameTextBox_KeyDown: Déplacement du focus pour éviter le rectangle en pointillés");
                                
                                // Trouver la fenêtre parente
                                var window = Window.GetWindow(this);
                                if (window != null)
                                {
                                    // Donner le focus à la fenêtre elle-même
                                    window.Focus();
                                    e.Handled = true;
                                    
                                    // Journaliser l'état du focus après déplacement
                                    var historyListBox = ControlExtensions.FindParentOfType<WpfListBox>(this);
                                    RenamingDiagnostic.LogFocusState(Keyboard.FocusedElement as UIElement, historyListBox);
                                }
                            }
                            catch (Exception focusEx)
                            {
                                Debug.WriteLine($"EditNameTextBox_KeyDown: Erreur lors du déplacement du focus: {focusEx.Message}");
                            }
                        }
                        catch (Exception cmdEx)
                        {
                            RenamingDiagnostic.LogRenameException(cmdEx, "Exécution de ConfirmerRenommageCommand via Enter");
                        }
                    }
                    else
                    {
                        RenamingDiagnostic.LogRenameResult(false, "ConfirmerRenommageCommand.CanExecute a retourné false");
                    }
                    e.Handled = true;
                }
                else if (e.Key == Key.Escape)
                {
                    // Annuler le renommage
                    Debug.WriteLine("EditNameTextBox_KeyDown: Executing AnnulerRenommageCommand");
                    if (_viewModel.AnnulerRenommageCommand.CanExecute(null))
                    {
                        try
                        {
                            _viewModel.AnnulerRenommageCommand.Execute(null);
                            RenamingDiagnostic.LogRenameResult(true, "Commande d'annulation exécutée avec succès via Escape");
                        }
                        catch (Exception cmdEx)
                        {
                            RenamingDiagnostic.LogRenameException(cmdEx, "Exécution de AnnulerRenommageCommand via Escape");
                        }
                    }
                    else
                    {
                        RenamingDiagnostic.LogRenameResult(false, "AnnulerRenommageCommand.CanExecute a retourné false");
                    }
                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"EditNameTextBox_KeyDown exception: {ex.Message}");
                RenamingDiagnostic.LogRenameException(ex, "Méthode EditNameTextBox_KeyDown");
            }
        }

        /// <summary>
        /// Gère la perte de focus du TextBox d'édition.
        /// </summary>
        private void EditNameTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            try
            {
                Debug.WriteLine("EditNameTextBox_LostFocus");
                
                // Obtenir le ViewModel si nécessaire
                if (_viewModel == null)
                {
                    _viewModel = FindViewModel();
                    if (_viewModel == null)
                    {
                        Debug.WriteLine("EditNameTextBox_LostFocus: ViewModel not found");
                        RenamingDiagnostic.LogRenameResult(false, "ViewModel non trouvé dans EditNameTextBox_LostFocus");
                        return;
                    }
                }
                
                // Journaliser l'état avant confirmation
                RenamingDiagnostic.LogRenameConfirmAttempt(_viewModel, EditNameTextBox);
                
                // Confirmer le renommage automatiquement à la perte de focus
                Debug.WriteLine("EditNameTextBox_LostFocus: Executing ConfirmerRenommageCommand");
                if (_viewModel.ConfirmerRenommageCommand.CanExecute(null))
                {
                    try
                    {
                        _viewModel.ConfirmerRenommageCommand.Execute(null);
                        RenamingDiagnostic.LogRenameResult(true, "Commande exécutée avec succès via LostFocus");
                    }
                    catch (Exception cmdEx)
                    {
                        RenamingDiagnostic.LogRenameException(cmdEx, "Exécution de ConfirmerRenommageCommand via LostFocus");
                    }
                }
                else
                {
                    RenamingDiagnostic.LogRenameResult(false, "ConfirmerRenommageCommand.CanExecute a retourné false dans LostFocus");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"EditNameTextBox_LostFocus exception: {ex.Message}");
                RenamingDiagnostic.LogRenameException(ex, "Méthode EditNameTextBox_LostFocus");
            }
        }

        /// <summary>
        /// Méthode de diagnostic pour tester directement les commandes du ViewModel
        /// </summary>
        private void TestViewModelCommands()
        {
            try
            {
                if (_currentItem == null)
                {
                    Debug.WriteLine("TestViewModelCommands: _currentItem est null");
                    WpfMessageBox.Show("L'élément actuel est null", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                var vm = FindViewModel();
                if (vm == null)
                {
                    Debug.WriteLine("TestViewModelCommands: ViewModel non trouvé");
                    WpfMessageBox.Show("ViewModel non trouvé", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                // Tester les commandes
                Debug.WriteLine($"TestViewModelCommands: ViewModel trouvé, testing commands");
                
                // Vérifier si les commandes sont initialisées
                if (vm.DemarrerRenommageCommand == null)
                {
                    Debug.WriteLine("TestViewModelCommands: DemarrerRenommageCommand est null");
                    WpfMessageBox.Show("DemarrerRenommageCommand est null", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                // Vérifier si la commande peut être exécutée
                bool canExecute = vm.DemarrerRenommageCommand.CanExecute(_currentItem);
                Debug.WriteLine($"TestViewModelCommands: DemarrerRenommageCommand.CanExecute = {canExecute}");
                
                // Exécuter la commande directement
                if (canExecute)
                {
                    Debug.WriteLine("TestViewModelCommands: Executing DemarrerRenommageCommand");
                    vm.DemarrerRenommageCommand.Execute(_currentItem);
                    WpfMessageBox.Show("Commande exécutée avec succès", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    Debug.WriteLine("TestViewModelCommands: Cannot execute DemarrerRenommageCommand");
                    WpfMessageBox.Show("La commande ne peut pas être exécutée", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"TestViewModelCommands exception: {ex.Message}");
                WpfMessageBox.Show($"Erreur: {ex.Message}", "Diagnostic", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }




        









        /// <summary>
        /// Affiche un feedback visuel à l'utilisateur
        /// </summary>
        /// <param name="success">Indique si l'opération a réussi</param>
        /// <param name="message">Message optionnel à afficher</param>
        private void ShowFeedback(bool success, string? message = null)
        {
            try
            {
                // Animation visuelle sur le contrôle
                if (success)
                {
                    // Animation de succès (légère pulsation verte)
                    var originalBackground = this.Background;
                    var successBrush = new SolidColorBrush(System.Windows.Media.Color.FromArgb(40, 0, 200, 0));
                    
                    this.Background = successBrush;
                    
                    // Restaurer l'arrière-plan d'origine après un délai
                    var timer = new System.Windows.Threading.DispatcherTimer
                    {
                        Interval = TimeSpan.FromMilliseconds(300)
                    };
                    
                    timer.Tick += (s, e) =>
                    {
                        this.Background = originalBackground;
                        timer.Stop();
                    };
                    
                    timer.Start();
                }
                else
                {
                    // Animation d'échec (légère pulsation rouge)
                    var originalBackground = this.Background;
                    var errorBrush = new SolidColorBrush(System.Windows.Media.Color.FromArgb(40, 200, 0, 0));
                    
                    this.Background = errorBrush;
                    
                    // Restaurer l'arrière-plan d'origine après un délai
                    var timer = new System.Windows.Threading.DispatcherTimer
                    {
                        Interval = TimeSpan.FromMilliseconds(300)
                    };
                    
                    timer.Tick += (s, e) =>
                    {
                        this.Background = originalBackground;
                        timer.Stop();
                    };
                    
                    timer.Start();
                    
                    // Afficher le message d'erreur si fourni
                    if (!string.IsNullOrEmpty(message))
                    {
                        Debug.WriteLine($"ShowFeedback: {message}");
                        
                        // Option 1: Afficher dans une infobulle (tooltip)
                        ToolTip = message;
                        
                        // Réinitialiser l'infobulle après un délai
                        var tooltipTimer = new System.Windows.Threading.DispatcherTimer
                        {
                            Interval = TimeSpan.FromSeconds(3)
                        };
                        
                        tooltipTimer.Tick += (s, e) =>
                        {
                            ToolTip = null;
                            tooltipTimer.Stop();
                        };
                        
                        tooltipTimer.Start();
                        
                        // Option 2: On pourrait aussi utiliser un système de notification global
                        // si l'application en dispose
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ShowFeedback exception: {ex.Message}");
                // Ne rien faire de plus pour éviter une boucle d'erreurs
            }
        }

        // ANCIEN SYSTÈME TOTALEMENT SUPPRIMÉ
        // OnHideItemTitleChanged et UpdateTitleVisibility supprimés
        // Le système SOLID gère maintenant toute la visibilité

        /// <summary>
        /// Récupère le service de logging via l'architecture de services.
        /// </summary>
        private ILoggingService? GetLoggingService()
        {
            try
            {
                // Méthode 1 : Tenter de résoudre via App.Services (architecture moderne)
                if (WpfApplication.Current is App app && app.Services != null)
                {
                    var loggingService = app.Services.GetService(typeof(ILoggingService)) as ILoggingService;
                    if (loggingService != null)
                    {
                        return loggingService;
                    }
                }

                // Méthode 2 : Fallback vers les ressources de l'application (méthode legacy)
                if (WpfApplication.Current?.Resources["LoggingService"] is ILoggingService resourceLoggingService)
                {
                    return resourceLoggingService;
                }
            }
            catch (Exception)
            {
                // Ignorer les erreurs de résolution de service
            }

            return null;
        }


    }

    /// <summary>
    /// Extensions pour les contrôles WPF.
    /// </summary>
    public static class ControlExtensions
    {
        /// <summary>
        /// Trouve un parent du type spécifié.
        /// </summary>
        /// <typeparam name="T">Type du parent à trouver.</typeparam>
        /// <param name="element">Élément à partir duquel chercher.</param>
        /// <returns>Le parent trouvé ou null.</returns>
        public static T? FindParentOfType<T>(this DependencyObject element) where T : DependencyObject
        {
            DependencyObject? parent = VisualTreeHelper.GetParent(element);
            
            if (parent == null)
                return null;
                
            if (parent is T typedParent)
                return typedParent;
                
            return FindParentOfType<T>(parent);
        }
    }
}
