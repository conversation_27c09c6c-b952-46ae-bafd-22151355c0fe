using System;
using System.Drawing;
using System.Windows;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Implémentation du chargeur de ressources d'icône.
    /// Responsabilité unique : charger les icônes depuis les ressources avec fallback.
    /// </summary>
    public class IconResourceLoader : IIconResourceLoader
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de IconResourceLoader.
        /// </summary>
        /// <param name="loggingService">Service de logging pour enregistrer les opérations de chargement.</param>
        public IconResourceLoader(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <inheritdoc />
        public Icon LoadIconFromResource(string resourcePath)
        {
            if (string.IsNullOrWhiteSpace(resourcePath))
            {
                throw new ArgumentException("Le chemin de la ressource ne peut pas être vide.", nameof(resourcePath));
            }

            try
            {
                _loggingService.LogInfo($"IconResourceLoader: Tentative de chargement de l'icône depuis: {resourcePath}");

                // Vérifier d'abord si la ressource existe
                if (!ResourceExists(resourcePath))
                {
                    _loggingService.LogWarning($"IconResourceLoader: Ressource non trouvée: {resourcePath} - Utilisation de l'icône de fallback");
                    return GetFallbackIcon();
                }

                // Charger la ressource
                var iconUri = new Uri(resourcePath);
                using (var iconStream = System.Windows.Application.GetResourceStream(iconUri)?.Stream)
                {
                    if (iconStream != null)
                    {
                        _loggingService.LogInfo("IconResourceLoader: Stream de l'icône obtenu avec succès");
                        var icon = new Icon(iconStream);
                        _loggingService.LogInfo("IconResourceLoader: Icône chargée avec succès depuis les ressources");
                        return icon;
                    }
                    else
                    {
                        _loggingService.LogError("IconResourceLoader: Impossible d'obtenir le stream de l'icône");
                        return GetFallbackIcon();
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"IconResourceLoader: Erreur lors du chargement de l'icône depuis {resourcePath}: {ex.Message}", ex);
                _loggingService.LogInfo("IconResourceLoader: Utilisation de l'icône de fallback");
                return GetFallbackIcon();
            }
        }

        /// <inheritdoc />
        public Icon GetFallbackIcon()
        {
            try
            {
                _loggingService.LogInfo("IconResourceLoader: Utilisation de l'icône système par défaut");
                return SystemIcons.Application;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"IconResourceLoader: Erreur lors de l'obtention de l'icône de fallback: {ex.Message}", ex);
                throw; // Si même l'icône de fallback échoue, c'est un problème critique
            }
        }

        /// <inheritdoc />
        public bool ResourceExists(string resourcePath)
        {
            if (string.IsNullOrWhiteSpace(resourcePath))
            {
                return false;
            }

            try
            {
                var iconUri = new Uri(resourcePath);
                var resourceInfo = System.Windows.Application.GetResourceStream(iconUri);
                
                bool exists = resourceInfo != null;
                resourceInfo?.Stream?.Dispose(); // Nettoyer le stream si il existe
                
                _loggingService.LogInfo($"IconResourceLoader: Vérification d'existence de la ressource {resourcePath}: {exists}");
                
                return exists;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"IconResourceLoader: Erreur lors de la vérification d'existence de {resourcePath}: {ex.Message}");
                return false;
            }
        }
    }
}
