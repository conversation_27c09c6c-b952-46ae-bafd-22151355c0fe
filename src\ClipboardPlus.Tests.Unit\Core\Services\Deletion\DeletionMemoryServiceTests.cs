using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Core.Services.Deletion
{
    /// <summary>
    /// Tests unitaires pour DeletionMemoryService
    /// </summary>
    [TestFixture]
    public class DeletionMemoryServiceTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private DeletionMemoryService _service = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _service = new DeletionMemoryService(_mockLoggingService.Object);
        }

        private List<ClipboardItem> CreateTestItems()
        {
            return new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, TextPreview = "Item 1" },
                new ClipboardItem { Id = 2, TextPreview = "Item 2" },
                new ClipboardItem { Id = 3, TextPreview = "Item 3" }
            };
        }

        [Test]
        public void ExistsInMemory_WithExistingItem_ShouldReturnTrue()
        {
            // Arrange
            var items = CreateTestItems();

            // Act
            var result = _service.ExistsInMemory(2, items);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void ExistsInMemory_WithNonExistingItem_ShouldReturnFalse()
        {
            // Arrange
            var items = CreateTestItems();

            // Act
            var result = _service.ExistsInMemory(999, items);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void ExistsInMemory_WithNullCollection_ShouldReturnFalse()
        {
            // Act
            var result = _service.ExistsInMemory(1, null!);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void ExistsInMemory_WithEmptyCollection_ShouldReturnFalse()
        {
            // Arrange
            var items = new List<ClipboardItem>();

            // Act
            var result = _service.ExistsInMemory(1, items);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task RemoveFromMemoryAsync_WithNullCollection_ShouldReturnFailure()
        {
            // Arrange
            string operationId = "test-op";

            // Act
            var result = await _service.RemoveFromMemoryAsync(1, null, operationId);

            // Assert
            Assert.That(result.Success, Is.False);
            Assert.That(result.ExistedInMemory, Is.False);
            Assert.That(result.RemovedItem, Is.Null);
            Assert.That(result.Message, Does.Contain("collection historyItems est null"));

            // Verify logging
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(operationId) && s.Contains("Tentative de suppression mémoire"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(operationId) && s.Contains("collection historyItems est null"))), Times.Once);
        }

        [Test]
        public async Task RemoveFromMemoryAsync_WithNonExistingItem_ShouldReturnSuccessButNotExisted()
        {
            // Arrange
            var items = CreateTestItems();
            string operationId = "test-op";
            long nonExistingId = 999;

            // Act
            var result = await _service.RemoveFromMemoryAsync(nonExistingId, items, operationId);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.ExistedInMemory, Is.False);
            Assert.That(result.RemovedItem, Is.Null);
            Assert.That(result.Message, Does.Contain($"Élément avec ID {nonExistingId} non trouvé en mémoire"));

            // Verify collection unchanged
            Assert.That(items.Count, Is.EqualTo(3));

            // Verify logging
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(operationId) && s.Contains("Tentative de suppression mémoire"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("non trouvé en mémoire"))), Times.Once);
        }

        [Test]
        public async Task RemoveFromMemoryAsync_WithExistingItem_ShouldReturnSuccessAndRemoveItem()
        {
            // Arrange
            var items = CreateTestItems();
            string operationId = "test-op";
            long existingId = 2;
            var originalItem = items.First(x => x.Id == existingId);

            // Act
            var result = await _service.RemoveFromMemoryAsync(existingId, items, operationId);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.ExistedInMemory, Is.True);
            Assert.That(result.RemovedItem, Is.Not.Null);
            Assert.That(result.RemovedItem.Id, Is.EqualTo(existingId));
            Assert.That(result.RemovedItem.TextPreview, Is.EqualTo(originalItem.TextPreview));
            Assert.That(result.Message, Does.Contain($"Élément avec ID {existingId} supprimé de la mémoire avec succès"));

            // Verify item was removed from collection
            Assert.That(items.Count, Is.EqualTo(2));
            Assert.That(items.Any(x => x.Id == existingId), Is.False);

            // Verify logging
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(operationId) && s.Contains("Tentative de suppression mémoire"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("supprimé de la mémoire avec succès"))), Times.Once);
        }

        [Test]
        public void FindItemInMemory_WithExistingItem_ShouldReturnItem()
        {
            // Arrange
            var items = CreateTestItems();
            long existingId = 2;

            // Act
            var result = _service.FindItemInMemory(existingId, items);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(existingId));
            Assert.That(result.TextPreview, Is.EqualTo("Item 2"));
        }

        [Test]
        public void FindItemInMemory_WithNonExistingItem_ShouldReturnNull()
        {
            // Arrange
            var items = CreateTestItems();

            // Act
            var result = _service.FindItemInMemory(999, items);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public void FindItemInMemory_WithNullCollection_ShouldReturnNull()
        {
            // Act
            var result = _service.FindItemInMemory(1, null!);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<System.ArgumentNullException>(() => new DeletionMemoryService(null!));
        }

        [Test]
        public async Task RemoveFromMemoryAsync_ShouldLogCorrectMessages()
        {
            // Arrange
            var items = CreateTestItems();
            string operationId = "detailed-test";
            long itemId = 1;

            // Act
            await _service.RemoveFromMemoryAsync(itemId, items, operationId);

            // Assert - Verify specific log messages
            _mockLoggingService.Verify(x => x.LogDebug($"[{operationId}] Tentative de suppression mémoire pour ID: {itemId}"), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains($"[{operationId}]") && s.Contains("supprimé de la mémoire avec succès"))), Times.Once);
        }
    }

    /// <summary>
    /// Tests pour les méthodes statiques de MemoryDeletionResult
    /// </summary>
    [TestFixture]
    public class MemoryDeletionResultTests
    {
        [Test]
        public void CreateSuccess_WithBasicParameters_ShouldReturnValidResult()
        {
            // Arrange
            bool existedInMemory = true;
            var item = new ClipboardItem { Id = 1, TextPreview = "Test" };
            string message = "Success message";

            // Act
            var result = MemoryDeletionResult.CreateSuccess(existedInMemory, item, message);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.ExistedInMemory, Is.EqualTo(existedInMemory));
            Assert.That(result.RemovedItem, Is.EqualTo(item));
            Assert.That(result.Message, Is.EqualTo(message));
        }

        [Test]
        public void CreateSuccess_WithMinimalParameters_ShouldReturnValidResult()
        {
            // Act
            var result = MemoryDeletionResult.CreateSuccess(false);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.ExistedInMemory, Is.False);
            Assert.That(result.RemovedItem, Is.Null);
            Assert.That(result.Message, Is.Null);
        }

        [Test]
        public void CreateFailure_ShouldReturnValidFailureResult()
        {
            // Arrange
            string message = "Failure message";

            // Act
            var result = MemoryDeletionResult.CreateFailure(message);

            // Assert
            Assert.That(result.Success, Is.False);
            Assert.That(result.ExistedInMemory, Is.False);
            Assert.That(result.RemovedItem, Is.Null);
            Assert.That(result.Message, Is.EqualTo(message));
        }
    }
}
