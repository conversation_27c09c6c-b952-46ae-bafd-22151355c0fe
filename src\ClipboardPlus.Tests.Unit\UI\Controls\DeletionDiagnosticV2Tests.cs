using System;
using System.IO;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.Controls;
using ClipboardPlus.UI.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.UI.Controls
{
    [TestFixture]
    public class DeletionDiagnosticV2Tests
    {
        private DeletionDiagnostic _deletionDiagnostic = null!;
        private ClipboardHistoryViewModel _testViewModel = null!;
        private string _testLogDirectory = null!;
        private string _testLogFile = null!;
        private IServiceProvider _serviceProvider = null!;

        [SetUp]
        public void SetUp()
        {
            // Créer un répertoire de test temporaire
            _testLogDirectory = Path.Combine(Path.GetTempPath(), "ClipboardPlusTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testLogDirectory);
            _testLogFile = Path.Combine(_testLogDirectory, "deletion_diagnostic.log");

            // Configurer les services pour créer un vrai ViewModel
            _serviceProvider = ClipboardPlus.Services.Configuration.HostConfiguration.ConfigureServices();
            
            // Créer l'instance à tester
            _deletionDiagnostic = new DeletionDiagnostic();

            // Créer un vrai ViewModel pour les tests
            _testViewModel = _serviceProvider.GetRequiredService<ClipboardHistoryViewModel>();
        }

        [TearDown]
        public void TearDown()
        {
            // Nettoyer les services
            if (_serviceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }

            // Nettoyer les fichiers de test
            if (Directory.Exists(_testLogDirectory))
            {
                Directory.Delete(_testLogDirectory, true);
            }
        }

        [Test]
        public void LogDeletionStart_V2_WithNullItem_ShouldNotThrow()
        {
            // Arrange
            ClipboardItem? nullItem = null;

            // Act & Assert - Ne doit pas lever d'exception
            Assert.DoesNotThrow(() => _deletionDiagnostic.LogDeletionStart_V2(nullItem, _testViewModel));
        }

        [Test]
        public void LogDeletionStart_V2_WithValidItem_ShouldNotThrow()
        {
            // Arrange
            var testItem = new ClipboardItem
            {
                Id = 123,
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.Now,
                TextPreview = "Test content for V2"
            };

            // Act & Assert - Ne doit pas lever d'exception
            Assert.DoesNotThrow(() => _deletionDiagnostic.LogDeletionStart_V2(testItem, _testViewModel));
        }

        [Test]
        public void LogDeletionStart_V2_WithCustomMessage_ShouldNotThrow()
        {
            // Arrange
            var testItem = new ClipboardItem
            {
                Id = 456,
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.Now,
                TextPreview = "Test content with custom message"
            };
            var customMessage = "Test de suppression avec message personnalisé V2";

            // Act & Assert - Ne doit pas lever d'exception
            Assert.DoesNotThrow(() => _deletionDiagnostic.LogDeletionStart_V2(testItem, _testViewModel, customMessage));
        }

        [Test]
        public void LogDeletionStart_V2_BothOverloads_ShouldProduceLogs()
        {
            // Arrange
            var testItem = new ClipboardItem
            {
                Id = 789,
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.Now,
                TextPreview = "Test content for both overloads V2"
            };
            var customMessage = "Message personnalisé pour test V2";

            // Act - Appeler les deux surcharges
            _deletionDiagnostic.LogDeletionStart_V2(testItem, _testViewModel);
            _deletionDiagnostic.LogDeletionStart_V2(testItem, _testViewModel, customMessage);

            // Assert - Vérifier que les appels se sont exécutés sans erreur
            // Note: Ce test vérifie principalement la non-régression
            Assert.Pass("Les deux surcharges s'exécutent sans erreur");
        }

        [Test]
        public void LogDeletionStart_V2_PerformanceTest_ShouldCompleteQuickly()
        {
            // Arrange
            var testItem = new ClipboardItem
            {
                Id = 999,
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.Now,
                TextPreview = "Performance test content V2"
            };

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act - Mesurer le temps d'exécution
            for (int i = 0; i < 10; i++)
            {
                _deletionDiagnostic.LogDeletionStart_V2(testItem, _testViewModel);
            }

            stopwatch.Stop();

            // Assert - Vérifier que l'exécution est raisonnablement rapide (moins de 5 secondes pour 10 appels)
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(5000), 
                $"LogDeletionStart_V2 a pris {stopwatch.ElapsedMilliseconds}ms pour 10 appels, ce qui est trop lent");
        }

        [Test]
        public void LogDeletionStart_V2_WithNullViewModel_ShouldHandleGracefully()
        {
            // Arrange
            var testItem = new ClipboardItem
            {
                Id = 111,
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.Now,
                TextPreview = "Test with null ViewModel"
            };

            // Act & Assert - Doit gérer gracieusement le ViewModel null
            Assert.DoesNotThrow(() => _deletionDiagnostic.LogDeletionStart_V2(testItem, null!));
        }
    }
}
