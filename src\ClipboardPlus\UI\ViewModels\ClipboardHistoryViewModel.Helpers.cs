using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.Core.Services.NewItem.Implementations;
using ClipboardPlus.Diagnostics;
using ClipboardPlus.UI.Helpers;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using WpfApplication = System.Windows.Application;

namespace ClipboardPlus.UI.ViewModels
{


    public partial class ClipboardHistoryViewModel
    {






        /// <summary>
        /// Détermine si le code s'exécute dans un environnement de test.
        /// REFACTORISÉ : Utilise maintenant ITestEnvironmentDetector injecté (architecture SOLID).
        /// Fallback vers TestEnvironmentDetector par défaut si aucun service n'est injecté.
        /// </summary>
        public bool IsInTestEnvironment()
        {
            try
            {
                // Utiliser le service injecté si disponible
                if (_testEnvironmentDetector != null)
                {
                    return _testEnvironmentDetector.IsInTestEnvironment();
                }

                // Fallback : créer une instance par défaut de TestEnvironmentDetector
                var fallbackDetector = new TestEnvironmentDetector(_loggingService);
                return fallbackDetector.IsInTestEnvironment();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors de la détection de l'environnement de test: {ex.Message}", ex);
                return false; // En cas d'erreur, considérer comme mode normal
            }
        }

        /// <summary>
        /// Force la notification de changement de propriété pour un élément
        /// </summary>
        private void NotifyPropertyChangedFor(ClipboardItem? item)
        {
            _loggingService?.LogInfo($"[DÉBUT] NotifyPropertyChangedFor - ThreadID: {Environment.CurrentManagedThreadId}, Item: {(item?.Id.ToString() ?? "null")}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            if (item == null)
            {
                _loggingService?.LogWarning("NotifyPropertyChangedFor: Item est null, sortie prématurée");
                return;
            }

            _loggingService?.LogInfo($"NotifyPropertyChangedFor: Item={item.Id}, CustomName='{item.CustomName}', IsPinned={item.IsPinned}, DataType={item.DataType}");

            try
            {
                if (WpfApplication.Current?.Dispatcher == null)
                {
                    _loggingService?.LogError("NotifyPropertyChangedFor: WpfApplication.Current.Dispatcher est null");
                    return;
                }

                WpfApplication.Current.Dispatcher.Invoke(() =>
                {
                    _loggingService?.LogInfo($"NotifyPropertyChangedFor (Dispatcher): Début de l'exécution dans le thread UI");
                    int index = HistoryItems.IndexOf(item);
                    if (index >= 0 && index < HistoryItems.Count)
                    {
                        ClipboardItem tempItem = item;
                        HistoryItems.RemoveAt(index);
                        HistoryItems.Insert(index, tempItem);
                        OnPropertyChanged(nameof(HistoryItems));
                        _loggingService?.LogInfo("NotifyPropertyChangedFor: Mise à jour de l'élément terminée avec succès");
                    }
                    else
                    {
                        _loggingService?.LogWarning($"NotifyPropertyChangedFor: Item not found in HistoryItems (Count: {HistoryItems.Count})");
                         string itemIds = string.Join(", ", HistoryItems.Select(i => i.Id.ToString()));
                        _loggingService?.LogInfo($"NotifyPropertyChangedFor: IDs des éléments présents: [{itemIds}]");
                        try
                        {
                            HistoryItems.Add(item);
                            OnPropertyChanged(nameof(HistoryItems));
                        }
                        catch (Exception addEx)
                        {
                            _loggingService?.LogError($"NotifyPropertyChangedFor - Erreur lors de l'ajout de l'élément: {addEx.Message}", addEx);
                        }
                    }
                });
                _loggingService?.LogInfo($"[FIN] NotifyPropertyChangedFor - Succès pour Item: {item.Id}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"NotifyPropertyChangedFor - EXCEPTION GÉNÉRALE: {ex.Message}", ex);
                _loggingService?.LogInfo($"[FIN] NotifyPropertyChangedFor - Échec: {ex.Message}");
            }
        }

        /// <summary>
        /// Journalise le début d'une tentative de suppression d'un élément
        /// </summary>
        public void LogDeletionStart(ClipboardItem? item)
        {
            _deletionDiagnostic?.LogDeletionStart_V2(item, this);
        }

        /// <summary>
        /// Journalise le résultat d'une tentative de suppression d'un élément
        /// </summary>
        public void LogDeletionResult(bool success, ClipboardItem? item, string? message = null)
        {
            // Utiliser le nouveau système de logging si disponible, sinon fallback vers l'ancien
            if (_deletionResultLogger != null)
            {
                var context = success && item != null
                    ? DeletionResultContext.CreateSuccess(item, this, message)
                    : DeletionResultContext.CreateFailure(item, message ?? "Suppression échouée", this);

                _ = Task.Run(async () => await _deletionResultLogger.LogDeletionResultAsync(context));
            }
            else
            {
                // Fallback vers le système de diagnostic
                _deletionDiagnostic?.LogDeletionResult(success, item, message, this);
            }
        }

        /// <summary>
        /// Journalise une exception survenue lors d'une tentative de suppression
        /// </summary>
        public void LogDeletionException(Exception ex, string context)
        {
            _deletionDiagnostic?.LogDeletionException(ex, context);
        }

        /// <summary>
        /// Journalise l'état de la collection à un moment donné
        /// </summary>
        public void LogCollectionState(string context)
        {
            _deletionDiagnostic?.LogCollectionState(this, context);
        }

        /// <summary>
        /// Réinitialise l'état des opérations en cours.
        /// </summary>
        public void ResetOperationState()
        {
            _loggingService?.LogInfo($"ResetOperationState appelé - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            IsOperationInProgress = false;
        }

        private void ResetOperationFlagsWithTimer()
        {
            _loggingService?.LogInfo("ResetOperationFlagsWithTimer appelé depuis le ViewModel.");
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(150)
            };
            timer.Tick += (s, args) =>
            {
                timer.Stop();
                IsOperationInProgress = false;
                _isItemPasteInProgress = false;
                PasteSelectedItemCommand.NotifyCanExecuteChanged();
                _loggingService?.LogInfo("Flags d'opération de collage réinitialisés par timer.");
            };
            timer.Start();
        }

        /// <summary>
        /// Réinitialise l'état des opérations en cours de manière simplifiée
        /// </summary>
        public void ResetOperationStateSimple()
        {
            _loggingService?.LogInfo($"ResetOperationStateSimple appelé - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            IsOperationInProgress = false;
            _isItemPasteInProgress = false;
            PasteSelectedItemCommand.NotifyCanExecuteChanged();
        }

        /// <summary>
        /// Nettoie la base de données en supprimant tous les éléments qui ne sont pas présents
        /// dans la liste en mémoire. Délègue au ClipboardHistoryManager pour la logique métier.
        /// </summary>
        public async Task<int> PurgeOrphanedItemsAsync()
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            _loggingService?.LogInfo($"[{operationId}][DÉBUT] PurgeOrphanedItemsAsync - Thread: {Environment.CurrentManagedThreadId}");

            // Validations spécifiques au ViewModel
            if (IsInTestEnvironment())
            {
                _loggingService?.LogInfo($"[{operationId}] PurgeOrphanedItemsAsync: Mode test détecté, la purge des orphelins est désactivée.");
                return 0;
            }

            if (IsOperationInProgress)
            {
                _loggingService?.LogInfo($"[{operationId}] PurgeOrphanedItemsAsync: Opération en cours détectée, purge reportée.");
                return 0;
            }

            try
            {
                // Délégation DIRECTE au service éprouvé - les collections sont synchronisées
                var result = await _clipboardHistoryManager.PurgeOrphanedItemsAsync();
                _loggingService?.LogInfo($"[{operationId}][FIN] Délégation réussie - {result} élément(s) orphelin(s) supprimé(s)");
                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] PurgeOrphanedItemsAsync: Erreur lors de la délégation: {ex.Message}", ex);
                ErrorMessageHelper.ShowError("Une erreur est survenue lors du nettoyage des données.", "ClipboardPlus - Erreur de Purge", ex, $"[{operationId}] PurgeOrphanedItemsAsync", this);
                return 0;
            }
        }

        /// <summary>
        /// Filtre les éléments de l'historique en fonction du texte de recherche.
        /// </summary>
        private void FilterHistoryItems()
        {
            try
            {
                IsLoading = true;

                if (string.IsNullOrEmpty(SearchText))
                {
                    if (!_historyCollectionSynchronizer.IsSynchronizingCollections)
                    {
                         _ = _historyCollectionSynchronizer.LoadHistoryAsync("filter_clear");
                    }
                }
                else
                {
                    if (_clipboardHistoryManager.HistoryItems == null)
                    {
                        _loggingService?.LogWarning("FilterHistoryItems: _clipboardHistoryManager.HistoryItems est null");
                        return;
                    }

                    var historyCopy = new List<ClipboardItem>(_clipboardHistoryManager.HistoryItems);

                    var filteredItems = historyCopy
                        .Where(item =>
                            (item.TextPreview != null && item.TextPreview.Contains(SearchText, StringComparison.OrdinalIgnoreCase)) ||
                            (item.CustomName != null && item.CustomName.Contains(SearchText, StringComparison.OrdinalIgnoreCase)))
                        .ToList();

                    HistoryItems.Clear();
                    foreach (var item in filteredItems)
                    {
                        HistoryItems.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorMessageHelper.ShowError(
                    "Erreur lors du filtrage des éléments.",
                                "ClipboardPlus - Erreur",
                    ex,
                    "FilterHistoryItems",
                    this);
            }
            finally
            {
                IsLoading = false;
            }
        }
    }
}