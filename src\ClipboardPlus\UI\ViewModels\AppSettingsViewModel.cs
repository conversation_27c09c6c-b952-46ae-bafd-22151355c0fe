using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using System.Linq;
using System.Reflection;
using WpfMessageBox = System.Windows.MessageBox;
using ClipboardPlus.UI.Windows;
using WpfApplication = System.Windows.Application;
using CommunityToolkit.Mvvm.Messaging;
using ClipboardPlus.UI.Messages;

namespace ClipboardPlus.UI.ViewModels
{
    /// <summary>
    /// ViewModel pour la fenêtre des paramètres de l'application.
    /// </summary>
    public class AppSettingsViewModel : ObservableObject
    {
        private readonly ISettingsManager _settingsManager;
        private readonly IUserThemeManager _userThemeManager;
        private readonly IGlobalShortcutService _globalShortcutService;
        private readonly IUserNotificationService _userNotificationService;
        private readonly ILoggingService? _loggingService;

        private int _maxHistoryItemsVM;
        private bool _startWithWindowsVM;
        private string _shortcutTextVM;
        private ThemeInfo? _selectedThemeVM;
        private string _statusMessage;
        private bool _isBusy;
        private int _maxImageDimensionForThumbnailVM;
        private int _maxStorableItemSizeMBVM;
        private bool _hideTimestampVM;
        private bool _hideItemTitleVM;

        /// <summary>
        /// Instance du service de journalisation.
        /// </summary>
        public ILoggingService? LoggingService => _loggingService;

        /// <summary>
        /// Instance du gestionnaire de paramètres pour un accès direct.
        /// </summary>
        public ISettingsManager SettingsManager => _settingsManager;

        /// <summary>
        /// Collection des thèmes disponibles.
        /// </summary>
        public ObservableCollection<ThemeInfo> AvailableThemes { get; } = new ObservableCollection<ThemeInfo>();

        /// <summary>
        /// Nombre maximum d'éléments dans l'historique.
        /// </summary>
        public int MaxHistoryItemsVM
        {
            get => _maxHistoryItemsVM;
            set => SetProperty(ref _maxHistoryItemsVM, value);
        }

        /// <summary>
        /// Indique si l'application doit démarrer avec Windows.
        /// </summary>
        public bool StartWithWindowsVM
        {
            get => _startWithWindowsVM;
            set => SetProperty(ref _startWithWindowsVM, value);
        }

        /// <summary>
        /// Texte représentant le raccourci clavier global.
        /// </summary>
        public string ShortcutTextVM
        {
            get
            {
                _loggingService?.LogInfo($"[AppSettingsViewModel] GET ShortcutTextVM: '{_shortcutTextVM}'");
                return _shortcutTextVM;
            }
            set
            {
                _loggingService?.LogInfo($"[AppSettingsViewModel] SET ShortcutTextVM: '{_shortcutTextVM}' -> '{value}'");
                SetProperty(ref _shortcutTextVM, value);
            }
        }

        /// <summary>
        /// Thème sélectionné.
        /// </summary>
        public ThemeInfo? SelectedThemeVM
        {
            get => _selectedThemeVM;
            set
            {
                if (SetProperty(ref _selectedThemeVM, value) && value != null)
                {
                    ApplyThemeAsync(value).ConfigureAwait(false);
                }
            }
        }

        /// <summary>
        /// Message de statut pour l'utilisateur.
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// Indique si le ViewModel est occupé à effectuer une opération.
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        /// <summary>
        /// Dimension maximale pour les miniatures d'images (en pixels).
        /// </summary>
        public int MaxImageDimensionForThumbnailVM
        {
            get => _maxImageDimensionForThumbnailVM;
            set => SetProperty(ref _maxImageDimensionForThumbnailVM, value);
        }

        /// <summary>
        /// Taille maximale des éléments stockables (en Mo).
        /// </summary>
        public int MaxStorableItemSizeMBVM
        {
            get => _maxStorableItemSizeMBVM;
            set => SetProperty(ref _maxStorableItemSizeMBVM, value);
        }

        /// <summary>
        /// Indique si l'horodatage doit être masqué dans l'interface.
        /// </summary>
        public bool HideTimestampVM
        {
            get
            {
                _loggingService?.LogInfo($"[AppSettingsViewModel] GET HideTimestampVM: {_hideTimestampVM}");
                return _hideTimestampVM;
            }
            set
            {
                _loggingService?.LogInfo($"[AppSettingsViewModel] SET HideTimestampVM: {_hideTimestampVM} -> {value}");
                SetProperty(ref _hideTimestampVM, value);
            }
        }

        /// <summary>
        /// Indique si le titre de l'élément doit être masqué dans l'interface.
        /// </summary>
        public bool HideItemTitleVM
        {
            get
            {
                _loggingService?.LogInfo($"[AppSettingsViewModel] GET HideItemTitleVM: {_hideItemTitleVM}");
                return _hideItemTitleVM;
            }
            set
            {
                _loggingService?.LogInfo($"[AppSettingsViewModel] SET HideItemTitleVM: {_hideItemTitleVM} -> {value}");
                SetProperty(ref _hideItemTitleVM, value);
            }
        }

        /// <summary>
        /// Version de l'application, récupérée depuis les informations d'assemblage.
        /// </summary>
        public string ApplicationDisplayVersion => 
            Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0.0";

        /// <summary>
        /// Année courante pour l'affichage du copyright.
        /// </summary>
        public string CurrentYear => DateTime.Now.Year.ToString();

        /// <summary>
        /// Commande pour appliquer tous les paramètres et fermer la fenêtre.
        /// </summary>
        public IAsyncRelayCommand ApplyAllSettingsAndCloseCommand { get; }

        /// <summary>
        /// Commande pour annuler les modifications et fermer la fenêtre.
        /// </summary>
        public IRelayCommand CancelSettingsAndCloseCommand { get; }

        /// <summary>
        /// Commande pour appliquer les paramètres sans fermer la fenêtre.
        /// </summary>
        public IAsyncRelayCommand ApplySettingsCommand { get; }

        /// <summary>
        /// Commande pour ouvrir la fenêtre de capture de raccourci dédiée.
        /// </summary>
        public IRelayCommand OpenShortcutCaptureCommand { get; }

        /// <summary>
        /// Initialise une nouvelle instance de AppSettingsViewModel.
        /// </summary>
        /// <param name="settingsManager">Gestionnaire des paramètres pour l'état de la fenêtre.</param>
        /// <param name="userThemeManager">Gestionnaire des thèmes.</param>
        /// <param name="globalShortcutService">Service de gestion des raccourcis globaux.</param>
        /// <param name="userNotificationService">Service de notification à l'utilisateur.</param>
        /// <param name="loggingService">Service de journalisation.</param>
        public AppSettingsViewModel(
            ISettingsManager settingsManager,
            IUserThemeManager userThemeManager,
            IGlobalShortcutService globalShortcutService,
            IUserNotificationService userNotificationService,
            ILoggingService? loggingService = null)
        {
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
            _userThemeManager = userThemeManager ?? throw new ArgumentNullException(nameof(userThemeManager));
            _globalShortcutService = globalShortcutService ?? throw new ArgumentNullException(nameof(globalShortcutService));
            _userNotificationService = userNotificationService ?? throw new ArgumentNullException(nameof(userNotificationService));
            _loggingService = loggingService;

            // Initialiser les propriétés par défaut
            _maxHistoryItemsVM = 50;
            _startWithWindowsVM = false;
            _shortcutTextVM = "Ctrl+Alt+V";
            _statusMessage = string.Empty;
            _isBusy = false;
            _maxImageDimensionForThumbnailVM = 256;
            _maxStorableItemSizeMBVM = 10;
            _hideTimestampVM = false;
            _hideItemTitleVM = false;

            // Initialiser les commandes
            ApplySettingsCommand = new AsyncRelayCommand(ApplySettingsAsync);
            ApplyAllSettingsAndCloseCommand = new AsyncRelayCommand(ApplySettingsAndCloseAsync);
            CancelSettingsAndCloseCommand = new RelayCommand(CancelSettingsAndClose);
            OpenShortcutCaptureCommand = new RelayCommand(OpenShortcutCaptureWindow);
        }

        /// <summary>
        /// Initialise le ViewModel en chargeant les paramètres et les thèmes.
        /// </summary>
        /// <returns>Tâche asynchrone.</returns>
        public Task InitializeViewModelAsync()
        {
            try
            {
                _loggingService?.LogInfo($"[VM_INIT] InitializeViewModelAsync DÉBUT - Lecture depuis SettingsManager");
                IsBusy = true;
                StatusMessage = "Chargement des paramètres...";

                // Lire les valeurs depuis la source de vérité unique : ISettingsManager
                MaxHistoryItemsVM = _settingsManager.MaxHistoryItems;
                StartWithWindowsVM = _settingsManager.StartWithWindows;
                ShortcutTextVM = _settingsManager.ShortcutKeyCombination;
                MaxImageDimensionForThumbnailVM = _settingsManager.MaxImageDimensionForThumbnail;
                MaxStorableItemSizeMBVM = (int)(_settingsManager.MaxStorableItemSizeBytes / (1024 * 1024));
                HideTimestampVM = _settingsManager.HideTimestamp;
                HideItemTitleVM = _settingsManager.HideItemTitle;

                _loggingService?.LogInfo($"[VM_INIT] Valeurs lues depuis SettingsManager. Shortcut: '{ShortcutTextVM}'");

                // Charger les thèmes disponibles
                var themes = _userThemeManager.GetAvailableThemes();
                AvailableThemes.Clear();
                foreach (var theme in themes)
                {
                    AvailableThemes.Add(theme);
                }

                // Sélectionner le thème actif
                string activeThemePath = _settingsManager.ActiveThemePath;
                SelectedThemeVM = AvailableThemes.FirstOrDefault(t => t.FilePath == activeThemePath) ?? 
                                 AvailableThemes.FirstOrDefault(t => t.Name == "Default");

                StatusMessage = "Paramètres chargés avec succès.";
                _loggingService?.LogInfo($"[VM_INIT] InitializeViewModelAsync SUCCÈS.");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[VM_INIT] InitializeViewModelAsync ERREUR: {ex.Message}", ex);
                StatusMessage = $"Erreur lors du chargement des paramètres: {ex.Message}";
                _userNotificationService.ShowError("Erreur", $"Une erreur est survenue lors du chargement des paramètres: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// Applique un thème à l'application.
        /// </summary>
        /// <param name="theme">Thème à appliquer.</param>
        /// <returns>Tâche asynchrone.</returns>
        private async Task ApplyThemeAsync(ThemeInfo theme)
        {
            try
            {
                if (theme == null)
                    return;

                StatusMessage = $"Application du thème {theme.Name}...";
                IsBusy = true;

                // Appliquer le thème
                await _userThemeManager.ApplyThemeAsync(theme);

                // Sauvegarder le chemin du thème dans la source de vérité
                _settingsManager.ActiveThemePath = theme.FilePath;

                StatusMessage = $"Thème {theme.Name} appliqué avec succès.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Erreur lors de l'application du thème: {ex.Message}";
                _userNotificationService.ShowError("Erreur", $"Une erreur est survenue lors de l'application du thème: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }



        /// <summary>
        /// Applique les paramètres de manière orchestrée avec architecture SOLID.
        ///
        /// Cette méthode a été refactorisée pour utiliser une séparation claire des responsabilités :
        /// - SettingsValidationService : Validation des changements
        /// - SettingsApplicationService : Application des paramètres de base et avancés
        /// - SettingsPersistenceOrchestrator : Sauvegarde en persistance
        /// - SettingsNotificationService : Envoi des messages et notifications
        ///
        /// Gère automatiquement le rollback en cas d'erreur et affiche les messages appropriés.
        /// </summary>
        /// <returns>Tâche asynchrone représentant l'opération d'application des paramètres.</returns>
        private async Task ApplySettingsAsync()
        {
            _loggingService?.LogInfo("[VM_APPLY] ApplySettingsAsync DÉBUT");
            IsBusy = true;
            StatusMessage = "Application des paramètres...";

            try
            {
                // Vérification des dépendances critiques
                if (_loggingService == null)
                {
                    StatusMessage = "Erreur: Service de logging non disponible";
                    return;
                }

                // 1. Création des services orchestrateurs (injection temporaire)
                var settingsApplicationService = new ClipboardPlus.Services.Settings.SettingsApplicationService(
                    _settingsManager, _globalShortcutService, _userNotificationService, _loggingService);
                var settingsValidationService = new ClipboardPlus.Services.Settings.SettingsValidationService(_loggingService);
                var settingsNotificationService = new ClipboardPlus.Services.Settings.SettingsNotificationService(_loggingService);
                var settingsPersistenceOrchestrator = new ClipboardPlus.Services.Settings.SettingsPersistenceOrchestrator(
                    _settingsManager, _loggingService);

                // 2. Création des snapshots de données
                var currentSettings = CreateCurrentSettingsSnapshot();
                var newSettings = CreateNewSettingsSnapshot();

                // Log de compatibilité pour les tests de caractérisation
                _loggingService?.LogInfo("[VM_APPLY] Valeurs du ViewModel à appliquer:");

                // 3. Validation des changements
                var validationResult = await settingsValidationService.ValidateAllChangesAsync(currentSettings, newSettings);
                if (!validationResult.IsValid)
                {
                    HandleValidationErrors(validationResult);
                    return;
                }

                // 4. Application des paramètres de base avec rollback automatique
                var basicResult = await settingsApplicationService.ApplyBasicSettingsAsync(newSettings.BasicSettings);

                // Gestion du rollback pour le raccourci si échec
                if (!basicResult.Success && basicResult.Errors.Any(e => e.Contains("raccourci")))
                {
                    // Restaurer l'ancien raccourci dans le ViewModel ET dans le SettingsManager
                    ShortcutTextVM = currentSettings.BasicSettings.ShortcutKeyCombination;
                    _settingsManager.ShortcutKeyCombination = currentSettings.BasicSettings.ShortcutKeyCombination;
                    _loggingService?.LogInfo($"[VM_APPLY] Rollback raccourci ViewModel: '{ShortcutTextVM}'");
                }

                // 5. Application des paramètres avancés
                var advancedResult = await settingsApplicationService.ApplyAdvancedSettingsAsync(newSettings.AdvancedSettings);

                // 6. Persistance orchestrée
                var persistenceResult = await settingsPersistenceOrchestrator.SaveAllSettingsAsync(newSettings);

                // 7. Notifications et messages
                await settingsNotificationService.SendVisibilityChangedMessagesAsync(
                    new ClipboardPlus.Core.DataModels.Settings.VisibilitySettings(
                        newSettings.AdvancedSettings.HideTimestamp,
                        newSettings.AdvancedSettings.HideItemTitle));

                // 8. Finalisation
                var combinedResult = CombineResults(basicResult, advancedResult, persistenceResult);
                await settingsNotificationService.NotifySettingsAppliedAsync(combinedResult);

                // Définir le message de statut en fonction du résultat
                if (combinedResult.Success)
                {
                    StatusMessage = "Paramètres appliqués avec succès.";
                }
                else
                {
                    StatusMessage = $"Erreur lors de l'application des paramètres: {combinedResult.Message}";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[VM_APPLY] ApplySettingsAsync ERREUR", ex);
                HandleApplicationError(ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// Applique les paramètres et signale que la fenêtre doit être fermée.
        /// </summary>
        /// <returns>Tâche asynchrone.</returns>
        private async Task ApplySettingsAndCloseAsync()
        {
            try
            {
                // Vérifier si les services sont disponibles
                if (_userThemeManager == null)
                {
                    throw new InvalidOperationException("Le gestionnaire de thèmes n'est pas disponible.");
                }
                
                if (_globalShortcutService == null)
                {
                    throw new InvalidOperationException("Le service de raccourcis n'est pas disponible.");
                }
                
                // Appliquer les paramètres de manière sécurisée
                try
                {
                    // Indiquer que l'opération est en cours
                    IsBusy = true;
                    StatusMessage = "Application des paramètres en cours...";
                    
                    await ApplySettingsAsync();
                    
                    // L'indicateur IsBusy et le message de statut sont déjà gérés dans ApplySettingsAsync
                }
                catch (Exception ex)
                {
                    // Journaliser l'erreur
                    _loggingService?.LogError($"Erreur dans ApplySettingsAsync: {ex.Message}", ex);
                    
                    // Indiquer que l'opération a échoué (bien que cela soit normalement géré dans ApplySettingsAsync)
                    IsBusy = false;
                    StatusMessage = $"Erreur: {ex.Message}";
                    
                    // Propager l'exception pour être traitée par l'appelant
                    throw;
                }
            }
            catch (Exception ex)
            {
                // En cas d'erreur, nous la propageons pour qu'elle soit traitée par l'appelant
                StatusMessage = $"Erreur lors de l'application des paramètres: {ex.Message}";
                
                // Journaliser l'erreur
                _loggingService?.LogError($"Erreur dans ApplySettingsAndCloseAsync: {ex.Message}", ex);
                
                // Propager l'exception pour la gestion dans l'UI, mais avec plus d'informations
                throw new InvalidOperationException($"Échec de l'application des paramètres: {ex.Message}", ex);
            }
            finally
            {
                // S'assurer que IsBusy est remis à false
                IsBusy = false;
            }
        }

        /// <summary>
        /// Annule les modifications et signale que la fenêtre doit être fermée.
        /// </summary>
        private void CancelSettingsAndClose()
        {
            // La fenêtre sera fermée par le code-behind
        }

        /// <summary>
        /// Ouvre la fenêtre de capture de raccourci dédiée.
        /// </summary>
        private async void OpenShortcutCaptureWindow()
        {
            try
            {
                StatusMessage = "Ouverture de la fenêtre de capture de raccourci...";

                // Obtenir le fournisseur de services
                var app = System.Windows.Application.Current as App;
                if (app?.Services == null)
                {
                    throw new InvalidOperationException("Le conteneur de services n'a pas été initialisé correctement.");
                }

                // Créer la fenêtre de capture avec le raccourci actuel
                var captureWindow = await Windows.ShortcutCaptureWindow.CreateAsync(app.Services, ShortcutTextVM);

                // Configurer les événements
                captureWindow.Owner = System.Windows.Application.Current.MainWindow;

                // Afficher la fenêtre en mode modal
                bool? result = captureWindow.ShowDialog();

                if (result == true)
                {
                    // Récupérer le nouveau raccourci
                    string? newShortcut = captureWindow.GetCapturedShortcut();
                    if (!string.IsNullOrEmpty(newShortcut))
                    {
                        ShortcutTextVM = newShortcut;
                        StatusMessage = $"Raccourci modifié : {newShortcut}";
                    }
                    else
                    {
                        StatusMessage = "Aucun raccourci sélectionné.";
                    }
                }
                else
                {
                    StatusMessage = "Modification du raccourci annulée.";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Erreur lors de l'ouverture de la fenêtre: {ex.Message}";
                _userNotificationService.ShowError("Erreur", $"Impossible d'ouvrir la fenêtre de capture: {ex.Message}");
            }
        }

        #region Méthodes Helper pour ApplySettingsAsync

        /// <summary>
        /// Crée un snapshot des paramètres actuels.
        /// </summary>
        private ClipboardPlus.Core.DataModels.Settings.CompleteSettingsData CreateCurrentSettingsSnapshot()
        {
            var basicSettings = new ClipboardPlus.Core.DataModels.Settings.BasicSettingsData(
                _settingsManager.MaxHistoryItems,
                _settingsManager.StartWithWindows,
                _settingsManager.ShortcutKeyCombination,
                _settingsManager.MaxImageDimensionForThumbnail,
                _settingsManager.MaxStorableItemSizeBytes
            );

            var advancedSettings = new ClipboardPlus.Core.DataModels.Settings.AdvancedSettingsData(
                _settingsManager.HideTimestamp,
                _settingsManager.HideItemTitle,
                AvailableThemes?.FirstOrDefault(t => t.FilePath == _settingsManager.ActiveThemePath)
            );

            return new ClipboardPlus.Core.DataModels.Settings.CompleteSettingsData(basicSettings, advancedSettings);
        }

        /// <summary>
        /// Crée un snapshot des nouveaux paramètres depuis le ViewModel.
        /// </summary>
        private ClipboardPlus.Core.DataModels.Settings.CompleteSettingsData CreateNewSettingsSnapshot()
        {
            var basicSettings = new ClipboardPlus.Core.DataModels.Settings.BasicSettingsData(
                MaxHistoryItemsVM,
                StartWithWindowsVM,
                ShortcutTextVM,
                MaxImageDimensionForThumbnailVM,
                MaxStorableItemSizeMBVM * 1024L * 1024L // Conversion MB vers bytes
            );

            var advancedSettings = new ClipboardPlus.Core.DataModels.Settings.AdvancedSettingsData(
                HideTimestampVM,
                HideItemTitleVM,
                SelectedThemeVM
            );

            return new ClipboardPlus.Core.DataModels.Settings.CompleteSettingsData(basicSettings, advancedSettings);
        }

        /// <summary>
        /// Gère les erreurs de validation.
        /// </summary>
        private void HandleValidationErrors(ClipboardPlus.Core.DataModels.Settings.SettingsValidationResult validationResult)
        {
            _loggingService?.LogInfo($"[VM_APPLY] Erreurs de validation: {validationResult.ValidationErrors.Count}");

            foreach (var error in validationResult.ValidationErrors)
            {
                _loggingService?.LogInfo($"[VM_APPLY] -> Erreur: {error}");
            }

            StatusMessage = $"Erreurs de validation: {validationResult.Message}";
            _userNotificationService.ShowError("Erreurs de validation", validationResult.Message);
        }

        /// <summary>
        /// Combine les résultats de plusieurs opérations.
        /// </summary>
        private ClipboardPlus.Core.DataModels.Settings.SettingsApplicationResult CombineResults(
            ClipboardPlus.Core.DataModels.Settings.SettingsApplicationResult basicResult,
            ClipboardPlus.Core.DataModels.Settings.SettingsApplicationResult advancedResult,
            ClipboardPlus.Core.DataModels.Settings.PersistenceResult persistenceResult)
        {
            var combinedSettings = new Dictionary<string, object>();
            var combinedErrors = new List<string>();

            // Combiner les paramètres appliqués
            foreach (var setting in basicResult.AppliedSettings)
                combinedSettings[setting.Key] = setting.Value;
            foreach (var setting in advancedResult.AppliedSettings)
                combinedSettings[setting.Key] = setting.Value;

            // Combiner les erreurs
            combinedErrors.AddRange(basicResult.Errors);
            combinedErrors.AddRange(advancedResult.Errors);
            combinedErrors.AddRange(persistenceResult.Errors);

            var success = basicResult.Success && advancedResult.Success && persistenceResult.Success;
            var message = success ? "Tous les paramètres appliqués avec succès" : "Paramètres appliqués avec des erreurs";

            return new ClipboardPlus.Core.DataModels.Settings.SettingsApplicationResult(
                success, message, combinedSettings, combinedErrors);
        }

        /// <summary>
        /// Gère les erreurs d'application générales.
        /// </summary>
        private void HandleApplicationError(Exception ex)
        {
            _loggingService?.LogError("[VM_APPLY] ApplySettingsAsync ERREUR", ex);
            StatusMessage = $"Erreur lors de l'application des paramètres: {ex.Message}";
            _userNotificationService.ShowError("Erreur", $"Une erreur s'est produite lors de l'application des paramètres: {ex.Message}");
        }

        #endregion
    }
}