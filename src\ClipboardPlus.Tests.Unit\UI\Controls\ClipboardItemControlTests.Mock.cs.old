using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.Controls;
using ClipboardPlus.UI.ViewModels;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using ClipboardPlus.Tests.Unit.TestHelpers;
using CommunityToolkit.Mvvm.Input;

namespace ClipboardPlus.Tests.Unit.UI.Controls
{
    /// <summary>
    /// Tests unitaires pour ClipboardItemControl en utilisant une version mockée
    /// qui n'utilise pas le XAML pour éviter les problèmes de ressources
    /// </summary>
    [TestClass]
    public class ClipboardItemControlMockTests : UITestBase
    {
        /// <summary>
        /// Attribut personnalisé pour exécuter un test dans un thread STA
        /// </summary>
        public class STATestMethodAttribute : TestMethodAttribute
        {
            public override TestResult[] Execute(ITestMethod testMethod)
            {
                TestResult[]? result = null;
                var thread = new Thread(() =>
                {
                    // Exécution du test dans un thread STA
                    result = base.Execute(testMethod);
                });
                
                thread.SetApartmentState(ApartmentState.STA);
                thread.Start();
                thread.Join();
                
                return result;
            }
        }

        /// <summary>
        /// Mock de ClipboardHistoryViewModel pour les tests
        /// </summary>
        private class MockClipboardHistoryViewModel : IMockClipboardHistoryViewModel
        {
            public ObservableCollection<ClipboardItem> HistoryItems { get; } = new ObservableCollection<ClipboardItem>();
            
            private ClipboardItem? _itemEnRenommage;
            public ClipboardItem? ItemEnRenommage
            {
                get => _itemEnRenommage;
                set
                {
                    _itemEnRenommage = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ItemEnRenommage)));
                }
            }
            
            private string _nouveauNom = string.Empty;
            public string NouveauNom
            {
                get => _nouveauNom;
                set
                {
                    _nouveauNom = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(NouveauNom)));
                }
            }
            
            IRelayCommand<ClipboardItem> IMockClipboardHistoryViewModel.DemarrerRenommageCommand => DemarrerRenommageCommand;
            IRelayCommand IMockClipboardHistoryViewModel.AnnulerRenommageCommand => AnnulerRenommageCommand;
            IRelayCommand IMockClipboardHistoryViewModel.ConfirmerRenommageCommand => ConfirmerRenommageCommand;
            IRelayCommand<ClipboardItem> IMockClipboardHistoryViewModel.BasculerEpinglageCommand => BasculerEpinglageCommand;
            IRelayCommand<ClipboardItem> IMockClipboardHistoryViewModel.AfficherPreviewCommand => AfficherPreviewCommand;
            IRelayCommand<ClipboardItem> IMockClipboardHistoryViewModel.SupprimerElementCommand => SupprimerElementCommand;
            
            public TestRelayCommand<ClipboardItem> DemarrerRenommageCommand { get; } = new TestRelayCommand<ClipboardItem>();
            public TestRelayCommand AnnulerRenommageCommand { get; } = new TestRelayCommand();
            public TestRelayCommand ConfirmerRenommageCommand { get; } = new TestRelayCommand();
            public TestRelayCommand<ClipboardItem> BasculerEpinglageCommand { get; } = new TestRelayCommand<ClipboardItem>();
            public TestRelayCommand<ClipboardItem> AfficherPreviewCommand { get; } = new TestRelayCommand<ClipboardItem>();
            public TestRelayCommand<ClipboardItem> SupprimerElementCommand { get; } = new TestRelayCommand<ClipboardItem>();
            
            public event PropertyChangedEventHandler? PropertyChanged;
        }

        /// <summary>
        /// Command qui implémente IRelayCommand pour les tests
        /// </summary>
        private class TestRelayCommand : IRelayCommand
        {
            public bool CanExecuteValue { get; set; } = true;
            public bool WasExecuted { get; private set; }
            public object? LastParameter { get; private set; }

            public bool CanExecute(object? parameter) => CanExecuteValue;

            public void Execute(object? parameter)
            {
                WasExecuted = true;
                LastParameter = parameter;
            }
            
            public void NotifyCanExecuteChanged() { }

            public event EventHandler? CanExecuteChanged;
        }

        /// <summary>
        /// Command typée qui implémente IRelayCommand pour les tests
        /// </summary>
        private class TestRelayCommand<T> : IRelayCommand<T>
        {
            public bool CanExecuteValue { get; set; } = true;
            public bool WasExecuted { get; private set; }
            public T? LastParameter { get; private set; }

            public bool CanExecute(object? parameter) => CanExecuteValue;
            
            public bool CanExecute(T? parameter) => CanExecuteValue;

            public void Execute(object? parameter)
            {
                WasExecuted = true;
                if (parameter is T typedParam)
                {
                    LastParameter = typedParam;
                    Execute(typedParam);
                }
            }
            
            public void Execute(T? parameter)
            {
                WasExecuted = true;
                LastParameter = parameter;
            }
            
            public void NotifyCanExecuteChanged() { }

            public event EventHandler? CanExecuteChanged;
        }

        [TestInitialize]
        public void Initialize()
        {
            // Initialiser l'environnement de test UI
            InitializeUITestEnvironment();
        }

        /// <summary>
        /// Teste que la propriété CurrentItem est mise à jour lors du changement de DataContext
        /// </summary>
        [STATestMethod]
        public void DataContextChanged_UpdatesCurrentItem()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            var item = new ClipboardItem { Id = 1, CustomName = "Test Item" };
            
            // Act - Appeler directement la méthode de changement de DataContext
            control.ClipboardItemControl_DataContextChanged(control, 
                new DependencyPropertyChangedEventArgs(UserControl.DataContextProperty, null, item));
            
            // Assert
            Assert.IsNotNull(control.CurrentItem);
            Assert.AreEqual(1, control.CurrentItem.Id);
            Assert.AreEqual("Test Item", control.CurrentItem.CustomName);
        }
        
        /// <summary>
        /// Teste que la méthode RenameMenuItem_Click exécute la commande DemarrerRenommageCommand
        /// </summary>
        [STATestMethod]
        public void RenameMenuItem_Click_ExecutesCommand()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            var item = new ClipboardItem { Id = 1, CustomName = "Test Item" };
            var viewModel = new MockClipboardHistoryViewModel();
            viewModel.HistoryItems.Add(item);
            
            // Configurer l'état interne du contrôle
            control.CurrentItem = item;
            control.ViewModel = viewModel;
            
            // Act - Appeler directement la méthode RenameMenuItem_Click
            control.RenameMenuItem_Click(null, new RoutedEventArgs());
            
            // Assert
            Assert.IsTrue(viewModel.DemarrerRenommageCommand.WasExecuted);
            Assert.AreEqual(item, viewModel.DemarrerRenommageCommand.LastParameter);
        }
        
        /// <summary>
        /// Teste que la méthode PinMenuItem_Click exécute la commande BasculerEpinglageCommand
        /// </summary>
        [STATestMethod]
        public void PinMenuItem_Click_ExecutesCommand()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            var item = new ClipboardItem { Id = 1, CustomName = "Test Item", IsPinned = false };
            var viewModel = new MockClipboardHistoryViewModel();
            viewModel.HistoryItems.Add(item);
            
            // Configurer l'état interne du contrôle
            control.CurrentItem = item;
            control.ViewModel = viewModel;
            
            // Act - Appeler directement la méthode PinMenuItem_Click
            control.PinMenuItem_Click(null, new RoutedEventArgs());
            
            // Assert
            Assert.IsTrue(viewModel.BasculerEpinglageCommand.WasExecuted);
            Assert.AreEqual(item, viewModel.BasculerEpinglageCommand.LastParameter);
        }
        
        /// <summary>
        /// Teste que la méthode PreviewMenuItem_Click exécute la commande AfficherPreviewCommand
        /// </summary>
        [STATestMethod]
        public void PreviewMenuItem_Click_ExecutesCommand()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            var item = new ClipboardItem { Id = 1, CustomName = "Test Item" };
            var viewModel = new MockClipboardHistoryViewModel();
            viewModel.HistoryItems.Add(item);
            
            // Configurer l'état interne du contrôle
            control.CurrentItem = item;
            control.ViewModel = viewModel;
            
            // Act - Appeler directement la méthode PreviewMenuItem_Click
            control.PreviewMenuItem_Click(null, new RoutedEventArgs());
            
            // Assert
            Assert.IsTrue(viewModel.AfficherPreviewCommand.WasExecuted);
            Assert.AreEqual(item, viewModel.AfficherPreviewCommand.LastParameter);
        }
        
        /// <summary>
        /// Teste que la méthode DeleteMenuItem_Click exécute la commande SupprimerElementCommand
        /// </summary>
        [STATestMethod]
        public void DeleteMenuItem_Click_ExecutesCommand()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            var item = new ClipboardItem { Id = 1, CustomName = "Test Item" };
            var viewModel = new MockClipboardHistoryViewModel();
            viewModel.HistoryItems.Add(item);
            
            // Configurer l'état interne du contrôle
            control.CurrentItem = item;
            control.ViewModel = viewModel;
            
            // Act - Appeler directement la méthode DeleteMenuItem_Click
            control.DeleteMenuItem_Click(null, new RoutedEventArgs());
            
            // Assert
            Assert.IsTrue(viewModel.SupprimerElementCommand.WasExecuted);
            Assert.AreEqual(item, viewModel.SupprimerElementCommand.LastParameter);
        }
        
        /// <summary>
        /// Teste que la méthode DeleteMenuItem_Click n'exécute pas la commande si l'élément est null
        /// </summary>
        [STATestMethod]
        public void DeleteMenuItem_Click_WithNullItem_DoesNotExecuteCommand()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            var viewModel = new MockClipboardHistoryViewModel();
            
            // Configurer l'état interne du contrôle
            control.CurrentItem = null;
            control.ViewModel = viewModel;
            
            // Act - Appeler directement la méthode DeleteMenuItem_Click
            control.DeleteMenuItem_Click(null, new RoutedEventArgs());
            
            // Assert
            Assert.IsFalse(viewModel.SupprimerElementCommand.WasExecuted);
        }
        
        /// <summary>
        /// Teste que FindViewModel retourne le ViewModel déjà défini
        /// </summary>
        [STATestMethod]
        public void FindViewModel_ReturnsViewModel_WhenAlreadySet()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            var viewModel = new MockClipboardHistoryViewModel();
            
            // Configurer l'état interne du contrôle
            control.ViewModel = viewModel;
            
            // Act
            var result = control.FindViewModel();
            
            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(viewModel, result);
        }
        
        /// <summary>
        /// Teste que FindViewModel retourne null quand la recherche a déjà été tentée
        /// </summary>
        [STATestMethod]
        public void FindViewModel_ReturnsNull_WhenSearchAttempted()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            
            // Configurer l'état interne du contrôle
            control.ViewModelSearchAttempted = true;
            
            // Act
            var result = control.FindViewModel();
            
            // Assert
            Assert.IsNull(result);
        }
        
        /// <summary>
        /// Teste que EditNameTextBox_KeyDown avec la touche Enter confirme le renommage
        /// </summary>
        [STATestMethod]
        public void EditNameTextBox_KeyDown_EnterKey_ConfirmsRenaming()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            var viewModel = new MockClipboardHistoryViewModel();
            
            // Configurer l'état interne du contrôle
            control.ViewModel = viewModel;
            
            // Créer un KeyEventArgs simplifié pour l'appel direct à EditNameTextBox_KeyDown
            var keyEventArgs = new KeyEventArgs(
                Keyboard.PrimaryDevice,
                PresentationSource.FromVisual(control),
                0, 
                Key.Enter);
            keyEventArgs.RoutedEvent = Keyboard.KeyDownEvent;
            
            // Act
            control.EditNameTextBox_KeyDown(null, keyEventArgs);
            
            // Assert
            Assert.IsTrue(viewModel.ConfirmerRenommageCommand.WasExecuted);
        }
        
        /// <summary>
        /// Teste que EditNameTextBox_KeyDown avec la touche Escape annule le renommage
        /// </summary>
        [STATestMethod]
        public void EditNameTextBox_KeyDown_EscapeKey_CancelsRenaming()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            var viewModel = new MockClipboardHistoryViewModel();
            
            // Configurer l'état interne du contrôle
            control.ViewModel = viewModel;
            
            // Créer un KeyEventArgs simplifié pour l'appel direct à EditNameTextBox_KeyDown
            var keyEventArgs = new KeyEventArgs(
                Keyboard.PrimaryDevice,
                PresentationSource.FromVisual(control),
                0, 
                Key.Escape);
            keyEventArgs.RoutedEvent = Keyboard.KeyDownEvent;
            
            // Act
            control.EditNameTextBox_KeyDown(null, keyEventArgs);
            
            // Assert
            Assert.IsTrue(viewModel.AnnulerRenommageCommand.WasExecuted);
        }
        
        /// <summary>
        /// Teste que EditNameTextBox_LostFocus confirme le renommage
        /// </summary>
        [STATestMethod]
        public void EditNameTextBox_LostFocus_ConfirmsRenaming()
        {
            // Arrange
            var control = new MockClipboardItemControl();
            var viewModel = new MockClipboardHistoryViewModel();
            
            // Configurer l'état interne du contrôle
            control.ViewModel = viewModel;
            
            // Act
            control.EditNameTextBox_LostFocus(null, new RoutedEventArgs());
            
            // Assert
            Assert.IsTrue(viewModel.ConfirmerRenommageCommand.WasExecuted);
        }
    }
    
    /// <summary>
    /// Interface pour le mock du ViewModel
    /// </summary>
    public interface IMockClipboardHistoryViewModel : INotifyPropertyChanged
    {
        ClipboardItem? ItemEnRenommage { get; set; }
        string NouveauNom { get; set; }
        ObservableCollection<ClipboardItem> HistoryItems { get; }
        IRelayCommand<ClipboardItem> DemarrerRenommageCommand { get; }
        IRelayCommand AnnulerRenommageCommand { get; }
        IRelayCommand ConfirmerRenommageCommand { get; }
        IRelayCommand<ClipboardItem> BasculerEpinglageCommand { get; }
        IRelayCommand<ClipboardItem> AfficherPreviewCommand { get; }
        IRelayCommand<ClipboardItem> SupprimerElementCommand { get; }
    }
}