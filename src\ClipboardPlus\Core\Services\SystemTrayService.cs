using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Forms;
using System.Drawing;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.UI.Views;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.UI.Windows;
using WpfMessageBox = System.Windows.MessageBox;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.IO;
using System.Threading.Tasks;
using System.Reflection;
using WpfApplication = System.Windows.Application;
using System.Threading;
using ClipboardPlus.Core.Services.SystemTray;
using ClipboardPlus.Core.Services.Windows;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service de gestion de l'icône dans la zone de notification.
    /// </summary>
    public class SystemTrayService : ISystemTrayService, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILoggingService? _loggingService;
        private readonly ISystemTrayOrchestrator _orchestrator;
        
        /// <summary>
        /// Initialise une nouvelle instance de la classe SystemTrayService.
        /// </summary>
        /// <param name="serviceProvider">Le fournisseur de services pour l'injection de dépendances.</param>
        public SystemTrayService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _loggingService = serviceProvider.GetService(typeof(ILoggingService)) as ILoggingService;

            // Créer l'orchestrateur avec injection de dépendances
            _orchestrator = new SystemTrayOrchestrator(
                serviceProvider.GetService<IThreadValidator>()!,
                serviceProvider.GetService<INotifyIconCleanupService>()!,
                serviceProvider.GetService<INotifyIconFactory>()!,
                serviceProvider.GetService<IIconResourceLoader>()!,
                serviceProvider.GetService<IContextMenuBuilder>()!,
                serviceProvider.GetService<IVisibilityManager>()!,
                serviceProvider.GetService<IStartupNotificationService>()!,
                _loggingService!,
                serviceProvider,
                serviceProvider.GetService<IHistoryWindowService>()!,
                serviceProvider.GetService<ISettingsWindowService>()!
            );

            _loggingService?.LogInfo("SystemTrayService: Constructeur appelé avec orchestrateur SOLID");
        }
        
        /// <summary>
        /// Initialise l'icône dans la zone de notification.
        /// </summary>
        public void Initialize()
        {
            _loggingService?.LogInfo("========== SystemTrayService.Initialize: DÉBUT (via orchestrateur SOLID) ==========");
            try
            {
                // Déléguer l'initialisation à l'orchestrateur SOLID
                _orchestrator.Initialize();

                _loggingService?.LogInfo("SystemTrayService: Initialisation terminée avec succès via orchestrateur SOLID");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"SystemTrayService: Erreur lors de l'initialisation: {ex.Message}", ex);
                throw; // Remonter l'exception pour qu'elle soit gérée par l'appelant
            }
            _loggingService?.LogInfo("========== SystemTrayService.Initialize: FIN ==========");
        }

        /// <summary>
        /// Version V2 de Initialize qui utilise l'injection de dépendances explicite
        /// au lieu du Service Locator pattern.
        /// Cette méthode utilise la nouvelle méthode Initialize_V2 de l'orchestrateur.
        /// </summary>
        public void Initialize_V2()
        {
            _loggingService?.LogInfo("========== SystemTrayService.Initialize_V2: DÉBUT (Version sans Service Locator) ==========");
            try
            {
                // Déléguer l'initialisation à l'orchestrateur SOLID avec la méthode V2
                _orchestrator.Initialize_V2();

                _loggingService?.LogInfo("SystemTrayService: Initialisation V2 terminée avec succès via orchestrateur SOLID");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"SystemTrayService: Erreur lors de l'initialisation V2: {ex.Message}", ex);
                throw;
            }
            finally
            {
                _loggingService?.LogInfo("========== SystemTrayService.Initialize_V2: FIN ==========");
            }
        }

        /// <summary>
        /// Affiche une notification dans la zone de notification.
        /// </summary>
        public void ShowNotification(string title, string message, System.Windows.Forms.ToolTipIcon iconType)
        {
            _loggingService?.LogInfo($"SystemTrayService.ShowNotification: Délégation à l'orchestrateur SOLID pour '{title}'");
            _orchestrator.ShowNotification(title, message, iconType);
        }
        
        /// <summary>
        /// Ouvre la fenêtre des paramètres.
        /// </summary>
        public async void OpenSettingsWindow()
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            try
            {
                _loggingService?.LogInfo($"[{operationId}] OpenSettingsWindow: Début de l'ouverture des paramètres");
                
                // Vérifier si la fenêtre des paramètres existe déjà
                var existingWindow = System.Windows.Application.Current.Windows.OfType<AppSettingsWindow>().FirstOrDefault();
                
                if (existingWindow != null)
                {
                    // Activer la fenêtre existante
                    _loggingService?.LogInfo($"[{operationId}] OpenSettingsWindow: Activation de la fenêtre existante");
                    
                    // S'assurer que la fenêtre n'est pas minimisée
                    if (existingWindow.WindowState == WindowState.Minimized)
                    {
                        existingWindow.WindowState = WindowState.Normal;
                    }
                    
                    existingWindow.Activate();
                    existingWindow.Focus();
                    _loggingService?.LogInfo($"[{operationId}] OpenSettingsWindow: Fenêtre existante activée");
                }
                else
                {
                    // Créer et afficher la fenêtre des paramètres de manière asynchrone
                    _loggingService?.LogInfo($"[{operationId}] OpenSettingsWindow: Création d'une nouvelle fenêtre de paramètres");
                    
                    // Utiliser un SemaphoreSlim pour éviter les problèmes de synchronisation
                    using (var semaphore = new SemaphoreSlim(1, 1))
                    {
                        await semaphore.WaitAsync();
                        try
                        {
                            var settingsWindow = await AppSettingsWindow.CreateAsync(_serviceProvider);
                            
                            // Afficher la fenêtre en mode modal
                            _loggingService?.LogInfo($"[{operationId}] OpenSettingsWindow: Affichage de la fenêtre");
                            bool? result = settingsWindow.ShowDialog();
                            
                            // Actions après fermeture si nécessaire
                            if (result == true)
                            {
                                _loggingService?.LogInfo($"[{operationId}] OpenSettingsWindow: Paramètres appliqués avec succès");
                            }
                            else
                            {
                                _loggingService?.LogInfo($"[{operationId}] OpenSettingsWindow: Fermeture sans appliquer les paramètres");
                            }
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] OpenSettingsWindow: Erreur lors de l'ouverture des paramètres: {ex.Message}", ex);
                WpfMessageBox.Show($"Erreur lors de l'ouverture des paramètres: {ex.Message}",
                               "ClipboardPlus - Erreur",
                               MessageBoxButton.OK,
                               MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// Nettoie les ressources utilisées par le service.
        /// </summary>
        public void Dispose()
        {
            _loggingService?.LogInfo("SystemTrayService.Dispose: Délégation à l'orchestrateur SOLID");
            _orchestrator?.Dispose();
        }
        

        
        /// <summary>
        /// Affiche la fenêtre d'historique du presse-papiers.
        /// </summary>
        public async Task ShowHistoryWindow()
        {
            try
            {
                _loggingService?.LogInfo("ShowHistoryWindow: Début de l'affichage de la fenêtre d'historique");
                
                // Vérifier si la fenêtre d'historique existe déjà
                var existingWindow = System.Windows.Application.Current.Windows.OfType<ClipboardHistoryWindow>().FirstOrDefault();
                
                if (existingWindow != null)
                {
                    // Activer la fenêtre existante
                    _loggingService?.LogInfo("ShowHistoryWindow: Activation de la fenêtre existante");

                    // Rendre la fenêtre visible si elle était cachée
                    existingWindow.Show();
                    
                    // S'assurer que la fenêtre n'est pas minimisée
                    if (existingWindow.WindowState == WindowState.Minimized)
                    {
                        existingWindow.WindowState = WindowState.Normal;
                    }

                    // Activer la fenêtre et la mettre au premier plan
                    existingWindow.Activate();
                    existingWindow.Focus();
                    
                    _loggingService?.LogInfo("ShowHistoryWindow: Fenêtre existante activée");
                }
                else
                {
                    // Créer et afficher une nouvelle fenêtre d'historique
                    _loggingService?.LogInfo("ShowHistoryWindow: Création d'une nouvelle fenêtre d'historique");
                    
                    var clipboardHistoryViewModel = _serviceProvider.GetService<ClipboardHistoryViewModel>();
                    if (clipboardHistoryViewModel == null)
                    {
                        _loggingService?.LogError("ShowHistoryWindow: Impossible d'obtenir ClipboardHistoryViewModel du conteneur de services");
                        return;
                    }

                    // 🚨 CORRECTION CRITIQUE : Initialiser le ViewModel pour activer les managers
                    await clipboardHistoryViewModel.InitializeAsync();

                    var historyWindow = new ClipboardHistoryWindow(clipboardHistoryViewModel);
                    
                    _loggingService?.LogInfo("ShowHistoryWindow: Affichage de la nouvelle fenêtre");
                    historyWindow.Show();
                    historyWindow.Activate();
                    
                    _loggingService?.LogInfo("ShowHistoryWindow: Nouvelle fenêtre affichée et activée");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ShowHistoryWindow: Erreur lors de l'affichage de la fenêtre d'historique: {ex.Message}", ex);
                WpfMessageBox.Show($"Erreur lors de l'affichage de l'historique: {ex.Message}",
                               "ClipboardPlus - Erreur",
                               MessageBoxButton.OK,
                               MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Version V3 de Initialize qui utilise le constructeur V2 de SystemTrayOrchestrator
        /// (sans Service Locator pattern).
        /// Cette méthode crée un nouvel orchestrateur qui utilise uniquement l'injection de dépendances explicite.
        /// </summary>
        public SystemTrayOrchestrator CreateOrchestratorV2()
        {
            try
            {
                _loggingService?.LogInfo("SystemTrayService: Début de la création de l'orchestrateur V2 (sans Service Locator)");

                // Créer un nouvel orchestrateur avec le constructeur V2 (sans IServiceProvider)
                var orchestratorV2 = new SystemTrayOrchestrator(
                    _serviceProvider.GetService<IThreadValidator>()!,
                    _serviceProvider.GetService<INotifyIconCleanupService>()!,
                    _serviceProvider.GetService<INotifyIconFactory>()!,
                    _serviceProvider.GetService<IIconResourceLoader>()!,
                    _serviceProvider.GetService<IContextMenuBuilder>()!,
                    _serviceProvider.GetService<IVisibilityManager>()!,
                    _serviceProvider.GetService<IStartupNotificationService>()!,
                    _loggingService!,
                    _serviceProvider.GetService<IHistoryWindowService>()!,
                    _serviceProvider.GetService<ISettingsWindowService>()!
                );

                _loggingService?.LogInfo("SystemTrayService: Orchestrateur V2 créé avec succès (sans Service Locator)");

                return orchestratorV2;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"SystemTrayService: Erreur lors de la création de l'orchestrateur V2: {ex.Message}", ex);
                throw;
            }
        }
    }
}