<?xml version="1.0" encoding="utf-8"?>
<assembly manifestVersion="1.0" xmlns="urn:schemas-microsoft-com:asm.v1">
  <assemblyIdentity version="*******" name="ClipboardPlus.app"/>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <!-- Options du niveau de contrôle de compte d'utilisateur Windows
             Si vous souhaitez modifier le niveau de contrôle de compte d'utilisateur Windows, remplacez le 
             nœud requestedExecutionLevel par l'une des propositions suivantes.

        <requestedExecutionLevel  level="asInvoker" uiAccess="false" />
        <requestedExecutionLevel  level="requireAdministrator" uiAccess="false" />
        <requestedExecutionLevel  level="highestAvailable" uiAccess="false" />

            La spécification du nœud requestedExecutionLevel désactive la virtualisation de fichiers et du Registre.
            Si vous souhaitez utiliser la virtualisation de fichiers et du Registre pour la compatibilité 
            descendante, supprimez le nœud requestedExecutionLevel.
        -->
        <requestedExecutionLevel level="asInvoker" uiAccess="false" />
      </requestedPrivileges>
    </security>
  </trustInfo>

  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!-- Liste des versions de Windows pour lesquelles cette application a été testée
           et sur lesquelles elle doit fonctionner. Décommentez les éléments appropriés et Windows va 
           sélectionner automatiquement l'environnement le plus compatible. -->

      <!-- Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}" />

      <!-- Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}" />

      <!-- Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}" />

      <!-- Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}" />

      <!-- Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}" />

    </application>
  </compatibility>

  <!-- Les paramètres de DPI haute résolution ont été retirés et sont désormais configurés dans le fichier projet via
       la propriété ApplicationHighDpiMode ou l'API Application.SetHighDpiMode dans le code. -->

</assembly> 