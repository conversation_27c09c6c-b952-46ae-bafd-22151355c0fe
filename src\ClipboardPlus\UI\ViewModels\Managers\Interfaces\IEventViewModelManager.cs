// ============================================================================
// INTERFACE EVENT VIEWMODEL MANAGER - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Interface pour la gestion des événements dans le ViewModel
// 📋 RESPONSABILITÉ : Gestion des événements et synchronisation
// 🏗️ ARCHITECTURE : Extraction de Events.cs (54 lignes) + Events.Refactored.cs (167 lignes)
//
// ============================================================================

using System;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.UI.ViewModels.Managers.Interfaces
{
    /// <summary>
    /// Interface pour le manager de gestion des événements dans le ViewModel.
    /// 
    /// Ce manager est responsable de :
    /// - La gestion des événements système et application
    /// - La synchronisation entre les composants
    /// - La propagation des notifications
    /// - L'orchestration des événements inter-managers
    /// </summary>
    public interface IEventViewModelManager : IDisposable
    {
        #region Propriétés d'État

        /// <summary>
        /// Indique si le gestionnaire d'événements est actif.
        /// </summary>
        bool IsEventHandlingActive { get; set; }

        /// <summary>
        /// Nombre d'événements traités depuis le démarrage.
        /// </summary>
        int ProcessedEventCount { get; }

        /// <summary>
        /// Indique si la synchronisation automatique est activée.
        /// </summary>
        bool IsAutoSyncEnabled { get; set; }

        #endregion

        #region Événements Publics

        /// <summary>
        /// Événement déclenché lorsque l'historique change.
        /// </summary>
        event EventHandler? HistoryChanged;

        /// <summary>
        /// Événement déclenché lorsque la sélection change.
        /// </summary>
        event EventHandler<ClipboardItem?>? SelectionChanged;

        /// <summary>
        /// Événement déclenché lorsqu'un élément est ajouté à l'historique.
        /// </summary>
        event EventHandler<ClipboardItem>? ItemAdded;

        /// <summary>
        /// Événement déclenché lorsqu'un élément est supprimé de l'historique.
        /// </summary>
        event EventHandler<ClipboardItem>? ItemRemoved;

        /// <summary>
        /// Événement déclenché lorsqu'un élément est modifié.
        /// </summary>
        event EventHandler<ClipboardItem>? ItemModified;

        /// <summary>
        /// Événement déclenché lorsque l'état d'épinglage d'un élément change.
        /// </summary>
        event EventHandler<ItemPinStateChangedEventArgs>? ItemPinStateChanged;

        /// <summary>
        /// Événement déclenché lorsqu'une synchronisation est nécessaire.
        /// </summary>
        event EventHandler<SyncRequiredEventArgs>? SyncRequired;

        /// <summary>
        /// Événement déclenché pour demander la fermeture du dialogue.
        /// </summary>
        event EventHandler? RequestCloseDialog;

        #endregion

        #region Méthodes de Gestion des Événements

        /// <summary>
        /// Démarre la gestion des événements.
        /// </summary>
        void StartEventHandling();

        /// <summary>
        /// Arrête la gestion des événements.
        /// </summary>
        void StopEventHandling();

        /// <summary>
        /// Traite un événement de changement d'historique.
        /// </summary>
        /// <param name="reason">Raison du changement</param>
        void HandleHistoryChanged(string reason);

        /// <summary>
        /// Traite un événement de changement de sélection.
        /// </summary>
        /// <param name="newSelection">Nouvel élément sélectionné</param>
        /// <param name="oldSelection">Ancien élément sélectionné</param>
        void HandleSelectionChanged(ClipboardItem? newSelection, ClipboardItem? oldSelection);

        /// <summary>
        /// Traite un événement d'ajout d'élément.
        /// </summary>
        /// <param name="item">Élément ajouté</param>
        /// <param name="source">Source de l'ajout</param>
        void HandleItemAdded(ClipboardItem item, string source);

        /// <summary>
        /// Traite un événement de suppression d'élément.
        /// </summary>
        /// <param name="item">Élément supprimé</param>
        /// <param name="source">Source de la suppression</param>
        void HandleItemRemoved(ClipboardItem item, string source);

        /// <summary>
        /// Traite un événement de modification d'élément.
        /// </summary>
        /// <param name="item">Élément modifié</param>
        /// <param name="changeType">Type de modification</param>
        void HandleItemModified(ClipboardItem item, string changeType);

        #endregion

        #region Méthodes de Synchronisation

        /// <summary>
        /// Force une synchronisation complète de tous les composants.
        /// </summary>
        /// <param name="reason">Raison de la synchronisation</param>
        void ForceSynchronization(string reason);

        /// <summary>
        /// Synchronise l'historique avec la source de données.
        /// </summary>
        /// <param name="reason">Raison de la synchronisation</param>
        void SynchronizeHistory(string reason);

        /// <summary>
        /// Synchronise la sélection entre les composants.
        /// </summary>
        /// <param name="selectedItem">Élément à synchroniser</param>
        void SynchronizeSelection(ClipboardItem? selectedItem);

        /// <summary>
        /// Vérifie si une synchronisation est nécessaire.
        /// </summary>
        /// <returns>True si une synchronisation est nécessaire</returns>
        bool IsSynchronizationRequired();

        #endregion

        #region Méthodes de Notification

        /// <summary>
        /// Notifie tous les abonnés d'un changement d'historique.
        /// </summary>
        /// <param name="reason">Raison du changement</param>
        void NotifyHistoryChanged(string reason);

        /// <summary>
        /// Notifie tous les abonnés d'un changement de sélection.
        /// </summary>
        /// <param name="newSelection">Nouvel élément sélectionné</param>
        void NotifySelectionChanged(ClipboardItem? newSelection);

        /// <summary>
        /// Notifie tous les abonnés qu'un élément a été ajouté.
        /// </summary>
        /// <param name="item">Élément ajouté</param>
        void NotifyItemAdded(ClipboardItem item);

        /// <summary>
        /// Notifie tous les abonnés qu'un élément a été supprimé.
        /// </summary>
        /// <param name="item">Élément supprimé</param>
        void NotifyItemRemoved(ClipboardItem item);

        /// <summary>
        /// Notifie tous les abonnés qu'un élément a été modifié.
        /// </summary>
        /// <param name="item">Élément modifié</param>
        /// <param name="changeType">Type de modification</param>
        void NotifyItemModified(ClipboardItem item, string changeType);

        /// <summary>
        /// Déclenche l'événement RequestCloseDialog.
        /// </summary>
        void TriggerRequestCloseDialog();

        #endregion

        #region Méthodes d'Abonnement

        /// <summary>
        /// S'abonne aux événements d'un autre manager.
        /// </summary>
        /// <param name="manager">Manager auquel s'abonner</param>
        void SubscribeToManager(object manager);

        /// <summary>
        /// Se désabonne des événements d'un manager.
        /// </summary>
        /// <param name="manager">Manager duquel se désabonner</param>
        void UnsubscribeFromManager(object manager);

        /// <summary>
        /// S'abonne aux événements système pertinents.
        /// </summary>
        void SubscribeToSystemEvents();

        /// <summary>
        /// Se désabonne des événements système.
        /// </summary>
        void UnsubscribeFromSystemEvents();

        #endregion

        #region Méthodes d'Initialisation et Nettoyage

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        void Initialize();

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        void Cleanup();

        #endregion
    }

    #region Classes d'Arguments d'Événements

    /// <summary>
    /// Arguments d'événement pour un changement d'état d'épinglage.
    /// </summary>
    public class ItemPinStateChangedEventArgs : EventArgs
    {
        public ClipboardItem Item { get; }
        public bool WasPinned { get; }
        public bool IsPinned { get; }

        public ItemPinStateChangedEventArgs(ClipboardItem item, bool wasPinned, bool isPinned)
        {
            Item = item;
            WasPinned = wasPinned;
            IsPinned = isPinned;
        }
    }

    /// <summary>
    /// Arguments d'événement pour une synchronisation requise.
    /// </summary>
    public class SyncRequiredEventArgs : EventArgs
    {
        public string Reason { get; }
        public string Component { get; }
        public SyncPriority Priority { get; }

        public SyncRequiredEventArgs(string reason, string component, SyncPriority priority = SyncPriority.Normal)
        {
            Reason = reason;
            Component = component;
            Priority = priority;
        }
    }

    /// <summary>
    /// Priorité de synchronisation.
    /// </summary>
    public enum SyncPriority
    {
        Low,
        Normal,
        High,
        Critical
    }

    #endregion
}
