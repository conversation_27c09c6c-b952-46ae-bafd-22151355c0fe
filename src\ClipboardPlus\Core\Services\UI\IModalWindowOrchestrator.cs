using System;
using System.Threading.Tasks;
using System.Windows;

namespace ClipboardPlus.Core.Services.UI
{
    /// <summary>
    /// Service pour l'orchestration des fenêtres modales.
    /// Responsabilité unique : Gérer la création, configuration et affichage des fenêtres modales.
    /// </summary>
    public interface IModalWindowOrchestrator
    {
        /// <summary>
        /// Affiche une fenêtre modale de manière asynchrone.
        /// </summary>
        /// <typeparam name="T">Type de fenêtre à afficher</typeparam>
        /// <param name="services">Fournisseur de services pour la création de la fenêtre</param>
        /// <param name="owner">Fenêtre propriétaire (optionnel)</param>
        /// <returns>Résultat du dialogue modal</returns>
        Task<bool?> ShowModalWindowAsync<T>(IServiceProvider services, Window? owner = null) 
            where T : Window;

        /// <summary>
        /// Affiche une fenêtre modale avec configuration personnalisée.
        /// </summary>
        /// <param name="windowFactory">Factory pour créer la fenêtre</param>
        /// <param name="owner">Fenêtre propriétaire (optionnel)</param>
        /// <param name="configureWindow">Action pour configurer la fenêtre avant affichage</param>
        /// <returns>Résultat du dialogue modal</returns>
        Task<bool?> ShowModalWindowAsync(
            Func<Task<Window>> windowFactory,
            Window? owner = null,
            Action<Window>? configureWindow = null);
    }
}
