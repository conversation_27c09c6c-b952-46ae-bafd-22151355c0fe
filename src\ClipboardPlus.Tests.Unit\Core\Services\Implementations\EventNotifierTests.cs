using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.Implementations;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.Implementations
{
    [TestFixture]
    public class EventNotifierTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<Action> _mockCallback = null!;
        private EventNotifier _eventNotifier = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockCallback = new Mock<Action>();
            _eventNotifier = new EventNotifier(_mockCallback.Object, _mockLoggingService.Object);
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithValidParameters_CreatesInstance()
        {
            // Act & Assert
            Assert.That(_eventNotifier, Is.Not.Null);
        }

        [Test]
        public void Constructor_WithNullCallback_CreatesInstance()
        {
            // Act & Assert
            var notifier = new EventNotifier(null, _mockLoggingService.Object);
            Assert.That(notifier, Is.Not.Null);
        }

        [Test]
        public void Constructor_WithNullLoggingService_CreatesInstance()
        {
            // Act & Assert
            var notifier = new EventNotifier(_mockCallback.Object, null);
            Assert.That(notifier, Is.Not.Null);
        }

        [Test]
        public void Constructor_WithAllNullParameters_CreatesInstance()
        {
            // Act & Assert
            var notifier = new EventNotifier(null, null);
            Assert.That(notifier, Is.Not.Null);
        }

        #endregion

        #region NotifyHistoryChanged Tests

        [Test]
        public void NotifyHistoryChanged_WithValidItemAndCallback_InvokesCallback()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            // Act
            _eventNotifier.NotifyHistoryChanged(item, HistoryChangeType.ItemAdded);

            // Assert
            _mockCallback.Verify(c => c.Invoke(), Times.Once);
        }

        [Test]
        public void NotifyHistoryChanged_WithDifferentChangeTypes_InvokesCallbackForEach()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            // Act
            _eventNotifier.NotifyHistoryChanged(item, HistoryChangeType.ItemAdded);
            _eventNotifier.NotifyHistoryChanged(item, HistoryChangeType.ItemUpdated);
            _eventNotifier.NotifyHistoryChanged(item, HistoryChangeType.ItemMovedToTop);
            _eventNotifier.NotifyHistoryChanged(item, HistoryChangeType.ItemsRemoved);

            // Assert
            _mockCallback.Verify(c => c.Invoke(), Times.Exactly(4));
        }

        [Test]
        public void NotifyHistoryChanged_WithNullCallback_DoesNotThrow()
        {
            // Arrange
            var notifierWithoutCallback = new EventNotifier(null, _mockLoggingService.Object);
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            // Act & Assert
            Assert.DoesNotThrow(() => 
                notifierWithoutCallback.NotifyHistoryChanged(item, HistoryChangeType.ItemAdded));
        }

        [Test]
        public void NotifyHistoryChanged_WithNullItem_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                _eventNotifier.NotifyHistoryChanged(null!, HistoryChangeType.ItemAdded));

            Assert.That(ex!.ParamName, Is.EqualTo("item"));
        }

        [Test]
        public void NotifyHistoryChanged_WithCallbackException_PropagatesException()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            var exceptionMessage = "Callback error";
            _mockCallback.Setup(c => c.Invoke()).Throws(new Exception(exceptionMessage));

            // Act & Assert
            var ex = Assert.Throws<Exception>(() =>
                _eventNotifier.NotifyHistoryChanged(item, HistoryChangeType.ItemAdded));

            Assert.That(ex!.Message, Is.EqualTo(exceptionMessage));
        }

        [Test]
        public void NotifyHistoryChanged_WithValidParameters_LogsCorrectly()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 42,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            // Act
            _eventNotifier.NotifyHistoryChanged(item, HistoryChangeType.ItemUpdated);

            // Assert
            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains("EventNotifier.NotifyHistoryChanged") && 
                    s.Contains("Type: ItemUpdated") && 
                    s.Contains("Item ID: 42"))),
                Times.Once);

            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains("Déclenchement du callback pour ItemUpdated"))),
                Times.Once);

            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains("Callback exécuté avec succès"))),
                Times.Once);
        }

        [Test]
        public void NotifyHistoryChanged_WithNullCallback_LogsNoCallbackMessage()
        {
            // Arrange
            var notifierWithoutCallback = new EventNotifier(null, _mockLoggingService.Object);
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            // Act
            notifierWithoutCallback.NotifyHistoryChanged(item, HistoryChangeType.ItemAdded);

            // Assert
            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains("Aucun callback configuré"))),
                Times.Once);
        }

        [Test]
        public void NotifyHistoryChanged_WithCallbackException_LogsError()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            var exceptionMessage = "Callback error";
            var exception = new Exception(exceptionMessage);
            _mockCallback.Setup(c => c.Invoke()).Throws(exception);

            // Act & Assert
            Assert.Throws<Exception>(() =>
                _eventNotifier.NotifyHistoryChanged(item, HistoryChangeType.ItemAdded));

            _mockLoggingService.Verify(
                l => l.LogError(
                    It.Is<string>(s => 
                        s.Contains("EventNotifier.NotifyHistoryChanged") && 
                        s.Contains("Erreur lors de l'exécution du callback") &&
                        s.Contains(exceptionMessage)),
                    It.Is<Exception>(ex => ex == exception)),
                Times.Once);
        }

        [Test]
        public void NotifyHistoryChanged_WithAllChangeTypes_WorksCorrectly()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            var changeTypes = new[]
            {
                HistoryChangeType.ItemAdded,
                HistoryChangeType.ItemUpdated,
                HistoryChangeType.ItemMovedToTop,
                HistoryChangeType.ItemsRemoved
            };

            // Act & Assert
            foreach (var changeType in changeTypes)
            {
                Assert.DoesNotThrow(() => 
                    _eventNotifier.NotifyHistoryChanged(item, changeType));
            }

            _mockCallback.Verify(c => c.Invoke(), Times.Exactly(changeTypes.Length));
        }

        #endregion
    }
}
