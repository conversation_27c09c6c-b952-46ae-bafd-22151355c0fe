using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.Visibility
{
    /// <summary>
    /// Tests exhaustifs pour TimestampVisibilityRule - Prévention de régression
    /// </summary>
    [TestFixture]
    public class TimestampVisibilityRuleTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private TimestampVisibilityRule _timestampRule = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _timestampRule = new TimestampVisibilityRule(_mockLoggingService.Object);
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithValidLoggingService_InitializesCorrectly()
        {
            // Act & Assert
            Assert.IsNotNull(_timestampRule);
        }

        [Test]
        public void Constructor_WithNullLoggingService_InitializesWithoutException()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new TimestampVisibilityRule(null));
        }

        #endregion

        #region Core Logic Tests

        [Test]
        public void ShouldBeVisible_GlobalTrue_ReturnsTrue()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem { Timestamp = System.DateTime.Now };

            // Act
            var result = _timestampRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Horodatage devrait être visible quand global autorisé");
        }

        [Test]
        public void ShouldBeVisible_GlobalFalse_ReturnsFalse()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = false };
            var item = new ClipboardItem { Timestamp = System.DateTime.Now };

            // Act
            var result = _timestampRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "Horodatage devrait être masqué quand global désactivé");
        }

        #endregion

        #region Edge Cases Tests

        [Test]
        public void ShouldBeVisible_DefaultTimestamp_GlobalTrue_ReturnsTrue()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem(); // Timestamp par défaut

            // Act
            var result = _timestampRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Horodatage par défaut devrait être visible si global autorisé");
        }

        [Test]
        public void ShouldBeVisible_MinValue_GlobalTrue_ReturnsTrue()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem { Timestamp = System.DateTime.MinValue };

            // Act
            var result = _timestampRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Horodatage MinValue devrait être visible si global autorisé");
        }

        [Test]
        public void ShouldBeVisible_MaxValue_GlobalTrue_ReturnsTrue()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem { Timestamp = System.DateTime.MaxValue };

            // Act
            var result = _timestampRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Horodatage MaxValue devrait être visible si global autorisé");
        }

        [Test]
        public void ShouldBeVisible_FutureDate_GlobalTrue_ReturnsTrue()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var futureDate = System.DateTime.Now.AddYears(10);
            var item = new ClipboardItem { Timestamp = futureDate };

            // Act
            var result = _timestampRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Horodatage futur devrait être visible si global autorisé");
        }

        [Test]
        public void ShouldBeVisible_PastDate_GlobalTrue_ReturnsTrue()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var pastDate = System.DateTime.Now.AddYears(-10);
            var item = new ClipboardItem { Timestamp = pastDate };

            // Act
            var result = _timestampRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Horodatage passé devrait être visible si global autorisé");
        }

        #endregion

        #region Null Safety Tests

        [Test]
        public void ShouldBeVisible_NullItem_ReturnsFalse()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };

            // Act
            var result = _timestampRule.ShouldBeVisible(null!, context);

            // Assert
            Assert.IsFalse(result, "Item null devrait retourner false");
        }

        [Test]
        public void ShouldBeVisible_NullContext_ReturnsFalse()
        {
            // Arrange
            var item = new ClipboardItem { Timestamp = System.DateTime.Now };

            // Act
            var result = _timestampRule.ShouldBeVisible(item, null!);

            // Assert
            Assert.IsFalse(result, "Context null devrait retourner false");
        }

        [Test]
        public void ShouldBeVisible_BothNull_ReturnsFalse()
        {
            // Act
            var result = _timestampRule.ShouldBeVisible(null!, null!);

            // Assert
            Assert.IsFalse(result, "Item et context null devraient retourner false");
        }

        #endregion

        #region Consistency Tests

        [Test]
        public void ShouldBeVisible_MultipleCallsSameParameters_ConsistentResults()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem { Timestamp = System.DateTime.Now };

            // Act - Appels multiples
            var result1 = _timestampRule.ShouldBeVisible(item, context);
            var result2 = _timestampRule.ShouldBeVisible(item, context);
            var result3 = _timestampRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result1);
            Assert.IsTrue(result2);
            Assert.IsTrue(result3);
            Assert.AreEqual(result1, result2, "Résultats devraient être identiques");
            Assert.AreEqual(result2, result3, "Résultats devraient être identiques");
        }

        [Test]
        public void ShouldBeVisible_StateChanges_ReflectedCorrectly()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem { Timestamp = System.DateTime.Now };

            // Act & Assert - État initial
            Assert.IsTrue(_timestampRule.ShouldBeVisible(item, context));

            // Act & Assert - Changement global
            context.GlobalTimestampVisibility = false;
            Assert.IsFalse(_timestampRule.ShouldBeVisible(item, context));

            // Act & Assert - Retour à l'état initial
            context.GlobalTimestampVisibility = true;
            Assert.IsTrue(_timestampRule.ShouldBeVisible(item, context));
        }

        #endregion

        #region Regression Prevention Tests

        [Test]
        public void ShouldBeVisible_AfterItemModification_RespectsSetting()
        {
            // Arrange - Simule modification d'item avec horodatage caché
            var context = new VisibilityContext { GlobalTimestampVisibility = false };
            var item = new ClipboardItem { Timestamp = System.DateTime.Now };

            // Modifier l'item (simule renommage ou autre modification)
            item.CustomName = "Nouveau nom";
            item.IsPinned = true;

            // Act
            var result = _timestampRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "RÉGRESSION: L'horodatage doit rester caché après modification si l'utilisateur a choisi de cacher les horodatages");
        }

        [Test]
        public void ShouldBeVisible_DifferentItemsSameContext_ConsistentBehavior()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = false };
            var item1 = new ClipboardItem { Id = 1, Timestamp = System.DateTime.Now };
            var item2 = new ClipboardItem { Id = 2, Timestamp = System.DateTime.Now.AddHours(-1) };
            var item3 = new ClipboardItem { Id = 3, Timestamp = System.DateTime.Now.AddDays(1) };

            // Act
            var result1 = _timestampRule.ShouldBeVisible(item1, context);
            var result2 = _timestampRule.ShouldBeVisible(item2, context);
            var result3 = _timestampRule.ShouldBeVisible(item3, context);

            // Assert
            Assert.IsFalse(result1, "Tous les items devraient avoir le même comportement");
            Assert.IsFalse(result2, "Tous les items devraient avoir le même comportement");
            Assert.IsFalse(result3, "Tous les items devraient avoir le même comportement");
        }

        #endregion

        #region Logging Tests

        [Test]
        public void ShouldBeVisible_LogsDecision_WhenLoggingServiceAvailable()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem { Id = 456, Timestamp = System.DateTime.Now };

            // Act
            _timestampRule.ShouldBeVisible(item, context);

            // Assert
            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains("TIMESTAMP_VISIBILITY_RULE") && 
                    s.Contains("Item ID: 456") && 
                    s.Contains("GlobalTimestampVisibility: True") &&
                    s.Contains("Result: True"))), 
                Times.Once,
                "La décision de visibilité devrait être loggée");
        }

        [Test]
        public void ShouldBeVisible_HandlesLoggingException_Gracefully()
        {
            // Arrange
            _mockLoggingService.Setup(l => l.LogInfo(It.IsAny<string>()))
                              .Throws(new System.Exception("Logging error"));
            
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem { Timestamp = System.DateTime.Now };

            // Act & Assert
            Assert.DoesNotThrow(() => _timestampRule.ShouldBeVisible(item, context),
                "Exception de logging ne devrait pas faire planter la règle");
        }

        #endregion

        #region Performance Tests

        [Test]
        public void ShouldBeVisible_PerformanceTest_CompletesQuickly()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem { Timestamp = System.DateTime.Now };
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act - 1000 appels
            for (int i = 0; i < 1000; i++)
            {
                _timestampRule.ShouldBeVisible(item, context);
            }

            // Assert
            stopwatch.Stop();
            Assert.Less(stopwatch.ElapsedMilliseconds, 100,
                "1000 appels devraient prendre moins de 100ms");
        }

        #endregion

        #region Thread Safety Tests

        [Test]
        public void ShouldBeVisible_ConcurrentAccess_ThreadSafe()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem { Timestamp = System.DateTime.Now };
            var results = new bool[100];
            var tasks = new System.Threading.Tasks.Task[100];

            // Act - Accès concurrent
            for (int i = 0; i < 100; i++)
            {
                int index = i;
                tasks[i] = System.Threading.Tasks.Task.Run(() =>
                {
                    results[index] = _timestampRule.ShouldBeVisible(item, context);
                });
            }

            System.Threading.Tasks.Task.WaitAll(tasks);

            // Assert
            Assert.IsTrue(results.All(r => r == true), "Tous les résultats devraient être identiques en accès concurrent");
        }

        #endregion
    }
}
