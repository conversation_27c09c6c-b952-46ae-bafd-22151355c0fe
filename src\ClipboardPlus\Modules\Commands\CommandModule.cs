using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Modules.Core;
using ClipboardPlus.Modules.Core.Events;
using Prism.Events;
using CommunityToolkit.Mvvm.Input;

namespace ClipboardPlus.Modules.Commands
{
    /// <summary>
    /// Implémentation du module de gestion des commandes.
    /// 
    /// Ce module encapsule toute la logique de création, d'exécution et de gestion
    /// du cycle de vie de toutes les commandes de l'application.
    /// </summary>
    public class CommandModule : ModuleBase, ICommandModule
    {
        private readonly IClipboardHistoryManager _historyManager;
        private readonly IClipboardInteractionService _clipboardService;
        private readonly ILoggingService _loggingService;
        private readonly IEventAggregator _eventAggregator;

        private readonly CommandRegistry _commandRegistry;
        private readonly CommandContext _commandContext;

        /// <inheritdoc />
        public override string ModuleName => "CommandModule";

        /// <inheritdoc />
        public override Version ModuleVersion => new Version(1, 0, 0);

        /// <inheritdoc />
        public ICommand DeleteSelectedItemCommand { get; private set; }

        /// <inheritdoc />
        public ICommand ClearHistoryCommand { get; private set; }

        /// <inheritdoc />
        public ICommand RenameItemCommand { get; private set; }

        /// <inheritdoc />
        public ICommand CopyToClipboardCommand { get; private set; }

        /// <inheritdoc />
        public ICommand PasteSelectedItemCommand { get; private set; }

        /// <inheritdoc />
        public ICommand TogglePinCommand { get; private set; }

        /// <inheritdoc />
        public ICommand FinalizeAndSaveNewItemCommand { get; private set; }

        /// <inheritdoc />
        public ICommand CancelNewItemCommand { get; private set; }

        /// <inheritdoc />
        public ICommand ConfirmRenameCommand { get; private set; }

        /// <inheritdoc />
        public ICommand CancelRenameCommand { get; private set; }

        /// <inheritdoc />
        public ICommandRegistry CommandRegistry => _commandRegistry;

        /// <inheritdoc />
        public ICommandContext CommandContext
        {
            get => _commandContext;
            set => throw new InvalidOperationException("CommandContext cannot be replaced after initialization");
        }

        /// <inheritdoc />
        public event EventHandler<CommandExecutingEventArgs>? CommandExecuting;

        /// <inheritdoc />
        public event EventHandler<CommandExecutedEventArgs>? CommandExecuted;

        /// <inheritdoc />
        public event EventHandler<CommandFailedEventArgs>? CommandFailed;

        public CommandModule(
            IClipboardHistoryManager historyManager,
            IClipboardInteractionService clipboardService,
            ILoggingService loggingService,
            IEventAggregator eventAggregator)
        {
            _historyManager = historyManager ?? throw new ArgumentNullException(nameof(historyManager));
            _clipboardService = clipboardService ?? throw new ArgumentNullException(nameof(clipboardService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));

            _commandRegistry = new CommandRegistry();
            _commandContext = new CommandContext();

            // Initialiser les commandes (sera fait dans OnInitializeAsync)
            DeleteSelectedItemCommand = null!;
            ClearHistoryCommand = null!;
            RenameItemCommand = null!;
            CopyToClipboardCommand = null!;
            PasteSelectedItemCommand = null!;
            TogglePinCommand = null!;
            FinalizeAndSaveNewItemCommand = null!;
            CancelNewItemCommand = null!;
            ConfirmRenameCommand = null!;
            CancelRenameCommand = null!;
        }

        /// <inheritdoc />
        protected override Task OnInitializeAsync()
        {
            _loggingService.LogInfo($"Initializing {ModuleName}...");

            // Créer toutes les commandes
            CreateCommands();

            // Enregistrer les commandes dans le registre
            RegisterCommands();

            _loggingService.LogInfo($"{ModuleName} initialized successfully with {_commandRegistry.Commands.Count} commands");
            return Task.CompletedTask;
        }

        /// <inheritdoc />
        protected override Task OnStartAsync()
        {
            _loggingService.LogInfo($"Starting {ModuleName}...");
            
            // Publier un événement de démarrage
            _eventAggregator.GetEvent<ModuleStateChangedPrismEvent>().Publish(
                new ModuleStateChangedEvent(ModuleName, ClipboardPlus.Modules.Core.ModuleState.Starting, ClipboardPlus.Modules.Core.ModuleState.Running));
            
            return Task.CompletedTask;
        }

        /// <inheritdoc />
        protected override Task OnStopAsync()
        {
            _loggingService.LogInfo($"Stopping {ModuleName}...");
            
            // Publier un événement d'arrêt
            _eventAggregator.GetEvent<ModuleStateChangedPrismEvent>().Publish(
                new ModuleStateChangedEvent(ModuleName, ClipboardPlus.Modules.Core.ModuleState.Running, ClipboardPlus.Modules.Core.ModuleState.Stopping));
            
            return Task.CompletedTask;
        }

        /// <inheritdoc />
        protected override void OnDispose()
        {
            _commandRegistry.Clear();
            _loggingService.LogInfo($"{ModuleName} disposed");
        }

        private void CreateCommands()
        {
            DeleteSelectedItemCommand = new AsyncRelayCommand(
                ExecuteDeleteSelectedItemAsync,
                CanExecuteDeleteSelectedItem);

            ClearHistoryCommand = new AsyncRelayCommand(
                ExecuteClearHistoryAsync,
                CanExecuteClearHistory);

            RenameItemCommand = new RelayCommand<ClipboardItem>(
                ExecuteRenameItem,
                CanExecuteRenameItem);

            CopyToClipboardCommand = new AsyncRelayCommand<ClipboardItem>(
                ExecuteCopyToClipboardAsync,
                CanExecuteCopyToClipboard);

            PasteSelectedItemCommand = new AsyncRelayCommand(
                ExecutePasteSelectedItemAsync,
                CanExecutePasteSelectedItem);

            TogglePinCommand = new AsyncRelayCommand<ClipboardItem>(
                ExecuteTogglePinAsync,
                CanExecuteTogglePin);

            FinalizeAndSaveNewItemCommand = new AsyncRelayCommand(
                ExecuteFinalizeAndSaveNewItemAsync,
                CanExecuteFinalizeAndSaveNewItem);

            CancelNewItemCommand = new RelayCommand(
                ExecuteCancelNewItem,
                CanExecuteCancelNewItem);

            ConfirmRenameCommand = new AsyncRelayCommand(
                ExecuteConfirmRenameAsync,
                CanExecuteConfirmRename);

            CancelRenameCommand = new RelayCommand(
                ExecuteCancelRename,
                CanExecuteCancelRename);
        }

        private void RegisterCommands()
        {
            _commandRegistry.Register("DeleteSelectedItem", DeleteSelectedItemCommand);
            _commandRegistry.Register("ClearHistory", ClearHistoryCommand);
            _commandRegistry.Register("RenameItem", RenameItemCommand);
            _commandRegistry.Register("CopyToClipboard", CopyToClipboardCommand);
            _commandRegistry.Register("PasteSelectedItem", PasteSelectedItemCommand);
            _commandRegistry.Register("TogglePin", TogglePinCommand);
            _commandRegistry.Register("FinalizeAndSaveNewItem", FinalizeAndSaveNewItemCommand);
            _commandRegistry.Register("CancelNewItem", CancelNewItemCommand);
            _commandRegistry.Register("ConfirmRename", ConfirmRenameCommand);
            _commandRegistry.Register("CancelRename", CancelRenameCommand);
        }

        #region Command Implementations

        private async Task ExecuteDeleteSelectedItemAsync()
        {
            var item = _commandContext.SelectedItem;
            if (item == null) return;

            try
            {
                await _historyManager.DeleteItemAsync(item.Id);
                _loggingService.LogInfo($"Item deleted: {item.Id}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Failed to delete item {item.Id}", ex);
                throw;
            }
        }

        private bool CanExecuteDeleteSelectedItem()
        {
            return _commandContext.SelectedItem != null && !_commandContext.IsRenamingItem;
        }

        private async Task ExecuteClearHistoryAsync()
        {
            try
            {
                // ✅ CORRECTION: Préserver les éléments épinglés lors de "Supprimer Tout"
                await _historyManager.ClearHistoryAsync(preservePinned: true);
                _commandContext.SelectedItem = null;
                _loggingService.LogInfo("History cleared (pinned items preserved)");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Failed to clear history", ex);
                throw;
            }
        }

        private bool CanExecuteClearHistory()
        {
            return !_commandContext.IsRenamingItem && !_commandContext.IsCreatingNewItem;
        }

        private void ExecuteRenameItem(ClipboardItem? item)
        {
            if (item == null) return;

            _commandContext.IsRenamingItem = true;
            _commandContext.RenamingItemId = item.Id;
            _commandContext.SelectedItem = item;
            
            _loggingService.LogInfo($"Started renaming item: {item.Id}");
        }

        private bool CanExecuteRenameItem(ClipboardItem? item)
        {
            return item != null && !_commandContext.IsRenamingItem && !_commandContext.IsCreatingNewItem;
        }

        private async Task ExecuteCopyToClipboardAsync(ClipboardItem? item)
        {
            if (item?.RawData == null) return;

            try
            {
                // Convertir les données brutes en texte pour l'instant
                // TODO: Gérer d'autres types de données (images, fichiers, etc.)
                var text = System.Text.Encoding.UTF8.GetString(item.RawData);
                await _clipboardService.SetClipboardContentAsync(text);
                _loggingService.LogInfo($"Item copied to clipboard: {item.Id}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Failed to copy item {item.Id} to clipboard", ex);
                throw;
            }
        }

        private bool CanExecuteCopyToClipboard(ClipboardItem? item)
        {
            return item?.RawData != null;
        }

        private async Task ExecutePasteSelectedItemAsync()
        {
            var item = _commandContext.SelectedItem;
            if (item == null) return;

            string operationId = Guid.NewGuid().ToString("N").Substring(0, 8);
            _loggingService.LogInfo($"[DÉBUT] PasteSelectedItem [{operationId}] - Item: {item.Id}");

            try
            {
                await _historyManager.UseItemAsync(item.Id);

                // Simuler l'action de collage (Ctrl+V)
                // Cette partie pourrait être abstraite dans un service "IUserActionSimulator"
                System.Windows.Forms.SendKeys.SendWait("^(v)");

                _loggingService.LogInfo($"[FIN] PasteSelectedItem [{operationId}] - Succès.");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"PasteSelectedItem [{operationId}] - Erreur: {ex.Message}", ex);
                throw;
            }
        }

        private bool CanExecutePasteSelectedItem()
        {
            return _commandContext.SelectedItem != null && !_commandContext.IsRenamingItem;
        }

        private async Task ExecuteTogglePinAsync(ClipboardItem? item)
        {
            if (item == null) return;

            _loggingService.LogInfo($"[DÉBUT] TogglePin - Item: {item.Id}, État actuel: {item.IsPinned}");

            try
            {
                long itemId = item.Id;
                bool oldPinnedState = item.IsPinned;

                item.IsPinned = !item.IsPinned;
                _loggingService.LogInfo($"TogglePin: Nouvel état d'épinglage: {item.IsPinned}");

                try
                {
                    await _historyManager.UpdateItemAsync(item);
                }
                catch (Exception updateEx)
                {
                    _loggingService.LogError($"TogglePin: ERREUR lors de l'appel à UpdateItem: {updateEx.Message}", updateEx);
                    item.IsPinned = oldPinnedState; // Rollback
                    throw;
                }

                _loggingService.LogInfo($"[FIN] TogglePin - Succès pour ItemID: {itemId}, Nouvel état: {(item.IsPinned ? "Épinglé" : "Non épinglé")}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"TogglePin - EXCEPTION: {ex.Message}", ex);
                throw;
            }
        }

        private bool CanExecuteTogglePin(ClipboardItem? item)
        {
            return item != null && !_commandContext.IsRenamingItem;
        }

        private async Task ExecuteFinalizeAndSaveNewItemAsync()
        {
            if (string.IsNullOrWhiteSpace(_commandContext.NewItemContent)) return;

            try
            {
                var newItem = new ClipboardItem
                {
                    DataType = ClipboardDataType.Text,
                    Timestamp = DateTime.Now,
                    RawData = System.Text.Encoding.UTF8.GetBytes(_commandContext.NewItemContent),
                    TextPreview = _commandContext.NewItemContent.Length > 100 
                        ? _commandContext.NewItemContent.Substring(0, 100) + "..." 
                        : _commandContext.NewItemContent,
                    CustomName = _commandContext.NewItemContent.Length > 50 
                        ? _commandContext.NewItemContent.Substring(0, 50) + "..." 
                        : _commandContext.NewItemContent
                };

                await _historyManager.AddItemAsync(newItem);
                
                // Réinitialiser le contexte
                _commandContext.IsCreatingNewItem = false;
                _commandContext.NewItemContent = null;
                
                _loggingService.LogInfo($"New item finalized and saved: {newItem.Id}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Failed to finalize and save new item", ex);
                throw;
            }
        }

        private bool CanExecuteFinalizeAndSaveNewItem()
        {
            return _commandContext.IsCreatingNewItem && 
                   !string.IsNullOrWhiteSpace(_commandContext.NewItemContent);
        }

        private void ExecuteCancelNewItem()
        {
            _commandContext.IsCreatingNewItem = false;
            _commandContext.NewItemContent = null;
            
            _loggingService.LogInfo("New item creation cancelled");
        }

        private bool CanExecuteCancelNewItem()
        {
            return _commandContext.IsCreatingNewItem;
        }

        private async Task ExecuteConfirmRenameAsync()
        {
            if (_commandContext.RenamingItemId == null || _commandContext.SelectedItem == null) return;

            try
            {
                // Note: Pour l'instant, nous ne modifions que le nom personnalisé
                // Dans une vraie implémentation, il faudrait une méthode UpdateItemAsync
                var item = _commandContext.SelectedItem;
                // item.CustomName = newName; // TODO: Récupérer le nouveau nom depuis l'UI
                
                await _historyManager.AddItemAsync(item); // Workaround car pas de UpdateItemAsync
                
                // Réinitialiser le contexte
                _commandContext.IsRenamingItem = false;
                _commandContext.RenamingItemId = null;
                
                _loggingService.LogInfo($"Item renamed: {item.Id}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Failed to rename item {_commandContext.RenamingItemId}", ex);
                throw;
            }
        }

        private bool CanExecuteConfirmRename()
        {
            return _commandContext.IsRenamingItem && _commandContext.RenamingItemId != null;
        }

        private void ExecuteCancelRename()
        {
            _commandContext.IsRenamingItem = false;
            _commandContext.RenamingItemId = null;
            
            _loggingService.LogInfo("Rename operation cancelled");
        }

        private bool CanExecuteCancelRename()
        {
            return _commandContext.IsRenamingItem;
        }

        #endregion

        #region ICommandModule Implementation

        /// <inheritdoc />
        public void RegisterCommand(string name, ICommand command)
        {
            _commandRegistry.Register(name, command);
        }

        /// <inheritdoc />
        public void UnregisterCommand(string name)
        {
            _commandRegistry.Unregister(name);
        }

        /// <inheritdoc />
        public ICommand? GetCommand(string name)
        {
            return _commandRegistry.Get(name);
        }

        /// <inheritdoc />
        public async Task ExecuteCommandAsync(string commandName, object? parameter = null)
        {
            var command = _commandRegistry.Get(commandName);
            if (command == null)
            {
                throw new InvalidOperationException($"Command '{commandName}' not found");
            }

            var stopwatch = Stopwatch.StartNew();
            var executingArgs = new CommandExecutingEventArgs(commandName, parameter, _commandContext);
            
            try
            {
                OnCommandExecuting(executingArgs);
                if (executingArgs.Cancel) return;

                if (command is IAsyncRelayCommand asyncCommand)
                {
                    await asyncCommand.ExecuteAsync(parameter);
                }
                else if (command.CanExecute(parameter))
                {
                    command.Execute(parameter);
                }

                stopwatch.Stop();
                OnCommandExecuted(new CommandExecutedEventArgs(commandName, parameter, _commandContext, null, stopwatch.Elapsed));
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                OnCommandFailed(new CommandFailedEventArgs(commandName, parameter, _commandContext, ex, stopwatch.Elapsed));
                throw;
            }
        }

        /// <inheritdoc />
        public bool CanExecuteCommand(string commandName, object? parameter = null)
        {
            var command = _commandRegistry.Get(commandName);
            return command?.CanExecute(parameter) ?? false;
        }

        /// <inheritdoc />
        public IEnumerable<string> GetRegisteredCommands()
        {
            return _commandRegistry.Commands.Keys;
        }

        /// <inheritdoc />
        public void InvalidateAllCommands()
        {
            foreach (var command in _commandRegistry.Commands.Values)
            {
                if (command is IRelayCommand relayCommand)
                {
                    relayCommand.NotifyCanExecuteChanged();
                }
            }
        }

        /// <inheritdoc />
        public void InvalidateCommand(string commandName)
        {
            var command = _commandRegistry.Get(commandName);
            if (command is IRelayCommand relayCommand)
            {
                relayCommand.NotifyCanExecuteChanged();
            }
        }

        #endregion

        #region Event Handlers

        protected virtual void OnCommandExecuting(CommandExecutingEventArgs e)
        {
            CommandExecuting?.Invoke(this, e);
        }

        protected virtual void OnCommandExecuted(CommandExecutedEventArgs e)
        {
            CommandExecuted?.Invoke(this, e);

            // Publier aussi sur le bus d'événements (si disponible)
            try
            {
                if (_eventAggregator != null)
                {
                    _eventAggregator.GetEvent<CommandModulePrismEvent>().Publish(new CommandModuleEventData
                    {
                        ModuleName = ModuleName ?? "CommandModule",
                        CommandName = e.CommandName ?? "Unknown",
                        Parameter = e.Parameter,
                        Result = e.Result,
                        ExecutionTime = e.ExecutionTime
                    });
                }
            }
            catch (Exception ex)
            {
                // Ignorer les erreurs de publication d'événements pour ne pas casser les tests
                _loggingService?.LogWarning($"Failed to publish CommandExecuted event: {ex.Message}");
            }
        }

        protected virtual void OnCommandFailed(CommandFailedEventArgs e)
        {
            CommandFailed?.Invoke(this, e);

            // Publier aussi sur le bus d'événements (si disponible)
            try
            {
                if (_eventAggregator != null)
                {
                    _eventAggregator.GetEvent<CommandModulePrismEvent>().Publish(new CommandModuleEventData
                    {
                        ModuleName = ModuleName ?? "CommandModule",
                        CommandName = e.CommandName ?? "Unknown",
                        Parameter = e.Parameter,
                        Result = e.Exception, // Exception comme résultat en cas d'échec
                        ExecutionTime = e.ExecutionTime
                    });
                }
            }
            catch (Exception ex)
            {
                // Ignorer les erreurs de publication d'événements pour ne pas casser les tests
                _loggingService?.LogWarning($"Failed to publish CommandFailed event: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// Implémentation du registre des commandes.
    /// </summary>
    internal class CommandRegistry : ICommandRegistry
    {
        private readonly Dictionary<string, ICommand> _commands = new();

        public IReadOnlyDictionary<string, ICommand> Commands => _commands.AsReadOnly();

        public void Register(string name, ICommand command)
        {
            _commands[name] = command;
        }

        public bool Unregister(string name)
        {
            return _commands.Remove(name);
        }

        public ICommand? Get(string name)
        {
            return _commands.TryGetValue(name, out var command) ? command : null;
        }

        public bool Contains(string name)
        {
            return _commands.ContainsKey(name);
        }

        public void Clear()
        {
            _commands.Clear();
        }
    }

    /// <summary>
    /// Implémentation du contexte d'exécution des commandes.
    /// </summary>
    internal class CommandContext : ICommandContext
    {
        private readonly Dictionary<string, object> _properties = new();

        public ClipboardItem? SelectedItem { get; set; }
        public IEnumerable<ClipboardItem> SelectedItems { get; set; } = Enumerable.Empty<ClipboardItem>();
        public string? NewItemContent { get; set; }
        public bool IsCreatingNewItem { get; set; }
        public bool IsRenamingItem { get; set; }
        public long? RenamingItemId { get; set; }

        public IDictionary<string, object> Properties => _properties;

        public T? GetProperty<T>(string key)
        {
            return _properties.TryGetValue(key, out var value) && value is T typedValue ? typedValue : default;
        }

        public void SetProperty<T>(string key, T value)
        {
            if (value != null)
            {
                _properties[key] = value;
            }
            else
            {
                _properties.Remove(key);
            }
        }

        public bool RemoveProperty(string key)
        {
            return _properties.Remove(key);
        }
    }

    /// <summary>
    /// Événement spécifique au module de commandes.
    /// </summary>
    public class CommandModuleEvent : ModuleEventBase
    {
        public string CommandName { get; }
        public object? Parameter { get; }
        public object? Result { get; }
        public TimeSpan ExecutionTime { get; }

        public CommandModuleEvent(string sourceModule, string commandName, object? parameter,
            object? result, TimeSpan executionTime)
            : base(sourceModule)
        {
            CommandName = commandName;
            Parameter = parameter;
            Result = result;
            ExecutionTime = executionTime;
        }
    }
}
