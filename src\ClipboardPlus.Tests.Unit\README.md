# Tests Unitaires pour ClipboardPlus

Ce projet contient les tests unitaires pour l'application ClipboardPlus.

## Génération des rapports de couverture de code

Pour générer un rapport de couverture de code dans un emplacement fixe, utilisez le script PowerShell `run-tests-with-coverage.ps1` disponible à la racine du projet :

```powershell
# Depuis la racine du projet
.\run-tests-with-coverage.ps1
```

Ce script exécutera tous les tests unitaires et :

1. Générera un rapport de couverture au format Cobertura XML
2. Copiera ce rapport dans le dossier `.\CodeCoverage\` à la racine du projet
3. Générera automatiquement un rapport HTML dans `.\CodeCoverage\CoverageReport_HTML\`
4. Nettoiera le dossier TestResults temporaire

Les rapports seront toujours disponibles aux emplacements suivants :
- XML : `.\CodeCoverage\coverage.cobertura.xml`
- HTML : `.\CodeCoverage\CoverageReport_HTML\index.html`

Cela facilite :
- L'intégration avec des outils d'analyse de couverture
- Le suivi de l'évolution de la couverture au fil du temps
- La configuration des outils CI/CD
- La visualisation immédiate des résultats via le rapport HTML

## Prérequis pour la génération du rapport HTML

Le script utilise ReportGenerator pour convertir le rapport XML en HTML. Si l'outil n'est pas installé, le script affichera un message avec les instructions d'installation :

```powershell
dotnet tool install -g dotnet-reportgenerator-globaltool
```

## Autres outils de visualisation des rapports de couverture

En plus du rapport HTML généré automatiquement, vous pouvez utiliser :
- Extension VS Code "Coverage Gutters" pour afficher la couverture directement dans l'éditeur
- Extensions Visual Studio pour l'analyse de couverture

## Extensions requises pour le développement

- .NET Runtime 8.0 ou supérieur
- MSTest v3 (inclus dans les dépendances NuGet)
- Coverlet (inclus dans les dépendances NuGet) 