using System;
using System.Runtime.InteropServices;
using System.Text;

namespace ClipboardPlus.UI.Native
{
    /// <summary>
    /// Classe d'encapsulation pour les fonctions natives de user32.dll
    /// </summary>
    public static class User32
    {
        /// <summary>
        /// Récupère le handle de la fenêtre actuellement au premier plan.
        /// </summary>
        /// <returns>Handle de la fenêtre active ou IntPtr.Zero en cas d'échec.</returns>
        [DllImport("user32.dll")]
        public static extern IntPtr GetForegroundWindow();

        /// <summary>
        /// Vérifie si une fenêtre est activée et peut recevoir des entrées.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre à vérifier.</param>
        /// <returns>True si la fenêtre est activée, False sinon.</returns>
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool IsWindowEnabled(IntPtr hWnd);

        /// <summary>
        /// Place la fenêtre spécifiée au premier plan.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre à mettre au premier plan.</param>
        /// <returns>True si la fenêtre a été mise au premier plan, False sinon.</returns>
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool SetForegroundWindow(IntPtr hWnd);

        /// <summary>
        /// Récupère le texte de la barre de titre d'une fenêtre.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre.</param>
        /// <param name="lpString">Buffer pour recevoir le texte.</param>
        /// <param name="nMaxCount">Taille maximale du buffer.</param>
        /// <returns>Longueur du texte copié, 0 en cas d'échec.</returns>
        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        /// <summary>
        /// Récupère la longueur du texte de la barre de titre d'une fenêtre.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre.</param>
        /// <returns>Longueur du texte, 0 en cas d'échec.</returns>
        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int GetWindowTextLength(IntPtr hWnd);

        /// <summary>
        /// Vérifie si une fenêtre est visible.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre à vérifier.</param>
        /// <returns>True si la fenêtre est visible, False sinon.</returns>
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool IsWindowVisible(IntPtr hWnd);

        /// <summary>
        /// Récupère le thread qui a créé la fenêtre spécifiée.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre.</param>
        /// <param name="lpdwProcessId">Pointeur vers une variable qui reçoit l'ID du processus.</param>
        /// <returns>ID du thread qui a créé la fenêtre.</returns>
        [DllImport("user32.dll", SetLastError = true)]
        public static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

        /// <summary>
        /// Envoie un message à une fenêtre.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre destinataire.</param>
        /// <param name="Msg">Message à envoyer.</param>
        /// <param name="wParam">Paramètre supplémentaire spécifique au message.</param>
        /// <param name="lParam">Paramètre supplémentaire spécifique au message.</param>
        /// <returns>Résultat du traitement du message.</returns>
        [DllImport("user32.dll")]
        public static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        /// <summary>
        /// Poste un message dans la file d'attente des messages de la fenêtre spécifiée et retourne immédiatement.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre destinataire.</param>
        /// <param name="Msg">Message à poster.</param>
        /// <param name="wParam">Paramètre supplémentaire spécifique au message.</param>
        /// <param name="lParam">Paramètre supplémentaire spécifique au message.</param>
        /// <returns>True si le message a été posté, False sinon.</returns>
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool PostMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        /// <summary>
        /// Récupère des informations sur l'état du clavier.
        /// </summary>
        /// <param name="nVirtKey">Code de la touche virtuelle.</param>
        /// <returns>État de la touche.</returns>
        [DllImport("user32.dll")]
        public static extern short GetKeyState(int nVirtKey);

        /// <summary>
        /// Attache le thread d'entrée d'un thread à celui d'un autre thread.
        /// </summary>
        /// <param name="idAttach">ID du thread à attacher.</param>
        /// <param name="idAttachTo">ID du thread auquel attacher.</param>
        /// <param name="fAttach">True pour attacher, False pour détacher.</param>
        /// <returns>True si l'opération a réussi, False sinon.</returns>
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool AttachThreadInput(uint idAttach, uint idAttachTo, bool fAttach);

        /// <summary>
        /// Récupère l'ID du thread actuel.
        /// </summary>
        /// <returns>ID du thread actuel.</returns>
        [DllImport("kernel32.dll")]
        public static extern uint GetCurrentThreadId();

        /// <summary>
        /// Constantes pour les touches virtuelles.
        /// </summary>
        public const int VK_CONTROL = 0x11;
        public const int VK_SHIFT = 0x10;
        public const int VK_MENU = 0x12; // ALT
        public const int VK_LWIN = 0x5B; // Touche Windows gauche
        public const int VK_RWIN = 0x5C; // Touche Windows droite

        /// <summary>
        /// Constantes pour les messages Windows.
        /// </summary>
        public const uint WM_KEYDOWN = 0x0100;
        public const uint WM_KEYUP = 0x0101;
        public const uint WM_CHAR = 0x0102;
        public const uint WM_PASTE = 0x0302;
    }
} 