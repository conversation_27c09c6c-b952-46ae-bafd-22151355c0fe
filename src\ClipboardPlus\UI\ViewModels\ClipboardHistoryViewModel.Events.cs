using ClipboardPlus.Core.DataModels; // Nécessaire pour ClipboardItem
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.Helpers; // Ajout de cet using
using System;
using System.Collections.Generic; // Nécessaire pour List<T> et HashSet<T>
using System.Diagnostics;
using System.Linq; // Nécessaire pour les méthodes LINQ comme Select, Except
using System.Threading.Tasks; // Nécessaire pour Task
using WpfApplication = System.Windows.Application; // Alias pour Application
using System.Windows; // Pour ErrorMessageHelper si utilisé directement (sinon via this)

namespace ClipboardPlus.UI.ViewModels
{
    public partial class ClipboardHistoryViewModel
    {
        // Architecture finalisée : Synchronisation via HistorySynchronizationService
        private bool _preventHistoryChangedReaction = false; // Conservé pour la compatibilité avec l'orchestrateur

        /// <summary>
        /// Gère l'événement de changement d'historique.
        ///
        /// MIGRATION TERMINÉE: Cette méthode utilise exclusivement
        /// la nouvelle architecture refactorisée avec l'orchestrateur.
        /// </summary>
        private async void ClipboardHistoryManager_HistoryChanged(object? sender, EventArgs e)
        {
            // MIGRATION FINALISÉE : Utiliser exclusivement la nouvelle architecture
            if (_historyChangeOrchestrator != null && _featureFlagService != null)
            {
                await ClipboardHistoryManager_HistoryChanged_Refactored(sender, e);
                return;
            }

            // Services critiques manquants - fallback d'urgence pour éviter les crashes
            _loggingService?.LogCritical("[ClipboardHistoryViewModel] ERREUR CRITIQUE: Services de migration non initialisés - utilisation du fallback d'urgence");

            // Fallback d'urgence minimal pour éviter les crashes
            try
            {
                if (_preventHistoryChangedReaction || _isReorderingItems || IsOperationInProgress || _isItemPasteInProgress || _clipboardHistoryManager == null)
                {
                    return;
                }
                _ = LoadHistoryAsync();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Fallback d'urgence HistoryChanged: {ex.Message}", ex);
            }
        }


    }
}