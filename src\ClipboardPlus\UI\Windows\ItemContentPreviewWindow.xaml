<Window x:Class="ClipboardPlus.UI.Windows.ItemContentPreviewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ClipboardPlus.UI.Windows"
        xmlns:imaging="clr-namespace:System.Windows.Media.Imaging;assembly=PresentationCore"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        xmlns:doc="clr-namespace:System.Windows.Documents;assembly=PresentationFramework"
        mc:Ignorable="d"
        Title="Prévisualisation du contenu" 
        Height="450" 
        Width="600"
        WindowStartupLocation="CenterScreen"
        Loaded="Window_Loaded"
        Closing="Window_Closing">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- En-tête avec informations sur l'élément -->
        <StackPanel Grid.Row="0" Margin="0,0,0,10">
            <TextBlock Text="{Binding ItemToDisplay.CustomName}" 
                       FontSize="16" 
                       FontWeight="SemiBold" 
                       Margin="0,0,0,5"/>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                <TextBlock Text="Type: " FontWeight="SemiBold"/>
                <TextBlock Text="{Binding ItemToDisplay.DataType}"/>
                <TextBlock Text=" | " Margin="5,0"/>
                <TextBlock Text="Date: " FontWeight="SemiBold"/>
                <TextBlock Text="{Binding ItemToDisplay.Timestamp, StringFormat='{}{0:dd/MM/yyyy HH:mm}'}"/>
            </StackPanel>
        </StackPanel>
        
        <!-- Zone de contenu -->
        <Border Grid.Row="1" 
                BorderBrush="#CCCCCC" 
                BorderThickness="1" 
                Padding="5">
            <ContentControl Content="{Binding FormattedPreviewContent}">
                <ContentControl.Resources>
                    <!-- Template pour les images -->
                    <DataTemplate DataType="{x:Type imaging:BitmapImage}">
                        <ScrollViewer HorizontalScrollBarVisibility="Auto" 
                                      VerticalScrollBarVisibility="Auto">
                            <Image Source="{Binding}" 
                                   Stretch="None" 
                                   HorizontalAlignment="Center" 
                                   VerticalAlignment="Center"/>
                        </ScrollViewer>
                    </DataTemplate>
                    
                    <!-- Template pour le texte -->
                    <DataTemplate DataType="{x:Type sys:String}">
                        <ScrollViewer HorizontalScrollBarVisibility="Auto" 
                                      VerticalScrollBarVisibility="Auto">
                            <TextBox Text="{Binding Mode=OneWay}" 
                                     IsReadOnly="True" 
                                     TextWrapping="Wrap" 
                                     BorderThickness="0" 
                                     Background="Transparent" 
                                     FontFamily="Consolas"/>
                        </ScrollViewer>
                    </DataTemplate>
                    
                    <!-- Template pour les documents -->
                    <DataTemplate DataType="{x:Type doc:FlowDocument}">
                        <FlowDocumentScrollViewer Document="{Binding}"/>
                    </DataTemplate>
                </ContentControl.Resources>
            </ContentControl>
        </Border>
        
        <!-- Boutons d'action -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,10,0,0">
            <Button Content="Fermer" 
                    Width="80" 
                    Height="25" 
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window> 