using ClipboardPlus.UI.ViewModels.Construction.Implementations;
using ClipboardPlus.UI.ViewModels.Construction.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.UI.ViewModels.Construction.DependencyInjection
{
    /// <summary>
    /// Extensions pour configurer l'injection de dépendances du ClipboardHistoryViewModel.
    /// Cette classe centralise l'enregistrement de tous les services nécessaires.
    /// </summary>
    public static class SOLIDArchitectureServiceCollectionExtensions
    {
        /// <summary>
        /// Enregistre tous les services pour la construction du ClipboardHistoryViewModel.
        /// Cette méthode configure l'injection de dépendances complète.
        /// </summary>
        /// <param name="services">Collection de services à configurer</param>
        /// <returns>Collection de services configurée pour chaînage fluide</returns>
        public static IServiceCollection AddClipboardHistoryViewModelSOLIDArchitecture(this IServiceCollection services)
        {
            // Enregistrement des services individuels
            services.AddTransient<IParameterValidator, ParameterValidator>();
            services.AddTransient<IServiceResolver, ServiceResolver>();
            services.AddTransient<IEventConfigurationService, EventConfigurationService>();
            services.AddTransient<ICommandInitializer, CommandInitializer>();
            services.AddTransient<IOrchestrationService, OrchestrationService>();

            // Enregistrement du Builder principal
            services.AddTransient<IClipboardHistoryViewModelBuilder, ClipboardHistoryViewModelBuilder>();

            return services;
        }

        /// <summary>
        /// Enregistre les services avec des durées de vie personnalisées.
        /// Cette méthode permet un contrôle fin sur la durée de vie de chaque service.
        /// </summary>
        /// <param name="services">Collection de services à configurer</param>
        /// <param name="builderLifetime">Durée de vie du Builder (par défaut : Transient)</param>
        /// <param name="servicesLifetime">Durée de vie des services (par défaut : Transient)</param>
        /// <returns>Collection de services configurée pour chaînage fluide</returns>
        public static IServiceCollection AddClipboardHistoryViewModelSOLIDArchitecture(
            this IServiceCollection services,
            ServiceLifetime builderLifetime = ServiceLifetime.Transient,
            ServiceLifetime servicesLifetime = ServiceLifetime.Transient)
        {
            // Enregistrement des services individuels avec durée de vie personnalisée
            services.Add(new ServiceDescriptor(typeof(IParameterValidator), typeof(ParameterValidator), servicesLifetime));
            services.Add(new ServiceDescriptor(typeof(IServiceResolver), typeof(ServiceResolver), servicesLifetime));
            services.Add(new ServiceDescriptor(typeof(IEventConfigurationService), typeof(EventConfigurationService), servicesLifetime));
            services.Add(new ServiceDescriptor(typeof(ICommandInitializer), typeof(CommandInitializer), servicesLifetime));
            services.Add(new ServiceDescriptor(typeof(IOrchestrationService), typeof(OrchestrationService), servicesLifetime));

            // Enregistrement du Builder principal avec durée de vie personnalisée
            services.Add(new ServiceDescriptor(typeof(IClipboardHistoryViewModelBuilder), typeof(ClipboardHistoryViewModelBuilder), builderLifetime));

            return services;
        }

        /// <summary>
        /// Enregistre uniquement les services de base sans le Builder.
        /// Utile pour les scénarios où le Builder est géré manuellement.
        /// </summary>
        /// <param name="services">Collection de services à configurer</param>
        /// <returns>Collection de services configurée pour chaînage fluide</returns>
        public static IServiceCollection AddClipboardHistoryViewModelSOLIDServices(this IServiceCollection services)
        {
            // Enregistrement des services individuels uniquement
            services.AddTransient<IParameterValidator, ParameterValidator>();
            services.AddTransient<IServiceResolver, ServiceResolver>();
            services.AddTransient<IEventConfigurationService, EventConfigurationService>();
            services.AddTransient<ICommandInitializer, CommandInitializer>();
            services.AddTransient<IOrchestrationService, OrchestrationService>();

            return services;
        }

        /// <summary>
        /// Valide que tous les services sont correctement enregistrés.
        /// Cette méthode peut être utilisée dans les tests ou au démarrage de l'application.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services à valider</param>
        /// <returns>True si tous les services sont disponibles, False sinon</returns>
        public static bool ValidateSOLIDArchitectureRegistration(this IServiceProvider serviceProvider)
        {
            try
            {
                // Validation de la disponibilité de tous les services
                var parameterValidator = serviceProvider.GetService<IParameterValidator>();
                var serviceResolver = serviceProvider.GetService<IServiceResolver>();
                var eventConfigurationService = serviceProvider.GetService<IEventConfigurationService>();
                var commandInitializer = serviceProvider.GetService<ICommandInitializer>();
                var orchestrationService = serviceProvider.GetService<IOrchestrationService>();
                var builder = serviceProvider.GetService<IClipboardHistoryViewModelBuilder>();

                return parameterValidator != null &&
                       serviceResolver != null &&
                       eventConfigurationService != null &&
                       commandInitializer != null &&
                       orchestrationService != null &&
                       builder != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Obtient des informations de diagnostic sur l'enregistrement des services.
        /// Utile pour le debugging et la validation de la configuration.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services à diagnostiquer</param>
        /// <returns>Dictionnaire avec le statut de chaque service</returns>
        public static Dictionary<string, bool> GetSOLIDArchitectureDiagnostics(this IServiceProvider serviceProvider)
        {
            var diagnostics = new Dictionary<string, bool>();

            try
            {
                diagnostics["IParameterValidator"] = serviceProvider.GetService<IParameterValidator>() != null;
                diagnostics["IServiceResolver"] = serviceProvider.GetService<IServiceResolver>() != null;
                diagnostics["IEventConfigurationService"] = serviceProvider.GetService<IEventConfigurationService>() != null;
                diagnostics["ICommandInitializer"] = serviceProvider.GetService<ICommandInitializer>() != null;
                diagnostics["IOrchestrationService"] = serviceProvider.GetService<IOrchestrationService>() != null;
                diagnostics["IClipboardHistoryViewModelBuilder"] = serviceProvider.GetService<IClipboardHistoryViewModelBuilder>() != null;
            }
            catch
            {
                // En cas d'erreur, marquer tous les services comme indisponibles
                diagnostics["Error"] = false;
                diagnostics["ErrorMessage"] = true; // Utiliser bool pour cohérence du type
            }

            return diagnostics;
        }
    }
}
