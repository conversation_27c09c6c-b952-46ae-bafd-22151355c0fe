using System;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.NewItem.Implementations;
using ClipboardPlus.UI.Helpers;

namespace ClipboardPlus.Tests.Unit.Core.Services.NewItem
{
    /// <summary>
    /// Tests unitaires pour ErrorHandlingService.
    /// Vérifie le respect du principe SRP : responsabilité unique de gestion d'erreurs.
    /// </summary>
    [TestFixture]
    [Category("NewItem")]
    [Category("ErrorHandling")]
    public class ErrorHandlingServiceTests
    {
        private ErrorHandlingService _service = null!;

        [SetUp]
        public void SetUp()
        {
            _service = new ErrorHandlingService();
        }

        #region Tests de Délégation vers ErrorMessageHelper

        [Test]
        [Description("Valide que HandleError délègue correctement vers ErrorMessageHelper")]
        public void HandleError_WithAllParameters_DelegatesToErrorMessageHelper()
        {
            // Arrange
            var message = "Message d'erreur de test";
            var title = "Titre de test";
            var exception = new InvalidOperationException("Exception de test");
            var context = "Contexte de test";
            var viewModel = new object();

            // Act & Assert - Ne devrait pas lever d'exception
            Assert.DoesNotThrow(() =>
            {
                _service.HandleError(message, title, exception, context, viewModel);
            }, "HandleError devrait déléguer vers ErrorMessageHelper sans lever d'exception");
        }

        [Test]
        [Description("Valide que HandleError fonctionne avec des paramètres minimaux")]
        public void HandleError_WithMinimalParameters_DelegatesToErrorMessageHelper()
        {
            // Arrange
            var message = "Message simple";
            var title = "Titre simple";

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _service.HandleError(message, title);
            }, "HandleError devrait fonctionner avec les paramètres minimaux");
        }

        [Test]
        [Description("Valide que HandleError gère les paramètres null")]
        public void HandleError_WithNullParameters_HandlesGracefully()
        {
            // Act & Assert - Tester différentes combinaisons de null
            Assert.DoesNotThrow(() =>
            {
                _service.HandleError("Message", "Titre", null, null, null);
            }, "HandleError devrait gérer les paramètres optionnels null");

            Assert.DoesNotThrow(() =>
            {
                _service.HandleError("Message", "Titre", null, "", null);
            }, "HandleError devrait gérer les chaînes vides");
        }

        #endregion

        #region Tests de Robustesse

        [Test]
        [Description("Valide que HandleError gère les chaînes vides")]
        public void HandleError_WithEmptyStrings_HandlesGracefully()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _service.HandleError("", "");
            }, "HandleError devrait gérer les chaînes vides");

            Assert.DoesNotThrow(() =>
            {
                _service.HandleError(string.Empty, string.Empty);
            }, "HandleError devrait gérer string.Empty");
        }

        [Test]
        [Description("Valide que HandleError gère les exceptions complexes")]
        public void HandleError_WithComplexException_HandlesCorrectly()
        {
            // Arrange
            var innerException = new ArgumentException("Exception interne");
            var outerException = new InvalidOperationException("Exception externe", innerException);

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _service.HandleError("Message avec exception complexe", "Titre", outerException, "Contexte complexe", new object());
            }, "HandleError devrait gérer les exceptions avec InnerException");
        }

        [Test]
        [Description("Valide que HandleError peut être appelée plusieurs fois")]
        public void HandleError_CalledMultipleTimes_WorksCorrectly()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _service.HandleError("Premier message", "Premier titre");
                _service.HandleError("Deuxième message", "Deuxième titre");
                _service.HandleError("Troisième message", "Troisième titre");
            }, "HandleError devrait pouvoir être appelée plusieurs fois");
        }

        #endregion

        #region Tests de Comportement Spécifique

        [Test]
        [Description("Valide que HandleError accepte des messages longs")]
        public void HandleError_WithLongMessage_HandlesCorrectly()
        {
            // Arrange
            var longMessage = new string('A', 1000); // Message de 1000 caractères
            var longTitle = new string('B', 100);    // Titre de 100 caractères

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _service.HandleError(longMessage, longTitle);
            }, "HandleError devrait gérer les messages longs");
        }

        [Test]
        [Description("Valide que HandleError accepte des caractères spéciaux")]
        public void HandleError_WithSpecialCharacters_HandlesCorrectly()
        {
            // Arrange
            var messageWithSpecialChars = "Message avec caractères spéciaux: àéèùç ñ 中文 🚀 \n\t\r";
            var titleWithSpecialChars = "Titre spécial: ©®™";

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _service.HandleError(messageWithSpecialChars, titleWithSpecialChars);
            }, "HandleError devrait gérer les caractères spéciaux");
        }

        [Test]
        [Description("Valide que HandleError fonctionne avec différents types de ViewModel")]
        public void HandleError_WithDifferentViewModelTypes_HandlesCorrectly()
        {
            // Arrange
            var stringViewModel = "String ViewModel";
            var intViewModel = 42;
            var complexViewModel = new { Property1 = "Value1", Property2 = 123 };

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _service.HandleError("Message 1", "Titre 1", null, "Context 1", stringViewModel);
                _service.HandleError("Message 2", "Titre 2", null, "Context 2", intViewModel);
                _service.HandleError("Message 3", "Titre 3", null, "Context 3", complexViewModel);
            }, "HandleError devrait accepter différents types de ViewModel");
        }

        #endregion

        #region Tests de Création d'Instance

        [Test]
        [Description("Valide que ErrorHandlingService peut être créée sans paramètres")]
        public void Constructor_WithoutParameters_CreatesValidInstance()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var service = new ErrorHandlingService();
                service.HandleError("Test", "Test");
            }, "ErrorHandlingService devrait pouvoir être créée sans paramètres");
        }

        [Test]
        [Description("Valide que plusieurs instances peuvent coexister")]
        public void MultipleInstances_CanCoexist()
        {
            // Arrange
            var service1 = new ErrorHandlingService();
            var service2 = new ErrorHandlingService();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                service1.HandleError("Message service 1", "Titre service 1");
                service2.HandleError("Message service 2", "Titre service 2");
            }, "Plusieurs instances d'ErrorHandlingService devraient pouvoir coexister");
        }

        #endregion

        #region Tests de Conformité Interface

        [Test]
        [Description("Valide que ErrorHandlingService implémente correctement IErrorHandlingService")]
        public void ErrorHandlingService_ImplementsInterface_Correctly()
        {
            // Act & Assert
            Assert.That(_service, Is.InstanceOf<ClipboardPlus.Core.Services.NewItem.Interfaces.IErrorHandlingService>(),
                "ErrorHandlingService devrait implémenter IErrorHandlingService");
        }

        #endregion
    }
}
