using System;

namespace ClipboardPlus.Core.Services.Visibility
{
    /// <summary>
    /// Types de visibilité supportés par le système
    /// </summary>
    public enum VisibilityType
    {
        /// <summary>
        /// Visibilité des titres personnalisés des éléments
        /// </summary>
        Title,
        
        /// <summary>
        /// Visibilité des horodatages des éléments
        /// </summary>
        Timestamp
    }

    /// <summary>
    /// Contexte pour les décisions de visibilité
    /// Contient tous les paramètres globaux nécessaires aux règles
    /// </summary>
    public class VisibilityContext
    {
        /// <summary>
        /// Indique si les titres doivent être affichés globalement
        /// </summary>
        public bool GlobalTitleVisibility { get; set; }
        
        /// <summary>
        /// Indique si les horodatages doivent être affichés globalement
        /// </summary>
        public bool GlobalTimestampVisibility { get; set; }
        
        // Extensible pour futurs critères de visibilité
        // Par exemple : ThemeMode, UserRole, etc.
    }

    /// <summary>
    /// Arguments d'événement pour les changements de visibilité
    /// Respecte le pattern EventArgs standard .NET
    /// </summary>
    public class VisibilityChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Type de visibilité qui a changé
        /// </summary>
        public VisibilityType Type { get; }
        
        /// <summary>
        /// Nouvel état de visibilité
        /// </summary>
        public bool IsVisible { get; }
        
        /// <summary>
        /// Initialise une nouvelle instance de VisibilityChangedEventArgs
        /// </summary>
        /// <param name="type">Type de visibilité concerné</param>
        /// <param name="isVisible">Nouvel état de visibilité</param>
        public VisibilityChangedEventArgs(VisibilityType type, bool isVisible)
        {
            Type = type;
            IsVisible = isVisible;
        }
    }
}
