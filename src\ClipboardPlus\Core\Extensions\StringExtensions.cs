using System;

namespace ClipboardPlus.Core.Extensions
{
    public static class StringExtensions
    {
        /// <summary>
        /// Tronque une chaîne de caractères à une longueur maximale spécifiée et ajoute des points de suspension si nécessaire.
        /// </summary>
        /// <param name="value">La chaîne à tronquer.</param>
        /// <param name="maxLength">La longueur maximale de la chaîne retournée (y compris les points de suspension).</param>
        /// <returns>La chaîne tronquée, ou la chaîne originale si elle est plus courte ou égale à la longueur maximale.</returns>
        public static string Truncate(this string? value, int maxLength)
        {
            if (string.IsNullOrEmpty(value))
            {
                return string.Empty;
            }

            if (maxLength <= 0)
            {
                return string.Empty;
            }
            
            if (maxLength <= 3) // Espace minimum pour "..."
            {
                 return value.Length > maxLength ? new string('.', maxLength) : value;
            }

            if (value.Length > maxLength)
            {
                return value.Substring(0, maxLength - 3) + "...";
            }

            return value;
        }
    }
} 