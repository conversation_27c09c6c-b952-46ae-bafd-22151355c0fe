using System;
using System.IO;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Diagnostics;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;
using System.Windows.Input;
using WpfTextBox = System.Windows.Controls.TextBox;
using WpfMessageBox = System.Windows.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;
using WpfListBox = System.Windows.Controls.ListBox;

namespace ClipboardPlus.UI.Controls
{
    /// <summary>
    /// Classe utilitaire pour diagnostiquer les problèmes de renommage
    /// </summary>
    public static class RenamingDiagnostic
    {
        private static readonly string DiagnosticFilePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
            "ClipboardPlus_Renaming_Diagnostic.txt");
            
        /// <summary>
        /// Journalise le début d'une opération de renommage
        /// </summary>
        public static void LogRenameStart(ClipboardItem item, ClipboardHistoryViewModel viewModel)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"=== DÉBUT RENOMMAGE - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} ===");
                sb.AppendLine($"Item ID: {item.Id}");
                sb.AppendLine($"Item CustomName: {item.CustomName ?? "(null)"}");
                sb.AppendLine($"Item TextPreview: {(item.TextPreview?.Length > 50 ? item.TextPreview.Substring(0, 50) + "..." : item.TextPreview ?? "(null)")}");
                sb.AppendLine($"ViewModel.ItemEnRenommage: {(viewModel.ItemEnRenommage?.Id.ToString() ?? "null")}");
                sb.AppendLine($"ViewModel.NouveauNom: {viewModel.NouveauNom ?? "(null)"}");
                sb.AppendLine();
                
                AppendToLog(sb.ToString());
                Debug.WriteLine(sb.ToString());
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LogRenameStart exception: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Journalise une tentative de confirmation de renommage
        /// </summary>
        public static void LogRenameConfirmAttempt(ClipboardHistoryViewModel? viewModel, WpfTextBox? editTextBox)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"=== TENTATIVE CONFIRMATION RENOMMAGE - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} ===");
                sb.AppendLine($"ViewModel: {(viewModel != null ? "Non null" : "NULL")}");
                
                if (viewModel != null)
                {
                    sb.AppendLine($"ViewModel.ItemEnRenommage: {(viewModel.ItemEnRenommage?.Id.ToString() ?? "null")}");
                    sb.AppendLine($"ViewModel.NouveauNom: {viewModel.NouveauNom ?? "(null)"}");
                    
                    if (viewModel.ItemEnRenommage != null)
                    {
                        sb.AppendLine($"Item ID: {viewModel.ItemEnRenommage.Id}");
                        sb.AppendLine($"Item CustomName: {viewModel.ItemEnRenommage.CustomName ?? "(null)"}");
                    }
                    
                    sb.AppendLine($"ConfirmerRenommageCommand.CanExecute: {viewModel.ConfirmerRenommageCommand.CanExecute(null)}");
                }
                
                if (editTextBox != null)
                {
                    sb.AppendLine($"TextBox.Text: {editTextBox.Text ?? "(null)"}");
                    sb.AppendLine($"TextBox.Visibility: {editTextBox.Visibility}");
                    sb.AppendLine($"TextBox.IsVisible: {editTextBox.IsVisible}");
                    sb.AppendLine($"TextBox.IsEnabled: {editTextBox.IsEnabled}");
                    sb.AppendLine($"TextBox.IsFocused: {editTextBox.IsFocused}");
                }
                
                sb.AppendLine();
                
                AppendToLog(sb.ToString());
                Debug.WriteLine(sb.ToString());
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LogRenameConfirmAttempt exception: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Journalise le résultat d'une opération de renommage
        /// </summary>
        public static void LogRenameResult(bool success, string? message = null)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"=== RÉSULTAT RENOMMAGE - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} ===");
                sb.AppendLine($"Succès: {success}");
                if (message != null)
                {
                    sb.AppendLine($"Message: {message}");
                }
                sb.AppendLine();
                
                AppendToLog(sb.ToString());
                Debug.WriteLine(sb.ToString());
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LogRenameResult exception: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Journalise une exception survenue pendant le renommage
        /// </summary>
        public static void LogRenameException(Exception ex, string context)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"=== EXCEPTION RENOMMAGE - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} ===");
                sb.AppendLine($"Contexte: {context}");
                sb.AppendLine($"Type: {ex.GetType().FullName}");
                sb.AppendLine($"Message: {ex.Message}");
                sb.AppendLine($"StackTrace: {ex.StackTrace}");
                sb.AppendLine();
                
                AppendToLog(sb.ToString());
                Debug.WriteLine(sb.ToString());
                
                // Afficher une boîte de dialogue pour informer l'utilisateur
                WpfMessageBox.Show(
                    $"Une erreur est survenue lors du renommage:\n\n{ex.Message}\n\nConsultez le fichier de diagnostic pour plus d'informations:\n{DiagnosticFilePath}",
                    "Erreur de renommage",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            catch (Exception logEx)
            {
                Debug.WriteLine($"LogRenameException exception: {logEx.Message}");
            }
        }
        
        /// <summary>
        /// Journalise l'état du focus et des éléments visuels après une opération de renommage
        /// </summary>
        public static void LogFocusState(UIElement? focusedElement, WpfListBox? historyListBox)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"=== ÉTAT DU FOCUS - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} ===");
                
                // Élément actuellement focusé
                sb.AppendLine($"Élément avec le focus: {(focusedElement?.GetType().Name ?? "null")}");
                
                // État de la ListBox
                if (historyListBox != null)
                {
                    sb.AppendLine($"ListBox.IsFocused: {historyListBox.IsFocused}");
                    sb.AppendLine($"ListBox.IsKeyboardFocused: {historyListBox.IsKeyboardFocused}");
                    sb.AppendLine($"ListBox.IsKeyboardFocusWithin: {historyListBox.IsKeyboardFocusWithin}");
                    sb.AppendLine($"ListBox.FocusVisualStyle: {(historyListBox.FocusVisualStyle != null ? "Défini" : "null")}");
                    
                    // Élément sélectionné
                    var selectedItem = historyListBox.SelectedItem;
                    sb.AppendLine($"ListBox.SelectedItem: {(selectedItem != null ? "Non null" : "null")}");
                    
                    if (selectedItem is ClipboardItem clipboardItem)
                    {
                        sb.AppendLine($"  SelectedItem.ID: {clipboardItem.Id}");
                        sb.AppendLine($"  SelectedItem.CustomName: {clipboardItem.CustomName ?? "(null)"}");
                    }
                    
                    // Élément conteneur sélectionné
                    var selectedContainer = historyListBox.ItemContainerGenerator.ContainerFromItem(selectedItem) as ListBoxItem;
                    sb.AppendLine($"SelectedContainer: {(selectedContainer != null ? "Non null" : "null")}");
                    
                    if (selectedContainer != null)
                    {
                        sb.AppendLine($"  SelectedContainer.IsFocused: {selectedContainer.IsFocused}");
                        sb.AppendLine($"  SelectedContainer.IsSelected: {selectedContainer.IsSelected}");
                        sb.AppendLine($"  SelectedContainer.FocusVisualStyle: {(selectedContainer.FocusVisualStyle != null ? "Défini" : "null")}");
                    }
                }
                else
                {
                    sb.AppendLine("ListBox: null");
                }
                
                // Élément avec le focus clavier
                var keyboardFocused = Keyboard.FocusedElement as UIElement;
                sb.AppendLine($"Keyboard.FocusedElement: {(keyboardFocused?.GetType().Name ?? "null")}");
                
                sb.AppendLine();
                
                AppendToLog(sb.ToString());
                Debug.WriteLine(sb.ToString());
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LogFocusState exception: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Ajoute du texte au fichier de diagnostic
        /// </summary>
        private static void AppendToLog(string text)
        {
            try
            {
                // Créer le répertoire parent si nécessaire
                string? directory = Path.GetDirectoryName(DiagnosticFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                // Ajouter le texte au fichier
                File.AppendAllText(DiagnosticFilePath, text, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"AppendToLog exception: {ex.Message}");
            }
        }
    }
} 