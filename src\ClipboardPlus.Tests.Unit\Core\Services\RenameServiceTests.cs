using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    [TestFixture]
    public class RenameServiceTests
    {
        private Mock<IClipboardHistoryManager> _mockHistoryManager = null!;
        private Mock<ILoggingService> _mockLogger = null!;
        private RenameService _renameService = null!;

        [SetUp]
        public void SetUp()
        {
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockLogger = new Mock<ILoggingService>();
            _renameService = new RenameService(_mockHistoryManager.Object, _mockLogger.Object);
        }

        [Test]
        public void Constructor_WithValidParameters_InitializesCorrectly()
        {
            // Act & Assert
            Assert.IsNotNull(_renameService);
        }

        [Test]
        public void Constructor_WithNullHistoryManager_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new RenameService(null!, _mockLogger.Object));
        }

        [Test]
        public void Constructor_WithNullLogger_InitializesWithoutException()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new RenameService(_mockHistoryManager.Object, null));
        }

        [Test]
        public async Task RenameItemAsync_WithValidItem_ReturnsSuccessResult()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test content", CustomName = "Old name" };
            const string newName = "New name";

            _mockHistoryManager.Setup(m => m.UpdateItemAsync(It.IsAny<ClipboardItem>()))
                              .Returns(Task.CompletedTask);

            // Act
            var result = await _renameService.RenameItemAsync(item, newName);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Success);
            Assert.AreEqual(item, result.UpdatedItem);
            Assert.AreEqual("Old name", result.OldName);
            Assert.AreEqual(newName, result.NewName);
            Assert.IsNull(result.ErrorMessage);
            Assert.AreEqual(newName, item.CustomName);
        }

        [Test]
        public async Task RenameItemAsync_WithNullItem_ReturnsFailureResult()
        {
            // Act
            var result = await _renameService.RenameItemAsync(null!, "New name");

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Success);
            Assert.IsNull(result.UpdatedItem);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(result.ErrorMessage!.Contains("ne peut pas être null"));
        }

        [Test]
        public async Task RenameItemAsync_WithEmptyNewName_ReturnsSuccessResult()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test content", CustomName = "Old name" };
            const string newName = "";

            _mockHistoryManager.Setup(m => m.UpdateItemAsync(It.IsAny<ClipboardItem>()))
                              .Returns(Task.CompletedTask);

            // Act
            var result = await _renameService.RenameItemAsync(item, newName);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Success);
            Assert.AreEqual(newName, item.CustomName);
        }

        [Test]
        public async Task RenameItemAsync_WithNullNewName_ReturnsSuccessResult()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test content", CustomName = "Old name" };

            _mockHistoryManager.Setup(m => m.UpdateItemAsync(It.IsAny<ClipboardItem>()))
                              .Returns(Task.CompletedTask);

            // Act
            var result = await _renameService.RenameItemAsync(item, null!);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Success);
            Assert.IsNull(item.CustomName);
        }

        [Test]
        public async Task RenameItemAsync_WithUpdateFailure_ReturnsFailureResult()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test content", CustomName = "Old name" };
            const string newName = "New name";
            var expectedException = new InvalidOperationException("Database error");

            _mockHistoryManager.Setup(m => m.UpdateItemAsync(It.IsAny<ClipboardItem>()))
                              .ThrowsAsync(expectedException);

            // Act
            var result = await _renameService.RenameItemAsync(item, newName);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Success);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(result.ErrorMessage!.Contains("Database error"));
        }

        [Test]
        public async Task RenameItemAsync_CallsUpdateItemAsync()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test content" };
            const string newName = "New name";

            _mockHistoryManager.Setup(m => m.UpdateItemAsync(It.IsAny<ClipboardItem>()))
                              .Returns(Task.CompletedTask);

            // Act
            await _renameService.RenameItemAsync(item, newName);

            // Assert
            _mockHistoryManager.Verify(m => m.UpdateItemAsync(item), Times.Once);
        }

        [Test]
        public async Task RenameItemAsync_LogsOperationStart()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test content", CustomName = "Old name" };
            const string newName = "New name";

            _mockHistoryManager.Setup(m => m.UpdateItemAsync(It.IsAny<ClipboardItem>()))
                              .Returns(Task.CompletedTask);

            // Act
            await _renameService.RenameItemAsync(item, newName);

            // Assert
            _mockLogger.Verify(l => l.LogDebug(It.Is<string>(s => s.Contains("Début du renommage"))), Times.Once);
        }

        [Test]
        public async Task RenameItemAsync_LogsOperationSuccess()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test content", CustomName = "Old name" };
            const string newName = "New name";

            _mockHistoryManager.Setup(m => m.UpdateItemAsync(It.IsAny<ClipboardItem>()))
                              .Returns(Task.CompletedTask);

            // Act
            await _renameService.RenameItemAsync(item, newName);

            // Assert
            _mockLogger.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Renommage réussi"))), Times.Once);
        }

        [Test]
        public async Task RenameItemAsync_LogsOperationFailure()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test content" };
            const string newName = "New name";
            var expectedException = new InvalidOperationException("Test error");

            _mockHistoryManager.Setup(m => m.UpdateItemAsync(It.IsAny<ClipboardItem>()))
                              .ThrowsAsync(expectedException);

            // Act
            await _renameService.RenameItemAsync(item, newName);

            // Assert
            _mockLogger.Verify(l => l.LogError(It.Is<string>(s => s.Contains("Erreur lors du renommage")), expectedException), Times.Once);
        }

        [Test]
        public async Task RenameItemAsync_WithSameName_StillUpdates()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test content", CustomName = "Same name" };
            const string newName = "Same name";

            _mockHistoryManager.Setup(m => m.UpdateItemAsync(It.IsAny<ClipboardItem>()))
                              .Returns(Task.CompletedTask);

            // Act
            var result = await _renameService.RenameItemAsync(item, newName);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Success);
            _mockHistoryManager.Verify(m => m.UpdateItemAsync(item), Times.Once);
        }
    }

    [TestFixture]
    public class RenameResultTests
    {
        [Test]
        public void Constructor_InitializesWithDefaults()
        {
            // Act
            var result = new RenameResult();

            // Assert
            Assert.IsFalse(result.Success);
            Assert.IsNull(result.UpdatedItem);
            Assert.IsNull(result.OldName);
            Assert.IsNull(result.NewName);
            Assert.IsNull(result.ErrorMessage);
        }

        [Test]
        public void Properties_CanBeSetAndRetrieved()
        {
            // Arrange
            var result = new RenameResult();
            var testItem = new ClipboardItem { Id = 1, TextPreview = "Test" };

            // Act
            result.Success = true;
            result.UpdatedItem = testItem;
            result.OldName = "Old";
            result.NewName = "New";
            result.ErrorMessage = "Error";

            // Assert
            Assert.IsTrue(result.Success);
            Assert.AreEqual(testItem, result.UpdatedItem);
            Assert.AreEqual("Old", result.OldName);
            Assert.AreEqual("New", result.NewName);
            Assert.AreEqual("Error", result.ErrorMessage);
        }

        [Test]
        public void CreateSuccess_WithValidParameters_ReturnsSuccessResult()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test" };
            const string oldName = "Old name";
            const string newName = "New name";

            // Act
            var result = RenameResult.CreateSuccess(item, oldName, newName);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Success);
            Assert.AreEqual(item, result.UpdatedItem);
            Assert.AreEqual(oldName, result.OldName);
            Assert.AreEqual(newName, result.NewName);
            Assert.IsNull(result.ErrorMessage);
        }

        [Test]
        public void CreateSuccess_WithNullNames_ReturnsSuccessResult()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, TextPreview = "Test" };

            // Act
            var result = RenameResult.CreateSuccess(item, null, null);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Success);
            Assert.AreEqual(item, result.UpdatedItem);
            Assert.IsNull(result.OldName);
            Assert.IsNull(result.NewName);
            Assert.IsNull(result.ErrorMessage);
        }

        [Test]
        public void CreateFailure_WithErrorMessage_ReturnsFailureResult()
        {
            // Arrange
            const string errorMessage = "Test error message";

            // Act
            var result = RenameResult.CreateFailure(errorMessage);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Success);
            Assert.IsNull(result.UpdatedItem);
            Assert.IsNull(result.OldName);
            Assert.IsNull(result.NewName);
            Assert.AreEqual(errorMessage, result.ErrorMessage);
        }

        [Test]
        public void CreateFailure_WithAllParameters_ReturnsFailureResult()
        {
            // Arrange
            const string errorMessage = "Test error";
            const string oldName = "Old";
            const string newName = "New";

            // Act
            var result = RenameResult.CreateFailure(errorMessage, oldName, newName);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Success);
            Assert.IsNull(result.UpdatedItem);
            Assert.AreEqual(oldName, result.OldName);
            Assert.AreEqual(newName, result.NewName);
            Assert.AreEqual(errorMessage, result.ErrorMessage);
        }

        [Test]
        public void CreateFailure_WithNullErrorMessage_ReturnsFailureResult()
        {
            // Act
            var result = RenameResult.CreateFailure(null!);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Success);
            Assert.IsNull(result.ErrorMessage);
        }
    }
}
