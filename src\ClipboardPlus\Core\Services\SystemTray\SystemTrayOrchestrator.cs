using System;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Controls;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Windows;
using System.Windows.Interop;
using ClipboardPlus.Native;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Implémentation de l'orchestrateur principal du système de notification.
    /// Responsabilité unique : orchestrer l'initialisation complète du système tray et gérer son cycle de vie.
    /// </summary>
    public class SystemTrayOrchestrator : ISystemTrayOrchestrator
    {
        private readonly IThreadValidator _threadValidator;
        private readonly INotifyIconCleanupService _cleanupService;
        private readonly INotifyIconFactory _notifyIconFactory;
        private readonly IIconResourceLoader _iconLoader;
        private readonly IContextMenuBuilder _menuBuilder;
        private readonly IVisibilityManager _visibilityManager;
        private readonly IStartupNotificationService _notificationService;
        private readonly ILoggingService _loggingService;
        private readonly IServiceProvider _serviceProvider;
        private readonly IHistoryWindowService _historyWindowService;
        private readonly ISettingsWindowService _settingsWindowService;

        private NotifyIcon? _notifyIcon;
        private System.Windows.Controls.ContextMenu? _contextMenu;
        private SimpleContextMenuService? _simpleContextMenuService; // SOLUTION ULTRA-SIMPLE
        private NativeContextMenuService? _nativeContextMenuService; // SOLUTION NATIVE FINALE
        private bool _isInitialized;
        private bool _disposed;

        /// <summary>
        /// Initialise une nouvelle instance de SystemTrayOrchestrator.
        /// Version originale avec IServiceProvider (pour compatibilité temporaire).
        /// </summary>
        public SystemTrayOrchestrator(
            IThreadValidator threadValidator,
            INotifyIconCleanupService cleanupService,
            INotifyIconFactory notifyIconFactory,
            IIconResourceLoader iconLoader,
            IContextMenuBuilder menuBuilder,
            IVisibilityManager visibilityManager,
            IStartupNotificationService notificationService,
            ILoggingService loggingService,
            IServiceProvider serviceProvider,
            IHistoryWindowService historyWindowService,
            ISettingsWindowService settingsWindowService)
        {
            _threadValidator = threadValidator ?? throw new ArgumentNullException(nameof(threadValidator));
            _cleanupService = cleanupService ?? throw new ArgumentNullException(nameof(cleanupService));
            _notifyIconFactory = notifyIconFactory ?? throw new ArgumentNullException(nameof(notifyIconFactory));
            _iconLoader = iconLoader ?? throw new ArgumentNullException(nameof(iconLoader));
            _menuBuilder = menuBuilder ?? throw new ArgumentNullException(nameof(menuBuilder));
            _visibilityManager = visibilityManager ?? throw new ArgumentNullException(nameof(visibilityManager));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _historyWindowService = historyWindowService ?? throw new ArgumentNullException(nameof(historyWindowService));
            _settingsWindowService = settingsWindowService ?? throw new ArgumentNullException(nameof(settingsWindowService));
        }

        /// <summary>
        /// Initialise une nouvelle instance de SystemTrayOrchestrator.
        /// Version V2 sans Service Locator - utilise uniquement l'injection de dépendances explicite.
        /// </summary>
        public SystemTrayOrchestrator(
            IThreadValidator threadValidator,
            INotifyIconCleanupService cleanupService,
            INotifyIconFactory notifyIconFactory,
            IIconResourceLoader iconLoader,
            IContextMenuBuilder menuBuilder,
            IVisibilityManager visibilityManager,
            IStartupNotificationService notificationService,
            ILoggingService loggingService,
            IHistoryWindowService historyWindowService,
            ISettingsWindowService settingsWindowService)
        {
            _threadValidator = threadValidator ?? throw new ArgumentNullException(nameof(threadValidator));
            _cleanupService = cleanupService ?? throw new ArgumentNullException(nameof(cleanupService));
            _notifyIconFactory = notifyIconFactory ?? throw new ArgumentNullException(nameof(notifyIconFactory));
            _iconLoader = iconLoader ?? throw new ArgumentNullException(nameof(iconLoader));
            _menuBuilder = menuBuilder ?? throw new ArgumentNullException(nameof(menuBuilder));
            _visibilityManager = visibilityManager ?? throw new ArgumentNullException(nameof(visibilityManager));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _historyWindowService = historyWindowService ?? throw new ArgumentNullException(nameof(historyWindowService));
            _settingsWindowService = settingsWindowService ?? throw new ArgumentNullException(nameof(settingsWindowService));

            // Note: _serviceProvider n'est plus utilisé dans cette version V2
            _serviceProvider = null!; // Temporaire pour éviter les erreurs de compilation
        }

        /// <inheritdoc />
        public bool IsInitialized => _isInitialized && !_disposed;

        /// <inheritdoc />
        public void Initialize()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SystemTrayOrchestrator));
            }

            _loggingService.LogInfo("========== SystemTrayOrchestrator.Initialize: DÉBUT ==========");

            try
            {
                // Étape 1: Validation du thread UI
                _threadValidator.ValidateUIThread();

                // Étape 2: Nettoyage des instances existantes
                if (_cleanupService.RequiresCleanup(_notifyIcon))
                {
                    _cleanupService.CleanupExistingIcon(_notifyIcon);
                    _notifyIcon = null;
                    _contextMenu = null;
                }

                // Étape 3: Création du NotifyIcon
                _notifyIcon = _notifyIconFactory.CreateNotifyIcon("ClipboardPlus", visible: true);

                // Étape 4: Chargement et configuration de l'icône
                var icon = _iconLoader.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico");
                _notifyIconFactory.ConfigureIcon(_notifyIcon, icon);

                // Étape 5: Construction du menu contextuel
                _contextMenu = _menuBuilder.BuildContextMenu(
                    onHistoryClick: () => HandleHistoryClick(),
                    onSettingsClick: () => HandleSettingsClick(),
                    onExitClick: () => HandleExitClick()
                );

                // Étape 5.1: Menu contextuel créé et prêt pour affichage
                _loggingService.LogInfo("SystemTrayOrchestrator: Menu contextuel créé et prêt pour affichage");

                // SOLUTION ULTRA-SIMPLE : Initialiser le service de menu simple
                _simpleContextMenuService = new SimpleContextMenuService(_loggingService);
                _loggingService.LogInfo("SystemTrayOrchestrator: Service de menu contextuel ULTRA-SIMPLE initialisé");

                // SOLUTION NATIVE FINALE : Initialiser le service de menu natif
                _nativeContextMenuService = new NativeContextMenuService(_loggingService);
                _loggingService.LogInfo("SystemTrayOrchestrator: Service de menu contextuel NATIF initialisé");

                // Étape 6: Configuration des événements
                ConfigureEvents();

                // Étape 7: Gestion de la visibilité
                _visibilityManager.ConfigureInitialVisibility(_notifyIcon);

                // Étape 8: Affichage de la notification de démarrage
                _notificationService.ShowStartupNotification(_notifyIcon);

                _isInitialized = true;
                _loggingService.LogInfo("SystemTrayOrchestrator: Initialisation terminée avec succès");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"SystemTrayOrchestrator: Erreur lors de l'initialisation: {ex.Message}", ex);
                
                // Nettoyage en cas d'erreur
                CleanupResources();
                
                throw; // Remonter l'exception
            }
            finally
            {
                _loggingService.LogInfo("========== SystemTrayOrchestrator.Initialize: FIN ==========");
            }
        }

        /// <summary>
        /// Version V2 de Initialize qui utilise l'injection de dépendances explicite
        /// au lieu du Service Locator pattern.
        /// Cette méthode utilise les services IHistoryWindowService et ISettingsWindowService injectés.
        /// </summary>
        public void Initialize_V2()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SystemTrayOrchestrator));
            }

            _loggingService.LogInfo("========== SystemTrayOrchestrator.Initialize_V2: DÉBUT (Version sans Service Locator) ==========");

            try
            {
                // Étape 1: Validation du thread UI
                _threadValidator.ValidateUIThread();

                // Étape 2: Nettoyage des instances existantes
                if (_cleanupService.RequiresCleanup(_notifyIcon))
                {
                    _cleanupService.CleanupExistingIcon(_notifyIcon);
                    _notifyIcon = null;
                    _contextMenu = null;
                }

                // Étape 3: Création du NotifyIcon
                _notifyIcon = _notifyIconFactory.CreateNotifyIcon("ClipboardPlus", visible: true);

                // Étape 4: Chargement et configuration de l'icône
                var icon = _iconLoader.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico");
                _notifyIconFactory.ConfigureIcon(_notifyIcon, icon);

                // Étape 5: Construction du menu contextuel avec les nouvelles méthodes V2
                _contextMenu = _menuBuilder.BuildContextMenu(
                    onHistoryClick: () => HandleHistoryClick_V2(),
                    onSettingsClick: () => HandleSettingsClick_V2(),
                    onExitClick: () => HandleExitClick()
                );

                // Étape 5.1: Menu contextuel créé et prêt pour affichage
                _loggingService.LogInfo("SystemTrayOrchestrator: Menu contextuel V2 créé et prêt pour affichage");

                // SOLUTION ULTRA-SIMPLE : Initialiser le service de menu simple
                _simpleContextMenuService = new SimpleContextMenuService(_loggingService);
                _loggingService.LogInfo("SystemTrayOrchestrator: Service de menu contextuel ULTRA-SIMPLE initialisé (V2)");

                // SOLUTION NATIVE FINALE : Initialiser le service de menu natif
                _nativeContextMenuService = new NativeContextMenuService(_loggingService);
                _loggingService.LogInfo("SystemTrayOrchestrator: Service de menu contextuel NATIF initialisé (V2)");

                // Étape 6: Configuration des événements
                ConfigureEvents();

                // Étape 7: Gestion de la visibilité
                bool isVisible = _visibilityManager.ConfigureInitialVisibility(_notifyIcon);
                _loggingService.LogInfo($"SystemTrayOrchestrator: Visibilité configurée - Visible: {isVisible}");

                // Étape 8: Finalisation
                _isInitialized = true;
                _loggingService.LogInfo("SystemTrayOrchestrator: Initialisation V2 terminée avec succès");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"SystemTrayOrchestrator.Initialize_V2: Erreur lors de l'initialisation: {ex.Message}", ex);
                throw;
            }
            finally
            {
                _loggingService.LogInfo("========== SystemTrayOrchestrator.Initialize_V2: FIN ==========");
            }
        }

        /// <inheritdoc />
        public void ShowNotification(string title, string message, ToolTipIcon iconType)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SystemTrayOrchestrator));
            }

            if (!_isInitialized || _notifyIcon == null)
            {
                _loggingService.LogError("SystemTrayOrchestrator.ShowNotification: L'orchestrateur n'est pas initialisé");
                return;
            }

            _notificationService.ShowCustomNotification(_notifyIcon, title, message, iconType);
        }

        private void ConfigureEvents()
        {
            if (_notifyIcon == null)
            {
                throw new InvalidOperationException("NotifyIcon n'est pas initialisé");
            }

            _loggingService.LogInfo("SystemTrayOrchestrator: Configuration des événements...");

            // Diagnostic de l'état du NotifyIcon
            _loggingService.LogInfo($"SystemTrayOrchestrator: [DIAGNOSTIC] État NotifyIcon - Visible: {_notifyIcon.Visible}, Text: '{_notifyIcon.Text}', Icon: {(_notifyIcon.Icon != null ? "Présent" : "Null")}");

            // Configuration de l'événement MouseUp pour capturer les clics droits
            _loggingService.LogInfo("SystemTrayOrchestrator: [DIAGNOSTIC] Attachement de l'événement MouseUp...");
            _notifyIcon.MouseUp += OnNotifyIconMouseUp;
            _loggingService.LogInfo("SystemTrayOrchestrator: [DIAGNOSTIC] Événement MouseUp attaché avec succès");

            // Ajoutons aussi MouseDown et MouseClick pour diagnostic
            _loggingService.LogInfo("SystemTrayOrchestrator: [DIAGNOSTIC] Attachement d'événements supplémentaires pour diagnostic...");
            _notifyIcon.MouseDown += OnNotifyIconMouseDown_Diagnostic;
            _notifyIcon.MouseClick += OnNotifyIconMouseClick_Diagnostic;
            _loggingService.LogInfo("SystemTrayOrchestrator: [DIAGNOSTIC] Tous les événements de diagnostic attachés");

            // Test final de l'état
            _loggingService.LogInfo($"SystemTrayOrchestrator: [DIAGNOSTIC] Configuration terminée - NotifyIcon.Visible: {_notifyIcon.Visible}");

            _loggingService.LogInfo("SystemTrayOrchestrator: Événements configurés avec succès");
        }

        private void OnNotifyIconMouseUp(object? sender, MouseEventArgs e)
        {
            try
            {
                _loggingService.LogInfo($"SystemTrayOrchestrator: [MouseUp] Clic souris détecté - Bouton: {e.Button}");

                if (e.Button == MouseButtons.Left)
                {
                    // Fermer le menu contextuel s'il est ouvert avant d'ouvrir l'historique
                    if (_contextMenu != null && _contextMenu.IsOpen)
                    {
                        _contextMenu.IsOpen = false;
                        _loggingService.LogInfo("SystemTrayOrchestrator: [DIAGNOSTIC] Menu fermé par clic gauche");
                    }

                    HandleHistoryClick();
                }
                else if (e.Button == MouseButtons.Right && _contextMenu != null)
                {
                    // RETOUR À L'ANCIEN SYSTÈME : Utiliser le ContextMenu original
                    _loggingService.LogInfo("🔄 [MENU_ORIGINAL] SystemTrayOrchestrator: CLIC DROIT - retour à l'ancien système");

                    // Positionner le menu à la position de la souris
                    _contextMenu.PlacementTarget = null;
                    _contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.MousePoint;

                    // Ouvrir le menu - WPF gère automatiquement la fermeture
                    _contextMenu.IsOpen = true;

                    // SOLUTION: Forcer le focus sur le menu pour qu'il se ferme correctement
                    if (System.Windows.Interop.ComponentDispatcher.IsThreadModal)
                    {
                        // Ne rien faire si un autre menu est déjà ouvert
                    }
                    else
                    {
                        if (PresentationSource.FromVisual(_contextMenu) is HwndSource hwndSource)
                        {
                            _loggingService.LogInfo("SystemTrayOrchestrator: Application de la solution SetForegroundWindow.");
                            NativeMethods.SetForegroundWindow(hwndSource.Handle);
                        }
                        else
                        {
                            _loggingService.LogWarning("SystemTrayOrchestrator: Impossible d'obtenir HwndSource pour le menu contextuel.");
                        }
                    }

                    _loggingService.LogInfo("🔄 [MENU_ORIGINAL] Menu contextuel original ouvert");
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"SystemTrayOrchestrator: Erreur lors du traitement du clic souris: {ex.Message}", ex);
            }
        }

        private async void HandleHistoryClick()
        {
            try
            {
                _loggingService.LogInfo("SystemTrayOrchestrator: Gestion du clic sur 'Afficher l'historique'");

                // Récupérer le service et appeler ShowHistoryWindow
                var systemTrayService = _serviceProvider.GetService(typeof(ISystemTrayService)) as ISystemTrayService;
                if (systemTrayService != null)
                {
                    await systemTrayService.ShowHistoryWindow();
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"SystemTrayOrchestrator: Erreur lors de l'affichage de l'historique: {ex.Message}", ex);
            }
        }

        private void HandleSettingsClick()
        {
            try
            {
                _loggingService.LogInfo("SystemTrayOrchestrator: Gestion du clic sur 'Paramètres'");
                
                // Récupérer le service et appeler OpenSettingsWindow
                var systemTrayService = _serviceProvider.GetService(typeof(ISystemTrayService)) as ISystemTrayService;
                systemTrayService?.OpenSettingsWindow();
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"SystemTrayOrchestrator: Erreur lors de l'ouverture des paramètres: {ex.Message}", ex);
            }
        }



        /// <summary>
        /// Version V2 de HandleHistoryClick qui utilise l'injection de dépendances explicite
        /// au lieu du Service Locator pattern.
        /// Cette méthode utilise le service IHistoryWindowService injecté.
        /// </summary>
        private void HandleHistoryClick_V2()
        {
            try
            {
                _loggingService.LogInfo("SystemTrayOrchestrator: Gestion du clic sur 'Afficher l'historique' (V2 - sans Service Locator)");

                // Utiliser le service injecté directement au lieu du Service Locator
                _historyWindowService.ShowHistoryWindowAsync();

                _loggingService.LogInfo("SystemTrayOrchestrator: Commande d'affichage de l'historique envoyée avec succès (V2)");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"SystemTrayOrchestrator: Erreur lors de l'affichage de l'historique (V2): {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Version V2 de HandleSettingsClick qui utilise l'injection de dépendances explicite
        /// au lieu du Service Locator pattern.
        /// Cette méthode utilise le service ISettingsWindowService injecté.
        /// </summary>
        private void HandleSettingsClick_V2()
        {
            try
            {
                _loggingService.LogInfo("SystemTrayOrchestrator: Gestion du clic sur 'Paramètres' (V2 - sans Service Locator)");

                // Utiliser le service injecté directement au lieu du Service Locator
                _settingsWindowService.OpenSettingsWindowAsync();

                _loggingService.LogInfo("SystemTrayOrchestrator: Commande d'ouverture des paramètres envoyée avec succès (V2)");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"SystemTrayOrchestrator: Erreur lors de l'ouverture des paramètres (V2): {ex.Message}", ex);
            }
        }

        private void HandleExitClick()
        {
            try
            {
                _loggingService.LogInfo("SystemTrayOrchestrator: Gestion du clic sur 'Quitter'");
                
                // Fermer l'application
                System.Windows.Application.Current?.Shutdown();
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"SystemTrayOrchestrator: Erreur lors de la fermeture de l'application: {ex.Message}", ex);
            }
        }

        private void OnNotifyIconMouseDown_Diagnostic(object? sender, MouseEventArgs e)
        {
            _loggingService.LogInfo($"SystemTrayOrchestrator: [DIAGNOSTIC] [MouseDown] Bouton: {e.Button}, Position: ({e.X}, {e.Y})");
        }

        private void OnNotifyIconMouseClick_Diagnostic(object? sender, MouseEventArgs e)
        {
            _loggingService.LogInfo($"SystemTrayOrchestrator: [DIAGNOSTIC] [MouseClick] Bouton: {e.Button}, Position: ({e.X}, {e.Y})");
        }



        private void CleanupResources()
        {
            try
            {
                _loggingService.LogInfo("SystemTrayOrchestrator: Nettoyage des ressources...");

                if (_notifyIcon != null)
                {
                    _cleanupService.CleanupExistingIcon(_notifyIcon);
                    _notifyIcon = null;
                }

                _contextMenu = null;
                _isInitialized = false;

                _loggingService.LogInfo("SystemTrayOrchestrator: Nettoyage des ressources terminé");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"SystemTrayOrchestrator: Erreur lors du nettoyage des ressources: {ex.Message}", ex);
            }
        }

        /// <inheritdoc />
        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            _loggingService.LogInfo("SystemTrayOrchestrator: Disposition des ressources...");

            CleanupResources();

            _disposed = true;

            _loggingService.LogInfo("SystemTrayOrchestrator: Disposition terminée");
        }
    }
}
