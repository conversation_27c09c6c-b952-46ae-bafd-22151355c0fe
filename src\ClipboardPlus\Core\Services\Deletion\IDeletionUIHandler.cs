using System.Collections.ObjectModel;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Service de gestion UI pour les opérations de suppression.
    /// Gère la suppression et le rollback des éléments dans l'interface utilisateur.
    /// </summary>
    public interface IDeletionUIHandler
    {
        /// <summary>
        /// Supprime un élément de la collection UI et mémorise sa position pour un éventuel rollback.
        /// </summary>
        /// <param name="collection">La collection d'éléments UI</param>
        /// <param name="item">L'élément à supprimer</param>
        /// <returns>Les informations de suppression pour un éventuel rollback</returns>
        UIRemovalInfo RemoveFromUI(ObservableCollection<ClipboardItem> collection, ClipboardItem item);

        /// <summary>
        /// Restaure un élément dans la collection UI à sa position d'origine.
        /// </summary>
        /// <param name="collection">La collection d'éléments UI</param>
        /// <param name="removalInfo">Les informations de suppression</param>
        void RollbackUI(ObservableCollection<ClipboardItem> collection, UIRemovalInfo removalInfo);
    }

    /// <summary>
    /// Informations sur la suppression d'un élément UI pour permettre le rollback.
    /// </summary>
    public class UIRemovalInfo
    {
        /// <summary>
        /// L'élément qui a été supprimé.
        /// </summary>
        public ClipboardItem Item { get; set; } = null!;

        /// <summary>
        /// Position originale de l'élément dans la collection.
        /// </summary>
        public int OriginalIndex { get; set; }

        /// <summary>
        /// Indique si la suppression UI a réussi.
        /// </summary>
        public bool RemovalSuccessful { get; set; }

        /// <summary>
        /// Timestamp de la suppression pour le diagnostic.
        /// </summary>
        public DateTime RemovalTimestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Crée des informations de suppression réussie.
        /// </summary>
        public static UIRemovalInfo CreateSuccess(ClipboardItem item, int originalIndex)
        {
            return new UIRemovalInfo
            {
                Item = item,
                OriginalIndex = originalIndex,
                RemovalSuccessful = true
            };
        }

        /// <summary>
        /// Crée des informations de suppression échouée.
        /// </summary>
        public static UIRemovalInfo CreateFailure(ClipboardItem item)
        {
            return new UIRemovalInfo
            {
                Item = item,
                OriginalIndex = -1,
                RemovalSuccessful = false
            };
        }
    }
}
