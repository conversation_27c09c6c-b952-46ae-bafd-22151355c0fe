using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels.Settings;

namespace ClipboardPlus.Core.Services.Settings
{
    /// <summary>
    /// Service responsable des notifications et messages liés aux paramètres.
    /// Centralise l'envoi des messages WeakReference et notifications utilisateur.
    /// </summary>
    public interface ISettingsNotificationService
    {
        /// <summary>
        /// Envoie les messages de changement de visibilité (HideTimestamp, HideItemTitle).
        /// </summary>
        /// <param name="settings">Paramètres de visibilité à notifier</param>
        /// <returns>Tâche représentant l'opération asynchrone</returns>
        Task SendVisibilityChangedMessagesAsync(VisibilitySettings settings);

        /// <summary>
        /// Notifie que les paramètres ont été appliqués avec succès ou erreur.
        /// </summary>
        /// <param name="result">Résultat de l'application des paramètres</param>
        /// <returns>Tâche représentant l'opération asynchrone</returns>
        Task NotifySettingsAppliedAsync(SettingsApplicationResult result);
    }
}
