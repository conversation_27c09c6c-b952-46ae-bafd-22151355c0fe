using System;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;

namespace ClipboardPlus.Tests.Unit.Core.Services.SystemTray
{
    /// <summary>
    /// Tests unitaires pour NotifyIconFactory.
    /// Vérifie la responsabilité unique : création et configuration de NotifyIcon.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class NotifyIconFactoryTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private NotifyIconFactory _factory;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _factory = new NotifyIconFactory(_mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new NotifyIconFactory(null));
        }

        [Test]
        public void CreateNotifyIcon_WithValidText_CreatesNotifyIcon()
        {
            // Arrange
            const string text = "Test Icon";

            // Act
            var result = _factory.CreateNotifyIcon(text);

            // Assert
            Assert.That(result, Is.Not.Null, "Should create NotifyIcon instance");
            Assert.That(result.Text, Is.EqualTo(text), "Should set correct text");
            Assert.That(result.Visible, Is.False, "Should default to invisible");

            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains($"texte: '{text}', visible: False"))),
                Times.Once,
                "Should log creation with correct parameters");

            _mockLoggingService.Verify(
                x => x.LogInfo("NotifyIconFactory: Instance NotifyIcon créée avec succès."),
                Times.Once,
                "Should log successful creation");

            // Cleanup
            result.Dispose();
        }

        [Test]
        public void CreateNotifyIcon_WithVisibleTrue_CreatesVisibleIcon()
        {
            // Arrange
            const string text = "Test Icon";

            // Act
            var result = _factory.CreateNotifyIcon(text, visible: true);

            // Assert
            Assert.That(result.Visible, Is.True, "Should create visible icon when specified");

            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("visible: True"))),
                Times.Once,
                "Should log creation with visible=true");

            // Cleanup
            result.Dispose();
        }

        [Test]
        public void CreateNotifyIcon_WithNullText_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => _factory.CreateNotifyIcon(null));
            Assert.That(ex.ParamName, Is.EqualTo("text"));
        }

        [Test]
        public void CreateNotifyIcon_WithEmptyText_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => _factory.CreateNotifyIcon(""));
            Assert.That(ex.ParamName, Is.EqualTo("text"));
        }

        [Test]
        public void CreateNotifyIcon_WithWhitespaceText_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => _factory.CreateNotifyIcon("   "));
            Assert.That(ex.ParamName, Is.EqualTo("text"));
        }

        [Test]
        public void ConfigureIcon_WithValidParameters_ConfiguresIcon()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();
            var icon = SystemIcons.Application;

            // Act
            _factory.ConfigureIcon(notifyIcon, icon);

            // Assert
            Assert.That(notifyIcon.Icon, Is.EqualTo(icon), "Should set the icon correctly");

            _mockLoggingService.Verify(
                x => x.LogInfo("NotifyIconFactory: Configuration de l'icône du NotifyIcon..."),
                Times.Once,
                "Should log configuration start");

            _mockLoggingService.Verify(
                x => x.LogInfo("NotifyIconFactory: Icône configurée avec succès."),
                Times.Once,
                "Should log successful configuration");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ConfigureIcon_WithNullNotifyIcon_ThrowsArgumentNullException()
        {
            // Arrange
            var icon = SystemIcons.Application;

            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _factory.ConfigureIcon(null, icon));
            Assert.That(ex.ParamName, Is.EqualTo("notifyIcon"));
        }

        [Test]
        public void ConfigureIcon_WithNullIcon_ThrowsArgumentNullException()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _factory.ConfigureIcon(notifyIcon, null));
            Assert.That(ex.ParamName, Is.EqualTo("icon"));

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void CreateNotifyIcon_WithException_LogsErrorAndThrows()
        {
            // Arrange - Créer un mock qui lève une exception lors du logging
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo(It.IsAny<string>()))
                             .Throws(new InvalidOperationException("Test exception"));

            var factory = new NotifyIconFactory(mockLoggingService.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => factory.CreateNotifyIcon("Test"));

            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de la création de NotifyIcon")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when creation fails");
        }

        [Test]
        public void ConfigureIcon_WithException_LogsErrorAndThrows()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();
            var icon = SystemIcons.Application;

            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo("NotifyIconFactory: Configuration de l'icône du NotifyIcon..."))
                             .Throws(new InvalidOperationException("Test exception"));

            var factory = new NotifyIconFactory(mockLoggingService.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => factory.ConfigureIcon(notifyIcon, icon));

            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de la configuration de l'icône")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when configuration fails");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void Factory_IsStateless()
        {
            // Act - Créer plusieurs icônes
            var icon1 = _factory.CreateNotifyIcon("Icon1");
            var icon2 = _factory.CreateNotifyIcon("Icon2");

            // Assert - Chaque icône doit être indépendante
            Assert.That(icon1.Text, Is.EqualTo("Icon1"));
            Assert.That(icon2.Text, Is.EqualTo("Icon2"));
            Assert.That(icon1, Is.Not.SameAs(icon2), "Factory should create independent instances");

            // Cleanup
            icon1.Dispose();
            icon2.Dispose();
        }

        [Test]
        public void CreateNotifyIcon_MultipleCallsWithSameParameters_CreatesDifferentInstances()
        {
            // Act
            var icon1 = _factory.CreateNotifyIcon("Test");
            var icon2 = _factory.CreateNotifyIcon("Test");

            // Assert
            Assert.That(icon1, Is.Not.SameAs(icon2), "Should create different instances for each call");
            Assert.That(icon1.Text, Is.EqualTo(icon2.Text), "But with same configuration");

            // Cleanup
            icon1.Dispose();
            icon2.Dispose();
        }

        [TearDown]
        public void TearDown()
        {
            // Nettoyer les ressources si nécessaire
        }
    }
}
