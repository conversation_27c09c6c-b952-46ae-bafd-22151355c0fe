using System;
using System.Drawing;
using System.Windows.Forms;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Implémentation de la factory pour NotifyIcon.
    /// Responsabilité unique : créer et configurer les instances NotifyIcon.
    /// </summary>
    public class NotifyIconFactory : INotifyIconFactory
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de NotifyIconFactory.
        /// </summary>
        /// <param name="loggingService">Service de logging pour enregistrer les opérations de création.</param>
        public NotifyIconFactory(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <inheritdoc />
        public NotifyIcon CreateNotifyIcon(string text, bool visible = false)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                throw new ArgumentException("Le texte de l'icône ne peut pas être vide.", nameof(text));
            }

            try
            {
                _loggingService.LogInfo($"NotifyIconFactory: Création d'une nouvelle instance NotifyIcon avec texte: '{text}', visible: {visible}");

                var notifyIcon = new NotifyIcon
                {
                    Text = text,
                    Visible = visible
                };

                _loggingService.LogInfo("NotifyIconFactory: Instance NotifyIcon créée avec succès.");

                return notifyIcon;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"NotifyIconFactory: Erreur lors de la création de NotifyIcon: {ex.Message}", ex);
                throw; // Remonter l'exception car la création est critique
            }
        }

        /// <inheritdoc />
        public void ConfigureIcon(NotifyIcon notifyIcon, Icon icon)
        {
            if (notifyIcon == null)
            {
                throw new ArgumentNullException(nameof(notifyIcon));
            }

            if (icon == null)
            {
                throw new ArgumentNullException(nameof(icon));
            }

            try
            {
                _loggingService.LogInfo("NotifyIconFactory: Configuration de l'icône du NotifyIcon...");

                notifyIcon.Icon = icon;

                _loggingService.LogInfo("NotifyIconFactory: Icône configurée avec succès.");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"NotifyIconFactory: Erreur lors de la configuration de l'icône: {ex.Message}", ex);
                throw; // Remonter l'exception car la configuration est critique
            }
        }
    }
}
