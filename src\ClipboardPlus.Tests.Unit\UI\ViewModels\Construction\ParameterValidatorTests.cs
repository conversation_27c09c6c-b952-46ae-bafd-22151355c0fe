using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels.Construction.Implementations;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.UI.ViewModels.Construction
{
    /// <summary>
    /// Tests unitaires pour ParameterValidator.
    /// Validation du comportement de validation des paramètres obligatoires.
    /// </summary>
    [TestFixture]
    [Category("Unit")]
    [Category("ParameterValidator")]
    public class ParameterValidatorTests
    {
        private ParameterValidator _validator = null!;
        private Mock<IClipboardHistoryManager> _mockHistoryManager = null!;
        private Mock<IClipboardInteractionService> _mockClipboardService = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IUserNotificationService> _mockNotificationService = null!;
        private Mock<IUserInteractionService> _mockUserInteractionService = null!;
        private Mock<IServiceProvider> _mockServiceProvider = null!;
        private Mock<IRenameService> _mockRenameService = null!;

        [SetUp]
        public void Setup()
        {
            _validator = new ParameterValidator();
            
            // Créer tous les mocks nécessaires
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockClipboardService = new Mock<IClipboardInteractionService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockNotificationService = new Mock<IUserNotificationService>();
            _mockUserInteractionService = new Mock<IUserInteractionService>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockRenameService = new Mock<IRenameService>();
        }

        [Test]
        public void ValidateRequiredParameters_WithAllValidParameters_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _validator.ValidateRequiredParameters(
                _mockHistoryManager.Object,
                _mockClipboardService.Object,
                _mockSettingsManager.Object,
                _mockNotificationService.Object,
                _mockUserInteractionService.Object,
                _mockServiceProvider.Object,
                _mockRenameService.Object
            ));
        }

        [Test]
        public void ValidateRequiredParameters_WithNullHistoryManager_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _validator.ValidateRequiredParameters(
                null!,
                _mockClipboardService.Object,
                _mockSettingsManager.Object,
                _mockNotificationService.Object,
                _mockUserInteractionService.Object,
                _mockServiceProvider.Object,
                _mockRenameService.Object
            ));
            
            Assert.That(ex.ParamName, Is.EqualTo("historyManager"));
        }

        [Test]
        public void ValidateRequiredParameters_WithNullClipboardService_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _validator.ValidateRequiredParameters(
                _mockHistoryManager.Object,
                null!,
                _mockSettingsManager.Object,
                _mockNotificationService.Object,
                _mockUserInteractionService.Object,
                _mockServiceProvider.Object,
                _mockRenameService.Object
            ));
            
            Assert.That(ex.ParamName, Is.EqualTo("clipboardService"));
        }

        [Test]
        public void ValidateRequiredParameters_WithNullSettingsManager_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _validator.ValidateRequiredParameters(
                _mockHistoryManager.Object,
                _mockClipboardService.Object,
                null!,
                _mockNotificationService.Object,
                _mockUserInteractionService.Object,
                _mockServiceProvider.Object,
                _mockRenameService.Object
            ));
            
            Assert.That(ex.ParamName, Is.EqualTo("settingsManager"));
        }

        [Test]
        public void ValidateRequiredParameters_WithNullNotificationService_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _validator.ValidateRequiredParameters(
                _mockHistoryManager.Object,
                _mockClipboardService.Object,
                _mockSettingsManager.Object,
                null!,
                _mockUserInteractionService.Object,
                _mockServiceProvider.Object,
                _mockRenameService.Object
            ));
            
            Assert.That(ex.ParamName, Is.EqualTo("notificationService"));
        }

        [Test]
        public void ValidateRequiredParameters_WithNullUserInteractionService_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _validator.ValidateRequiredParameters(
                _mockHistoryManager.Object,
                _mockClipboardService.Object,
                _mockSettingsManager.Object,
                _mockNotificationService.Object,
                null!,
                _mockServiceProvider.Object,
                _mockRenameService.Object
            ));
            
            Assert.That(ex.ParamName, Is.EqualTo("userInteractionService"));
        }

        [Test]
        public void ValidateRequiredParameters_WithNullServiceProvider_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _validator.ValidateRequiredParameters(
                _mockHistoryManager.Object,
                _mockClipboardService.Object,
                _mockSettingsManager.Object,
                _mockNotificationService.Object,
                _mockUserInteractionService.Object,
                null!,
                _mockRenameService.Object
            ));
            
            Assert.That(ex.ParamName, Is.EqualTo("serviceProvider"));
        }

        [Test]
        public void ValidateRequiredParameters_WithNullRenameService_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _validator.ValidateRequiredParameters(
                _mockHistoryManager.Object,
                _mockClipboardService.Object,
                _mockSettingsManager.Object,
                _mockNotificationService.Object,
                _mockUserInteractionService.Object,
                _mockServiceProvider.Object,
                null!
            ));
            
            Assert.That(ex.ParamName, Is.EqualTo("renameService"));
        }
    }
}
