using System;
using System.Reflection;
using System.ComponentModel;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using System.Collections.Generic;

namespace ClipboardPlus.Tests.Unit.Core.DataModels
{
    /// <summary>
    /// Tests supplémentaires pour l'ApplicationSettings qui ne nécessitent pas de thread STA.
    /// Ces tests se concentrent sur la structure et les comportements internes.
    /// </summary>
    [TestFixture]
    public class ApplicationSettingsExtendedTests
    {
        [Test]
        public void ApplicationSettings_RequiredFields_ShouldExist()
        {
            // Arrange & Act
            Type type = typeof(ApplicationSettings);
            
            // Assert
            Assert.That(type.GetField("_maxHistoryItems", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _maxHistoryItems devrait exister");
            Assert.That(type.GetField("_activeThemePath", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _activeThemePath devrait exister");
            Assert.That(type.GetField("_shortcutKeyCombination", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _shortcutKeyCombination devrait exister");
            Assert.That(type.GetField("_startWithWindows", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _startWithWindows devrait exister");
            Assert.That(type.GetField("_maxImageDimensionForThumbnail", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _maxImageDimensionForThumbnail devrait exister");
            Assert.That(type.GetField("_maxTextPreviewLength", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _maxTextPreviewLength devrait exister");
            Assert.That(type.GetField("_maxStorableItemSizeBytes", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _maxStorableItemSizeBytes devrait exister");
            Assert.That(type.GetField("_hideTimestamp", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _hideTimestamp devrait exister");
            Assert.That(type.GetField("_hideItemTitle", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _hideItemTitle devrait exister");
            Assert.That(type.GetField("_settingsWindowWidth", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _settingsWindowWidth devrait exister");
            Assert.That(type.GetField("_settingsWindowHeight", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _settingsWindowHeight devrait exister");
            Assert.That(type.GetField("_settingsWindowTop", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _settingsWindowTop devrait exister");
            Assert.That(type.GetField("_settingsWindowLeft", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _settingsWindowLeft devrait exister");
        }

        [Test]
        public void ApplicationSettings_RequiredProperties_ShouldExist()
        {
            // Arrange & Act
            Type type = typeof(ApplicationSettings);
            
            // Assert
            Assert.That(type.GetProperty("MaxHistoryItems"), Is.Not.Null,
                "La propriété MaxHistoryItems devrait exister");
            Assert.That(type.GetProperty("ActiveThemePath"), Is.Not.Null,
                "La propriété ActiveThemePath devrait exister");
            Assert.That(type.GetProperty("ShortcutKeyCombination"), Is.Not.Null,
                "La propriété ShortcutKeyCombination devrait exister");
            Assert.That(type.GetProperty("StartWithWindows"), Is.Not.Null,
                "La propriété StartWithWindows devrait exister");
            Assert.That(type.GetProperty("MaxImageDimensionForThumbnail"), Is.Not.Null,
                "La propriété MaxImageDimensionForThumbnail devrait exister");
            Assert.That(type.GetProperty("MaxTextPreviewLength"), Is.Not.Null,
                "La propriété MaxTextPreviewLength devrait exister");
            Assert.That(type.GetProperty("MaxStorableItemSizeBytes"), Is.Not.Null,
                "La propriété MaxStorableItemSizeBytes devrait exister");
            Assert.That(type.GetProperty("HideTimestamp"), Is.Not.Null,
                "La propriété HideTimestamp devrait exister");
            Assert.That(type.GetProperty("HideItemTitle"), Is.Not.Null,
                "La propriété HideItemTitle devrait exister");
            Assert.That(type.GetProperty("SettingsWindowWidth"), Is.Not.Null,
                "La propriété SettingsWindowWidth devrait exister");
            Assert.That(type.GetProperty("SettingsWindowHeight"), Is.Not.Null,
                "La propriété SettingsWindowHeight devrait exister");
            Assert.That(type.GetProperty("SettingsWindowTop"), Is.Not.Null,
                "La propriété SettingsWindowTop devrait exister");
            Assert.That(type.GetProperty("SettingsWindowLeft"), Is.Not.Null,
                "La propriété SettingsWindowLeft devrait exister");
        }

        [Test]
        public void ApplicationSettings_PropertiesShouldHaveCorrectTypes()
        {
            // Arrange & Act
            Type type = typeof(ApplicationSettings);
            
            // Assert
            Assert.That(type.GetProperty("MaxHistoryItems")?.PropertyType, Is.EqualTo(typeof(int)),
                "MaxHistoryItems devrait être de type int");
            Assert.That(type.GetProperty("ActiveThemePath")?.PropertyType, Is.EqualTo(typeof(string)),
                "ActiveThemePath devrait être de type string");
            Assert.That(type.GetProperty("ShortcutKeyCombination")?.PropertyType, Is.EqualTo(typeof(string)),
                "ShortcutKeyCombination devrait être de type string");
            Assert.That(type.GetProperty("StartWithWindows")?.PropertyType, Is.EqualTo(typeof(bool)),
                "StartWithWindows devrait être de type bool");
            Assert.That(type.GetProperty("MaxImageDimensionForThumbnail")?.PropertyType, Is.EqualTo(typeof(int)),
                "MaxImageDimensionForThumbnail devrait être de type int");
            Assert.That(type.GetProperty("MaxTextPreviewLength")?.PropertyType, Is.EqualTo(typeof(int)),
                "MaxTextPreviewLength devrait être de type int");
            Assert.That(type.GetProperty("MaxStorableItemSizeBytes")?.PropertyType, Is.EqualTo(typeof(long)),
                "MaxStorableItemSizeBytes devrait être de type long");
            Assert.That(type.GetProperty("HideTimestamp")?.PropertyType, Is.EqualTo(typeof(bool)),
                "HideTimestamp devrait être de type bool");
            Assert.That(type.GetProperty("HideItemTitle")?.PropertyType, Is.EqualTo(typeof(bool)),
                "HideItemTitle devrait être de type bool");
            Assert.That(type.GetProperty("SettingsWindowWidth")?.PropertyType, Is.EqualTo(typeof(double)),
                "SettingsWindowWidth devrait être de type double");
            Assert.That(type.GetProperty("SettingsWindowHeight")?.PropertyType, Is.EqualTo(typeof(double)),
                "SettingsWindowHeight devrait être de type double");
            Assert.That(type.GetProperty("SettingsWindowTop")?.PropertyType, Is.EqualTo(typeof(double)),
                "SettingsWindowTop devrait être de type double");
            Assert.That(type.GetProperty("SettingsWindowLeft")?.PropertyType, Is.EqualTo(typeof(double)),
                "SettingsWindowLeft devrait être de type double");
        }

        [Test]
        public void ApplicationSettings_PropertiesShouldHaveGettersAndSetters()
        {
            // Arrange & Act
            Type type = typeof(ApplicationSettings);
            
            // Assert
            foreach (var propertyName in new[] 
            {
                "MaxHistoryItems", "ActiveThemePath", "ShortcutKeyCombination", "StartWithWindows",
                "MaxImageDimensionForThumbnail", "MaxTextPreviewLength", "MaxStorableItemSizeBytes",
                "HideTimestamp", "HideItemTitle", "SettingsWindowWidth", "SettingsWindowHeight",
                "SettingsWindowTop", "SettingsWindowLeft"
            })
            {
                var property = type.GetProperty(propertyName);
                Assert.That(property?.CanRead, Is.True, $"La propriété {propertyName} devrait avoir un getter");
                Assert.That(property?.CanWrite, Is.True, $"La propriété {propertyName} devrait avoir un setter");
            }
        }

        [Test]
        public void ApplicationSettings_DefaultFieldValues_ShouldBeCorrect()
        {
            // Arrange
            Type type = typeof(ApplicationSettings);
            
            // Act & Assert
            var maxHistoryItemsField = type.GetField("_maxHistoryItems", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(maxHistoryItemsField?.GetValue(new ApplicationSettings()), Is.EqualTo(50),
                "La valeur par défaut de _maxHistoryItems devrait être 50");
            
            var shortcutKeyCombinationField = type.GetField("_shortcutKeyCombination", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(shortcutKeyCombinationField?.GetValue(new ApplicationSettings()), Is.EqualTo("Ctrl+Alt+V"),
                "La valeur par défaut de _shortcutKeyCombination devrait être 'Ctrl+Alt+V'");
            
            var startWithWindowsField = type.GetField("_startWithWindows", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(startWithWindowsField?.GetValue(new ApplicationSettings()), Is.EqualTo(false),
                "La valeur par défaut de _startWithWindows devrait être false");
            
            var maxImageDimensionForThumbnailField = type.GetField("_maxImageDimensionForThumbnail", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(maxImageDimensionForThumbnailField?.GetValue(new ApplicationSettings()), Is.EqualTo(256),
                "La valeur par défaut de _maxImageDimensionForThumbnail devrait être 256");
            
            var maxTextPreviewLengthField = type.GetField("_maxTextPreviewLength", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(maxTextPreviewLengthField?.GetValue(new ApplicationSettings()), Is.EqualTo(30),
                "La valeur par défaut de _maxTextPreviewLength devrait être 30");
            
            var maxStorableItemSizeBytesField = type.GetField("_maxStorableItemSizeBytes", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(maxStorableItemSizeBytesField?.GetValue(new ApplicationSettings()), Is.EqualTo(10 * 1024 * 1024),
                "La valeur par défaut de _maxStorableItemSizeBytes devrait être 10 Mo");
            
            var hideTimestampField = type.GetField("_hideTimestamp", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(hideTimestampField?.GetValue(new ApplicationSettings()), Is.EqualTo(false),
                "La valeur par défaut de _hideTimestamp devrait être false");
            
            var hideItemTitleField = type.GetField("_hideItemTitle", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(hideItemTitleField?.GetValue(new ApplicationSettings()), Is.EqualTo(false),
                "La valeur par défaut de _hideItemTitle devrait être false");
            
            var settingsWindowWidthField = type.GetField("_settingsWindowWidth", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(settingsWindowWidthField?.GetValue(new ApplicationSettings()), Is.EqualTo(525),
                "La valeur par défaut de _settingsWindowWidth devrait être 525");
            
            var settingsWindowHeightField = type.GetField("_settingsWindowHeight", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(settingsWindowHeightField?.GetValue(new ApplicationSettings()), Is.EqualTo(480),
                "La valeur par défaut de _settingsWindowHeight devrait être 480");
            
            var settingsWindowTopField = type.GetField("_settingsWindowTop", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(settingsWindowTopField?.GetValue(new ApplicationSettings()), Is.EqualTo(-1),
                "La valeur par défaut de _settingsWindowTop devrait être -1");
            
            var settingsWindowLeftField = type.GetField("_settingsWindowLeft", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(settingsWindowLeftField?.GetValue(new ApplicationSettings()), Is.EqualTo(-1),
                "La valeur par défaut de _settingsWindowLeft devrait être -1");
        }

        [Test]
        public void ApplicationSettings_ShouldImplementINotifyPropertyChanged()
        {
            // Arrange & Act
            Type type = typeof(ApplicationSettings);
            
            // Assert
            Assert.That(typeof(INotifyPropertyChanged).IsAssignableFrom(type), Is.True,
                "ApplicationSettings devrait implémenter INotifyPropertyChanged");
        }
        
        [Test]
        public void ApplicationSettings_PropertyChangedEventShouldBeRaised_ForAllProperties()
        {
            // Arrange
            var settings = new ApplicationSettings();
            var propertyChangedEvents = new Dictionary<string, bool>();
            
            // Initialiser le dictionnaire avec toutes les propriétés à tester
            string[] propertiesToTest = new[]
            {
                "MaxHistoryItems", "ActiveThemePath", "ShortcutKeyCombination", "StartWithWindows",
                "MaxImageDimensionForThumbnail", "MaxTextPreviewLength", "MaxStorableItemSizeBytes",
                "HideTimestamp", "HideItemTitle", "SettingsWindowWidth", "SettingsWindowHeight",
                "SettingsWindowTop", "SettingsWindowLeft"
            };
            
            foreach (var prop in propertiesToTest)
            {
                propertyChangedEvents[prop] = false;
            }
            
            // S'abonner à l'événement PropertyChanged
            ((INotifyPropertyChanged)settings).PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName != null && propertyChangedEvents.ContainsKey(e.PropertyName))
                {
                    propertyChangedEvents[e.PropertyName] = true;
                }
            };
            
            // Act - Modifier chaque propriété
            settings.MaxHistoryItems = 100;
            settings.ActiveThemePath = "NewThemePath";
            settings.ShortcutKeyCombination = "Ctrl+Shift+V";
            settings.StartWithWindows = true;
            settings.MaxImageDimensionForThumbnail = 512;
            settings.MaxTextPreviewLength = 50;
            settings.MaxStorableItemSizeBytes = 20 * 1024 * 1024;
            settings.HideTimestamp = true;
            settings.HideItemTitle = true;
            settings.SettingsWindowWidth = 600;
            settings.SettingsWindowHeight = 550;
            settings.SettingsWindowTop = 100;
            settings.SettingsWindowLeft = 100;
            
            // Assert - Vérifier que chaque événement PropertyChanged a été déclenché
            foreach (var prop in propertiesToTest)
            {
                Assert.That(propertyChangedEvents[prop], Is.True,
                    $"L'événement PropertyChanged n'a pas été déclenché pour la propriété {prop}");
            }
        }
        
        [Test]
        public void ApplicationSettings_PropertyChangedEventShouldNotBeRaised_WhenValueIsUnchanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            var propertyChangedCount = 0;
            
            // Définir des valeurs initiales
            settings.MaxHistoryItems = 100;
            settings.ActiveThemePath = "TestThemePath";
            
            // Réinitialiser le compteur après la configuration initiale
            ((INotifyPropertyChanged)settings).PropertyChanged += (sender, e) => propertyChangedCount++;
            propertyChangedCount = 0;
            
            // Act - Affecter les mêmes valeurs
            settings.MaxHistoryItems = 100;
            settings.ActiveThemePath = "TestThemePath";
            
            // Assert
            Assert.That(propertyChangedCount, Is.EqualTo(0),
                "Aucun événement PropertyChanged ne devrait être déclenché lorsque la valeur ne change pas");
        }
        
        [Test]
        public void ApplicationSettings_PropertyValidation_ShouldAcceptAnyValue()
        {
            // Arrange
            var settings = new ApplicationSettings();
            
            // Act & Assert - MaxHistoryItems
            settings.MaxHistoryItems = -10;
            Assert.That(settings.MaxHistoryItems, Is.EqualTo(-10),
                "MaxHistoryItems devrait accepter n'importe quelle valeur");
            
            settings.MaxHistoryItems = 1000;
            Assert.That(settings.MaxHistoryItems, Is.EqualTo(1000),
                "MaxHistoryItems devrait accepter n'importe quelle valeur");
            
            // Act & Assert - MaxImageDimensionForThumbnail
            settings.MaxImageDimensionForThumbnail = 0;
            Assert.That(settings.MaxImageDimensionForThumbnail, Is.EqualTo(0),
                "MaxImageDimensionForThumbnail devrait accepter n'importe quelle valeur");
            
            settings.MaxImageDimensionForThumbnail = 2000;
            Assert.That(settings.MaxImageDimensionForThumbnail, Is.EqualTo(2000),
                "MaxImageDimensionForThumbnail devrait accepter n'importe quelle valeur");
            
            // Act & Assert - MaxTextPreviewLength
            settings.MaxTextPreviewLength = 0;
            Assert.That(settings.MaxTextPreviewLength, Is.EqualTo(0),
                "MaxTextPreviewLength devrait accepter n'importe quelle valeur");
            
            settings.MaxTextPreviewLength = 1000;
            Assert.That(settings.MaxTextPreviewLength, Is.EqualTo(1000),
                "MaxTextPreviewLength devrait accepter n'importe quelle valeur");
            
            // Act & Assert - MaxStorableItemSizeBytes
            settings.MaxStorableItemSizeBytes = 0;
            Assert.That(settings.MaxStorableItemSizeBytes, Is.EqualTo(0),
                "MaxStorableItemSizeBytes devrait accepter n'importe quelle valeur");
            
            long maxBytes = 100L * 1024 * 1024;
            settings.MaxStorableItemSizeBytes = maxBytes * 2;
            Assert.That(settings.MaxStorableItemSizeBytes, Is.EqualTo(maxBytes * 2),
                "MaxStorableItemSizeBytes devrait accepter n'importe quelle valeur");
        }
        
        [Test]
        public void ApplicationSettings_ActiveThemePath_ShouldAcceptNullOrEmpty()
        {
            // Arrange
            var settings = new ApplicationSettings();
            string originalPath = settings.ActiveThemePath;
            
            // Act - Null
            settings.ActiveThemePath = null!;
            
            // Assert - Null
            // La classe accepte les valeurs null, donc on vérifie simplement que la propriété est null
            Assert.That(settings.ActiveThemePath, Is.Null,
                "ActiveThemePath devrait accepter null");
            
            // Act - Empty
            settings.ActiveThemePath = string.Empty;
            
            // Assert - Empty
            Assert.That(settings.ActiveThemePath, Is.EqualTo(string.Empty),
                "ActiveThemePath devrait accepter une chaîne vide");
            
            // Act - Whitespace
            settings.ActiveThemePath = "   ";
            
            // Assert - Whitespace
            Assert.That(settings.ActiveThemePath, Is.EqualTo("   "),
                "ActiveThemePath devrait accepter une chaîne composée uniquement d'espaces");
        }
        
        [Test]
        public void ApplicationSettings_ShortcutKeyCombination_ShouldAcceptNullOrEmpty()
        {
            // Arrange
            var settings = new ApplicationSettings();
            string originalShortcut = settings.ShortcutKeyCombination;
            
            // Act - Null
            settings.ShortcutKeyCombination = null!;
            
            // Assert - Null
            // La classe accepte les valeurs null, donc on vérifie simplement que la propriété est null
            Assert.That(settings.ShortcutKeyCombination, Is.Null,
                "ShortcutKeyCombination devrait accepter null");
            
            // Act - Empty
            settings.ShortcutKeyCombination = string.Empty;
            
            // Assert - Empty
            Assert.That(settings.ShortcutKeyCombination, Is.EqualTo(string.Empty),
                "ShortcutKeyCombination devrait accepter une chaîne vide");
            
            // Act - Whitespace
            settings.ShortcutKeyCombination = "   ";
            
            // Assert - Whitespace
            Assert.That(settings.ShortcutKeyCombination, Is.EqualTo("   "),
                "ShortcutKeyCombination devrait accepter une chaîne composée uniquement d'espaces");
        }
    }
} 