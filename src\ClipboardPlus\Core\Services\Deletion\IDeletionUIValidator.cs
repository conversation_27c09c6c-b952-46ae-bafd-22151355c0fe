using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Service de validation spécifique pour les opérations de suppression UI.
    /// Encapsule la logique de validation pour SupprimerElement() afin de réduire la complexité cyclomatique.
    /// </summary>
    public interface IDeletionUIValidator
    {
        /// <summary>
        /// Valide qu'un élément peut être supprimé depuis l'interface utilisateur.
        /// </summary>
        /// <param name="item">L'élément à valider</param>
        /// <returns>Le résultat de la validation</returns>
        UIValidationResult ValidateItem(ClipboardItem? item);

        /// <summary>
        /// Valide que le gestionnaire d'historique est disponible et accessible.
        /// </summary>
        /// <param name="manager">Le gestionnaire à valider</param>
        /// <returns>Le résultat de la validation</returns>
        UIValidationResult ValidateHistoryManager(IClipboardHistoryManager? manager);

        /// <summary>
        /// Valide qu'aucune opération n'est en cours.
        /// </summary>
        /// <param name="isOperationInProgress">Indique si une opération est en cours</param>
        /// <returns>Le résultat de la validation</returns>
        UIValidationResult ValidateOperationState(bool isOperationInProgress);
    }

    /// <summary>
    /// Résultat d'une validation UI.
    /// </summary>
    public class UIValidationResult
    {
        /// <summary>
        /// Indique si la validation a réussi.
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Message d'erreur en cas d'échec de validation.
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// Code d'erreur pour le diagnostic.
        /// </summary>
        public string ErrorCode { get; set; } = string.Empty;

        /// <summary>
        /// Crée un résultat de validation réussie.
        /// </summary>
        public static UIValidationResult Success()
        {
            return new UIValidationResult { IsValid = true };
        }

        /// <summary>
        /// Crée un résultat de validation échouée.
        /// </summary>
        public static UIValidationResult Failure(string errorMessage, string errorCode = "")
        {
            return new UIValidationResult 
            { 
                IsValid = false, 
                ErrorMessage = errorMessage,
                ErrorCode = errorCode
            };
        }
    }
}
