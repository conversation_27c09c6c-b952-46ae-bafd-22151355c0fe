using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;

namespace ClipboardPlus.Tests.Unit.Services
{
    /// <summary>
    /// Tests pour ApplicationExitService - Validation de l'équivalence comportementale avec App.OnExit
    /// </summary>
    [TestFixture]
    public class ApplicationExitServiceTests
    {
        private Mock<IServiceProvider> _mockServiceProvider = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IApplicationLifetimeManager> _mockLifetimeManager = null!;
        private ApplicationExitService _applicationExitService = null!;

        [SetUp]
        public void SetUp()
        {
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockLifetimeManager = new Mock<IApplicationLifetimeManager>();
            
            _applicationExitService = new ApplicationExitService();
        }

        [Test]
        public void ExecuteExitSequenceAsync_WithNullServices_ShouldNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _applicationExitService.ExecuteExitSequenceAsync(null));
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_WithNullLoggingService_ShouldStillExecuteLogic()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns((ILoggingService?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert
            _mockSettingsManager.Verify(sm => sm.SaveSettingsToPersistenceAsync(), Times.Once);
            _mockLifetimeManager.Verify(lm => lm.Shutdown(
                _mockServiceProvider.Object, null, null, false), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_WithNullSettingsManager_ShouldSkipSettingsSave()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns((ISettingsManager?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Début de la fermeture de l'application"), Times.Once);
            _mockLifetimeManager.Verify(lm => lm.Shutdown(
                _mockServiceProvider.Object, null, null, false), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_WithNullLifetimeManager_ShouldSkipShutdown()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns((IApplicationLifetimeManager?)null);

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert
            _mockSettingsManager.Verify(sm => sm.SaveSettingsToPersistenceAsync(), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Début de la fermeture de l'application"), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_WithValidSettingsManager_ShouldLogAllWindowParameters()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            _mockSettingsManager.Setup(sm => sm.SettingsWindowWidth).Returns(800.0);
            _mockSettingsManager.Setup(sm => sm.SettingsWindowHeight).Returns(600.0);
            _mockSettingsManager.Setup(sm => sm.SettingsWindowTop).Returns(100.0);
            _mockSettingsManager.Setup(sm => sm.SettingsWindowLeft).Returns(200.0);
            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .Returns(Task.CompletedTask);

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] Sauvegarde finale des paramètres..."), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] -> Width: 800"), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] -> Height: 600"), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] -> Top: 100"), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] -> Left: 200"), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_WithSettingsSaveException_ShouldLogCriticalWithSpecificMessage()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            var testException = new InvalidOperationException("Test settings save error");
            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .ThrowsAsync(testException);

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogCritical(
                "OnExit: EXCEPTION CRITIQUE lors de la sauvegarde des paramètres.", testException), Times.Once);
            // Vérifier que l'exécution continue malgré l'exception
            _mockLifetimeManager.Verify(lm => lm.Shutdown(
                _mockServiceProvider.Object, null, null, false), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_WithLifetimeManagerException_ShouldLogCriticalInGlobalCatch()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            var testException = new InvalidOperationException("Test lifetime manager error");
            _mockLifetimeManager.Setup(lm => lm.Shutdown(It.IsAny<IServiceProvider>(), null, null, false))
                .Throws(testException);

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogCritical(
                $"OnExit: Exception non gérée lors de la fermeture: {testException.Message}", testException), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_ShouldCallShutdownWithExactParameters_Services_Null_Null_False()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert
            _mockLifetimeManager.Verify(lm => lm.Shutdown(
                _mockServiceProvider.Object, null, null, false), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_ShouldAlwaysForceFlushInFinally()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            // Simuler une exception pour vérifier que ForceFlush est appelé dans finally
            var testException = new InvalidOperationException("Test exception");
            _mockLifetimeManager.Setup(lm => lm.Shutdown(It.IsAny<IServiceProvider>(), null, null, false))
                .Throws(testException);

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert
            _mockLoggingService.Verify(ls => ls.ForceFlush(), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Fermeture de l'application terminée"), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_WithValidServices_ShouldExecuteCompleteSequence()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .Returns(Task.CompletedTask);

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert - Vérifier la séquence complète
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Début de la fermeture de l'application"), Times.Once);
            _mockSettingsManager.Verify(sm => sm.SaveSettingsToPersistenceAsync(), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Sauvegarde finale des paramètres terminée avec succès."), Times.Once);
            _mockLifetimeManager.Verify(lm => lm.Shutdown(_mockServiceProvider.Object, null, null, false), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Fermeture de l'application terminée"), Times.Once);
            _mockLoggingService.Verify(ls => ls.ForceFlush(), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_UsesAsyncCorrectly_NoGetAwaiterGetResult()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);

            var taskCompletionSource = new TaskCompletionSource<bool>();
            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .Returns(taskCompletionSource.Task);

            // Act - Démarrer l'exécution sans attendre
            var executionTask = _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);
            
            // Vérifier que la tâche n'est pas encore terminée (preuve que c'est vraiment asynchrone)
            Assert.IsFalse(executionTask.IsCompleted);
            
            // Compléter la tâche de sauvegarde
            taskCompletionSource.SetResult(true);
            
            // Attendre la fin
            await executionTask;

            // Assert
            _mockSettingsManager.Verify(sm => sm.SaveSettingsToPersistenceAsync(), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Sauvegarde finale des paramètres terminée avec succès."), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_WithTimeoutScenario_ShouldHandleTimeoutGracefully()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            // Simuler une opération qui prend du temps mais se termine
            var delayTask = Task.Delay(100); // 100ms de délai pour simuler une opération
            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .Returns(delayTask);

            // Act - Exécuter et mesurer le temps
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            stopwatch.Stop();

            // Assert - Le service doit exécuter normalement
            _mockSettingsManager.Verify(sm => sm.SaveSettingsToPersistenceAsync(), Times.Once);
            _mockLifetimeManager.Verify(lm => lm.Shutdown(_mockServiceProvider.Object, null, null, false), Times.Once);

            // Le test ne doit pas prendre plus de quelques secondes
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 5000, "Le test ne devrait pas prendre plus de 5 secondes");

            // Vérifier que l'opération a pris au moins le temps du délai
            Assert.IsTrue(stopwatch.ElapsedMilliseconds >= 100, "Le test devrait prendre au moins 100ms");
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_WithCancellationToken_ShouldHandleCancellation()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            // Simuler une OperationCanceledException (timeout)
            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .ThrowsAsync(new OperationCanceledException("Timeout de 10 secondes dépassé"));

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert - Vérifier que le timeout est géré correctement
            _mockLoggingService.Verify(ls => ls.LogCritical(
                It.Is<string>(msg => msg == "OnExit: TIMEOUT lors de la sauvegarde des paramètres (10s)."),
                It.IsAny<Exception?>()), Times.Once);

            // L'exécution doit continuer malgré le timeout
            _mockLifetimeManager.Verify(lm => lm.Shutdown(_mockServiceProvider.Object, null, null, false), Times.Once);
            _mockLoggingService.Verify(ls => ls.ForceFlush(), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_PerformanceTest_ShouldCompleteQuickly()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .Returns(Task.CompletedTask);

            // Act - Mesurer le temps d'exécution
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            stopwatch.Stop();

            // Assert - L'exécution doit être rapide (moins de 1 seconde)
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 1000,
                $"L'exécution a pris {stopwatch.ElapsedMilliseconds}ms, ce qui est trop lent");

            // Vérifier que tous les services ont été appelés
            _mockSettingsManager.Verify(sm => sm.SaveSettingsToPersistenceAsync(), Times.Once);
            _mockLifetimeManager.Verify(lm => lm.Shutdown(_mockServiceProvider.Object, null, null, false), Times.Once);
            _mockLoggingService.Verify(ls => ls.ForceFlush(), Times.Once);
        }

        [Test]
        public async Task ExecuteExitSequenceAsync_WithEmergencyLogScenario_ShouldCreateEmergencyLog()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            // Simuler une exception critique qui déclenche la sauvegarde d'urgence
            var criticalException = new OutOfMemoryException("Mémoire insuffisante");
            _mockLifetimeManager.Setup(lm => lm.Shutdown(It.IsAny<IServiceProvider>(), null, null, false))
                .Throws(criticalException);

            // Act
            await _applicationExitService.ExecuteExitSequenceAsync(_mockServiceProvider.Object);

            // Assert - Vérifier que l'exception critique est loggée
            _mockLoggingService.Verify(ls => ls.LogCritical(
                $"OnExit: Exception non gérée lors de la fermeture: {criticalException.Message}", criticalException), Times.Once);

            // Note: La sauvegarde d'urgence dans un fichier est difficile à tester sans système de fichiers
            // mais le code est là pour les cas critiques réels
            _mockLoggingService.Verify(ls => ls.ForceFlush(), Times.Once);
        }
    }
}
