using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests d'intégration pour la méthode DeleteItemAsync avec architecture SOLID
    /// </summary>
    [TestFixture]
    public class DeleteItemAsyncIntegrationTests
    {
        private Mock<IPersistenceService> _mockPersistenceService = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IClipboardInteractionService> _mockClipboardInteractionService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;

        private DeletionValidator _deletionValidator = null!;
        private DeletionMemoryService _deletionMemoryService = null!;
        private DeletionRetryService _deletionRetryService = null!;
        private DeletionStateManager _deletionStateManager = null!;
        private DeletionNotificationService _deletionNotificationService = null!;

        private ClipboardHistoryManager _historyManager = null!;

        [SetUp]
        public void Setup()
        {
            // Mocks des services principaux
            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockClipboardInteractionService = new Mock<IClipboardInteractionService>();
            _mockLoggingService = new Mock<ILoggingService>();

            // Services de suppression réels
            _deletionValidator = new DeletionValidator(_mockLoggingService.Object);
            _deletionMemoryService = new DeletionMemoryService(_mockLoggingService.Object);
            _deletionRetryService = new DeletionRetryService(_mockPersistenceService.Object, _mockLoggingService.Object);
            _deletionStateManager = new DeletionStateManager(_mockLoggingService.Object);

            // Créer un callback mock pour les notifications
            Action mockHistoryChangedCallback = () => { /* Mock callback */ };
            _deletionNotificationService = new DeletionNotificationService(_mockLoggingService.Object, mockHistoryChangedCallback);

            // ClipboardHistoryManager avec les nouveaux services
            _historyManager = new ClipboardHistoryManager(
                _mockPersistenceService.Object,
                _mockSettingsManager.Object,
                _mockClipboardInteractionService.Object,
                _deletionValidator,
                _deletionMemoryService,
                _deletionRetryService,
                _deletionStateManager,
                _deletionNotificationService
            );
        }

        [Test]
        public async Task DeleteItemAsync_WithValidId_ShouldReturnTrue()
        {
            // Arrange
            long testId = 123;

            // Créer une liste d'éléments avec l'élément à supprimer
            var testItems = new List<ClipboardItem>
            {
                new ClipboardItem
                {
                    Id = testId,
                    TextPreview = "Test item",
                    Timestamp = DateTime.Now
                }
            };

            // Mock du chargement initial avec l'élément à supprimer
            _mockPersistenceService
                .Setup(x => x.GetAllClipboardItemsAsync())
                .ReturnsAsync(testItems);

            // Mock de la suppression en BDD réussie
            _mockPersistenceService
                .Setup(x => x.DeleteClipboardItemAsync(testId))
                .ReturnsAsync(true);

            // Attendre que l'initialisation soit terminée
            await Task.Delay(200);

            // Act
            var result = await _historyManager.DeleteItemAsync(testId);

            // Assert
            Assert.That(result, Is.True);

            // Vérifier que la suppression en BDD a été appelée
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(testId), Times.Once);
        }

        [Test]
        public async Task DeleteItemAsync_WithInvalidId_ShouldReturnFalse()
        {
            // Arrange
            long invalidId = -1;

            // Mock du chargement initial pour éviter les appels à la BDD
            _mockPersistenceService
                .Setup(x => x.GetAllClipboardItemsAsync())
                .ReturnsAsync(new List<ClipboardItem>());

            // Act
            var result = await _historyManager.DeleteItemAsync(invalidId);

            // Assert
            Assert.That(result, Is.False);

            // Vérifier que la suppression en BDD n'a pas été appelée
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(It.IsAny<long>()), Times.Never);
        }

        [Test]
        public async Task DeleteItemAsync_WithDatabaseFailure_ShouldStillReturnTrueIfRemovedFromMemory()
        {
            // Arrange
            long testId = 456;

            // Créer une liste d'éléments avec l'élément à supprimer
            var testItems = new List<ClipboardItem>
            {
                new ClipboardItem
                {
                    Id = testId,
                    TextPreview = "Test item for DB failure",
                    Timestamp = DateTime.Now
                }
            };

            // Mock du chargement initial avec l'élément à supprimer
            _mockPersistenceService
                .Setup(x => x.GetAllClipboardItemsAsync())
                .ReturnsAsync(testItems);

            // Mock de la suppression en BDD échouée
            _mockPersistenceService
                .Setup(x => x.DeleteClipboardItemAsync(testId))
                .ReturnsAsync(false);

            // Attendre que l'initialisation soit terminée
            await Task.Delay(200);

            // Act
            var result = await _historyManager.DeleteItemAsync(testId);

            // Assert
            Assert.That(result, Is.True); // Succès car retiré de la mémoire

            // Vérifier que la suppression en BDD a été tentée
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(testId), Times.AtLeastOnce);
        }
    }
}
