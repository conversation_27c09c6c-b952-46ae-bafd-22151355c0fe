using System;
using System.Reactive.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.UI.Models
{
    /// <summary>
    /// Modèle encapsulant les paramètres globaux de l'application.
    /// Cette classe sert de pont entre les services de paramètres et l'interface utilisateur.
    /// </summary>
    public class AppSettings : ObservableObject, IDisposable
    {
        private readonly ISettingsManager _settingsManager;
        private readonly IVisibilityStateManager _visibilityStateManager;
        private readonly ILoggingService _loggingService;
        
        private bool _hideItemTitle;
        
        /// <summary>
        /// Indique si les titres des éléments doivent être masqués dans l'interface.
        /// </summary>
        public bool HideItemTitle
        {
            get => _hideItemTitle;
            set
            {
                if (SetProperty(ref _hideItemTitle, value))
                {
                    _loggingService.LogInfo($"[APP_SETTINGS] HideItemTitle changé à {value} - Début synchronisation");
                    _visibilityStateManager.UpdateTitleVisibilityFromSettings(value);
                    _loggingService.LogInfo($"[APP_SETTINGS] HideItemTitle synchronisation terminée");
                }
            }
        }
        
        private bool _hideTimestamp;
        
        /// <summary>
        /// Indique si les horodatages doivent être masqués dans l'interface.
        /// </summary>
        public bool HideTimestamp
        {
            get => _hideTimestamp;
            set
            {
                if (SetProperty(ref _hideTimestamp, value))
                {
                    _loggingService.LogInfo($"AppSettings: HideTimestamp changé à {value}");
                    _settingsManager.HideTimestamp = value;
                }
            }
        }
        
        /// <summary>
        /// Initialise une nouvelle instance de la classe AppSettings.
        /// </summary>
        /// <param name="settingsManager">Service de gestion des paramètres.</param>
        /// <param name="visibilityStateManager">Gestionnaire d'état de visibilité SOLID.</param>
        /// <param name="loggingService">Service de journalisation.</param>
        public AppSettings(
            ISettingsManager settingsManager,
            IVisibilityStateManager visibilityStateManager,
            ILoggingService loggingService)
        {
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
            _visibilityStateManager = visibilityStateManager ?? throw new ArgumentNullException(nameof(visibilityStateManager));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            
            // Initialiser les propriétés avec les valeurs actuelles des paramètres
            _hideItemTitle = _settingsManager.HideItemTitle;
            _hideTimestamp = _settingsManager.HideTimestamp;
            
            _loggingService.LogInfo($"AppSettings: Initialisé avec HideItemTitle={_hideItemTitle}, HideTimestamp={_hideTimestamp}");

            // ARCHITECTURE SOLID : Écouter les événements du gestionnaire de visibilité
            _visibilityStateManager.VisibilityChanged += OnVisibilityStateChanged;

            // S'abonner aux changements des paramètres
            _settingsManager.SettingChanged += OnSettingChanged;
        }

        /// <summary>
        /// Gestionnaire pour les changements d'état de visibilité
        /// ARCHITECTURE SOLID : Synchronisation avec VisibilityStateManager
        /// </summary>
        private void OnVisibilityStateChanged(object? sender, VisibilityChangedEventArgs e)
        {
            _loggingService.LogInfo($"[APP_SETTINGS] OnVisibilityStateChanged - Type: {e.Type}, IsVisible: {e.IsVisible}");

            switch (e.Type)
            {
                case VisibilityType.Title:
                    bool hideTitle = !e.IsVisible;
                    if (_hideItemTitle != hideTitle)
                    {
                        _loggingService.LogInfo($"[APP_SETTINGS] Synchronisation HideItemTitle - Ancien: {_hideItemTitle}, Nouveau: {hideTitle}");
                        _hideItemTitle = hideTitle;
                        OnPropertyChanged(nameof(HideItemTitle));
                        _loggingService.LogInfo($"[APP_SETTINGS] HideItemTitle synchronisé et PropertyChanged déclenché");
                    }
                    else
                    {
                        _loggingService.LogInfo($"[APP_SETTINGS] HideItemTitle inchangé - Aucune synchronisation nécessaire");
                    }
                    break;

                case VisibilityType.Timestamp:
                    bool hideTimestamp = !e.IsVisible;
                    if (_hideTimestamp != hideTimestamp)
                    {
                        _loggingService.LogInfo($"[APP_SETTINGS] Synchronisation HideTimestamp - Ancien: {_hideTimestamp}, Nouveau: {hideTimestamp}");
                        _hideTimestamp = hideTimestamp;
                        OnPropertyChanged(nameof(HideTimestamp));
                        _loggingService.LogInfo($"[APP_SETTINGS] HideTimestamp synchronisé et PropertyChanged déclenché");
                    }
                    else
                    {
                        _loggingService.LogInfo($"[APP_SETTINGS] HideTimestamp inchangé - Aucune synchronisation nécessaire");
                    }
                    break;
            }
        }

        /// <summary>
        /// Gère les changements de paramètres pour mettre à jour les propriétés correspondantes.
        /// </summary>
        /// <param name="propertyName">Nom de la propriété modifiée.</param>
        private void OnSettingChanged(string propertyName)
        {
            if (propertyName == nameof(_settingsManager.HideTimestamp))
            {
                bool newValue = _settingsManager.HideTimestamp;
                if (_hideTimestamp != newValue)
                {
                    _loggingService.LogInfo($"AppSettings: Notification externe de changement de HideTimestamp à {newValue}");
                    _hideTimestamp = newValue;
                    OnPropertyChanged(nameof(HideTimestamp));
                }
            }
            // HideItemTitle est maintenant géré via VisibilityStateManager
        }
        
        /// <summary>
        /// Libère les ressources utilisées par l'instance.
        /// </summary>
        public void Dispose()
        {
            _visibilityStateManager.VisibilityChanged -= OnVisibilityStateChanged;
            _settingsManager.SettingChanged -= OnSettingChanged;
        }
    }
} 