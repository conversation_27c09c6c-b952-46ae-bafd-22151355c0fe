using System;
using System.Threading.Tasks;
using System.IO;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests
{
    [TestClass]
    public class PersistenceServiceTests
    {
        private PersistenceService _persistenceService;

        [TestInitialize]
        public void Initialize()
        {
            _persistenceService = new PersistenceService();
        }

        [TestMethod]
        public async Task InitializeAsync_ShouldCreateDatabase()
        {
            // Act
            await _persistenceService.InitializeAsync();

            // Assert - Le test passe si aucune exception n'est levée
            Assert.IsTrue(true, "L'initialisation de la base de données a réussi");
        }

        [TestMethod]
        public async Task InsertAndRetrieveClipboardItem_ShouldWork()
        {
            // Arrange
            await _persistenceService.InitializeAsync();
            var testItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test de texte",
                CustomName = "Item de test",
                RawData = System.Text.Encoding.UTF8.GetBytes("Contenu de test")
            };

            // Act
            var id = await _persistenceService.InsertClipboardItemAsync(testItem);
            var items = await _persistenceService.GetAllClipboardItemsAsync();

            // Assert
            Assert.IsTrue(id > 0, "L'ID de l'élément inséré devrait être positif");
            Assert.IsTrue(items.Count > 0, "La liste des éléments ne devrait pas être vide");
            
            var retrievedItem = items.Find(i => i.Id == id);
            Assert.IsNotNull(retrievedItem, "L'élément inséré devrait être récupérable");
            Assert.AreEqual("Test de texte", retrievedItem.TextPreview);
        }
    }
} 