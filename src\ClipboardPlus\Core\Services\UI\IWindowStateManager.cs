using System.Windows;

namespace ClipboardPlus.Core.Services.UI
{
    /// <summary>
    /// Service pour la gestion de l'état des fenêtres.
    /// Responsabilité unique : Gérer l'état, la visibilité et l'activation des fenêtres.
    /// </summary>
    public interface IWindowStateManager
    {
        /// <summary>
        /// Tente d'activer une fenêtre existante du type spécifié.
        /// </summary>
        /// <typeparam name="T">Type de fenêtre à rechercher</typeparam>
        /// <returns>True si une fenêtre a été trouvée et activée, false sinon</returns>
        bool TryActivateExistingWindow<T>() where T : Window;

        /// <summary>
        /// Recherche une fenêtre du type spécifié.
        /// </summary>
        /// <typeparam name="T">Type de fenêtre à rechercher</typeparam>
        /// <returns>La fenêtre trouvée ou null si aucune n'existe</returns>
        T? FindWindow<T>() where T : Window;

        /// <summary>
        /// S'assure qu'une fenêtre est visible (pas minimisée) et l'active.
        /// </summary>
        /// <param name="window">Fenêtre à rendre visible</param>
        void EnsureWindowVisible(Window window);

        /// <summary>
        /// Recherche une fenêtre par son type exact.
        /// </summary>
        /// <param name="windowType">Type exact de la fenêtre</param>
        /// <returns>La fenêtre trouvée ou null si aucune n'existe</returns>
        Window? FindWindowByType(System.Type windowType);
    }
}
