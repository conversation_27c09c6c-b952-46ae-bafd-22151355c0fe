using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.Diagnostics
{
    /// <summary>
    /// Interface pour la collecte de données de diagnostic
    /// Respecte le Single Responsibility Principle (SRP)
    /// </summary>
    public interface IDiagnosticDataCollector
    {
        /// <summary>
        /// Collecte les données de diagnostic d'un élément du presse-papiers
        /// </summary>
        /// <param name="item">L'élément à analyser (peut être null)</param>
        /// <returns>Données de diagnostic de l'élément</returns>
        DiagnosticData CollectItemData(ClipboardItem? item);

        /// <summary>
        /// Collecte les données de diagnostic du ViewModel
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <returns>Données de diagnostic du ViewModel</returns>
        DiagnosticData CollectViewModelData(ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Collecte les données de diagnostic du système
        /// </summary>
        /// <returns>Données de diagnostic du système</returns>
        DiagnosticData CollectSystemData();
    }

    /// <summary>
    /// DTO pour les données de diagnostic collectées
    /// </summary>
    public record DiagnosticData(
        Dictionary<string, object> Properties,
        List<string> Warnings,
        List<string> Errors,
        DateTime CollectedAt
    );
}
