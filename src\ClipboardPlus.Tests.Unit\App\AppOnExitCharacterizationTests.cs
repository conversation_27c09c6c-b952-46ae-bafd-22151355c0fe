using System;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.App
{
    /// <summary>
    /// Tests de caractérisation pour la méthode OnExit de App.
    /// Ces tests documentent et protègent le comportement actuel avant refactorisation.
    /// </summary>
    [TestFixture]
    public class AppOnExitCharacterizationTests
    {
        private Mock<IServiceProvider> _mockServiceProvider = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IApplicationLifetimeManager> _mockLifetimeManager = null!;
        private OnExitLogicTester _onExitTester = null!;

        [SetUp]
        public void SetUp()
        {
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockLifetimeManager = new Mock<IApplicationLifetimeManager>();

            _onExitTester = new OnExitLogicTester();
            _onExitTester.SetServices(_mockServiceProvider.Object);
        }

        private static ExitEventArgs CreateExitEventArgs(int exitCode = 0)
        {
            // ExitEventArgs n'a pas de constructeur public, utilisons la réflexion
            var constructor = typeof(ExitEventArgs).GetConstructor(
                BindingFlags.NonPublic | BindingFlags.Instance,
                null,
                new[] { typeof(int) },
                null);

            if (constructor != null)
            {
                return (ExitEventArgs)constructor.Invoke(new object[] { exitCode });
            }

            // Fallback : utiliser Activator.CreateInstance
            return (ExitEventArgs)Activator.CreateInstance(typeof(ExitEventArgs),
                BindingFlags.NonPublic | BindingFlags.Instance,
                null,
                new object[] { exitCode },
                null)!;
        }

        [Test]
        public void OnExit_WithNullServices_ShouldNotThrow()
        {
            // Arrange
            _onExitTester.SetServices(null);
            var exitEventArgs = CreateExitEventArgs();

            // Act & Assert
            Assert.DoesNotThrow(() => _onExitTester.ExecuteOnExitLogic(exitEventArgs));
        }

        [Test]
        public void OnExit_WithNullLoggingService_ShouldStillExecuteLogic()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns((ILoggingService?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert
            _mockSettingsManager.Verify(sm => sm.SaveSettingsToPersistenceAsync(), Times.Once);
            _mockLifetimeManager.Verify(lm => lm.Shutdown(
                _mockServiceProvider.Object, null, null, false), Times.Once);
        }

        [Test]
        public void OnExit_WithNullSettingsManager_ShouldSkipSettingsSave()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns((ISettingsManager?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Début de la fermeture de l'application"), Times.Once);
            _mockLifetimeManager.Verify(lm => lm.Shutdown(
                _mockServiceProvider.Object, null, null, false), Times.Once);
        }

        [Test]
        public void OnExit_WithNullLifetimeManager_ShouldSkipShutdown()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns((IApplicationLifetimeManager?)null);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert
            _mockSettingsManager.Verify(sm => sm.SaveSettingsToPersistenceAsync(), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Début de la fermeture de l'application"), Times.Once);
        }

        [Test]
        public void OnExit_WithValidSettingsManager_ShouldLogAllWindowParameters()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            _mockSettingsManager.Setup(sm => sm.SettingsWindowWidth).Returns(800.0);
            _mockSettingsManager.Setup(sm => sm.SettingsWindowHeight).Returns(600.0);
            _mockSettingsManager.Setup(sm => sm.SettingsWindowTop).Returns(100.0);
            _mockSettingsManager.Setup(sm => sm.SettingsWindowLeft).Returns(200.0);
            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .Returns(Task.CompletedTask);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] Sauvegarde finale des paramètres..."), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] -> Width: 800"), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] -> Height: 600"), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] -> Top: 100"), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] -> Left: 200"), Times.Once);
        }

        [Test]
        public void OnExit_WithSettingsSaveException_ShouldLogCriticalWithSpecificMessage()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            var testException = new InvalidOperationException("Test settings save error");
            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .ThrowsAsync(testException);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogCritical(
                "OnExit: EXCEPTION CRITIQUE lors de la sauvegarde des paramètres.", testException), Times.Once);
            // Vérifier que l'exécution continue malgré l'exception
            _mockLifetimeManager.Verify(lm => lm.Shutdown(
                _mockServiceProvider.Object, null, null, false), Times.Once);
        }

        [Test]
        public void OnExit_WithLifetimeManagerException_ShouldLogCriticalInGlobalCatch()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            var testException = new InvalidOperationException("Test lifetime manager error");
            _mockLifetimeManager.Setup(lm => lm.Shutdown(It.IsAny<IServiceProvider>(), null, null, false))
                .Throws(testException);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogCritical(
                $"OnExit: Exception non gérée lors de la fermeture: {testException.Message}", testException), Times.Once);
        }

        [Test]
        public void OnExit_ShouldCallShutdownWithExactParameters_Services_Null_Null_False()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert
            _mockLifetimeManager.Verify(lm => lm.Shutdown(
                _mockServiceProvider.Object, null, null, false), Times.Once);
        }

        [Test]
        public void OnExit_ShouldAlwaysCallBaseOnExitWithEventArgs()
        {
            // Arrange
            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert
            Assert.IsTrue(_onExitTester.BaseOnExitCalled);
            Assert.AreSame(exitEventArgs, _onExitTester.BaseOnExitEventArgs);
        }

        [Test]
        public void OnExit_ShouldAlwaysCallEnvironmentExitWithZero()
        {
            // Arrange
            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert
            Assert.IsTrue(_onExitTester.EnvironmentExitCalled);
            Assert.AreEqual(0, _onExitTester.EnvironmentExitCode);
        }

        [Test]
        public void OnExit_ShouldAlwaysForceFlushInFinally()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            // Simuler une exception pour vérifier que ForceFlush est appelé dans finally
            var testException = new InvalidOperationException("Test exception");
            _mockLifetimeManager.Setup(lm => lm.Shutdown(It.IsAny<IServiceProvider>(), null, null, false))
                .Throws(testException);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert
            _mockLoggingService.Verify(ls => ls.ForceFlush(), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Fermeture de l'application terminée"), Times.Once);
        }

        [Test]
        public void OnExit_WithValidServices_ShouldExecuteCompleteSequence()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsManager)))
                .Returns(_mockSettingsManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .Returns(Task.CompletedTask);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitLogic(exitEventArgs);

            // Assert - Vérifier la séquence complète
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Début de la fermeture de l'application"), Times.Once);
            _mockSettingsManager.Verify(sm => sm.SaveSettingsToPersistenceAsync(), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Sauvegarde finale des paramètres terminée avec succès."), Times.Once);
            _mockLifetimeManager.Verify(lm => lm.Shutdown(_mockServiceProvider.Object, null, null, false), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Fermeture de l'application terminée"), Times.Once);
            _mockLoggingService.Verify(ls => ls.ForceFlush(), Times.Once);
            Assert.IsTrue(_onExitTester.BaseOnExitCalled);
            Assert.IsTrue(_onExitTester.EnvironmentExitCalled);
        }
    }

    /// <summary>
    /// Classe qui extrait et teste la logique de OnExit sans hériter de Application
    /// </summary>
    public class OnExitLogicTester
    {
        public bool BaseOnExitCalled { get; private set; }
        public ExitEventArgs? BaseOnExitEventArgs { get; private set; }
        public bool EnvironmentExitCalled { get; private set; }
        public int EnvironmentExitCode { get; private set; }

        private IServiceProvider? _testServices;

        public void SetServices(IServiceProvider? services)
        {
            _testServices = services;
        }

        public void ExecuteOnExitLogic(ExitEventArgs e)
        {
            // Réinitialiser les flags
            BaseOnExitCalled = false;
            BaseOnExitEventArgs = null;
            EnvironmentExitCalled = false;
            EnvironmentExitCode = -1;

            // Exécuter la logique exacte de OnExit
            ExecuteOnExitLogicWithInterception(e);
        }

        private void ExecuteOnExitLogicWithInterception(ExitEventArgs e)
        {
            // LOGIQUE EXACTE COPIÉE DE App.OnExit (lignes 117-164)

            // Récupérer le service de journalisation
            var loggingService = _testServices?.GetService<ILoggingService>();
            loggingService?.LogInfo("OnExit: Début de la fermeture de l'application");

            try
            {
                // Sauvegarder tous les paramètres une dernière fois
                var settingsManager = _testServices?.GetService<ISettingsManager>();
                if (settingsManager != null)
                {
                    loggingService?.LogInfo("[APP_EXIT] Sauvegarde finale des paramètres...");
                    loggingService?.LogInfo($"[APP_EXIT] -> Width: {settingsManager.SettingsWindowWidth}");
                    loggingService?.LogInfo($"[APP_EXIT] -> Height: {settingsManager.SettingsWindowHeight}");
                    loggingService?.LogInfo($"[APP_EXIT] -> Top: {settingsManager.SettingsWindowTop}");
                    loggingService?.LogInfo($"[APP_EXIT] -> Left: {settingsManager.SettingsWindowLeft}");

                    try
                    {
                        // Appel synchrone d'une méthode asynchrone dans un contexte de sortie
                        settingsManager.SaveSettingsToPersistenceAsync().GetAwaiter().GetResult();
                        loggingService?.LogInfo("OnExit: Sauvegarde finale des paramètres terminée avec succès.");
                    }
                    catch (Exception ex)
                    {
                        loggingService?.LogCritical("OnExit: EXCEPTION CRITIQUE lors de la sauvegarde des paramètres.", ex);
                        // Dans un cas réel, on pourrait écrire dans un fichier de log d'urgence.
                        // System.IO.File.WriteAllText("settings_save_error.log", ex.ToString());
                    }
                }

                // Nettoyage des ressources via le service
                var lifetimeManager = _testServices?.GetService<IApplicationLifetimeManager>();
                lifetimeManager?.Shutdown(_testServices, null, null, false);
            }
            catch (Exception ex)
            {
                loggingService?.LogCritical($"OnExit: Exception non gérée lors de la fermeture: {ex.Message}", ex);
            }
            finally
            {
                loggingService?.LogInfo("OnExit: Fermeture de l'application terminée");
                loggingService?.ForceFlush(); // S'assurer que tous les logs sont écrits
            }

            // Simuler l'appel à base.OnExit(e)
            BaseOnExitCalled = true;
            BaseOnExitEventArgs = e;

            // Simuler l'appel à Environment.Exit(0)
            EnvironmentExitCalled = true;
            EnvironmentExitCode = 0;
        }
    }
}
