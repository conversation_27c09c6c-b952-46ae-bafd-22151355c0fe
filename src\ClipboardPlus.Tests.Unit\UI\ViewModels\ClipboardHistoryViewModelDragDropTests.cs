using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Tests.Unit.Helpers;
using GongSolutions.Wpf.DragDrop;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.UI.ViewModels
{
    [TestFixture]
    public class ClipboardHistoryViewModelDragDropTests
    {
        private Mock<IClipboardHistoryManager> _mockHistoryManager = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IClipboardInteractionService> _mockClipboardInteraction = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IUserNotificationService> _mockUserNotificationService = null!;
        private Mock<IUserInteractionService> _mockUserInteractionService = null!;
        private ClipboardHistoryViewModel _viewModel = null!;
        private ObservableCollection<ClipboardItem> _historyItems = null!;

        [SetUp]
        public void Setup()
        {
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockClipboardInteraction = new Mock<IClipboardInteractionService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockUserNotificationService = new Mock<IUserNotificationService>();
            _mockUserInteractionService = new Mock<IUserInteractionService>();

            // Configurer les mocks
            _historyItems = new ObservableCollection<ClipboardItem>
            {
                new ClipboardItem { Id = 1, CustomName = "Item 1", TextPreview = "Content 1", DataType = ClipboardDataType.Text },
                new ClipboardItem { Id = 2, CustomName = "Item 2", TextPreview = "Content 2", DataType = ClipboardDataType.Text },
                new ClipboardItem { Id = 3, CustomName = "Item 3", TextPreview = "Content 3", DataType = ClipboardDataType.Text }
            };

            _mockHistoryManager.Setup(m => m.PersistNewItemOrderAsync(It.IsAny<System.Collections.Generic.List<ClipboardItem>>()))
                .Returns(Task.CompletedTask);

            // Configurer le service de logging pour être injecté dans le ViewModel
            var serviceProvider = new Mock<IServiceProvider>();
            serviceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);

            // Créer le ViewModel
            _viewModel = ClipboardHistoryViewModelTestHelper.CreateViewModel();

            // Initialiser le ViewModel avec les éléments
            _viewModel.HistoryItems.Clear();
            foreach (var item in _historyItems)
            {
                _viewModel.HistoryItems.Add(item);
            }
        }

        [Test]
        public void DragOver_WithValidClipboardItem_ShouldSetDropEffects()
        {
            // Arrange
            var item = _historyItems.First();
            var mockDropInfo = new Mock<IDropInfo>();
            mockDropInfo.Setup(d => d.Data).Returns(item);

            // Act
            _viewModel.DragOver(mockDropInfo.Object);

            // Assert
            mockDropInfo.VerifySet(d => d.DropTargetAdorner = DropTargetAdorners.Insert);
            mockDropInfo.VerifySet(d => d.Effects = System.Windows.DragDropEffects.Move);
        }

        [Test]
        public void DragOver_WhenCalledMultipleTimesWithSameItem_ShouldLogOnlyOnce()
        {
            // Arrange
            var item = _historyItems.First();
            var mockDropInfo = new Mock<IDropInfo>();
            mockDropInfo.Setup(d => d.Data).Returns(item);

            // Act
            _viewModel.DragOver(mockDropInfo.Object); // Premier appel
            _viewModel.DragOver(mockDropInfo.Object); // Deuxième appel avec le même élément

            // Assert
            mockDropInfo.VerifySet(d => d.DropTargetAdorner = DropTargetAdorners.Insert, Times.Exactly(2));
            mockDropInfo.VerifySet(d => d.Effects = System.Windows.DragDropEffects.Move, Times.Exactly(2));
        }

        [Test]
        public void DragOver_WhenExceptionOccurs_ShouldLogError()
        {
            // Arrange
            var mockDropInfo = new Mock<IDropInfo>();
            mockDropInfo.Setup(d => d.Data).Throws(new InvalidOperationException("Test exception"));

            // Act & Assert
            Assert.DoesNotThrow(() => _viewModel.DragOver(mockDropInfo.Object));
        }

        [Test]
        public void Drop_WithValidClipboardItem_ShouldNotThrow()
        {
            // Arrange
            var item = _historyItems.First();
            var mockDropInfo = new Mock<IDropInfo>();
            mockDropInfo.Setup(d => d.Data).Returns(item);

            // Act & Assert
            Assert.DoesNotThrow(() => _viewModel.Drop(mockDropInfo.Object));
        }
    }
}