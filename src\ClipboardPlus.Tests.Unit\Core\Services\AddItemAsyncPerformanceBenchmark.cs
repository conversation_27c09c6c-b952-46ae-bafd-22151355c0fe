using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Interfaces;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Benchmark de performance pour comparer l'ancienne et la nouvelle implémentation d'AddItemAsync.
    /// 
    /// OBJECTIF: Mesurer l'impact de la migration vers l'architecture SOLID sur les performances.
    /// </summary>
    [TestFixture]
    public class AddItemAsyncPerformanceBenchmark
    {
        private ClipboardHistoryManager _historyManager;
        private Mock<IPersistenceService> _mockPersistenceService;
        private Mock<ISettingsManager> _mockSettingsManager;
        private Mock<IClipboardInteractionService> _mockClipboardInteractionService;

        [SetUp]
        public void SetUp()
        {
            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockClipboardInteractionService = new Mock<IClipboardInteractionService>();

            // Configuration des mocks pour des performances optimales
            var idCounter = 1L;
            _mockPersistenceService.Setup(p => p.InsertClipboardItemAsync(It.IsAny<ClipboardItem>()))
                .Returns(() => Task.FromResult(idCounter++));
            
            _mockPersistenceService.Setup(p => p.UpdateClipboardItemAsync(It.IsAny<ClipboardItem>()))
                .Returns(Task.FromResult(1));
                
            _mockPersistenceService.Setup(p => p.GetAllClipboardItemsAsync())
                .Returns(Task.FromResult(new List<ClipboardItem>()));

            _mockSettingsManager.Setup(s => s.MaxHistoryItems).Returns(1000);
            _mockSettingsManager.Setup(s => s.MaxStorableItemSizeBytes).Returns(10 * 1024 * 1024); // 10MB

            _historyManager = new ClipboardHistoryManager(
                _mockPersistenceService.Object,
                _mockSettingsManager.Object,
                _mockClipboardInteractionService.Object);
        }

        [Test]
        [Description("BENCHMARK: Performance de l'implémentation SOLID actuelle")]
        public async Task AddItemAsync_SOLID_Implementation_PerformanceBenchmark()
        {
            // Arrange
            const int iterations = 100;
            const int warmupIterations = 10;
            var executionTimes = new List<long>();

            // Warmup - Exclure du benchmark
            for (int i = 0; i < warmupIterations; i++)
            {
                var warmupItem = CreateTestItem($"Warmup {i}");
                await _historyManager.AddItemAsync(warmupItem);
            }

            // Act - Benchmark réel
            for (int i = 0; i < iterations; i++)
            {
                var item = CreateTestItem($"Benchmark Item {i}");
                
                var stopwatch = Stopwatch.StartNew();
                await _historyManager.AddItemAsync(item);
                stopwatch.Stop();
                
                executionTimes.Add(stopwatch.ElapsedTicks);
            }

            // Assert et Métriques
            var averageMs = executionTimes.Average() / TimeSpan.TicksPerMillisecond;
            var minMs = executionTimes.Min() / TimeSpan.TicksPerMillisecond;
            var maxMs = executionTimes.Max() / TimeSpan.TicksPerMillisecond;
            var stdDev = CalculateStandardDeviation(executionTimes) / TimeSpan.TicksPerMillisecond;

            TestContext.WriteLine($"=== BENCHMARK RÉSULTATS - IMPLÉMENTATION SOLID ===");
            TestContext.WriteLine($"Itérations: {iterations}");
            TestContext.WriteLine($"Temps moyen: {averageMs:F2} ms");
            TestContext.WriteLine($"Temps min: {minMs:F2} ms");
            TestContext.WriteLine($"Temps max: {maxMs:F2} ms");
            TestContext.WriteLine($"Écart-type: {stdDev:F2} ms");
            TestContext.WriteLine($"Percentile 95: {CalculatePercentile(executionTimes, 0.95) / TimeSpan.TicksPerMillisecond:F2} ms");

            // Seuils de performance acceptables
            Assert.That(averageMs, Is.LessThan(50), 
                "Le temps moyen d'exécution ne devrait pas dépasser 50ms");
            Assert.That(maxMs, Is.LessThan(200), 
                "Le temps maximum d'exécution ne devrait pas dépasser 200ms");
        }

        [Test]
        [Description("BENCHMARK: Performance avec détection de doublons")]
        public async Task AddItemAsync_WithDuplicateDetection_PerformanceBenchmark()
        {
            // Arrange
            const int iterations = 50;
            const int historySize = 100;
            var executionTimes = new List<long>();

            // Créer un historique existant pour tester la détection de doublons
            var existingItems = new List<ClipboardItem>();
            for (int i = 0; i < historySize; i++)
            {
                existingItems.Add(CreateTestItem($"Existing Item {i}"));
            }

            _mockPersistenceService.Setup(p => p.GetAllClipboardItemsAsync())
                .Returns(Task.FromResult(existingItems));

            // Act - Benchmark avec détection de doublons
            for (int i = 0; i < iterations; i++)
            {
                var item = CreateTestItem($"New Item {i}");
                
                var stopwatch = Stopwatch.StartNew();
                await _historyManager.AddItemAsync(item);
                stopwatch.Stop();
                
                executionTimes.Add(stopwatch.ElapsedTicks);
            }

            // Assert et Métriques
            var averageMs = executionTimes.Average() / TimeSpan.TicksPerMillisecond;
            var maxMs = executionTimes.Max() / TimeSpan.TicksPerMillisecond;

            TestContext.WriteLine($"=== BENCHMARK RÉSULTATS - AVEC DÉTECTION DOUBLONS ===");
            TestContext.WriteLine($"Taille historique: {historySize} éléments");
            TestContext.WriteLine($"Itérations: {iterations}");
            TestContext.WriteLine($"Temps moyen: {averageMs:F2} ms");
            TestContext.WriteLine($"Temps max: {maxMs:F2} ms");

            // Seuils de performance avec détection de doublons
            Assert.That(averageMs, Is.LessThan(100), 
                "Avec détection de doublons, le temps moyen ne devrait pas dépasser 100ms");
        }

        [Test]
        [Description("BENCHMARK: Performance avec gros volumes")]
        public async Task AddItemAsync_HighVolume_PerformanceBenchmark()
        {
            // Arrange
            const int iterations = 1000;
            var totalStopwatch = Stopwatch.StartNew();

            // Act - Test de volume
            for (int i = 0; i < iterations; i++)
            {
                var item = CreateTestItem($"Volume Item {i}");
                await _historyManager.AddItemAsync(item);
            }

            totalStopwatch.Stop();

            // Assert et Métriques
            var totalMs = totalStopwatch.ElapsedMilliseconds;
            var averagePerItemMs = (double)totalMs / iterations;
            var throughputPerSecond = iterations / (totalMs / 1000.0);

            TestContext.WriteLine($"=== BENCHMARK RÉSULTATS - GROS VOLUME ===");
            TestContext.WriteLine($"Total d'éléments: {iterations}");
            TestContext.WriteLine($"Temps total: {totalMs} ms");
            TestContext.WriteLine($"Temps moyen par élément: {averagePerItemMs:F2} ms");
            TestContext.WriteLine($"Débit: {throughputPerSecond:F0} éléments/seconde");

            // Seuils de performance pour gros volumes
            Assert.That(averagePerItemMs, Is.LessThan(10), 
                "En gros volume, le temps moyen par élément ne devrait pas dépasser 10ms");
            Assert.That(throughputPerSecond, Is.GreaterThan(100), 
                "Le débit devrait être d'au moins 100 éléments/seconde");
        }

        [Test]
        [Description("BENCHMARK: Utilisation mémoire")]
        public async Task AddItemAsync_MemoryUsage_Benchmark()
        {
            // Arrange
            const int iterations = 500;
            
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            var initialMemory = GC.GetTotalMemory(false);

            // Act
            for (int i = 0; i < iterations; i++)
            {
                var item = CreateTestItem($"Memory Test Item {i}");
                await _historyManager.AddItemAsync(item);
            }

            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            var finalMemory = GC.GetTotalMemory(false);

            // Assert et Métriques
            var memoryIncrease = finalMemory - initialMemory;
            var memoryPerItem = memoryIncrease / iterations;

            TestContext.WriteLine($"=== BENCHMARK RÉSULTATS - MÉMOIRE ===");
            TestContext.WriteLine($"Mémoire initiale: {initialMemory / 1024:N0} KB");
            TestContext.WriteLine($"Mémoire finale: {finalMemory / 1024:N0} KB");
            TestContext.WriteLine($"Augmentation: {memoryIncrease / 1024:N0} KB");
            TestContext.WriteLine($"Mémoire par élément: {memoryPerItem:N0} bytes");

            // Seuil de mémoire raisonnable
            Assert.That(memoryPerItem, Is.LessThan(10000), 
                "L'utilisation mémoire par élément ne devrait pas dépasser 10KB");
        }

        #region Helper Methods

        private ClipboardItem CreateTestItem(string content)
        {
            return new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = content,
                RawData = Encoding.UTF8.GetBytes(content),
                Timestamp = DateTime.Now
            };
        }

        private double CalculateStandardDeviation(List<long> values)
        {
            var average = values.Average();
            var sumOfSquares = values.Sum(x => Math.Pow(x - average, 2));
            return Math.Sqrt(sumOfSquares / values.Count);
        }

        private long CalculatePercentile(List<long> values, double percentile)
        {
            var sorted = values.OrderBy(x => x).ToList();
            var index = (int)Math.Ceiling(percentile * sorted.Count) - 1;
            return sorted[Math.Max(0, Math.Min(index, sorted.Count - 1))];
        }

        #endregion
    }
}
