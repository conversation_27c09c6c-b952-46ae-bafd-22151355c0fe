using System;
using System.Windows;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Service de prise de décision pour la visibilité des fenêtres.
    /// Extrait de la méthode Window_Deactivated pour améliorer la testabilité et la maintenabilité.
    /// </summary>
    public interface IWindowVisibilityDecisionService
    {
        /// <summary>
        /// Détermine si une fenêtre doit être masquée en fonction du diagnostic et de la classification.
        /// </summary>
        /// <param name="diagnostic">Résultat du diagnostic des fenêtres</param>
        /// <param name="classification">Résultat de la classification de la fenêtre active</param>
        /// <param name="targetWindow">Fenêtre cible pour la décision de masquage</param>
        /// <returns>Décision de visibilité avec détails</returns>
        WindowVisibilityDecision ShouldHideWindow(
            WindowDiagnosticResult diagnostic,
            WindowClassificationResult classification,
            Window targetWindow);

        /// <summary>
        /// Évalue la décision de visibilité avec un contexte étendu.
        /// </summary>
        /// <param name="decisionContext">Contexte complet pour la prise de décision</param>
        /// <returns>Décision de visibilité détaillée</returns>
        WindowVisibilityDecision EvaluateVisibilityDecision(WindowVisibilityDecisionContext decisionContext);

        /// <summary>
        /// Configure les règles de décision de visibilité.
        /// </summary>
        /// <param name="config">Configuration des règles</param>
        void ConfigureDecisionRules(WindowVisibilityDecisionConfig config);
    }

    /// <summary>
    /// Décision de visibilité pour une fenêtre.
    /// </summary>
    public class WindowVisibilityDecision
    {
        /// <summary>
        /// Indique si la fenêtre doit être masquée.
        /// </summary>
        public bool ShouldHide { get; set; }

        /// <summary>
        /// Raison de la décision.
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// Type de décision prise.
        /// </summary>
        public WindowVisibilityDecisionType DecisionType { get; set; }

        /// <summary>
        /// Détails supplémentaires pour le diagnostic.
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// Niveau de confiance de la décision (0.0 à 1.0).
        /// </summary>
        public double ConfidenceLevel { get; set; } = 1.0;

        /// <summary>
        /// Action recommandée à effectuer.
        /// </summary>
        public WindowVisibilityAction RecommendedAction { get; set; }

        /// <summary>
        /// Horodatage de la décision.
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Durée de l'évaluation en millisecondes.
        /// </summary>
        public double EvaluationDurationMs { get; set; }
    }

    /// <summary>
    /// Contexte pour la prise de décision de visibilité.
    /// </summary>
    public class WindowVisibilityDecisionContext
    {
        /// <summary>
        /// Résultat du diagnostic des fenêtres.
        /// </summary>
        public WindowDiagnosticResult? Diagnostic { get; set; }

        /// <summary>
        /// Résultat de la classification de la fenêtre active.
        /// </summary>
        public WindowClassificationResult? Classification { get; set; }

        /// <summary>
        /// Fenêtre cible pour la décision.
        /// </summary>
        public Window? TargetWindow { get; set; }

        /// <summary>
        /// Nom de la fenêtre cible.
        /// </summary>
        public string TargetWindowName { get; set; } = string.Empty;

        /// <summary>
        /// Indique si la fenêtre cible est actuellement active.
        /// </summary>
        public bool IsTargetWindowActive { get; set; }

        /// <summary>
        /// Configuration des règles de décision.
        /// </summary>
        public WindowVisibilityDecisionConfig? Config { get; set; }

        /// <summary>
        /// Contexte utilisateur supplémentaire.
        /// </summary>
        public string UserContext { get; set; } = string.Empty;
    }

    /// <summary>
    /// Configuration pour les règles de décision de visibilité.
    /// </summary>
    public class WindowVisibilityDecisionConfig
    {
        /// <summary>
        /// Indique si les fenêtres de l'application doivent empêcher le masquage.
        /// </summary>
        public bool ApplicationWindowsPreventsHiding { get; set; } = true;

        /// <summary>
        /// Indique si une fenêtre active empêche le masquage.
        /// </summary>
        public bool ActiveWindowPreventsHiding { get; set; } = true;

        /// <summary>
        /// Indique si le logging détaillé est activé.
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = true;

        /// <summary>
        /// Indique si les métriques de performance sont collectées.
        /// </summary>
        public bool EnablePerformanceMetrics { get; set; } = false;

        /// <summary>
        /// Niveau de confiance minimum requis pour une décision (0.0 à 1.0).
        /// </summary>
        public double MinimumConfidenceLevel { get; set; } = 0.8;
    }

    /// <summary>
    /// Types de décision de visibilité.
    /// </summary>
    public enum WindowVisibilityDecisionType
    {
        /// <summary>
        /// Masquage ignoré - fenêtre de l'application active.
        /// </summary>
        IgnoredApplicationWindow,

        /// <summary>
        /// Masquage ignoré - fenêtre cible toujours active.
        /// </summary>
        IgnoredStillActive,

        /// <summary>
        /// Masquage autorisé - fenêtre externe active.
        /// </summary>
        HideExternalWindow,

        /// <summary>
        /// Masquage autorisé - aucune fenêtre active.
        /// </summary>
        HideNoActiveWindow,

        /// <summary>
        /// Décision par défaut - pas de masquage.
        /// </summary>
        DefaultNoHide,

        /// <summary>
        /// Erreur de décision.
        /// </summary>
        DecisionError
    }

    /// <summary>
    /// Actions recommandées pour la visibilité des fenêtres.
    /// </summary>
    public enum WindowVisibilityAction
    {
        /// <summary>
        /// Aucune action requise.
        /// </summary>
        None,

        /// <summary>
        /// Masquer la fenêtre immédiatement.
        /// </summary>
        HideImmediately,

        /// <summary>
        /// Masquer la fenêtre avec délai.
        /// </summary>
        HideWithDelay,

        /// <summary>
        /// Minimiser la fenêtre.
        /// </summary>
        Minimize,

        /// <summary>
        /// Garder la fenêtre visible.
        /// </summary>
        KeepVisible,

        /// <summary>
        /// Reporter la décision.
        /// </summary>
        Defer
    }
}
