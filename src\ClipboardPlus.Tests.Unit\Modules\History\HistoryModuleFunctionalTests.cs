using NUnit.Framework;
using Moq;
using ClipboardPlus.Modules.History;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Modules.Core;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Prism.Events;
using ModuleState = ClipboardPlus.Modules.Core.ModuleState;

namespace ClipboardPlus.Tests.Unit.Modules.History
{
    /// <summary>
    /// Test fonctionnel pour HistoryModule - vérifie le comportement métier
    /// dans un scénario d'usage réel de gestion de l'historique du presse-papiers
    /// </summary>
    [TestFixture]
    public class HistoryModuleFunctionalTests
    {
        private Mock<IClipboardHistoryManager> _mockHistoryManager;
        private Mock<ILoggingService> _mockLoggingService;
        private IEventAggregator _eventAggregator;
        private HistoryModule _historyModule;
        private List<ClipboardItem> _testItems;

        [SetUp]
        public void SetUp()
        {
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockLoggingService = new Mock<ILoggingService>();
            _eventAggregator = new EventAggregator();

            _testItems = new List<ClipboardItem>
            {
                new ClipboardItem
                {
                    Id = 1,
                    TextPreview = "Test Item 1",
                    RawData = System.Text.Encoding.UTF8.GetBytes("Test Item 1"),
                    DataType = ClipboardDataType.Text,
                    Timestamp = DateTime.UtcNow.AddMinutes(-10),
                    IsPinned = false
                },
                new ClipboardItem
                {
                    Id = 2,
                    TextPreview = "Test Item 2",
                    RawData = System.Text.Encoding.UTF8.GetBytes("Test Item 2"),
                    DataType = ClipboardDataType.Text,
                    Timestamp = DateTime.UtcNow.AddMinutes(-5),
                    IsPinned = true
                }
            };

            // Configuration des mocks
            _mockHistoryManager.Setup(m => m.HistoryItems)
                .Returns(_testItems);

            _historyModule = new HistoryModule(
                _mockHistoryManager.Object,
                _mockLoggingService.Object,
                _eventAggregator
            );
        }

        [TearDown]
        public void TearDown()
        {
            _historyModule?.Dispose();
        }

        [Test]
        public async Task HistoryModule_CompleteWorkflow_ShouldManageClipboardHistoryCorrectly()
        {
            // Arrange - Scénario : Workflow complet de gestion d'historique du presse-papiers
            // Un utilisateur démarre l'application, ajoute des éléments, les gère, puis ferme l'app

            // Phase 1: Démarrage de l'application
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();

            // Vérifier que le module est correctement initialisé
            Assert.That(_historyModule.State, Is.EqualTo(ModuleState.Running));
            Assert.That(_historyModule.ModuleName, Is.EqualTo("HistoryModule"));
            Assert.That(_historyModule.TotalItemCount, Is.EqualTo(2));
            Assert.That(_historyModule.FilteredItemCount, Is.EqualTo(2));
            Assert.That(_historyModule.IsSynchronizing, Is.False);

            // Phase 2: Chargement de l'historique
            await _historyModule.LoadHistoryAsync("Application startup");

            // Vérifier que le chargement est loggé
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Loading history from context: Application startup"))), Times.Once);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("History loaded successfully"))), Times.AtLeastOnce);

            // Phase 3: Ajout d'un nouvel élément (utilisateur copie quelque chose)
            var newItem = new ClipboardItem
            {
                Id = 3,
                TextPreview = "New copied text",
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.UtcNow
            };

            _mockHistoryManager.Setup(m => m.AddItemAsync(It.IsAny<ClipboardItem>()))
                .ReturnsAsync(3L);

            await _historyModule.AddItemAsync(newItem);

            // Vérifier que l'ajout fonctionne
            _mockHistoryManager.Verify(m => m.AddItemAsync(It.Is<ClipboardItem>(
                item => item.TextPreview == "New copied text")), Times.Once);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Item added to history"))), Times.Once);

            // Phase 4: Suppression d'un élément (utilisateur supprime un élément)
            var itemToRemove = _testItems[0];
            _mockHistoryManager.Setup(m => m.DeleteItemAsync(itemToRemove.Id))
                .ReturnsAsync(true);

            await _historyModule.RemoveItemAsync(itemToRemove);

            // Vérifier que la suppression fonctionne
            _mockHistoryManager.Verify(m => m.DeleteItemAsync(1L), Times.Once);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Item removed from history"))), Times.Once);

            // Phase 5: Synchronisation forcée (utilisateur clique sur "Actualiser")
            await _historyModule.ForceSynchronizationAsync("User requested refresh");

            // Vérifier que la synchronisation fonctionne
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Force synchronization requested: User requested refresh"))), Times.Once);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Force synchronization completed"))), Times.Once);

            // Phase 6: Nettoyage de l'historique (utilisateur clique sur "Supprimer tout")
            _mockHistoryManager.Setup(m => m.ClearHistoryAsync(true))
                .Returns(Task.CompletedTask);

            await _historyModule.ClearHistoryAsync();

            // Vérifier que le nettoyage préserve les éléments épinglés
            _mockHistoryManager.Verify(m => m.ClearHistoryAsync(true), Times.Once);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("History cleared"))), Times.Once);

            // Phase 7: Arrêt de l'application
            await _historyModule.StopAsync();

            // Vérifier que l'arrêt fonctionne correctement
            Assert.That(_historyModule.State, Is.EqualTo(ModuleState.Stopped));
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Stopping HistoryModule"))), Times.Once);

            // Vérifier que toutes les opérations métier ont été correctement loggées
            _mockLoggingService.Verify(l => l.LogInfo(It.IsAny<string>()), Times.AtLeast(6));
        }
    }
}
