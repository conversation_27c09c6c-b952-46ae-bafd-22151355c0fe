using System;
using System.Threading;
using System.Windows.Interop;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Utils;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.Services
{
    public class SingleInstanceService : ISingleInstanceService
    {
        private const string APP_MUTEX_NAME = "ClipboardPlusApplicationMutex";
        private Mutex? _appMutex;
        private readonly ILoggingService _logger;
        private Action? _showWindowAction;
        private bool _disposed = false;
        private readonly object _disposeLock = new object();

        public bool IsFirstInstance { get; private set; }

        public SingleInstanceService(ILoggingService logger)
        {
            _logger = logger;
            _appMutex = new Mutex(true, APP_MUTEX_NAME, out bool ownsMutex);
            IsFirstInstance = ownsMutex;
        }

        public void NotifyExistingInstance()
        {
            if (_disposed)
            {
                _logger.LogWarning("SingleInstanceService: Tentative d'utilisation d'un service disposé.");
                return;
            }

            if (IsFirstInstance) return;

            _logger.LogInfo("SingleInstanceService: Notifying existing instance.");
            try
            {
                AppNativeMethods.PostMessage(
                    (IntPtr)AppNativeMethods.HWND_BROADCAST,
                    AppNativeMethods.WM_SHOWME,
                    IntPtr.Zero,
                    IntPtr.Zero);

                _logger.LogInfo("SingleInstanceService: Signal sent to the existing instance.");
            }
            catch (Exception ex)
            {
                _logger.LogError($"SingleInstanceService: Error while communicating with the existing instance: {ex.Message}", ex);
            }
        }

        public void RegisterWindowMessageHandler(Action? showWindowAction)
        {
            if (_disposed)
            {
                _logger.LogWarning("SingleInstanceService: Tentative d'enregistrement d'un gestionnaire sur un service disposé.");
                return;
            }

            _showWindowAction = showWindowAction;
            if (_showWindowAction != null)
            {
                ComponentDispatcher.ThreadFilterMessage += ComponentDispatcher_ThreadFilterMessage;
            }
        }

        private void ComponentDispatcher_ThreadFilterMessage(ref MSG msg, ref bool handled)
        {
            if (_disposed)
                return;

            if (msg.message == AppNativeMethods.WM_SHOWME)
            {
                _logger.LogInfo("SingleInstanceService: WM_SHOWME message received - showing history window.");

                _showWindowAction?.Invoke();

                handled = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            lock (_disposeLock)
            {
                if (_disposed)
                    return;

                if (disposing)
                {
                    // Désinscrire l'événement de message
                    if (_showWindowAction != null)
                    {
                        try
                        {
                            ComponentDispatcher.ThreadFilterMessage -= ComponentDispatcher_ThreadFilterMessage;
                            _logger.LogInfo("SingleInstanceService: Gestionnaire de messages désinscrit.");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("SingleInstanceService: Erreur lors de la désinscription du gestionnaire de messages.", ex);
                        }
                    }

                    // Libérer et disposer le mutex
                    if (IsFirstInstance && _appMutex != null)
                    {
                        try
                        {
                            // Vérifier si le mutex n'est pas déjà disposé
                            if (!_appMutex.SafeWaitHandle.IsClosed && !_appMutex.SafeWaitHandle.IsInvalid)
                            {
                                _appMutex.ReleaseMutex();
                                _logger.LogInfo("SingleInstanceService: Mutex libéré.");
                            }
                            else
                            {
                                _logger.LogInfo("SingleInstanceService: Mutex déjà fermé, libération ignorée.");
                            }
                        }
                        catch (ObjectDisposedException)
                        {
                            _logger.LogInfo("SingleInstanceService: Mutex déjà disposé, libération ignorée.");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("SingleInstanceService: Erreur lors de la libération du mutex.", ex);
                        }
                        finally
                        {
                            try
                            {
                                _appMutex.Dispose();
                                _logger.LogInfo("SingleInstanceService: Mutex disposé.");
                            }
                            catch (ObjectDisposedException)
                            {
                                _logger.LogInfo("SingleInstanceService: Mutex déjà disposé.");
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError("SingleInstanceService: Erreur lors du disposal du mutex.", ex);
                            }
                            finally
                            {
                                _appMutex = null;
                            }
                        }
                    }
                }

                _disposed = true;
            }
        }
    }
} 