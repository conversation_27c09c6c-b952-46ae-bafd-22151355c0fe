using System;
using System.Runtime.InteropServices;
using System.Threading;
using NUnit.Framework;
using ClipboardPlus.Utils;
using System.Reflection;
using Moq;
using ClipboardPlus.Core.Services;
using System.Diagnostics.CodeAnalysis;

namespace ClipboardPlus.Tests.Unit.Utils
{
    [TestFixture]
    public class KeyboardSimulatorTests
    {
        private static readonly object _lockObjectField;
        private static readonly FieldInfo _isSimulationInProgressField;
        private static readonly FieldInfo _lastSimulationTimeField;
        private static readonly FieldInfo _minimumTimeBetweenSimulationsField;
        
        // Initialisation statique pour la réflexion
        static KeyboardSimulatorTests()
        {
            var type = typeof(KeyboardSimulator);
            
            var lockObjectFieldInfo = type.GetField("_lockObject", BindingFlags.NonPublic | BindingFlags.Static);
            _lockObjectField = lockObjectFieldInfo?.GetValue(null) ?? throw new InvalidOperationException("Champ _lockObject non trouvé");
            
            _isSimulationInProgressField = type.GetField("_isSimulationInProgress", BindingFlags.NonPublic | BindingFlags.Static)
                ?? throw new InvalidOperationException("Champ _isSimulationInProgress non trouvé");
                
            _lastSimulationTimeField = type.GetField("_lastSimulationTime", BindingFlags.NonPublic | BindingFlags.Static)
                ?? throw new InvalidOperationException("Champ _lastSimulationTime non trouvé");

            _minimumTimeBetweenSimulationsField = type.GetField("_minimumTimeBetweenSimulations", BindingFlags.NonPublic | BindingFlags.Static)
                ?? throw new InvalidOperationException("Champ _minimumTimeBetweenSimulations non trouvé");
        }
        
        [SetUp]
        public void Setup()
        {
            // Réinitialiser les valeurs des champs statiques avant chaque test
            _isSimulationInProgressField.SetValue(null, false);
            _lastSimulationTimeField.SetValue(null, DateTime.MinValue);
        }
        
        [Test]
        public void SimulateCtrlV_WithSimulationInProgress_ReturnsFalse()
        {
            // Arrange - Simuler qu'une simulation est déjà en cours
            _isSimulationInProgressField.SetValue(null, true);
            
            // Act
            bool result = KeyboardSimulator.SimulateCtrlV();
            
            // Assert
            Assert.IsFalse(result, "SimulateCtrlV devrait retourner false quand une simulation est déjà en cours");
        }
        
        [Test]
        public void SimulateCtrlV_WithRecentSimulation_ReturnsFalse()
        {
            // Arrange - Simuler une simulation récente
            _lastSimulationTimeField.SetValue(null, DateTime.Now);
            
            // Act
            bool result = KeyboardSimulator.SimulateCtrlV();
            
            // Assert
            Assert.IsFalse(result, "SimulateCtrlV devrait retourner false quand une simulation a été effectuée récemment");
        }

        [Test]
        public void SimulateCtrlV_WithNegativeDelay_ShouldHandleCorrectly()
        {
            // Arrange
            int negativeDelay = -10;
            
            // Act - Utiliser un délai négatif ne devrait pas causer d'erreur
            bool result = KeyboardSimulator.SimulateCtrlV(negativeDelay);
            
            // Assert - Le résultat peut être true ou false selon l'environnement
            // mais la méthode ne devrait pas lever d'exception
            Assert.IsTrue(true, "SimulateCtrlV avec un délai négatif ne devrait pas lever d'exception");
        }

        [Test]
        public void SimulateCtrlV_WithZeroDelay_ShouldHandleCorrectly()
        {
            // Arrange
            int zeroDelay = 0;
            
            // Act
            bool result = KeyboardSimulator.SimulateCtrlV(zeroDelay);
            
            // Assert - Le résultat peut être true ou false selon l'environnement
            // mais la méthode ne devrait pas lever d'exception
            Assert.IsTrue(true, "SimulateCtrlV avec un délai de zéro ne devrait pas lever d'exception");
        }

        [Test]
        public void SimulateCtrlV_WithExcessiveDelay_ShouldLimitDelay()
        {
            // Arrange
            int excessiveDelay = 1000; // Beaucoup plus grand que la limite de 50ms
            
            // Act
            // Nous ne pouvons pas tester directement la limitation, mais vérifions que la méthode s'exécute sans erreur
            bool result = KeyboardSimulator.SimulateCtrlV(excessiveDelay);
            
            // Assert
            Assert.IsTrue(true, "SimulateCtrlV avec un délai excessif ne devrait pas lever d'exception");
        }

        [Test]
        public void SimulateCtrlV_MinimumTimeBetweenSimulations_HasCorrectValue()
        {
            // Arrange & Act
            object timeSpanValue = _minimumTimeBetweenSimulationsField.GetValue(null) ?? 
                throw new InvalidOperationException("La valeur de _minimumTimeBetweenSimulations ne peut pas être null");
            
            // Vérification du type avant le cast
            if (timeSpanValue is TimeSpan minimumTime)
            {
                // Assert
                Assert.AreEqual(500, minimumTime.TotalMilliseconds, 
                    "Le délai minimum entre les simulations devrait être de 500ms");
            }
            else
            {
                Assert.Fail($"La valeur de _minimumTimeBetweenSimulations n'est pas de type TimeSpan mais de type {timeSpanValue.GetType().Name}");
            }
        }

        [Test]
        public void SimulateCtrlV_WhenLockCannotBeAcquired_ReturnsFalse()
        {
            // Ce test ne peut pas être réalisé directement car nous ne pouvons pas acquérir le verrou
            // en dehors de la méthode sans bloquer le thread de test.
            // Une approche possible serait d'utiliser un autre thread, mais cela compliquerait le test.
            // Pour l'instant, nous vérifions simplement l'existence du code de verrouillage.
            
            // Arrange & Act - Vérifier que le verrou existe
            Assert.IsNotNull(_lockObjectField, "Le verrou _lockObject devrait exister");
            
            // Assert - Vérifier que le verrou est un objet valide
            Assert.That(_lockObjectField, Is.InstanceOf<object>(),
                "Le verrou _lockObject devrait être un objet");
        }
        
        [Test]
        public void EnsureForegroundWindowAndExecute_ExecutesAction()
        {
            // Arrange
            bool actionExecuted = false;
            Action testAction = () => { actionExecuted = true; };
            
            // Act - Utiliser IntPtr.Zero comme handle car nous ne pouvons pas obtenir de vrais handles dans un test unitaire
            try
            {
                KeyboardSimulator.EnsureForegroundWindowAndExecute(IntPtr.Zero, testAction);
            }
            catch (Exception)
            {
                // La méthode peut lever une exception dans un environnement de test, mais l'action devrait quand même être exécutée
            }
            
            // Assert
            Assert.IsTrue(actionExecuted, "L'action fournie à EnsureForegroundWindowAndExecute devrait être exécutée");
        }

        [Test]
        public void EnsureForegroundWindowAndExecute_WhenActionThrows_PropagatesException()
        {
            // Arrange
            Action throwingAction = () => { throw new Exception("Action intentionnellement levée"); };

            // Act & Assert - L'exception devrait être propagée
            Assert.Throws<Exception>(() =>
                KeyboardSimulator.EnsureForegroundWindowAndExecute(IntPtr.Zero, throwingAction));
        }

        [Test]
        public void EnsureForegroundWindowAndExecute_WithValidHandle_SetsCorrectWindow()
        {
            // Arrange
            bool actionExecuted = false;
            Action testAction = () => { actionExecuted = true; };
            IntPtr handle = new IntPtr(1); // Handle non nul mais arbitraire
            
            // Act
            try
            {
                KeyboardSimulator.EnsureForegroundWindowAndExecute(handle, testAction);
            }
            catch (Exception)
            {
                // La méthode peut lever une exception dans un environnement de test
            }
            
            // Assert
            Assert.IsTrue(actionExecuted, "L'action devrait être exécutée même avec un handle arbitraire");
        }
        
        [Test]
        public void NativeStructures_HaveCorrectSize()
        {
            // Arrange & Act
            var nativeMethodsType = typeof(KeyboardSimulator).GetNestedType("NativeMethods", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.IsNotNull(nativeMethodsType, "La classe NativeMethods n'a pas été trouvée");
            
            var inputType = nativeMethodsType?.GetNestedType("INPUT", BindingFlags.Public);
            Assert.IsNotNull(inputType, "La structure INPUT n'a pas été trouvée");
            
            var keyboardInputType = nativeMethodsType?.GetNestedType("KEYBDINPUT", BindingFlags.Public);
            Assert.IsNotNull(keyboardInputType, "La structure KEYBDINPUT n'a pas été trouvée");
            
            var mouseInputType = nativeMethodsType?.GetNestedType("MOUSEINPUT", BindingFlags.Public);
            Assert.IsNotNull(mouseInputType, "La structure MOUSEINPUT n'a pas été trouvée");
            
            var hardwareInputType = nativeMethodsType?.GetNestedType("HARDWAREINPUT", BindingFlags.Public);
            Assert.IsNotNull(hardwareInputType, "La structure HARDWAREINPUT n'a pas été trouvée");
            
            // Assert - Vérifier que les structures ont la taille attendue pour l'interop
            if (inputType != null && keyboardInputType != null && mouseInputType != null && hardwareInputType != null)
            {
                Assert.IsTrue(Marshal.SizeOf(inputType) > 0, "La structure INPUT doit avoir une taille valide");
                Assert.IsTrue(Marshal.SizeOf(keyboardInputType) > 0, "La structure KEYBDINPUT doit avoir une taille valide");
                Assert.IsTrue(Marshal.SizeOf(mouseInputType) > 0, "La structure MOUSEINPUT doit avoir une taille valide");
                Assert.IsTrue(Marshal.SizeOf(hardwareInputType) > 0, "La structure HARDWAREINPUT doit avoir une taille valide");
            }
        }

        [Test]
        public void InputStructure_HasCorrectFields()
        {
            // Arrange
            var nativeMethodsType = typeof(KeyboardSimulator).GetNestedType("NativeMethods", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.IsNotNull(nativeMethodsType, "La classe NativeMethods n'a pas été trouvée");
            
            var inputType = nativeMethodsType?.GetNestedType("INPUT", BindingFlags.Public);
            Assert.IsNotNull(inputType, "La structure INPUT n'a pas été trouvée");
            
            // Act & Assert
            if (inputType != null)
            {
                var typeField = inputType.GetField("type");
                Assert.IsNotNull(typeField, "Le champ 'type' devrait exister dans la structure INPUT");
                
                var uField = inputType.GetField("u");
                Assert.IsNotNull(uField, "Le champ 'u' devrait exister dans la structure INPUT");
            }
        }

        [Test]
        public void KeyboardInputStructure_HasCorrectFields()
        {
            // Arrange
            var nativeMethodsType = typeof(KeyboardSimulator).GetNestedType("NativeMethods", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.IsNotNull(nativeMethodsType, "La classe NativeMethods n'a pas été trouvée");
            
            var keyboardInputType = nativeMethodsType?.GetNestedType("KEYBDINPUT", BindingFlags.Public);
            Assert.IsNotNull(keyboardInputType, "La structure KEYBDINPUT n'a pas été trouvée");
            
            // Act & Assert
            if (keyboardInputType != null)
            {
                Assert.IsNotNull(keyboardInputType.GetField("wVk"), "Le champ 'wVk' devrait exister");
                Assert.IsNotNull(keyboardInputType.GetField("wScan"), "Le champ 'wScan' devrait exister");
                Assert.IsNotNull(keyboardInputType.GetField("dwFlags"), "Le champ 'dwFlags' devrait exister");
                Assert.IsNotNull(keyboardInputType.GetField("time"), "Le champ 'time' devrait exister");
                Assert.IsNotNull(keyboardInputType.GetField("dwExtraInfo"), "Le champ 'dwExtraInfo' devrait exister");
            }
        }
        
        [Test]
        public void KeyboardConstants_HaveCorrectValues()
        {
            // Arrange
            var nativeMethodsType = typeof(KeyboardSimulator).GetNestedType("NativeMethods", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.IsNotNull(nativeMethodsType, "La classe NativeMethods n'a pas été trouvée");
            
            // Act & Assert - Vérifier les constantes clés
            if (nativeMethodsType != null)
            {
                var keyUpField = nativeMethodsType.GetField("KEYEVENTF_KEYUP", BindingFlags.Public | BindingFlags.Static);
                Assert.IsNotNull(keyUpField, "La constante KEYEVENTF_KEYUP n'a pas été trouvée");
                Assert.AreEqual(0x0002, keyUpField?.GetValue(null), "KEYEVENTF_KEYUP devrait avoir la valeur 0x0002");
                
                var vkControlField = nativeMethodsType.GetField("VK_CONTROL", BindingFlags.Public | BindingFlags.Static);
                Assert.IsNotNull(vkControlField, "La constante VK_CONTROL n'a pas été trouvée");
                Assert.AreEqual(0x11, vkControlField?.GetValue(null), "VK_CONTROL devrait avoir la valeur 0x11");
                
                var vkVField = nativeMethodsType.GetField("VK_V", BindingFlags.Public | BindingFlags.Static);
                Assert.IsNotNull(vkVField, "La constante VK_V n'a pas été trouvée");
                Assert.AreEqual(0x56, vkVField?.GetValue(null), "VK_V devrait avoir la valeur 0x56");
            }
        }
        
        [Test]
        public void GetLoggingService_DoesNotThrow()
        {
            // Arrange
            var method = typeof(KeyboardSimulator).GetMethod("GetLoggingService", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.IsNotNull(method, "La méthode GetLoggingService n'a pas été trouvée");
            
            // Act & Assert - La méthode ne devrait pas lever d'exception même sans Application.Current
            try
            {
                method?.Invoke(null, null);
                Assert.IsTrue(true, "GetLoggingService ne devrait pas lever d'exception");
            }
            catch (Exception ex)
            {
                Assert.Fail($"GetLoggingService a levé une exception: {ex.GetBaseException().Message}");
            }
        }

        [Test]
        public void DllImportMethods_AreCorrectlyDefined()
        {
            // Arrange
            var nativeMethodsType = typeof(KeyboardSimulator).GetNestedType("NativeMethods", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.IsNotNull(nativeMethodsType, "La classe NativeMethods n'a pas été trouvée");
            
            // Act & Assert
            if (nativeMethodsType != null)
            {
                var sendInputMethod = nativeMethodsType.GetMethod("SendInput");
                Assert.IsNotNull(sendInputMethod, "La méthode SendInput devrait être définie");
                
                if (sendInputMethod != null)
                {
                    var dllImportAttr = sendInputMethod.GetCustomAttribute<DllImportAttribute>();
                    Assert.IsNotNull(dllImportAttr, "SendInput devrait avoir l'attribut DllImport");
                    Assert.AreEqual("user32.dll", dllImportAttr?.Value, "SendInput devrait être importée de user32.dll");
                }
            }
        }
    }
} 