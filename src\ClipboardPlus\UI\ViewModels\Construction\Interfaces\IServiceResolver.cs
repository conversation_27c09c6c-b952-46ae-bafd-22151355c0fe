using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.UI.ViewModels.Construction.Models;

namespace ClipboardPlus.UI.ViewModels.Construction.Interfaces
{
    /// <summary>
    /// Service de résolution des dépendances via ServiceProvider ou fallback.
    /// Responsabilité : Résolution et configuration de tous les services optionnels et complexes selon le principe DIP.
    /// </summary>
    public interface IServiceResolver
    {
        /// <summary>
        /// Résout les services optionnels avec fallback via ServiceProvider.
        /// Cette méthode centralise la logique de résolution des services optionnels
        /// en utilisant le ServiceProvider ou les instances fournies directement.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services pour l'injection de dépendances</param>
        /// <param name="deletionLogger">Logger pour les opérations de suppression (optionnel)</param>
        /// <param name="healthService">Service de santé des collections (optionnel)</param>
        /// <param name="visibilityManager">Gestionnaire de visibilité (optionnel)</param>
        /// <param name="newItemOrchestrator">Orchestrateur de création d'éléments (optionnel)</param>
        /// <param name="testDetector">Détecteur d'environnement de test (optionnel)</param>
        /// <returns>DTO contenant tous les services résolus</returns>
        ResolvedServices ResolveOptionalServices(
            IServiceProvider serviceProvider,
            IDeletionResultLogger? deletionLogger,
            ICollectionHealthService? healthService,
            IVisibilityStateManager? visibilityManager,
            INewItemCreationOrchestrator? newItemOrchestrator,
            ITestEnvironmentDetector? testDetector);

        /// <summary>
        /// Résout les services complexes nécessitant une résolution spéciale.
        /// Cette méthode résout les services directement via le ServiceProvider
        /// sans dépendance circulaire au ViewModel.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services pour l'injection de dépendances</param>
        /// <returns>DTO contenant les services complexes résolus</returns>
        ComplexServices ResolveComplexServices(IServiceProvider serviceProvider);
    }
}
