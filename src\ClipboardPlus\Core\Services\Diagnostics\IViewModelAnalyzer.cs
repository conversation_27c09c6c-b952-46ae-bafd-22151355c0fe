using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.Diagnostics
{
    /// <summary>
    /// Interface pour l'analyse de l'état du ViewModel sans utiliser la réflexion
    /// Respecte le Single Responsibility Principle (SRP)
    /// </summary>
    public interface IViewModelAnalyzer
    {
        /// <summary>
        /// Analyse l'état général du ViewModel
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <returns>Résultat de l'analyse</returns>
        ViewModelAnalysisResult AnalyzeState(ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Analyse la santé des collections du ViewModel
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <returns>Informations sur la santé des collections</returns>
        CollectionHealthInfo AnalyzeCollectionHealth(ClipboardHistoryViewModel viewModel);
    }

    /// <summary>
    /// Résultat de l'analyse d'un ViewModel
    /// </summary>
    public record ViewModelAnalysisResult(
        int ItemCount,
        bool IsLoading,
        bool HasSelectedItem,
        string? SelectedItemPreview,
        List<string> Issues
    );

    /// <summary>
    /// Informations sur la santé des collections
    /// </summary>
    public record CollectionHealthInfo(
        int TotalItems,
        int PinnedItems,
        int VisibleItems,
        bool HasDuplicates,
        List<string> HealthWarnings
    );
}
