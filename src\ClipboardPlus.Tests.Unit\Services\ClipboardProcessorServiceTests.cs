using NUnit.Framework;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using System.Windows.Media.Imaging;
using System.Threading;
using Moq;
using ClipboardPlus.Core.DataModels;
using System.Windows.Media;
using System.Collections.Specialized;
using System.Text;
using System;

namespace ClipboardPlus.Tests.Unit.Services
{
    [TestFixture]
    public class ClipboardProcessorServiceTests
    {
        private IClipboardProcessorService _clipboardProcessorService = null!;
        private Mock<ILoggingService> _mockLogger = null!;
        private Mock<IClipboardHistoryManager> _mockHistoryManager = null!;
        private Mock<IClipboardInteractionService> _mockClipboardInteraction = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockClipboardInteraction = new Mock<IClipboardInteractionService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockSettingsManager.Setup(m => m.ThumbnailSize).Returns(50);

            _clipboardProcessorService = new ClipboardProcessorService(
                _mockLogger.Object,
                _mockHistoryManager.Object,
                _mockClipboardInteraction.Object,
                _mockSettingsManager.Object);
        }

        // SUPPRIMÉ: Test CreateItemFromImageAsync_WithValidImage_CreatesThumbnail
        // Ce test nécessite STA pour RenderTargetBitmap et BitmapSource
        // La logique de création d'images sera testée via des tests d'intégration de plus haut niveau

        [Test]
        public async Task CreateItemFromTextAsync_WithValidText_ReturnsTextItem()
        {
            // Arrange
            string validText = "This is a valid text item.";

            // Act
            var clipboardItem = await _clipboardProcessorService.CreateItemFromTextAsync(validText);

            // Assert
            Assert.That(clipboardItem, Is.Not.Null);
            Assert.That(clipboardItem!.DataType, Is.EqualTo(ClipboardDataType.Text));
            Assert.That(clipboardItem.TextPreview, Is.EqualTo(validText));
        }

        [Test]
        public async Task CreateItemFromImageAsync_WithNullImage_ReturnsNull()
        {
            // Arrange
            BitmapSource? nullBitmap = null;

            // Act
#pragma warning disable CS8604 // Test qui passe volontairement une valeur nulle.
            var clipboardItem = await _clipboardProcessorService.CreateItemFromImageAsync(nullBitmap, 50);
#pragma warning restore CS8604

            // Assert
            Assert.That(clipboardItem, Is.Null);
        }

        #region CreateItemFromFileDropListAsync Tests

        [Test]
        [Description("Vérifie que CreateItemFromFileDropListAsync retourne null avec une collection null")]
        public async Task CreateItemFromFileDropListAsync_WithNullCollection_ReturnsNull()
        {
            // Arrange
            StringCollection? nullCollection = null;

            // Act
            var result = await _clipboardProcessorService.CreateItemFromFileDropListAsync(nullCollection!);

            // Assert
            Assert.That(result, Is.Null, "Devrait retourner null avec une collection null");
        }

        [Test]
        [Description("Vérifie que CreateItemFromFileDropListAsync retourne null avec une collection vide")]
        public async Task CreateItemFromFileDropListAsync_WithEmptyCollection_ReturnsNull()
        {
            // Arrange
            var emptyCollection = new StringCollection();

            // Act
            var result = await _clipboardProcessorService.CreateItemFromFileDropListAsync(emptyCollection);

            // Assert
            Assert.That(result, Is.Null, "Devrait retourner null avec une collection vide");
        }

        [Test]
        [Description("Vérifie que CreateItemFromFileDropListAsync crée un ClipboardItem correct avec un seul fichier")]
        public async Task CreateItemFromFileDropListAsync_WithSingleFile_CreatesCorrectItem()
        {
            // Arrange
            var singleFileCollection = new StringCollection();
            const string testFilePath = @"C:\Test\Document.txt";
            singleFileCollection.Add(testFilePath);

            // Act
            var result = await _clipboardProcessorService.CreateItemFromFileDropListAsync(singleFileCollection);

            // Assert
            Assert.That(result, Is.Not.Null, "Devrait créer un ClipboardItem");
            Assert.That(result!.DataType, Is.EqualTo(ClipboardDataType.FilePath), "Le type de données devrait être FilePath");
            Assert.That(result.TextPreview, Is.EqualTo(testFilePath), "Le TextPreview devrait contenir le chemin du fichier");
            Assert.That(result.CustomName, Is.EqualTo("Fichier: Document.txt"), "Le CustomName devrait contenir le nom du fichier");
            Assert.That(result.IsPinned, Is.False, "IsPinned devrait être false par défaut");
            Assert.That(result.OrderIndex, Is.EqualTo(0), "OrderIndex devrait être 0 par défaut");

            // Vérifier RawData
            var expectedRawData = Encoding.UTF8.GetBytes(testFilePath);
            Assert.That(result.RawData, Is.EqualTo(expectedRawData), "RawData devrait contenir les bytes du chemin du fichier");

            // Vérifier Timestamp (doit être récent)
            var timeDifference = DateTime.Now - result.Timestamp;
            Assert.That(timeDifference.TotalSeconds, Is.LessThan(5), "Timestamp devrait être récent");
        }

        [Test]
        [Description("Vérifie que CreateItemFromFileDropListAsync crée un ClipboardItem correct avec plusieurs fichiers")]
        public async Task CreateItemFromFileDropListAsync_WithMultipleFiles_CreatesCorrectItem()
        {
            // Arrange
            var multipleFilesCollection = new StringCollection();
            const string file1 = @"C:\Test\Document1.txt";
            const string file2 = @"C:\Test\Document2.pdf";
            const string file3 = @"C:\Test\Image.jpg";
            multipleFilesCollection.Add(file1);
            multipleFilesCollection.Add(file2);
            multipleFilesCollection.Add(file3);

            var expectedTextPreview = string.Join("\n", new[] { file1, file2, file3 });

            // Act
            var result = await _clipboardProcessorService.CreateItemFromFileDropListAsync(multipleFilesCollection);

            // Assert
            Assert.That(result, Is.Not.Null, "Devrait créer un ClipboardItem");
            Assert.That(result!.DataType, Is.EqualTo(ClipboardDataType.FilePath), "Le type de données devrait être FilePath");
            Assert.That(result.TextPreview, Is.EqualTo(expectedTextPreview), "Le TextPreview devrait contenir tous les chemins séparés par des retours à la ligne");
            Assert.That(result.CustomName, Is.EqualTo("3 fichiers"), "Le CustomName devrait indiquer le nombre de fichiers");
            Assert.That(result.IsPinned, Is.False, "IsPinned devrait être false par défaut");
            Assert.That(result.OrderIndex, Is.EqualTo(0), "OrderIndex devrait être 0 par défaut");

            // Vérifier RawData
            var expectedRawData = Encoding.UTF8.GetBytes(expectedTextPreview);
            Assert.That(result.RawData, Is.EqualTo(expectedRawData), "RawData devrait contenir les bytes de tous les chemins");

            // Vérifier Timestamp (doit être récent)
            var timeDifference = DateTime.Now - result.Timestamp;
            Assert.That(timeDifference.TotalSeconds, Is.LessThan(5), "Timestamp devrait être récent");
        }

        [Test]
        [Description("Vérifie que CreateItemFromFileDropListAsync gère correctement les chemins avec des caractères spéciaux")]
        public async Task CreateItemFromFileDropListAsync_WithSpecialCharacters_HandlesCorrectly()
        {
            // Arrange
            var specialFilesCollection = new StringCollection();
            const string fileWithSpaces = @"C:\Test Folder\My Document.txt";
            const string fileWithAccents = @"C:\Téléchargements\Fichier été.pdf";
            const string fileWithSymbols = @"C:\Test\File@#$%^&()_+.txt";
            specialFilesCollection.Add(fileWithSpaces);
            specialFilesCollection.Add(fileWithAccents);
            specialFilesCollection.Add(fileWithSymbols);

            var expectedTextPreview = string.Join("\n", new[] { fileWithSpaces, fileWithAccents, fileWithSymbols });

            // Act
            var result = await _clipboardProcessorService.CreateItemFromFileDropListAsync(specialFilesCollection);

            // Assert
            Assert.That(result, Is.Not.Null, "Devrait créer un ClipboardItem même avec des caractères spéciaux");
            Assert.That(result!.TextPreview, Is.EqualTo(expectedTextPreview), "Devrait préserver les caractères spéciaux dans TextPreview");
            Assert.That(result.CustomName, Is.EqualTo("3 fichiers"), "Le CustomName devrait indiquer le nombre de fichiers");

            // Vérifier que l'encodage UTF-8 préserve les caractères spéciaux
            var decodedRawData = Encoding.UTF8.GetString(result.RawData!);
            Assert.That(decodedRawData, Is.EqualTo(expectedTextPreview), "RawData décodé devrait préserver les caractères spéciaux");
        }

        [Test]
        [Description("Vérifie que CreateItemFromFileDropListAsync gère correctement un fichier sans extension")]
        public async Task CreateItemFromFileDropListAsync_WithFileWithoutExtension_HandlesCorrectly()
        {
            // Arrange
            var fileCollection = new StringCollection();
            const string fileWithoutExtension = @"C:\Test\README";
            fileCollection.Add(fileWithoutExtension);

            // Act
            var result = await _clipboardProcessorService.CreateItemFromFileDropListAsync(fileCollection);

            // Assert
            Assert.That(result, Is.Not.Null, "Devrait créer un ClipboardItem même sans extension");
            Assert.That(result!.CustomName, Is.EqualTo("Fichier: README"), "Le CustomName devrait utiliser le nom du fichier sans extension");
        }

        [Test]
        [Description("Vérifie que CreateItemFromFileDropListAsync gère correctement les chemins très longs")]
        public async Task CreateItemFromFileDropListAsync_WithLongPaths_HandlesCorrectly()
        {
            // Arrange
            var longPathCollection = new StringCollection();
            var longPath = @"C:\Very\Long\Path\With\Many\Subdirectories\And\Even\More\Subdirectories\To\Test\Long\Path\Handling\VeryLongFileName.txt";
            longPathCollection.Add(longPath);

            // Act
            var result = await _clipboardProcessorService.CreateItemFromFileDropListAsync(longPathCollection);

            // Assert
            Assert.That(result, Is.Not.Null, "Devrait créer un ClipboardItem même avec des chemins longs");
            Assert.That(result!.TextPreview, Is.EqualTo(longPath), "Devrait préserver le chemin complet dans TextPreview");
            Assert.That(result.CustomName, Is.EqualTo("Fichier: VeryLongFileName.txt"), "Le CustomName devrait extraire correctement le nom du fichier");
        }

        #endregion
    }
}