using System;
using System.Windows.Forms;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Interface pour le nettoyage des instances existantes de NotifyIcon.
    /// Responsabilité unique : gérer le nettoyage sécurisé des instances NotifyIcon existantes.
    /// </summary>
    public interface INotifyIconCleanupService
    {
        /// <summary>
        /// Nettoie une instance existante de NotifyIcon si elle existe.
        /// </summary>
        /// <param name="existingIcon">L'instance existante à nettoyer, peut être null.</param>
        void CleanupExistingIcon(NotifyIcon? existingIcon);

        /// <summary>
        /// Vérifie si une instance de NotifyIcon nécessite un nettoyage.
        /// </summary>
        /// <param name="icon">L'instance à vérifier.</param>
        /// <returns>True si un nettoyage est nécessaire, false sinon.</returns>
        bool RequiresCleanup(NotifyIcon? icon);
    }
}
