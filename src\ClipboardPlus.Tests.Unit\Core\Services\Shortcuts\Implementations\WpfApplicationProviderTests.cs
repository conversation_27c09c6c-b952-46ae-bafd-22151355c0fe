using System.Windows;
using ClipboardPlus.Core.Services.Shortcuts.Implementations;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services.Shortcuts.Implementations
{
    [TestFixture]
    public class WpfApplicationProviderTests
    {
        private WpfApplicationProvider _wpfProvider = null!;

        [SetUp]
        public void SetUp()
        {
            _wpfProvider = new WpfApplicationProvider();
        }

        [Test]
        public void Constructor_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new WpfApplicationProvider());
        }

        [Test]
        public void GetMainWindow_InTestEnvironment_ReturnsNull()
        {
            // Act
            var result = _wpfProvider.GetMainWindow();

            // Assert
            Assert.That(result, Is.Null, "Dans l'environnement de test, Application.Current est généralement null");
        }

        [Test]
        public void IsMainWindowLoaded_InTestEnvironment_ReturnsFalse()
        {
            // Act
            var result = _wpfProvider.IsMainWindowLoaded();

            // Assert
            Assert.That(result, Is.False, "Dans l'environnement de test, la fenêtre principale n'est pas chargée");
        }

        [Test]
        public void IsWpfApplicationAvailable_InTestEnvironment_ReturnsFalse()
        {
            // Act
            var result = _wpfProvider.IsWpfApplicationAvailable();

            // Assert
            Assert.That(result, Is.False, "Dans l'environnement de test, Application.Current n'est généralement pas disponible");
        }

        [Test]
        public void GetMainWindow_HandlesExceptions_ReturnsNull()
        {
            // Act & Assert
            // Même si une exception est levée, la méthode devrait retourner null
            Assert.DoesNotThrow(() => _wpfProvider.GetMainWindow());
        }

        [Test]
        public void IsMainWindowLoaded_HandlesExceptions_ReturnsFalse()
        {
            // Act & Assert
            // Même si une exception est levée, la méthode devrait retourner false
            Assert.DoesNotThrow(() => _wpfProvider.IsMainWindowLoaded());
        }

        [Test]
        public void IsWpfApplicationAvailable_HandlesExceptions_ReturnsFalse()
        {
            // Act & Assert
            // Même si une exception est levée, la méthode devrait retourner false
            Assert.DoesNotThrow(() => _wpfProvider.IsWpfApplicationAvailable());
        }

        [Test]
        public void AllMethods_AreConsistent()
        {
            // Act
            var isAppAvailable = _wpfProvider.IsWpfApplicationAvailable();
            var mainWindow = _wpfProvider.GetMainWindow();
            var isMainWindowLoaded = _wpfProvider.IsMainWindowLoaded();

            // Assert
            // Dans l'environnement de test, tous devraient être cohérents
            if (!isAppAvailable)
            {
                Assert.That(mainWindow, Is.Null, "Si l'application n'est pas disponible, MainWindow devrait être null");
                Assert.That(isMainWindowLoaded, Is.False, "Si l'application n'est pas disponible, MainWindow ne peut pas être chargée");
            }
        }

        [Test]
        public void MultipleCallsToSameMethod_ReturnConsistentResults()
        {
            // Act
            var result1 = _wpfProvider.IsWpfApplicationAvailable();
            var result2 = _wpfProvider.IsWpfApplicationAvailable();
            var result3 = _wpfProvider.IsWpfApplicationAvailable();

            // Assert
            Assert.That(result2, Is.EqualTo(result1), "Les appels multiples devraient retourner le même résultat");
            Assert.That(result3, Is.EqualTo(result1), "Les appels multiples devraient retourner le même résultat");
        }

        [Test]
        public void GetMainWindow_MultipleCallsReturnConsistentResults()
        {
            // Act
            var window1 = _wpfProvider.GetMainWindow();
            var window2 = _wpfProvider.GetMainWindow();

            // Assert
            Assert.That(window2, Is.EqualTo(window1), "Les appels multiples à GetMainWindow devraient retourner le même résultat");
        }

        [Test]
        public void IsMainWindowLoaded_MultipleCallsReturnConsistentResults()
        {
            // Act
            var loaded1 = _wpfProvider.IsMainWindowLoaded();
            var loaded2 = _wpfProvider.IsMainWindowLoaded();

            // Assert
            Assert.That(loaded2, Is.EqualTo(loaded1), "Les appels multiples à IsMainWindowLoaded devraient retourner le même résultat");
        }
    }
}
