using System;
using System.Collections.Generic;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour le service de chargement de prévisualisation de contenu.
    /// Orchestrateur principal qui remplace la logique LoadItemContent() du ContentPreviewViewModel.
    /// Respecte le principe de responsabilité unique (SRP) en se concentrant uniquement sur le chargement.
    /// </summary>
    public interface IContentPreviewLoader
    {
        /// <summary>
        /// Charge le contenu de prévisualisation pour un élément du presse-papiers.
        /// </summary>
        /// <param name="item">L'élément du presse-papiers à traiter</param>
        /// <returns>Le contenu formaté pour l'affichage, ou null si l'item est null</returns>
        /// <exception cref="ArgumentNullException">Levée si item est null</exception>
        object? LoadPreviewContent(ClipboardItem item);

        /// <summary>
        /// Vérifie si un type de données est supporté par le loader.
        /// </summary>
        /// <param name="dataType">Le type de données à vérifier</param>
        /// <returns>True si le type est supporté, false sinon</returns>
        bool IsDataTypeSupported(ClipboardDataType dataType);

        /// <summary>
        /// Obtient la liste des types de données supportés.
        /// </summary>
        /// <returns>Énumération des types supportés</returns>
        IEnumerable<ClipboardDataType> GetSupportedDataTypes();
    }
}
