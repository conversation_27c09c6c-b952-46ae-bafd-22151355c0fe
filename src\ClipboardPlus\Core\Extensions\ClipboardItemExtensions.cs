using ClipboardPlus.Core.DataModels;
using System.IO;
using System.Windows;
using System.Windows.Media.Imaging;

namespace ClipboardPlus.Core.Extensions
{
    public static class ClipboardItemExtensions
    {
        public static System.Windows.DataObject ToDataObject(this ClipboardItem item)
        {
            var dataObject = new System.Windows.DataObject();

            if (item.RawData == null)
            {
                return dataObject;
            }

            if (item.DataType == ClipboardDataType.Text)
            {
                // Pour le texte, RawData est un tableau de bytes encodé en UTF-8
                dataObject.SetText(System.Text.Encoding.UTF8.GetString(item.RawData));
            }
            else if (item.DataType == ClipboardDataType.Image)
            {
                using (var stream = new MemoryStream(item.RawData))
                {
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.StreamSource = stream;
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    dataObject.SetImage(bitmap);
                }
            }
            // Ajoutez ici d'autres types de données si nécessaire (fichiers, etc.)

            return dataObject;
        }
    }
} 