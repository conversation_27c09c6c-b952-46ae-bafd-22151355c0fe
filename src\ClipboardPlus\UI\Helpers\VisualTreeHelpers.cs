using System.Windows;
using System.Windows.Media;

namespace ClipboardPlus.UI.Helpers
{
    /// <summary>
    /// Fournit des méthodes utilitaires pour naviguer dans l'arbre visuel WPF.
    /// </summary>
    public static class VisualTreeHelpers
    {
        /// <summary>
        /// Trouve le premier ancêtre du type spécifié.
        /// </summary>
        /// <typeparam name="T">Type de l'ancêtre à trouver.</typeparam>
        /// <param name="dependencyObject">L'objet à partir duquel chercher.</param>
        /// <returns>L'ancêtre trouvé ou null si aucun n'est trouvé.</returns>
        public static T? FindAncestor<T>(DependencyObject dependencyObject) where T : DependencyObject
        {
            if (dependencyObject == null)
                return null;

            DependencyObject parent = VisualTreeHelper.GetParent(dependencyObject);
            
            if (parent == null)
                return null;

            if (parent is T ancestorObject)
                return ancestorObject;

            return FindAncestor<T>(parent);
        }

        /// <summary>
        /// Trouve le premier descendant du type spécifié.
        /// </summary>
        /// <typeparam name="T">Type du descendant à trouver.</typeparam>
        /// <param name="dependencyObject">L'objet à partir duquel chercher.</param>
        /// <returns>Le descendant trouvé ou null si aucun n'est trouvé.</returns>
        public static T? FindDescendant<T>(DependencyObject dependencyObject) where T : DependencyObject
        {
            if (dependencyObject == null)
                return null;

            // Parcourir les enfants
            int childCount = VisualTreeHelper.GetChildrenCount(dependencyObject);
            for (int i = 0; i < childCount; i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(dependencyObject, i);
                
                if (child is T descendantObject)
                    return descendantObject;

                T? childResult = FindDescendant<T>(child);
                if (childResult != null)
                    return childResult;
            }

            return null;
        }

        /// <summary>
        /// Trouve tous les descendants du type spécifié.
        /// </summary>
        /// <typeparam name="T">Type des descendants à trouver.</typeparam>
        /// <param name="dependencyObject">L'objet à partir duquel chercher.</param>
        /// <param name="results">Liste de résultats à remplir.</param>
        public static void FindAllDescendants<T>(DependencyObject dependencyObject, System.Collections.Generic.List<T> results) where T : DependencyObject
        {
            if (dependencyObject == null || results == null)
                return;

            // Parcourir les enfants
            int childCount = VisualTreeHelper.GetChildrenCount(dependencyObject);
            for (int i = 0; i < childCount; i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(dependencyObject, i);
                
                if (child is T descendantObject)
                    results.Add(descendantObject);

                FindAllDescendants<T>(child, results);
            }
        }

        /// <summary>
        /// Trouve tous les descendants du type spécifié et renvoie une liste.
        /// </summary>
        /// <typeparam name="T">Type des descendants à trouver.</typeparam>
        /// <param name="dependencyObject">L'objet à partir duquel chercher.</param>
        /// <returns>Une liste des descendants trouvés.</returns>
        public static System.Collections.Generic.List<T> FindDescendants<T>(DependencyObject dependencyObject) where T : DependencyObject
        {
            var results = new System.Collections.Generic.List<T>();
            FindAllDescendants<T>(dependencyObject, results);
            return results;
        }
    }
} 