using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Implémentation de la gestion de l'état global d'une opération de suppression
    /// Responsabilité : Déterminer le résultat final basé sur les résultats mémoire et BDD
    /// </summary>
    public class DeletionStateManager : IDeletionStateManager
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Constructeur avec injection de dépendances
        /// </summary>
        /// <param name="loggingService">Service de logging</param>
        public DeletionStateManager(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new System.ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Détermine le résultat global d'une opération de suppression
        /// </summary>
        /// <param name="memoryResult">Résultat de la suppression en mémoire</param>
        /// <param name="databaseResult">Résultat de la suppression en base de données</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Résultat global de l'opération</returns>
        public Task<GlobalDeletionResult> DetermineGlobalResultAsync(
            MemoryDeletionResult memoryResult, 
            DatabaseDeletionResult databaseResult, 
            string operationId)
        {
            _loggingService.LogDebug($"[{operationId}] Analyse du résultat global de suppression");

            // Analyser la cohérence
            var consistency = AnalyzeConsistency(memoryResult, databaseResult);

            // Déterminer le succès global et la notification
            bool globalSuccess = DetermineGlobalSuccess(memoryResult, databaseResult, consistency);
            bool shouldNotify = DetermineShouldNotify(memoryResult, databaseResult, globalSuccess);
            string message = GenerateGlobalMessage(memoryResult, databaseResult, consistency, globalSuccess);

            if (globalSuccess)
            {
                _loggingService.LogInfo($"[{operationId}] Résultat global: SUCCÈS - {message}");
            }
            else
            {
                _loggingService.LogWarning($"[{operationId}] Résultat global: ÉCHEC - {message}");
            }

            if (globalSuccess)
            {
                return Task.FromResult(GlobalDeletionResult.CreateSuccess(memoryResult, databaseResult, consistency, message, shouldNotify));
            }
            else
            {
                return Task.FromResult(GlobalDeletionResult.CreateFailure(memoryResult, databaseResult, consistency, message, shouldNotify));
            }
        }

        /// <summary>
        /// Analyse la cohérence entre les résultats mémoire et BDD
        /// </summary>
        /// <param name="memoryResult">Résultat de la suppression en mémoire</param>
        /// <param name="databaseResult">Résultat de la suppression en base de données</param>
        /// <returns>Analyse de cohérence</returns>
        public ConsistencyAnalysis AnalyzeConsistency(MemoryDeletionResult memoryResult, DatabaseDeletionResult databaseResult)
        {
            // Cas cohérents
            if (memoryResult.Success && databaseResult.Success)
            {
                return new ConsistencyAnalysis
                {
                    IsConsistent = true,
                    InconsistencyType = InconsistencyType.None,
                    Message = "Suppression réussie en mémoire et en BDD",
                    Severity = SeverityLevel.None
                };
            }

            if (!memoryResult.Success && !databaseResult.Success)
            {
                return new ConsistencyAnalysis
                {
                    IsConsistent = true,
                    InconsistencyType = InconsistencyType.BothFailed,
                    Message = "Échec cohérent en mémoire et en BDD",
                    Severity = SeverityLevel.Info
                };
            }

            // Cas incohérents
            if (memoryResult.Success && !databaseResult.Success)
            {
                return new ConsistencyAnalysis
                {
                    IsConsistent = false,
                    InconsistencyType = InconsistencyType.MemorySuccessDatabaseFailure,
                    Message = "Incohérence: suppression mémoire réussie mais BDD échouée",
                    Severity = SeverityLevel.Warning
                };
            }

            if (!memoryResult.Success && databaseResult.Success)
            {
                return new ConsistencyAnalysis
                {
                    IsConsistent = false,
                    InconsistencyType = InconsistencyType.MemoryFailureDatabaseSuccess,
                    Message = "Incohérence: suppression BDD réussie mais mémoire échouée",
                    Severity = SeverityLevel.Error
                };
            }

            return new ConsistencyAnalysis
            {
                IsConsistent = false,
                InconsistencyType = InconsistencyType.UnexpectedState,
                Message = "État inattendu dans l'analyse de cohérence",
                Severity = SeverityLevel.Critical
            };
        }

        /// <summary>
        /// Détermine si l'opération globale est un succès
        /// </summary>
        private bool DetermineGlobalSuccess(MemoryDeletionResult memoryResult, DatabaseDeletionResult databaseResult, ConsistencyAnalysis consistency)
        {
            // Si l'élément existait en mémoire et a été supprimé, c'est un succès même si la BDD échoue
            if (memoryResult.ExistedInMemory && memoryResult.Success)
            {
                return true;
            }

            // Si l'élément n'existait pas en mémoire mais la suppression BDD a réussi
            if (!memoryResult.ExistedInMemory && databaseResult.Success)
            {
                return true;
            }

            // Si l'élément n'existait ni en mémoire ni en BDD, c'est considéré comme un succès
            if (!memoryResult.ExistedInMemory && !databaseResult.Success)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Détermine si une notification doit être envoyée
        /// </summary>
        private bool DetermineShouldNotify(MemoryDeletionResult memoryResult, DatabaseDeletionResult databaseResult, bool globalSuccess)
        {
            // Notifier si l'élément existait en mémoire et a été supprimé avec succès
            return globalSuccess && memoryResult.ExistedInMemory && memoryResult.Success;
        }

        /// <summary>
        /// Génère un message explicatif du résultat global
        /// </summary>
        private string GenerateGlobalMessage(MemoryDeletionResult memoryResult, DatabaseDeletionResult databaseResult, 
            ConsistencyAnalysis consistency, bool globalSuccess)
        {
            if (globalSuccess)
            {
                if (memoryResult.ExistedInMemory && memoryResult.Success)
                {
                    return databaseResult.Success 
                        ? "Suppression complète réussie (mémoire + BDD)"
                        : "Suppression mémoire réussie (BDD échouée mais acceptable)";
                }
                else
                {
                    return "Élément non trouvé (considéré comme supprimé)";
                }
            }
            else
            {
                return consistency.Message ?? "Échec de l'opération de suppression";
            }
        }
    }
}
