using System;
using System.Windows;
using System.Windows.Controls;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// SOLUTION NATIVE FINALE : Utilise un vrai ContextMenu WPF qui se ferme automatiquement
    /// </summary>
    public class NativeContextMenuService
    {
        private readonly ILoggingService _loggingService;
        private ContextMenu? _contextMenu;
        private Window? _dummyWindow;

        public NativeContextMenuService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Affiche un menu contextuel natif à la position spécifiée
        /// </summary>
        public void ShowContextMenu(System.Windows.Point position, Action onHistoryClick, Action onSettingsClick, Action onExitClick)
        {
            try
            {
                _loggingService.LogInfo("🎯 [MENU_NATIVE] DÉBUT ShowContextMenu NATIF");
                _loggingService.LogInfo($"🎯 [MENU_NATIVE] Position: X={position.X}, Y={position.Y}");

                // Fermer le menu précédent s'il existe
                if (_contextMenu != null)
                {
                    _contextMenu.IsOpen = false;
                    _contextMenu = null;
                }

                // Créer une fenêtre invisible comme PlacementTarget
                if (_dummyWindow == null)
                {
                    _dummyWindow = new Window
                    {
                        WindowStyle = WindowStyle.None,
                        AllowsTransparency = true,
                        Background = System.Windows.Media.Brushes.Transparent,
                        ShowInTaskbar = false,
                        Width = 1,
                        Height = 1,
                        Left = position.X,
                        Top = position.Y,
                        Topmost = true
                    };
                }
                else
                {
                    _dummyWindow.Left = position.X;
                    _dummyWindow.Top = position.Y;
                }

                // Créer le menu contextuel NATIF
                _contextMenu = new ContextMenu();
                
                // CONFIGURATION NATIVE pour fermeture automatique
                _contextMenu.StaysOpen = false; // Se ferme automatiquement !
                _contextMenu.Focusable = true;
                _contextMenu.PlacementTarget = _dummyWindow;
                _contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.Bottom;

                // Ajouter les éléments du menu
                var historyItem = new MenuItem
                {
                    Header = "Afficher l'historique",
                    Icon = "📋"
                };
                historyItem.Click += (s, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_NATIVE] Clic sur 'Afficher l'historique'");
                    onHistoryClick();
                };

                var settingsItem = new MenuItem
                {
                    Header = "Paramètres",
                    Icon = "⚙️"
                };
                settingsItem.Click += (s, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_NATIVE] Clic sur 'Paramètres'");
                    onSettingsClick();
                };

                var separator = new Separator();

                var exitItem = new MenuItem
                {
                    Header = "Quitter",
                    Icon = "❌"
                };
                exitItem.Click += (s, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_NATIVE] Clic sur 'Quitter'");
                    onExitClick();
                };

                _contextMenu.Items.Add(historyItem);
                _contextMenu.Items.Add(settingsItem);
                _contextMenu.Items.Add(separator);
                _contextMenu.Items.Add(exitItem);

                // Événements pour monitoring
                _contextMenu.Opened += (sender, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_NATIVE] ✅ Menu contextuel NATIF ouvert");
                };

                _contextMenu.Closed += (sender, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_NATIVE] ❌ Menu contextuel NATIF fermé automatiquement");
                    
                    // Nettoyer la fenêtre dummy
                    if (_dummyWindow != null)
                    {
                        _dummyWindow.Hide();
                    }
                };

                // Afficher la fenêtre dummy et le menu
                _dummyWindow.Show();
                _contextMenu.IsOpen = true;

                _loggingService.LogInfo("🎯 [MENU_NATIVE] ✅ Menu contextuel NATIF affiché");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"🎯 [MENU_NATIVE] ❌ ERREUR: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Nettoie les ressources
        /// </summary>
        public void Dispose()
        {
            if (_contextMenu != null)
            {
                _contextMenu.IsOpen = false;
                _contextMenu = null;
            }

            if (_dummyWindow != null)
            {
                _dummyWindow.Close();
                _dummyWindow = null;
            }
        }
    }
}
