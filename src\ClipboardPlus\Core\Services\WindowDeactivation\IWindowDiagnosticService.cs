using System;
using System.Collections.Generic;
using System.Windows;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Service de diagnostic des fenêtres système.
    /// Extrait de la méthode Window_Deactivated pour améliorer la testabilité et la maintenabilité.
    /// </summary>
    public interface IWindowDiagnosticService
    {
        /// <summary>
        /// Analyse l'état actuel de toutes les fenêtres du système.
        /// </summary>
        /// <returns>Résultat du diagnostic avec toutes les informations collectées</returns>
        WindowDiagnosticResult AnalyzeCurrentWindowState();

        /// <summary>
        /// Analyse l'état des fenêtres de manière asynchrone.
        /// </summary>
        /// <returns>Résultat du diagnostic</returns>
        Task<WindowDiagnosticResult> AnalyzeCurrentWindowStateAsync();

        /// <summary>
        /// Obtient des informations détaillées sur une fenêtre spécifique.
        /// </summary>
        /// <param name="window">Fenêtre à analyser</param>
        /// <returns>Informations détaillées sur la fenêtre</returns>
        WindowInfo GetWindowInfo(Window window);
    }

    /// <summary>
    /// Résultat du diagnostic des fenêtres système.
    /// </summary>
    public class WindowDiagnosticResult
    {
        /// <summary>
        /// Liste de toutes les fenêtres détectées.
        /// </summary>
        public IReadOnlyList<WindowInfo> AllWindows { get; set; } = new List<WindowInfo>();

        /// <summary>
        /// Fenêtre actuellement active (peut être null).
        /// </summary>
        public Window? ActiveWindow { get; set; }

        /// <summary>
        /// Informations sur la fenêtre active (peut être null).
        /// </summary>
        public WindowInfo? ActiveWindowInfo { get; set; }

        /// <summary>
        /// Nombre total de fenêtres détectées.
        /// </summary>
        public int TotalWindowCount => AllWindows.Count;

        /// <summary>
        /// Horodatage du diagnostic.
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Durée du diagnostic en millisecondes.
        /// </summary>
        public double DiagnosticDurationMs { get; set; }

        /// <summary>
        /// Indique si le diagnostic s'est déroulé sans erreur.
        /// </summary>
        public bool IsSuccessful { get; set; } = true;

        /// <summary>
        /// Message d'erreur si le diagnostic a échoué.
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Informations détaillées sur une fenêtre.
    /// </summary>
    public class WindowInfo
    {
        /// <summary>
        /// Référence à la fenêtre (peut être null si la fenêtre a été fermée).
        /// </summary>
        public Window? Window { get; set; }

        /// <summary>
        /// Type de la fenêtre.
        /// </summary>
        public string WindowTypeName { get; set; } = string.Empty;

        /// <summary>
        /// Nom complet du type de la fenêtre.
        /// </summary>
        public string WindowTypeFullName { get; set; } = string.Empty;

        /// <summary>
        /// Titre de la fenêtre.
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Indique si la fenêtre est actuellement active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Indique si la fenêtre est visible.
        /// </summary>
        public bool IsVisible { get; set; }

        /// <summary>
        /// Indique si la fenêtre est chargée.
        /// </summary>
        public bool IsLoaded { get; set; }

        /// <summary>
        /// Type du propriétaire de la fenêtre (peut être null).
        /// </summary>
        public string? OwnerTypeName { get; set; }

        /// <summary>
        /// Nom complet du type du propriétaire (peut être null).
        /// </summary>
        public string? OwnerTypeFullName { get; set; }

        /// <summary>
        /// Indique si la fenêtre a un propriétaire.
        /// </summary>
        public bool HasOwner => !string.IsNullOrEmpty(OwnerTypeName);

        /// <summary>
        /// État de la fenêtre (Normal, Minimized, Maximized).
        /// </summary>
        public WindowState WindowState { get; set; }

        /// <summary>
        /// Horodatage de la collecte des informations.
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}
