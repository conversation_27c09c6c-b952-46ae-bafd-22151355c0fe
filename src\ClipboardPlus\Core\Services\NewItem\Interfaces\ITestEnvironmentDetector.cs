namespace ClipboardPlus.Core.Services.NewItem.Interfaces
{
    /// <summary>
    /// Interface pour détecter si l'application s'exécute dans un environnement de test.
    /// Cette interface permet d'abstraire la logique de détection du mode test,
    /// facilitant les tests unitaires et respectant le principe d'inversion de dépendance.
    /// </summary>
    public interface ITestEnvironmentDetector
    {
        /// <summary>
        /// Détermine si l'application s'exécute actuellement dans un environnement de test.
        /// </summary>
        /// <returns>
        /// true si l'application s'exécute dans un contexte de test (tests unitaires, tests d'intégration, etc.),
        /// false si l'application s'exécute en mode normal.
        /// </returns>
        bool IsInTestEnvironment();
    }
}
