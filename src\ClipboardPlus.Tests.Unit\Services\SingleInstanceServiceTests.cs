using System;
using System.Threading;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services
{
    [TestFixture]
    public class SingleInstanceServiceTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private SingleInstanceService _service = null!;

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Mock<ILoggingService>();
        }

        [Test]
        public void Constructor_ShouldInitializeProperties()
        {
            // Act
            _service = new SingleInstanceService(_mockLoggingService.Object);
            
            // Assert
            Assert.That(_service.IsFirstInstance, Is.Not.Null);
            // Vérifier qu'aucune erreur n'a été journalisée
            int errorLogCount = 0;
            foreach (var invocation in _mockLoggingService.Invocations)
            {
                if (invocation.Method.Name == "LogError")
                {
                    errorLogCount++;
                }
            }
            Assert.That(errorLogCount, Is.EqualTo(0), "Aucune erreur ne devrait être journalisée pendant la construction");
        }

        [Test]
        public void NotifyExistingInstance_WhenFirstInstance_ShouldNotSendMessage()
        {
            // Arrange
            _service = new SingleInstanceService(_mockLoggingService.Object);
            
            // Simuler que c'est la première instance
            var isFirstInstanceField = typeof(SingleInstanceService).GetProperty("IsFirstInstance", 
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            var isFirstInstanceBackingField = typeof(SingleInstanceService).GetField("<IsFirstInstance>k__BackingField", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (isFirstInstanceBackingField != null)
            {
                isFirstInstanceBackingField.SetValue(_service, true);
            }
            else
            {
                // Si le backing field n'est pas trouvé, utiliser une réflexion plus générique
                var field = typeof(SingleInstanceService).GetField("IsFirstInstance", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                field?.SetValue(_service, true);
            }
            
            // Capturer les invocations avant l'appel
            int initialLogCount = _mockLoggingService.Invocations.Count;
            
            // Act
            _service.NotifyExistingInstance();
            
            // Assert
            // Vérifier qu'aucun message de notification n'a été journalisé
            bool notificationMessageFound = false;
            foreach (var invocation in _mockLoggingService.Invocations.Skip(initialLogCount))
            {
                if (invocation.Method.Name == "LogInfo" && 
                    invocation.Arguments.Count > 0 && 
                    invocation.Arguments[0] is string message && 
                    message.Contains("Notifying existing instance"))
                {
                    notificationMessageFound = true;
                    break;
                }
            }
            Assert.That(notificationMessageFound, Is.False, "Aucun message de notification ne devrait être journalisé");
        }

        [Test]
        public void NotifyExistingInstance_WhenNotFirstInstance_ShouldSendMessage()
        {
            // Arrange
            _service = new SingleInstanceService(_mockLoggingService.Object);
            
            // Simuler que ce n'est pas la première instance
            var isFirstInstanceField = typeof(SingleInstanceService).GetProperty("IsFirstInstance", 
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            var isFirstInstanceBackingField = typeof(SingleInstanceService).GetField("<IsFirstInstance>k__BackingField", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (isFirstInstanceBackingField != null)
            {
                isFirstInstanceBackingField.SetValue(_service, false);
            }
            else
            {
                // Si le backing field n'est pas trouvé, utiliser une réflexion plus générique
                var field = typeof(SingleInstanceService).GetField("IsFirstInstance", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                field?.SetValue(_service, false);
            }
            
            // Capturer les invocations avant l'appel
            int initialLogCount = _mockLoggingService.Invocations.Count;
            
            // Act
            _service.NotifyExistingInstance();
            
            // Assert
            // Vérifier qu'un message de notification a été journalisé
            bool notificationMessageFound = false;
            foreach (var invocation in _mockLoggingService.Invocations.Skip(initialLogCount))
            {
                if (invocation.Method.Name == "LogInfo" && 
                    invocation.Arguments.Count > 0 && 
                    invocation.Arguments[0] is string message && 
                    message.Contains("Notifying existing instance"))
                {
                    notificationMessageFound = true;
                    break;
                }
            }
            Assert.That(notificationMessageFound, Is.True, "Un message de notification devrait être journalisé");
        }

        [Test]
        public void NotifyExistingInstance_WhenDisposed_ShouldLogWarning()
        {
            // Arrange
            _service = new SingleInstanceService(_mockLoggingService.Object);
            
            // Disposer le service
            _service.Dispose();
            
            // Capturer les invocations avant l'appel
            int initialLogCount = _mockLoggingService.Invocations.Count;
            _mockLoggingService.Invocations.Clear();
            
            // Act
            _service.NotifyExistingInstance();
            
            // Assert
            // Vérifier qu'un avertissement a été journalisé
            bool warningFound = false;
            foreach (var invocation in _mockLoggingService.Invocations)
            {
                if (invocation.Method.Name == "LogWarning" && 
                    invocation.Arguments.Count > 0 && 
                    invocation.Arguments[0] is string message && 
                    message.Contains("service disposé"))
                {
                    warningFound = true;
                    break;
                }
            }
            Assert.That(warningFound, Is.True, "Un avertissement concernant l'utilisation d'un service disposé devrait être journalisé");
        }

        [Test]
        public void RegisterWindowMessageHandler_ShouldStoreAction()
        {
            // Arrange
            _service = new SingleInstanceService(_mockLoggingService.Object);
            var actionCalled = false;
            Action showWindowAction = () => actionCalled = true;
            
            // Act
            _service.RegisterWindowMessageHandler(showWindowAction);
            
            // Assert
            var showWindowActionField = typeof(SingleInstanceService).GetField("_showWindowAction", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var storedAction = showWindowActionField?.GetValue(_service) as Action;
            
            Assert.That(storedAction, Is.Not.Null);

            // Vérifier que l'action peut être appelée
            storedAction?.Invoke();
            Assert.That(actionCalled, Is.True);
        }

        [Test]
        public void RegisterWindowMessageHandler_WhenDisposed_ShouldLogWarning()
        {
            // Arrange
            _service = new SingleInstanceService(_mockLoggingService.Object);
            var actionCalled = false;
            Action showWindowAction = () => actionCalled = true;
            
            // Disposer le service
            _service.Dispose();
            
            // Capturer les invocations avant l'appel
            _mockLoggingService.Invocations.Clear();
            
            // Act
            _service.RegisterWindowMessageHandler(showWindowAction);
            
            // Assert
            // Vérifier qu'un avertissement a été journalisé
            bool warningFound = false;
            foreach (var invocation in _mockLoggingService.Invocations)
            {
                if (invocation.Method.Name == "LogWarning" && 
                    invocation.Arguments.Count > 0 && 
                    invocation.Arguments[0] is string message && 
                    message.Contains("service disposé"))
                {
                    warningFound = true;
                    break;
                }
            }
            Assert.That(warningFound, Is.True, "Un avertissement concernant l'enregistrement sur un service disposé devrait être journalisé");

            // Vérifier que l'action peut toujours être appelée même si le service est disposé
            showWindowAction();
            Assert.That(actionCalled, Is.True, "L'action devrait pouvoir être appelée même après disposal du service");
        }

        [Test]
        public void Dispose_ShouldCleanupResources()
        {
            // Arrange
            _service = new SingleInstanceService(_mockLoggingService.Object);
            
            // Act
            _service.Dispose();
            
            // Assert
            var disposedField = typeof(SingleInstanceService).GetField("_disposed", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var isDisposed = (bool)(disposedField?.GetValue(_service) ?? false);
            
            Assert.That(isDisposed, Is.True, "Le champ _disposed devrait être à true après l'appel à Dispose");
        }

        [Test]
        public void Dispose_WhenCalledMultipleTimes_ShouldOnlyDisposeOnce()
        {
            // Arrange
            _service = new SingleInstanceService(_mockLoggingService.Object);
            
            // Act
            _service.Dispose();
            int invocationCount = _mockLoggingService.Invocations.Count;
            _mockLoggingService.Invocations.Clear(); // Réinitialiser les appels
            _service.Dispose(); // Deuxième appel
            
            // Assert
            Assert.That(_mockLoggingService.Invocations.Count, Is.EqualTo(0), 
                "Aucun message ne devrait être journalisé lors du second appel à Dispose");
        }
    }
} 