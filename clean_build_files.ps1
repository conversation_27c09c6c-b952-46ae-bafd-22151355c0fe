# Nettoyage des dossiers bin, obj et TestResults
Write-Host "Nettoyage des dossiers de compilation et de tests..." -ForegroundColor Cyan

$pathsToDelete = @(
    "src\ClipboardPlus\bin",
    "src\ClipboardPlus\obj",
    "src\ClipboardPlus.Tests.Integration\bin",
    "src\ClipboardPlus.Tests.Integration\obj",
    "src\ClipboardPlus.Tests.STA\bin",
    "src\ClipboardPlus.Tests.STA\obj",
    "src\ClipboardPlus.Tests.Unit\bin",
    "src\ClipboardPlus.Tests.Unit\obj",
    "src\ClipboardPlus.Tests.Unit\TestResults"
)

foreach ($relativePath in $pathsToDelete) {
    $fullPath = Join-Path -Path $PSScriptRoot -ChildPath $relativePath
    if (Test-Path $fullPath) {
        try {
            Remove-Item $fullPath -Recurse -Force -ErrorAction Stop
            Write-Host "Suppression : $relativePath" -ForegroundColor Green
        }
        catch {
            Write-Warning "Impossible de supprimer : $relativePath -> $_"
        }
    }
    else {
        Write-Host "Dossier inexistant : $relativePath" -ForegroundColor Yellow
    }
}

Write-Host "Nettoyage fini." -ForegroundColor Cyan
