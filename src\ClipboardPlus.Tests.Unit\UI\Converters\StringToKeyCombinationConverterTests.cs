using System;
using System.Globalization;
using System.Windows.Input;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.Converters;

namespace ClipboardPlus.Tests.Unit.UI.Converters
{
    [TestFixture]
    public class StringToKeyCombinationConverterTests
    {
        private StringToKeyCombinationConverter? _converter;

        [SetUp]
        public void Setup()
        {
            _converter = new StringToKeyCombinationConverter();
        }

        [Test]
        public void Convert_ValidShortcut_ReturnsKeyCombination()
        {
            // Arrange
            string shortcut = "Ctrl+C";
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            // Act
            var result = _converter.Convert(shortcut, typeof(KeyCombination), parameter: new object(), culture: CultureInfo.InvariantCulture) as KeyCombination;

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être un KeyCombination");
            Assert.That(result!.ToString(), Is.EqualTo(shortcut), "La combinaison de touches devrait correspondre à la chaîne d'entrée");
        }

        [Test]
        public void Convert_EmptyString_ReturnsDefaultKeyCombination()
        {
            // Arrange & Act
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            var result = _converter.Convert(string.Empty, typeof(KeyCombination), parameter: new object(), culture: CultureInfo.InvariantCulture) as KeyCombination;

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être un KeyCombination");
            Assert.That(result!.ToString(), Is.EqualTo(new KeyCombination().ToString()), "Une chaîne vide devrait retourner une combinaison par défaut");
        }

        [Test]
        public void Convert_WithAltShiftShortcutString_ReturnsKeyCombination()
        {
            // Arrange
            string shortcut = "Alt+Shift+X";
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            // Act
            var result = _converter.Convert(shortcut, typeof(KeyCombination), parameter: new object(), culture: CultureInfo.InvariantCulture) as KeyCombination;

            // Assert
            if (result == null)
            {
                Assert.Fail("Le résultat ne devrait pas être null");
                return;
            }

            Assert.That((result.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt, Is.True,
                "Le modificateur Alt devrait être activé");
            Assert.That((result.Modifiers & ModifierKeys.Shift) == ModifierKeys.Shift, Is.True,
                "Le modificateur Shift devrait être activé");
            Assert.That((result.Modifiers & ModifierKeys.Control) == ModifierKeys.Control, Is.False,
                "Le modificateur Control ne devrait pas être activé");
            Assert.That(result.Key, Is.EqualTo(Key.X), "La touche devrait être X");
        }
        
        [Test]
        public void Convert_WithNullValue_ReturnsDefaultKeyCombination()
        {
            // Arrange
            object? value = null;
            Type targetType = typeof(KeyCombination);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            var result = _converter.Convert(value!, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.InstanceOf<KeyCombination>(), "Le résultat devrait être un KeyCombination");

            var keyCombination = (KeyCombination)result;
            // Vérifier les valeurs par défaut telles que définies dans le constructeur de KeyCombination
            Assert.That((keyCombination.Modifiers & ModifierKeys.Windows) == ModifierKeys.Windows, Is.True,
                "Le modificateur Windows devrait être activé par défaut");
            Assert.That(keyCombination.Key, Is.EqualTo(Key.V), "La touche par défaut devrait être V");
        }

        [Test]
        public void Convert_WithInvalidString_ReturnsDefaultKeyCombination()
        {
            // Arrange
            object value = "InvalidShortcutFormat";
            Type targetType = typeof(KeyCombination);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            var result = _converter.Convert(value, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.InstanceOf<KeyCombination>(), "Le résultat devrait être un KeyCombination");

            var keyCombination = (KeyCombination)result;
            // Vérifier les valeurs par défaut telles que définies dans le constructeur de KeyCombination
            Assert.That((keyCombination.Modifiers & ModifierKeys.Windows) == ModifierKeys.Windows, Is.True,
                "Le modificateur Windows devrait être activé par défaut");
            Assert.That(keyCombination.Key, Is.EqualTo(Key.V), "La touche par défaut devrait être V");
        }

        [Test]
        public void ConvertBack_ValidKeyCombination_ReturnsString()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            var keyCombination = KeyCombination.Parse("Ctrl+V");

            // Act
            var result = _converter.ConvertBack(keyCombination, typeof(string), parameter: new object(), culture: CultureInfo.InvariantCulture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être une chaîne");
            string? resultString = result as string;
            Assert.That(resultString, Is.Not.Null, "Le résultat devrait être castable en string");
            Assert.That(resultString, Is.EqualTo("Ctrl+V"), "La chaîne devrait correspondre à la combinaison de touches");
        }
        
        [Test]
        public void ConvertBack_Null_ReturnsEmptyString()
        {
            // Arrange & Act
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            var result = _converter.ConvertBack(null!, typeof(string), parameter: new object(), culture: CultureInfo.InvariantCulture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat devrait être une chaîne");
            string? resultString = result as string;
            Assert.That(resultString, Is.Not.Null, "Le résultat devrait être castable en string");
            Assert.That(resultString, Is.EqualTo(string.Empty), "Une valeur null devrait retourner une chaîne vide");
        }

        [Test]
        public void ConvertBack_WithEmptyKeyCombination_ReturnsEmptyString()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            var keyCombination = new KeyCombination(ModifierKeys.None, Key.None);

            // Act
            var result = _converter.ConvertBack(keyCombination, typeof(string), parameter: new object(), culture: CultureInfo.InvariantCulture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.InstanceOf<string>(), "Le résultat devrait être une chaîne");
            Assert.That(result.ToString(), Is.EqualTo("None"), "La chaîne devrait contenir 'None' pour une touche None");
        }

        [Test]
        public void ConvertBack_WithNonKeyCombinationValue_ReturnsEmptyString()
        {
            // Arrange
            if (_converter == null)
            {
                Assert.Fail("Le convertisseur ne devrait pas être null");
                return;
            }

            object value = "NotAKeyCombination";

            // Act
            var result = _converter.ConvertBack(value, typeof(string), parameter: new object(), culture: CultureInfo.InvariantCulture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.InstanceOf<string>(), "Le résultat devrait être une chaîne");
            Assert.That(result.ToString(), Is.EqualTo(string.Empty), "La chaîne devrait être vide pour une valeur qui n'est pas un KeyCombination");
        }
    }
} 