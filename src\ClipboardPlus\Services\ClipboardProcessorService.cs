using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace ClipboardPlus.Services
{
    public class ClipboardProcessorService : IClipboardProcessorService
    {
        private readonly ILoggingService _logger;
        private readonly IClipboardHistoryManager _clipboardHistoryManager;
        private readonly IClipboardInteractionService _clipboardInteractionService;
        private readonly ISettingsManager _settingsManager;

        public ClipboardProcessorService(
            ILoggingService logger, 
            IClipboardHistoryManager clipboardHistoryManager,
            IClipboardInteractionService clipboardInteractionService,
            ISettingsManager settingsManager)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _clipboardHistoryManager = clipboardHistoryManager ?? throw new ArgumentNullException(nameof(clipboardHistoryManager));
            _clipboardInteractionService = clipboardInteractionService ?? throw new ArgumentNullException(nameof(clipboardInteractionService));
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
        }

        /// <summary>
        /// Traite le contenu actuel du presse-papiers et l'ajoute à l'historique si nécessaire.
        /// </summary>
        public async Task ProcessCurrentClipboardContentAsync()
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            _logger.LogInfo($"[{operationId}] ProcessCurrentClipboardContentAsync: DÉBUT du traitement du contenu du presse-papiers - ThreadID: {System.Environment.CurrentManagedThreadId}");

            try
            {
                // Vérifier si le presse-papiers contient du texte
                _logger.LogInfo($"[{operationId}] Vérification du contenu texte...");
                if (_clipboardInteractionService.ContainsText())
                {
                    _logger.LogInfo($"[{operationId}] Texte détecté dans le presse-papiers");
                    var text = await _clipboardInteractionService.GetTextAsync();
                    if (!string.IsNullOrEmpty(text))
                    {
                        var preview = text.Length > 50 ? text.Substring(0, 47) + "..." : text;
                        _logger.LogInfo($"[{operationId}] Texte récupéré: '{preview}'");

                        var item = await CreateItemFromTextAsync(text);
                        if (item != null)
                        {
                            _logger.LogInfo($"[{operationId}] Élément texte créé, ajout à l'historique (architecture SOLID)...");
                            var itemId = await _clipboardHistoryManager.AddItemAsync(item);
                            _logger.LogInfo($"[{operationId}] ✅ Élément texte ajouté à l'historique avec ID: {itemId}");
                        }
                        else
                        {
                            _logger.LogWarning($"[{operationId}] ❌ Échec de création de l'élément texte");
                        }
                        return;
                    }
                    else
                    {
                        _logger.LogInfo($"[{operationId}] Texte vide ou null, ignoré");
                    }
                }
                else
                {
                    _logger.LogInfo($"[{operationId}] Aucun texte détecté dans le presse-papiers");
                }

                // Vérifier si le presse-papiers contient une image
                _logger.LogInfo($"[{operationId}] Vérification du contenu image...");
                if (_clipboardInteractionService.ContainsImage())
                {
                    _logger.LogInfo($"[{operationId}] Image détectée dans le presse-papiers");
                    var image = await _clipboardInteractionService.GetImageAsync();
                    if (image != null)
                    {
                        _logger.LogInfo($"[{operationId}] Image récupérée: {image.PixelWidth}x{image.PixelHeight}");
                        var thumbnailSize = _settingsManager.ThumbnailSize;
                        var item = await CreateItemFromImageAsync(image, thumbnailSize);
                        if (item != null)
                        {
                            _logger.LogInfo($"[{operationId}] Élément image créé, ajout à l'historique (architecture SOLID)...");
                            var itemId = await _clipboardHistoryManager.AddItemAsync(item);
                            _logger.LogInfo($"[{operationId}] ✅ Élément image ajouté à l'historique avec ID: {itemId}");
                        }
                        else
                        {
                            _logger.LogWarning($"[{operationId}] ❌ Échec de création de l'élément image");
                        }
                        return;
                    }
                    else
                    {
                        _logger.LogInfo($"[{operationId}] Image null, ignorée");
                    }
                }
                else
                {
                    _logger.LogInfo($"[{operationId}] Aucune image détectée dans le presse-papiers");
                }

                // Vérifier si le presse-papiers contient des fichiers
                _logger.LogInfo($"[{operationId}] Vérification du contenu fichiers...");
                if (_clipboardInteractionService.ContainsFileDropList())
                {
                    _logger.LogInfo($"[{operationId}] Liste de fichiers détectée dans le presse-papiers");
                    var files = await _clipboardInteractionService.GetFileDropListAsync();
                    if (files != null && files.Count > 0)
                    {
                        _logger.LogInfo($"[{operationId}] {files.Count} fichier(s) récupéré(s)");
                        var item = await CreateItemFromFileDropListAsync(files);
                        if (item != null)
                        {
                            _logger.LogInfo($"[{operationId}] Élément fichier créé, ajout à l'historique (architecture SOLID)...");
                            var itemId = await _clipboardHistoryManager.AddItemAsync(item);
                            _logger.LogInfo($"[{operationId}] ✅ Élément fichier ajouté à l'historique avec ID: {itemId}");
                        }
                        else
                        {
                            _logger.LogWarning($"[{operationId}] ❌ Échec de création de l'élément fichier");
                        }
                        return;
                    }
                    else
                    {
                        _logger.LogInfo($"[{operationId}] Liste de fichiers vide ou null");
                    }
                }
                else
                {
                    _logger.LogInfo($"[{operationId}] Aucune liste de fichiers détectée dans le presse-papiers");
                }

                _logger.LogWarning($"[{operationId}] ⚠️ Aucun contenu compatible détecté dans le presse-papiers");
            }
            catch (Exception ex)
            {
                _logger.LogError($"[{operationId}] ❌ ProcessCurrentClipboardContentAsync: Erreur lors du traitement du contenu du presse-papiers: {ex.Message}", ex);
                throw;
            }
            finally
            {
                _logger.LogInfo($"[{operationId}] ProcessCurrentClipboardContentAsync: FIN du traitement");
            }
        }

        public Task<ClipboardItem?> CreateItemFromFileDropListAsync(StringCollection files)
        {
            if (files == null || files.Count == 0)
            {
                _logger.LogInfo($"[ClipboardProcessor] Liste de fichiers vide ignorée.");
                return Task.FromResult<ClipboardItem?>(null);
            }
            
            var fileList = new List<string>(files.Cast<string>());
            var fileListString = string.Join("\n", fileList);
            
            var clipboardItem = new ClipboardItem
            {
                Timestamp = DateTime.Now,
                DataType = ClipboardDataType.FilePath,
                TextPreview = fileListString,
                RawData = Encoding.UTF8.GetBytes(fileListString),
                IsPinned = false,
                OrderIndex = 0
            };

            var fileCount = files.Count;
            clipboardItem.CustomName = fileCount == 1 
                ? "Fichier: " + Path.GetFileName(files[0]) 
                : $"{fileCount} fichiers";

            return Task.FromResult<ClipboardItem?>(clipboardItem);
        }

        public async Task<ClipboardItem?> CreateItemFromImageAsync(BitmapSource imageSource, int thumbnailMaxDimension)
        {
             if (imageSource == null)
            {
                _logger.LogInfo($"[ClipboardProcessor] ImageSource nulle ignorée.");
                return null;
            }

            // S'assurer que toutes les opérations sur les objets WPF se font sur le thread UI
            return await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                try
                {
                    var clipboardItem = new ClipboardItem
                    {
                        Timestamp = DateTime.Now,
                        DataType = ClipboardDataType.Image,
                        CustomName = null,
                        IsPinned = false,
                        OrderIndex = 0
                    };

                    // Créer une copie figée de l'image pour éviter les problèmes de thread
                    BitmapSource frozenImageSource = imageSource;
                    if (!imageSource.IsFrozen)
                    {
                        frozenImageSource = imageSource.Clone();
                        frozenImageSource.Freeze();
                    }

                    using (var memoryStream = new MemoryStream())
                    {
                        var encoder = new PngBitmapEncoder();
                        encoder.Frames.Add(BitmapFrame.Create(frozenImageSource));
                        encoder.Save(memoryStream);
                        clipboardItem.RawData = memoryStream.ToArray();
                    }

                    try
                    {
                        BitmapSource thumbnail = CreateThumbnail(frozenImageSource, thumbnailMaxDimension);

                        FormatConvertedBitmap formattedThumbnail = new FormatConvertedBitmap();
                        formattedThumbnail.BeginInit();
                        formattedThumbnail.Source = thumbnail;
                        formattedThumbnail.DestinationFormat = PixelFormats.Pbgra32;
                        formattedThumbnail.EndInit();
                        formattedThumbnail.Freeze();
                        clipboardItem.ThumbnailSource = formattedThumbnail;
                    }
                    catch (Exception thumbnailEx)
                    {
                        _logger.LogError($"[ClipboardProcessor] Erreur création miniature: {thumbnailEx.Message}", thumbnailEx);
                        clipboardItem.ThumbnailSource = null;
                    }

                    return clipboardItem;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"[ClipboardProcessor] Erreur lors de la création de l'élément image: {ex.Message}", ex);
                    return null;
                }
            });
        }

        public Task<ClipboardItem?> CreateItemFromTextAsync(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                _logger.LogInfo($"[ClipboardProcessor] Texte vide ignoré.");
                return Task.FromResult<ClipboardItem?>(null);
            }

            var clipboardItem = new ClipboardItem
            {
                Timestamp = DateTime.Now,
                TextPreview = text,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(text),
                IsPinned = false,
                OrderIndex = 0
            };
            var previewText = text.Length > 50 ? text.Substring(0, 47) + "..." : text;
            clipboardItem.CustomName = previewText.Replace("\r", " ").Replace("\n", " ").Trim();
            
            return Task.FromResult<ClipboardItem?>(clipboardItem);
        }

        private BitmapSource CreateThumbnail(BitmapSource originalSource, int maxDimension)
        {
            if (originalSource.PixelWidth == 0 || originalSource.PixelHeight == 0 || maxDimension <= 0)
            {
                return BitmapSource.Create(1, 1, 96, 96, PixelFormats.Pbgra32, null, new byte[4] { 0, 0, 0, 0 }, 4);
            }

            double originalWidth = originalSource.PixelWidth;
            double originalHeight = originalSource.PixelHeight;
            double factor;

            if (originalWidth > originalHeight)
            {
                factor = (double)maxDimension / originalWidth;
            }
            else
            {
                factor = (double)maxDimension / originalHeight;
            }

            int newWidth = (int)Math.Max(1, Math.Round(originalWidth * factor));
            int newHeight = (int)Math.Max(1, Math.Round(originalHeight * factor));

            TransformedBitmap thumbnail = new TransformedBitmap();
            thumbnail.BeginInit();
            thumbnail.Source = originalSource;
            thumbnail.Transform = new ScaleTransform((double)newWidth / originalWidth, (double)newHeight / originalHeight, 0.5, 0.5);
            thumbnail.EndInit();
            
            thumbnail.Freeze();
            return thumbnail;
        }
    }
} 