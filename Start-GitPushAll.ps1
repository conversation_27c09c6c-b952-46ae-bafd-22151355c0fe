Write-Host "-------------------------------------"
Write-Host " Automatisation Git: Add, Commit, Push"
Write-Host "-------------------------------------`n"

# Obtenir la branche actuelle
$currentBranch = git rev-parse --abbrev-ref HEAD
Write-Host "`n--- Branche actuelle : $currentBranch ---"

# Afficher l'état du repo
git status

# Vérifier s'il y a des changements
$changes = git status --porcelain

if (-not $changes) {
    Write-Host "`nAucun changement detecte. Rien a commit."
    exit
}

# Demander un message de commit
$commitMessage = Read-Host "`nEntrez votre message de commit"
Write-Host "`nMessage de commit choisi: '$commitMessage'"

# Confirmation utilisateur
$confirm = Read-Host "`nProceder avec add, commit, et push? (O/N)"
if ($confirm -ne 'O' -and $confirm -ne 'o') {
    Write-Host "Operation annulee."
    exit
}

# Ajouter tous les fichiers
Write-Host "`n--- Ajout de tous les fichiers (git add .) ---"
git add .

# Commit
Write-Host "`n--- Commit des changements ---"
git commit -m "$commitMessage"

# Push
Write-Host "`n--- Push vers le depôt distant ---"
$pushResult = git push 2>&1
if ($LASTEXITCODE -ne 0) {
    # Si le push échoue (branche sans upstream), proposer de créer la branche distante
    if ($pushResult -match "no upstream branch") {
        Write-Host "La branche '$currentBranch' n'a pas de branche distante correspondante."
        $createUpstream = Read-Host "Creer la branche distante 'origin/$currentBranch' ? (O/N)"
        if ($createUpstream -eq 'O' -or $createUpstream -eq 'o') {
            Write-Host "`n--- Creation de la branche distante ---"
            git push --set-upstream origin $currentBranch
        } else {
            Write-Host "Push annule. La branche reste locale."
        }
    } else {
        Write-Host "Erreur lors du push:"
        Write-Host $pushResult
    }
}

# Verifier s'il y a des branches distantes à fusionner
Write-Host "`n--- Verification des branches distantes ---"
git fetch --all --quiet

# Obtenir la liste des branches distantes (exclure HEAD et la branche actuelle)
$remoteBranches = git branch -r | Where-Object {
    $_ -notmatch "HEAD" -and
    $_ -notmatch "origin/$currentBranch" -and
    $_ -notmatch "origin/main"
} | ForEach-Object { $_.Trim() -replace "origin/", "" }

if ($remoteBranches.Count -gt 0) {
    Write-Host "Branches distantes detectees:"
    $remoteBranches | ForEach-Object { Write-Host "  - $_" }

    $mergeRemote = Read-Host "`nSouhaitez-vous fusionner une ou plusieurs branches distantes dans '$currentBranch' ? (O/N)"
    if ($mergeRemote -eq 'O' -or $mergeRemote -eq 'o') {
        foreach ($remoteBranch in $remoteBranches) {
            $mergeBranch = Read-Host "Fusionner 'origin/$remoteBranch' dans '$currentBranch' ? (O/N/S pour Sauter)"
            if ($mergeBranch -eq 'O' -or $mergeBranch -eq 'o') {
                Write-Host "`n--- Fusion de 'origin/$remoteBranch' ---"
                git merge "origin/$remoteBranch"
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "Fusion reussie !"
                } else {
                    Write-Host "Conflit detecte. Resolvez les conflits manuellement."
                    $continueAfterConflict = Read-Host "Continuer avec les autres branches ? (O/N)"
                    if ($continueAfterConflict -ne 'O' -and $continueAfterConflict -ne 'o') {
                        break
                    }
                }
            } elseif ($mergeBranch -eq 'S' -or $mergeBranch -eq 's') {
                Write-Host "Branche '$remoteBranch' sautee."
            }
        }

        # Proposer de pousser les changements après fusion
        $pushAfterMerge = Read-Host "`nPousser les changements après fusion ? (O/N)"
        if ($pushAfterMerge -eq 'O' -or $pushAfterMerge -eq 'o') {
            git push
        }
    }
}

# Si on n'est pas sur la branche 'main', proposer de fusionner dans main
if ($currentBranch -ne 'main') {
    $merge = Read-Host "`nSouhaitez-vous fusionner la branche '$currentBranch' dans 'main' ? (O/N)"
    if ($merge -eq 'O' -or $merge -eq 'o') {
        Write-Host "`n--- Fusion dans 'main' ---"
        git checkout main
        git pull
        git merge $currentBranch
        git push

        # Proposer suppression de la branche locale
        $deleteLocal = Read-Host "`nSouhaitez-vous supprimer la branche locale '$currentBranch' maintenant qu'elle est fusionnee ? (O/N)"
        if ($deleteLocal -eq 'O' -or $deleteLocal -eq 'o') {
            git branch -d $currentBranch

            # Proposer suppression distante
            $deleteRemote = Read-Host "Supprimer aussi la branche distante 'origin/$currentBranch' ? (O/N)"
            if ($deleteRemote -eq 'O' -or $deleteRemote -eq 'o') {
                git push origin --delete $currentBranch
            }
        } else {
            git checkout $currentBranch
        }
    }
}

Write-Host "`n-------------------------------------"
Write-Host "         Operation terminee"
Write-Host "-------------------------------------"
