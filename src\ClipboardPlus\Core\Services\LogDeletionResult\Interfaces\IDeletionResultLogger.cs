using System.Threading.Tasks;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Interfaces
{
    /// <summary>
    /// Interface principale pour le logging des résultats de suppression.
    /// Remplace la méthode monolithique LogDeletionResult par une architecture modulaire.
    /// </summary>
    public interface IDeletionResultLogger
    {
        /// <summary>
        /// Enregistre le résultat d'une suppression de manière asynchrone.
        /// </summary>
        /// <param name="context">Contexte de la suppression</param>
        /// <returns>Résultat de l'opération de logging</returns>
        Task<DeletionLoggingResult> LogDeletionResultAsync(DeletionResultContext context);

        /// <summary>
        /// Enregistre le résultat d'une suppression de manière synchrone.
        /// </summary>
        /// <param name="context">Contexte de la suppression</param>
        /// <returns>Résultat de l'opération de logging</returns>
        DeletionLoggingResult LogDeletionResult(DeletionResultContext context);

        /// <summary>
        /// Obtient les métriques de performance du logger.
        /// </summary>
        /// <returns>Métriques de performance</returns>
        DeletionLoggerMetrics GetPerformanceMetrics();

        /// <summary>
        /// Réinitialise les métriques de performance.
        /// </summary>
        void ResetMetrics();
    }
}
