using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services.ContentHandlers
{
    /// <summary>
    /// Handler pour le contenu chemin de fichier.
    /// Remplace la logique LoadFilePathContent() du ContentPreviewViewModel.
    /// </summary>
    public class FilePathContentHandler : BaseContentHandler
    {
        private readonly IFileSystemService _fileSystemService;
        private readonly IFileSizeFormatter _fileSizeFormatter;

        /// <summary>
        /// Initialise une nouvelle instance de FilePathContentHandler.
        /// </summary>
        /// <param name="fileSystemService">Service de système de fichiers</param>
        /// <param name="fileSizeFormatter">Service de formatage des tailles de fichiers</param>
        /// <param name="loggingService">Service de logging optionnel</param>
        public FilePathContentHandler(
            IFileSystemService fileSystemService, 
            IFileSizeFormatter fileSizeFormatter,
            ILoggingService? loggingService = null) 
            : base(loggingService)
        {
            _fileSystemService = fileSystemService ?? throw new ArgumentNullException(nameof(fileSystemService));
            _fileSizeFormatter = fileSizeFormatter ?? throw new ArgumentNullException(nameof(fileSizeFormatter));
        }

        /// <inheritdoc />
        public override ClipboardDataType SupportedDataType => ClipboardDataType.FilePath;

        /// <inheritdoc />
        public override string GetDefaultUnavailableMessage()
        {
            return "Chemin de fichier non disponible.";
        }

        /// <inheritdoc />
        protected override object HandleContentInternal(ClipboardItem item)
        {
            _loggingService?.LogInfo($"FilePathContentHandler.HandleContentInternal - Traitement du chemin de fichier pour l'élément ID {item.Id}");

            // Priorité 1: Utiliser RawData si disponible
            if (item.RawData != null && item.RawData.Length > 0)
            {
                _loggingService?.LogInfo($"FilePathContentHandler - Données brutes disponibles, taille: {item.RawData.Length} octets");
                
                string? decodedPath = DecodeRawDataAsText(item.RawData);
                if (!string.IsNullOrWhiteSpace(decodedPath))
                {
                    _loggingService?.LogInfo($"FilePathContentHandler - Chemin décodé: '{decodedPath}'");
                    
                    string formattedPath = FormatFilePath(decodedPath);
                    _loggingService?.LogInfo($"FilePathContentHandler - Chemin formaté: '{formattedPath}'");
                    
                    return formattedPath;
                }
                else
                {
                    _loggingService?.LogWarning("FilePathContentHandler - Échec du décodage des données brutes, utilisation du TextPreview");
                }
            }
            else
            {
                _loggingService?.LogWarning("FilePathContentHandler - Données brutes non disponibles, utilisation du TextPreview");
            }

            // Priorité 2: Utiliser TextPreview si RawData n'est pas disponible
            if (!string.IsNullOrEmpty(item.TextPreview))
            {
                _loggingService?.LogInfo($"FilePathContentHandler - Utilisation du TextPreview: '{item.TextPreview}'");
                return item.TextPreview;
            }

            // Priorité 3: Message par défaut si aucune donnée n'est disponible
            string defaultMessage = GetDefaultUnavailableMessage();
            _loggingService?.LogInfo($"FilePathContentHandler - Aucune donnée disponible, utilisation du message par défaut: '{defaultMessage}'");
            
            return defaultMessage;
        }

        /// <summary>
        /// Formate un chemin de fichier avec des informations supplémentaires.
        /// </summary>
        /// <param name="filePath">Le chemin du fichier</param>
        /// <returns>Le chemin formaté avec des informations sur l'existence et la taille</returns>
        private string FormatFilePath(string filePath)
        {
            try
            {
                _loggingService?.LogInfo($"FilePathContentHandler.FormatFilePath - Formatage du chemin: '{filePath}'");

                // Validation du chemin
                if (!_fileSystemService.IsValidPath(filePath))
                {
                    _loggingService?.LogWarning($"FilePathContentHandler.FormatFilePath - Chemin invalide: '{filePath}'");
                    return $"Chemin: {filePath}\n(Chemin invalide)";
                }

                // Vérification de l'existence du fichier
                if (_fileSystemService.FileExists(filePath))
                {
                    _loggingService?.LogInfo($"FilePathContentHandler.FormatFilePath - Fichier existe: '{filePath}'");
                    
                    // Obtenir des informations supplémentaires sur le fichier
                    long? fileSize = _fileSystemService.GetFileSize(filePath);
                    DateTime? lastModified = _fileSystemService.GetLastWriteTime(filePath);

                    string result = $"Chemin: {filePath}\n(Fichier existant";
                    
                    if (fileSize.HasValue)
                    {
                        string formattedSize = _fileSizeFormatter.FormatSize(fileSize.Value);
                        result += $", Taille: {formattedSize}";
                        _loggingService?.LogInfo($"FilePathContentHandler.FormatFilePath - Taille du fichier: {formattedSize}");
                    }

                    if (lastModified.HasValue)
                    {
                        result += $", Modifié: {lastModified.Value:dd/MM/yyyy HH:mm}";
                        _loggingService?.LogInfo($"FilePathContentHandler.FormatFilePath - Dernière modification: {lastModified.Value}");
                    }

                    result += ")";
                    return result;
                }
                else if (_fileSystemService.DirectoryExists(filePath))
                {
                    _loggingService?.LogInfo($"FilePathContentHandler.FormatFilePath - Répertoire existe: '{filePath}'");
                    return $"Chemin: {filePath}\n(Répertoire existant)";
                }
                else
                {
                    _loggingService?.LogInfo($"FilePathContentHandler.FormatFilePath - Fichier/répertoire n'existe pas: '{filePath}'");
                    return $"Chemin: {filePath}\n(Le fichier n'existe plus)";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"FilePathContentHandler.FormatFilePath - Erreur lors du formatage de '{filePath}': {ex.Message}", ex);
                return $"Chemin: {filePath}\n(Erreur lors de la vérification)";
            }
        }
    }
}
