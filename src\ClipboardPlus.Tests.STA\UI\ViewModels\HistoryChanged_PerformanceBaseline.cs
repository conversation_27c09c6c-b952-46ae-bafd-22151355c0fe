using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;
using Moq;
using NUnit.Framework;

// Référence vers les classes du harnais de sécurité
using ClipboardPlus.Tests.STA.UI.ViewModels;

namespace ClipboardPlus.Tests.STA.UI.ViewModels
{
    /// <summary>
    /// Tests de performance baseline pour ClipboardHistoryManager_HistoryChanged.
    ///
    /// OBJECTIF: Établir les métriques de performance actuelles pour détecter
    /// les régressions majeures (>50%) après refactoring.
    ///
    /// Métriques simples uniquement - pas de micro-optimisations.
    /// </summary>
    [TestFixture]
    [Apartment(System.Threading.ApartmentState.STA)]
    public class HistoryChanged_PerformanceBaseline
    {
        private Mock<IClipboardHistoryManager> _mockHistoryManager = null!;
        private Mock<IClipboardInteractionService> _mockClipboardInteractionService = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IUserNotificationService> _mockUserNotificationService = null!;
        private Mock<IUserInteractionService> _mockUserInteractionService = null!;
        private TestableClipboardHistoryViewModel _viewModel = null!;
        private HistoryChangedBehaviorCapture _behaviorCapture = null!;

        [SetUp]
        public void Setup()
        {
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockClipboardInteractionService = new Mock<IClipboardInteractionService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockUserNotificationService = new Mock<IUserNotificationService>();
            _mockUserInteractionService = new Mock<IUserInteractionService>();
            _behaviorCapture = new HistoryChangedBehaviorCapture();

            // Configuration avec une liste d'items réaliste pour les tests de performance
            var testItems = CreateTestItems(50); // 50 items pour simuler un usage normal
            _mockHistoryManager.Setup(m => m.HistoryItems).Returns(testItems);
            _mockSettingsManager.Setup(s => s.MaxHistoryItems).Returns(100);
            _mockSettingsManager.Setup(s => s.HideTimestamp).Returns(false);

            _viewModel = new TestableClipboardHistoryViewModel(
                _mockHistoryManager.Object,
                _mockClipboardInteractionService.Object,
                _mockSettingsManager.Object,
                _mockUserNotificationService.Object,
                _mockUserInteractionService.Object,
                new Mock<IServiceProvider>().Object, // Fournir un mock pour IServiceProvider
                _behaviorCapture);
        }

        [TearDown]
        public void TearDown()
        {
            _viewModel?.Dispose();
        }

        /// <summary>
        /// Établit la baseline de temps d'exécution pour HistoryChanged.
        ///
        /// SEUIL: Si après refactoring le temps moyen dépasse 150ms (50% de plus que baseline),
        /// c'est une régression de performance inacceptable.
        /// </summary>
        [Test]
        [Description("BASELINE: Temps d'exécution moyen de HistoryChanged")]
        public void HistoryChanged_ExecutionTime_ShouldEstablishBaseline()
        {
            // Arrange
            const int iterations = 10; // Nombre d'itérations pour calculer la moyenne
            var executionTimes = new List<long>();

            // Act - Mesurer le temps d'exécution sur plusieurs itérations
            for (int i = 0; i < iterations; i++)
            {
                _behaviorCapture.Reset();

                var stopwatch = Stopwatch.StartNew();
                try
                {
                    _viewModel.TriggerHistoryChangedForTest();
                }
                catch (Exception)
                {
                    // Ignorer les exceptions pour ce test de performance
                    // On mesure juste le temps d'exécution
                }
                stopwatch.Stop();

                executionTimes.Add(stopwatch.ElapsedMilliseconds);
            }

            // Assert - Calculer et afficher les métriques baseline
            var averageTime = executionTimes.Average();
            var maxTime = executionTimes.Max();
            var minTime = executionTimes.Min();

            TestContext.WriteLine($"=== BASELINE PERFORMANCE METRICS ===");
            TestContext.WriteLine($"Temps d'exécution moyen: {averageTime:F2}ms");
            TestContext.WriteLine($"Temps minimum: {minTime}ms");
            TestContext.WriteLine($"Temps maximum: {maxTime}ms");
            TestContext.WriteLine($"Écart-type: {CalculateStandardDeviation(executionTimes):F2}ms");
            TestContext.WriteLine($"=====================================");

            // Seuil de régression: 100ms (ajustable selon les résultats)
            Assert.That(averageTime, Is.LessThan(100),
                $"BASELINE ÉTABLIE: Temps moyen actuel {averageTime:F2}ms. " +
                $"Après refactoring, ne doit pas dépasser {averageTime * 1.5:F2}ms (seuil +50%)");

            // Vérifier qu'aucune exécution n'est anormalement lente
            Assert.That(maxTime, Is.LessThan(500),
                $"Aucune exécution ne devrait dépasser 500ms (actuel max: {maxTime}ms)");
        }

        /// <summary>
        /// Test de baseline mémoire simple.
        ///
        /// Vérifie qu'il n'y a pas de fuite mémoire évidente après plusieurs exécutions.
        /// </summary>
        [Test]
        [Description("BASELINE: Pas de fuite mémoire évidente")]
        public void HistoryChanged_MemoryUsage_ShouldNotLeakMemory()
        {
            // Arrange
            const int iterations = 100; // Plus d'itérations pour détecter les fuites

            // Mesure mémoire initiale
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            var initialMemory = GC.GetTotalMemory(false);

            // Act - Exécuter plusieurs fois pour détecter les fuites
            for (int i = 0; i < iterations; i++)
            {
                _behaviorCapture.Reset();
                try
                {
                    _viewModel.TriggerHistoryChangedForTest();
                }
                catch (Exception)
                {
                    // Ignorer les exceptions pour ce test mémoire
                }
            }

            // Forcer le garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            var finalMemory = GC.GetTotalMemory(false);

            // Assert
            var memoryDifference = finalMemory - initialMemory;
            var memoryDifferenceKB = memoryDifference / 1024.0;

            TestContext.WriteLine($"=== BASELINE MEMORY METRICS ===");
            TestContext.WriteLine($"Mémoire initiale: {initialMemory / 1024.0:F2} KB");
            TestContext.WriteLine($"Mémoire finale: {finalMemory / 1024.0:F2} KB");
            TestContext.WriteLine($"Différence: {memoryDifferenceKB:F2} KB");
            TestContext.WriteLine($"Itérations: {iterations}");
            TestContext.WriteLine($"===============================");

            // Seuil tolérant: 1MB de différence max (peut inclure d'autres allocations)
            Assert.That(Math.Abs(memoryDifferenceKB), Is.LessThan(1024),
                $"BASELINE MÉMOIRE: Différence actuelle {memoryDifferenceKB:F2} KB. " +
                $"Pas de fuite mémoire majeure détectée après {iterations} exécutions");
        }

        /// <summary>
        /// Test de stress simple pour vérifier la stabilité sous charge.
        /// </summary>
        [Test]
        [Description("BASELINE: Stabilité sous charge modérée")]
        public void HistoryChanged_StressTest_ShouldRemainStable()
        {
            // Arrange
            const int stressIterations = 50;
            int successfulExecutions = 0;
            int exceptions = 0;
            var executionTimes = new List<long>();

            // Act - Test de stress modéré
            for (int i = 0; i < stressIterations; i++)
            {
                _behaviorCapture.Reset();
                var stopwatch = Stopwatch.StartNew();

                try
                {
                    _viewModel.TriggerHistoryChangedForTest();
                    successfulExecutions++;
                }
                catch (Exception)
                {
                    exceptions++;
                }

                stopwatch.Stop();
                executionTimes.Add(stopwatch.ElapsedMilliseconds);
            }

            // Assert - Vérifier la stabilité
            var successRate = (double)successfulExecutions / stressIterations * 100;
            var averageTime = executionTimes.Average();

            TestContext.WriteLine($"=== BASELINE STRESS TEST ===");
            TestContext.WriteLine($"Exécutions réussies: {successfulExecutions}/{stressIterations} ({successRate:F1}%)");
            TestContext.WriteLine($"Exceptions: {exceptions}");
            TestContext.WriteLine($"Temps moyen sous stress: {averageTime:F2}ms");
            TestContext.WriteLine($"============================");

            // La méthode devrait être stable même avec des exceptions internes
            Assert.That(successRate, Is.GreaterThan(50),
                $"BASELINE STABILITÉ: Taux de succès actuel {successRate:F1}%. " +
                $"Après refactoring, ne devrait pas être significativement pire");
        }

        #region Helper Methods

        private List<ClipboardItem> CreateTestItems(int count)
        {
            var items = new List<ClipboardItem>();
            for (int i = 1; i <= count; i++)
            {
                items.Add(new ClipboardItem
                {
                    Id = i,
                    DataType = ClipboardDataType.Text,
                    TextPreview = $"Test Item {i}",
                    CustomName = $"Item {i}",
                    Timestamp = DateTime.Now.AddMinutes(-i),
                    IsPinned = i % 10 == 0 // Quelques items épinglés
                });
            }
            return items;
        }

        private double CalculateStandardDeviation(List<long> values)
        {
            var average = values.Average();
            var sumOfSquares = values.Sum(x => Math.Pow(x - average, 2));
            return Math.Sqrt(sumOfSquares / values.Count);
        }

        #endregion
    }
}
