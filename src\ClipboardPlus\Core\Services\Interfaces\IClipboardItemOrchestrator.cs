using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Interfaces
{
    /// <summary>
    /// Interface pour l'orchestrateur principal des opérations sur les éléments du presse-papiers.
    /// Responsabilité unique : Coordonner les différents services selon le pattern SOLID.
    /// </summary>
    public interface IClipboardItemOrchestrator
    {
        /// <summary>
        /// Ajoute un élément au presse-papiers en orchestrant toutes les étapes nécessaires.
        /// </summary>
        /// <param name="item">L'élément à ajouter</param>
        /// <returns>L'ID de l'élément ajouté ou mis à jour</returns>
        /// <exception cref="ArgumentNullException">Si l'élément est null</exception>
        /// <exception cref="ArgumentException">Si l'élément ne respecte pas les règles de validation</exception>
        Task<long> AddItemAsync(ClipboardItem item);
    }
}
