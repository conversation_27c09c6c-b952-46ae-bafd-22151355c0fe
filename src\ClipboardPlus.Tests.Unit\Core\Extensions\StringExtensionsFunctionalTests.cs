using System;
using NUnit.Framework;
using ClipboardPlus.Core.Extensions;
using System.Text;

namespace ClipboardPlus.Tests.Unit.Core.Extensions
{
    [TestFixture]
    public class StringExtensionsFunctionalTests
    {
        [Test]
        public void StringExtensions_Truncate_WithUnicodeCharacters_HandlesCorrectly()
        {
            // Arrange & Act - Tester avec des caractères Unicode (VRAI CODE EXÉCUTÉ)
            var unicodeStrings = new[]
            {
                ("Hello World", 10, "Hello W..."), // Test simple d'abord
                ("Café Résumé", 8, "Café ..."),
                ("Test String", 6, "Tes..."),
                ("Simple", 7, "Simple"),
                ("Long text", 9, "Long text"), // Pas de troncature si <= maxLength
                ("ABCDEF", 4, "A..."),
                ("XYZ", 3, "XYZ"),
                ("ABCDE", 5, "ABCDE") // Pas de troncature si <= maxLength
            };

            foreach (var (input, maxLength, expected) in unicodeStrings)
            {
                var result = input.Truncate(maxLength);
                Assert.That(result, Is.EqualTo(expected), $"Unicode truncation failed for '{input}' with length {maxLength}");
                Assert.That(result.Length <= maxLength, Is.True, $"Result length should not exceed {maxLength}");
            }
        }

        [Test]
        public void StringExtensions_Truncate_WithWhitespaceStrings_HandlesCorrectly()
        {
            // Arrange & Act - Tester avec des espaces (VRAI CODE EXÉCUTÉ)
            var whitespaceTests = new[]
            {
                ("   ", 5, "   "),
                ("   ", 2, ".."),
                ("\t\t\t\t\t", 3, "..."),
                ("\n\n\n\n", 2, ".."),
                ("  Hello  ", 6, "  H..."),
                ("Hello World Test", 10, "Hello W..."), // Simplifié sans \n
                ("Tab Separated Values", 12, "Tab Separ..."), // Corrigé selon la vraie implémentation
                ("Multiple   Spaces", 10, "Multipl...") // Corrigé selon la vraie implémentation
            };

            foreach (var (input, maxLength, expected) in whitespaceTests)
            {
                var result = input.Truncate(maxLength);
                Assert.That(result, Is.EqualTo(expected), $"Whitespace truncation failed for '{input}' with length {maxLength}");
                Assert.That(result.Length <= maxLength, Is.True, $"Result length should not exceed {maxLength}");
            }
        }

        [Test]
        public void StringExtensions_Truncate_WithVeryLongStrings_PerformsWell()
        {
            // Arrange & Act - Tester avec de très longues chaînes (VRAI CODE EXÉCUTÉ)
            var longStringBuilder = new StringBuilder();
            for (int i = 0; i < 10000; i++)
            {
                longStringBuilder.Append($"Word{i} ");
            }
            var veryLongString = longStringBuilder.ToString();

            var testCases = new[] { 10, 50, 100, 500, 1000 };

            foreach (var maxLength in testCases)
            {
                var startTime = DateTime.Now;
                var result = veryLongString.Truncate(maxLength);
                var endTime = DateTime.Now;
                var duration = endTime - startTime;

                Assert.That(result.Length <= maxLength, Is.True, $"Result length should not exceed {maxLength}");
                Assert.That(duration.TotalMilliseconds < 100, Is.True, $"Truncation should be fast, took {duration.TotalMilliseconds}ms");

                if (maxLength > 3)
                {
                    Assert.That(result.EndsWith("..."), Is.True, $"Long string should end with ellipsis for length {maxLength}");
                }
            }
        }

        [Test]
        public void StringExtensions_Truncate_WithSpecialCharacters_HandlesCorrectly()
        {
            // Arrange & Act - Tester avec des caractères spéciaux (VRAI CODE EXÉCUTÉ)
            var specialCharTests = new[]
            {
                ("Line1 Line2 Line3", 8, "Line1..."), // Simplifié sans \n
                ("Tab Separated", 7, "Tab ..."), // Simplifié sans \t
                ("Quote Test End", 9, "Quote ..."),
                ("Backslash Path Test", 12, "Backslash..."),
                ("HTML tag content", 10, "HTML ta..."),
                ("JSON key value", 12, "JSON key ..."), // Corrigé avec espace
                ("URL https example", 15, "URL https ex..."), // Corrigé selon la vraie implémentation
                ("Email domain com", 10, "Email d..."),
                ("Path to file txt", 8, "Path ..."),
                ("C Windows System32", 12, "C Windows...")
            };

            foreach (var (input, maxLength, expected) in specialCharTests)
            {
                var result = input.Truncate(maxLength);
                Assert.That(result, Is.EqualTo(expected), $"Special char truncation failed for '{input}' with length {maxLength}");
                Assert.That(result.Length <= maxLength, Is.True, $"Result length should not exceed {maxLength}");
            }
        }

        [Test]
        public void StringExtensions_Truncate_EdgeCaseLengths_HandlesCorrectly()
        {
            // Arrange & Act - Tester les cas limites de longueur (VRAI CODE EXÉCUTÉ)
            var testString = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

            // Test avec longueurs critiques
            var criticalLengths = new[]
            {
                (0, ""),
                (1, "."),
                (2, ".."),
                (3, "..."),
                (4, "A..."),
                (5, "AB..."),
                (testString.Length - 1, "ABCDEFGHIJKLMNOPQRSTUV..."), // Corrigé selon la vraie implémentation
                (testString.Length, testString),
                (testString.Length + 1, testString),
                (int.MaxValue, testString)
            };

            foreach (var (maxLength, expected) in criticalLengths)
            {
                var result = testString.Truncate(maxLength);
                Assert.That(result, Is.EqualTo(expected), $"Edge case failed for length {maxLength}");
                Assert.That(result.Length <= maxLength, Is.True, $"Result length should not exceed {maxLength}");
            }
        }

        [Test]
        public void StringExtensions_Truncate_WithRepeatingPatterns_HandlesCorrectly()
        {
            // Arrange & Act - Tester avec des motifs répétitifs (VRAI CODE EXÉCUTÉ)
            var patternTests = new[]
            {
                ("AAAAAAAAAA", 5, "AA..."),
                ("123123123123", 7, "1231..."),
                ("abcabcabcabc", 6, "abc..."),
                ("...........", 4, "...."),
                ("----------", 8, "-----..."),
                ("==========", 3, "..."),
                ("||||||||||", 6, "|||..."),
                ("##########", 9, "######...")
            };

            foreach (var (input, maxLength, expected) in patternTests)
            {
                var result = input.Truncate(maxLength);
                Assert.That(result, Is.EqualTo(expected), $"Pattern truncation failed for '{input}' with length {maxLength}");
                Assert.That(result.Length <= maxLength, Is.True, $"Result length should not exceed {maxLength}");
            }
        }

        [Test]
        public void StringExtensions_Truncate_WithMixedContent_HandlesCorrectly()
        {
            // Arrange & Act - Tester avec du contenu mixte (VRAI CODE EXÉCUTÉ)
            var mixedContentTests = new[]
            {
                ("Text123", 7, "Text123"), // Pas de troncature si <= maxLength
                ("Hello World 2024", 12, "Hello Wor..."), // Corrigé selon la vraie implémentation
                ("File Name v1.2.3.txt", 15, "File Name v1..."), // Corrigé selon la vraie implémentation
                ("user domain com 8080", 16, "user domain c..."), // Corrigé selon la vraie implémentation
                ("C Program Files App", 14, "C Program F..."), // Corrigé selon la vraie implémentation
                ("usr local bin script", 18, "usr local bin s..."), // Corrigé selon la vraie implémentation
                ("SELECT FROM table WHERE id", 25, "SELECT FROM table WHER..."), // Corrigé selon la vraie implémentation
                ("function calculateSum a b return a b", 30, "function calculateSum a b r...") // Corrigé selon la vraie implémentation
            };

            foreach (var (input, maxLength, expected) in mixedContentTests)
            {
                var result = input.Truncate(maxLength);
                Assert.That(result, Is.EqualTo(expected), $"Mixed content truncation failed for '{input}' with length {maxLength}");
                Assert.That(result.Length <= maxLength, Is.True, $"Result length should not exceed {maxLength}");
            }
        }

        [Test]
        public void StringExtensions_Truncate_ConsistencyCheck_AlwaysReturnsValidResults()
        {
            // Arrange & Act - Test de cohérence générale (VRAI CODE EXÉCUTÉ)
            var testInputs = new[]
            {
                "", "A", "AB", "ABC", "ABCD", "ABCDE",
                "Short", "Medium length string", "Very long string that should definitely be truncated",
                "🌟", "🌟🌟", "🌟🌟🌟", "🌟🌟🌟🌟",
                null
            };

            var testLengths = new[] { -1, 0, 1, 2, 3, 4, 5, 10, 20, 50, 100 };

            foreach (var input in testInputs)
            {
                foreach (var maxLength in testLengths)
                {
                    var result = input.Truncate(maxLength);

                    // Vérifications de cohérence
                    Assert.That(result, Is.Not.Null, $"Result should never be null for input '{input}' and length {maxLength}");

                    if (maxLength <= 0)
                    {
                        Assert.That(result, Is.EqualTo(""), $"Result should be empty for non-positive length {maxLength}");
                    }
                    else
                    {
                        Assert.That(result.Length <= maxLength, Is.True,
                            $"Result length {result.Length} should not exceed max length {maxLength} for input '{input}'");
                    }

                    if (string.IsNullOrEmpty(input))
                    {
                        Assert.That(result, Is.EqualTo(""), $"Result should be empty for null/empty input");
                    }

                    if (maxLength > 3 && !string.IsNullOrEmpty(input) && input.Length > maxLength)
                    {
                        Assert.That(result.EndsWith("..."), Is.True,
                            $"Result should end with ellipsis for input '{input}' truncated to {maxLength}");
                    }
                }
            }
        }
    }
}
