using System;
using System.Linq;
using System.Text;
using ClipboardPlus.Core.Services.Diagnostics;

namespace ClipboardPlus.Services.Diagnostics
{
    /// <summary>
    /// Implémentation du formateur de diagnostic avec StringBuilder optimisé
    /// </summary>
    public class DiagnosticFormatter : IDiagnosticFormatter
    {
        private const string SEPARATOR = "=====";
        private const string SUB_SEPARATOR = "-----";

        public string FormatDiagnosticHeader(string message, DateTime timestamp)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"{SEPARATOR} DÉBUT DIAGNOSTIC DE SUPPRESSION - {timestamp:yyyy-MM-dd HH:mm:ss.fff} {SEPARATOR}");
            sb.AppendLine($"Opération: {message}");
            sb.AppendLine($"Thread ID: {Environment.CurrentManagedThreadId}");
            sb.AppendLine($"Application Version: {GetType().Assembly.GetName().Version}");
            sb.AppendLine();
            return sb.ToString();
        }

        public string FormatItemInformation(DiagnosticData itemData)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"{SUB_SEPARATOR} INFORMATIONS ÉLÉMENT {SUB_SEPARATOR}");

            if (itemData.Properties.ContainsKey("IsNull") && itemData.Properties["IsNull"] is bool isNull && isNull)
            {
                sb.AppendLine("Élément: NULL");
            }
            else
            {
                sb.AppendLine($"ID: {itemData.Properties.GetValueOrDefault("Id", "N/A")}");
                sb.AppendLine($"Type: {itemData.Properties.GetValueOrDefault("DataType", "N/A")}");
                sb.AppendLine($"Timestamp: {itemData.Properties.GetValueOrDefault("Timestamp", "N/A")}");
                sb.AppendLine($"Épinglé: {itemData.Properties.GetValueOrDefault("IsPinned", "N/A")}");
                sb.AppendLine($"Titre visible: {itemData.Properties.GetValueOrDefault("IsTitleVisible", "N/A")}");
                sb.AppendLine($"Nom personnalisé: {itemData.Properties.GetValueOrDefault("CustomName", "N/A")}");
                sb.AppendLine($"Aperçu: {itemData.Properties.GetValueOrDefault("TextPreview", "N/A")}");
                var hasRawData = itemData.Properties.GetValueOrDefault("HasRawData", false) is bool hasData && hasData;
                sb.AppendLine($"Données brutes: {(hasRawData ? "Oui" : "Non")} ({itemData.Properties.GetValueOrDefault("RawDataSize", 0)} bytes)");
            }

            // Avertissements
            if (itemData.Warnings.Any())
            {
                sb.AppendLine("⚠️ Avertissements:");
                foreach (var warning in itemData.Warnings)
                {
                    sb.AppendLine($"  - {warning}");
                }
            }

            // Erreurs
            if (itemData.Errors.Any())
            {
                sb.AppendLine("❌ Erreurs:");
                foreach (var error in itemData.Errors)
                {
                    sb.AppendLine($"  - {error}");
                }
            }

            sb.AppendLine();
            return sb.ToString();
        }

        public string FormatViewModelInformation(DiagnosticData viewModelData)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"{SUB_SEPARATOR} INFORMATIONS VIEWMODEL {SUB_SEPARATOR}");

            sb.AppendLine($"Nombre d'éléments: {viewModelData.Properties.GetValueOrDefault("HistoryItemsCount", "N/A")}");
            var hasSelectedItem = viewModelData.Properties.GetValueOrDefault("HasSelectedItem", false) is bool hasSelected && hasSelected;
            sb.AppendLine($"Élément sélectionné: {(hasSelectedItem ? "Oui" : "Non")} (ID: {viewModelData.Properties.GetValueOrDefault("SelectedItemId", "N/A")})");
            sb.AppendLine($"En cours de chargement: {viewModelData.Properties.GetValueOrDefault("IsLoading", "N/A")}");
            sb.AppendLine($"Éléments épinglés: {viewModelData.Properties.GetValueOrDefault("PinnedItemsCount", "N/A")}");
            sb.AppendLine($"Titres visibles: {viewModelData.Properties.GetValueOrDefault("VisibleTitleCount", "N/A")}");

            // État des commandes
            sb.AppendLine("État des commandes:");
            var hasDeleteCommand = viewModelData.Properties.GetValueOrDefault("HasDeleteCommand", false) is bool hasDelete && hasDelete;
            var hasClearAllCommand = viewModelData.Properties.GetValueOrDefault("HasClearAllCommand", false) is bool hasClearAll && hasClearAll;
            var hasPasteCommand = viewModelData.Properties.GetValueOrDefault("HasPasteCommand", false) is bool hasPaste && hasPaste;

            sb.AppendLine($"  - Supprimer élément: {(hasDeleteCommand ? "Disponible" : "Indisponible")}");
            sb.AppendLine($"  - Supprimer tout: {(hasClearAllCommand ? "Disponible" : "Indisponible")}");
            sb.AppendLine($"  - Coller: {(hasPasteCommand ? "Disponible" : "Indisponible")}");

            // Avertissements
            if (viewModelData.Warnings.Any())
            {
                sb.AppendLine("⚠️ Avertissements:");
                foreach (var warning in viewModelData.Warnings)
                {
                    sb.AppendLine($"  - {warning}");
                }
            }

            // Erreurs
            if (viewModelData.Errors.Any())
            {
                sb.AppendLine("❌ Erreurs:");
                foreach (var error in viewModelData.Errors)
                {
                    sb.AppendLine($"  - {error}");
                }
            }

            sb.AppendLine();
            return sb.ToString();
        }

        public string FormatSystemInformation(DiagnosticData systemData)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"{SUB_SEPARATOR} INFORMATIONS SYSTÈME {SUB_SEPARATOR}");

            sb.AppendLine($"Thread ID: {systemData.Properties.GetValueOrDefault("ThreadId", "N/A")}");
            sb.AppendLine($"Timestamp: {systemData.Properties.GetValueOrDefault("Timestamp", "N/A")}");
            sb.AppendLine($"Version application: {systemData.Properties.GetValueOrDefault("ApplicationVersion", "N/A")}");
            sb.AppendLine($"Mémoire utilisée: {FormatBytes((long)systemData.Properties.GetValueOrDefault("WorkingSet", 0L))}");
            sb.AppendLine($"Processeurs: {systemData.Properties.GetValueOrDefault("ProcessorCount", "N/A")}");
            sb.AppendLine($"OS: {systemData.Properties.GetValueOrDefault("OSVersion", "N/A")}");
            sb.AppendLine($"64-bit: {systemData.Properties.GetValueOrDefault("Is64BitProcess", "N/A")}");

            // Avertissements
            if (systemData.Warnings.Any())
            {
                sb.AppendLine("⚠️ Avertissements:");
                foreach (var warning in systemData.Warnings)
                {
                    sb.AppendLine($"  - {warning}");
                }
            }

            // Erreurs
            if (systemData.Errors.Any())
            {
                sb.AppendLine("❌ Erreurs:");
                foreach (var error in systemData.Errors)
                {
                    sb.AppendLine($"  - {error}");
                }
            }

            sb.AppendLine();
            return sb.ToString();
        }

        public string AssembleDiagnosticReport(string header, string itemInfo, string viewModelInfo, string systemInfo)
        {
            var sb = new StringBuilder();
            sb.Append(header);
            sb.Append(itemInfo);
            sb.Append(viewModelInfo);
            sb.Append(systemInfo);
            sb.AppendLine($"{SEPARATOR} FIN DIAGNOSTIC DE SUPPRESSION {SEPARATOR}");
            return sb.ToString();
        }

        private static string FormatBytes(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} MB";
            return $"{bytes / (1024 * 1024 * 1024):F1} GB";
        }
    }
}
