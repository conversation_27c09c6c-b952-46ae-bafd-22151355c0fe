using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Interfaces
{
    /// <summary>
    /// Interface pour la gestion de l'historique en mémoire dans l'architecture SOLID.
    ///
    /// Responsabilité unique : Gérer la liste des éléments en mémoire et fournir
    /// l'accès aux données pour la détection des doublons.
    /// </summary>
    /// <remarks>
    /// Cette interface est utilisée dans le cadre de la migration vers l'architecture SOLID
    /// pour résoudre le problème critique où le DuplicateDetector ne voyait pas les éléments
    /// de l'historique (collection vide).
    ///
    /// L'implémentation HistoryManagerWrapper utilise cette interface pour maintenir
    /// une synchronisation entre l'historique réel et la collection accessible au
    /// DuplicateDetector.
    ///
    /// Date de migration : 2025-07-18
    /// Problème résolu : Détection des doublons défaillante
    /// </remarks>
    public interface IHistoryManager
    {
        /// <summary>
        /// Ajoute un élément à l'historique en mémoire.
        /// </summary>
        /// <param name="item">L'élément à ajouter</param>
        void AddToHistory(ClipboardItem item);

        /// <summary>
        /// Met à jour la position d'un élément existant dans l'historique.
        /// </summary>
        /// <param name="item">L'élément à déplacer en première position</param>
        void MoveToTop(ClipboardItem item);

        /// <summary>
        /// Met à jour un élément existant dans la collection et le déplace en première position.
        /// Utilisé quand un duplicata est détecté pour mettre à jour le timestamp et repositionner l'élément.
        /// </summary>
        /// <param name="existingItem">L'élément existant à mettre à jour et repositionner</param>
        void UpdateExistingItem(ClipboardItem existingItem);

        /// <summary>
        /// Applique la limite maximale d'éléments dans l'historique.
        /// </summary>
        /// <param name="maxItems">Nombre maximum d'éléments autorisés</param>
        /// <returns>Nombre d'éléments supprimés</returns>
        Task<int> EnforceMaxHistoryItemsAsync(int maxItems);

        /// <summary>
        /// Obtient la liste actuelle des éléments de l'historique.
        /// </summary>
        /// <returns>
        /// Une collection en lecture seule contenant tous les éléments de l'historique
        /// actuellement disponibles. Cette collection est utilisée par le DuplicateDetector
        /// pour la détection des doublons.
        /// </returns>
        /// <remarks>
        /// CRITIQUE : Cette méthode doit toujours retourner la collection réelle et à jour
        /// de l'historique. Dans l'implémentation HistoryManagerWrapper, elle retourne
        /// la collection synchronisée avec l'historique persistant.
        ///
        /// Avant la migration SOLID, cette collection était vide, causant l'échec
        /// de la détection des doublons.
        /// </remarks>
        IReadOnlyList<ClipboardItem> GetHistoryItems();
    }
}
