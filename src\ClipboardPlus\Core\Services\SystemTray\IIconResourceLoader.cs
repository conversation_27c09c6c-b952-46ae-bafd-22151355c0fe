using System;
using System.Drawing;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Interface pour le chargement des ressources d'icône.
    /// Responsabilité unique : charger les icônes depuis les ressources avec fallback.
    /// </summary>
    public interface IIconResourceLoader
    {
        /// <summary>
        /// Charge une icône depuis les ressources de l'application.
        /// </summary>
        /// <param name="resourcePath">Le chemin de la ressource (ex: "pack://application:,,,/Resources/icon.ico").</param>
        /// <returns>L'icône chargée ou l'icône de fallback si le chargement échoue.</returns>
        Icon LoadIconFromResource(string resourcePath);

        /// <summary>
        /// Obtient l'icône de fallback utilisée en cas d'échec de chargement.
        /// </summary>
        /// <returns>L'icône de fallback par défaut.</returns>
        Icon GetFallbackIcon();

        /// <summary>
        /// Vérifie si une ressource d'icône existe.
        /// </summary>
        /// <param name="resourcePath">Le chemin de la ressource à vérifier.</param>
        /// <returns>True si la ressource existe, false sinon.</returns>
        bool ResourceExists(string resourcePath);
    }
}
