using System;
using System.Windows;
using System.Threading.Tasks;
using System.Windows.Threading;
using ClipboardPlus.Core.Services;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Orchestrateur du démarrage de l'application.
    /// Centralise la logique de démarrage et d'initialisation des services.
    /// </summary>
    public interface IStartupOrchestrator
    {
        /// <summary>
        /// Initialise et démarre l'application.
        /// </summary>
        /// <param name="serviceProvider">Le fournisseur de services pour résoudre les dépendances.</param>
        /// <param name="shortcutActivatedHandler">Gestionnaire d'événement pour l'activation des raccourcis.</param>
        /// <param name="clipboardChangedHandler">Gestionnaire d'événement pour les changements du presse-papiers.</param>
        /// <returns>L'instance de ISystemTrayService initialisée, ou null si l'initialisation a échoué.</returns>
        Task<ISystemTrayService?> StartApplication(IServiceProvider serviceProvider, EventHandler? shortcutActivatedHandler, EventHandler? clipboardChangedHandler);

        /// <summary>
        /// Crée et affiche une fenêtre principale cachée pour permettre à l'application de rester en vie.
        /// </summary>
        /// <returns>La fenêtre principale cachée.</returns>
        Window CreateHiddenMainWindow();
    }

    /// <summary>
    /// Implémentation de l'orchestrateur de démarrage.
    /// </summary>
    public class StartupOrchestrator : IStartupOrchestrator
    {
        private readonly ILoggingService _loggingService;
        private readonly IUserNotificationService _userNotificationService;
        private ISystemTrayService? _systemTrayService;

        /// <summary>
        /// Initialise une nouvelle instance de la classe <see cref="StartupOrchestrator"/>.
        /// </summary>
        /// <param name="loggingService">Le service de journalisation.</param>
        /// <param name="userNotificationService">Le service de notification utilisateur.</param>
        public StartupOrchestrator(ILoggingService loggingService, IUserNotificationService userNotificationService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _userNotificationService = userNotificationService ?? throw new ArgumentNullException(nameof(userNotificationService));
        }

        /// <inheritdoc />
        public async Task<ISystemTrayService?> StartApplication(IServiceProvider serviceProvider, EventHandler? shortcutActivatedHandler, EventHandler? clipboardChangedHandler)
        {
            if (serviceProvider == null)
            {
                _loggingService.LogCritical("StartApplication: Le fournisseur de services est null. Impossible de continuer.");
                return null;
            }

            _loggingService.LogInfo("========== StartupOrchestrator.StartApplication: DÉBUT ==========");

            try
            {
                // Initialiser les services de base
                _loggingService.LogInfo("StartApplication: Résolution de IApplicationLifetimeManager.");
                var lifetimeManager = serviceProvider.GetService<IApplicationLifetimeManager>();
                if (lifetimeManager == null)
                {
                    _loggingService.LogCritical("StartApplication: Le service ApplicationLifetimeManager n'a pas pu être résolu. Arrêt.");
                    return null;
                }
                _loggingService.LogInfo("StartApplication: IApplicationLifetimeManager résolu avec succès.");

                // Initialiser les services et abonner aux événements
                _loggingService.LogInfo("StartApplication: Appel de lifetimeManager.InitializeServices...");
                _systemTrayService = await lifetimeManager.InitializeServices(
                    serviceProvider, 
                    shortcutActivatedHandler ?? ((s, e) => { _loggingService.LogDebug("Raccourci activé (handler par défaut)."); }), 
                    clipboardChangedHandler ?? ((s, e) => { _loggingService.LogDebug("Presse-papiers modifié (handler par défaut)."); }));

                if (_systemTrayService == null)
                {
                    _loggingService.LogCritical("StartApplication: lifetimeManager.InitializeServices a renvoyé NULL.");
                }
                else
                {
                    _loggingService.LogInfo("StartApplication: lifetimeManager.InitializeServices a renvoyé une instance de SystemTrayService.");
                }

                _loggingService.LogInfo("========== StartupOrchestrator.StartApplication: FIN ==========");
                return _systemTrayService;
            }
            catch (Exception ex)
            {
                _loggingService.LogCritical($"StartApplication: Exception non gérée: {ex.Message}", ex);
                
                // Utiliser le service GlobalExceptionManager pour journaliser l'exception
                var exceptionManager = serviceProvider.GetService<IGlobalExceptionManager>();
                if (exceptionManager != null)
                {
                    exceptionManager.LogUnhandledException("UI-Thread-Startup", ex, false);
                }
                else
                {
                    // Fallback si le service n'est pas disponible
                    _userNotificationService.ShowError("Erreur critique", $"Une erreur critique est survenue lors du démarrage de l'application : {ex.Message}\n\nL'application va se fermer.");
                }
                
                return null;
            }
        }

        /// <inheritdoc />
        public Window CreateHiddenMainWindow()
        {
            _loggingService.LogInfo("CreateHiddenMainWindow: Création d'une fenêtre principale cachée");
            try
            {
                var hiddenWindow = new Window
                {
                    Width = 0,
                    Height = 0,
                    WindowStyle = WindowStyle.None,
                    ShowInTaskbar = false,
                    Visibility = Visibility.Hidden,
                    Title = "ClipboardPlusMainWindow"
                };
                
                _loggingService.LogInfo("CreateHiddenMainWindow: Fenêtre principale cachée créée avec succès");
                return hiddenWindow;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"CreateHiddenMainWindow: Erreur lors de la création de la fenêtre principale cachée: {ex.Message}", ex);
                throw; // Propager l'exception pour permettre à l'appelant de la gérer
            }
        }
    }
}