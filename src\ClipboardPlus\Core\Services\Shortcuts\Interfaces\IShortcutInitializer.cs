using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Shortcuts.Models;

namespace ClipboardPlus.Core.Services.Shortcuts.Interfaces
{
    /// <summary>
    /// Interface pour l'initialisation des raccourcis globaux.
    /// Encapsule toute la logique d'initialisation complexe.
    /// </summary>
    public interface IShortcutInitializer : IDisposable
    {
        /// <summary>
        /// Initialise les raccourcis globaux avec la configuration spécifiée.
        /// </summary>
        /// <param name="context">Le contexte d'initialisation.</param>
        /// <returns>Le résultat de l'initialisation.</returns>
        Task<InitializationResult> InitializeAsync(InitializationContext context);

        /// <summary>
        /// Événement déclenché lorsqu'un raccourci est activé.
        /// </summary>
        event EventHandler<ShortcutActivatedEventArgs>? ShortcutActivated;
    }
}
