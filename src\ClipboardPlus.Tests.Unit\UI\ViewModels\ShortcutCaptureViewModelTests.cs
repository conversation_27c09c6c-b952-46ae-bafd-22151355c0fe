using System;
using System.Threading.Tasks;
using System.Windows.Input;
using NUnit.Framework;
using Moq;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.UI.ViewModels
{
    [TestFixture]
    public class ShortcutCaptureViewModelTests
    {
        private Mock<IGlobalShortcutService> _mockGlobalShortcutService = null!;
        private Mock<IUserNotificationService> _mockUserNotificationService = null!;
        private ShortcutCaptureViewModel _viewModel = null!;

        [SetUp]
        public void SetUp()
        {
            _mockGlobalShortcutService = new Mock<IGlobalShortcutService>();
            _mockUserNotificationService = new Mock<IUserNotificationService>();
            
            _viewModel = new ShortcutCaptureViewModel(
                _mockGlobalShortcutService.Object,
                _mockUserNotificationService.Object,
                "Ctrl+Alt+V");
        }

        [Test]
        public void Constructor_WithValidParameters_InitializesCorrectly()
        {
            // Assert
            Assert.That(_viewModel.CurrentShortcut, Is.EqualTo("Ctrl+Alt+V"));
            Assert.That(_viewModel.NewShortcut, Is.Null);
            Assert.That(_viewModel.IsValid, Is.False);
            Assert.That(_viewModel.IsCapturing, Is.False);
            Assert.That(_viewModel.StatusMessage, Is.Not.Null.And.Not.Empty);
        }

        [Test]
        public void Constructor_WithNullGlobalShortcutService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new ShortcutCaptureViewModel(null!, _mockUserNotificationService.Object));
        }

        [Test]
        public void Constructor_WithNullUserNotificationService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new ShortcutCaptureViewModel(_mockGlobalShortcutService.Object, null!));
        }

        [Test]
        public void InitializeAsync_CompletesSuccessfully()
        {
            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _viewModel.InitializeAsync());
        }

        [Test]
        public void Commands_AreNotNull()
        {
            // Assert
            Assert.That(_viewModel.ResetCommand, Is.Not.Null);
            Assert.That(_viewModel.CancelCommand, Is.Not.Null);
            Assert.That(_viewModel.ValidateCommand, Is.Not.Null);
        }

        [Test]
        public void ValidateCommand_WhenIsValidIsFalse_CannotExecute()
        {
            // Arrange
            _viewModel.IsValid = false;

            // Act & Assert
            Assert.That(_viewModel.ValidateCommand.CanExecute(null), Is.False);
        }

        [Test]
        public void OnShortcutCaptured_WithValidShortcut_SetsPropertiesCorrectly()
        {
            // Arrange
            var keyCombination = new KeyCombination(ModifierKeys.Control | ModifierKeys.Shift, Key.C);
            _mockGlobalShortcutService.Setup(s => s.IsValidShortcut(keyCombination)).Returns(true);
            _mockGlobalShortcutService.Setup(s => s.IsShortcutAlreadyRegistered(keyCombination)).Returns(false);

            // Act
            _viewModel.OnShortcutCaptured(keyCombination);

            // Assert
            Assert.That(_viewModel.NewShortcut, Is.EqualTo(keyCombination));
            Assert.That(_viewModel.IsCapturing, Is.False);
            Assert.That(_viewModel.IsValid, Is.True);
            Assert.That(_viewModel.StatusMessage, Contains.Substring("✅"));
        }

        [Test]
        public void OnShortcutCaptured_WithInvalidShortcut_SetsIsValidToFalse()
        {
            // Arrange
            var keyCombination = new KeyCombination(ModifierKeys.None, Key.A);
            _mockGlobalShortcutService.Setup(s => s.IsValidShortcut(keyCombination)).Returns(false);

            // Act
            _viewModel.OnShortcutCaptured(keyCombination);

            // Assert
            Assert.That(_viewModel.IsValid, Is.False);
            Assert.That(_viewModel.StatusMessage, Contains.Substring("❌"));
        }

        [Test]
        public void OnShortcutCaptured_WithAlreadyRegisteredShortcut_SetsIsValidToFalse()
        {
            // Arrange
            var keyCombination = new KeyCombination(ModifierKeys.Control | ModifierKeys.Shift, Key.C);
            _mockGlobalShortcutService.Setup(s => s.IsValidShortcut(keyCombination)).Returns(true);
            _mockGlobalShortcutService.Setup(s => s.IsShortcutAlreadyRegistered(keyCombination)).Returns(true);

            // Act
            _viewModel.OnShortcutCaptured(keyCombination);

            // Assert
            Assert.That(_viewModel.IsValid, Is.False);
            Assert.That(_viewModel.StatusMessage, Contains.Substring("⚠️"));
        }

        [Test]
        public void OnShortcutCaptured_WithSameShortcutAsCurrent_SetsIsValidToTrue()
        {
            // Arrange
            var keyCombination = KeyCombination.Parse("Ctrl+Alt+V");
            _mockGlobalShortcutService.Setup(s => s.IsValidShortcut(keyCombination)).Returns(true);
            _mockGlobalShortcutService.Setup(s => s.IsShortcutAlreadyRegistered(keyCombination)).Returns(false);

            // Act
            _viewModel.OnShortcutCaptured(keyCombination);

            // Assert
            Assert.That(_viewModel.IsValid, Is.True);
            Assert.That(_viewModel.StatusMessage, Contains.Substring("ℹ️"));
        }

        [Test]
        public void ResetCommand_Execute_ResetsProperties()
        {
            // Arrange
            _viewModel.NewShortcut = new KeyCombination(ModifierKeys.Control, Key.A);
            _viewModel.IsValid = true;
            _viewModel.IsCapturing = true;

            // Act
            _viewModel.ResetCommand.Execute(null);

            // Assert
            Assert.That(_viewModel.NewShortcut, Is.Null);
            Assert.That(_viewModel.IsValid, Is.False);
            Assert.That(_viewModel.IsCapturing, Is.False);
            Assert.That(_viewModel.StatusMessage, Contains.Substring("Cliquez dans la zone"));
        }

        [Test]
        public void CancelCommand_Execute_RaisesCancelRequestedEvent()
        {
            // Arrange
            bool eventRaised = false;
            _viewModel.CancelRequested += (s, e) => eventRaised = true;

            // Act
            _viewModel.CancelCommand.Execute(null);

            // Assert
            Assert.That(eventRaised, Is.True);
        }

        [Test]
        public void ValidateCommand_Execute_WhenValid_RaisesShortcutValidatedEvent()
        {
            // Arrange
            string? validatedShortcut = null;
            _viewModel.ShortcutValidated += (s, shortcut) => validatedShortcut = shortcut;
            
            var keyCombination = new KeyCombination(ModifierKeys.Control | ModifierKeys.Shift, Key.C);
            _viewModel.NewShortcut = keyCombination;
            _viewModel.IsValid = true;

            // Act
            _viewModel.ValidateCommand.Execute(null);

            // Assert
            Assert.That(validatedShortcut, Is.EqualTo(keyCombination.ToString()));
        }

        [Test]
        public void OnIsValidChanged_UpdatesValidateCommandCanExecute()
        {
            // Arrange
            bool canExecuteBefore = _viewModel.ValidateCommand.CanExecute(null);

            // Act
            _viewModel.IsValid = true;
            bool canExecuteAfter = _viewModel.ValidateCommand.CanExecute(null);

            // Assert
            Assert.That(canExecuteBefore, Is.False);
            Assert.That(canExecuteAfter, Is.True);
        }
    }
}
