using System;
using System.Windows;
using ClipboardPlus.Core.Services;
using WpfApplication = System.Windows.Application;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation du service de gestion du threading UI pour les changements d'historique.
    /// 
    /// Ce service gère la complexité de la redirection des appels vers le thread UI
    /// de manière sûre et testable.
    /// </summary>
    public class HistoryChangeThreadingService : IHistoryChangeThreadingService
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du service de threading.
        /// </summary>
        /// <param name="loggingService">Service de logging pour tracer les opérations de threading</param>
        public HistoryChangeThreadingService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Assure que l'exécution se fait sur le thread UI.
        /// 
        /// Cette méthode reproduit la logique originale de ClipboardHistoryManager_HistoryChanged
        /// pour la gestion du threading UI.
        /// </summary>
        /// <param name="action">Action à exécuter sur le thread UI</param>
        /// <param name="eventId">Identifiant de l'événement pour le logging</param>
        /// <returns>True si l'action a été exécutée directement, False si elle a été redirigée</returns>
        public bool EnsureUIThread(Action action, string eventId)
        {
            if (action == null)
            {
                _loggingService.LogWarning($"[HistoryChangeThreadingService] [{eventId}] Action null fournie à EnsureUIThread");
                return false;
            }

            // Vérifier si nous sommes déjà sur le thread UI
            if (IsOnUIThread())
            {
                _loggingService.LogInfo($"[HistoryChangeThreadingService] [{eventId}] Déjà sur le thread UI, exécution directe");
                try
                {
                    action();
                    return true;
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"[HistoryChangeThreadingService] [{eventId}] Erreur lors de l'exécution directe: {ex.Message}", ex);
                    throw;
                }
            }

            // Rediriger vers le thread UI
            _loggingService.LogInfo($"[HistoryChangeThreadingService] [{eventId}] Redirection vers le thread UI via Dispatcher.Invoke");
            
            try
            {
                // Vérifier si l'application WPF est disponible (peut être null dans les tests)
                if (WpfApplication.Current?.Dispatcher != null)
                {
                    WpfApplication.Current.Dispatcher.Invoke(action);
                    _loggingService.LogInfo($"[HistoryChangeThreadingService] [{eventId}] Redirection réussie vers le thread UI");
                    return false; // Action redirigée, pas exécutée directement
                }
                else
                {
                    // Dans un environnement de test ou sans WPF, exécuter directement
                    _loggingService.LogInfo($"[HistoryChangeThreadingService] [{eventId}] WpfApplication.Current non disponible, exécution directe");
                    action();
                    return true; // Action exécutée directement
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"[HistoryChangeThreadingService] [{eventId}] Erreur lors de la redirection vers le thread UI: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Vérifie si le thread actuel est le thread UI.
        /// 
        /// Cette méthode reproduit la logique de vérification originale.
        /// </summary>
        /// <returns>True si on est sur le thread UI, False sinon</returns>
        public bool IsOnUIThread()
        {
            try
            {
                // Reproduire la logique originale de vérification
                return WpfApplication.Current?.Dispatcher != null && 
                       WpfApplication.Current.Dispatcher.CheckAccess();
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"[HistoryChangeThreadingService] Erreur lors de la vérification du thread UI: {ex.Message}");
                // En cas d'erreur, assumer qu'on n'est pas sur le thread UI pour la sécurité
                return false;
            }
        }
    }
}
