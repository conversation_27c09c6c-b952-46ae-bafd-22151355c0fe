using System;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour les handlers de contenu spécialisés.
    /// Chaque handler est responsable du traitement d'un type spécifique de données.
    /// Respecte le principe ouvert/fermé (OCP) - ouvert à l'extension, fermé à la modification.
    /// </summary>
    public interface IContentHandler
    {
        /// <summary>
        /// Le type de données que ce handler peut traiter.
        /// </summary>
        ClipboardDataType SupportedDataType { get; }

        /// <summary>
        /// Traite le contenu d'un élément du presse-papiers.
        /// </summary>
        /// <param name="item">L'élément à traiter</param>
        /// <returns>Le contenu formaté pour l'affichage</returns>
        /// <exception cref="ArgumentNullException">Levée si item est null</exception>
        /// <exception cref="ArgumentException">Levée si le type de données n'est pas supporté</exception>
        object HandleContent(ClipboardItem item);

        /// <summary>
        /// Vérifie si ce handler peut traiter l'élément donné.
        /// </summary>
        /// <param name="item">L'élément à vérifier</param>
        /// <returns>True si le handler peut traiter cet élément, false sinon</returns>
        bool CanHandle(ClipboardItem item);

        /// <summary>
        /// Obtient le message par défaut à afficher quand le contenu n'est pas disponible.
        /// </summary>
        /// <returns>Message par défaut pour ce type de contenu</returns>
        string GetDefaultUnavailableMessage();
    }
}
