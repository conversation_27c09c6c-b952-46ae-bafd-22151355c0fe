using System;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.UI.ViewModels
{
    /// <summary>
    /// ViewModel pour la fenêtre de capture de raccourci clavier.
    /// </summary>
    public partial class ShortcutCaptureViewModel : ViewModelBase
    {
        private readonly IGlobalShortcutService _globalShortcutService;
        private readonly IUserNotificationService _userNotificationService;

        /// <summary>
        /// Raccourci actuel (avant modification).
        /// </summary>
        [ObservableProperty]
        private string _currentShortcut;

        /// <summary>
        /// Nouveau raccourci capturé.
        /// </summary>
        [ObservableProperty]
        private KeyCombination? _newShortcut;

        /// <summary>
        /// Message de statut affiché à l'utilisateur.
        /// </summary>
        [ObservableProperty]
        private string _statusMessage;

        /// <summary>
        /// Indique si le raccourci capturé est valide.
        /// </summary>
        [ObservableProperty]
        private bool _isValid;

        /// <summary>
        /// Indique si une capture est en cours.
        /// </summary>
        [ObservableProperty]
        private bool _isCapturing;

        /// <summary>
        /// Commande pour réinitialiser la capture.
        /// </summary>
        public IRelayCommand ResetCommand { get; }

        /// <summary>
        /// Commande pour annuler la capture.
        /// </summary>
        public IRelayCommand CancelCommand { get; }

        /// <summary>
        /// Commande pour valider le raccourci capturé.
        /// </summary>
        public IRelayCommand ValidateCommand { get; }

        /// <summary>
        /// Événement déclenché lorsque l'utilisateur annule la capture.
        /// </summary>
        public event EventHandler? CancelRequested;

        /// <summary>
        /// Événement déclenché lorsque l'utilisateur valide le raccourci.
        /// </summary>
        public event EventHandler<string>? ShortcutValidated;

        /// <summary>
        /// Initialise une nouvelle instance de ShortcutCaptureViewModel.
        /// </summary>
        /// <param name="globalShortcutService">Service de gestion des raccourcis globaux.</param>
        /// <param name="userNotificationService">Service de notification utilisateur.</param>
        /// <param name="currentShortcut">Raccourci actuel à modifier.</param>
        public ShortcutCaptureViewModel(
            IGlobalShortcutService globalShortcutService,
            IUserNotificationService userNotificationService,
            string currentShortcut = "")
        {
            _globalShortcutService = globalShortcutService ?? throw new ArgumentNullException(nameof(globalShortcutService));
            _userNotificationService = userNotificationService ?? throw new ArgumentNullException(nameof(userNotificationService));
            
            _currentShortcut = currentShortcut;
            _statusMessage = "Cliquez dans la zone de capture et appuyez sur la nouvelle combinaison...";
            _isValid = false;
            _isCapturing = false;

            // Initialiser les commandes
            ResetCommand = new RelayCommand(ResetCapture);
            CancelCommand = new RelayCommand(CancelCapture);
            ValidateCommand = new RelayCommand(ValidateShortcut, () => IsValid);
        }

        /// <summary>
        /// Initialise le ViewModel de manière asynchrone.
        /// </summary>
        public Task InitializeAsync()
        {
            // Aucune initialisation asynchrone nécessaire pour l'instant
            return Task.CompletedTask;
        }

        /// <summary>
        /// Méthode appelée lorsqu'un nouveau raccourci est capturé.
        /// </summary>
        /// <param name="keyCombination">La combinaison de touches capturée.</param>
        public void OnShortcutCaptured(KeyCombination keyCombination)
        {
            try
            {
                NewShortcut = keyCombination;
                IsCapturing = false;

                // Valider le raccourci avec feedback immédiat
                ValidateShortcut(keyCombination);

                // Déclencher une notification de changement pour l'UI
                OnPropertyChanged(nameof(NewShortcut));
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ Erreur lors de la capture: {ex.Message}";
                IsValid = false;
                IsCapturing = false;
            }
        }

        /// <summary>
        /// Valide un raccourci clavier avec feedback visuel amélioré.
        /// </summary>
        /// <param name="keyCombination">La combinaison de touches à valider.</param>
        private void ValidateShortcut(KeyCombination keyCombination)
        {
            try
            {
                if (keyCombination == null)
                {
                    StatusMessage = "ℹ️ Aucun raccourci capturé.";
                    IsValid = false;
                    return;
                }

                // Vérifier si le raccourci est valide
                if (!_globalShortcutService.IsValidShortcut(keyCombination))
                {
                    StatusMessage = "❌ Raccourci invalide. Utilisez au moins 2 modificateurs (Ctrl, Alt, Shift, Win) + une touche.";
                    IsValid = false;
                    return;
                }

                // Vérifier si le raccourci est déjà utilisé
                if (_globalShortcutService.IsShortcutAlreadyRegistered(keyCombination))
                {
                    StatusMessage = "⚠️ Ce raccourci est déjà utilisé par une autre application. Choisissez une autre combinaison.";
                    IsValid = false;
                    return;
                }

                // Vérifier si c'est le même raccourci qu'actuellement
                if (keyCombination.ToString() == CurrentShortcut)
                {
                    StatusMessage = "ℹ️ C'est le même raccourci qu'actuellement. Vous pouvez valider pour confirmer.";
                    IsValid = true;
                    return;
                }

                // Raccourci valide et nouveau
                StatusMessage = $"✅ Parfait ! Le raccourci '{keyCombination}' est valide et disponible.";
                IsValid = true;
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ Erreur lors de la validation: {ex.Message}";
                IsValid = false;
            }
        }

        /// <summary>
        /// Réinitialise la capture avec feedback visuel.
        /// </summary>
        private void ResetCapture()
        {
            NewShortcut = null;
            StatusMessage = "🔄 Zone réinitialisée. Cliquez dans la zone de capture et appuyez sur la nouvelle combinaison...";
            IsValid = false;
            IsCapturing = false;

            // Déclencher les notifications de changement pour l'UI
            OnPropertyChanged(nameof(NewShortcut));
        }

        /// <summary>
        /// Annule la capture.
        /// </summary>
        private void CancelCapture()
        {
            CancelRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Valide le raccourci capturé.
        /// </summary>
        private void ValidateShortcut()
        {
            if (IsValid && NewShortcut != null)
            {
                ShortcutValidated?.Invoke(this, NewShortcut.ToString());
            }
        }

        /// <summary>
        /// Met à jour la disponibilité de la commande de validation.
        /// </summary>
        partial void OnIsValidChanged(bool value)
        {
            ValidateCommand.NotifyCanExecuteChanged();
        }
    }
}
