using System;
using System.Threading.Tasks;
using ClipboardPlus.Modules.Core.Events;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Modules.Core.Events
{
    /// <summary>
    /// Tests unitaires pour le EventBus.
    /// 
    /// Ces tests valident le comportement du bus d'événements,
    /// incluant la publication, l'abonnement et la gestion des weak references.
    /// </summary>
    [TestFixture]
    public class EventBusTests
    {
        private EventBus _eventBus;

        [SetUp]
        public void SetUp()
        {
            _eventBus = new EventBus();
        }

        [TearDown]
        public void TearDown()
        {
            _eventBus?.Dispose();
        }

        #region Tests d'abonnement

        [Test]
        public void Subscribe_WithSyncHandler_ShouldReturnSubscription()
        {
            // Arrange
            var handlerCalled = false;
            Action<TestEvent> handler = e => handlerCalled = true;

            // Act
            var subscription = _eventBus.Subscribe<TestEvent>(handler);

            // Assert
            Assert.That(subscription, Is.Not.Null);
            Assert.That(subscription.IsActive, Is.True);
            Assert.That(subscription.EventType, Is.EqualTo(typeof(TestEvent)));
        }

        [Test]
        public void Subscribe_WithAsyncHandler_ShouldReturnSubscription()
        {
            // Arrange
            var handlerCalled = false;
            Func<TestEvent, Task> handler = e => 
            {
                handlerCalled = true;
                return Task.CompletedTask;
            };

            // Act
            var subscription = _eventBus.Subscribe<TestEvent>(handler);

            // Assert
            Assert.That(subscription, Is.Not.Null);
            Assert.That(subscription.IsActive, Is.True);
            Assert.That(subscription.EventType, Is.EqualTo(typeof(TestEvent)));
        }

        [Test]
        public void Subscribe_WithNullHandler_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                _eventBus.Subscribe<TestEvent>((Action<TestEvent>)null));
            
            Assert.Throws<ArgumentNullException>(() => 
                _eventBus.Subscribe<TestEvent>((Func<TestEvent, Task>)null));
        }

        #endregion

        #region Tests de publication synchrone

        [Test]
        public void Publish_WithSyncHandler_ShouldCallHandler()
        {
            // Arrange
            var handlerCalled = false;
            var receivedEvent = (TestEvent)null;

            // Garder une référence forte au handler pour éviter la GC
            Action<TestEvent> handler = e =>
            {
                handlerCalled = true;
                receivedEvent = e;
            };

            _eventBus.Subscribe<TestEvent>(handler, keepSubscriberReferenceAlive: true);

            var testEvent = new TestEvent { Message = "Test message" };

            // Act
            _eventBus.Publish(testEvent);

            // Assert
            Assert.That(handlerCalled, Is.True);
            Assert.That(receivedEvent, Is.EqualTo(testEvent));
            Assert.That(receivedEvent.Message, Is.EqualTo("Test message"));
        }

        [Test]
        public void Publish_WithMultipleHandlers_ShouldCallAllHandlers()
        {
            // Arrange
            var handler1Called = false;
            var handler2Called = false;

            // Garder des références fortes aux handlers
            Action<TestEvent> handler1 = e => handler1Called = true;
            Action<TestEvent> handler2 = e => handler2Called = true;

            _eventBus.Subscribe<TestEvent>(handler1, keepSubscriberReferenceAlive: true);
            _eventBus.Subscribe<TestEvent>(handler2, keepSubscriberReferenceAlive: true);

            var testEvent = new TestEvent { Message = "Test" };

            // Act
            _eventBus.Publish(testEvent);

            // Assert
            Assert.That(handler1Called, Is.True);
            Assert.That(handler2Called, Is.True);
        }

        [Test]
        public void Publish_WithNullEvent_ShouldNotThrow()
        {
            // Arrange
            var handlerCalled = false;
            _eventBus.Subscribe<TestEvent>(e => handlerCalled = true);

            // Act & Assert
            Assert.DoesNotThrow(() => _eventBus.Publish<TestEvent>(null));
            Assert.That(handlerCalled, Is.False);
        }

        #endregion

        #region Tests de publication asynchrone

        [Test]
        public async Task PublishAsync_WithAsyncHandler_ShouldCallHandler()
        {
            // Arrange
            var handlerCalled = false;
            var receivedEvent = (TestEvent)null;

            // Garder une référence forte au handler
            Func<TestEvent, Task> handler = async e =>
            {
                await Task.Delay(10); // Simule un traitement async
                handlerCalled = true;
                receivedEvent = e;
            };

            _eventBus.Subscribe<TestEvent>(handler, keepSubscriberReferenceAlive: true);

            var testEvent = new TestEvent { Message = "Async test" };

            // Act
            await _eventBus.PublishAsync(testEvent);

            // Assert
            Assert.That(handlerCalled, Is.True);
            Assert.That(receivedEvent, Is.EqualTo(testEvent));
        }

        [Test]
        public async Task PublishAsync_WithMixedHandlers_ShouldCallAllHandlers()
        {
            // Arrange
            var syncHandlerCalled = false;
            var asyncHandlerCalled = false;

            // Garder des références fortes aux handlers
            Action<TestEvent> syncHandler = e => syncHandlerCalled = true;
            Func<TestEvent, Task> asyncHandler = async e =>
            {
                await Task.Delay(10);
                asyncHandlerCalled = true;
            };

            _eventBus.Subscribe<TestEvent>(syncHandler, keepSubscriberReferenceAlive: true);
            _eventBus.Subscribe<TestEvent>(asyncHandler, keepSubscriberReferenceAlive: true);

            var testEvent = new TestEvent { Message = "Mixed test" };

            // Act
            await _eventBus.PublishAsync(testEvent);

            // Assert
            Assert.That(syncHandlerCalled, Is.True);
            Assert.That(asyncHandlerCalled, Is.True);
        }

        [Test]
        public async Task PublishAsync_WithNullEvent_ShouldNotThrow()
        {
            // Arrange
            var handlerCalled = false;
            _eventBus.Subscribe<TestEvent>(async e =>
            {
                await Task.Delay(10);
                handlerCalled = true;
            });

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _eventBus.PublishAsync<TestEvent>(null));
            Assert.That(handlerCalled, Is.False);
        }

        #endregion

        #region Tests de désabonnement

        [Test]
        public void Unsubscribe_WithValidSubscription_ShouldStopCallingHandler()
        {
            // Arrange
            var handlerCalled = false;
            var subscription = _eventBus.Subscribe<TestEvent>(e => handlerCalled = true);

            // Act
            _eventBus.Unsubscribe(subscription);
            _eventBus.Publish(new TestEvent { Message = "Test" });

            // Assert
            Assert.That(handlerCalled, Is.False);
            Assert.That(subscription.IsActive, Is.False);
        }

        [Test]
        public void Unsubscribe_WithNullSubscription_ShouldNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _eventBus.Unsubscribe(null));
        }

        [Test]
        public void UnsubscribeAll_WithValidSubscriber_ShouldStopCallingHandlers()
        {
            // Arrange
            var subscriber = new object();
            var handlerCalled = false;
            
            // Note: Pour tester UnsubscribeAll, nous devons simuler un abonnement avec un subscriber spécifique
            // Dans la vraie implémentation, cela nécessiterait une modification de l'API
            _eventBus.Subscribe<TestEvent>(e => handlerCalled = true);

            // Act
            _eventBus.UnsubscribeAll(subscriber);
            _eventBus.Publish(new TestEvent { Message = "Test" });

            // Assert
            // Note: Ce test est limité car l'implémentation actuelle ne track pas le subscriber
            // Dans une vraie implémentation, il faudrait modifier l'API pour passer le subscriber
        }

        #endregion

        #region Tests de nettoyage

        [Test]
        public void Clear_ShouldRemoveAllSubscriptions()
        {
            // Arrange
            var handler1Called = false;
            var handler2Called = false;
            
            _eventBus.Subscribe<TestEvent>(e => handler1Called = true);
            _eventBus.Subscribe<TestEvent>(e => handler2Called = true);

            // Act
            _eventBus.Clear();
            _eventBus.Publish(new TestEvent { Message = "Test" });

            // Assert
            Assert.That(handler1Called, Is.False);
            Assert.That(handler2Called, Is.False);
        }

        #endregion

        #region Tests de gestion des erreurs

        [Test]
        public void Publish_WithHandlerThatThrows_ShouldNotStopOtherHandlers()
        {
            // Arrange
            var handler1Called = false;
            var handler2Called = false;

            // Garder des références fortes aux handlers
            Action<TestEvent> throwingHandler = e => throw new InvalidOperationException("Test exception");
            Action<TestEvent> handler1 = e => handler1Called = true;
            Action<TestEvent> handler2 = e => handler2Called = true;

            _eventBus.Subscribe<TestEvent>(throwingHandler, keepSubscriberReferenceAlive: true);
            _eventBus.Subscribe<TestEvent>(handler1, keepSubscriberReferenceAlive: true);
            _eventBus.Subscribe<TestEvent>(handler2, keepSubscriberReferenceAlive: true);

            // Act & Assert
            Assert.DoesNotThrow(() => _eventBus.Publish(new TestEvent { Message = "Test" }));
            Assert.That(handler1Called, Is.True);
            Assert.That(handler2Called, Is.True);
        }

        [Test]
        public async Task PublishAsync_WithHandlerThatThrows_ShouldNotStopOtherHandlers()
        {
            // Arrange
            var handler1Called = false;
            var handler2Called = false;

            // Garder des références fortes aux handlers
            Func<TestEvent, Task> throwingHandler = async e =>
            {
                await Task.Delay(10);
                throw new InvalidOperationException("Async test exception");
            };
            Func<TestEvent, Task> asyncHandler1 = async e =>
            {
                await Task.Delay(10);
                handler1Called = true;
            };
            Action<TestEvent> syncHandler2 = e => handler2Called = true;

            _eventBus.Subscribe<TestEvent>(throwingHandler, keepSubscriberReferenceAlive: true);
            _eventBus.Subscribe<TestEvent>(asyncHandler1, keepSubscriberReferenceAlive: true);
            _eventBus.Subscribe<TestEvent>(syncHandler2, keepSubscriberReferenceAlive: true);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _eventBus.PublishAsync(new TestEvent { Message = "Test" }));
            Assert.That(handler1Called, Is.True);
            Assert.That(handler2Called, Is.True);
        }

        #endregion

        #region Tests de dispose

        [Test]
        public void Dispose_ShouldClearAllSubscriptions()
        {
            // Arrange
            var handlerCalled = false;
            _eventBus.Subscribe<TestEvent>(e => handlerCalled = true);

            // Act
            _eventBus.Dispose();

            // Assert
            Assert.Throws<ObjectDisposedException>(() => 
                _eventBus.Publish(new TestEvent { Message = "Test" }));
        }

        [Test]
        public void Dispose_CalledMultipleTimes_ShouldNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _eventBus.Dispose();
                _eventBus.Dispose();
            });
        }

        #endregion

        #region Classes de test

        public class TestEvent : IModuleEvent
        {
            public Guid EventId { get; } = Guid.NewGuid();
            public DateTime Timestamp { get; } = DateTime.UtcNow;
            public string SourceModule { get; } = "TestModule";
            public string? TargetModule { get; } = null;
            public string Message { get; set; } = string.Empty;
        }

        #endregion
    }
}
