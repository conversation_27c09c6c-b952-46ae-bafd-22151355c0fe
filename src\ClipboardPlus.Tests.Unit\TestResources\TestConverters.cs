using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.TestResources
{
    /// <summary>
    /// Version de test du convertisseur BooleanToVisibilityConverter
    /// </summary>
    public class TestBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isInverted = parameter as string == "Invert";
            bool boolValue = value is bool val && val;

            if (isInverted)
                boolValue = !boolValue;

            return boolValue ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isInverted = parameter as string == "Invert";
            bool result = value is Visibility visibility && visibility == Visibility.Visible;

            if (isInverted)
                result = !result;

            return result;
        }
    }

    /// <summary>
    /// Version de test du convertisseur BooleanToColorConverter
    /// </summary>
    public class TestBooleanToColorConverter : IValueConverter
    {
        public string TrueColor { get; set; } = "#4CAF50"; // Vert par défaut
        public string FalseColor { get; set; } = "#D3D3D3"; // Gris clair par défaut

        private SolidColorBrush _trueBrush = new SolidColorBrush(Colors.Green);
        private SolidColorBrush _falseBrush = new SolidColorBrush(Colors.LightGray);

        public TestBooleanToColorConverter()
        {
            UpdateBrushes();
        }

        private void UpdateBrushes()
        {
            try
            {
                var colorConverter = new BrushConverter();
                var trueBrushResult = colorConverter.ConvertFrom(TrueColor);
                var falseBrushResult = colorConverter.ConvertFrom(FalseColor);

                if (trueBrushResult is SolidColorBrush trueBrush)
                    _trueBrush = trueBrush;

                if (falseBrushResult is SolidColorBrush falseBrush)
                    _falseBrush = falseBrush;
            }
            catch
            {
                _trueBrush = new SolidColorBrush(Colors.Green);
                _falseBrush = new SolidColorBrush(Colors.LightGray);
            }
        }

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            UpdateBrushes(); // Mettre à jour en cas de changement
            bool boolValue = value is bool val && val;
            return boolValue ? _trueBrush : _falseBrush;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Version de test du convertisseur DataTypeToIconConverter
    /// </summary>
    public class TestDataTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not ClipboardDataType dataType)
            {
                return string.Empty;
            }

            return GetIconCharacter(dataType);
        }

        private string GetIconCharacter(ClipboardDataType dataType)
        {
            return dataType switch
            {
                ClipboardDataType.Text => "\uE8C4",      // Texte
                ClipboardDataType.Rtf => "\uE8E5",       // Texte enrichi
                ClipboardDataType.Html => "\uE8CD",      // HTML
                ClipboardDataType.Image => "\uEB9F",     // Image
                ClipboardDataType.FilePath => "\uEC50",  // Fichiers
                _ => "\uEA92",                          // Par défaut/Inconnu
            };
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 