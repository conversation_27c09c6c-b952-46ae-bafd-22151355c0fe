using System.Collections.Generic;

namespace ClipboardPlus.Core.Services.Logging
{
    /// <summary>
    /// Interface pour les destinations de log (Console, Debug, Fichier).
    /// Utilise le pattern Strategy pour permettre l'extensibilité.
    /// </summary>
    public interface ILogTarget
    {
        /// <summary>
        /// Écrit une entrée de log vers cette destination.
        /// </summary>
        /// <param name="entry">L'entrée de log à écrire</param>
        void Write(LogEntry entry);
        
        /// <summary>
        /// Force l'écriture immédiate des données en attente (optionnel).
        /// Certaines cibles comme le fichier peuvent avoir un buffer.
        /// </summary>
        void Flush();
        
        /// <summary>
        /// Écrit plusieurs entrées de log en une seule opération (optimisation).
        /// Par défaut, appelle Write() pour chaque entrée.
        /// </summary>
        /// <param name="entries">Les entrées de log à écrire</param>
        void WriteBatch(IEnumerable<LogEntry> entries)
        {
            foreach (var entry in entries)
            {
                Write(entry);
            }
        }
    }
}
