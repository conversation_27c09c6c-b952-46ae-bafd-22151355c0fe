using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using ClipboardPlus.UI.Helpers;

namespace ClipboardPlus.UI.ViewModels
{
    /// <summary>
    /// Partie refactorisée du ClipboardHistoryViewModel pour la gestion des événements.
    ///
    /// Cette classe contient la nouvelle implémentation de ClipboardHistoryManager_HistoryChanged
    /// utilisant l'architecture modulaire avec l'orchestrateur.
    /// </summary>
    public partial class ClipboardHistoryViewModel
    {
        // Les champs _historyChangeOrchestrator et _featureFlagService sont maintenant déclarés dans le fichier principal

        /// <summary>
        /// Initialise l'orchestrateur de changements d'historique et le service de feature flags.
        /// Cette méthode doit être appelée après l'injection de dépendance.
        /// </summary>
        /// <param name="orchestrator">Orchestrateur à utiliser</param>
        /// <param name="featureFlagService">Service de feature flags</param>
        public void InitializeHistoryChangeOrchestrator(IHistoryChangeOrchestrator orchestrator, IFeatureFlagService featureFlagService)
        {
            _historyChangeOrchestrator = orchestrator ?? throw new ArgumentNullException(nameof(orchestrator));
            _featureFlagService = featureFlagService ?? throw new ArgumentNullException(nameof(featureFlagService));

            // Initialiser l'orchestrateur dans le synchronizer aussi
            _historyCollectionSynchronizer?.InitializeOrchestrator(orchestrator);

            _loggingService?.LogInfo("[ClipboardHistoryViewModel] Orchestrateur et FeatureFlagService initialisés");
        }

        /// <summary>
        /// Méthode principale de gestion des événements de changement d'historique.
        ///
        /// Cette méthode utilise exclusivement la nouvelle architecture refactorisée avec l'orchestrateur.
        /// </summary>
        private async void ClipboardHistoryManager_HistoryChanged_Progressive(object? sender, EventArgs e)
        {
            var eventId = Guid.NewGuid().ToString("N")[..8];
            var beforeCount = HistoryItems?.Count ?? 0;
            var beforeIds = HistoryItems?.Take(5).Select(i => i.Id).ToList() ?? new List<long>();

            _loggingService?.LogInfo($"🎯 [UI-EVENT-{eventId}] HistoryChanged_Progressive DÉBUT - Sender: {sender?.GetType().Name}, Count AVANT: {beforeCount}, IDs (5 premiers): [{string.Join(", ", beforeIds)}]");

            await ClipboardHistoryManager_HistoryChanged_Refactored(sender, e);

            var afterCount = HistoryItems?.Count ?? 0;
            var afterIds = HistoryItems?.Take(5).Select(i => i.Id).ToList() ?? new List<long>();
            _loggingService?.LogInfo($"🎯 [UI-EVENT-{eventId}] HistoryChanged_Progressive FIN - Count APRÈS: {afterCount}, IDs (5 premiers): [{string.Join(", ", afterIds)}]");
        }

        /// <summary>
        /// PHASE 4 : Gestion des événements via l'architecture modulaire pure (fallback supprimé).
        /// Utilise l'orchestrateur et les services spécialisés.
        /// </summary>
        private async Task ClipboardHistoryManager_HistoryChanged_Refactored(object? sender, EventArgs e)
        {
            var diagnosticId = Guid.NewGuid().ToString("N")[..8];
            var beforeCount = HistoryItems?.Count ?? 0;
            var beforeIds = HistoryItems?.Take(3).Select(i => i.Id).ToList() ?? new List<long>();

            _loggingService?.LogInfo($"🎯 [UI-EVENT-{diagnosticId}] HistoryChanged_Refactored DÉBUT - Sender: {sender?.GetType().Name} (HashCode: {sender?.GetHashCode()}), Count AVANT: {beforeCount}, IDs: [{string.Join(", ", beforeIds)}]");

            // L'orchestrateur est maintenant garanti d'être initialisé

            try
            {
                // Construire les arguments pour l'orchestrateur
                var args = new HistoryChangedEventArgs
                {
                    Context = new HistoryChangeContext
                    {
                        PreventReaction = _preventHistoryChangedReaction,
                        IsUpdatingItem = false,
                        IsReorderingItems = _isReorderingItems,
                        HistoryManager = _clipboardHistoryManager,
                        IsOperationInProgress = IsOperationInProgress,
                        IsItemPasteInProgress = _isItemPasteInProgress,
                        IsInTestEnvironment = IsInTestEnvironment()
                    },
                    SyncContext = new HistorySynchronizationContext
                    {
                        UIItems = HistoryItems,
                        ManagerItems = _clipboardHistoryManager?.HistoryItems,
                        Synchronizer = _historyCollectionSynchronizer,
                        EventId = string.Empty // Sera généré par l'orchestrateur
                    },
                    LoadHistoryAction = async () =>
                    {
                        // Charger silencieusement sans déclencher HistoryChanged
                        await _clipboardHistoryManager!.LoadHistorySilentlyAsync();
                        // Puis synchroniser l'UI directement sans passer par l'orchestrateur
                        _historyCollectionSynchronizer.SynchronizeUIDirectly("reload_after_silent_load");
                    },
                    ErrorHandler = (ex, eventId) => ErrorMessageHelper.ShowError(
                        "Une erreur s'est produite lors du traitement des changements dans l'historique.",
                        "ClipboardPlus - Erreur",
                        ex,
                        $"HistoryChanged [{eventId}]",
                        this)
                };

                // Déléguer le traitement à l'orchestrateur
                var result = await _historyChangeOrchestrator.HandleHistoryChangedAsync(args);

                // Logging du résultat pour diagnostic
                var afterCount = HistoryItems?.Count ?? 0;
                var afterIds = HistoryItems?.Take(3).Select(i => i.Id).ToList() ?? new List<long>();

                if (result.Success)
                {
                    _loggingService?.LogInfo($"🎯 [UI-EVENT-{diagnosticId}] HistoryChanged_Refactored SUCCÈS - {result.Message}, Count APRÈS: {afterCount}, IDs: [{string.Join(", ", afterIds)}]");
                }
                else
                {
                    _loggingService?.LogError($"🎯 [UI-EVENT-{diagnosticId}] HistoryChanged_Refactored ÉCHEC - {result.Message}, Count APRÈS: {afterCount}");
                }

                // Diagnostic de suppression si disponible
                _deletionDiagnostic?.LogCollectionState(this, $"Fin HistoryChanged_Refactored [{result.EventId}]");
            }
            catch (Exception ex)
            {
                // Gestion d'erreur de dernier recours
                _loggingService?.LogError($"ClipboardHistoryManager_HistoryChanged_Refactored: Exception critique: {ex.Message}", ex);

                // Afficher l'erreur à l'utilisateur
                ErrorMessageHelper.ShowError(
                    "Une erreur critique s'est produite lors du traitement des changements dans l'historique.",
                    "ClipboardPlus - Erreur Critique",
                    ex,
                    "HistoryChanged_Refactored",
                    this);
            }
        }

        /// <summary>
        /// Méthode de transition pour basculer vers la version refactorisée.
        ///
        /// Cette méthode permet de tester la nouvelle implémentation.
        /// </summary>
        public async Task TestRefactoredHistoryChangedAsync()
        {
            _loggingService?.LogInfo("TestRefactoredHistoryChangedAsync: Test de la version refactorisée");

            try
            {
                // Simuler un événement de changement d'historique
                await ClipboardHistoryManager_HistoryChanged_Refactored(this, EventArgs.Empty);
                _loggingService?.LogInfo("TestRefactoredHistoryChangedAsync: Test réussi");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"TestRefactoredHistoryChangedAsync: Échec du test - {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Obtient les statistiques de l'orchestrateur de changements d'historique.
        /// </summary>
        /// <returns>Statistiques actuelles ou null si l'orchestrateur n'est pas initialisé</returns>
        public HistoryChangeStatistics? GetHistoryChangeStatistics()
        {
            return _historyChangeOrchestrator?.GetStatistics();
        }

        /// <summary>
        /// Remet à zéro les statistiques de l'orchestrateur.
        /// </summary>
        public void ResetHistoryChangeStatistics()
        {
            _historyChangeOrchestrator?.ResetStatistics();
            _loggingService?.LogInfo("ClipboardHistoryViewModel: Statistiques de changements d'historique remises à zéro");
        }

        // Architecture finalisée - monitoring intégré
    }
}
