using NUnit.Framework;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services.Configuration;

namespace ClipboardPlus.Tests.Unit.Services.Configuration
{
    [TestFixture]
    public class HostConfigurationTests
    {
        [Test]
        public void ConfigureServices_RegistersAllRequiredServices()
        {
            // Arrange
            var services = HostConfiguration.ConfigureServices();
            
            // Act
            int registeredServicesCount = 0;
            var requiredServices = new[]
            {
                typeof(ILoggingService),
                typeof(IClipboardHistoryManager),
                typeof(ISystemTrayService),
                typeof(IPersistenceService),
                typeof(ISettingsManager),
                typeof(IUserThemeManager),
                typeof(IGlobalShortcutService),
                typeof(ISystemStartupManager),
                typeof(IClipboardInteractionService),
                typeof(IClipboardProcessorService),
                typeof(IApplicationLifetimeManager),
                typeof(ISingleInstanceService)
            };

            foreach (var serviceType in requiredServices)
            {
                if (services.GetService(serviceType) != null)
                {
                    registeredServicesCount++;
                }
            }

            // Assert
            Assert.That(registeredServicesCount, Is.EqualTo(requiredServices.Length), $"Tous les services requis devraient être enregistrés. Trouvés: {registeredServicesCount}/{requiredServices.Length}");
        }
    }
} 