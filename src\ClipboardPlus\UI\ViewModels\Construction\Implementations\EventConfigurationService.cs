using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.UI.ViewModels.Construction.Interfaces;

namespace ClipboardPlus.UI.ViewModels.Construction.Implementations
{
    /// <summary>
    /// Implémentation du service de configuration des événements et messages.
    /// Responsabilité unique : Configuration de tous les événements et messages WeakReference selon le principe SRP.
    /// </summary>
    public class EventConfigurationService : IEventConfigurationService
    {


        /// <summary>
        /// Configure les événements du gestionnaire de visibilité.
        /// Cette méthode centralise la configuration des événements de visibilité
        /// avec gestion appropriée des services optionnels et du logging (lignes 269-273).
        ///
        /// IMPLÉMENTATION COMPLÈTE : Délègue à la méthode publique du ViewModel
        /// pour préserver la logique exacte de configuration des événements de visibilité.
        ///
        /// Logique extraite :
        /// - Abonnement à l'événement VisibilityChanged si le gestionnaire est disponible
        /// - Logging approprié selon la disponibilité du service
        /// - Gestion des services optionnels avec validation null
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel à configurer</param>
        /// <param name="visibilityManager">Gestionnaire de visibilité (peut être null)</param>
        /// <param name="loggingService">Service de logging (peut être null)</param>
        public void ConfigureVisibilityEvents(
            ClipboardHistoryViewModel viewModel,
            IVisibilityStateManager? visibilityManager,
            ILoggingService? loggingService)
        {
            if (viewModel == null) throw new ArgumentNullException(nameof(viewModel));

            // Déléguer à la méthode publique du ViewModel qui préserve la logique exacte
            // des lignes 269-273 du constructeur original
            viewModel.ConfigureVisibilityEventsPublic();
        }
    }
}
