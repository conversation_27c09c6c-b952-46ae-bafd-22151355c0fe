using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Implementations
{
    /// <summary>
    /// Service de notification d'événements liés à l'historique.
    /// Responsabilité unique : Notifier les changements d'historique.
    /// </summary>
    public class EventNotifier : IEventNotifier
    {
        private readonly ILoggingService? _loggingService;
        private readonly Action? _historyChangedCallback;

        /// <summary>
        /// Initialise une nouvelle instance du service de notification d'événements.
        /// </summary>
        /// <param name="historyChangedCallback">Callback à appeler lors des changements d'historique</param>
        /// <param name="loggingService">Service de logging pour traçabilité</param>
        public EventNotifier(Action? historyChangedCallback = null, ILoggingService? loggingService = null)
        {
            _historyChangedCallback = historyChangedCallback;
            _loggingService = loggingService;
        }

        /// <summary>
        /// Notifie qu'un changement a eu lieu dans l'historique.
        /// </summary>
        /// <param name="item">L'élément qui a causé le changement</param>
        /// <param name="changeType">Type de changement effectué</param>
        public void NotifyHistoryChanged(ClipboardItem item, HistoryChangeType changeType)
        {
            if (item == null)
            {
                throw new ArgumentNullException(nameof(item));
            }

            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] EventNotifier.NotifyHistoryChanged - Type: {changeType}, Item ID: {item.Id}");

            try
            {
                // Appeler le callback si disponible
                if (_historyChangedCallback != null)
                {
                    _loggingService?.LogInfo($"[{operationId}] EventNotifier.NotifyHistoryChanged - Déclenchement du callback pour {changeType}");
                    _historyChangedCallback.Invoke();
                    _loggingService?.LogInfo($"[{operationId}] EventNotifier.NotifyHistoryChanged - Callback exécuté avec succès");
                }
                else
                {
                    _loggingService?.LogInfo($"[{operationId}] EventNotifier.NotifyHistoryChanged - Aucun callback configuré");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] EventNotifier.NotifyHistoryChanged - Erreur lors de l'exécution du callback: {ex.Message}", ex);
                throw;
            }
        }
    }
}
