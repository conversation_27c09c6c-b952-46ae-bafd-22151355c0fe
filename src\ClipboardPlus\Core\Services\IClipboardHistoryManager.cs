using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour le gestionnaire d'historique du presse-papiers.
    /// </summary>
    public interface IClipboardHistoryManager
    {
        /// <summary>
        /// Liste des éléments dans l'historique du presse-papiers.
        /// </summary>
        List<ClipboardItem> HistoryItems { get; }

        /// <summary>
        /// Événement déclenché lorsque l'historique change (ajout, suppression, modification).
        /// </summary>
        event EventHandler HistoryChanged;

        /// <summary>
        /// Déclenche manuellement l'événement HistoryChanged.
        /// Utilisé par les services externes pour notifier des changements.
        /// </summary>
        void NotifyHistoryChanged();



        /// <summary>
        /// Ajoute un élément à l'historique du presse-papiers.
        /// </summary>
        /// <param name="item">L'élément à ajouter.</param>
        /// <returns>L'ID de l'élément ajouté.</returns>
        Task<long> AddItemAsync(ClipboardItem? item);

        /// <summary>
        /// Trouve un élément existant qui est un doublon de l'élément fourni.
        /// Un doublon est défini par un DataType identique et des RawData identiques.
        /// </summary>
        /// <param name="itemToTest">L'élément à tester.</param>
        /// <returns>L'élément ClipboardItem existant s'il est un doublon, sinon null.</returns>
        Task<ClipboardItem?> FindDuplicateAsync(ClipboardItem? itemToTest);

        /// <summary>
        /// Met à jour un élément existant dans l'historique.
        /// </summary>
        /// <param name="item">L'élément à mettre à jour.</param>
        Task UpdateItemAsync(ClipboardItem? item);

        /// <summary>
        /// Supprime un élément de l'historique de manière asynchrone.
        /// Cette méthode utilise une architecture SOLID avec des services modulaires pour la validation,
        /// la suppression en mémoire, les tentatives de retry, la gestion d'état et les notifications.
        /// </summary>
        /// <param name="id">L'ID de l'élément à supprimer.</param>
        /// <returns>True si la suppression a réussi, False sinon.</returns>
        Task<bool> DeleteItemAsync(long id);

        /// <summary>
        /// Utilise un élément de l'historique en le copiant dans le presse-papiers.
        /// </summary>
        /// <param name="id">L'ID de l'élément à utiliser.</param>
        Task UseItemAsync(long id);

        /// <summary>
        /// Efface tous les éléments de l'historique, avec option de préserver les éléments épinglés.
        /// </summary>
        /// <param name="preservePinned">Si vrai, les éléments épinglés ne seront pas supprimés.</param>
        Task ClearHistoryAsync(bool preservePinned = true);

        /// <summary>
        /// Persiste le nouvel ordre des éléments après une opération de glisser-déposer.
        /// </summary>
        /// <param name="itemsInNewOrder">Les éléments dans leur nouvel ordre.</param>
        /// <returns>Tâche asynchrone.</returns>
        Task PersistNewItemOrderAsync(IEnumerable<ClipboardItem> itemsInNewOrder);
        
        /// <summary>
        /// Nettoie la base de données en supprimant tous les éléments qui ne sont pas présents
        /// dans la liste en mémoire. Utile lors de la fermeture de l'application pour s'assurer
        /// que la base de données est synchronisée avec l'état actuel de l'application.
        /// </summary>
        /// <returns>Nombre d'éléments supprimés de la base de données.</returns>
        Task<int> PurgeOrphanedItemsAsync();

        /// <summary>
        /// Supprime les éléments non épinglés plus anciens que la période spécifiée.
        /// </summary>
        /// <param name="olderThan">Période avant laquelle supprimer les éléments</param>
        /// <returns>Nombre d'éléments supprimés</returns>
        Task<int> ClearItemsOlderThanAsync(TimeSpan olderThan);

        /// <summary>
        /// Charge les éléments de l'historique depuis la base de données sans déclencher d'événement HistoryChanged.
        /// Cette méthode est utilisée pour éviter les boucles infinies lors de la synchronisation.
        /// </summary>
        Task LoadHistorySilentlyAsync();
    }
} 