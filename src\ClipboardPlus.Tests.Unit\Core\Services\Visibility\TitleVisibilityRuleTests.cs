using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.Visibility
{
    /// <summary>
    /// Tests exhaustifs pour TitleVisibilityRule - Prévention de régression
    /// </summary>
    [TestFixture]
    public class TitleVisibilityRuleTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private TitleVisibilityRule _titleRule = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _titleRule = new TitleVisibilityRule(_mockLoggingService.Object);
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithValidLoggingService_InitializesCorrectly()
        {
            // Act & Assert
            Assert.IsNotNull(_titleRule);
        }

        [Test]
        public void Constructor_WithNullLoggingService_InitializesWithoutException()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new TitleVisibilityRule(null));
        }

        #endregion

        #region Core Logic Tests - 4 Scenarios SOLID

        [Test]
        public void ShouldBeVisible_GlobalTrue_TitleExists_ReturnsTrue()
        {
            // Arrange - Cas 1: Global autorisé + titre présent = VISIBLE
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = "Mon Titre Personnalisé" };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Titre devrait être visible quand global autorisé ET titre présent");
        }

        [Test]
        public void ShouldBeVisible_GlobalTrue_NoTitle_ReturnsFalse()
        {
            // Arrange - Cas 2: Global autorisé + pas de titre = MASQUÉ
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = null };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "Titre devrait être masqué quand pas de titre même si global autorisé");
        }

        [Test]
        public void ShouldBeVisible_GlobalFalse_TitleExists_ReturnsFalse()
        {
            // Arrange - Cas 3: Global masqué + titre présent = MASQUÉ
            var context = new VisibilityContext { GlobalTitleVisibility = false };
            var item = new ClipboardItem { CustomName = "Titre Existant" };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "Titre devrait être masqué quand global désactivé même avec titre");
        }

        [Test]
        public void ShouldBeVisible_GlobalFalse_NoTitle_ReturnsFalse()
        {
            // Arrange - Cas 4: Global masqué + pas de titre = MASQUÉ
            var context = new VisibilityContext { GlobalTitleVisibility = false };
            var item = new ClipboardItem { CustomName = null };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "Titre devrait être masqué quand global désactivé et pas de titre");
        }

        #endregion

        #region Edge Cases Tests

        [Test]
        public void ShouldBeVisible_EmptyString_ReturnsFalse()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = "" };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "Titre vide devrait être considéré comme pas de titre");
        }

        [Test]
        public void ShouldBeVisible_WhitespaceOnly_ReturnsFalse()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = "   \t\n  " };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "Titre avec seulement des espaces devrait être considéré comme pas de titre");
        }

        [Test]
        public void ShouldBeVisible_SingleCharacter_ReturnsTrue()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = "A" };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Titre d'un seul caractère devrait être visible");
        }

        [Test]
        public void ShouldBeVisible_VeryLongTitle_ReturnsTrue()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var longTitle = new string('A', 1000);
            var item = new ClipboardItem { CustomName = longTitle };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Titre très long devrait être visible");
        }

        [Test]
        public void ShouldBeVisible_SpecialCharacters_ReturnsTrue()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = "🎯 Titre avec émojis & caractères spéciaux @#$%" };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Titre avec caractères spéciaux devrait être visible");
        }

        #endregion

        #region Null Safety Tests

        [Test]
        public void ShouldBeVisible_NullItem_ReturnsFalse()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };

            // Act
            var result = _titleRule.ShouldBeVisible(null!, context);

            // Assert
            Assert.IsFalse(result, "Item null devrait retourner false");
        }

        [Test]
        public void ShouldBeVisible_NullContext_ReturnsFalse()
        {
            // Arrange
            var item = new ClipboardItem { CustomName = "Titre" };

            // Act
            var result = _titleRule.ShouldBeVisible(item, null!);

            // Assert
            Assert.IsFalse(result, "Context null devrait retourner false");
        }

        [Test]
        public void ShouldBeVisible_BothNull_ReturnsFalse()
        {
            // Act
            var result = _titleRule.ShouldBeVisible(null!, null!);

            // Assert
            Assert.IsFalse(result, "Item et context null devraient retourner false");
        }

        #endregion

        #region Regression Prevention Tests

        [Test]
        public void ShouldBeVisible_AfterRename_WithHiddenGlobal_StaysHidden()
        {
            // Arrange - Simule le bug de régression où les titres réapparaissaient après renommage
            var context = new VisibilityContext { GlobalTitleVisibility = false }; // Utilisateur veut cacher
            var item = new ClipboardItem { CustomName = "Nouveau nom après renommage" };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "RÉGRESSION: Le titre doit rester caché après renommage si l'utilisateur a choisi de cacher les titres");
        }

        [Test]
        public void ShouldBeVisible_MultipleCallsSameParameters_ConsistentResults()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = "Titre Test" };

            // Act - Appels multiples
            var result1 = _titleRule.ShouldBeVisible(item, context);
            var result2 = _titleRule.ShouldBeVisible(item, context);
            var result3 = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result1);
            Assert.IsTrue(result2);
            Assert.IsTrue(result3);
            Assert.AreEqual(result1, result2, "Résultats devraient être identiques");
            Assert.AreEqual(result2, result3, "Résultats devraient être identiques");
        }

        [Test]
        public void ShouldBeVisible_StateChanges_ReflectedCorrectly()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = "Titre" };

            // Act & Assert - État initial
            Assert.IsTrue(_titleRule.ShouldBeVisible(item, context));

            // Act & Assert - Changement global
            context.GlobalTitleVisibility = false;
            Assert.IsFalse(_titleRule.ShouldBeVisible(item, context));

            // Act & Assert - Retour à l'état initial
            context.GlobalTitleVisibility = true;
            Assert.IsTrue(_titleRule.ShouldBeVisible(item, context));
        }

        #endregion

        #region Logging Tests

        [Test]
        public void ShouldBeVisible_LogsDecision_WhenLoggingServiceAvailable()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { Id = 123, CustomName = "Test Title" };

            // Act
            _titleRule.ShouldBeVisible(item, context);

            // Assert
            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains("TITLE_VISIBILITY_RULE") && 
                    s.Contains("Item ID: 123") && 
                    s.Contains("GlobalTitleVisibility: True") &&
                    s.Contains("HasCustomName: True") &&
                    s.Contains("CustomName: 'Test Title'") &&
                    s.Contains("Result: True"))), 
                Times.Once,
                "La décision de visibilité devrait être loggée");
        }

        [Test]
        public void ShouldBeVisible_HandlesLoggingException_Gracefully()
        {
            // Arrange
            _mockLoggingService.Setup(l => l.LogInfo(It.IsAny<string>()))
                              .Throws(new System.Exception("Logging error"));
            
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = "Test" };

            // Act & Assert
            Assert.DoesNotThrow(() => _titleRule.ShouldBeVisible(item, context),
                "Exception de logging ne devrait pas faire planter la règle");
        }

        #endregion

        #region Performance Tests

        [Test]
        public void ShouldBeVisible_PerformanceTest_CompletesQuickly()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = "Test Title" };
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act - 1000 appels
            for (int i = 0; i < 1000; i++)
            {
                _titleRule.ShouldBeVisible(item, context);
            }

            // Assert
            stopwatch.Stop();
            Assert.Less(stopwatch.ElapsedMilliseconds, 100, 
                "1000 appels devraient prendre moins de 100ms");
        }

        #endregion
    }
}
