using System;

namespace ClipboardPlus.Core.Services.Logging
{
    /// <summary>
    /// Interface pour la configuration du système de logging.
    /// Permet l'injection de dépendances et la configuration par environnement.
    /// </summary>
    public interface ILoggingConfiguration
    {
        /// <summary>
        /// Chemin du fichier de log principal.
        /// </summary>
        string LogFilePath { get; }
        
        /// <summary>
        /// Indique si la sortie console est activée.
        /// </summary>
        bool ConsoleOutputEnabled { get; }
        
        /// <summary>
        /// Indique si la sortie debug est activée.
        /// </summary>
        bool DebugOutputEnabled { get; }
        
        /// <summary>
        /// Niveau minimum de log à enregistrer.
        /// </summary>
        string MinimumLevel { get; }
        
        /// <summary>
        /// Taille maximale du buffer avant flush automatique.
        /// </summary>
        int MaxBufferSize { get; }
        
        /// <summary>
        /// Intervalle de flush automatique.
        /// </summary>
        TimeSpan FlushInterval { get; }
        
        /// <summary>
        /// Taille maximale du fichier de log en MB.
        /// </summary>
        int MaxLogSizeMB { get; }
    }
}
