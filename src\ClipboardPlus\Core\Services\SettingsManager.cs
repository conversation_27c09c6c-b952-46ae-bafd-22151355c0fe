using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using System.Reflection;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Gestionnaire des paramètres de l'application.
    /// </summary>
    public class SettingsManager : ISettingsManager
    {
        private readonly IPersistenceService _persistenceService;
        private readonly ApplicationSettings _settings;
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Événement déclenché lorsqu'un paramètre est modifié.
        /// </summary>
        public event Action<string>? SettingChanged;

        /// <summary>
        /// Initialise une nouvelle instance de SettingsManager.
        /// </summary>
        /// <param name="persistenceService">Service de persistance pour charger/enregistrer les paramètres.</param>
        /// <param name="loggingService">Service de logging (optionnel).</param>
        public SettingsManager(IPersistenceService persistenceService, ILoggingService? loggingService = null)
        {
            _persistenceService = persistenceService ?? throw new ArgumentNullException(nameof(persistenceService));
            _loggingService = loggingService;
            _settings = new ApplicationSettings();
        }

        /// <summary>
        /// Nombre maximum d'éléments dans l'historique du presse-papiers.
        /// </summary>
        public int MaxHistoryItems
        {
            get => _settings.MaxHistoryItems;
            set
            {
                if (_settings.MaxHistoryItems != value)
                {
                    _settings.MaxHistoryItems = value;
                    SettingChanged?.Invoke(nameof(MaxHistoryItems));
                }
            }
        }

        /// <summary>
        /// Chemin vers le fichier de thème actif.
        /// </summary>
        public string ActiveThemePath
        {
            get => _settings.ActiveThemePath;
            set
            {
                if (_settings.ActiveThemePath != value)
                {
                    _settings.ActiveThemePath = value;
                    SettingChanged?.Invoke(nameof(ActiveThemePath));
                }
            }
        }

        /// <summary>
        /// Combinaison de touches pour activer l'application.
        /// </summary>
        public string ShortcutKeyCombination
        {
            get
            {
                _loggingService?.LogInfo($"[SettingsManager] GET ShortcutKeyCombination: '{_settings.ShortcutKeyCombination}'");
                return _settings.ShortcutKeyCombination;
            }
            set
            {
                _loggingService?.LogInfo($"[SettingsManager] SET ShortcutKeyCombination: '{_settings.ShortcutKeyCombination}' -> '{value}'");
                if (_settings.ShortcutKeyCombination != value)
                {
                    _settings.ShortcutKeyCombination = value;
                    _loggingService?.LogInfo($"[SettingsManager] ShortcutKeyCombination CHANGED, émission événement");
                    SettingChanged?.Invoke(nameof(ShortcutKeyCombination));
                }
                else
                {
                    _loggingService?.LogInfo($"[SettingsManager] ShortcutKeyCombination INCHANGÉ");
                }
            }
        }

        /// <summary>
        /// Indique si l'application doit démarrer avec Windows.
        /// </summary>
        public bool StartWithWindows
        {
            get => _settings.StartWithWindows;
            set
            {
                if (_settings.StartWithWindows != value)
                {
                    _settings.StartWithWindows = value;
                    SettingChanged?.Invoke(nameof(StartWithWindows));
                }
            }
        }

        /// <summary>
        /// Dimension maximale pour les miniatures d'images.
        /// </summary>
        public int MaxImageDimensionForThumbnail
        {
            get => _settings.MaxImageDimensionForThumbnail;
            set
            {
                if (_settings.MaxImageDimensionForThumbnail != value)
                {
                    _settings.MaxImageDimensionForThumbnail = value;
                    SettingChanged?.Invoke(nameof(MaxImageDimensionForThumbnail));
                }
            }
        }
        
        /// <summary>
        /// Taille des miniatures en pixels.
        /// </summary>
        public int ThumbnailSize => _settings.MaxImageDimensionForThumbnail;

        /// <summary>
        /// Taille maximale d'un élément stockable (en octets).
        /// </summary>
        public long MaxStorableItemSizeBytes
        {
            get => _settings.MaxStorableItemSizeBytes;
            set
            {
                if (_settings.MaxStorableItemSizeBytes != value)
                {
                    _settings.MaxStorableItemSizeBytes = value;
                    SettingChanged?.Invoke(nameof(MaxStorableItemSizeBytes));
                }
            }
        }

        /// <summary>
        /// Longueur maximale de l'aperçu du texte.
        /// </summary>
        public int MaxTextPreviewLength
        {
            get => _settings.MaxTextPreviewLength;
            set
            {
                if (_settings.MaxTextPreviewLength != value)
                {
                    _settings.MaxTextPreviewLength = value;
                    SettingChanged?.Invoke(nameof(MaxTextPreviewLength));
                }
            }
        }

        /// <summary>
        /// Indique si l'horodatage doit être masqué dans l'interface.
        /// </summary>
        public bool HideTimestamp
        {
            get 
            {
                _loggingService?.LogInfo($"[SettingsManager] GET HideTimestamp: {_settings.HideTimestamp}");
                return _settings.HideTimestamp;
            }
            set
            {
                _loggingService?.LogInfo($"[SettingsManager] SET HideTimestamp: {_settings.HideTimestamp} -> {value}");
                if (_settings.HideTimestamp != value)
                {
                    _settings.HideTimestamp = value;
                    _loggingService?.LogInfo($"[SettingsManager] HideTimestamp CHANGED, émission événement SettingChanged");
                    SettingChanged?.Invoke(nameof(HideTimestamp));
                }
                else
                {
                    _loggingService?.LogInfo($"[SettingsManager] HideTimestamp INCHANGÉ");
                }
            }
        }

        /// <summary>
        /// Indique si le titre des éléments doit être masqué dans l'interface.
        /// </summary>
        public bool HideItemTitle
        {
            get 
            {
                _loggingService?.LogInfo($"[SettingsManager] GET HideItemTitle: {_settings.HideItemTitle}");
                return _settings.HideItemTitle;
            }
            set
            {
                _loggingService?.LogInfo($"[SettingsManager] SET HideItemTitle: {_settings.HideItemTitle} -> {value}");
                if (_settings.HideItemTitle != value)
                {
                    _settings.HideItemTitle = value;
                    _loggingService?.LogInfo($"[SettingsManager] HideItemTitle CHANGED, émission événement SettingChanged");
                    SettingChanged?.Invoke(nameof(HideItemTitle));
                }
                else
                {
                    _loggingService?.LogInfo($"[SettingsManager] HideItemTitle INCHANGÉ");
                }
            }
        }

        /// <summary>
        /// Largeur de la fenêtre des paramètres.
        /// </summary>
        public double SettingsWindowWidth
        {
            get
            {
                _loggingService?.LogInfo($"[SM_GET] Lecture de SettingsWindowWidth. Valeur: {_settings.SettingsWindowWidth}");
                return _settings.SettingsWindowWidth;
            }
            set
            {
                _loggingService?.LogInfo($"[SM_SET] Tentative de modification de SettingsWindowWidth. Actuelle: {_settings.SettingsWindowWidth}, Nouvelle: {value}");
                if (Math.Abs(_settings.SettingsWindowWidth - value) > 0.01)
                {
                    _settings.SettingsWindowWidth = value;
                    _loggingService?.LogInfo($"[SM_SET] SettingsWindowWidth MODIFIÉE. Nouvelle valeur en cache: {_settings.SettingsWindowWidth}");
                    SettingChanged?.Invoke(nameof(SettingsWindowWidth));
                }
            }
        }

        /// <summary>
        /// Hauteur de la fenêtre des paramètres.
        /// </summary>
        public double SettingsWindowHeight
        {
            get
            {
                _loggingService?.LogInfo($"[SM_GET] Lecture de SettingsWindowHeight. Valeur: {_settings.SettingsWindowHeight}");
                return _settings.SettingsWindowHeight;
            }
            set
            {
                _loggingService?.LogInfo($"[SM_SET] Tentative de modification de SettingsWindowHeight. Actuelle: {_settings.SettingsWindowHeight}, Nouvelle: {value}");
                if (Math.Abs(_settings.SettingsWindowHeight - value) > 0.01)
                {
                    _settings.SettingsWindowHeight = value;
                    _loggingService?.LogInfo($"[SM_SET] SettingsWindowHeight MODIFIÉE. Nouvelle valeur en cache: {_settings.SettingsWindowHeight}");
                    SettingChanged?.Invoke(nameof(SettingsWindowHeight));
                }
            }
        }

        /// <summary>
        /// Position Y (Top) de la fenêtre des paramètres.
        /// </summary>
        public double SettingsWindowTop
        {
            get
            {
                _loggingService?.LogInfo($"[SM_GET] Lecture de SettingsWindowTop. Valeur: {_settings.SettingsWindowTop}");
                return _settings.SettingsWindowTop;
            }
            set
            {
                _loggingService?.LogInfo($"[SM_SET] Tentative de modification de SettingsWindowTop. Actuelle: {_settings.SettingsWindowTop}, Nouvelle: {value}");
                if (Math.Abs(_settings.SettingsWindowTop - value) > 0.01)
                {
                    _settings.SettingsWindowTop = value;
                    _loggingService?.LogInfo($"[SM_SET] SettingsWindowTop MODIFIÉE. Nouvelle valeur en cache: {_settings.SettingsWindowTop}");
                    SettingChanged?.Invoke(nameof(SettingsWindowTop));
                }
            }
        }

        /// <summary>
        /// Position X (Left) de la fenêtre des paramètres.
        /// </summary>
        public double SettingsWindowLeft
        {
            get
            {
                _loggingService?.LogInfo($"[SM_GET] Lecture de SettingsWindowLeft. Valeur: {_settings.SettingsWindowLeft}");
                return _settings.SettingsWindowLeft;
            }
            set
            {
                _loggingService?.LogInfo($"[SM_SET] Tentative de modification de SettingsWindowLeft. Actuelle: {_settings.SettingsWindowLeft}, Nouvelle: {value}");
                if (Math.Abs(_settings.SettingsWindowLeft - value) > 0.01)
                {
                    _settings.SettingsWindowLeft = value;
                    _loggingService?.LogInfo($"[SM_SET] SettingsWindowLeft MODIFIÉE. Nouvelle valeur en cache: {_settings.SettingsWindowLeft}");
                    SettingChanged?.Invoke(nameof(SettingsWindowLeft));
                }
            }
        }

        /// <summary>
        /// Sauvegarde tous les paramètres actuels dans la source de persistance.
        /// </summary>
        public async Task SaveSettingsToPersistenceAsync()
        {
            _loggingService?.LogInfo("[SettingsManager] Début de la sauvegarde de tous les paramètres.");
            foreach (var property in typeof(ApplicationSettings).GetProperties())
            {
                if (property.CanRead)
                {
                    var key = property.Name;
                    var value = property.GetValue(_settings);
                    string? stringValue = Convert.ToString(value, System.Globalization.CultureInfo.InvariantCulture);

                    if (stringValue != null)
                    {
                        await _persistenceService.SaveApplicationSettingAsync(key, stringValue);
                    }
                }
            }
            _loggingService?.LogInfo("[SettingsManager] Fin de la sauvegarde de tous les paramètres.");
        }

        /// <summary>
        /// Charge les paramètres depuis la source de persistance.
        /// </summary>
        public async Task LoadSettingsAsync()
        {
            _loggingService?.LogInfo($"[SettingsManager] LoadSettingsAsync DÉBUT - ShortcutKeyCombination avant: '{_settings.ShortcutKeyCombination}'");
            var settingsDict = await _persistenceService.GetApplicationSettingsAsync();
            _loggingService?.LogInfo($"[SettingsManager] Dictionnaire chargé avec {settingsDict?.Count ?? 0} paramètres");

            if (settingsDict == null)
            {
                _loggingService?.LogWarning("[SettingsManager] Le dictionnaire de paramètres est null, utilisation des valeurs par défaut");
                return;
            }

            foreach (var kvp in settingsDict)
            {
                _loggingService?.LogInfo($"[SettingsManager] Chargement depuis DB: Clé='{kvp.Key}', Valeur='{kvp.Value}'");
                var property = typeof(ApplicationSettings).GetProperty(kvp.Key);
                if (property != null && property.CanWrite)
                {
                    try
                    {
                        var convertedValue = ConvertToPropertyType(property.PropertyType, kvp.Value);
                    if (convertedValue != null)
                    {
                            property.SetValue(_settings, convertedValue);
                            _loggingService?.LogInfo($"[SettingsManager] Valeur convertie et assignée pour {kvp.Key}: {convertedValue}");
                }
                        else
                {
                            _loggingService?.LogWarning($"[SettingsManager] Valeur non assignée pour {kvp.Key} car la conversion a retourné null");
                }
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError($"Erreur de conversion pour la clé {kvp.Key} et valeur {kvp.Value}", ex);
                    }
                }
            }
            _loggingService?.LogInfo($"[SettingsManager] LoadSettingsAsync FIN - ShortcutKeyCombination après: '{_settings.ShortcutKeyCombination}'");
        }

        /// <summary>
        /// Sauvegarde un paramètre dans la source de persistance.
        /// </summary>
        /// <typeparam name="T">Type de la valeur du paramètre.</typeparam>
        /// <param name="propertySelector">Expression pour sélectionner la propriété à sauvegarder.</param>
        /// <param name="value">Nouvelle valeur du paramètre.</param>
        public async Task SaveSettingAsync<T>(Expression<Func<ApplicationSettings, T>> propertySelector, T value)
        {
            // Extraire le nom de la propriété depuis l'expression lambda
            string propertyName = GetPropertyName(propertySelector);
            _loggingService?.LogInfo($"[SettingsManager] SaveSettingAsync DÉBUT - {propertyName}: '{value}'");

            // Mettre à jour la valeur dans l'objet de paramètres local
            var property = typeof(ApplicationSettings).GetProperty(propertyName);
            if (property != null)
            {
                var oldValue = property.GetValue(_settings);
                _loggingService?.LogInfo($"[SettingsManager] Mise à jour cache local {propertyName}: '{oldValue}' -> '{value}'");

                property.SetValue(_settings, value);

                // Convertir la valeur en chaîne pour la persistance
                string stringValue = Convert.ToString(value) ?? string.Empty;
                _loggingService?.LogInfo($"[SettingsManager] Sauvegarde en base {propertyName}: '{stringValue}'");

                // Persister le paramètre
                await _persistenceService.SaveApplicationSettingAsync(propertyName, stringValue);
                _loggingService?.LogInfo($"[SettingsManager] Sauvegarde en base TERMINÉE pour {propertyName}");

                // Déclencher l'événement SettingChanged
                _loggingService?.LogInfo($"[SettingsManager] Émission événement SettingChanged pour {propertyName}");
                SettingChanged?.Invoke(propertyName);
            }

            _loggingService?.LogInfo($"[SettingsManager] SaveSettingAsync FIN - {propertyName}");
        }

        // Méthodes utilitaires privées

        /// <summary>
        /// Extrait le nom de la propriété depuis une expression lambda.
        /// </summary>
        private string GetPropertyName<T>(Expression<Func<ApplicationSettings, T>> propertySelector)
        {
            if (propertySelector.Body is MemberExpression memberExpression)
            {
                return memberExpression.Member.Name;
            }
            
            throw new ArgumentException("L'expression doit être une propriété simple", nameof(propertySelector));
        }

        /// <summary>
        /// Convertit une chaîne en un type spécifique pour l'assignation à une propriété.
        /// </summary>
        private object? ConvertToPropertyType(Type targetType, string value)
        {
            try
            {
                if (targetType == typeof(int))
                    return int.Parse(value);
                else if (targetType == typeof(long))
                    return long.Parse(value);
                else if (targetType == typeof(double))
                    return double.Parse(value, System.Globalization.CultureInfo.InvariantCulture);
                else if (targetType == typeof(bool))
                    return bool.Parse(value);
                else if (targetType == typeof(string))
                    return value;
                else if (targetType.IsEnum)
                    return Enum.Parse(targetType, value);
                else
                    return null;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ConvertToPropertyType: Erreur de conversion de '{value}' vers le type {targetType.Name}", ex);
                // En cas d'erreur de conversion, retourner null
                return null;
            }
        }
    }
} 