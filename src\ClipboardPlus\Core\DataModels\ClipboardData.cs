using System.Collections.Specialized;
using System.Windows.Media.Imaging;

namespace ClipboardPlus.Core.DataModels;

/// <summary>
/// DTO pour transporter les données extraites du presse-papiers.
/// </summary>
public class ClipboardData
{
    public string? Text { get; set; }
    public BitmapSource? Image { get; set; }
    public StringCollection? FileDropList { get; set; }
    
    public bool ContainsText() => !string.IsNullOrEmpty(Text);
    public bool ContainsImage() => Image != null;
    public bool ContainsFileDropList() => FileDropList != null && FileDropList.Count > 0;
} 