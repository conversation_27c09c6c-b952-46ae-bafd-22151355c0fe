using System;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.NewItem.Implementations;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.NewItem
{
    /// <summary>
    /// Tests unitaires pour TestModeHandler.
    /// Vérifie le respect du principe SRP : responsabilité unique de gestion du mode test.
    /// </summary>
    [TestFixture]
    [Category("NewItem")]
    [Category("TestMode")]
    public class TestModeHandlerTests
    {
        private TestModeHandler _handler = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IOperationStateManager> _mockStateManager = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockStateManager = new Mock<IOperationStateManager>();
            _handler = new TestModeHandler(_mockLoggingService.Object);
        }

        #region Tests de Détection du Mode Test

        [Test]
        [Description("Valide que IsInTestMode retourne true dans un environnement de test")]
        public void IsInTestMode_InTestEnvironment_ReturnsTrue()
        {
            // Act
            var result = _handler.IsInTestMode();

            // Assert
            Assert.That(result, Is.True, "IsInTestMode devrait retourner true dans un environnement de test");
        }

        [Test]
        [Description("Valide que IsInTestMode ne lève pas d'exception")]
        public void IsInTestMode_DoesNotThrowException()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _handler.IsInTestMode(), 
                "IsInTestMode ne devrait jamais lever d'exception");
        }

        #endregion

        #region Tests de Gestion du Mode Test

        [Test]
        [Description("Valide que HandleTestMode configure correctement l'état pour le mode test")]
        public void HandleTestMode_ConfiguresStateCorrectly()
        {
            // Act
            _handler.HandleTestMode(_mockStateManager.Object);

            // Assert
            _mockStateManager.VerifySet(x => x.NewItemTextContent = "Élément de test", Times.Once,
                "Le contenu de test devrait être défini");
            _mockStateManager.VerifySet(x => x.IsItemCreationActive = true, Times.Once,
                "La création d'élément devrait être activée");
            _mockStateManager.Verify(x => x.RefreshItemCreationCommands(), Times.Once,
                "Les commandes devraient être rafraîchies");
        }

        [Test]
        [Description("Valide que HandleTestMode génère les logs appropriés")]
        public void HandleTestMode_GeneratesCorrectLogs()
        {
            // Act
            _handler.HandleTestMode(_mockStateManager.Object);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo("Mode test détecté - Configuration du contenu de test"),
                Times.Once,
                "Un log de début devrait être généré");
            
            _mockLoggingService.Verify(
                x => x.LogInfo("Mode test configuré avec succès"),
                Times.Once,
                "Un log de succès devrait être généré");
        }

        [Test]
        [Description("Valide que HandleTestMode propage les exceptions du StateManager")]
        public void HandleTestMode_WhenStateManagerThrows_PropagatesException()
        {
            // Arrange
            var expectedException = new InvalidOperationException("Test exception");
            _mockStateManager.SetupSet(x => x.NewItemTextContent = It.IsAny<string>())
                .Throws(expectedException);

            // Act & Assert
            var actualException = Assert.Throws<InvalidOperationException>(
                () => _handler.HandleTestMode(_mockStateManager.Object),
                "L'exception du StateManager devrait être propagée");
            
            Assert.That(actualException, Is.SameAs(expectedException),
                "L'exception propagée devrait être la même instance");
            
            // Vérifier que l'erreur est loggée
            _mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de la gestion du mode test")), expectedException),
                Times.Once,
                "L'erreur devrait être loggée");
        }

        [Test]
        [Description("Valide que HandleTestMode avec StateManager null lève ArgumentNullException")]
        public void HandleTestMode_WithNullStateManager_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(
                () => _handler.HandleTestMode(null!),
                "HandleTestMode devrait lever ArgumentNullException avec StateManager null");
        }

        #endregion

        #region Tests de Robustesse

        [Test]
        [Description("Valide que le handler fonctionne sans service de logging")]
        public void TestModeHandler_WithoutLoggingService_WorksCorrectly()
        {
            // Arrange
            var handlerWithoutLogging = new TestModeHandler(null);

            // Act & Assert - Ne devrait pas lever d'exception
            Assert.DoesNotThrow(() =>
            {
                var isInTestMode = handlerWithoutLogging.IsInTestMode();
                Assert.That(isInTestMode, Is.True, "Devrait détecter le mode test même sans logging");
            }, "Le handler devrait fonctionner sans service de logging");
        }

        [Test]
        [Description("Valide que HandleTestMode fonctionne sans logging")]
        public void HandleTestMode_WithoutLoggingService_WorksCorrectly()
        {
            // Arrange
            var handlerWithoutLogging = new TestModeHandler(null);

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                handlerWithoutLogging.HandleTestMode(_mockStateManager.Object);
            }, "HandleTestMode devrait fonctionner sans service de logging");
            
            // Vérifier que les opérations sur le StateManager ont bien eu lieu
            _mockStateManager.VerifySet(x => x.NewItemTextContent = "Élément de test", Times.Once);
            _mockStateManager.VerifySet(x => x.IsItemCreationActive = true, Times.Once);
            _mockStateManager.Verify(x => x.RefreshItemCreationCommands(), Times.Once);
        }

        [Test]
        [Description("Valide que le handler peut être créé sans paramètres")]
        public void Constructor_WithoutParameters_CreatesValidInstance()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var handler = new TestModeHandler();
                var result = handler.IsInTestMode();
                Assert.That(result, Is.True, "Devrait détecter le mode test");
            }, "Le handler devrait pouvoir être créé sans paramètres");
        }

        #endregion

        #region Tests de Gestion d'Erreurs

        [Test]
        [Description("Valide que IsInTestMode retourne false en cas d'exception interne")]
        public void IsInTestMode_WhenInternalExceptionOccurs_ReturnsFalse()
        {
            // Note: Ce test est difficile à implémenter car IsInTestMode utilise des APIs système
            // qui sont difficiles à mocker. Dans un vrai projet, on pourrait injecter une abstraction
            // pour les APIs système (Application.Current, StackTrace, etc.)
            
            // Pour l'instant, on vérifie juste que la méthode ne lève pas d'exception
            Assert.DoesNotThrow(() => _handler.IsInTestMode(),
                "IsInTestMode ne devrait jamais lever d'exception, même en cas d'erreur interne");
        }

        #endregion

        #region Tests de Comportement Spécifique

        [Test]
        [Description("Valide que HandleTestMode définit exactement le contenu attendu")]
        public void HandleTestMode_SetsExpectedTestContent()
        {
            // Act
            _handler.HandleTestMode(_mockStateManager.Object);

            // Assert - Vérifier le contenu exact
            _mockStateManager.VerifySet(x => x.NewItemTextContent = "Élément de test", Times.Once,
                "Le contenu de test devrait être exactement 'Élément de test'");
        }

        [Test]
        [Description("Valide que HandleTestMode active la création d'élément")]
        public void HandleTestMode_ActivatesItemCreation()
        {
            // Act
            _handler.HandleTestMode(_mockStateManager.Object);

            // Assert
            _mockStateManager.VerifySet(x => x.IsItemCreationActive = true, Times.Once,
                "IsItemCreationActive devrait être défini à true");
        }

        [Test]
        [Description("Valide que HandleTestMode rafraîchit les commandes")]
        public void HandleTestMode_RefreshesCommands()
        {
            // Act
            _handler.HandleTestMode(_mockStateManager.Object);

            // Assert
            _mockStateManager.Verify(x => x.RefreshItemCreationCommands(), Times.Once,
                "RefreshItemCreationCommands devrait être appelée une fois");
        }

        #endregion
    }
}
