using System;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation du service de synchronisation intelligente des collections d'historique.
    ///
    /// Ce service reproduit fidèlement la logique complexe de synchronisation
    /// de la méthode ClipboardHistoryManager_HistoryChanged originale.
    /// </summary>
    public class HistorySynchronizationService : IHistorySynchronizationService
    {
        private readonly ILoggingService _loggingService;
        private const int MAX_SYNCHRONIZATION_ATTEMPTS = 3;

        /// <summary>
        /// Initialise une nouvelle instance du service de synchronisation.
        /// </summary>
        /// <param name="loggingService">Service de logging pour tracer les opérations</param>
        public HistorySynchronizationService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Synchronise les collections UI et Manager si nécessaire.
        ///
        /// Cette méthode reproduit exactement la logique de la méthode originale :
        /// 1. Compare les collections
        /// 2. Si synchronisées, reset le compteur et retourne
        /// 3. Si désynchronisées, tente une synchronisation
        /// 4. Si max tentatives atteint, demande un rechargement complet
        /// </summary>
        public async Task<SynchronizationResult> SynchronizeIfNeededAsync(HistorySynchronizationContext context)
        {
            if (context == null)
            {
                _loggingService.LogError("[HistorySynchronizationService] Contexte de synchronisation null");
                return SynchronizationResult.Error("Contexte de synchronisation null");
            }

            if (context.Synchronizer == null)
            {
                _loggingService.LogError($"[HistorySynchronizationService] [{context.EventId}] Synchronizer null dans le contexte");
                return SynchronizationResult.Error("Synchronizer null");
            }

            try
            {
                // Étape 1: Comparer les collections
                var comparison = CompareCollections(context);

                if (comparison.AreSynchronized)
                {
                    // Collections déjà synchronisées - reproduire la logique originale
                    _loggingService.LogInfo($"[HistorySynchronizationService] [{context.EventId}] Collections déjà synchronisées (taille et IDs), aucune action nécessaire");
                    context.Synchronizer.SynchronizationCounter = 0; // Reset du compteur comme dans l'original
                    return SynchronizationResult.AlreadySynchronized();
                }

                // Étape 2: Désynchronisation détectée
                _loggingService.LogInfo($"[HistorySynchronizationService] [{context.EventId}] Désynchronisation détectée - UI: {comparison.UICount}, Manager: {comparison.ManagerCount}");

                // Étape 3: Vérifier le nombre de tentatives
                if (context.Synchronizer.SynchronizationCounter < MAX_SYNCHRONIZATION_ATTEMPTS)
                {
                    // Tentative de synchronisation - reproduire la logique originale
                    context.Synchronizer.SynchronizationCounter++;
                    _loggingService.LogInfo($"[HistorySynchronizationService] [{context.EventId}] Tentative de synchronisation #{context.Synchronizer.SynchronizationCounter}/{MAX_SYNCHRONIZATION_ATTEMPTS}");

                    await context.Synchronizer.SynchronizeCollectionsAsync($"HistoryChanged_{context.EventId}");

                    // CORRECTION CRITIQUE: Vérifier si la synchronisation a réellement réussi
                    var postSyncComparison = CompareCollections(context);
                    if (postSyncComparison.AreSynchronized)
                    {
                        _loggingService.LogInfo($"[HistorySynchronizationService] [{context.EventId}] Synchronisation réussie - Reset du compteur");
                        context.Synchronizer.SynchronizationCounter = 0; // Reset sur succès
                        return SynchronizationResult.Synchronized();
                    }
                    else
                    {
                        _loggingService.LogWarning($"[HistorySynchronizationService] [{context.EventId}] Synchronisation échouée - UI:{postSyncComparison.UICount}, Manager:{postSyncComparison.ManagerCount}");
                        // Ne pas reset le compteur, laisser la prochaine tentative ou le rechargement complet
                        return SynchronizationResult.RequiresRetryResult();
                    }
                }
                else
                {
                    // Max tentatives atteint - reproduire la logique originale
                    _loggingService.LogWarning($"[HistorySynchronizationService] [{context.EventId}] Nombre maximal de tentatives de synchronisation atteint ({MAX_SYNCHRONIZATION_ATTEMPTS}), rechargement complet nécessaire");
                    context.Synchronizer.SynchronizationCounter = 0; // Reset comme dans l'original
                    return SynchronizationResult.RequiresFullReloadResult();
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"[HistorySynchronizationService] [{context.EventId}] Erreur lors de la synchronisation: {ex.Message}", ex);
                return SynchronizationResult.Error($"Erreur de synchronisation: {ex.Message}");
            }
        }

        /// <summary>
        /// Compare deux collections pour déterminer si elles sont synchronisées.
        ///
        /// Reproduit exactement la logique de comparaison de la méthode originale.
        /// </summary>
        public CollectionComparison CompareCollections(HistorySynchronizationContext context)
        {
            if (context?.UIItems == null || context.ManagerItems == null)
            {
                _loggingService.LogWarning($"[HistorySynchronizationService] [{context?.EventId}] Collections null lors de la comparaison");
                return new CollectionComparison(0, 0, false);
            }

            int uiCount = context.UIItems.Count;
            int managerCount = context.ManagerItems.Count;

            // Reproduire exactement la logique de comparaison originale
            bool areSynchronized = uiCount == managerCount &&
                context.UIItems.Select(i => i.Id).OrderBy(id => id)
                    .SequenceEqual(context.ManagerItems.Select(i => i.Id).OrderBy(id => id));

            _loggingService.LogInfo($"[HistorySynchronizationService] [{context.EventId}] Comparaison: UI={uiCount}, Manager={managerCount}, Synchronisées={areSynchronized}");

            return new CollectionComparison(uiCount, managerCount, areSynchronized);
        }
    }
}
