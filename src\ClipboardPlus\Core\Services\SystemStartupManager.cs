using System;
using System.IO;
using System.Reflection;
using Microsoft.Win32;
using WpfApplication = System.Windows.Application;
using ClipboardPlus.Core.Extensions;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Gestionnaire de démarrage automatique avec Windows
    /// </summary>
    public class SystemStartupManager : ISystemStartupManager
    {
        private const string REG_KEY_RUN = @"SOFTWARE\Microsoft\Windows\CurrentVersion\Run";
        private const string APP_NAME = "ClipboardPlus";
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Constructeur du gestionnaire de démarrage automatique
        /// </summary>
        public SystemStartupManager()
        {
            try
            {
                // Tenter d'obtenir le service de journalisation
                _loggingService = WpfApplication.Current?.Services()?.GetService(typeof(ILoggingService)) as ILoggingService;
            }
            catch (Exception ex)
            {
                // Si une erreur se produit lors de l'obtention du service de journalisation,
                // nous continuons sans journalisation (pas de log possible ici)
                // Note: Debug.WriteLine nécessaire car _loggingService n'est pas encore initialisé
                System.Diagnostics.Debug.WriteLine($"Erreur lors de l'initialisation du service de journalisation dans SystemStartupManager: {ex.Message}");
            }
        }

        /// <summary>
        /// Détermine si l'application est configurée pour démarrer avec Windows
        /// </summary>
        /// <returns>True si l'application démarre avec Windows, False sinon</returns>
        public bool IsStartupEnabled()
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(REG_KEY_RUN, false);
                if (key == null)
                {
                    _loggingService?.LogWarning($"Clé de registre '{REG_KEY_RUN}' introuvable");
                    return false;
                }

                var value = key.GetValue(APP_NAME) as string;
                return !string.IsNullOrEmpty(value);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Erreur lors de la vérification du démarrage automatique", ex);
                return false;
            }
        }

        /// <summary>
        /// Active ou désactive le démarrage automatique avec Windows
        /// </summary>
        /// <param name="enable">True pour activer, False pour désactiver</param>
        /// <returns>True si l'opération a réussi, False sinon</returns>
        public bool SetStartupEnabled(bool enable)
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(REG_KEY_RUN, true);
                if (key == null)
                {
                    _loggingService?.LogWarning($"Clé de registre '{REG_KEY_RUN}' introuvable");
                    return false;
                }

                if (enable)
                {
                    var exePath = Assembly.GetEntryAssembly()?.Location;
                    if (string.IsNullOrEmpty(exePath))
                    {
                        _loggingService?.LogWarning("Impossible d'obtenir le chemin de l'exécutable");
                        return false;
                    }

                    // Remplacer .dll par .exe pour les applications .NET Core
                    if (exePath.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
                    {
                        exePath = Path.ChangeExtension(exePath, ".exe");
                    }

                    key.SetValue(APP_NAME, exePath);
                    _loggingService?.LogInfo($"Démarrage automatique activé: {exePath}");
                }
                else
                {
                    key.DeleteValue(APP_NAME, false);
                    _loggingService?.LogInfo("Démarrage automatique désactivé");
                }

                return true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Erreur lors de la modification du démarrage automatique", ex);
                return false;
            }
        }

        /// <summary>
        /// Synchronise l'état du démarrage automatique avec le paramètre de l'application
        /// </summary>
        /// <param name="shouldStartWithWindows">Valeur du paramètre StartWithWindows</param>
        public void SyncStartupWithSettings(bool shouldStartWithWindows)
        {
            try
            {
                bool isCurrentlyEnabled = IsStartupEnabled();
                
                if (shouldStartWithWindows != isCurrentlyEnabled)
                {
                    _loggingService?.LogInfo($"Synchronisation du démarrage automatique: {shouldStartWithWindows}");
                    SetStartupEnabled(shouldStartWithWindows);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Erreur lors de la synchronisation du démarrage automatique", ex);
            }
        }
    }
} 