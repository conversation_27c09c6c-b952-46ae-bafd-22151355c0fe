// ============================================================================
// EVENT VIEWMODEL MANAGER IMPLEMENTATION - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Implémentation concrète de la gestion des événements
// 📋 RESPONSABILITÉ : Orchestration des événements et synchronisation
// 🏗️ ARCHITECTURE : Extraction de Events.cs + Events.Refactored.cs
//
// ============================================================================

using System;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels.Managers.Interfaces;
using CommunityToolkit.Mvvm.ComponentModel;

namespace ClipboardPlus.UI.ViewModels.Managers.Implementations
{
    /// <summary>
    /// Implémentation concrète du manager de gestion des événements.
    /// 
    /// Cette classe orchestre les événements entre les différents composants
    /// et assure la synchronisation des données.
    /// </summary>
    public class EventViewModelManager : ObservableObject, IEventViewModelManager
    {
        #region Champs Privés

        private bool _isEventHandlingActive = true;
        private int _processedEventCount;
        private bool _isAutoSyncEnabled = true;
        private bool _isDisposed;

        #endregion

        #region Propriétés d'État (IEventViewModelManager)

        /// <summary>
        /// Indique si le gestionnaire d'événements est actif.
        /// </summary>
        public bool IsEventHandlingActive
        {
            get => _isEventHandlingActive;
            set => SetProperty(ref _isEventHandlingActive, value);
        }

        /// <summary>
        /// Nombre d'événements traités depuis le démarrage.
        /// </summary>
        public int ProcessedEventCount => _processedEventCount;

        /// <summary>
        /// Indique si la synchronisation automatique est activée.
        /// </summary>
        public bool IsAutoSyncEnabled
        {
            get => _isAutoSyncEnabled;
            set => SetProperty(ref _isAutoSyncEnabled, value);
        }

        #endregion

        #region Événements Publics (IEventViewModelManager)

        /// <summary>
        /// Événement déclenché lorsque l'historique change.
        /// </summary>
        public event EventHandler? HistoryChanged;

        /// <summary>
        /// Événement déclenché lorsque la sélection change.
        /// </summary>
        public event EventHandler<ClipboardItem?>? SelectionChanged;

        /// <summary>
        /// Événement déclenché lorsqu'un élément est ajouté à l'historique.
        /// </summary>
        public event EventHandler<ClipboardItem>? ItemAdded;

        /// <summary>
        /// Événement déclenché lorsqu'un élément est supprimé de l'historique.
        /// </summary>
        public event EventHandler<ClipboardItem>? ItemRemoved;

        /// <summary>
        /// Événement déclenché lorsqu'un élément est modifié.
        /// </summary>
        public event EventHandler<ClipboardItem>? ItemModified;

        /// <summary>
        /// Événement déclenché lorsque l'état d'épinglage d'un élément change.
        /// </summary>
        public event EventHandler<ItemPinStateChangedEventArgs>? ItemPinStateChanged;

        /// <summary>
        /// Événement déclenché lorsqu'une synchronisation est nécessaire.
        /// </summary>
        public event EventHandler<SyncRequiredEventArgs>? SyncRequired;

        /// <summary>
        /// Événement déclenché pour demander la fermeture du dialogue.
        /// </summary>
        public event EventHandler? RequestCloseDialog;

        #endregion

        #region Méthodes de Gestion des Événements (IEventViewModelManager)

        /// <summary>
        /// Démarre la gestion des événements.
        /// </summary>
        public void StartEventHandling()
        {
            if (_isDisposed) return;

            IsEventHandlingActive = true;
            SubscribeToSystemEvents();
        }

        /// <summary>
        /// Arrête la gestion des événements.
        /// </summary>
        public void StopEventHandling()
        {
            if (_isDisposed) return;

            IsEventHandlingActive = false;
            UnsubscribeFromSystemEvents();
        }

        /// <summary>
        /// Traite un événement de changement d'historique.
        /// </summary>
        /// <param name="reason">Raison du changement</param>
        public void HandleHistoryChanged(string reason)
        {
            if (_isDisposed || !_isEventHandlingActive) return;

            _processedEventCount++;
            OnPropertyChanged(nameof(ProcessedEventCount));

            // Déclencher l'événement
            HistoryChanged?.Invoke(this, EventArgs.Empty);

            // Synchronisation automatique si activée
            if (_isAutoSyncEnabled)
            {
                SyncRequired?.Invoke(this, new SyncRequiredEventArgs(
                    reason, "HistoryChanged", SyncPriority.Normal));
            }
        }

        /// <summary>
        /// Traite un événement de changement de sélection.
        /// </summary>
        /// <param name="newSelection">Nouvel élément sélectionné</param>
        /// <param name="oldSelection">Ancien élément sélectionné</param>
        public void HandleSelectionChanged(ClipboardItem? newSelection, ClipboardItem? oldSelection)
        {
            if (_isDisposed || !_isEventHandlingActive) return;

            _processedEventCount++;
            OnPropertyChanged(nameof(ProcessedEventCount));

            // Déclencher l'événement
            SelectionChanged?.Invoke(this, newSelection);
        }

        /// <summary>
        /// Traite un événement d'ajout d'élément.
        /// </summary>
        /// <param name="item">Élément ajouté</param>
        /// <param name="source">Source de l'ajout</param>
        public void HandleItemAdded(ClipboardItem item, string source)
        {
            if (_isDisposed || !_isEventHandlingActive || item == null) return;

            _processedEventCount++;
            OnPropertyChanged(nameof(ProcessedEventCount));

            // Déclencher l'événement
            ItemAdded?.Invoke(this, item);

            // Déclencher aussi le changement d'historique
            HandleHistoryChanged($"Item added from {source}");
        }

        /// <summary>
        /// Traite un événement de suppression d'élément.
        /// </summary>
        /// <param name="item">Élément supprimé</param>
        /// <param name="source">Source de la suppression</param>
        public void HandleItemRemoved(ClipboardItem item, string source)
        {
            if (_isDisposed || !_isEventHandlingActive || item == null) return;

            _processedEventCount++;
            OnPropertyChanged(nameof(ProcessedEventCount));

            // Déclencher l'événement
            ItemRemoved?.Invoke(this, item);

            // Déclencher aussi le changement d'historique
            HandleHistoryChanged($"Item removed from {source}");
        }

        /// <summary>
        /// Traite un événement de modification d'élément.
        /// </summary>
        /// <param name="item">Élément modifié</param>
        /// <param name="changeType">Type de modification</param>
        public void HandleItemModified(ClipboardItem item, string changeType)
        {
            if (_isDisposed || !_isEventHandlingActive || item == null) return;

            _processedEventCount++;
            OnPropertyChanged(nameof(ProcessedEventCount));

            // Déclencher l'événement
            ItemModified?.Invoke(this, item);

            // Traitement spécial pour les changements d'épinglage
            if (changeType.Contains("Pin", StringComparison.OrdinalIgnoreCase))
            {
                var wasPinned = changeType.Contains("Unpin", StringComparison.OrdinalIgnoreCase);
                var isPinned = !wasPinned;
                
                ItemPinStateChanged?.Invoke(this, new ItemPinStateChangedEventArgs(
                    item, wasPinned, isPinned));
            }

            // Déclencher aussi le changement d'historique
            HandleHistoryChanged($"Item modified: {changeType}");
        }

        #endregion

        #region Méthodes de Synchronisation (IEventViewModelManager)

        /// <summary>
        /// Force une synchronisation complète de tous les composants.
        /// </summary>
        /// <param name="reason">Raison de la synchronisation</param>
        public void ForceSynchronization(string reason)
        {
            if (_isDisposed) return;

            SyncRequired?.Invoke(this, new SyncRequiredEventArgs(
                reason, "ForceSynchronization", SyncPriority.High));
        }

        /// <summary>
        /// Synchronise l'historique avec la source de données.
        /// </summary>
        /// <param name="reason">Raison de la synchronisation</param>
        public void SynchronizeHistory(string reason)
        {
            if (_isDisposed) return;

            SyncRequired?.Invoke(this, new SyncRequiredEventArgs(
                reason, "SynchronizeHistory", SyncPriority.Normal));
        }

        /// <summary>
        /// Synchronise la sélection entre les composants.
        /// </summary>
        /// <param name="selectedItem">Élément à synchroniser</param>
        public void SynchronizeSelection(ClipboardItem? selectedItem)
        {
            if (_isDisposed) return;

            HandleSelectionChanged(selectedItem, null);
        }

        /// <summary>
        /// Vérifie si une synchronisation est nécessaire.
        /// </summary>
        /// <returns>True si une synchronisation est nécessaire</returns>
        public bool IsSynchronizationRequired()
        {
            // Logique simple pour déterminer si une synchronisation est nécessaire
            // Cette méthode peut être enrichie selon les besoins spécifiques
            return _isAutoSyncEnabled && _isEventHandlingActive;
        }

        #endregion

        #region Méthodes de Notification (IEventViewModelManager)

        /// <summary>
        /// Notifie tous les abonnés d'un changement d'historique.
        /// </summary>
        /// <param name="reason">Raison du changement</param>
        public void NotifyHistoryChanged(string reason)
        {
            HandleHistoryChanged(reason);
        }

        /// <summary>
        /// Notifie tous les abonnés d'un changement de sélection.
        /// </summary>
        /// <param name="newSelection">Nouvel élément sélectionné</param>
        public void NotifySelectionChanged(ClipboardItem? newSelection)
        {
            HandleSelectionChanged(newSelection, null);
        }

        /// <summary>
        /// Notifie tous les abonnés qu'un élément a été ajouté.
        /// </summary>
        /// <param name="item">Élément ajouté</param>
        public void NotifyItemAdded(ClipboardItem item)
        {
            HandleItemAdded(item, "Notification");
        }

        /// <summary>
        /// Notifie tous les abonnés qu'un élément a été supprimé.
        /// </summary>
        /// <param name="item">Élément supprimé</param>
        public void NotifyItemRemoved(ClipboardItem item)
        {
            HandleItemRemoved(item, "Notification");
        }

        /// <summary>
        /// Notifie tous les abonnés qu'un élément a été modifié.
        /// </summary>
        /// <param name="item">Élément modifié</param>
        /// <param name="changeType">Type de modification</param>
        public void NotifyItemModified(ClipboardItem item, string changeType)
        {
            HandleItemModified(item, changeType);
        }

        /// <summary>
        /// Déclenche l'événement RequestCloseDialog.
        /// </summary>
        public void TriggerRequestCloseDialog()
        {
            if (_isDisposed) return;

            RequestCloseDialog?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region Méthodes d'Abonnement (IEventViewModelManager)

        /// <summary>
        /// S'abonne aux événements d'un autre manager.
        /// </summary>
        /// <param name="manager">Manager auquel s'abonner</param>
        public void SubscribeToManager(object manager)
        {
            if (_isDisposed || manager == null) return;

            // Logique d'abonnement spécifique selon le type de manager
            // Cette méthode sera enrichie lors de l'intégration
        }

        /// <summary>
        /// Se désabonne des événements d'un manager.
        /// </summary>
        /// <param name="manager">Manager duquel se désabonner</param>
        public void UnsubscribeFromManager(object manager)
        {
            if (_isDisposed || manager == null) return;

            // Logique de désabonnement spécifique selon le type de manager
            // Cette méthode sera enrichie lors de l'intégration
        }

        /// <summary>
        /// S'abonne aux événements système pertinents.
        /// </summary>
        public void SubscribeToSystemEvents()
        {
            if (_isDisposed) return;

            // S'abonner aux événements système (clipboard, application, etc.)
            // Cette méthode sera enrichie selon les besoins spécifiques
        }

        /// <summary>
        /// Se désabonne des événements système.
        /// </summary>
        public void UnsubscribeFromSystemEvents()
        {
            if (_isDisposed) return;

            // Se désabonner des événements système
            // Cette méthode sera enrichie selon les besoins spécifiques
        }

        #endregion

        #region Méthodes d'Initialisation et Nettoyage (IEventViewModelManager)

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        public void Initialize()
        {
            if (_isDisposed) return;

            StartEventHandling();
        }

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        public void Cleanup()
        {
            if (_isDisposed) return;

            StopEventHandling();
            _processedEventCount = 0;
        }

        /// <summary>
        /// Libère les ressources utilisées par le manager.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            Cleanup();
            _isDisposed = true;
        }

        #endregion
    }
}
