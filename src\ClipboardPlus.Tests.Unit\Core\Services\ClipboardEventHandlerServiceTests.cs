using NUnit.Framework;
using System;
using System.Threading.Tasks;
using Moq;
using ClipboardPlus.Core.Services;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests unitaires pour ClipboardEventHandlerService.
    /// Ces tests valident que le service reproduit exactement le comportement
    /// de la méthode ClipboardListener_ClipboardContentChanged de la classe App.
    /// </summary>
    [TestFixture]
    public class ClipboardEventHandlerServiceTests
    {
        private Mock<IServiceProvider> _mockServiceProvider = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IApplicationLifetimeManager> _mockLifetimeManager = null!;
        private Mock<IClipboardProcessorService> _mockClipboardProcessor = null!;
        private Mock<IGlobalExceptionManager> _mockExceptionManager = null!;
        private ClipboardEventHandlerService _service = null!;

        [SetUp]
        public void SetUp()
        {
            // Créer les mocks
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockLifetimeManager = new Mock<IApplicationLifetimeManager>();
            _mockClipboardProcessor = new Mock<IClipboardProcessorService>();
            _mockExceptionManager = new Mock<IGlobalExceptionManager>();

            // Par défaut, le ServiceProvider retourne null pour IApplicationLifetimeManager
            // (les tests spécifiques peuvent override ce comportement)
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns((IApplicationLifetimeManager?)null);

            // Créer le service avec les mocks
            _service = new ClipboardEventHandlerService(
                _mockServiceProvider.Object,
                _mockLoggingService.Object,
                _mockExceptionManager.Object);
        }

        /// <summary>
        /// Test : Comportement avec LifetimeManager disponible dès le premier appel
        /// </summary>
        [Test]
        public async Task HandleClipboardContentChangedAsync_WithAvailableLifetimeManager_ShouldCallProcessClipboardContentAsync()
        {
            // Arrange - Créer un nouveau service avec LifetimeManager disponible dès l'initialisation
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);
            _mockLifetimeManager.Setup(lm => lm.ProcessClipboardContentAsync())
                .Returns(Task.CompletedTask);

            // Créer un nouveau service avec le LifetimeManager configuré
            var serviceWithLifetimeManager = new ClipboardEventHandlerService(
                _mockServiceProvider.Object,
                _mockLoggingService.Object,
                _mockExceptionManager.Object);

            // Act
            await serviceWithLifetimeManager.HandleClipboardContentChangedAsync(this, EventArgs.Empty);

            // Assert
            _mockLifetimeManager.Verify(lm => lm.ProcessClipboardContentAsync(), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(s => s.Contains("Traitement du contenu du presse-papiers via _lifetimeManager"))),
                Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(s => s.Contains("Traitement terminé avec succès"))),
                Times.Once);
        }

        /// <summary>
        /// Test : Vérification que le LifetimeManager est récupéré lors de l'initialisation
        /// </summary>
        [Test]
        public void Constructor_WithAvailableLifetimeManager_ShouldInitializeSuccessfully()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);

            // Act
            var service = new ClipboardEventHandlerService(
                _mockServiceProvider.Object,
                _mockLoggingService.Object,
                _mockExceptionManager.Object);

            // Assert - L'implémentation peut appeler GetService plusieurs fois
            _mockServiceProvider.Verify(sp => sp.GetService(typeof(IApplicationLifetimeManager)), Times.AtLeastOnce);
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(s => s.Contains("IApplicationLifetimeManager initialisé avec succès"))),
                Times.Once);
        }

        /// <summary>
        /// Test : Fallback vers ClipboardProcessorService quand LifetimeManager n'est pas disponible
        /// </summary>
        [Test]
        public async Task HandleClipboardContentChangedAsync_WithNoLifetimeManager_ShouldFallbackToClipboardProcessor()
        {
            // Arrange - Le service par défaut n'a pas de LifetimeManager (configuré dans SetUp)
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IClipboardProcessorService)))
                .Returns(_mockClipboardProcessor.Object);
            _mockClipboardProcessor.Setup(cp => cp.ProcessCurrentClipboardContentAsync())
                .Returns(Task.CompletedTask);

            // Act
            await _service.HandleClipboardContentChangedAsync(this, EventArgs.Empty);

            // Assert - Vérifier les messages de log corrects selon l'implémentation actuelle
            _mockLoggingService.Verify(ls => ls.LogError(
                It.Is<string>(s => s.Contains("LifetimeManager non disponible - service non initialisé")),
                null),
                Times.Once);
            _mockLoggingService.Verify(ls => ls.LogWarning(
                It.Is<string>(s => s.Contains("LifetimeManager n'est pas disponible"))),
                Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(s => s.Contains("Tentative de traitement direct via IClipboardProcessorService"))),
                Times.Once);
            _mockClipboardProcessor.Verify(cp => cp.ProcessCurrentClipboardContentAsync(), Times.Once);
        }

        /// <summary>
        /// Test : Gestion d'erreurs avec GlobalExceptionManager
        /// </summary>
        [Test]
        public async Task HandleClipboardContentChangedAsync_WithProcessingException_ShouldUseGlobalExceptionManager()
        {
            // Arrange
            var testException = new InvalidOperationException("Test exception");
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);
            _mockLifetimeManager.Setup(lm => lm.ProcessClipboardContentAsync())
                .ThrowsAsync(testException);

            // Créer un service avec LifetimeManager disponible
            var serviceWithLifetimeManager = new ClipboardEventHandlerService(
                _mockServiceProvider.Object,
                _mockLoggingService.Object,
                _mockExceptionManager.Object);

            // Act
            await serviceWithLifetimeManager.HandleClipboardContentChangedAsync(this, EventArgs.Empty);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogError(
                It.Is<string>(s => s.Contains("Erreur lors du traitement du contenu du presse-papiers")),
                testException),
                Times.Once);
            _mockExceptionManager.Verify(em => em.LogUnhandledException(
                "ClipboardChanged", testException, false),
                Times.Once);
        }

        /// <summary>
        /// Test : Aucun service disponible
        /// </summary>
        [Test]
        public async Task HandleClipboardContentChangedAsync_WithNoServicesAvailable_ShouldLogError()
        {
            // Arrange - Le service par défaut n'a pas de LifetimeManager, et on configure aussi l'absence de ClipboardProcessor
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IClipboardProcessorService)))
                .Returns((IClipboardProcessorService?)null);

            // Act
            await _service.HandleClipboardContentChangedAsync(this, EventArgs.Empty);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogError(
                It.Is<string>(s => s.Contains("Aucun moyen de traiter le contenu du presse-papiers")),
                null),
                Times.Once);
        }

        /// <summary>
        /// Test : Appels multiples avec LifetimeManager initialisé - doit utiliser la même instance
        /// </summary>
        [Test]
        public async Task HandleClipboardContentChangedAsync_MultipleCallsWithInitializedLifetimeManager_ShouldUseConsistentInstance()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationLifetimeManager)))
                .Returns(_mockLifetimeManager.Object);
            _mockLifetimeManager.Setup(lm => lm.ProcessClipboardContentAsync())
                .Returns(Task.CompletedTask);

            // Créer un service avec LifetimeManager disponible
            var serviceWithLifetimeManager = new ClipboardEventHandlerService(
                _mockServiceProvider.Object,
                _mockLoggingService.Object,
                _mockExceptionManager.Object);

            // Act - Appels multiples
            await serviceWithLifetimeManager.HandleClipboardContentChangedAsync(this, EventArgs.Empty);
            await serviceWithLifetimeManager.HandleClipboardContentChangedAsync(this, EventArgs.Empty);

            // Assert - LifetimeManager doit être appelé deux fois
            _mockLifetimeManager.Verify(lm => lm.ProcessClipboardContentAsync(), Times.Exactly(2));
        }

        /// <summary>
        /// Test : Validation des arguments du constructeur
        /// </summary>
        [Test]
        public void Constructor_WithNullServiceProvider_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new ClipboardEventHandlerService(null!, _mockLoggingService.Object, _mockExceptionManager.Object));
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new ClipboardEventHandlerService(_mockServiceProvider.Object, null!, _mockExceptionManager.Object));
        }

        [Test]
        public void Constructor_WithNullExceptionManager_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new ClipboardEventHandlerService(_mockServiceProvider.Object, _mockLoggingService.Object, null!));
        }
    }
}
