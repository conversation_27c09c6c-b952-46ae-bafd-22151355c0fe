// ============================================================================
// HISTORY STATISTICS - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Classe pour les statistiques de l'historique
// 📋 RESPONSABILITÉ : Fournir des métriques sur l'historique
// 🏗️ ARCHITECTURE : Support pour IHistoryViewModelManager
//
// ============================================================================

using System;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.UI.ViewModels.Managers
{
    /// <summary>
    /// Statistiques de l'historique du presse-papiers.
    /// </summary>
    public class HistoryStatistics
    {
        /// <summary>
        /// Nombre total d'éléments dans l'historique.
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// Nombre d'éléments visibles après filtrage.
        /// </summary>
        public int FilteredItems { get; set; }

        /// <summary>
        /// Nombre d'éléments épinglés.
        /// </summary>
        public int PinnedItems { get; set; }

        /// <summary>
        /// Nombre d'éléments de type texte.
        /// </summary>
        public int TextItems { get; set; }

        /// <summary>
        /// Nombre d'éléments de type image.
        /// </summary>
        public int ImageItems { get; set; }

        /// <summary>
        /// Nombre d'éléments de type fichier.
        /// </summary>
        public int FileItems { get; set; }

        /// <summary>
        /// Élément le plus récent.
        /// </summary>
        public ClipboardItem? MostRecentItem { get; set; }

        /// <summary>
        /// Élément le plus ancien.
        /// </summary>
        public ClipboardItem? OldestItem { get; set; }

        /// <summary>
        /// Taille totale estimée en octets.
        /// </summary>
        public long TotalSizeBytes { get; set; }

        /// <summary>
        /// Filtre de recherche actuel.
        /// </summary>
        public string? CurrentFilter { get; set; }

        /// <summary>
        /// Timestamp de la dernière mise à jour des statistiques.
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Constructeur par défaut.
        /// </summary>
        public HistoryStatistics()
        {
            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// Pourcentage d'éléments filtrés.
        /// </summary>
        public double FilteredPercentage => TotalItems > 0 ? (double)FilteredItems / TotalItems * 100 : 0;

        /// <summary>
        /// Pourcentage d'éléments épinglés.
        /// </summary>
        public double PinnedPercentage => TotalItems > 0 ? (double)PinnedItems / TotalItems * 100 : 0;

        /// <summary>
        /// Indique si un filtre est actif.
        /// </summary>
        public bool IsFiltered => !string.IsNullOrEmpty(CurrentFilter);

        /// <summary>
        /// Taille moyenne par élément en octets.
        /// </summary>
        public double AverageSizeBytes => TotalItems > 0 ? (double)TotalSizeBytes / TotalItems : 0;

        /// <summary>
        /// Retourne une représentation textuelle des statistiques.
        /// </summary>
        public override string ToString()
        {
            return $"Total: {TotalItems}, Filtered: {FilteredItems}, Pinned: {PinnedItems}, Size: {TotalSizeBytes:N0} bytes";
        }
    }
}
