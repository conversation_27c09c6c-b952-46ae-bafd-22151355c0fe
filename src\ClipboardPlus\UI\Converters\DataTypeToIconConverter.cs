using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.UI.Converters
{
    /// <summary>
    /// Convertit un ClipboardDataType en ImageSource pour afficher l'icône appropriée.
    /// </summary>
    public class DataTypeToIconConverter : IValueConverter
    {
        private static readonly System.Windows.Media.FontFamily SegoeFluentIcons = new System.Windows.Media.FontFamily("Segoe MDL2 Assets");

        /// <summary>
        /// Convertit un ClipboardDataType en un contrôle d'icône approprié.
        /// </summary>
        /// <param name="value">La valeur à convertir, doit être de type ClipboardDataType.</param>
        /// <param name="targetType">Le type cible de la conversion.</param>
        /// <param name="parameter">Un paramètre facultatif pour la taille de l'icône.</param>
        /// <param name="culture">Les informations culturelles.</param>
        /// <returns>Un élément visuel (Path ou TextBlock) avec l'icône appropriée.</returns>
        public object? Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not ClipboardDataType dataType)
            {
                return null;
            }

            // Retourner directement le caractère d'icône
            return GetIconCharacter(dataType);
        }

        /// <summary>
        /// Obtient le caractère d'icône en fonction du type de données.
        /// </summary>
        private string GetIconCharacter(ClipboardDataType dataType)
        {
            return dataType switch
            {
                ClipboardDataType.Text => "\uE8C4",      // Texte
                ClipboardDataType.Rtf => "\uE8E5",       // Texte enrichi
                ClipboardDataType.Html => "\uE8CD",      // HTML
                ClipboardDataType.Image => "\uEB9F",     // Image
                ClipboardDataType.FilePath => "\uEC50",  // Fichiers
                _ => "\uEA92",                          // Par défaut/Inconnu
            };
        }

        /// <summary>
        /// Non implémenté - conversion retour non prise en charge.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("La conversion inverse n'est pas prise en charge.");
        }
    }
} 