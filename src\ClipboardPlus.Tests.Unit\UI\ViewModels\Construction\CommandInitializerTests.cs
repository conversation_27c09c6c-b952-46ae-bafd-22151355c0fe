using NUnit.Framework;
using ClipboardPlus.UI.ViewModels.Construction.Implementations;
using ClipboardPlus.UI.ViewModels.Construction.Interfaces;
using ClipboardPlus.UI.ViewModels.Construction;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Tests.Unit.Helpers;
using Moq;
using System;

namespace ClipboardPlus.Tests.Unit.UI.ViewModels.Construction
{
    /// <summary>
    /// Tests unitaires pour CommandInitializer.
    /// Valide l'implémentation complète de l'Étape 1.1 de la Phase 1.
    /// </summary>
    [TestFixture]
    public class CommandInitializerTests
    {
        private ICommandInitializer _commandInitializer = null!;
        private ClipboardHistoryViewModel _viewModel = null!;
        private Mock<IClipboardHistoryManager> _mockHistoryManager = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IClipboardInteractionService> _mockClipboardService = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IUserNotificationService> _mockNotificationService = null!;
        private Mock<IUserInteractionService> _mockUserInteractionService = null!;
        private Mock<IRenameService> _mockRenameService = null!;

        [SetUp]
        public void SetUp()
        {
            _commandInitializer = new CommandInitializer();
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockClipboardService = new Mock<IClipboardInteractionService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockNotificationService = new Mock<IUserNotificationService>();
            _mockUserInteractionService = new Mock<IUserInteractionService>();
            _mockRenameService = new Mock<IRenameService>();

            // Utiliser TestServiceProviderHelper pour créer un ServiceProvider avec les modules configurés
            var serviceProvider = TestServiceProviderHelper.CreateMockServiceProvider();

            // Créer un ViewModel minimal pour les tests en utilisant la Factory SOLID
            _viewModel = ClipboardHistoryViewModelFactory.CreateWithSOLIDArchitecture(
                _mockHistoryManager.Object,
                _mockClipboardService.Object,
                _mockSettingsManager.Object,
                _mockNotificationService.Object,
                _mockUserInteractionService.Object,
                serviceProvider,
                _mockRenameService.Object,
                null, // deletionResultLogger
                null, // collectionHealthService
                null, // visibilityStateManager
                null, // newItemCreationOrchestrator
                null  // testEnvironmentDetector
            );
        }

        [Test]
        public void InitializeAllCommands_WithValidViewModel_ShouldInitializeAllCommands()
        {
            // Act
            _commandInitializer.InitializeAllCommands(_viewModel);

            // Assert - Vérifier que toutes les commandes sont initialisées
            Assert.That(_viewModel.DemarrerRenommageCommand, Is.Not.Null, "DemarrerRenommageCommand doit être initialisé");
            Assert.That(_viewModel.ConfirmerRenommageCommand, Is.Not.Null, "ConfirmerRenommageCommand doit être initialisé");
            Assert.That(_viewModel.AnnulerRenommageCommand, Is.Not.Null, "AnnulerRenommageCommand doit être initialisé");
            
            Assert.That(_viewModel.PrepareNewItemCommand, Is.Not.Null, "PrepareNewItemCommand doit être initialisé");
            Assert.That(_viewModel.FinalizeAndSaveNewItemCommand, Is.Not.Null, "FinalizeAndSaveNewItemCommand doit être initialisé");
            Assert.That(_viewModel.DiscardNewItemCreationCommand, Is.Not.Null, "DiscardNewItemCreationCommand doit être initialisé");
            
            Assert.That(_viewModel.PasteSelectedItemCommand, Is.Not.Null, "PasteSelectedItemCommand doit être initialisé");
            Assert.That(_viewModel.BasculerEpinglageCommand, Is.Not.Null, "BasculerEpinglageCommand doit être initialisé");
            Assert.That(_viewModel.SupprimerElementCommand, Is.Not.Null, "SupprimerElementCommand doit être initialisé");
            Assert.That(_viewModel.SupprimerToutCommand, Is.Not.Null, "SupprimerToutCommand doit être initialisé");
        }

        [Test]
        public void InitializeAllCommands_WithNullViewModel_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => 
                _commandInitializer.InitializeAllCommands(null!));
            
            Assert.That(exception.ParamName, Is.EqualTo("viewModel"));
        }

        [Test]
        public void InitializeAllCommands_CalledMultipleTimes_ShouldReinitializeCommands()
        {
            // Arrange - Première initialisation
            _commandInitializer.InitializeAllCommands(_viewModel);
            var firstDemarrerCommand = _viewModel.DemarrerRenommageCommand;

            // Act - Deuxième initialisation
            _commandInitializer.InitializeAllCommands(_viewModel);

            // Assert - Les commandes doivent être réinitialisées (nouvelles instances)
            Assert.That(_viewModel.DemarrerRenommageCommand, Is.Not.Null);
            Assert.That(_viewModel.DemarrerRenommageCommand, Is.Not.SameAs(firstDemarrerCommand), 
                "Les commandes doivent être réinitialisées à chaque appel");
        }

        [Test]
        public void InitializeAllCommands_ShouldFollowCorrectOrder()
        {
            // Arrange - La Factory SOLID initialise déjà les commandes, donc on teste la réinitialisation
            var serviceProvider = TestServiceProviderHelper.CreateMockServiceProvider();
            var viewModel = ClipboardHistoryViewModelFactory.CreateWithSOLIDArchitecture(
                _mockHistoryManager.Object,
                _mockClipboardService.Object,
                _mockSettingsManager.Object,
                _mockNotificationService.Object,
                _mockUserInteractionService.Object,
                serviceProvider,
                _mockRenameService.Object,
                null, null, null, null, null
            );

            // Vérifier que les commandes sont déjà initialisées par la Factory SOLID
            Assert.That(viewModel.DemarrerRenommageCommand, Is.Not.Null, "Factory SOLID doit initialiser les commandes de renommage");
            Assert.That(viewModel.PrepareNewItemCommand, Is.Not.Null, "Factory SOLID doit initialiser les commandes de nouvel élément");
            Assert.That(viewModel.PasteSelectedItemCommand, Is.Not.Null, "Factory SOLID doit initialiser les commandes restantes");

            // Sauvegarder les références des commandes actuelles
            var originalDemarrerCommand = viewModel.DemarrerRenommageCommand;
            var originalPrepareCommand = viewModel.PrepareNewItemCommand;
            var originalPasteCommand = viewModel.PasteSelectedItemCommand;

            // Act - Réinitialiser les commandes
            _commandInitializer.InitializeAllCommands(viewModel);

            // Assert - Toutes les commandes doivent être réinitialisées (nouvelles instances)
            Assert.That(viewModel.DemarrerRenommageCommand, Is.Not.Null, "Commandes de renommage doivent être réinitialisées");
            Assert.That(viewModel.PrepareNewItemCommand, Is.Not.Null, "Commandes de nouvel élément doivent être réinitialisées");
            Assert.That(viewModel.PasteSelectedItemCommand, Is.Not.Null, "Commandes restantes doivent être réinitialisées");

            // Vérifier que ce sont de nouvelles instances (réinitialisation effective)
            Assert.That(viewModel.DemarrerRenommageCommand, Is.Not.SameAs(originalDemarrerCommand), "DemarrerRenommageCommand doit être une nouvelle instance");
            Assert.That(viewModel.PrepareNewItemCommand, Is.Not.SameAs(originalPrepareCommand), "PrepareNewItemCommand doit être une nouvelle instance");
            Assert.That(viewModel.PasteSelectedItemCommand, Is.Not.SameAs(originalPasteCommand), "PasteSelectedItemCommand doit être une nouvelle instance");
        }

        [Test]
        public void InitializeAllCommands_Performance_ShouldCompleteQuickly()
        {
            // Arrange
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            _commandInitializer.InitializeAllCommands(_viewModel);

            // Assert
            stopwatch.Stop();
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(100), 
                "L'initialisation des commandes doit être rapide (< 100ms)");
        }

        [TearDown]
        public void TearDown()
        {
            _viewModel?.Dispose();
        }
    }
}
