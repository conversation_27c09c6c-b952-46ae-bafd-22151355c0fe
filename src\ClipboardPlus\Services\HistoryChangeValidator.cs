using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation du service de validation des conditions préalables
    /// pour le traitement des changements d'historique.
    /// 
    /// Ce service extrait et isole la logique de validation complexe
    /// de la méthode ClipboardHistoryManager_HistoryChanged originale.
    /// </summary>
    public class HistoryChangeValidator : IHistoryChangeValidator
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du validateur de changements d'historique.
        /// </summary>
        /// <param name="loggingService">Service de logging pour tracer les validations</param>
        public HistoryChangeValidator(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Valide si le changement d'historique peut être traité en vérifiant
        /// toutes les conditions préalables.
        /// </summary>
        /// <param name="context">Contexte contenant l'état actuel du système</param>
        /// <returns>Résultat de validation avec raison détaillée si rejeté</returns>
        public ValidationResult ValidateHistoryChange(HistoryChangeContext context)
        {
            if (context == null)
            {
                var reason = "Le contexte de validation est null";
                _loggingService.LogWarning($"[HistoryChangeValidator] {reason}");
                return ValidationResult.Rejected(reason);
            }

            // Vérification 1: Réaction préventive activée
            if (context.PreventReaction)
            {
                var reason = "_preventHistoryChangedReaction est true";
                _loggingService.LogInfo($"[HistoryChangeValidator] Validation rejetée: {reason}");
                return ValidationResult.Rejected(reason);
            }

            // Vérification 2: Opérations de mise à jour en cours
            if (context.IsUpdatingItem || context.IsReorderingItems)
            {
                var operation = context.IsUpdatingItem ? "_isUpdatingItem" : "_isReorderingItems";
                var reason = $"{operation} est true";
                _loggingService.LogInfo($"[HistoryChangeValidator] Validation rejetée: {reason}");
                return ValidationResult.Rejected(reason);
            }

            // Vérification 3: Gestionnaire d'historique disponible
            if (context.HistoryManager == null)
            {
                var reason = "_clipboardHistoryManager est null";
                _loggingService.LogWarning($"[HistoryChangeValidator] Validation rejetée: {reason}");
                return ValidationResult.Rejected(reason);
            }

            // Vérification 4: Opérations en cours
            if (context.IsOperationInProgress || context.IsItemPasteInProgress)
            {
                var operation = context.IsOperationInProgress ? "IsOperationInProgress" : "_isItemPasteInProgress";
                var reason = $"Une opération est en cours ({operation})";
                _loggingService.LogInfo($"[HistoryChangeValidator] Validation rejetée: {reason}");
                return ValidationResult.Rejected(reason);
            }

            // Vérification 5: Validation des éléments d'historique
            if (context.HistoryManager.HistoryItems == null)
            {
                var reason = "_clipboardHistoryManager.HistoryItems est null";
                _loggingService.LogWarning($"[HistoryChangeValidator] Validation rejetée: {reason}");
                return ValidationResult.Rejected(reason);
            }

            // Toutes les validations sont passées
            _loggingService.LogInfo("[HistoryChangeValidator] Validation réussie: toutes les conditions sont remplies");
            return ValidationResult.Accepted();
        }
    }
}
