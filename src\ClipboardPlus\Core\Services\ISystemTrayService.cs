using System;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour le service de gestion de l'icône dans la zone de notification.
    /// </summary>
    public interface ISystemTrayService
    {
        /// <summary>
        /// Initialise l'icône dans la zone de notification.
        /// </summary>
        void Initialize();

        /// <summary>
        /// Affiche une notification dans la zone de notification.
        /// </summary>
        /// <param name="title">Titre de la notification.</param>
        /// <param name="message">Message de la notification.</param>
        /// <param name="iconType">Type d'icône à afficher.</param>
        void ShowNotification(string title, string message, System.Windows.Forms.ToolTipIcon iconType);

        /// <summary>
        /// Affiche la fenêtre des paramètres de l'application.
        /// </summary>
        void OpenSettingsWindow();

        /// <summary>
        /// Affiche la fenêtre d'historique du presse-papiers.
        /// </summary>
        Task ShowHistoryWindow();

        /// <summary>
        /// Nettoie les ressources utilisées par le service.
        /// </summary>
        void Dispose();
    }
}