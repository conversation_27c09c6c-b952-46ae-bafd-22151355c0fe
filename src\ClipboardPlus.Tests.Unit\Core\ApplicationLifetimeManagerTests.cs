using System;
using System.Threading;
using System.Threading.Tasks;
using ClipboardPlus.Core;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System.Linq;

namespace ClipboardPlus.Tests.Unit.Core
{
    [TestFixture]
    public class ApplicationLifetimeManagerTests
    {
        private Mock<IServiceProvider> _mockServiceProvider = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IClipboardProcessorService> _mockClipboardProcessorService = null!;
        private Mock<IClipboardListenerService> _mockClipboardListenerService = null!;
        private Mock<ISystemTrayService> _mockSystemTrayService = null!;
        private ClipboardPlus.Core.ApplicationLifetimeManager _lifetimeManager = null!;

        [SetUp]
        public void Setup()
        {
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockClipboardProcessorService = new Mock<IClipboardProcessorService>();
            _mockClipboardListenerService = new Mock<IClipboardListenerService>();
            _mockSystemTrayService = new Mock<ISystemTrayService>();

            // Configuration des mocks (ClipboardMonitorService supprimé)
            _mockServiceProvider
                .Setup(sp => sp.GetService(typeof(IClipboardListenerService)))
                .Returns(_mockClipboardListenerService.Object);
            
            _mockServiceProvider
                .Setup(sp => sp.GetService(typeof(ISystemTrayService)))
                .Returns(_mockSystemTrayService.Object);
            
            _mockClipboardListenerService
                .Setup(cls => cls.StartListening())
                .Returns(true);

            // Créer l'instance à tester
            _lifetimeManager = new ClipboardPlus.Core.ApplicationLifetimeManager(
                _mockServiceProvider.Object,
                _mockLoggingService.Object,
                _mockClipboardProcessorService.Object);
        }

        [Test]
        public async Task InitializeServices_ShouldStartClipboardListenerService()
        {
            // Act
            await _lifetimeManager.InitializeServices(
                _mockServiceProvider.Object,
                (s, e) => { },
                (s, e) => { });

            // Assert
            // Vérifier que StartListening a été appelé
            var startListeningCalls = _mockClipboardListenerService.Invocations
                .Where(i => i.Method.Name == "StartListening")
                .ToList();
                
            Assert.That(startListeningCalls.Count, Is.EqualTo(1), "StartListening aurait dû être appelé exactement une fois");
            
            // Vérifier que le message de log approprié a été journalisé
            var logInfoCalls = _mockLoggingService.Invocations
                .Where(i => i.Method.Name == "LogInfo")
                .ToList();
                
            Assert.That(logInfoCalls.Count, Is.GreaterThanOrEqualTo(1), "LogInfo aurait dû être appelé au moins une fois");
            
            // Vérifier qu'au moins un appel contient le message attendu
            bool successMessageLogged = logInfoCalls
                .Any(call => call.Arguments.Count > 0 && 
                      call.Arguments[0] is string message && 
                      message.Contains("ClipboardListenerService démarré avec succès"));
                      
            Assert.That(successMessageLogged, Is.True, "Un message de démarrage réussi aurait dû être journalisé");
        }

        [Test]
        public async Task InitializeServices_ShouldSubscribeToClipboardEvents()
        {
            // Act
            await _lifetimeManager.InitializeServices(
                _mockServiceProvider.Object,
                (s, e) => { },
                (s, e) => { });

            // Assert
            // Vérifier que l'abonnement aux événements a été effectué (ClipboardMonitorService supprimé)
            _mockClipboardListenerService.VerifyAdd(cls =>
                cls.ClipboardContentChanged += It.IsAny<EventHandler>());
        }

        [Test]
        public void Shutdown_ShouldStopClipboardListenerService()
        {
            // Arrange
            // Simuler que les services ont été initialisés
            _lifetimeManager.InitializeServices(
                _mockServiceProvider.Object,
                (s, e) => { },
                (s, e) => { }).Wait();

            // Act
            _lifetimeManager.Shutdown(_mockServiceProvider.Object, _mockSystemTrayService.Object, null, false);

            // Assert
            // Vérifier que StopListening a été appelé
            var stopListeningCalls = _mockClipboardListenerService.Invocations
                .Where(i => i.Method.Name == "StopListening")
                .ToList();
                
            Assert.That(stopListeningCalls.Count, Is.EqualTo(1), "StopListening aurait dû être appelé exactement une fois");
            
            // Vérifier que le message de log approprié a été journalisé
            var logInfoCalls = _mockLoggingService.Invocations
                .Where(i => i.Method.Name == "LogInfo")
                .ToList();
                
            Assert.That(logInfoCalls.Count, Is.GreaterThanOrEqualTo(1), "LogInfo aurait dû être appelé au moins une fois");
            
            // Vérifier qu'au moins un appel contient le message attendu
            bool shutdownMessageLogged = logInfoCalls
                .Any(call => call.Arguments.Count > 0 && 
                      call.Arguments[0] is string message && 
                      message.Contains("Arrêt du ClipboardListenerService"));
                      
            Assert.That(shutdownMessageLogged, Is.True, "Un message d'arrêt du service aurait dû être journalisé");
        }

        [Test]
        public void Shutdown_ShouldUnsubscribeFromClipboardEvents()
        {
            // Arrange
            // Simuler que les services ont été initialisés
            _lifetimeManager.InitializeServices(
                _mockServiceProvider.Object,
                (s, e) => { },
                (s, e) => { }).Wait();

            // Act
            _lifetimeManager.Shutdown(_mockServiceProvider.Object, _mockSystemTrayService.Object, null, false);

            // Assert
            // Vérifier que le désabonnement aux événements a été effectué (ClipboardMonitorService supprimé)
            _mockClipboardListenerService.VerifyRemove(cls =>
                cls.ClipboardContentChanged -= It.IsAny<EventHandler>());
        }

        [Test]
        public async Task ProcessClipboardContentAsync_ShouldCallProcessorService()
        {
            // Act
            await _lifetimeManager.ProcessClipboardContentAsync();

            // Assert
            _mockClipboardProcessorService.Verify(cps => 
                cps.ProcessCurrentClipboardContentAsync(), 
                Times.Once);
            
            _mockLoggingService.Verify(ls => 
                ls.LogInfo(It.Is<string>(s => s.Contains("Traitement du contenu du presse-papiers"))), 
                Times.AtLeastOnce);
        }

        [Test]
        public async Task ProcessClipboardContentAsync_WhenAlreadyProcessing_ShouldNotProcessTwice()
        {
            // Arrange
            // Simuler un traitement en cours avec un délai
            _mockClipboardProcessorService
                .Setup(cps => cps.ProcessCurrentClipboardContentAsync())
                .Returns(async () => {
                    await Task.Delay(100); // Simuler un traitement qui prend du temps
                });

            // Act - Lancer deux traitements en parallèle
            Task task1 = _lifetimeManager.ProcessClipboardContentAsync();
            Task task2 = _lifetimeManager.ProcessClipboardContentAsync();
            
            await Task.WhenAll(task1, task2);

            // Assert
            _mockClipboardProcessorService.Verify(cps => 
                cps.ProcessCurrentClipboardContentAsync(), 
                Times.Once, 
                "Le service de traitement ne devrait être appelé qu'une seule fois");
            
            _mockLoggingService.Verify(ls => 
                ls.LogInfo(It.Is<string>(s => s.Contains("déjà en cours, ignoré"))), 
                Times.Once, 
                "Le message de log indiquant que le traitement est déjà en cours devrait apparaître");
        }

        [Test]
        public async Task ProcessClipboardContentAsync_AfterCompletion_ShouldAllowNewProcessing()
        {
            // Arrange
            // Premier appel
            await _lifetimeManager.ProcessClipboardContentAsync();
            
            // Réinitialiser les mocks pour vérifier uniquement le second appel
            _mockClipboardProcessorService.Invocations.Clear();
            _mockLoggingService.Invocations.Clear();

            // Act - Second appel après que le premier soit terminé
            await _lifetimeManager.ProcessClipboardContentAsync();

            // Assert
            _mockClipboardProcessorService.Verify(cps => 
                cps.ProcessCurrentClipboardContentAsync(), 
                Times.Once, 
                "Le service de traitement devrait être appelé à nouveau après la fin du premier traitement");
        }

        [Test]
        public async Task ProcessClipboardContentAsync_WhenProcessorThrowsException_ShouldHandleGracefully()
        {
            // Arrange
            _mockClipboardProcessorService
                .Setup(cps => cps.ProcessCurrentClipboardContentAsync())
                .ThrowsAsync(new InvalidOperationException("Test exception"));

            // Act & Assert
            // Utiliser une approche différente pour tester qu'aucune exception n'est levée
            bool exceptionThrown = false;
            try
            {
                await _lifetimeManager.ProcessClipboardContentAsync();
            }
            catch
            {
                exceptionThrown = true;
            }
            
            // La méthode devrait gérer les exceptions sans les propager
            Assert.That(exceptionThrown, Is.False);

            _mockLoggingService.Verify(ls => 
                ls.LogError(It.Is<string>(s => s.Contains("Erreur lors du traitement")), It.IsAny<Exception>()), 
                Times.Once, 
                "L'erreur devrait être journalisée");
        }

        [Test]
        public async Task ClipboardContentChanged_ShouldTriggerProcessing()
        {
            // Arrange
            EventHandler? clipboardEventHandler = null;
            
            _mockClipboardListenerService
                .SetupAdd(cls => cls.ClipboardContentChanged += It.IsAny<EventHandler>())
                .Callback<EventHandler>(handler => clipboardEventHandler = handler);
            
            // Initialiser les services pour s'abonner aux événements
            await _lifetimeManager.InitializeServices(
                _mockServiceProvider.Object,
                (s, e) => { },
                (s, e) => { });
            
            // Réinitialiser les mocks pour vérifier uniquement l'appel suite à l'événement
            _mockClipboardProcessorService.Invocations.Clear();
            _mockLoggingService.Invocations.Clear();

            // Act - Déclencher l'événement de changement du presse-papiers
            clipboardEventHandler?.Invoke(this, EventArgs.Empty);
            
            // Laisser le temps à l'événement asynchrone de s'exécuter
            await Task.Delay(50);

            // Assert
            _mockLoggingService.Verify(ls => 
                ls.LogInfo(It.Is<string>(s => s.Contains("Événement de changement du presse-papiers reçu"))), 
                Times.Once);
            
            _mockClipboardProcessorService.Verify(cps => 
                cps.ProcessCurrentClipboardContentAsync(), 
                Times.Once,
                "Le service de traitement devrait être appelé suite à l'événement de changement du presse-papiers");
        }

        [Test]
        public async Task InitializeServices_WhenListenerFailsToStart_ShouldLogError()
        {
            // Arrange
            _mockClipboardListenerService
                .Setup(cls => cls.StartListening())
                .Returns(false);

            // Act
            await _lifetimeManager.InitializeServices(
                _mockServiceProvider.Object,
                (s, e) => { },
                (s, e) => { });

            // Assert
            // Vérifier que le message d'erreur approprié est journalisé
            var errorCalls = _mockLoggingService.Invocations
                .Where(i => i.Method.Name == "LogError")
                .ToList();
                
            Assert.That(errorCalls.Count, Is.EqualTo(1), "LogError aurait dû être appelé exactement une fois");
        }

        [Test]
        public void Shutdown_WithMutex_ShouldReleaseMutex()
        {
            // Arrange
            // Nous ne pouvons pas mocker Mutex, donc nous allons simplement vérifier que le log est appelé
            
            // Act
            _lifetimeManager.Shutdown(
                _mockServiceProvider.Object, 
                _mockSystemTrayService.Object, 
                null, // Pas de mutex réel pour le test
                false);
            
            // Assert
            // Vérifier que LogInfo a été appelé avec un message concernant la libération du mutex
            _mockLoggingService.Verify(ls => 
                ls.LogInfo(It.IsAny<string>()), 
                Times.AtLeastOnce);
        }

        [Test]
        public void Shutdown_WhenExceptionOccurs_ShouldLogErrorAndContinue()
        {
            // Arrange
            // Configurer le mock pour qu'il accepte l'appel à LogError
            _mockLoggingService.Setup(ls => ls.LogError(It.IsAny<string>(), It.IsAny<Exception>()));
            
            // Configurer le mock pour qu'il lance une exception
            _mockSystemTrayService
                .Setup(sts => sts.Dispose())
                .Throws(new InvalidOperationException("Test exception"));

            // Act & Assert
            // Utiliser une approche différente pour tester qu'aucune exception n'est levée
            Assert.DoesNotThrow(() => {
                _lifetimeManager.Shutdown(
                    _mockServiceProvider.Object, 
                    _mockSystemTrayService.Object, 
                    null, 
                    false);
            }, "La méthode devrait gérer les exceptions sans les propager");

            // Vérifier que LogError a été appelé
            _mockLoggingService.Verify(ls => ls.LogError(
                It.IsAny<string>(), 
                It.IsAny<Exception>()), 
                Times.Once);
        }
    }
} 