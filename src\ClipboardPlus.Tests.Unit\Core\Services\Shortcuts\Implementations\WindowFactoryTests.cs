using System;
using System.Windows;
using ClipboardPlus.Core.Services.Shortcuts.Implementations;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services.Shortcuts.Implementations
{
    [TestFixture]
    [Apartment(System.Threading.ApartmentState.STA)]
    public class WindowFactoryTests
    {
        private WindowFactory _windowFactory = null!;

        [SetUp]
        public void SetUp()
        {
            _windowFactory = new WindowFactory();
        }

        [Test]
        public void Constructor_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new WindowFactory());
        }

        [Test]
        public void CreateHiddenWindow_ReturnsValidWindow()
        {
            // Act
            var window = _windowFactory.CreateHiddenWindow();

            // Assert
            Assert.That(window, Is.Not.Null);
            Assert.That(window.Title, Is.EqualTo("ClipboardPlus Hidden Window"));
            Assert.That(window.Width, Is.EqualTo(1));
            Assert.That(window.Height, Is.EqualTo(1));
            Assert.That(window.Left, Is.EqualTo(-10000));
            Assert.That(window.Top, Is.EqualTo(-10000));
            Assert.That(window.WindowStyle, Is.EqualTo(WindowStyle.None));
            Assert.That(window.ShowInTaskbar, Is.False);
            Assert.That(window.Visibility, Is.EqualTo(System.Windows.Visibility.Hidden));
            Assert.That(window.AllowsTransparency, Is.True);
        }

        [Test]
        public void CreateHiddenWindow_MultipleCalls_ReturnDifferentInstances()
        {
            // Act
            var window1 = _windowFactory.CreateHiddenWindow();
            var window2 = _windowFactory.CreateHiddenWindow();

            // Assert
            Assert.That(window1, Is.Not.Null);
            Assert.That(window2, Is.Not.Null);
            Assert.That(window1, Is.Not.SameAs(window2), "Chaque appel devrait créer une nouvelle instance");
        }

        [Test]
        public void GetWindowHandle_WithNullWindow_ReturnsZero()
        {
            // Act
            var handle = _windowFactory.GetWindowHandle(null!);

            // Assert
            Assert.That(handle, Is.EqualTo(IntPtr.Zero));
        }

        [Test]
        public void GetWindowHandle_WithValidWindow_HandlesGracefully()
        {
            // Arrange
            var window = _windowFactory.CreateHiddenWindow();

            // Act & Assert
            // Dans l'environnement de test, cela peut retourner IntPtr.Zero ou un handle valide
            Assert.DoesNotThrow(() => _windowFactory.GetWindowHandle(window));
        }

        [Test]
        public void ShowAndHideWindow_WithNullWindow_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _windowFactory.ShowAndHideWindow(null!));
        }

        [Test]
        public void ShowAndHideWindow_WithValidWindow_DoesNotThrow()
        {
            // Arrange
            var window = _windowFactory.CreateHiddenWindow();

            // Act & Assert
            Assert.DoesNotThrow(() => _windowFactory.ShowAndHideWindow(window));
        }

        [Test]
        public void ShowAndHideWindow_WithValidWindow_WindowRemainsHidden()
        {
            // Arrange
            var window = _windowFactory.CreateHiddenWindow();

            // Act
            _windowFactory.ShowAndHideWindow(window);

            // Assert
            // Après ShowAndHideWindow, la fenêtre devrait être cachée
            Assert.That(window.Visibility, Is.EqualTo(System.Windows.Visibility.Hidden));
        }

        [Test]
        public void CreateHiddenWindow_WindowHasCorrectProperties()
        {
            // Act
            var window = _windowFactory.CreateHiddenWindow();

            // Assert - Vérifier toutes les propriétés importantes
            Assert.That(window.Title, Is.EqualTo("ClipboardPlus Hidden Window"));
            Assert.That(window.Width, Is.EqualTo(1));
            Assert.That(window.Height, Is.EqualTo(1));
            Assert.That(window.Left, Is.EqualTo(-10000));
            Assert.That(window.Top, Is.EqualTo(-10000));
            Assert.That(window.WindowStyle, Is.EqualTo(WindowStyle.None));
            Assert.That(window.ShowInTaskbar, Is.False);
            Assert.That(window.Visibility, Is.EqualTo(System.Windows.Visibility.Hidden));
            Assert.That(window.AllowsTransparency, Is.True);
            Assert.That(window.Background, Is.EqualTo(System.Windows.Media.Brushes.Transparent));
        }

        [Test]
        public void GetWindowHandle_WithSameWindow_ReturnsConsistentHandle()
        {
            // Arrange
            var window = _windowFactory.CreateHiddenWindow();

            // Act
            var handle1 = _windowFactory.GetWindowHandle(window);
            var handle2 = _windowFactory.GetWindowHandle(window);

            // Assert
            Assert.That(handle2, Is.EqualTo(handle1), "Le même handle devrait être retourné pour la même fenêtre");
        }

        [Test]
        public void WindowFactory_AllMethodsHandleExceptionsGracefully()
        {
            // Arrange
            var window = _windowFactory.CreateHiddenWindow();

            // Act & Assert - Toutes les méthodes devraient gérer les exceptions gracieusement
            Assert.DoesNotThrow(() => _windowFactory.GetWindowHandle(window));
            Assert.DoesNotThrow(() => _windowFactory.ShowAndHideWindow(window));
            Assert.DoesNotThrow(() => _windowFactory.GetWindowHandle(null!));
            Assert.DoesNotThrow(() => _windowFactory.ShowAndHideWindow(null!));
        }

        [TearDown]
        public void TearDown()
        {
            // Nettoyer les ressources si nécessaire
            // Les fenêtres créées dans les tests devraient être automatiquement nettoyées
        }
    }
}
