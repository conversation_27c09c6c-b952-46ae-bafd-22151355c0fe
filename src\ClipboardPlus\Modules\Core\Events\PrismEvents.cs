using Prism.Events;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Modules.Core;
using ClipboardPlus.Modules.Creation;
using System.Collections.Generic;

namespace ClipboardPlus.Modules.Core.Events
{
    /// <summary>
    /// Événement publié quand un nouvel élément est ajouté à l'historique.
    /// </summary>
    public class ItemAddedEvent : PubSubEvent<ClipboardItem>
    {
    }

    /// <summary>
    /// Événement publié quand un élément est supprimé de l'historique.
    /// </summary>
    public class ItemDeletedEvent : PubSubEvent<string>
    {
    }

    /// <summary>
    /// Événement publié quand l'historique est vidé.
    /// </summary>
    public class HistoryClearedEvent : PubSubEvent
    {
    }

    /// <summary>
    /// Événement publié quand un élément est sélectionné.
    /// </summary>
    public class ItemSelectedEvent : PubSubEvent<ClipboardItem>
    {
    }

    /// <summary>
    /// Événement publié quand une commande est exécutée.
    /// </summary>
    public class CommandExecutedEvent : PubSubEvent<string>
    {
    }

    /// <summary>
    /// Événement publié quand le processus de création d'un élément commence.
    /// </summary>
    public class CreationStartedEvent : PubSubEvent<ClipboardItem>
    {
    }

    /// <summary>
    /// Événement publié quand le processus de création d'un élément se termine.
    /// </summary>
    public class CreationCompletedEvent : PubSubEvent<ClipboardItem>
    {
    }

    /// <summary>
    /// Événement publié quand le processus de création d'un élément échoue.
    /// </summary>
    public class CreationFailedEvent : PubSubEvent<string>
    {
    }

    /// <summary>
    /// Événement publié quand l'état de création change.
    /// </summary>
    public class CreationStateChangedEvent : PubSubEvent<string>
    {
    }



    /// <summary>
    /// Événement Prism publié par le module d'historique.
    /// </summary>
    public class HistoryModulePrismEvent : PubSubEvent<HistoryModuleEventData>
    {
    }

    /// <summary>
    /// Événement Prism publié par le module de commandes.
    /// </summary>
    public class CommandModulePrismEvent : PubSubEvent<CommandModuleEventData>
    {
    }

    /// <summary>
    /// Événement Prism publié par le module de création.
    /// </summary>
    public class CreationModulePrismEvent : PubSubEvent<CreationModuleEventData>
    {
    }

    /// <summary>
    /// Événement Prism pour les changements d'état de module.
    /// </summary>
    public class ModuleStateChangedPrismEvent : PubSubEvent<ModuleStateChangedEvent>
    {
    }



    /// <summary>
    /// Données pour l'événement du module d'historique.
    /// </summary>
    public class HistoryModuleEventData
    {
        public string ModuleName { get; set; } = string.Empty;
        public string ChangeType { get; set; } = string.Empty;
        public IEnumerable<ClipboardItem> AffectedItems { get; set; } = new List<ClipboardItem>();
        public string Context { get; set; } = string.Empty;
    }

    /// <summary>
    /// Données pour l'événement du module de commandes.
    /// </summary>
    public class CommandModuleEventData
    {
        public string ModuleName { get; set; } = string.Empty;
        public string CommandName { get; set; } = string.Empty;
        public object? Parameter { get; set; }
        public object? Result { get; set; }
        public System.TimeSpan ExecutionTime { get; set; }
    }

    /// <summary>
    /// Données pour l'événement du module de création.
    /// </summary>
    public class CreationModuleEventData
    {
        public string ModuleName { get; set; } = string.Empty;
        public CreationState CurrentState { get; set; }
        public CreationState PreviousState { get; set; }
        public string? Reason { get; set; }
    }
}
