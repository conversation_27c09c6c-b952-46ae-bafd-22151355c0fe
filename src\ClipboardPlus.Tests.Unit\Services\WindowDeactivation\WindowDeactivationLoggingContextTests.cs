using System;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.Core.Services.WindowDeactivation;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.WindowDeactivation
{
    /// <summary>
    /// Tests unitaires pour WindowDeactivationLoggingContext.
    /// </summary>
    [TestFixture]
    public class WindowDeactivationLoggingContextTests
    {
        private WindowDeactivationLoggingContext? _context;

        [SetUp]
        public void SetUp()
        {
            _context = new WindowDeactivationLoggingContext();
        }

        [Test]
        public void Constructor_ShouldInitializeWithDefaultValues()
        {
            // Act
            var context = new WindowDeactivationLoggingContext();

            // Assert
            Assert.That(context.OperationId, Is.EqualTo(string.Empty));
            Assert.That(context.WindowName, Is.EqualTo(string.Empty));
            Assert.That(context.StartTime, Is.Not.EqualTo(default(DateTime)));
            Assert.That(context.Properties, Is.Not.Null);
            Assert.That(context.Properties, Is.Empty);
        }

        [Test]
        public void StartTime_ShouldBeSetToCurrentTime()
        {
            // Arrange
            var beforeCreation = DateTime.Now.AddSeconds(-1);
            var afterCreation = DateTime.Now.AddSeconds(1);

            // Act
            var context = new WindowDeactivationLoggingContext();

            // Assert
            Assert.That(context.StartTime, Is.GreaterThan(beforeCreation));
            Assert.That(context.StartTime, Is.LessThan(afterCreation));
        }

        [Test]
        public void OperationId_ShouldBeSettable()
        {
            // Arrange
            var operationId = "test-operation-123";

            // Act
            _context!.OperationId = operationId;

            // Assert
            Assert.That(_context.OperationId, Is.EqualTo(operationId));
        }

        [Test]
        public void WindowName_ShouldBeSettable()
        {
            // Arrange
            var windowName = "TestWindow";

            // Act
            _context!.WindowName = windowName;

            // Assert
            Assert.That(_context.WindowName, Is.EqualTo(windowName));
        }

        [Test]
        public void StartTime_ShouldBeSettable()
        {
            // Arrange
            var customTime = new DateTime(2025, 7, 4, 12, 30, 45);

            // Act
            _context!.StartTime = customTime;

            // Assert
            Assert.That(_context.StartTime, Is.EqualTo(customTime));
        }

        [Test]
        public void Properties_ShouldBeModifiable()
        {
            // Arrange
            var key = "TestProperty";
            var value = "TestValue";

            // Act
            _context!.Properties[key] = value;

            // Assert
            Assert.That(_context.Properties.ContainsKey(key), Is.True);
            Assert.That(_context.Properties[key], Is.EqualTo(value));
        }

        [Test]
        public void Properties_ShouldSupportMultipleTypes()
        {
            // Act
            _context!.Properties["StringValue"] = "test";
            _context.Properties["IntValue"] = 42;
            _context.Properties["BoolValue"] = true;
            _context.Properties["DateTimeValue"] = DateTime.Now;
            _context.Properties["DoubleValue"] = 3.14;

            // Assert
            Assert.That(_context.Properties["StringValue"], Is.EqualTo("test"));
            Assert.That(_context.Properties["IntValue"], Is.EqualTo(42));
            Assert.That(_context.Properties["BoolValue"], Is.EqualTo(true));
            Assert.That(_context.Properties["DateTimeValue"], Is.TypeOf<DateTime>());
            Assert.That(_context.Properties["DoubleValue"], Is.EqualTo(3.14));
        }

        [Test]
        public void Properties_ShouldSupportComplexObjects()
        {
            // Arrange
            var complexObject = new { Name = "Test", Value = 123 };
            var list = new List<string> { "item1", "item2" };

            // Act
            _context!.Properties["ComplexObject"] = complexObject;
            _context.Properties["List"] = list;

            // Assert
            Assert.That(_context.Properties["ComplexObject"], Is.EqualTo(complexObject));
            Assert.That(_context.Properties["List"], Is.EqualTo(list));
        }

        [Test]
        public void Properties_ShouldHandleNullValues()
        {
            // Act
            _context!.Properties["NullValue"] = null!;

            // Assert
            Assert.That(_context.Properties.ContainsKey("NullValue"), Is.True);
            Assert.That(_context.Properties["NullValue"], Is.Null);
        }

        [Test]
        public void Properties_ShouldSupportRemoval()
        {
            // Arrange
            _context!.Properties["TestKey"] = "TestValue";

            // Act
            var removed = _context.Properties.Remove("TestKey");

            // Assert
            Assert.That(removed, Is.True);
            Assert.That(_context.Properties.ContainsKey("TestKey"), Is.False);
        }

        [Test]
        public void Properties_ShouldSupportClear()
        {
            // Arrange
            _context!.Properties["Key1"] = "Value1";
            _context.Properties["Key2"] = "Value2";

            // Act
            _context.Properties.Clear();

            // Assert
            Assert.That(_context.Properties, Is.Empty);
        }

        [Test]
        public void Context_ShouldSupportCloning()
        {
            // Arrange
            _context!.OperationId = "test-op-123";
            _context.WindowName = "TestWindow";
            _context.StartTime = new DateTime(2025, 7, 4, 12, 0, 0);
            _context.Properties["TestKey"] = "TestValue";
            _context.Properties["NumberKey"] = 42;

            // Act
            var clonedContext = new WindowDeactivationLoggingContext
            {
                OperationId = _context.OperationId,
                WindowName = _context.WindowName,
                StartTime = _context.StartTime,
                Properties = new Dictionary<string, object>(_context.Properties)
            };

            // Assert
            Assert.That(clonedContext.OperationId, Is.EqualTo(_context.OperationId));
            Assert.That(clonedContext.WindowName, Is.EqualTo(_context.WindowName));
            Assert.That(clonedContext.StartTime, Is.EqualTo(_context.StartTime));
            Assert.That(clonedContext.Properties.Count, Is.EqualTo(_context.Properties.Count));
            Assert.That(clonedContext.Properties["TestKey"], Is.EqualTo(_context.Properties["TestKey"]));
            Assert.That(clonedContext.Properties["NumberKey"], Is.EqualTo(_context.Properties["NumberKey"]));
        }

        [Test]
        public void Context_ShouldHandleEmptyStrings()
        {
            // Act
            _context!.OperationId = string.Empty;
            _context.WindowName = string.Empty;

            // Assert
            Assert.That(_context.OperationId, Is.EqualTo(string.Empty));
            Assert.That(_context.WindowName, Is.EqualTo(string.Empty));
        }

        [Test]
        public void Context_ShouldHandleNullStrings()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _context!.OperationId = null!);
            Assert.DoesNotThrow(() => _context!.WindowName = null!);
        }

        [Test]
        public void Properties_ShouldSupportEnumeration()
        {
            // Arrange
            _context!.Properties["Key1"] = "Value1";
            _context.Properties["Key2"] = "Value2";
            _context.Properties["Key3"] = "Value3";

            // Act
            var keys = _context.Properties.Keys.ToList();
            var values = _context.Properties.Values.ToList();

            // Assert
            Assert.That(keys.Count, Is.EqualTo(3));
            Assert.That(values.Count, Is.EqualTo(3));
            Assert.That(keys.Contains("Key1"), Is.True);
            Assert.That(keys.Contains("Key2"), Is.True);
            Assert.That(keys.Contains("Key3"), Is.True);
        }

        [Test]
        public void Context_ShouldSupportTypicalUsageScenario()
        {
            // Arrange - Scénario d'utilisation typique
            var operationId = Guid.NewGuid().ToString("N")[..8];
            var windowName = "ClipboardHistoryWindow";
            var startTime = DateTime.Now;

            // Act
            _context!.OperationId = operationId;
            _context.WindowName = windowName;
            _context.StartTime = startTime;
            _context.Properties["IsClosing"] = false;
            _context.Properties["IsActive"] = true;
            _context.Properties["WindowState"] = "Normal";
            _context.Properties["DiagnosticDuration"] = 15.5;

            // Assert
            Assert.That(_context.OperationId, Is.EqualTo(operationId));
            Assert.That(_context.WindowName, Is.EqualTo(windowName));
            Assert.That(_context.StartTime, Is.EqualTo(startTime));
            Assert.That(_context.Properties["IsClosing"], Is.EqualTo(false));
            Assert.That(_context.Properties["IsActive"], Is.EqualTo(true));
            Assert.That(_context.Properties["WindowState"], Is.EqualTo("Normal"));
            Assert.That(_context.Properties["DiagnosticDuration"], Is.EqualTo(15.5));
        }

        [Test]
        public void Properties_ShouldSupportKeyOverwrite()
        {
            // Arrange
            var key = "TestKey";
            var originalValue = "OriginalValue";
            var newValue = "NewValue";

            // Act
            _context!.Properties[key] = originalValue;
            _context.Properties[key] = newValue;

            // Assert
            Assert.That(_context.Properties[key], Is.EqualTo(newValue));
            Assert.That(_context.Properties.Count, Is.EqualTo(1));
        }

        [Test]
        public void Context_ShouldHandleMaximumProperties()
        {
            // Act - Ajouter beaucoup de propriétés
            for (int i = 0; i < 100; i++)
            {
                _context!.Properties[$"Key{i}"] = $"Value{i}";
            }

            // Assert
            Assert.That(_context!.Properties.Count, Is.EqualTo(100));
            Assert.That(_context.Properties["Key0"], Is.EqualTo("Value0"));
            Assert.That(_context.Properties["Key99"], Is.EqualTo("Value99"));
        }
    }
}
