using System;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.SupprimerTout
{
    /// <summary>
    /// Analyseur pour les opérations de suppression de tous les éléments.
    /// Extrait de la méthode SupprimerTout originale pour séparer les responsabilités.
    /// Gère l'analyse et le comptage des éléments à supprimer.
    /// </summary>
    public class SupprimerToutAnalyzer
    {
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de l'analyseur.
        /// </summary>
        /// <param name="loggingService">Service de logging pour tracer les analyses.</param>
        public SupprimerToutAnalyzer(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Analyse une collection d'éléments pour déterminer lesquels seront supprimés.
        /// Correspond à la logique de comptage de la méthode originale.
        /// </summary>
        /// <param name="items">Collection d'éléments à analyser.</param>
        /// <param name="preservePinned">Indique si les éléments épinglés doivent être préservés.</param>
        /// <param name="operationId">Identifiant de l'opération pour le logging.</param>
        /// <returns>Analyse détaillée des éléments à supprimer.</returns>
        public SupprimerToutAnalysis AnalyzeItems(IEnumerable<ClipboardItem> items, bool preservePinned = true, string? operationId = null)
        {
            var logPrefix = string.IsNullOrEmpty(operationId) ? "[SupprimerToutAnalyzer]" : $"[{operationId}][SupprimerToutAnalyzer]";
            
            _loggingService?.LogInfo($"{logPrefix} Début de l'analyse des éléments (preservePinned: {preservePinned})");

            if (items == null)
            {
                _loggingService?.LogWarning($"{logPrefix} Collection d'éléments null, retour d'analyse vide");
                return new SupprimerToutAnalysis(0, 0, 0, false);
            }

            // Convertir en liste pour éviter les énumérations multiples (optimisation LINQ)
            var itemsList = items.ToList();
            
            _loggingService?.LogInfo($"{logPrefix} Analyse de {itemsList.Count} élément(s)");

            // Calcul optimisé des statistiques
            var analysis = CalculateStatistics(itemsList, preservePinned, logPrefix);

            _loggingService?.LogInfo($"{logPrefix} Analyse terminée - Total: {analysis.TotalItems}, " +
                                   $"Épinglés: {analysis.PinnedItems}, À supprimer: {analysis.ItemsToDelete}");

            return analysis;
        }

        /// <summary>
        /// Calcule les statistiques de suppression de manière optimisée.
        /// </summary>
        /// <param name="items">Liste des éléments à analyser.</param>
        /// <param name="preservePinned">Indique si les éléments épinglés doivent être préservés.</param>
        /// <param name="logPrefix">Préfixe pour les logs.</param>
        /// <returns>Analyse des statistiques.</returns>
        private SupprimerToutAnalysis CalculateStatistics(List<ClipboardItem> items, bool preservePinned, string logPrefix)
        {
            var totalItems = items.Count;

            if (totalItems == 0)
            {
                _loggingService?.LogInfo($"{logPrefix} Collection vide, aucun élément à analyser");
                return new SupprimerToutAnalysis(0, 0, 0, false);
            }

            // Optimisation : un seul parcours de la collection
            var pinnedCount = 0;
            var unpinnedCount = 0;

            foreach (var item in items)
            {
                if (item.IsPinned)
                    pinnedCount++;
                else
                    unpinnedCount++;
            }

            // Calcul des éléments à supprimer selon la stratégie
            var pinnedItems = preservePinned ? pinnedCount : 0;
            var itemsToDelete = preservePinned ? unpinnedCount : totalItems;

            _loggingService?.LogInfo($"{logPrefix} Statistiques calculées - " +
                                   $"Épinglés: {pinnedCount}, Non-épinglés: {unpinnedCount}, " +
                                   $"Stratégie: {(preservePinned ? "préserver épinglés" : "supprimer tout")}");

            return new SupprimerToutAnalysis(
                TotalItems: totalItems,
                PinnedItems: pinnedItems,
                ItemsToDelete: itemsToDelete,
                HasItemsToDelete: itemsToDelete > 0
            );
        }

        /// <summary>
        /// Analyse rapide pour vérifier s'il y a des éléments à supprimer sans calcul complet.
        /// Optimisation pour les cas où seule cette information est nécessaire.
        /// </summary>
        /// <param name="items">Collection d'éléments à vérifier.</param>
        /// <param name="preservePinned">Indique si les éléments épinglés doivent être préservés.</param>
        /// <returns>True s'il y a des éléments à supprimer, false sinon.</returns>
        public bool HasItemsToDelete(IEnumerable<ClipboardItem> items, bool preservePinned = true)
        {
            if (items == null)
                return false;

            if (!preservePinned)
                return items.Any(); // Si on ne préserve pas les épinglés, tout élément peut être supprimé

            // Si on préserve les épinglés, vérifier s'il y a des éléments non épinglés
            return items.Any(item => !item.IsPinned);
        }




    }
}
