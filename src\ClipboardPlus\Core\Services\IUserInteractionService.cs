using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service pour gérer les interactions utilisateur (dialogues, fenêtres, notifications).
    /// Permet de séparer la logique métier de l'interface utilisateur.
    /// </summary>
    public interface IUserInteractionService
    {
        /// <summary>
        /// Ouvre la fenêtre de nettoyage avancé de l'historique.
        /// </summary>
        /// <returns>Task qui se termine quand la fenêtre est ouverte</returns>
        Task OpenAdvancedCleanupWindowAsync();

        /// <summary>
        /// Affiche une notification temporaire à l'utilisateur.
        /// </summary>
        /// <param name="message">Message à afficher</param>
        /// <param name="isSuccess">True pour succès (vert), false pour erreur (rouge)</param>
        void ShowNotification(string message, bool isSuccess = true);

        /// <summary>
        /// Affiche un dialogue de confirmation.
        /// </summary>
        /// <param name="message">Message de confirmation</param>
        /// <param name="title">Titre du dialogue</param>
        /// <returns>True si l'utilisateur confirme, false sinon</returns>
        Task<bool> ShowConfirmationAsync(string message, string title = "Confirmation");
    }
}
