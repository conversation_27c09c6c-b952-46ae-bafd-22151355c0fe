using System;
using System.Runtime.InteropServices;
using System.Threading;
using ClipboardPlus.Native;
using System.Diagnostics;
using System.Linq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.Native;
using System.Text;
using System.Windows.Forms; // Ajout de la référence pour SendKeys

namespace ClipboardPlus.Utils
{
    /// <summary>
    /// Utilitaire pour simuler des actions clavier.
    /// </summary>
    public static class KeyboardSimulator
    {
        // Verrouillage pour éviter les appels simultanés
        private static readonly object _lockObject = new object();
        
        // Propriété pour suivre l'état de la dernière simulation
        private static DateTime _lastSimulationTime = DateTime.MinValue;
        private static bool _isSimulationInProgress = false;
        private static readonly TimeSpan _minimumTimeBetweenSimulations = TimeSpan.FromMilliseconds(500);
        
        /// <summary>
        /// Simule l'appui sur Ctrl+V pour coller depuis le presse-papiers
        /// </summary>
        /// <param name="delayBeforePaste"><PERSON><PERSON><PERSON> en ms avant de simuler la frappe (pour laisser le temps à la fenêtre cible de prendre le focus).</param>
        /// <returns>True si la simulation a réussi, False sinon.</returns>
        public static bool SimulateCtrlV(int delayBeforePaste = 50)
        {
            string operationId = Guid.NewGuid().ToString().Substring(0, 8);
            ILoggingService? loggingService = GetLoggingService();
            
            // Protéger contre les appels simultanés avec un verrouillage court
            if (!Monitor.TryEnter(_lockObject, 300))
            {
                return false;
            }
            
            try
            {
                // Protection contre les appels rapprochés
                if (_isSimulationInProgress)
                {
                    return false;
                }
                
                // Vérifier si une simulation a été effectuée récemment
                TimeSpan timeSinceLastSimulation = DateTime.Now - _lastSimulationTime;
                if (timeSinceLastSimulation < TimeSpan.FromMilliseconds(200))
                {
                    return false;
                }
                
                // Marquer le début de la simulation
                _isSimulationInProgress = true;
                _lastSimulationTime = DateTime.Now;
                
                // Attendre le délai spécifié avant de commencer (réduit au minimum nécessaire)
                if (delayBeforePaste > 0)
                {
                    Thread.Sleep(Math.Min(delayBeforePaste, 50)); // Limiter à 50ms maximum
                }

                // Vérifier et activer la fenêtre de premier plan
                IntPtr foregroundWindow = User32.GetForegroundWindow();
                if (foregroundWindow == IntPtr.Zero)
                {
                    return false;
                }
                
                // S'assurer que la fenêtre est bien au premier plan
                if (!User32.IsWindowEnabled(foregroundWindow) || !User32.IsWindowVisible(foregroundWindow))
                {
                    return false;
                }
                
                // Tenter plusieurs méthodes de collage en cascade pour plus de fiabilité
                
                // 1. Essayer en premier la méthode WM_PASTE qui est la plus directe
                try
                {
                    if (User32.PostMessage(foregroundWindow, User32.WM_PASTE, IntPtr.Zero, IntPtr.Zero))
                    {
                        return true;
                    }
                }
                catch (Exception) { }
                
                // 2. Essayer avec SendKeys qui est rapide mais peut échouer dans certains contextes
                try
                {
                    SendKeys.Send("^v"); // ^v représente Ctrl+V
                    return true;
                }
                catch (Exception) { }
                
                // 3. En dernier recours, utiliser SendInput qui est plus fiable mais peut être plus lent
                try
                {
                    // Préparation des entrées pour SendInput
                    NativeMethods.INPUT[] inputs = new NativeMethods.INPUT[4];

                    // 1. Appuyer sur CTRL
                    inputs[0] = new NativeMethods.INPUT
                    {
                        type = NativeMethods.InputType.Keyboard,
                        u = new NativeMethods.InputUnion
                        {
                            ki = new NativeMethods.KEYBDINPUT
                            {
                                wVk = (ushort)NativeMethods.VK_CONTROL,
                                wScan = 0,
                                dwFlags = 0,
                                time = 0,
                                dwExtraInfo = IntPtr.Zero
                            }
                        }
                    };
                    
                    // 2. Appuyer sur V
                    inputs[1] = new NativeMethods.INPUT
                    {
                        type = NativeMethods.InputType.Keyboard,
                        u = new NativeMethods.InputUnion
                        {
                            ki = new NativeMethods.KEYBDINPUT
                            {
                                wVk = (ushort)NativeMethods.VK_V,
                                wScan = 0,
                                dwFlags = 0,
                                time = 0,
                                dwExtraInfo = IntPtr.Zero
                            }
                        }
                    };
                    
                    // 3. Relâcher V
                    inputs[2] = new NativeMethods.INPUT
                    {
                        type = NativeMethods.InputType.Keyboard,
                        u = new NativeMethods.InputUnion
                        {
                            ki = new NativeMethods.KEYBDINPUT
                            {
                                wVk = (ushort)NativeMethods.VK_V,
                                wScan = 0,
                                dwFlags = NativeMethods.KEYEVENTF_KEYUP,
                                time = 0,
                                dwExtraInfo = IntPtr.Zero
                            }
                        }
                    };
                    
                    // 4. Relâcher CTRL
                    inputs[3] = new NativeMethods.INPUT
                    {
                        type = NativeMethods.InputType.Keyboard,
                        u = new NativeMethods.InputUnion
                        {
                            ki = new NativeMethods.KEYBDINPUT
                            {
                                wVk = (ushort)NativeMethods.VK_CONTROL,
                                wScan = 0,
                                dwFlags = NativeMethods.KEYEVENTF_KEYUP,
                                time = 0,
                                dwExtraInfo = IntPtr.Zero
                            }
                        }
                    };
                    
                    uint result = NativeMethods.SendInput((uint)inputs.Length, inputs, Marshal.SizeOf(typeof(NativeMethods.INPUT)));
                    return result == inputs.Length;
                }
                catch (Exception)
                {
                    return false;
                }
            }
            finally
            {
                // Réinitialiser le flag et libérer le verrou
                _isSimulationInProgress = false;
                Monitor.Exit(_lockObject);
            }
        }

        // Méthode pour obtenir le service de journalisation
        private static ILoggingService? GetLoggingService()
        {
            try
            {
                var app = System.Windows.Application.Current;
                if (app != null)
                {
                    var services = app.GetType().GetProperty("Services")?.GetValue(app);
                    if (services != null)
                    {
                        var getServiceMethod = services.GetType().GetMethod("GetService", new Type[] { typeof(Type) });
                        if (getServiceMethod != null)
                        {
                            return getServiceMethod.Invoke(services, new object[] { typeof(ILoggingService) }) as ILoggingService;
                        }
                    }
                }
            }
            catch
            {
                // Ignorer les erreurs
            }
            return null;
        }

        /// <summary>
        /// S'assure que la fenêtre spécifiée est au premier plan avant d'exécuter une action.
        /// </summary>
        /// <param name="windowHandle">Handle de la fenêtre à mettre au premier plan.</param>
        /// <param name="action">Action à exécuter une fois la fenêtre au premier plan.</param>
        public static void EnsureForegroundWindowAndExecute(IntPtr windowHandle, Action action)
        {
            try
            {
                // Récupérer la fenêtre actuellement au premier plan
                IntPtr currentForegroundWindow = User32.GetForegroundWindow();

                // Si la fenêtre cible n'est pas déjà au premier plan, la mettre au premier plan
                if (currentForegroundWindow != windowHandle)
                {
                    User32.SetForegroundWindow(windowHandle);
                    
                    // Attendre un court instant pour permettre à la fenêtre de prendre le focus
                    Thread.Sleep(100);
                }

                // Exécuter l'action spécifiée
                action();
            }
            catch (Exception ex)
            {
                // Enregistrer l'erreur
                Debug.WriteLine($"[KeyboardSimulator] Erreur lors de la mise au premier plan de la fenêtre: {ex.Message}");
                Debug.WriteLine($"[KeyboardSimulator] StackTrace: {ex.StackTrace}");
                
                // Propager l'exception pour qu'elle soit gérée par l'appelant
                throw;
            }
        }

        /// <summary>
        /// Simule l'appui sur une touche avec des modificateurs.
        /// </summary>
        /// <param name="key">La touche à simuler (par exemple, Key.A, Key.Enter).</param>
        /// <param name="modifiers">Les touches modificatrices (par exemple, ModifierKeys.Control, ModifierKeys.Shift).</param>
        public static void SimulateKeyPress(System.Windows.Input.Key key, System.Windows.Input.ModifierKeys modifiers)
        {
            var inputs = new System.Collections.Generic.List<NativeMethods.INPUT>();

            // Appuyer sur les modificateurs
            if (modifiers.HasFlag(System.Windows.Input.ModifierKeys.Control))
                inputs.Add(CreateKeyboardInput(NativeMethods.VK_CONTROL, false));
            if (modifiers.HasFlag(System.Windows.Input.ModifierKeys.Shift))
                inputs.Add(CreateKeyboardInput(NativeMethods.VK_SHIFT, false));
            if (modifiers.HasFlag(System.Windows.Input.ModifierKeys.Alt))
                inputs.Add(CreateKeyboardInput(NativeMethods.VK_MENU, false)); // VK_MENU est pour Alt

            // Appuyer sur la touche principale
            inputs.Add(CreateKeyboardInput(KeyToVirtualKey(key), false));

            // Relâcher la touche principale
            inputs.Add(CreateKeyboardInput(KeyToVirtualKey(key), true));

            // Relâcher les modificateurs dans l'ordre inverse
            if (modifiers.HasFlag(System.Windows.Input.ModifierKeys.Alt))
                inputs.Add(CreateKeyboardInput(NativeMethods.VK_MENU, true));
            if (modifiers.HasFlag(System.Windows.Input.ModifierKeys.Shift))
                inputs.Add(CreateKeyboardInput(NativeMethods.VK_SHIFT, true));
            if (modifiers.HasFlag(System.Windows.Input.ModifierKeys.Control))
                inputs.Add(CreateKeyboardInput(NativeMethods.VK_CONTROL, true));

            NativeMethods.SendInput((uint)inputs.Count, inputs.ToArray(), (int)Marshal.SizeOf(typeof(NativeMethods.INPUT)));
        }

        private static NativeMethods.INPUT CreateKeyboardInput(ushort virtualKey, bool isKeyUp)
        {
            return new NativeMethods.INPUT
            {
                type = NativeMethods.InputType.Keyboard,
                u = new NativeMethods.InputUnion
                {
                    ki = new NativeMethods.KEYBDINPUT
                    {
                        wVk = virtualKey,
                        wScan = 0,
                        dwFlags = isKeyUp ? NativeMethods.KEYEVENTF_KEYUP : 0u,
                        time = 0,
                        dwExtraInfo = IntPtr.Zero
                    }
                }
            };
        }

        private static ushort KeyToVirtualKey(System.Windows.Input.Key key)
        {
            // Cette méthode doit mapper les System.Windows.Input.Key aux codes de touches virtuelles (VK_*) de Windows.
            // C'est une implémentation simplifiée et peut nécessiter plus de cas pour une couverture complète.
            return (ushort)System.Windows.Input.KeyInterop.VirtualKeyFromKey(key);
        }

        private static class NativeMethods
        {
            [DllImport("user32.dll")]
            public static extern uint SendInput(uint nInputs, INPUT[] pInputs, int cbSize);

            [DllImport("user32.dll")]
            public static extern IntPtr GetForegroundWindow();

            [DllImport("user32.dll")]
            [return: MarshalAs(UnmanagedType.Bool)]
            public static extern bool IsWindowEnabled(IntPtr hWnd);

            [DllImport("user32.dll")]
            [return: MarshalAs(UnmanagedType.Bool)]
            public static extern bool SetForegroundWindow(IntPtr hWnd);

            public const int KEYEVENTF_KEYUP = 0x0002;
            public const int VK_CONTROL = 0x11;
            public const int VK_SHIFT = 0x10; // Ajouté
            public const int VK_MENU = 0x12; // Ajouté pour Alt
            public const int VK_V = 0x56;

            public enum InputType : uint
            {
                Mouse = 0,
                Keyboard = 1,
                Hardware = 2
            }

            [StructLayout(LayoutKind.Sequential)]
            public struct INPUT
            {
                public InputType type;
                public InputUnion u;
            }

            [StructLayout(LayoutKind.Explicit)]
            public struct InputUnion
            {
                [FieldOffset(0)]
                public MOUSEINPUT mi;
                [FieldOffset(0)]
                public KEYBDINPUT ki;
                [FieldOffset(0)]
                public HARDWAREINPUT hi;
            }

            [StructLayout(LayoutKind.Sequential)]
            public struct MOUSEINPUT
            {
                public int dx;
                public int dy;
                public uint mouseData;
                public uint dwFlags;
                public uint time;
                public IntPtr dwExtraInfo;
            }

            [StructLayout(LayoutKind.Sequential)]
            public struct KEYBDINPUT
            {
                public ushort wVk;
                public ushort wScan;
                public uint dwFlags;
                public uint time;
                public IntPtr dwExtraInfo;
            }

            [StructLayout(LayoutKind.Sequential)]
            public struct HARDWAREINPUT
            {
                public uint uMsg;
                public ushort wParamL;
                public ushort wParamH;
            }
        }
    }
}