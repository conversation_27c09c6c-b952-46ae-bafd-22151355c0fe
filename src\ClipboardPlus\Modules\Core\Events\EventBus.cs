using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ClipboardPlus.Modules.Core.Events
{
    /// <summary>
    /// Implémentation thread-safe du bus d'événements inter-modules.
    /// 
    /// Cette implémentation utilise des weak references pour éviter les fuites mémoire
    /// et supporte à la fois les handlers synchrones et asynchrones.
    /// </summary>
    public class EventBus : IEventBus, IDisposable
    {
        private readonly ConcurrentDictionary<Type, ConcurrentBag<WeakEventSubscription>> _subscriptions;
        private readonly object _cleanupLock = new object();
        private bool _disposed = false;

        public EventBus()
        {
            _subscriptions = new ConcurrentDictionary<Type, ConcurrentBag<WeakEventSubscription>>();
        }

        /// <inheritdoc />
        public async Task PublishAsync<TEvent>(TEvent eventData) where TEvent : class, IModuleEvent
        {
            ThrowIfDisposed();
            
            if (eventData == null) return;

            var eventType = typeof(TEvent);
            if (!_subscriptions.TryGetValue(eventType, out var subscriptions))
                return;

            var tasks = new List<Task>();
            var activeSubscriptions = GetActiveSubscriptions(subscriptions);

            foreach (var subscription in activeSubscriptions)
            {
                if (subscription.AsyncHandler != null)
                {
                    tasks.Add(InvokeHandlerSafelyAsync(subscription.AsyncHandler, eventData));
                }
                else if (subscription.SyncHandler != null)
                {
                    tasks.Add(Task.Run(() => InvokeHandlerSafely(subscription.SyncHandler, eventData)));
                }
            }

            if (tasks.Count > 0)
            {
                await Task.WhenAll(tasks);
            }
        }

        /// <inheritdoc />
        public void Publish<TEvent>(TEvent eventData) where TEvent : class, IModuleEvent
        {
            ThrowIfDisposed();
            
            if (eventData == null) return;

            var eventType = typeof(TEvent);
            if (!_subscriptions.TryGetValue(eventType, out var subscriptions))
                return;

            var activeSubscriptions = GetActiveSubscriptions(subscriptions);

            foreach (var subscription in activeSubscriptions)
            {
                if (subscription.SyncHandler != null)
                {
                    InvokeHandlerSafely(subscription.SyncHandler, eventData);
                }
                else if (subscription.AsyncHandler != null)
                {
                    // Pour les handlers async dans un contexte sync, on les exécute en fire-and-forget
                    _ = Task.Run(async () => await InvokeHandlerSafelyAsync(subscription.AsyncHandler, eventData));
                }
            }
        }

        /// <inheritdoc />
        public IEventSubscription Subscribe<TEvent>(Func<TEvent, Task> handler, bool keepSubscriberReferenceAlive = false) where TEvent : class, IModuleEvent
        {
            ThrowIfDisposed();

            if (handler == null) throw new ArgumentNullException(nameof(handler));

            var eventType = typeof(TEvent);
            var subscription = new EventSubscription(eventType, handler.Target ?? this, this);
            var weakSubscription = new WeakEventSubscription(subscription, null, handler, keepSubscriberReferenceAlive);

            _subscriptions.AddOrUpdate(
                eventType,
                new ConcurrentBag<WeakEventSubscription> { weakSubscription },
                (key, existing) =>
                {
                    existing.Add(weakSubscription);
                    return existing;
                });

            return subscription;
        }

        /// <inheritdoc />
        public IEventSubscription Subscribe<TEvent>(Action<TEvent> handler, bool keepSubscriberReferenceAlive = false) where TEvent : class, IModuleEvent
        {
            ThrowIfDisposed();

            if (handler == null) throw new ArgumentNullException(nameof(handler));

            var eventType = typeof(TEvent);
            var subscription = new EventSubscription(eventType, handler.Target ?? this, this);
            var weakSubscription = new WeakEventSubscription(subscription, handler, null, keepSubscriberReferenceAlive);

            _subscriptions.AddOrUpdate(
                eventType,
                new ConcurrentBag<WeakEventSubscription> { weakSubscription },
                (key, existing) =>
                {
                    existing.Add(weakSubscription);
                    return existing;
                });

            return subscription;
        }

        /// <inheritdoc />
        public void Unsubscribe(IEventSubscription subscription)
        {
            if (subscription == null || _disposed) return;

            subscription.Cancel();
            CleanupDeadSubscriptions();
        }

        /// <inheritdoc />
        public void UnsubscribeAll(object subscriber)
        {
            if (subscriber == null || _disposed) return;

            foreach (var kvp in _subscriptions)
            {
                var activeSubscriptions = GetActiveSubscriptions(kvp.Value);
                foreach (var subscription in activeSubscriptions)
                {
                    if (ReferenceEquals(subscription.Subscription.Subscriber, subscriber))
                    {
                        subscription.Subscription.Cancel();
                    }
                }
            }

            CleanupDeadSubscriptions();
        }

        /// <inheritdoc />
        public void Clear()
        {
            if (_disposed) return;

            foreach (var kvp in _subscriptions)
            {
                var activeSubscriptions = GetActiveSubscriptions(kvp.Value);
                foreach (var subscription in activeSubscriptions)
                {
                    subscription.Subscription.Cancel();
                }
            }

            _subscriptions.Clear();
        }

        private List<WeakEventSubscription> GetActiveSubscriptions(ConcurrentBag<WeakEventSubscription> subscriptions)
        {
            var active = new List<WeakEventSubscription>();
            
            foreach (var subscription in subscriptions)
            {
                if (subscription.IsAlive && subscription.Subscription.IsActive)
                {
                    active.Add(subscription);
                }
            }

            return active;
        }

        private async Task InvokeHandlerSafelyAsync(Func<object, Task> handler, object eventData)
        {
            try
            {
                await handler(eventData);
            }
            catch (Exception)
            {
                // Log l'erreur mais ne la propage pas pour éviter de casser les autres handlers
                // TODO: Ajouter un système de logging
            }
        }

        private void InvokeHandlerSafely(Action<object> handler, object eventData)
        {
            try
            {
                handler(eventData);
            }
            catch (Exception)
            {
                // Log l'erreur mais ne la propage pas pour éviter de casser les autres handlers
                // TODO: Ajouter un système de logging
            }
        }

        private void CleanupDeadSubscriptions()
        {
            if (_disposed) return;

            lock (_cleanupLock)
            {
                var keysToUpdate = new List<Type>();

                foreach (var kvp in _subscriptions)
                {
                    var hasDeadSubscriptions = kvp.Value.Any(s => !s.IsAlive || !s.Subscription.IsActive);
                    if (hasDeadSubscriptions)
                    {
                        keysToUpdate.Add(kvp.Key);
                    }
                }

                foreach (var key in keysToUpdate)
                {
                    if (_subscriptions.TryGetValue(key, out var subscriptions))
                    {
                        var activeSubscriptions = subscriptions.Where(s => s.IsAlive && s.Subscription.IsActive);
                        _subscriptions.TryUpdate(key, new ConcurrentBag<WeakEventSubscription>(activeSubscriptions), subscriptions);
                    }
                }
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(EventBus));
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                Clear();
                _disposed = true;
            }
        }

        /// <summary>
        /// Wrapper pour les subscriptions avec weak references et cache optionnel de références fortes.
        /// Implémente le modèle Prism EventAggregator pour un équilibre optimal entre performance et gestion mémoire.
        /// </summary>
        private class WeakEventSubscription
        {
            private readonly WeakReference _subscriptionRef;
            private readonly WeakReference? _syncHandlerRef;
            private readonly WeakReference? _asyncHandlerRef;

            // Cache de références fortes pour éviter la collecte par le GC (modèle Prism)
            private readonly object? _strongSyncHandlerRef;
            private readonly object? _strongAsyncHandlerRef;
            private readonly bool _keepSubscriberReferenceAlive;

            public WeakEventSubscription(EventSubscription subscription, Delegate? syncHandler, Delegate? asyncHandler, bool keepSubscriberReferenceAlive = false)
            {
                _subscriptionRef = new WeakReference(subscription);
                _keepSubscriberReferenceAlive = keepSubscriberReferenceAlive;

                if (keepSubscriberReferenceAlive)
                {
                    // Mode StrongReference : maintenir les handlers en vie
                    _strongSyncHandlerRef = syncHandler;
                    _strongAsyncHandlerRef = asyncHandler;
                    _syncHandlerRef = null;
                    _asyncHandlerRef = null;
                }
                else
                {
                    // Mode WeakReference : permettre la collecte automatique
                    _syncHandlerRef = syncHandler != null ? new WeakReference(syncHandler) : null;
                    _asyncHandlerRef = asyncHandler != null ? new WeakReference(asyncHandler) : null;
                    _strongSyncHandlerRef = null;
                    _strongAsyncHandlerRef = null;
                }
            }

            public EventSubscription Subscription => (EventSubscription)_subscriptionRef.Target!;

            public Action<object>? SyncHandler
            {
                get
                {
                    var handler = _keepSubscriberReferenceAlive
                        ? _strongSyncHandlerRef
                        : _syncHandlerRef?.Target;

                    if (handler == null) return null;

                    // Créer un wrapper qui peut accepter object et le caster au bon type
                    return obj => ((Delegate)handler).DynamicInvoke(obj);
                }
            }

            public Func<object, Task>? AsyncHandler
            {
                get
                {
                    var handler = _keepSubscriberReferenceAlive
                        ? _strongAsyncHandlerRef
                        : _asyncHandlerRef?.Target;

                    if (handler == null) return null;

                    // Créer un wrapper qui peut accepter object et le caster au bon type
                    return async obj => await (Task)((Delegate)handler).DynamicInvoke(obj);
                }
            }

            public bool IsAlive => _subscriptionRef.IsAlive &&
                                   (_keepSubscriberReferenceAlive ?
                                    (_strongSyncHandlerRef != null || _strongAsyncHandlerRef != null) :
                                    (_syncHandlerRef?.IsAlive != false && _asyncHandlerRef?.IsAlive != false));
        }
    }

    /// <summary>
    /// Implémentation de l'interface IEventSubscription.
    /// </summary>
    internal class EventSubscription : IEventSubscription
    {
        private readonly IEventBus _eventBus;
        private bool _isActive = true;

        public Guid SubscriptionId { get; }
        public Type EventType { get; }
        public object Subscriber { get; }
        public bool IsActive => _isActive;

        public EventSubscription(Type eventType, object subscriber, IEventBus eventBus)
        {
            SubscriptionId = Guid.NewGuid();
            EventType = eventType;
            Subscriber = subscriber;
            _eventBus = eventBus;
        }

        public void Cancel()
        {
            _isActive = false;
        }

        public void Dispose()
        {
            Cancel();
        }
    }
}
