using System;
using System.Collections.Specialized;
using System.Windows.Media.Imaging;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Core.DataModels
{
    /// <summary>
    /// Tests unitaires pour la classe ClipboardData
    /// </summary>
    [TestFixture]
    public class ClipboardDataTests
    {
        private ClipboardData _clipboardData = null!;

        [SetUp]
        public void Setup()
        {
            _clipboardData = new ClipboardData();
        }

        [TearDown]
        public void TearDown()
        {
            _clipboardData = null!;
        }

        #region Constructor Tests

        [Test]
        [Description("Vérifie que le constructeur initialise correctement les propriétés")]
        public void Constructor_InitializesPropertiesCorrectly()
        {
            // Act
            var clipboardData = new ClipboardData();

            // Assert
            Assert.That(clipboardData.Text, Is.Null, "Text devrait être null par défaut");
            Assert.That(clipboardData.Image, Is.Null, "Image devrait être null par défaut");
            Assert.That(clipboardData.FileDropList, <PERSON><PERSON>Null, "FileDropList devrait être null par défaut");
        }

        #endregion

        #region Property Tests

        [Test]
        [Description("Vérifie que la propriété Text peut être définie et récupérée")]
        public void Text_CanBeSetAndRetrieved()
        {
            // Arrange
            const string testText = "Test clipboard text";

            // Act
            _clipboardData.Text = testText;

            // Assert
            Assert.That(_clipboardData.Text, Is.EqualTo(testText));
        }

        [Test]
        [Description("Vérifie que la propriété Text peut être définie à null")]
        public void Text_CanBeSetToNull()
        {
            // Arrange
            _clipboardData.Text = "Some text";

            // Act
            _clipboardData.Text = null;

            // Assert
            Assert.That(_clipboardData.Text, Is.Null);
        }

        [Test]
        [Description("Vérifie que la propriété Image peut être définie et récupérée")]
        public void Image_CanBeSetAndRetrieved()
        {
            // Arrange
            var testImage = CreateTestBitmapSource();

            // Act
            _clipboardData.Image = testImage;

            // Assert
            Assert.That(_clipboardData.Image, Is.EqualTo(testImage));
        }

        [Test]
        [Description("Vérifie que la propriété Image peut être définie à null")]
        public void Image_CanBeSetToNull()
        {
            // Arrange
            _clipboardData.Image = CreateTestBitmapSource();

            // Act
            _clipboardData.Image = null;

            // Assert
            Assert.That(_clipboardData.Image, Is.Null);
        }

        [Test]
        [Description("Vérifie que la propriété FileDropList peut être définie et récupérée")]
        public void FileDropList_CanBeSetAndRetrieved()
        {
            // Arrange
            var testFileList = new StringCollection { "file1.txt", "file2.txt" };

            // Act
            _clipboardData.FileDropList = testFileList;

            // Assert
            Assert.That(_clipboardData.FileDropList, Is.EqualTo(testFileList));
        }

        [Test]
        [Description("Vérifie que la propriété FileDropList peut être définie à null")]
        public void FileDropList_CanBeSetToNull()
        {
            // Arrange
            _clipboardData.FileDropList = new StringCollection { "file1.txt" };

            // Act
            _clipboardData.FileDropList = null;

            // Assert
            Assert.That(_clipboardData.FileDropList, Is.Null);
        }

        #endregion

        #region ContainsText Tests

        [Test]
        [Description("Vérifie que ContainsText retourne true quand Text contient du texte")]
        public void ContainsText_WithValidText_ReturnsTrue()
        {
            // Arrange
            _clipboardData.Text = "Valid text";

            // Act
            bool result = _clipboardData.ContainsText();

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        [Description("Vérifie que ContainsText retourne false quand Text est null")]
        public void ContainsText_WithNullText_ReturnsFalse()
        {
            // Arrange
            _clipboardData.Text = null;

            // Act
            bool result = _clipboardData.ContainsText();

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        [Description("Vérifie que ContainsText retourne false quand Text est une chaîne vide")]
        public void ContainsText_WithEmptyText_ReturnsFalse()
        {
            // Arrange
            _clipboardData.Text = string.Empty;

            // Act
            bool result = _clipboardData.ContainsText();

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        [Description("Vérifie que ContainsText retourne true quand Text contient seulement des espaces")]
        public void ContainsText_WithWhitespaceText_ReturnsTrue()
        {
            // Arrange
            _clipboardData.Text = "   ";

            // Act
            bool result = _clipboardData.ContainsText();

            // Assert
            // Note: ContainsText() utilise !string.IsNullOrEmpty() qui retourne true pour les espaces
            Assert.That(result, Is.True);
        }

        [Test]
        [Description("Vérifie que ContainsText retourne true avec du texte contenant des espaces")]
        public void ContainsText_WithTextContainingSpaces_ReturnsTrue()
        {
            // Arrange
            _clipboardData.Text = "  Valid text with spaces  ";

            // Act
            bool result = _clipboardData.ContainsText();

            // Assert
            Assert.That(result, Is.True);
        }

        #endregion

        #region ContainsImage Tests

        [Test]
        [Description("Vérifie que ContainsImage retourne true quand Image est définie")]
        public void ContainsImage_WithValidImage_ReturnsTrue()
        {
            // Arrange
            _clipboardData.Image = CreateTestBitmapSource();

            // Act
            bool result = _clipboardData.ContainsImage();

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        [Description("Vérifie que ContainsImage retourne false quand Image est null")]
        public void ContainsImage_WithNullImage_ReturnsFalse()
        {
            // Arrange
            _clipboardData.Image = null;

            // Act
            bool result = _clipboardData.ContainsImage();

            // Assert
            Assert.That(result, Is.False);
        }

        #endregion

        #region ContainsFileDropList Tests

        [Test]
        [Description("Vérifie que ContainsFileDropList retourne true avec une liste non vide")]
        public void ContainsFileDropList_WithNonEmptyList_ReturnsTrue()
        {
            // Arrange
            _clipboardData.FileDropList = new StringCollection { "file1.txt", "file2.txt" };

            // Act
            bool result = _clipboardData.ContainsFileDropList();

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        [Description("Vérifie que ContainsFileDropList retourne false avec une liste null")]
        public void ContainsFileDropList_WithNullList_ReturnsFalse()
        {
            // Arrange
            _clipboardData.FileDropList = null;

            // Act
            bool result = _clipboardData.ContainsFileDropList();

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        [Description("Vérifie que ContainsFileDropList retourne false avec une liste vide")]
        public void ContainsFileDropList_WithEmptyList_ReturnsFalse()
        {
            // Arrange
            _clipboardData.FileDropList = new StringCollection();

            // Act
            bool result = _clipboardData.ContainsFileDropList();

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        [Description("Vérifie que ContainsFileDropList retourne true avec un seul fichier")]
        public void ContainsFileDropList_WithSingleFile_ReturnsTrue()
        {
            // Arrange
            _clipboardData.FileDropList = new StringCollection { "single_file.txt" };

            // Act
            bool result = _clipboardData.ContainsFileDropList();

            // Assert
            Assert.That(result, Is.True);
        }

        #endregion

        #region Integration Tests

        [Test]
        [Description("Vérifie qu'un ClipboardData peut contenir plusieurs types de données simultanément")]
        public void ClipboardData_CanContainMultipleDataTypes()
        {
            // Arrange
            _clipboardData.Text = "Test text";
            _clipboardData.Image = CreateTestBitmapSource();
            _clipboardData.FileDropList = new StringCollection { "file1.txt" };

            // Act & Assert
            Assert.That(_clipboardData.ContainsText(), Is.True, "Devrait contenir du texte");
            Assert.That(_clipboardData.ContainsImage(), Is.True, "Devrait contenir une image");
            Assert.That(_clipboardData.ContainsFileDropList(), Is.True, "Devrait contenir une liste de fichiers");
        }

        [Test]
        [Description("Vérifie qu'un ClipboardData vide ne contient aucun type de données")]
        public void ClipboardData_Empty_ContainsNoDataTypes()
        {
            // Act & Assert
            Assert.That(_clipboardData.ContainsText(), Is.False, "Ne devrait pas contenir de texte");
            Assert.That(_clipboardData.ContainsImage(), Is.False, "Ne devrait pas contenir d'image");
            Assert.That(_clipboardData.ContainsFileDropList(), Is.False, "Ne devrait pas contenir de liste de fichiers");
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Crée un BitmapSource de test pour les tests
        /// </summary>
        /// <returns>Un BitmapSource de test</returns>
        private static BitmapSource CreateTestBitmapSource()
        {
            // Créer une image de test simple (1x1 pixel)
            var bitmap = new WriteableBitmap(1, 1, 96, 96, System.Windows.Media.PixelFormats.Bgr32, null);
            bitmap.Freeze(); // Figer pour éviter les problèmes de thread
            return bitmap;
        }

        #endregion
    }
}
