using System;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Diagnostics;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Services.Diagnostics
{
    /// <summary>
    /// Implémentation du collecteur de données de diagnostic
    /// Remplace l'utilisation de la réflexion par des accès directs aux propriétés
    /// </summary>
    public class DiagnosticDataCollector : IDiagnosticDataCollector
    {
        private readonly ILoggingService _loggingService;

        public DiagnosticDataCollector(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        public DiagnosticData CollectItemData(ClipboardItem? item)
        {
            var properties = new Dictionary<string, object>();
            var warnings = new List<string>();
            var errors = new List<string>();

            try
            {
                if (item == null)
                {
                    properties["IsNull"] = true;
                    warnings.Add("L'élément à supprimer est null");
                }
                else
                {
                    properties["Id"] = item.Id;
                    properties["DataType"] = item.DataType.ToString();
                    properties["Timestamp"] = item.Timestamp;
                    properties["IsPinned"] = item.IsPinned;
                    properties["IsTitleVisible"] = item.IsTitleVisible;
                    properties["CustomName"] = item.CustomName ?? "null";
                    properties["TextPreview"] = !string.IsNullOrEmpty(item.TextPreview)
                        ? item.TextPreview.Substring(0, Math.Min(50, item.TextPreview.Length))
                        : "null";
                    properties["HasRawData"] = item.RawData != null;
                    properties["RawDataSize"] = item.RawData?.Length ?? 0;

                    // Validations
                    if (item.Id <= 0)
                        warnings.Add($"ID invalide: {item.Id}");
                    
                    if (item.Timestamp > DateTime.Now)
                        warnings.Add("Timestamp dans le futur");
                    
                    if (string.IsNullOrEmpty(item.TextPreview) && item.RawData == null)
                        warnings.Add("Élément sans contenu (TextPreview et RawData null)");
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Erreur lors de la collecte des données d'élément: {ex.Message}");
                _loggingService?.LogError($"Erreur DiagnosticDataCollector.CollectItemData: {ex.Message}", ex);
            }

            return new DiagnosticData(properties, warnings, errors, DateTime.Now);
        }

        public DiagnosticData CollectViewModelData(ClipboardHistoryViewModel viewModel)
        {
            var properties = new Dictionary<string, object>();
            var warnings = new List<string>();
            var errors = new List<string>();

            try
            {
                if (viewModel == null)
                {
                    errors.Add("ViewModel est null");
                    return new DiagnosticData(properties, warnings, errors, DateTime.Now);
                }

                // Collecte des propriétés accessibles publiquement (pas de réflexion)
                properties["HistoryItemsCount"] = viewModel.HistoryItems?.Count ?? 0;
                properties["HasSelectedItem"] = viewModel.SelectedClipboardItem != null;
                properties["IsLoading"] = viewModel.IsLoading;
                properties["SelectedItemId"] = viewModel.SelectedClipboardItem?.Id ?? -1;

                // Analyse des collections
                if (viewModel.HistoryItems != null)
                {
                    var pinnedCount = viewModel.HistoryItems.Count(item => item.IsPinned);
                    var visibleTitleCount = viewModel.HistoryItems.Count(item => item.IsTitleVisible);
                    
                    properties["PinnedItemsCount"] = pinnedCount;
                    properties["VisibleTitleCount"] = visibleTitleCount;
                    properties["HasItems"] = viewModel.HistoryItems.Any();

                    // Validations
                    if (viewModel.HistoryItems.Count == 0)
                        warnings.Add("Aucun élément dans l'historique");
                    
                    if (pinnedCount > viewModel.HistoryItems.Count / 2)
                        warnings.Add($"Beaucoup d'éléments épinglés: {pinnedCount}/{viewModel.HistoryItems.Count}");
                }
                else
                {
                    warnings.Add("Collection HistoryItems est null");
                }

                // État des commandes (vérification de disponibilité)
                properties["HasDeleteCommand"] = viewModel.SupprimerElementCommand != null;
                properties["HasClearAllCommand"] = viewModel.SupprimerToutCommand != null;
                properties["HasPasteCommand"] = viewModel.PasteSelectedItemCommand != null;

            }
            catch (Exception ex)
            {
                errors.Add($"Erreur lors de la collecte des données de ViewModel: {ex.Message}");
                _loggingService?.LogError($"Erreur DiagnosticDataCollector.CollectViewModelData: {ex.Message}", ex);
            }

            return new DiagnosticData(properties, warnings, errors, DateTime.Now);
        }

        public DiagnosticData CollectSystemData()
        {
            var properties = new Dictionary<string, object>();
            var warnings = new List<string>();
            var errors = new List<string>();

            try
            {
                properties["ThreadId"] = Environment.CurrentManagedThreadId;
                properties["Timestamp"] = DateTime.Now;
                properties["ApplicationVersion"] = GetType().Assembly.GetName().Version?.ToString() ?? "Unknown";
                properties["WorkingSet"] = Environment.WorkingSet;
                properties["ProcessorCount"] = Environment.ProcessorCount;
                properties["OSVersion"] = Environment.OSVersion.ToString();
                properties["Is64BitProcess"] = Environment.Is64BitProcess;

                // Vérifications système
                if (Environment.WorkingSet > 500 * 1024 * 1024) // 500MB
                    warnings.Add($"Utilisation mémoire élevée: {Environment.WorkingSet / (1024 * 1024)}MB");

            }
            catch (Exception ex)
            {
                errors.Add($"Erreur lors de la collecte des données système: {ex.Message}");
                _loggingService?.LogError($"Erreur DiagnosticDataCollector.CollectSystemData: {ex.Message}", ex);
            }

            return new DiagnosticData(properties, warnings, errors, DateTime.Now);
        }
    }
}
