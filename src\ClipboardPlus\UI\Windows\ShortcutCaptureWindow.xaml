<Window x:Class="ClipboardPlus.UI.Windows.ShortcutCaptureWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ClipboardPlus.UI.Windows"
        xmlns:controls="clr-namespace:ClipboardPlus.UI.Controls"
        mc:Ignorable="d"
        Title="Nouveau Raccourci Clavier"
        Height="200"
        Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        ShowInTaskbar="False"
        WindowStyle="ToolWindow"
        Background="White"
        Loaded="Window_Loaded"
        KeyDown="Window_KeyDown">
    
    <Window.Resources>
        <!-- Style pour les boutons -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="80"/>
            <Setter Property="Background" Value="#E1E1E1"/>
            <Setter Property="BorderBrush" Value="#ADADAD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="14"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E5F1FB"/>
                    <Setter Property="BorderBrush" Value="#0078D4"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#C7E0F4"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#F3F2F1"/>
                    <Setter Property="Foreground" Value="#A19F9D"/>
                    <Setter Property="BorderBrush" Value="#EDEBE9"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- Style pour le bouton principal -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="#0078D4"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#106EBE"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#106EBE"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#005A9E"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    
    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Message principal -->
        <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,20">
            <TextBlock Text="⌨️"
                       FontSize="32"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,10"/>
            <TextBlock Text="Appuyez sur le nouveau raccourci clavier"
                       FontSize="16"
                       FontWeight="Medium"
                       HorizontalAlignment="Center"
                       Foreground="#323130"/>
        </StackPanel>

        <!-- Zone d'écoute simple -->
        <Border Grid.Row="1"
                BorderBrush="#0078D4"
                BorderThickness="2"
                Background="#F8F9FA"
                CornerRadius="8"
                Padding="20">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock x:Name="StatusText"
                           Text="En écoute..."
                           FontSize="18"
                           FontWeight="Medium"
                           HorizontalAlignment="Center"
                           Foreground="#0078D4"
                           Margin="0,0,0,10"/>

                <TextBlock x:Name="CapturedShortcut"
                           Text=""
                           FontSize="16"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Foreground="#323130"
                           Visibility="Collapsed"/>
            </StackPanel>
        </Border>

        <!-- Instructions simples -->
        <TextBlock Grid.Row="2"
                   Text="Appuyez sur Échap pour annuler"
                   FontSize="11"
                   HorizontalAlignment="Center"
                   Foreground="#605E5C"
                   Margin="0,15,0,0"/>
    </Grid>
</Window>
