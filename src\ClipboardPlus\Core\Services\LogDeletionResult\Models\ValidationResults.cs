using System;
using System.Collections.Generic;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Models
{
    /// <summary>
    /// Résultat de validation post-suppression d'un élément.
    /// </summary>
    public class DeletionValidationResult
    {
        /// <summary>
        /// Indique si la validation a réussi.
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Indique si l'élément est encore présent dans la collection du ViewModel.
        /// </summary>
        public bool ItemStillInCollection { get; set; }

        /// <summary>
        /// Indique si l'élément est encore présent dans le gestionnaire d'historique.
        /// </summary>
        public bool ItemStillInManager { get; set; }

        /// <summary>
        /// Élément trouvé par référence (si présent).
        /// </summary>
        public ClipboardItem? FoundByReference { get; set; }

        /// <summary>
        /// Élément trouvé par ID (si présent).
        /// </summary>
        public ClipboardItem? FoundById { get; set; }

        /// <summary>
        /// Indique si les instances trouvées sont identiques.
        /// </summary>
        public bool InstancesMatch { get; set; }

        /// <summary>
        /// Liste des problèmes détectés.
        /// </summary>
        public List<ValidationIssue> Issues { get; set; } = new();

        /// <summary>
        /// Timestamp de la validation.
        /// </summary>
        public DateTime ValidationTimestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Résultat de validation de l'état des collections.
    /// </summary>
    public class CollectionValidationResult
    {
        /// <summary>
        /// Indique si l'état des collections est valide.
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Nombre d'éléments dans la collection du ViewModel.
        /// </summary>
        public int ViewModelItemCount { get; set; }

        /// <summary>
        /// Nombre d'éléments dans le gestionnaire d'historique.
        /// </summary>
        public int ManagerItemCount { get; set; }

        /// <summary>
        /// Indique si les collections sont synchronisées.
        /// </summary>
        public bool CollectionsInSync { get; set; }

        /// <summary>
        /// Nombre d'éléments null détectés.
        /// </summary>
        public int NullItemCount { get; set; }

        /// <summary>
        /// Nombre de doublons détectés.
        /// </summary>
        public int DuplicateCount { get; set; }

        /// <summary>
        /// Liste des anomalies détectées.
        /// </summary>
        public List<CollectionAnomaly> Anomalies { get; set; } = new();

        /// <summary>
        /// Timestamp de la validation.
        /// </summary>
        public DateTime ValidationTimestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Résultat de validation de cohérence entre les composants.
    /// </summary>
    public class ConsistencyValidationResult
    {
        /// <summary>
        /// Indique si la cohérence est maintenue.
        /// </summary>
        public bool IsConsistent { get; set; }

        /// <summary>
        /// Indique si le gestionnaire d'historique est accessible.
        /// </summary>
        public bool ManagerAccessible { get; set; }

        /// <summary>
        /// Indique si le ViewModel est dans un état valide.
        /// </summary>
        public bool ViewModelValid { get; set; }

        /// <summary>
        /// Indique si une opération de mise à jour est en cours.
        /// </summary>
        public bool UpdateInProgress { get; set; }

        /// <summary>
        /// Liste des incohérences détectées.
        /// </summary>
        public List<ConsistencyIssue> Inconsistencies { get; set; } = new();

        /// <summary>
        /// Timestamp de la validation.
        /// </summary>
        public DateTime ValidationTimestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Résultat de validation complet (toutes les validations combinées).
    /// </summary>
    public class ComprehensiveValidationResult
    {
        /// <summary>
        /// Identifiant unique de la validation.
        /// </summary>
        public Guid ValidationId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Indique si toutes les validations ont réussi.
        /// </summary>
        public bool IsFullyValid { get; set; }

        /// <summary>
        /// Résultat de la validation post-suppression.
        /// </summary>
        public DeletionValidationResult? PostDeletionValidation { get; set; }

        /// <summary>
        /// Résultat de la validation des collections.
        /// </summary>
        public CollectionValidationResult? CollectionValidation { get; set; }

        /// <summary>
        /// Résultat de la validation de cohérence.
        /// </summary>
        public ConsistencyValidationResult? ConsistencyValidation { get; set; }

        /// <summary>
        /// Durée totale de la validation.
        /// </summary>
        public TimeSpan ValidationDuration { get; set; }

        /// <summary>
        /// Timestamp de début de validation.
        /// </summary>
        public DateTime StartTimestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Timestamp de fin de validation.
        /// </summary>
        public DateTime EndTimestamp { get; set; }

        /// <summary>
        /// Nombre total de problèmes détectés.
        /// </summary>
        public int TotalIssuesCount => 
            (PostDeletionValidation?.Issues.Count ?? 0) +
            (CollectionValidation?.Anomalies.Count ?? 0) +
            (ConsistencyValidation?.Inconsistencies.Count ?? 0);

        /// <summary>
        /// Marque la validation comme terminée.
        /// </summary>
        public void MarkCompleted()
        {
            EndTimestamp = DateTime.Now;
            ValidationDuration = EndTimestamp - StartTimestamp;
            
            IsFullyValid = (PostDeletionValidation?.IsValid ?? true) &&
                          (CollectionValidation?.IsValid ?? true) &&
                          (ConsistencyValidation?.IsConsistent ?? true);
        }
    }

    /// <summary>
    /// Problème détecté lors de la validation.
    /// </summary>
    public class ValidationIssue
    {
        /// <summary>
        /// Niveau de sévérité du problème.
        /// </summary>
        public ValidationSeverity Severity { get; set; }

        /// <summary>
        /// Code du problème.
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Description du problème.
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Détails supplémentaires.
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// Élément concerné (si applicable).
        /// </summary>
        public ClipboardItem? RelatedItem { get; set; }

        /// <summary>
        /// Timestamp de détection.
        /// </summary>
        public DateTime DetectedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Anomalie détectée dans une collection.
    /// </summary>
    public class CollectionAnomaly
    {
        /// <summary>
        /// Type d'anomalie.
        /// </summary>
        public AnomalyType Type { get; set; }

        /// <summary>
        /// Description de l'anomalie.
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Éléments concernés.
        /// </summary>
        public List<ClipboardItem> AffectedItems { get; set; } = new();

        /// <summary>
        /// Nombre d'occurrences.
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// Timestamp de détection.
        /// </summary>
        public DateTime DetectedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Incohérence détectée entre les composants.
    /// </summary>
    public class ConsistencyIssue
    {
        /// <summary>
        /// Type d'incohérence.
        /// </summary>
        public ConsistencyIssueType Type { get; set; }

        /// <summary>
        /// Description de l'incohérence.
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Composants affectés.
        /// </summary>
        public List<string> AffectedComponents { get; set; } = new();

        /// <summary>
        /// Impact estimé.
        /// </summary>
        public ConsistencyImpact Impact { get; set; }

        /// <summary>
        /// Timestamp de détection.
        /// </summary>
        public DateTime DetectedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Niveaux de sévérité des problèmes de validation.
    /// </summary>
    public enum ValidationSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    /// <summary>
    /// Types d'anomalies dans les collections.
    /// </summary>
    public enum AnomalyType
    {
        NullItem,
        DuplicateId,
        InvalidData,
        OrphanedItem,
        CorruptedItem
    }

    /// <summary>
    /// Types d'incohérences entre composants.
    /// </summary>
    public enum ConsistencyIssueType
    {
        CountMismatch,
        DataMismatch,
        StateInconsistency,
        SynchronizationIssue,
        AccessibilityProblem
    }

    /// <summary>
    /// Impact des incohérences.
    /// </summary>
    public enum ConsistencyImpact
    {
        Low,
        Medium,
        High,
        Critical
    }
}
