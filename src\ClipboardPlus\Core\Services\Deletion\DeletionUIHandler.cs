using System.Collections.ObjectModel;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Implémentation du service de gestion UI pour les opérations de suppression.
    /// Gère la suppression et le rollback des éléments dans l'interface utilisateur.
    /// </summary>
    public class DeletionUIHandler : IDeletionUIHandler
    {
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du gestionnaire UI de suppression.
        /// </summary>
        /// <param name="loggingService">Service de logging optionnel</param>
        public DeletionUIHandler(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Supprime un élément de la collection UI et mémorise sa position pour un éventuel rollback.
        /// </summary>
        /// <param name="collection">La collection d'éléments UI</param>
        /// <param name="item">L'élément à supprimer</param>
        /// <returns>Les informations de suppression pour un éventuel rollback</returns>
        public UIRemovalInfo RemoveFromUI(ObservableCollection<ClipboardItem> collection, ClipboardItem item)
        {
            try
            {
                // Trouver l'index de l'élément avant suppression
                var originalIndex = collection.IndexOf(item);
                
                if (originalIndex == -1)
                {
                    _loggingService?.LogWarning($"Élément ID={item.Id} non trouvé dans la collection UI");
                    return UIRemovalInfo.CreateFailure(item);
                }

                // Supprimer l'élément de la collection UI
                collection.RemoveAt(originalIndex);
                
                _loggingService?.LogDebug($"Élément ID={item.Id} supprimé de l'UI à l'index {originalIndex}");
                
                return UIRemovalInfo.CreateSuccess(item, originalIndex);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors de la suppression UI de l'élément ID={item.Id} : {ex.Message}", ex);
                return UIRemovalInfo.CreateFailure(item);
            }
        }

        /// <summary>
        /// Restaure un élément dans la collection UI à sa position d'origine.
        /// </summary>
        /// <param name="collection">La collection d'éléments UI</param>
        /// <param name="removalInfo">Les informations de suppression</param>
        public void RollbackUI(ObservableCollection<ClipboardItem> collection, UIRemovalInfo removalInfo)
        {
            try
            {
                if (!removalInfo.RemovalSuccessful)
                {
                    _loggingService?.LogWarning($"Rollback ignoré : suppression originale de l'élément ID={removalInfo.Item.Id} avait échoué");
                    return;
                }

                // Déterminer la position de restauration
                var insertIndex = removalInfo.OriginalIndex;
                
                // Vérifier que l'index est valide pour la collection actuelle
                if (insertIndex < 0 || insertIndex > collection.Count)
                {
                    // Si l'index original n'est plus valide, insérer au début
                    insertIndex = 0;
                    _loggingService?.LogWarning($"Index original {removalInfo.OriginalIndex} invalide, insertion à l'index 0");
                }

                // Restaurer l'élément à sa position
                collection.Insert(insertIndex, removalInfo.Item);
                
                _loggingService?.LogInfo($"Rollback UI réussi : élément ID={removalInfo.Item.Id} restauré à l'index {insertIndex}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors du rollback UI de l'élément ID={removalInfo.Item.Id} : {ex.Message}", ex);
                
                // Tentative de restauration d'urgence au début de la collection
                try
                {
                    collection.Insert(0, removalInfo.Item);
                    _loggingService?.LogInfo($"Rollback d'urgence réussi : élément ID={removalInfo.Item.Id} restauré à l'index 0");
                }
                catch (Exception emergencyEx)
                {
                    _loggingService?.LogError($"Échec du rollback d'urgence pour l'élément ID={removalInfo.Item.Id} : {emergencyEx.Message}", emergencyEx);
                }
            }
        }
    }
}
