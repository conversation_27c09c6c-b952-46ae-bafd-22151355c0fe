using System;
using System.Threading.Tasks;
using ClipboardPlus.Modules.Core;
using NUnit.Framework;
using ModuleState = ClipboardPlus.Modules.Core.ModuleState;

namespace ClipboardPlus.Tests.Unit.Modules.Core
{
    /// <summary>
    /// Tests unitaires pour ModuleBase
    /// </summary>
    [TestFixture]
    public class ModuleBaseTests
    {
        private TestModule _testModule = null!;

        [SetUp]
        public void SetUp()
        {
            _testModule = new TestModule();
        }

        [TearDown]
        public void TearDown()
        {
            _testModule?.Dispose();
        }

        #region Tests de propriétés de base

        [Test]
        public void ModuleName_ShouldReturnCorrectName()
        {
            // Assert
            Assert.That(_testModule.ModuleName, Is.EqualTo("TestModule"));
        }

        [Test]
        public void ModuleVersion_ShouldReturnCorrectVersion()
        {
            // Assert
            Assert.That(_testModule.ModuleVersion, Is.EqualTo(new Version(1, 0, 0)));
        }

        [Test]
        public void State_InitialValue_ShouldBeCreated()
        {
            // Assert
            Assert.That(_testModule.State, Is.EqualTo(ModuleState.Created));
        }

        #endregion

        #region Tests du cycle de vie

        [Test]
        public async Task InitializeAsync_ShouldChangeStateToInitialized()
        {
            // Act
            await _testModule.InitializeAsync();

            // Assert
            Assert.That(_testModule.State, Is.EqualTo(ModuleState.Initialized));
        }

        [Test]
        public async Task StartAsync_AfterInitialize_ShouldChangeStateToRunning()
        {
            // Arrange
            await _testModule.InitializeAsync();

            // Act
            await _testModule.StartAsync();

            // Assert
            Assert.That(_testModule.State, Is.EqualTo(ModuleState.Running));
        }

        [Test]
        public async Task StopAsync_AfterStart_ShouldChangeStateToStopped()
        {
            // Arrange
            await _testModule.InitializeAsync();
            await _testModule.StartAsync();

            // Act
            await _testModule.StopAsync();

            // Assert
            Assert.That(_testModule.State, Is.EqualTo(ModuleState.Stopped));
        }

        [Test]
        public void StartAsync_WithoutInitialize_ShouldThrowModuleOperationException()
        {
            // Act & Assert
            Assert.ThrowsAsync<ModuleOperationException>(async () => await _testModule.StartAsync());
        }

        [Test]
        public void StopAsync_WithoutStart_ShouldThrowModuleOperationException()
        {
            // Act & Assert
            Assert.ThrowsAsync<ModuleOperationException>(async () => await _testModule.StopAsync());
        }

        #endregion

        #region Tests d'événements

        [Test]
        public async Task StateChanged_ShouldBeRaisedOnStateChange()
        {
            // Arrange
            var eventRaised = false;
            ModuleStateChangedEventArgs? eventArgs = null;

            _testModule.StateChanged += (sender, args) =>
            {
                // Capturer seulement le dernier événement (Initialized)
                if (args.NewState == ModuleState.Initialized)
                {
                    eventRaised = true;
                    eventArgs = args;
                }
            };

            // Act
            await _testModule.InitializeAsync();

            // Assert
            Assert.That(eventRaised, Is.True);
            Assert.That(eventArgs, Is.Not.Null);
            Assert.That(eventArgs.NewState, Is.EqualTo(ModuleState.Initialized));
        }

        [Test]
        public async Task StateChanged_ShouldNotBeRaisedForSameState()
        {
            // Arrange
            await _testModule.InitializeAsync();
            var eventRaisedCount = 0;

            _testModule.StateChanged += (sender, args) =>
            {
                eventRaisedCount++;
            };

            // Act - Essayer d'initialiser à nouveau (devrait lever une exception)
            // Assert
            Assert.ThrowsAsync<ModuleOperationException>(async () => await _testModule.InitializeAsync());
            Assert.That(eventRaisedCount, Is.EqualTo(0));
        }

        #endregion



        #region Tests de thread safety

        [Test]
        public async Task State_ConcurrentAccess_ShouldBeThreadSafe()
        {
            // Arrange
            var tasks = new Task[10];
            var states = new ModuleState[10];

            // Act - Accès concurrent à la propriété State
            for (int i = 0; i < 10; i++)
            {
                int index = i;
                tasks[i] = Task.Run(() =>
                {
                    states[index] = _testModule.State;
                });
            }

            await Task.WhenAll(tasks);

            // Assert - Tous les états devraient être identiques
            for (int i = 0; i < 10; i++)
            {
                Assert.That(states[i], Is.EqualTo(ModuleState.Created));
            }
        }

        #endregion

        #region Tests de dispose

        [Test]
        public void Dispose_ShouldChangeStateToDisposed()
        {
            // Act
            _testModule.Dispose();

            // Assert
            Assert.That(_testModule.State, Is.EqualTo(ModuleState.Disposed));
        }

        [Test]
        public void Dispose_CalledTwice_ShouldNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _testModule.Dispose();
                _testModule.Dispose();
            });
        }

        [Test]
        public async Task InitializeAsync_AfterDispose_ShouldThrowObjectDisposedException()
        {
            // Arrange
            _testModule.Dispose();

            // Act & Assert
            Assert.ThrowsAsync<ObjectDisposedException>(async () => await _testModule.InitializeAsync());
        }

        #endregion

        #region Classe de test

        /// <summary>
        /// Implémentation de test de ModuleBase pour les tests unitaires
        /// </summary>
        private class TestModule : ModuleBase
        {
            public override string ModuleName => "TestModule";
            public override Version ModuleVersion => new Version(1, 0, 0);

            protected override Task OnInitializeAsync()
            {
                return Task.CompletedTask;
            }

            protected override Task OnStartAsync()
            {
                return Task.CompletedTask;
            }

            protected override Task OnStopAsync()
            {
                return Task.CompletedTask;
            }

            protected override void OnDispose()
            {
                // Implémentation de test vide
            }


        }

        #endregion
    }
}
