using System;
using System.Threading;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services
{
    public interface IApplicationLifetimeManager
    {
        Task<ISystemTrayService?> InitializeServices(IServiceProvider services, EventHandler shortcutHandler, EventHandler clipboardHandler);
        void Shutdown(IServiceProvider? services, ISystemTrayService? systemTrayService, Mutex? appMutex, bool ownsMutex);
        Task ProcessClipboardContentAsync();
    }
} 