using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using ClipboardPlus.Core.DataModels;
using CommunityToolkit.Mvvm.Input;

namespace ClipboardPlus.Tests.Unit.UI.Controls
{
    /// <summary>
    /// Interface commune pour les mocks du ViewModel ClipboardHistoryViewModel
    /// </summary>
    public interface ITestClipboardHistoryViewModel : INotifyPropertyChanged
    {
        ClipboardItem? ItemEnRenommage { get; set; }
        string NouveauNom { get; set; }
        ObservableCollection<ClipboardItem> HistoryItems { get; }
        IRelayCommand<ClipboardItem> DemarrerRenommageCommand { get; }
        IRelayCommand AnnulerRenommageCommand { get; }
        IRelayCommand ConfirmerRenommageCommand { get; }
        IRelayCommand<ClipboardItem> BasculerEpinglageCommand { get; }
        IRelayCommand<ClipboardItem> AfficherPreviewCommand { get; }
        IRelayCommand<ClipboardItem> SupprimerElementCommand { get; }
    }
}
