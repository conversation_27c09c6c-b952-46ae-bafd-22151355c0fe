using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using System.Reflection;
using System.Diagnostics;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests de caractérisation pour la méthode WriteToLog.
    /// Ces tests documentent et protègent le comportement existant avant refactorisation.
    /// 
    /// CRITIQUE : Ces tests doivent passer à 100% avant toute modification de WriteToLog.
    /// </summary>
    [TestFixture]
    public class WriteToLogCharacterizationTests
    {
        private LoggingService _loggingService = null!;
        private string _originalLogPath = string.Empty;

        [SetUp]
        public void SetUp()
        {
            _loggingService = new LoggingService();
            _originalLogPath = _loggingService.GetLogFilePath();
        }

        [TearDown]
        public void TearDown()
        {
            _loggingService?.Dispose();
            
            // Nettoyer le fichier de log de test
            try
            {
                if (File.Exists(_originalLogPath))
                {
                    File.Delete(_originalLogPath);
                }
            }
            catch
            {
                // Ignorer les erreurs de nettoyage
            }
        }

        /// <summary>
        /// Test 1: Caractérisation du comportement avec service non initialisé
        /// </summary>
        [Test]
        public void WriteToLog_WithUninitializedService_ShouldSkipNonCriticalLogs()
        {
            // Arrange
            var uninitializedService = CreateUninitializedService();
            var initialLogSize = GetLogFileSize(uninitializedService.GetLogFilePath());

            // Act
            uninitializedService.LogDebug("Debug message - should be skipped");
            uninitializedService.LogInfo("Info message - should be skipped");
            Thread.Sleep(100); // Attendre le traitement

            // Assert
            var finalLogSize = GetLogFileSize(uninitializedService.GetLogFilePath());
            Assert.That(finalLogSize, Is.EqualTo(initialLogSize), 
                "Les logs non critiques ne doivent pas être écrits si le service n'est pas initialisé");

            uninitializedService.Dispose();
        }

        /// <summary>
        /// Test 2: Caractérisation du formatage des messages multi-lignes
        /// </summary>
        [Test]
        public void WriteToLog_WithMultilineMessage_ShouldFormatCorrectly()
        {
            // Arrange
            string multilineMessage = "Ligne 1\r\nLigne 2\nLigne 3\r\n\r\nLigne 5";
            
            // Act
            _loggingService.LogInfo(multilineMessage);
            _loggingService.ForceFlush();
            Thread.Sleep(100);

            // Assert
            var logContent = ReadLogFile();
            
            // Vérifier que la première ligne contient le header complet
            Assert.That(logContent, Does.Contain("[INFO]"), "Le niveau de log doit être présent");
            Assert.That(logContent, Does.Contain("[Thread:"), "L'ID du thread doit être présent");
            Assert.That(logContent, Does.Contain("Ligne 1"), "La première ligne du message doit être présente");
            
            // Vérifier que les lignes suivantes sont indentées
            Assert.That(logContent, Does.Contain("    Ligne 2"), "Les lignes suivantes doivent être indentées");
            Assert.That(logContent, Does.Contain("    Ligne 3"), "Les lignes suivantes doivent être indentées");
        }

        /// <summary>
        /// Test 3: Caractérisation de la détection du contexte UI-Thread vs Background
        /// </summary>
        [Test]
        public void WriteToLog_OnDifferentThreads_ShouldDetectContext()
        {
            // Arrange & Act - Thread principal
            _loggingService.LogInfo("Message from main thread");
            _loggingService.ForceFlush();

            // Act - Thread background
            string backgroundLogContent = "";
            var backgroundTask = Task.Run(() =>
            {
                var backgroundService = new LoggingService();
                backgroundService.LogInfo("Message from background thread");
                backgroundService.ForceFlush();
                Thread.Sleep(100);
                backgroundLogContent = ReadLogFile(backgroundService.GetLogFilePath());
                backgroundService.Dispose();
            });
            backgroundTask.Wait();

            // Assert
            var mainLogContent = ReadLogFile();
            
            // Le contexte doit être détecté (UI-Thread, Background, ou Non-UI)
            Assert.That(mainLogContent, Does.Match(@"\[(UI-Thread|Background|Non-UI|Inconnu)\]"), 
                "Le contexte d'exécution doit être détecté et loggé");
        }

        /// <summary>
        /// Test 4: Caractérisation de la gestion du buffer et conditions de flush
        /// </summary>
        [Test]
        public void WriteToLog_WithBufferFull_ShouldTriggerFlush()
        {
            // Arrange - Remplir le buffer (MAX_BUFFER_SIZE = 100)
            for (int i = 0; i < 50; i++)
            {
                _loggingService.LogDebug($"Message de remplissage {i}");
            }

            var initialLogSize = GetLogFileSize();

            // Act - Ajouter des messages pour dépasser le buffer
            for (int i = 50; i < 110; i++)
            {
                _loggingService.LogDebug($"Message de dépassement {i}");
            }
            Thread.Sleep(200); // Attendre le flush automatique

            // Assert
            var finalLogSize = GetLogFileSize();
            Assert.That(finalLogSize, Is.GreaterThan(initialLogSize), 
                "Le buffer doit être flushé automatiquement quand il dépasse MAX_BUFFER_SIZE");
        }

        /// <summary>
        /// Test 5: Caractérisation de l'affichage console avec couleurs
        /// </summary>
        [Test]
        public void WriteToLog_WithConsoleEnabled_ShouldDisplayColors()
        {
            // Arrange
            _loggingService.EnableConsoleOutput(true);
            var originalOut = Console.Out;
            var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            try
            {
                // Act
                _loggingService.LogDebug("Debug message");
                _loggingService.LogInfo("Info message");
                _loggingService.LogWarning("Warning message");
                _loggingService.LogError("Error message");
                _loggingService.LogCritical("Critical message");
                _loggingService.LogDeletion("Deletion message");
                
                Thread.Sleep(100);

                // Assert
                var consoleOutput = stringWriter.ToString();
                
                // Vérifier que les messages sont affichés dans la console
                Assert.That(consoleOutput, Does.Contain("Debug message"), "Les messages DEBUG doivent être affichés");
                Assert.That(consoleOutput, Does.Contain("Info message"), "Les messages INFO doivent être affichés");
                Assert.That(consoleOutput, Does.Contain("Warning message"), "Les messages WARNING doivent être affichés");
                Assert.That(consoleOutput, Does.Contain("Error message"), "Les messages ERROR doivent être affichés");
                Assert.That(consoleOutput, Does.Contain("Critical message"), "Les messages CRITICAL doivent être affichés");
                Assert.That(consoleOutput, Does.Contain("Deletion message"), "Les messages DELETION doivent être affichés");
            }
            finally
            {
                Console.SetOut(originalOut);
                stringWriter.Dispose();
            }
        }

        /// <summary>
        /// Test 6: Caractérisation des niveaux critiques qui forcent le flush
        /// </summary>
        [Test]
        public void WriteToLog_WithCriticalLevels_ShouldForceFlush()
        {
            // Arrange
            var initialLogSize = GetLogFileSize();

            // Act - Messages critiques qui doivent forcer un flush immédiat
            _loggingService.LogError("Error message");
            Thread.Sleep(50);
            var afterErrorSize = GetLogFileSize();

            _loggingService.LogCritical("Critical message");
            Thread.Sleep(50);
            var afterCriticalSize = GetLogFileSize();

            _loggingService.LogDeletion("Deletion message");
            Thread.Sleep(50);
            var afterDeletionSize = GetLogFileSize();

            // Assert
            Assert.That(afterErrorSize, Is.GreaterThan(initialLogSize), 
                "LogError doit forcer un flush immédiat");
            Assert.That(afterCriticalSize, Is.GreaterThan(afterErrorSize), 
                "LogCritical doit forcer un flush immédiat");
            Assert.That(afterDeletionSize, Is.GreaterThan(afterCriticalSize), 
                "LogDeletion doit forcer un flush immédiat");
        }

        /// <summary>
        /// Test 7: Caractérisation de la gestion d'erreurs lors de l'écriture
        /// </summary>
        [Test]
        public void WriteToLog_WithWriteError_ShouldHandleGracefully()
        {
            // Arrange - Créer un service avec un chemin de fichier invalide
            var invalidPathService = CreateServiceWithInvalidPath();

            // Act & Assert - Ne doit pas lever d'exception
            Assert.DoesNotThrow(() =>
            {
                invalidPathService.LogInfo("Test message with invalid path");
                invalidPathService.ForceFlush();
                Thread.Sleep(100);
            }, "WriteToLog doit gérer gracieusement les erreurs d'écriture");

            invalidPathService.Dispose();
        }

        #region Helper Methods

        private LoggingService CreateUninitializedService()
        {
            // Utiliser la réflexion pour créer un service non initialisé
            var service = new LoggingService();
            var isInitializedField = typeof(LoggingService).GetField("_isInitialized", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            isInitializedField?.SetValue(service, false);
            return service;
        }

        private LoggingService CreateServiceWithInvalidPath()
        {
            // Créer un service et modifier son chemin de fichier pour qu'il soit invalide
            var service = new LoggingService();
            var logFilePathField = typeof(LoggingService).GetField("_logFilePath", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            logFilePathField?.SetValue(service, "Z:\\invalid\\path\\test.log");
            return service;
        }

        private long GetLogFileSize(string? customPath = null)
        {
            var path = customPath ?? _originalLogPath;
            return File.Exists(path) ? new FileInfo(path).Length : 0;
        }

        private string ReadLogFile(string? customPath = null)
        {
            var path = customPath ?? _originalLogPath;
            if (!File.Exists(path)) return string.Empty;
            
            try
            {
                return File.ReadAllText(path);
            }
            catch
            {
                return string.Empty;
            }
        }

        #endregion
    }
}
