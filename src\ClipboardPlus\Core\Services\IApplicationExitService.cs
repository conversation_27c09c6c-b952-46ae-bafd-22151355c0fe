using System;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service responsable de l'orchestration de la séquence de fermeture de l'application.
    /// Extrait la logique complexe de App.OnExit pour respecter le principe de responsabilité unique.
    /// </summary>
    public interface IApplicationExitService
    {
        /// <summary>
        /// Exécute la séquence complète de fermeture de l'application.
        /// Cette méthode centralise toute la logique qui était dans App.OnExit.
        /// </summary>
        /// <param name="services">Le fournisseur de services pour résoudre les dépendances</param>
        /// <returns>Une tâche représentant l'opération asynchrone</returns>
        Task ExecuteExitSequenceAsync(IServiceProvider? services);
    }
}
