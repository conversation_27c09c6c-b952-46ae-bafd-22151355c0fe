using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using WpfApplication = System.Windows.Application;
using WpfMessageBox = System.Windows.MessageBox;
using System.Windows.Forms;
using System.Windows.Threading;
using System.Windows.Input;

namespace ClipboardPlus.UI.Views
{
    /// <summary>
    /// Logique d'interaction pour AppSettingsWindow.xaml
    /// </summary>
    public partial class AppSettingsWindow : Window
    {
        /// <summary>
        /// Méthode factory asynchrone pour créer une instance de AppSettingsWindow avec un ViewModel entièrement initialisé.
        /// </summary>
        /// <param name="services">Le fournisseur de services pour résoudre les dépendances.</param>
        /// <returns>Une instance de AppSettingsWindow prête à être affichée.</returns>
        public static async Task<AppSettingsWindow> CreateAsync(IServiceProvider services)
        {
            // Vérifier que le conteneur de services est valide
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }

            var loggingService = services.GetService<ILoggingService>();
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            loggingService?.LogInfo($"[{operationId}] CreateAsync: Début de la création de la fenêtre des paramètres");

            try
            {
                // Résoudre les dépendances nécessaires
                var settingsManager = services.GetRequiredService<ISettingsManager>();
                var themeManager = services.GetRequiredService<IUserThemeManager>();
                var shortcutService = services.GetRequiredService<IGlobalShortcutService>();
                var userNotificationService = services.GetRequiredService<IUserNotificationService>();

                loggingService?.LogInfo($"[{operationId}] CreateAsync: Services résolus avec succès");

                // Créer et initialiser le ViewModel
                var viewModel = new AppSettingsViewModel(settingsManager, themeManager, shortcutService, userNotificationService, loggingService);
                
                loggingService?.LogInfo($"[{operationId}] CreateAsync: ViewModel créé, initialisation en cours...");
                
                // Attendre que le ViewModel soit entièrement initialisé
                await viewModel.InitializeViewModelAsync();
                
                loggingService?.LogInfo($"[{operationId}] CreateAsync: ViewModel initialisé avec succès");

                // Créer la fenêtre et lui assigner le ViewModel initialisé
                var window = new AppSettingsWindow();
                window.DataContext = viewModel;

                // Préinitialiser la fenêtre pour éviter les problèmes de positionnement
                window.PreInitialize();
                
                loggingService?.LogInfo($"[{operationId}] CreateAsync: Fenêtre créée et préinitialisée avec succès");

                return window;
            }
            catch (Exception ex)
            {
                // Journaliser et relancer l'exception
                loggingService?.LogError($"[{operationId}] CreateAsync: Erreur lors de la création de AppSettingsWindow: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Initialise une nouvelle instance de la fenêtre des paramètres.
        /// </summary>
        public AppSettingsWindow()
        {
            InitializeComponent();
            
            // Abonner aux événements essentiels
            this.Loaded += AppSettingsWindow_Loaded;
            this.Closing += AppSettingsWindow_Closing;
            
            // Abonner à l'événement PreviewMouseDown pour effacer le message de statut
            this.PreviewMouseDown += AppSettingsWindow_PreviewMouseDown;
        }

        private void AppSettingsWindow_LocationChanged(object? sender, EventArgs e)
        {
            if (DataContext is AppSettingsViewModel vm && WindowState == WindowState.Normal && IsLoaded)
        {
            try
            {
                    // Éviter les mises à jour inutiles si la position n'a pas vraiment changé
                    double currentTop = vm.SettingsManager.SettingsWindowTop;
                    double currentLeft = vm.SettingsManager.SettingsWindowLeft;
                
                    // Utiliser un seuil pour éviter les mises à jour trop fréquentes
                    const double threshold = 1.0;
                    if (Math.Abs(currentTop - Top) > threshold || Math.Abs(currentLeft - Left) > threshold)
                    {
                        vm.LoggingService?.LogInfo($"[WINDOW_EVENT] LocationChanged: T={Top:F1}, L={Left:F1}");
                        vm.SettingsManager.SettingsWindowTop = Top;
                        vm.SettingsManager.SettingsWindowLeft = Left;
                    }
                }
                catch (Exception ex)
                {
                    // Journaliser l'erreur mais ne pas la propager
                    vm.LoggingService?.LogError($"[WINDOW_EVENT] Erreur dans LocationChanged: {ex.Message}", ex);
                }
            }
        }

        private void AppSettingsWindow_SizeChanged(object sender, SizeChangedEventArgs e)
                    {
            if (DataContext is AppSettingsViewModel vm && WindowState == WindowState.Normal && IsLoaded)
            {
                try
                {
                    // Éviter les mises à jour inutiles si la taille n'a pas vraiment changé
                    double currentWidth = vm.SettingsManager.SettingsWindowWidth;
                    double currentHeight = vm.SettingsManager.SettingsWindowHeight;
                    
                    // Utiliser un seuil pour éviter les mises à jour trop fréquentes
                    const double threshold = 1.0;
                    if (Math.Abs(currentWidth - ActualWidth) > threshold || Math.Abs(currentHeight - ActualHeight) > threshold)
                    {
                        vm.LoggingService?.LogInfo($"[WINDOW_EVENT] SizeChanged: W={ActualWidth:F1}, H={ActualHeight:F1}");
                        vm.SettingsManager.SettingsWindowWidth = ActualWidth;
                        vm.SettingsManager.SettingsWindowHeight = ActualHeight;
                    }
                }
                catch (Exception ex)
                {
                    // Journaliser l'erreur mais ne pas la propager
                    vm.LoggingService?.LogError($"[WINDOW_EVENT] Erreur dans SizeChanged: {ex.Message}", ex);
                }
            }
        }

        private void AppSettingsWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
                        {
            if (DataContext is AppSettingsViewModel vm)
            {
                var operationId = Guid.NewGuid().ToString().Substring(0, 8);
                vm.LoggingService?.LogInfo($"[{operationId}] AppSettingsWindow_Closing: Début de la fermeture. " +
                                          $"State: {WindowState}, W: {ActualWidth}, H: {ActualHeight}, T: {Top}, L: {Left}");
                
                try
                {
                    // Ne sauvegarder les dimensions que si la fenêtre est dans un état normal
                    if (WindowState == WindowState.Normal)
                    {
                        // Sauvegarder les dimensions finales dans le SettingsManager
                        vm.SettingsManager.SettingsWindowWidth = ActualWidth;
                        vm.SettingsManager.SettingsWindowHeight = ActualHeight;
                        vm.SettingsManager.SettingsWindowTop = Top;
                        vm.SettingsManager.SettingsWindowLeft = Left;
                        
                        vm.LoggingService?.LogInfo($"[{operationId}] AppSettingsWindow_Closing: Dimensions finales sauvegardées en mémoire: " +
                                                  $"W={ActualWidth}, H={ActualHeight}, T={Top}, L={Left}");
                        
                        // Lancer la sauvegarde asynchrone sans bloquer la fermeture de la fenêtre
                        Task.Run(async () => {
                            try
                            {
                                await vm.SettingsManager.SaveSettingsToPersistenceAsync();
                                vm.LoggingService?.LogInfo($"[{operationId}] AppSettingsWindow_Closing: Sauvegarde asynchrone des paramètres réussie");
                            }
                            catch (Exception ex)
                            {
                                vm.LoggingService?.LogError($"[{operationId}] AppSettingsWindow_Closing: Erreur lors de la sauvegarde asynchrone des paramètres", ex);
                            }
                        });
                    }
                    else
                    {
                        vm.LoggingService?.LogInfo($"[{operationId}] AppSettingsWindow_Closing: Fenêtre non normale (état: {WindowState}), dimensions non sauvegardées.");
                    }
                }
                catch (Exception ex)
                {
                    vm.LoggingService?.LogError($"[{operationId}] AppSettingsWindow_Closing: Erreur lors de la fermeture: {ex.Message}", ex);
                }
            }
        }

        private void AppSettingsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            if (DataContext is AppSettingsViewModel viewModel)
            {
                var operationId = Guid.NewGuid().ToString().Substring(0, 8);
                viewModel.LoggingService?.LogInfo($"[{operationId}] AppSettingsWindow_Loaded: Début du chargement");
                
                try
                {
                    // S'abonner aux événements de changement de taille et de position
                    // Cela doit être fait après le chargement pour éviter les événements indésirables
                    // pendant l'initialisation de la fenêtre
                    this.SizeChanged += AppSettingsWindow_SizeChanged;
                    this.LocationChanged += AppSettingsWindow_LocationChanged;
                    
                    viewModel.LoggingService?.LogInfo($"[{operationId}] AppSettingsWindow_Loaded: Abonnement aux événements SizeChanged et LocationChanged");
                    
                    // Vérifier que les dimensions finales sont correctes
                    viewModel.LoggingService?.LogInfo($"[{operationId}] AppSettingsWindow_Loaded: Dimensions finales - " +
                                                     $"W={ActualWidth}, H={ActualHeight}, T={Top}, L={Left}, " +
                                                     $"WindowStartupLocation={WindowStartupLocation}");
            }
            catch (Exception ex)
            {
                    viewModel.LoggingService?.LogError($"[{operationId}] AppSettingsWindow_Loaded: Erreur: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour le bouton OK.
        /// </summary>
        private async void OkButton_Click(object sender, RoutedEventArgs e)
        {
            await ApplySettingsAsync();
            DialogResult = true;
            Close();
        }

        /// <summary>
        /// Gestionnaire d'événement pour le bouton Annuler.
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // La propriété IsCancel=true du bouton gère la fermeture et DialogResult=false
            this.Close();
        }
        
        /// <summary>
        /// Gestionnaire d'événement pour le bouton Appliquer.
        /// </summary>
        private async void ApplyButton_Click(object sender, RoutedEventArgs e)
        {
            await ApplySettingsAsync();
        }
        
        /// <summary>
        /// Logique partagée pour appliquer les paramètres.
        /// </summary>
        private async Task ApplySettingsAsync()
        {
            if (DataContext is not AppSettingsViewModel vm) return;

            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            vm.LoggingService?.LogInfo($"[{operationId}] ApplySettingsAsync: Début de l'application des paramètres");

            try
            {
                // Désactiver les boutons pendant l'opération
                ApplyButton.IsEnabled = false;
                OkButton.IsEnabled = false;
                vm.LoggingService?.LogInfo($"[{operationId}] ApplySettingsAsync: Boutons désactivés");

                // La sauvegarde des dimensions est maintenant gérée par le Closing et App.OnExit.
                // Nous appelons directement la commande du ViewModel pour les autres paramètres.
                if (vm.ApplySettingsCommand.CanExecute(null))
                    {
                    await vm.ApplySettingsCommand.ExecuteAsync(null);
                    vm.LoggingService?.LogInfo($"[{operationId}] ApplySettingsAsync: Paramètres appliqués avec succès");
                }
                else
                {
                    vm.LoggingService?.LogWarning($"[{operationId}] ApplySettingsAsync: La commande ApplySettingsCommand ne peut pas être exécutée");
                }
            }
            catch (Exception ex)
            {
                vm.LoggingService?.LogError($"[{operationId}] ApplySettingsAsync: Erreur lors de l'application des paramètres: {ex.Message}", ex);
                WpfMessageBox.Show($"Erreur lors de l'application des paramètres: {ex.Message}", "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // Réactiver les boutons
                ApplyButton.IsEnabled = true;
                OkButton.IsEnabled = true;
                vm.LoggingService?.LogInfo($"[{operationId}] ApplySettingsAsync: Boutons réactivés, opération terminée");
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour les liens hypertextes.
        /// </summary>
        private void Hyperlink_RequestNavigate(object sender, System.Windows.Navigation.RequestNavigateEventArgs e)
        {
            try
            {
                // Ouvrir l'URL dans le navigateur par défaut
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = e.Uri.AbsoluteUri,
                    UseShellExecute = true
                });
                
                e.Handled = true;
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"Erreur lors de l'ouverture du lien: {ex.Message}", 
                                "Erreur", 
                                MessageBoxButton.OK, 
                                MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// Pré-initialise la fenêtre avec les dimensions et la position sauvegardées.
        /// Cette méthode est appelée avant l'affichage de la fenêtre pour éviter les problèmes de positionnement.
        /// </summary>
        public void PreInitialize()
        {
            if (DataContext is AppSettingsViewModel viewModel)
            {
                var operationId = Guid.NewGuid().ToString().Substring(0, 8);
                viewModel.LoggingService?.LogInfo($"[{operationId}] PreInitialize: Début de la pré-initialisation");
                
                try
                {
                    // 1. Lecture des dimensions sauvegardées
                    var savedWidth = viewModel.SettingsManager.SettingsWindowWidth;
                    var savedHeight = viewModel.SettingsManager.SettingsWindowHeight;
                    var savedTop = viewModel.SettingsManager.SettingsWindowTop;
                    var savedLeft = viewModel.SettingsManager.SettingsWindowLeft;
                    
                    viewModel.LoggingService?.LogInfo($"[{operationId}] PreInitialize: Valeurs lues - W: {savedWidth}, H: {savedHeight}, T: {savedTop}, L: {savedLeft}");
                    
                    // 2. Application des dimensions si valides
                    if (savedWidth > 0 && savedHeight > 0)
                    {
                        Width = savedWidth;
                        Height = savedHeight;
                        viewModel.LoggingService?.LogInfo($"[{operationId}] PreInitialize: Dimensions appliquées: W={Width}, H={Height}");
                    }
                    else
                    {
                        viewModel.LoggingService?.LogInfo($"[{operationId}] PreInitialize: Dimensions par défaut conservées: W={Width}, H={Height}");
                    }
                    
                    // 3. Définir le comportement de positionnement
                    // Par défaut, on centre la fenêtre
                    WindowStartupLocation = WindowStartupLocation.CenterScreen;
                    
                    // Si des coordonnées valides sont sauvegardées, vérifier si elles sont visibles
                    if (savedTop > 0 && savedLeft > 0)
                    {
                        bool isVisibleOnAnyScreen = System.Windows.Forms.Screen.AllScreens.Any(screen => 
                            screen.WorkingArea.IntersectsWith(new System.Drawing.Rectangle(
                                (int)savedLeft, (int)savedTop, (int)Width, (int)Height)));
                                
                        if (isVisibleOnAnyScreen)
                        {
                            WindowStartupLocation = WindowStartupLocation.Manual;
                            Top = savedTop;
                            Left = savedLeft;
                            viewModel.LoggingService?.LogInfo($"[{operationId}] PreInitialize: Position manuelle appliquée: T={Top}, L={Left}");
                        }
                        else
                        {
                            viewModel.LoggingService?.LogInfo($"[{operationId}] PreInitialize: Position hors écran, centrage de la fenêtre");
                        }
                    }
                    else
                    {
                        viewModel.LoggingService?.LogInfo($"[{operationId}] PreInitialize: Aucune position valide, centrage de la fenêtre");
                    }
                    
                    viewModel.LoggingService?.LogInfo($"[{operationId}] PreInitialize: Pré-initialisation terminée avec succès");
                }
                catch (Exception ex)
                {
                    viewModel.LoggingService?.LogError($"[{operationId}] PreInitialize: Erreur lors de la pré-initialisation: {ex.Message}", ex);
                    // En cas d'erreur, on utilise les valeurs par défaut et on centre la fenêtre
                    WindowStartupLocation = WindowStartupLocation.CenterScreen;
                }
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour effacer le message de statut lorsque l'utilisateur clique sur l'interface.
        /// </summary>
        private void AppSettingsWindow_PreviewMouseDown(object sender, MouseButtonEventArgs e)
        {
            // Si nous avons un message de succès et que l'utilisateur clique, effacer le message
            if (DataContext is AppSettingsViewModel vm && 
                vm.StatusMessage == "Paramètres appliqués avec succès.")
            {
                vm.StatusMessage = string.Empty;
            }
        }
    }
} 