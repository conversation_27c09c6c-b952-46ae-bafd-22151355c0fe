using System;
using System.Globalization;
using System.Windows.Data;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.UI.Converters
{
    /// <summary>
    /// Convertisseur entre une chaîne de caractères et un objet KeyCombination.
    /// </summary>
    [ValueConversion(typeof(string), typeof(KeyCombination))]
    public class StringToKeyCombinationConverter : IValueConverter
    {
        /// <summary>
        /// Convertit une chaîne de caractères en KeyCombination.
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string shortcutText && !string.IsNullOrEmpty(shortcutText))
            {
                if (KeyCombination.TryParse(shortcutText, out KeyCombination result))
                {
                    return result;
                }
            }
            
            // Valeur par défaut
            return new KeyCombination();
        }

        /// <summary>
        /// Convertit un KeyCombination en chaîne de caractères.
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is KeyCombination shortcut)
            {
                return shortcut.ToString();
            }
            
            return string.Empty;
        }
    }
} 