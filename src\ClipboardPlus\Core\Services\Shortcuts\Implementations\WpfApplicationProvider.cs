using System.Windows;
using ClipboardPlus.Core.Services.Shortcuts.Interfaces;
using WpfApp = System.Windows.Application;

namespace ClipboardPlus.Core.Services.Shortcuts.Implementations
{
    /// <summary>
    /// Implémentation de production pour IWpfApplicationProvider.
    /// Utilise WpfApplication.Current pour accéder à l'application WPF.
    /// </summary>
    public class WpfApplicationProvider : IWpfApplicationProvider
    {
        /// <inheritdoc />
        public Window? GetMainWindow()
        {
            try
            {
                return WpfApp.Current?.MainWindow;
            }
            catch
            {
                return null;
            }
        }

        /// <inheritdoc />
        public bool IsMainWindowLoaded()
        {
            try
            {
                var mainWindow = GetMainWindow();
                return mainWindow != null && mainWindow.IsLoaded;
            }
            catch
            {
                return false;
            }
        }

        /// <inheritdoc />
        public bool IsWpfApplicationAvailable()
        {
            try
            {
                return WpfApp.Current != null;
            }
            catch
            {
                return false;
            }
        }
    }
}
