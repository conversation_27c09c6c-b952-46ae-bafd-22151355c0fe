using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.UI.ViewModels.Construction;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ClipboardPlus.Tests.Unit.Helpers
{
    /// <summary>
    /// Helper pour créer des instances de ClipboardHistoryViewModel dans les tests.
    /// Simplifie la création avec tous les mocks nécessaires.
    /// </summary>
    public static class ClipboardHistoryViewModelTestHelper
    {
        /// <summary>
        /// Crée une instance de ClipboardHistoryViewModel avec tous les mocks par défaut.
        /// </summary>
        /// <param name="mockHistoryManager">Mock du gestionnaire d'historique (optionnel)</param>
        /// <param name="mockClipboardInteractionService">Mock du service d'interaction presse-papiers (optionnel)</param>
        /// <param name="mockSettingsManager">Mock du gestionnaire de paramètres (optionnel)</param>
        /// <param name="mockUserNotificationService">Mock du service de notification (optionnel)</param>
        /// <param name="mockUserInteractionService">Mock du service d'interaction utilisateur (optionnel)</param>
        /// <param name="mockServiceProvider">Mock du fournisseur de services (optionnel)</param>
        /// <param name="mockRenameService">Mock du service de renommage (optionnel)</param>
        /// <returns>Instance configurée de ClipboardHistoryViewModel</returns>
        public static ClipboardHistoryViewModel CreateViewModel(
            Mock<IClipboardHistoryManager>? mockHistoryManager = null,
            Mock<IClipboardInteractionService>? mockClipboardInteractionService = null,
            Mock<ISettingsManager>? mockSettingsManager = null,
            Mock<IUserNotificationService>? mockUserNotificationService = null,
            Mock<ClipboardPlus.Core.Services.IUserInteractionService>? mockUserInteractionService = null,
            IServiceProvider? serviceProvider = null,
            Mock<IRenameService>? mockRenameService = null)
        {
            // Créer les mocks par défaut si non fournis
            mockHistoryManager ??= new Mock<IClipboardHistoryManager>();
            mockClipboardInteractionService ??= new Mock<IClipboardInteractionService>();
            mockSettingsManager ??= new Mock<ISettingsManager>();
            mockUserNotificationService ??= new Mock<IUserNotificationService>();
            mockUserInteractionService ??= new Mock<ClipboardPlus.Core.Services.IUserInteractionService>();
            serviceProvider ??= TestServiceProviderHelper.CreateMockServiceProvider();
            mockRenameService ??= new Mock<IRenameService>();

            // Configuration par défaut des mocks
            mockHistoryManager.Setup(x => x.HistoryItems).Returns(new List<ClipboardItem>());
            mockSettingsManager.Setup(x => x.HideTimestamp).Returns(false);
            mockSettingsManager.Setup(x => x.HideItemTitle).Returns(false);
            mockSettingsManager.Setup(x => x.MaxTextPreviewLength).Returns(100);

            // Configuration du mock RenameService pour simuler le comportement réel
            mockRenameService.Setup(x => x.RenameItemAsync(It.IsAny<ClipboardItem>(), It.IsAny<string>()))
                .Returns<ClipboardItem, string>(async (item, newName) =>
                {
                    // Simuler le comportement réel : modifier l'item et appeler UpdateItemAsync
                    var oldName = item.CustomName;
                    item.CustomName = newName;

                    // Simuler l'appel à UpdateItemAsync comme le fait le vrai RenameService
                    await mockHistoryManager.Object.UpdateItemAsync(item);

                    return new RenameResult
                    {
                        Success = true,
                        UpdatedItem = item,
                        OldName = oldName,
                        NewName = newName,
                        ErrorMessage = null
                    };
                });

            // Migration vers l'architecture SOLID - Utilisation de la Factory
            var viewModel = ClipboardHistoryViewModelFactory.CreateWithSOLIDArchitecture(
                mockHistoryManager.Object,
                mockClipboardInteractionService.Object,
                mockSettingsManager.Object,
                mockUserNotificationService.Object,
                mockUserInteractionService.Object,
                serviceProvider,
                mockRenameService.Object,
                Mock.Of<IDeletionResultLogger>()
            );

            // Initialiser l'orchestrateur depuis le ServiceProvider
            var orchestrator = serviceProvider.GetService(typeof(IHistoryChangeOrchestrator)) as IHistoryChangeOrchestrator;
            var featureFlagService = new Mock<IFeatureFlagService>();
            featureFlagService.Setup(x => x.IsFeatureEnabled(It.IsAny<string>())).Returns(true);

            if (orchestrator != null)
            {
                viewModel.InitializeHistoryChangeOrchestrator(orchestrator, featureFlagService.Object);
            }

            return viewModel;
        }

        /// <summary>
        /// Crée un ensemble complet de mocks pour ClipboardHistoryViewModel.
        /// </summary>
        /// <returns>Tuple contenant tous les mocks nécessaires</returns>
        public static (
            Mock<IClipboardHistoryManager> HistoryManager,
            Mock<IClipboardInteractionService> ClipboardInteractionService,
            Mock<ISettingsManager> SettingsManager,
            Mock<IUserNotificationService> UserNotificationService,
            Mock<ClipboardPlus.Core.Services.IUserInteractionService> UserInteractionService,
            IServiceProvider ServiceProvider,
            Mock<IRenameService> RenameService,
            ClipboardHistoryViewModel ViewModel
        ) CreateViewModelWithMocks()
        {
            var mockHistoryManager = new Mock<IClipboardHistoryManager>();
            var mockClipboardInteractionService = new Mock<IClipboardInteractionService>();
            var mockSettingsManager = new Mock<ISettingsManager>();
            var mockUserNotificationService = new Mock<IUserNotificationService>();
            var mockUserInteractionService = new Mock<ClipboardPlus.Core.Services.IUserInteractionService>();
            var serviceProvider = TestServiceProviderHelper.CreateMockServiceProvider();
            var mockRenameService = new Mock<IRenameService>();

            var viewModel = CreateViewModel(
                mockHistoryManager,
                mockClipboardInteractionService,
                mockSettingsManager,
                mockUserNotificationService,
                mockUserInteractionService,
                serviceProvider,
                mockRenameService
            );

            return (
                mockHistoryManager,
                mockClipboardInteractionService,
                mockSettingsManager,
                mockUserNotificationService,
                mockUserInteractionService,
                serviceProvider,
                mockRenameService,
                viewModel
            );
        }

        /// <summary>
        /// Crée un ViewModel avec des données de test pré-chargées.
        /// </summary>
        /// <returns>ViewModel avec des éléments de test</returns>
        public static ClipboardHistoryViewModel CreateViewModelWithSampleData()
        {
            return CreateViewModelWithSampleData(hideTimestamp: false, hideItemTitle: false);
        }

        /// <summary>
        /// Crée un ViewModel avec des données de test pré-chargées et des paramètres personnalisés.
        /// </summary>
        /// <param name="hideTimestamp">Si true, cache les timestamps</param>
        /// <param name="hideItemTitle">Si true, cache les titres des éléments</param>
        /// <returns>ViewModel avec des éléments de test</returns>
        public static ClipboardHistoryViewModel CreateViewModelWithSampleData(bool hideTimestamp, bool hideItemTitle)
        {
            var mockHistoryManager = new Mock<IClipboardHistoryManager>();
            var mockClipboardInteractionService = new Mock<IClipboardInteractionService>();
            var mockSettingsManager = new Mock<ISettingsManager>();
            var mockUserNotificationService = new Mock<IUserNotificationService>();
            var mockUserInteractionService = new Mock<ClipboardPlus.Core.Services.IUserInteractionService>();
            var serviceProvider = TestServiceProviderHelper.CreateMockServiceProvider();
            var mockRenameService = new Mock<IRenameService>();
            var mockVisibilityStateManager = new Mock<IVisibilityStateManager>();

            // Créer des éléments de test
            var sampleItems = CreateSampleClipboardItems();

            // Configuration des mocks avec données de test
            mockHistoryManager.Setup(x => x.HistoryItems).Returns(sampleItems);
            mockSettingsManager.Setup(x => x.HideTimestamp).Returns(hideTimestamp);
            mockSettingsManager.Setup(x => x.HideItemTitle).Returns(hideItemTitle);
            mockSettingsManager.Setup(x => x.MaxTextPreviewLength).Returns(100);

            // Configuration du mock VisibilityStateManager
            mockVisibilityStateManager.Setup(x => x.GlobalTitleVisibility).Returns(!hideItemTitle);
            mockVisibilityStateManager.Setup(x => x.GlobalTimestampVisibility).Returns(!hideTimestamp);
            mockVisibilityStateManager.Setup(x => x.ShouldShowTitle(It.IsAny<ClipboardItem>())).Returns(!hideItemTitle);
            mockVisibilityStateManager.Setup(x => x.ShouldShowTimestamp(It.IsAny<ClipboardItem>())).Returns(!hideTimestamp);

            // Configuration du mock RenameService
            mockRenameService.Setup(x => x.RenameItemAsync(It.IsAny<ClipboardItem>(), It.IsAny<string>()))
                .Returns<ClipboardItem, string>(async (item, newName) =>
                {
                    var oldName = item.CustomName;
                    item.CustomName = newName;
                    await mockHistoryManager.Object.UpdateItemAsync(item);

                    return new RenameResult
                    {
                        Success = true,
                        UpdatedItem = item,
                        OldName = oldName,
                        NewName = newName,
                        ErrorMessage = null
                    };
                });

            var viewModel = ClipboardHistoryViewModelFactory.CreateWithSOLIDArchitecture(
                mockHistoryManager.Object,
                mockClipboardInteractionService.Object,
                mockSettingsManager.Object,
                mockUserNotificationService.Object,
                mockUserInteractionService.Object,
                serviceProvider,
                mockRenameService.Object,
                Mock.Of<IDeletionResultLogger>(),
                null, // collectionHealthService
                mockVisibilityStateManager.Object, // visibilityStateManager
                null, // newItemCreationOrchestrator
                null  // testEnvironmentDetector
            );

            // Initialiser l'orchestrateur depuis le ServiceProvider
            var orchestrator = serviceProvider.GetService(typeof(IHistoryChangeOrchestrator)) as IHistoryChangeOrchestrator;
            var featureFlagService = new Mock<IFeatureFlagService>();
            featureFlagService.Setup(x => x.IsFeatureEnabled(It.IsAny<string>())).Returns(true);

            if (orchestrator != null)
            {
                viewModel.InitializeHistoryChangeOrchestrator(orchestrator, featureFlagService.Object);

                // Déclencher le chargement initial des données
                // Ceci va appeler l'orchestrateur qui va synchroniser les données du mock vers l'UI
                _ = Task.Run(async () => await viewModel.LoadHistoryAsync());
            }

            return viewModel;
        }

        /// <summary>
        /// Crée une liste d'éléments de test pour les tests.
        /// </summary>
        /// <returns>Liste d'éléments ClipboardItem de test</returns>
        public static List<ClipboardItem> CreateTestItems(int count = 5)
        {
            var items = new List<ClipboardItem>();
            for (int i = 0; i < count; i++)
            {
                items.Add(new ClipboardItem
                {
                    Id = i + 1,
                    DataType = ClipboardDataType.Text,
                    TextPreview = $"Test item {i + 1}",
                    RawData = System.Text.Encoding.UTF8.GetBytes($"Test item {i + 1}"),
                    Timestamp = DateTime.Now.AddMinutes(-i * 5),
                    CustomName = $"Test Item {i + 1}",
                    IsPinned = i == 0, // Premier item épinglé
                    OrderIndex = i
                });
            }
            return items;
        }

        /// <summary>
        /// Crée une liste d'éléments de test pour les tests (méthode legacy).
        /// </summary>
        /// <returns>Liste d'éléments ClipboardItem de test</returns>
        private static List<ClipboardItem> CreateSampleClipboardItems()
        {
            return new List<ClipboardItem>
            {
                new ClipboardItem
                {
                    Id = 1,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Premier élément de test",
                    RawData = System.Text.Encoding.UTF8.GetBytes("Premier élément de test"),
                    Timestamp = DateTime.Now.AddMinutes(-10),
                    CustomName = "Test Item 1"
                },
                new ClipboardItem
                {
                    Id = 2,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Deuxième élément",
                    RawData = System.Text.Encoding.UTF8.GetBytes("Deuxième élément"),
                    Timestamp = DateTime.Now.AddMinutes(-5),
                    CustomName = "Test Item 2"
                },
                new ClipboardItem
                {
                    Id = 3,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Troisième élément avec nom personnalisé",
                    RawData = System.Text.Encoding.UTF8.GetBytes("Troisième élément avec nom personnalisé"),
                    Timestamp = DateTime.Now.AddMinutes(-2),
                    CustomName = "Custom Name"
                }
            };
        }
    }
}
