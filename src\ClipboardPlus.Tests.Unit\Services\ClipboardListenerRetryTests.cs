using System;
using System.Threading;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Services;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services
{
    [TestFixture]
    public class ClipboardListenerRetryTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IDispatcherService> _mockDispatcherService = null!;
        private ClipboardListenerOptions _options = null!;
        private TestableClipboardListenerService _service = null!;

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockDispatcherService = new Mock<IDispatcherService>();
            
            // Configurer le mock du dispatcher pour simuler l'exécution sur le thread UI
            _mockDispatcherService.Setup(d => d.CheckAccess()).Returns(true);
            _mockDispatcherService.Setup(d => d.Invoke(It.IsAny<Action>()))
                .Callback<Action>(action => action());
            _mockDispatcherService.Setup(d => d.Invoke<bool>(It.IsAny<Func<bool>>()))
                .Returns<Func<bool>>(func => func());
            
            _options = new ClipboardListenerOptions
            {
                DebounceIntervalMs = 50,
                StartupRetryCount = 5,
                StartupRetryDelayMs = 100,
                MaxRetryDelayMs = 1000,
                UseExponentialBackoff = true,
                UseBlockingUIOperations = true,
                EnableHealthMonitoring = false // Désactivé pour les tests
            };
            
            _service = new TestableClipboardListenerService(_mockLoggingService.Object, _mockDispatcherService.Object, _options);
        }

        [TearDown]
        public void TearDown()
        {
            _service.Dispose();
        }

        [Test]
        public void CalculateRetryDelay_WithExponentialBackoff_ReturnsCorrectDelays()
        {
            // Arrange
            _options.UseExponentialBackoff = true;
            _options.StartupRetryDelayMs = 100;
            _options.MaxRetryDelayMs = 1000;

            // Act & Assert
            _service.SetStartupAttempts(1);
            var delay1 = _service.TestCalculateRetryDelay();
            Assert.That(delay1, Is.EqualTo(100)); // 100 * 2^(1-1) = 100

            _service.SetStartupAttempts(2);
            var delay2 = _service.TestCalculateRetryDelay();
            Assert.That(delay2, Is.EqualTo(200)); // 100 * 2^(2-1) = 200

            _service.SetStartupAttempts(3);
            var delay3 = _service.TestCalculateRetryDelay();
            Assert.That(delay3, Is.EqualTo(400)); // 100 * 2^(3-1) = 400

            _service.SetStartupAttempts(4);
            var delay4 = _service.TestCalculateRetryDelay();
            Assert.That(delay4, Is.EqualTo(800)); // 100 * 2^(4-1) = 800

            _service.SetStartupAttempts(5);
            var delay5 = _service.TestCalculateRetryDelay();
            Assert.That(delay5, Is.EqualTo(1000)); // 100 * 2^(5-1) = 1600, mais limité à 1000
        }

        [Test]
        public void CalculateRetryDelay_WithoutExponentialBackoff_ReturnsConstantDelay()
        {
            // Arrange
            _options.UseExponentialBackoff = false;
            _options.StartupRetryDelayMs = 200;

            // Act & Assert
            _service.SetStartupAttempts(1);
            var delay1 = _service.TestCalculateRetryDelay();
            Assert.That(delay1, Is.EqualTo(200));

            _service.SetStartupAttempts(3);
            var delay3 = _service.TestCalculateRetryDelay();
            Assert.That(delay3, Is.EqualTo(200));

            _service.SetStartupAttempts(5);
            var delay5 = _service.TestCalculateRetryDelay();
            Assert.That(delay5, Is.EqualTo(200));
        }

        [Test]
        public void StartListening_LogsRetryAttempts()
        {
            // Arrange
            _options.StartupRetryCount = 3;
            _options.StartupRetryDelayMs = 50;
            _options.UseExponentialBackoff = true;

            // Simuler un échec initial puis un succès
            _mockDispatcherService.Setup(d => d.Invoke(It.IsAny<Func<bool>>()))
                .Returns(false); // Premier appel échoue

            // Act
            var result = _service.StartListening();

            // Assert
            Assert.That(result, Is.False); // Échoue après toutes les tentatives

            // Vérifier que les logs de retry ont été appelés
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("TryStartListening: Tentative"))), Times.AtLeast(1));
        }

        [Test]
        public void StartListening_ExceedsMaxRetries_LogsAbandon()
        {
            // Arrange
            _options.StartupRetryCount = 2;

            // Simuler un échec constant
            _mockDispatcherService.Setup(d => d.Invoke(It.IsAny<Func<bool>>()))
                .Returns(false);

            // Act
            var result = _service.StartListening();

            // Assert
            Assert.That(result, Is.False);
        }
    }

    // Classe de test qui hérite de ClipboardListenerService pour faciliter les tests
    public class TestableClipboardListenerService : ClipboardListenerService
    {
        public TestableClipboardListenerService(
            ILoggingService loggingService,
            IDispatcherService dispatcherService,
            ClipboardListenerOptions options)
            : base(loggingService, dispatcherService, options)
        {
        }

        public void SetStartupAttempts(int attempts)
        {
            var field = typeof(ClipboardListenerService).GetField("_startupAttempts",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            field?.SetValue(this, attempts);
        }

        public int TestCalculateRetryDelay()
        {
            var method = typeof(ClipboardListenerService).GetMethod("CalculateRetryDelay",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return (int)(method?.Invoke(this, null) ?? 0);
        }
    }
}
