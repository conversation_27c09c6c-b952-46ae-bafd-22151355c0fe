using System;
using NUnit.Framework;
using ClipboardPlus.Core.Services.Logging;

namespace ClipboardPlus.Tests.Unit.Core.Services.Logging
{
    /// <summary>
    /// Tests pour la classe LogEntry.
    /// Valide le formatage et les propriétés de l'entrée de log.
    /// </summary>
    [TestFixture]
    public class LogEntryTests
    {
        [Test]
        public void ToFormattedLines_WithSingleLineMessage_ShouldFormatCorrectly()
        {
            // Arrange
            var entry = new LogEntry(
                Level: "INFO",
                Message: "Test message",
                Timestamp: new DateTime(2025, 7, 12, 14, 30, 45, 123),
                ThreadId: "16",
                Context: "Non-UI",
                CallerInfo: "TestMethod (TestFile.cs:42)"
            );

            // Act
            var result = entry.ToFormattedLines();

            // Assert
            Assert.That(result.Length, Is.EqualTo(1));
            Assert.That(result[0], Is.EqualTo("[2025-07-12 14:30:45.123] [INFO] [Non-UI] [Thread:16] [TestMethod (TestFile.cs:42)] Test message"));
        }

        [Test]
        public void ToFormattedLines_WithMultiLineMessage_ShouldIndentCorrectly()
        {
            // Arrange
            var entry = new LogEntry(
                Level: "ERROR",
                Message: "Line 1\r\nLine 2\nLine 3",
                Timestamp: new DateTime(2025, 7, 12, 14, 30, 45, 123),
                ThreadId: "16",
                Context: "Background",
                CallerInfo: "ErrorMethod (ErrorFile.cs:100)"
            );

            // Act
            var result = entry.ToFormattedLines();

            // Assert
            Assert.That(result.Length, Is.EqualTo(3));
            Assert.That(result[0], Is.EqualTo("[2025-07-12 14:30:45.123] [ERROR] [Background] [Thread:16] [ErrorMethod (ErrorFile.cs:100)] Line 1"));
            Assert.That(result[1], Is.EqualTo("    Line 2"));
            Assert.That(result[2], Is.EqualTo("    Line 3"));
        }

        [Test]
        public void RequiresImmediateFlush_WithCriticalLevels_ShouldReturnTrue()
        {
            // Arrange & Act & Assert
            Assert.That(new LogEntry("AVERTISSEMENT", "", DateTime.Now, "", "", "").RequiresImmediateFlush, Is.True);
            Assert.That(new LogEntry("ERREUR", "", DateTime.Now, "", "", "").RequiresImmediateFlush, Is.True);
            Assert.That(new LogEntry("CRITIQUE", "", DateTime.Now, "", "", "").RequiresImmediateFlush, Is.True);
            Assert.That(new LogEntry("SUPPRESSION", "", DateTime.Now, "", "", "").RequiresImmediateFlush, Is.True);
        }

        [Test]
        public void RequiresImmediateFlush_WithNormalLevels_ShouldReturnFalse()
        {
            // Arrange & Act & Assert
            Assert.That(new LogEntry("DEBUG", "", DateTime.Now, "", "", "").RequiresImmediateFlush, Is.False);
            Assert.That(new LogEntry("INFO", "", DateTime.Now, "", "", "").RequiresImmediateFlush, Is.False);
        }

        [Test]
        public void ForceConsoleOutput_WithForcedLevels_ShouldReturnTrue()
        {
            // Arrange & Act & Assert
            Assert.That(new LogEntry("ERREUR", "", DateTime.Now, "", "", "").ForceConsoleOutput, Is.True);
            Assert.That(new LogEntry("CRITIQUE", "", DateTime.Now, "", "", "").ForceConsoleOutput, Is.True);
            Assert.That(new LogEntry("SUPPRESSION", "", DateTime.Now, "", "", "").ForceConsoleOutput, Is.True);
        }

        [Test]
        public void GetConsoleColor_WithDifferentLevels_ShouldReturnCorrectColors()
        {
            // Arrange & Act & Assert
            Assert.That(new LogEntry("DEBUG", "", DateTime.Now, "", "", "").GetConsoleColor(), Is.EqualTo(ConsoleColor.Gray));
            Assert.That(new LogEntry("INFO", "", DateTime.Now, "", "", "").GetConsoleColor(), Is.EqualTo(ConsoleColor.White));
            Assert.That(new LogEntry("AVERTISSEMENT", "", DateTime.Now, "", "", "").GetConsoleColor(), Is.EqualTo(ConsoleColor.Yellow));
            Assert.That(new LogEntry("ERREUR", "", DateTime.Now, "", "", "").GetConsoleColor(), Is.EqualTo(ConsoleColor.Red));
            Assert.That(new LogEntry("CRITIQUE", "", DateTime.Now, "", "", "").GetConsoleColor(), Is.EqualTo(ConsoleColor.DarkRed));
            Assert.That(new LogEntry("SUPPRESSION", "", DateTime.Now, "", "", "").GetConsoleColor(), Is.EqualTo(ConsoleColor.Magenta));
        }

        [Test]
        public void LogEntry_IsImmutable_ShouldNotAllowModification()
        {
            // Arrange
            var entry = new LogEntry("INFO", "Test", DateTime.Now, "1", "UI", "Caller");

            // Act & Assert - Vérifier que c'est un record immutable
            Assert.That(entry.Level, Is.EqualTo("INFO"));
            Assert.That(entry.Message, Is.EqualTo("Test"));
            
            // Les records sont immutables par design, pas besoin de test de modification
            Assert.That(entry, Is.Not.Null);
        }
    }
}
