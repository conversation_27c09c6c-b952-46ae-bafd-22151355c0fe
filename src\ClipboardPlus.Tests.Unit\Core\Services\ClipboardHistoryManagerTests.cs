using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    [TestFixture]
    public class ClipboardHistoryManagerTests
    {
        private Mock<IPersistenceService> _mockPersistenceService = new();
        private Mock<ISettingsManager> _mockSettingsManager = new();
        private Mock<IClipboardInteractionService> _mockClipboardInteractionService = new();
        private ClipboardHistoryManager _historyManager = null!;
        private List<ClipboardItem> _testItems = new();

        [SetUp]
        public void Initialize()
        {
            _testItems = new List<ClipboardItem>
            {
                new()
                {
                    Id = 1,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Premier élément de test",
                    CustomName = "Test 1",
                    RawData = System.Text.Encoding.UTF8.GetBytes("Contenu du premier élément"),
                    Timestamp = DateTime.UtcNow.AddMinutes(-10),
                    OrderIndex = 0
                },
                new()
                {
                    Id = 2,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Deuxième élément de test",
                    RawData = System.Text.Encoding.UTF8.GetBytes("Contenu du deuxième élément"),
                    Timestamp = DateTime.UtcNow.AddMinutes(-5),
                    OrderIndex = 1
                },
                new()
                {
                    Id = 3,
                    DataType = ClipboardDataType.Image,
                    TextPreview = "Image de test",
                    CustomName = "Mon image",
                    IsPinned = true,
                    Timestamp = DateTime.UtcNow.AddMinutes(-15),
                    OrderIndex = 2
                }
            };

            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockPersistenceService.Setup(m => m.GetAllClipboardItemsAsync())
                .ReturnsAsync(_testItems);
            _mockPersistenceService.Setup(m => m.InsertClipboardItemAsync(It.IsAny<ClipboardItem>()))
                .ReturnsAsync((ClipboardItem item) => item.Id > 0 ? item.Id : 4);
            _mockPersistenceService.Setup(m => m.UpdateClipboardItemAsync(It.IsAny<ClipboardItem>()))
                .Returns(Task.CompletedTask);
            _mockPersistenceService.Setup(m => m.DeleteClipboardItemAsync(It.IsAny<long>()))
                .ReturnsAsync(true);
            _mockPersistenceService.Setup(m => m.ClearClipboardItemsAsync(It.IsAny<bool>()))
                .Callback<bool>(preservePinned => 
                {
                    if (preservePinned)
                    {
                        _testItems.RemoveAll(i => !i.IsPinned);
                    }
                    else
                    {
                        _testItems.Clear();
                    }
                })
                .Returns(Task.CompletedTask);

            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockSettingsManager.Setup(m => m.MaxHistoryItems).Returns(50);
            _mockSettingsManager.Setup(m => m.MaxStorableItemSizeBytes).Returns(10 * 1024 * 1024); // 10 MB

            _mockClipboardInteractionService = new Mock<IClipboardInteractionService>();

            _historyManager = new ClipboardHistoryManager(
                _mockPersistenceService.Object, 
                _mockSettingsManager.Object,
                _mockClipboardInteractionService.Object);
            _historyManager.HistoryItems.AddRange(_testItems);
        }

        [Test]
        public async Task AddItem_AddsItemToPersistenceService()
        {
            var newItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Nouvel élément",
                RawData = System.Text.Encoding.UTF8.GetBytes("Contenu du nouvel élément"),
                Timestamp = DateTime.UtcNow
            };

            long newId = await _historyManager.AddItemAsync(newItem);

            _mockPersistenceService.Verify(m => m.InsertClipboardItemAsync(It.IsAny<ClipboardItem>()), Times.Once);
            Assert.That(newId, Is.EqualTo(4));
        }

        [Test]
        public void AddItem_WhenItemExceedsMaxSize_ThrowsArgumentException()
        {
            byte[] largeData = new byte[11 * 1024 * 1024];
            var largeItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Image,
                TextPreview = "Image trop grande",
                RawData = largeData,
                Timestamp = DateTime.UtcNow
            };

            Assert.ThrowsAsync<ArgumentException>(async () => await _historyManager.AddItemAsync(largeItem));
        }

        [Test]
        public async Task UpdateItem_UpdatesItemInPersistenceService()
        {
            var itemToUpdate = new ClipboardItem
            {
                Id = 2,
                DataType = ClipboardDataType.Text,
                TextPreview = "Élément modifié",
                CustomName = "Nom modifié",
                IsPinned = true,
                Timestamp = DateTime.UtcNow
            };

            await _historyManager.UpdateItemAsync(itemToUpdate);

            _mockPersistenceService.Verify(m => m.UpdateClipboardItemAsync(It.Is<ClipboardItem>(i =>
                i.Id == 2 && i.CustomName == "Nom modifié" && i.IsPinned)), Times.Once);
        }



        [Test]
        public async Task ClearHistory_RemovesAllUnpinnedItems()
        {
            await _historyManager.ClearHistoryAsync(true);
            _mockPersistenceService.Verify(m => m.ClearClipboardItemsAsync(true), Times.Once);
        }

        [Test]
        public async Task ClearHistory_RemovesAllItems()
        {
            await _historyManager.ClearHistoryAsync(false);
            _mockPersistenceService.Verify(m => m.ClearClipboardItemsAsync(false), Times.Once);
        }

        [Test]
        public async Task PersistNewItemOrderAsync_UpdatesItemOrderIndices()
        {
            var items = new List<ClipboardItem>
            {
                new() { Id = 3, OrderIndex = 0 },
                new() { Id = 1, OrderIndex = 1 },
                new() { Id = 2, OrderIndex = 2 }
            };

            await _historyManager.PersistNewItemOrderAsync(items);

            _mockPersistenceService.Verify(m => m.UpdateClipboardItemAsync(It.IsAny<ClipboardItem>()), Times.Exactly(3));
        }

        [Test]
        public async Task FindDuplicateAsync_WithNullItem_ReturnsNull()
        {
            var result = await _historyManager.FindDuplicateAsync(null);
            Assert.That(result, Is.Null, "FindDuplicateAsync devrait retourner null pour un item null");
        }

        [Test]
        public async Task FindDuplicateAsync_WithNullRawData_ReturnsNull()
        {
            var itemWithNullData = new ClipboardItem { Id = 999, DataType = ClipboardDataType.Text, RawData = null };
            var result = await _historyManager.FindDuplicateAsync(itemWithNullData);
            Assert.That(result, Is.Null, "FindDuplicateAsync devrait retourner null pour un item avec RawData null");
        }

        [Test]
        public async Task FindDuplicateAsync_WithEmptyRawData_ReturnsNull()
        {
            var itemWithEmptyData = new ClipboardItem { Id = 999, DataType = ClipboardDataType.Text, RawData = Array.Empty<byte>() };
            var result = await _historyManager.FindDuplicateAsync(itemWithEmptyData);
            Assert.That(result, Is.Null, "FindDuplicateAsync devrait retourner null pour un item avec RawData vide");
        }

        [Test]
        public async Task FindDuplicateAsync_WithMatchingItem_ReturnsExistingItem()
        {
            var existingItem = _testItems.First();
            var newItem = new ClipboardItem
            {
                Id = 999,
                DataType = existingItem.DataType,
                RawData = existingItem.RawData
            };

            var result = await _historyManager.FindDuplicateAsync(newItem);
            
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(existingItem));
        }

        [Test]
        public async Task FindDuplicateAsync_WithDifferentDataType_ReturnsNull()
        {
            var existingItem = _testItems.First(i => i.DataType == ClipboardDataType.Text);
            var newItem = new ClipboardItem
            {
                Id = 999,
                DataType = ClipboardDataType.Image, // Different type
                RawData = existingItem.RawData
            };
            
            var result = await _historyManager.FindDuplicateAsync(newItem);
            
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task FindDuplicateAsync_WithDifferentRawData_ReturnsNull()
        {
            var existingItem = _testItems.First();
            var newItem = new ClipboardItem
            {
                Id = 999,
                DataType = existingItem.DataType,
                RawData = System.Text.Encoding.UTF8.GetBytes("Données complètement différentes")
            };

            var result = await _historyManager.FindDuplicateAsync(newItem);
            
            Assert.That(result, Is.Null);
        }

        [Test]
        public void AddItemAsync_WithNullItem_ThrowsArgumentNullException()
        {
            Assert.ThrowsAsync<ArgumentNullException>(async () => await _historyManager.AddItemAsync(null));
        }

        [Test]
        public void UpdateItemAsync_WithNullItem_ThrowsArgumentNullException()
        {
            Assert.ThrowsAsync<ArgumentNullException>(async () => await _historyManager.UpdateItemAsync(null));
        }
        
        [Test]
        public async Task AddItemAsync_WithDuplicateItem_UpdatesExistingItem()
        {
            var existingItem = _testItems.First();
            var duplicateItem = new ClipboardItem
            {
                Id = 999, // New potential ID
                DataType = existingItem.DataType,
                RawData = existingItem.RawData, // Same data
                Timestamp = DateTime.UtcNow // New timestamp
            };
            
            _mockPersistenceService.Setup(m => m.FindDuplicateAsync(It.IsAny<ClipboardItem>())).ReturnsAsync(existingItem);

            await _historyManager.AddItemAsync(duplicateItem);

            _mockPersistenceService.Verify(m => m.UpdateClipboardItemAsync(It.Is<ClipboardItem>(i => i.Id == existingItem.Id && i.Timestamp == duplicateItem.Timestamp)), Times.Once, "L'élément existant aurait dû être mis à jour.");
            _mockPersistenceService.Verify(m => m.InsertClipboardItemAsync(It.IsAny<ClipboardItem>()), Times.Never, "Un nouvel élément n'aurait pas dû être inséré.");
            Assert.That(_historyManager.HistoryItems.First().Id, Is.EqualTo(existingItem.Id), "L'élément existant aurait dû être déplacé au début de la liste.");
        }

        [Test]
        public async Task AddItemAsync_WithValidNewItem_InsertsItem()
        {
            var newItem = new ClipboardItem
            {
                Id = 5,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Nouveau contenu unique")
            };
            _mockPersistenceService.Setup(m => m.FindDuplicateAsync(newItem)).ReturnsAsync((ClipboardItem?)null);
            _mockPersistenceService.Setup(m => m.InsertClipboardItemAsync(newItem)).ReturnsAsync(newItem.Id);

            await _historyManager.AddItemAsync(newItem);
            
            _mockPersistenceService.Verify(m => m.InsertClipboardItemAsync(newItem), Times.Once);
            Assert.That(_historyManager.HistoryItems.First().Id, Is.EqualTo(newItem.Id));
        }

        [Test]
        public async Task ClearHistoryAsync_WithPreservePinned_RemovesOnlyUnpinnedItems()
        {
            // Vérifier combien d'éléments sont épinglés avant le test
            int pinnedCount = _testItems.Count(i => i.IsPinned);
            
            await _historyManager.ClearHistoryAsync(true);
            
            // Vérifier que seuls les éléments épinglés restent
            Assert.That(_historyManager.HistoryItems.Count, Is.EqualTo(pinnedCount));
            Assert.That(_historyManager.HistoryItems.All(i => i.IsPinned), Is.True);
            _mockPersistenceService.Verify(m => m.ClearClipboardItemsAsync(true), Times.Once);
        }

        [Test]
        public async Task ClearHistoryAsync_WithoutPreservePinned_RemovesAllItems()
        {
            await _historyManager.ClearHistoryAsync(false);
            Assert.That(_historyManager.HistoryItems.Count, Is.EqualTo(0));
            _mockPersistenceService.Verify(m => m.ClearClipboardItemsAsync(false), Times.Once);
        }
    }
}