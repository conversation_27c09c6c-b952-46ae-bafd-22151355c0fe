using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Interface pour la gestion des notifications d'événements de suppression
    /// Responsabilité : Déclencher les événements et notifications appropriés
    /// </summary>
    public interface IDeletionNotificationService
    {
        /// <summary>
        /// Déclenche les notifications appropriées basées sur le résultat global
        /// </summary>
        /// <param name="globalResult">Résultat global de l'opération de suppression</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Résultat des notifications</returns>
        Task<NotificationResult> NotifyAsync(GlobalDeletionResult globalResult, string operationId);

        /// <summary>
        /// Déclenche l'événement HistoryChanged si nécessaire
        /// </summary>
        /// <param name="shouldNotify">Indique si la notification doit être déclenchée</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>True si l'événement a été déclenché</returns>
        Task<bool> TriggerHistoryChangedEventAsync(bool shouldNotify, string operationId);

        /// <summary>
        /// Log les détails de l'opération de suppression
        /// </summary>
        /// <param name="globalResult">Résultat global de l'opération</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Task représentant l'opération de logging</returns>
        Task LogDeletionDetailsAsync(GlobalDeletionResult globalResult, string operationId);
    }

    /// <summary>
    /// Résultat des notifications d'une opération de suppression
    /// </summary>
    public class NotificationResult
    {
        /// <summary>
        /// Indique si les notifications ont été envoyées avec succès
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Indique si l'événement HistoryChanged a été déclenché
        /// </summary>
        public bool HistoryChangedTriggered { get; set; }

        /// <summary>
        /// Indique si les détails ont été loggés
        /// </summary>
        public bool DetailsLogged { get; set; }

        /// <summary>
        /// Messages d'information sur les notifications
        /// </summary>
        public string[] Messages { get; set; } = System.Array.Empty<string>();

        /// <summary>
        /// Erreurs rencontrées pendant les notifications
        /// </summary>
        public string[] Errors { get; set; } = System.Array.Empty<string>();

        /// <summary>
        /// Crée un résultat de notification réussie
        /// </summary>
        public static NotificationResult CreateSuccess(bool historyChangedTriggered, bool detailsLogged, string[]? messages = null) =>
            new()
            {
                Success = true,
                HistoryChangedTriggered = historyChangedTriggered,
                DetailsLogged = detailsLogged,
                Messages = messages ?? System.Array.Empty<string>()
            };

        /// <summary>
        /// Crée un résultat de notification échouée
        /// </summary>
        public static NotificationResult CreateFailure(string[]? errors = null, string[]? messages = null) =>
            new()
            {
                Success = false,
                HistoryChangedTriggered = false,
                DetailsLogged = false,
                Errors = errors ?? System.Array.Empty<string>(),
                Messages = messages ?? System.Array.Empty<string>()
            };
    }
}
