using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Implementations
{
    /// <summary>
    /// Wrapper pour HistoryManager qui utilise une collection existante au lieu de créer la sienne.
    /// Utilisé pour l'intégration avec l'orchestrateur SOLID dans ClipboardHistoryManager.
    /// </summary>
    public class HistoryManagerWrapper : IHistoryManager
    {
        private readonly List<ClipboardItem> _existingHistoryItems;
        private readonly IPersistenceService _persistenceService;
        private readonly ILoggingService? _loggingService;

        public HistoryManagerWrapper(
            List<ClipboardItem> existingHistoryItems,
            IPersistenceService persistenceService,
            ILoggingService? loggingService)
        {
            _existingHistoryItems = existingHistoryItems ?? throw new ArgumentNullException(nameof(existingHistoryItems));
            _persistenceService = persistenceService ?? throw new ArgumentNullException(nameof(persistenceService));
            _loggingService = loggingService;
        }

        /// <summary>
        /// Retourne la collection existante d'éléments d'historique.
        /// </summary>
        public IReadOnlyList<ClipboardItem> GetHistoryItems()
        {
            return _existingHistoryItems.AsReadOnly();
        }

        /// <summary>
        /// Ajoute un élément à la collection existante d'historique.
        /// </summary>
        public void AddToHistory(ClipboardItem item)
        {
            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] HistoryManagerWrapper.AddToHistory - Ajout élément ID={item.Id} en position 0");

            try
            {
                // Ajouter en première position (plus récent en premier)
                _existingHistoryItems.Insert(0, item);
                
                _loggingService?.LogInfo($"[{operationId}] HistoryManagerWrapper.AddToHistory - Élément ID={item.Id} ajouté avec succès. Total éléments: {_existingHistoryItems.Count}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] HistoryManagerWrapper.AddToHistory - Erreur lors de l'ajout: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Applique la limite maximale d'éléments d'historique.
        /// </summary>
        public async Task<int> EnforceMaxHistoryItemsAsync(int maxItems)
        {
            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] HistoryManagerWrapper.EnforceMaxHistoryItemsAsync - Limite: {maxItems}, Actuel: {_existingHistoryItems.Count}");

            try
            {
                if (_existingHistoryItems.Count <= maxItems)
                {
                    _loggingService?.LogInfo($"[{operationId}] HistoryManagerWrapper.EnforceMaxHistoryItemsAsync - Aucune suppression nécessaire");
                    return 0;
                }

                // Supprimer les éléments en excès (les plus anciens)
                var itemsToRemove = _existingHistoryItems.Count - maxItems;
                var removedItems = new List<ClipboardItem>();

                for (int i = 0; i < itemsToRemove; i++)
                {
                    var lastIndex = _existingHistoryItems.Count - 1;
                    var itemToRemove = _existingHistoryItems[lastIndex];
                    removedItems.Add(itemToRemove);
                    _existingHistoryItems.RemoveAt(lastIndex);
                }

                // Supprimer de la base de données
                foreach (var item in removedItems)
                {
                    try
                    {
                        await _persistenceService.DeleteClipboardItemAsync(item.Id);
                        _loggingService?.LogInfo($"[{operationId}] HistoryManagerWrapper.EnforceMaxHistoryItemsAsync - Élément ID={item.Id} supprimé de la BDD");
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogWarning($"[{operationId}] HistoryManagerWrapper.EnforceMaxHistoryItemsAsync - Erreur lors de la suppression de l'élément ID={item.Id}: {ex.Message}");
                    }
                }

                _loggingService?.LogInfo($"[{operationId}] HistoryManagerWrapper.EnforceMaxHistoryItemsAsync - {itemsToRemove} éléments supprimés. Nouveau total: {_existingHistoryItems.Count}");
                return itemsToRemove;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] HistoryManagerWrapper.EnforceMaxHistoryItemsAsync - Erreur inattendue: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Met à jour un élément existant dans la collection.
        /// </summary>
        public void UpdateExistingItem(ClipboardItem existingItem)
        {
            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] HistoryManagerWrapper.UpdateExistingItemAsync - Mise à jour élément ID={existingItem.Id}");

            try
            {
                // Trouver l'élément dans la collection
                var index = _existingHistoryItems.FindIndex(item => item.Id == existingItem.Id);
                if (index >= 0)
                {
                    // Déplacer l'élément en première position (plus récent)
                    _existingHistoryItems.RemoveAt(index);
                    _existingHistoryItems.Insert(0, existingItem);
                    
                    _loggingService?.LogInfo($"[{operationId}] HistoryManagerWrapper.UpdateExistingItemAsync - Élément ID={existingItem.Id} déplacé en première position");
                }
                else
                {
                    _loggingService?.LogWarning($"[{operationId}] HistoryManagerWrapper.UpdateExistingItemAsync - Élément ID={existingItem.Id} non trouvé dans la collection");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] HistoryManagerWrapper.UpdateExistingItemAsync - Erreur inattendue: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Déplace un élément en haut de la liste (première position).
        /// </summary>
        public void MoveToTop(ClipboardItem item)
        {
            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] HistoryManagerWrapper.MoveToTop - Déplacement élément ID={item.Id} en première position");

            try
            {
                // Trouver l'élément dans la collection
                var index = _existingHistoryItems.FindIndex(existingItem => existingItem.Id == item.Id);
                if (index >= 0)
                {
                    // Supprimer de sa position actuelle
                    _existingHistoryItems.RemoveAt(index);

                    // Ajouter en première position
                    _existingHistoryItems.Insert(0, item);

                    _loggingService?.LogInfo($"[{operationId}] HistoryManagerWrapper.MoveToTop - Élément ID={item.Id} déplacé de la position {index} vers la position 0");
                }
                else
                {
                    _loggingService?.LogWarning($"[{operationId}] HistoryManagerWrapper.MoveToTop - Élément ID={item.Id} non trouvé dans la collection");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] HistoryManagerWrapper.MoveToTop - Erreur inattendue: {ex.Message}", ex);
                throw;
            }
        }
    }
}
