using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour le service de persistance des données de l'application.
    /// </summary>
    public interface IPersistenceService
    {
        /// <summary>
        /// Initialise la base de données et vérifie son intégrité.
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// Récupère tous les éléments du presse-papiers.
        /// </summary>
        /// <returns>Liste des éléments du presse-papiers.</returns>
        Task<List<ClipboardItem>> GetAllClipboardItemsAsync();

        /// <summary>
        /// Insère un nouvel élément dans le presse-papiers.
        /// </summary>
        /// <param name="item">Élément à insérer.</param>
        /// <returns>ID de l'élément inséré.</returns>
        Task<long> InsertClipboardItemAsync(ClipboardItem item);

        /// <summary>
        /// Met à jour un élément existant du presse-papiers.
        /// </summary>
        /// <param name="item">Élément à mettre à jour.</param>
        Task UpdateClipboardItemAsync(ClipboardItem item);

        /// <summary>
        /// Supprime un élément du presse-papiers.
        /// </summary>
        /// <param name="id">ID de l'élément à supprimer.</param>
        /// <returns>True si la suppression a réussi, False sinon.</returns>
        Task<bool> DeleteClipboardItemAsync(long id);

        /// <summary>
        /// Supprime tous les éléments du presse-papiers, avec option de préserver les éléments épinglés.
        /// </summary>
        /// <param name="preservePinned">Si vrai, les éléments épinglés ne seront pas supprimés.</param>
        Task ClearClipboardItemsAsync(bool preservePinned = true);

        /// <summary>
        /// Récupère tous les paramètres de l'application.
        /// </summary>
        /// <returns>Dictionnaire de paires clé-valeur représentant les paramètres.</returns>
        Task<Dictionary<string, string>> GetApplicationSettingsAsync();

        /// <summary>
        /// Enregistre un paramètre spécifique de l'application.
        /// </summary>
        /// <param name="key">Clé du paramètre.</param>
        /// <param name="value">Valeur du paramètre.</param>
        Task SaveApplicationSettingAsync(string key, string value);
        
        /// <summary>
        /// Recherche un élément dupliqué dans la base de données.
        /// </summary>
        /// <param name="item">Élément à rechercher.</param>
        /// <returns>L'élément dupliqué s'il existe, sinon null.</returns>
        Task<ClipboardItem?> FindDuplicateAsync(ClipboardItem item);
    }
} 