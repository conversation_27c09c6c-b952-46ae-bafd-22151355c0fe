#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.

using System;
using System.Collections.Generic;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.WindowDeactivation;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.WindowDeactivation
{
    /// <summary>
    /// Tests unitaires pour WindowDeactivationLoggingService.
    /// </summary>
    [TestFixture]
    public class WindowDeactivationLoggingServiceTests
    {
        private Mock<ILoggingService>? _mockLoggingService;
        private WindowDeactivationLoggingService? _loggingService;
        private WindowDeactivationLoggingConfig? _config;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _config = new WindowDeactivationLoggingConfig
            {
                LoggingLevel = WindowDeactivationLoggingLevel.Normal,
                EnableEmojiLogging = true,
                EnablePerformanceLogging = true,
                EnableWindowDetailsLogging = true,
                IncludeOperationIds = true,
                LogPrefix = "[WindowDeactivation]"
            };
            _loggingService = new WindowDeactivationLoggingService(_mockLoggingService.Object, _config);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new WindowDeactivationLoggingService(null!, _config!));
        }

        [Test]
        public void Constructor_WithNullConfig_ShouldUseDefaultConfig()
        {
            // Act
            var service = new WindowDeactivationLoggingService(_mockLoggingService!.Object, null);

            // Assert - Vérifier que le service fonctionne avec la config par défaut
            Assert.DoesNotThrow(() => service.LogDeactivationStart(null, "test-op"));
        }

        [Test]
        public void LogDeactivationStart_WithMinimalLevel_ShouldNotLog()
        {
            // Arrange
            _config!.LoggingLevel = WindowDeactivationLoggingLevel.Minimal;
            _loggingService = new WindowDeactivationLoggingService(_mockLoggingService!.Object, _config);

            // Act
            _loggingService.LogDeactivationStart(null, "test-op");

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.IsAny<string>()), Times.Never);
        }

        [Test]
        public void LogDeactivationStart_WithNormalLevel_ShouldLog()
        {
            // Arrange
            var operationId = "test-op-123";

            // Act
            _loggingService!.LogDeactivationStart(null, operationId);

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s =>
                s.Contains("[DÉBUT]") &&
                s.Contains("Window_Deactivated") &&
                s.Contains(operationId))), Times.Once);
        }

        [Test]
        public void LogDeactivationStart_WithEmojiDisabled_ShouldNotIncludeEmoji()
        {
            // Arrange
            _config!.EnableEmojiLogging = false;
            _loggingService = new WindowDeactivationLoggingService(_mockLoggingService!.Object, _config);

            // Act
            _loggingService.LogDeactivationStart(null, "test-op");

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s =>
                !s.Contains("🚨"))), Times.Once);
        }

        [Test]
        public void LogDiagnosticResults_WithSuccessfulDiagnostic_ShouldLog()
        {
            // Arrange
            var diagnostic = new WindowDiagnosticResult
            {
                IsSuccessful = true,
                DiagnosticDurationMs = 15.5,
                AllWindows = new List<WindowInfo>
                {
                    new WindowInfo { WindowTypeName = "TestWindow1", Title = "Test 1", IsActive = true },
                    new WindowInfo { WindowTypeName = "TestWindow2", Title = "Test 2", IsActive = false },
                    new WindowInfo { WindowTypeName = "TestWindow3", Title = "Test 3", IsActive = false }
                }
            };

            // Act
            _loggingService!.LogDiagnosticResults(diagnostic, "test-op");

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s =>
                s.Contains("DIAGNOSTIC") &&
                s.Contains("fenêtres") &&
                s.Contains("15,5ms"))), Times.Once);
        }

        [Test]
        public void LogDiagnosticResults_WithDetailedLevel_ShouldLogWindowDetails()
        {
            // Arrange
            _config!.LoggingLevel = WindowDeactivationLoggingLevel.Detailed;
            _loggingService = new WindowDeactivationLoggingService(_mockLoggingService!.Object, _config);
            
            var diagnostic = new WindowDiagnosticResult
            {
                IsSuccessful = true,
                AllWindows = new List<WindowInfo>
                {
                    new WindowInfo { WindowTypeName = "TestWindow", Title = "Test", IsActive = true }
                }
            };

            // Act
            _loggingService.LogDiagnosticResults(diagnostic, "test-op");

            // Assert
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("TestWindow") && s.Contains("Test"))), Times.AtLeastOnce);
        }

        [Test]
        public void LogClassificationResults_WithMinimalLevel_ShouldNotLog()
        {
            // Arrange
            _config!.LoggingLevel = WindowDeactivationLoggingLevel.Minimal;
            _loggingService = new WindowDeactivationLoggingService(_mockLoggingService!.Object, _config);

            var classification = new WindowClassificationResult
            {
                WindowTypeFullName = "TestWindow"
            };

            // Act
            _loggingService.LogClassificationResults(classification, "test-op");

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.IsAny<string>()), Times.Never);
        }

        [Test]
        public void LogClassificationResults_WithNormalLevel_ShouldLog()
        {
            // Arrange
            var classification = new WindowClassificationResult
            {
                WindowTypeFullName = "TestWindow.FullName"
            };

            // Act
            _loggingService!.LogClassificationResults(classification, "test-op");

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("Analyse de la fenêtre active") && 
                s.Contains("TestWindow.FullName"))), Times.Once);
        }

        [Test]
        public void LogVisibilityDecision_WithHideDecision_ShouldLogWithHideEmoji()
        {
            // Arrange
            var decision = new WindowVisibilityDecision
            {
                ShouldHide = true,
                Reason = "Test hide reason"
            };

            // Act
            _loggingService!.LogVisibilityDecision(decision, "test-op");

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("🚨") && 
                s.Contains("MASQUAGE") &&
                s.Contains("Test hide reason"))), Times.Once);
        }

        [Test]
        public void LogVisibilityDecision_WithKeepDecision_ShouldLogWithKeepEmoji()
        {
            // Arrange
            var decision = new WindowVisibilityDecision
            {
                ShouldHide = false,
                Reason = "Test keep reason"
            };

            // Act
            _loggingService!.LogVisibilityDecision(decision, "test-op");

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("✅") && 
                s.Contains("conservée visible") &&
                s.Contains("Test keep reason"))), Times.Once);
        }

        [Test]
        public void LogDeactivationEnd_WithMinimalLevel_ShouldNotLog()
        {
            // Arrange
            _config!.LoggingLevel = WindowDeactivationLoggingLevel.Minimal;
            _loggingService = new WindowDeactivationLoggingService(_mockLoggingService!.Object, _config);

            // Act
            _loggingService.LogDeactivationEnd(null, "test-op");

            // Assert
            _mockLoggingService.Verify(x => x.LogInfo(It.IsAny<string>()), Times.Never);
        }

        [Test]
        public void LogDeactivationEnd_WithNormalLevel_ShouldLog()
        {
            // Arrange
            var operationId = "test-op-123";

            // Act
            _loggingService!.LogDeactivationEnd(null, operationId);

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s =>
                s.Contains("[FIN]") &&
                s.Contains("Window_Deactivated") &&
                s.Contains(operationId))), Times.Once);
        }

        [Test]
        public void LogPerformanceMetrics_WithPerformanceLoggingDisabled_ShouldNotLog()
        {
            // Arrange
            _config!.EnablePerformanceLogging = false;
            _loggingService = new WindowDeactivationLoggingService(_mockLoggingService!.Object, _config);
            
            var metrics = new WindowDeactivationMetrics
            {
                TotalDurationMs = 25.5
            };

            // Act
            _loggingService.LogPerformanceMetrics(metrics, "test-op");

            // Assert
            _mockLoggingService.Verify(x => x.LogInfo(It.IsAny<string>()), Times.Never);
        }

        [Test]
        public void LogPerformanceMetrics_WithPerformanceLoggingEnabled_ShouldLog()
        {
            // Arrange
            var metrics = new WindowDeactivationMetrics
            {
                TotalDurationMs = 25.5,
                DiagnosticDurationMs = 10.2,
                ClassificationDurationMs = 5.1,
                DecisionDurationMs = 3.8,
                WindowsAnalyzed = 4
            };

            // Act
            _loggingService!.LogPerformanceMetrics(metrics, "test-op");

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("MÉTRIQUES DE PERFORMANCE"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s =>
                s.Contains("25,5ms"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("4"))), Times.Once);
        }

        [Test]
        public void Service_ShouldRespectLoggingConfiguration()
        {
            // Arrange - Créer un service avec configuration minimale
            var minimalConfig = new WindowDeactivationLoggingConfig
            {
                LoggingLevel = WindowDeactivationLoggingLevel.Minimal,
                EnableEmojiLogging = false
            };
            var minimalService = new WindowDeactivationLoggingService(_mockLoggingService!.Object, minimalConfig);

            // Act
            minimalService.LogDeactivationStart(null, "test-op");

            // Assert - Avec Minimal level, aucun log ne devrait être émis
            _mockLoggingService!.Verify(x => x.LogInfo(It.IsAny<string>()), Times.Never);
        }

        [Test]
        public void LogValidationResult_WithIgnoreResult_ShouldLog()
        {
            // Arrange
            var validationResult = new WindowStateValidationResult
            {
                ShouldIgnore = true,
                Reason = "Test ignore reason"
            };

            // Act
            _loggingService!.LogValidationResult(validationResult, "test-op");

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("IGNORÉ") && 
                s.Contains("Test ignore reason"))), Times.Once);
        }

        [Test]
        public void LogValidationResult_WithContinueResult_ShouldLog()
        {
            // Arrange
            var validationResult = new WindowStateValidationResult
            {
                ShouldIgnore = false,
                Reason = "Test continue reason"
            };

            // Act
            _loggingService!.LogValidationResult(validationResult, "test-op");

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("CONTINUER") && 
                s.Contains("Test continue reason"))), Times.Once);
        }

        [Test]
        public void AllLogMethods_WithOperationIdNull_ShouldHandleGracefully()
        {
            // Arrange
            var diagnostic = new WindowDiagnosticResult { IsSuccessful = true };
            var classification = new WindowClassificationResult();
            var decision = new WindowVisibilityDecision();
            var validation = new WindowStateValidationResult();
            var metrics = new WindowDeactivationMetrics();

            // Act & Assert - Aucune exception ne devrait être levée
            Assert.DoesNotThrow(() => _loggingService!.LogDeactivationStart(null, null));
            Assert.DoesNotThrow(() => _loggingService!.LogDiagnosticResults(diagnostic, null));
            Assert.DoesNotThrow(() => _loggingService!.LogClassificationResults(classification, null));
            Assert.DoesNotThrow(() => _loggingService!.LogVisibilityDecision(decision, null));
            Assert.DoesNotThrow(() => _loggingService!.LogValidationResult(validation, null));
            Assert.DoesNotThrow(() => _loggingService!.LogPerformanceMetrics(metrics, null));
            Assert.DoesNotThrow(() => _loggingService!.LogDeactivationEnd(null, null));
        }
    }
}
