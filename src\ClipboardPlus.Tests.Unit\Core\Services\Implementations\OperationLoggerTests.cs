using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.Implementations;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.Implementations
{
    [TestFixture]
    public class OperationLoggerTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private OperationLogger _operationLogger;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _operationLogger = new OperationLogger(_mockLoggingService.Object);
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithValidParameters_CreatesInstance()
        {
            // Act & Assert
            Assert.That(_operationLogger, Is.Not.Null);
        }

        [Test]
        public void Constructor_WithNullLoggingService_CreatesInstance()
        {
            // Act & Assert
            var logger = new OperationLogger(null);
            Assert.That(logger, Is.Not.Null);
        }

        #endregion

        #region StartOperation Tests

        [Test]
        public void StartOperation_WithValidOperationName_ReturnsContext()
        {
            // Act
            var context = _operationLogger.StartOperation("TestOperation");

            // Assert
            Assert.That(context, Is.Not.Null);
            Assert.That(context.OperationId, Is.Not.Null.And.Not.Empty);
        }

        [Test]
        public void StartOperation_WithValidOperationNameAndItem_ReturnsContext()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            // Act
            var context = _operationLogger.StartOperation("TestOperation", item);

            // Assert
            Assert.That(context, Is.Not.Null);
            Assert.That(context.OperationId, Is.Not.Null.And.Not.Empty);
        }

        [Test]
        public void StartOperation_WithNullOperationName_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() =>
                _operationLogger.StartOperation(null!));

            Assert.That(ex.ParamName, Is.EqualTo("operationName"));
        }

        [Test]
        public void StartOperation_WithEmptyOperationName_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() =>
                _operationLogger.StartOperation(""));

            Assert.That(ex.ParamName, Is.EqualTo("operationName"));
        }

        [Test]
        public void StartOperation_WithWhitespaceOperationName_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() =>
                _operationLogger.StartOperation("   "));

            Assert.That(ex.ParamName, Is.EqualTo("operationName"));
        }

        [Test]
        public void StartOperation_LogsStartMessage()
        {
            // Act
            _operationLogger.StartOperation("TestOperation");

            // Assert
            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains("OperationLogger.StartOperation") && 
                    s.Contains("Début de l'opération 'TestOperation'"))),
                Times.Once);
        }

        [Test]
        public void StartOperation_WithItem_LogsStartMessageWithItemId()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 42,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            // Act
            _operationLogger.StartOperation("TestOperation", item);

            // Assert
            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains("OperationLogger.StartOperation") && 
                    s.Contains("Début de l'opération 'TestOperation'") &&
                    s.Contains("pour l'élément ID=42"))),
                Times.Once);
        }

        [Test]
        public void StartOperation_MultipleOperations_ReturnsDifferentContexts()
        {
            // Act
            var context1 = _operationLogger.StartOperation("Operation1");
            var context2 = _operationLogger.StartOperation("Operation2");

            // Assert
            Assert.That(context1.OperationId, Is.Not.EqualTo(context2.OperationId));
        }

        #endregion
    }

    [TestFixture]
    public class OperationContextTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private OperationLogger _operationLogger;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _operationLogger = new OperationLogger(_mockLoggingService.Object);
        }

        #region LogInfo Tests

        [Test]
        public void LogInfo_WithValidMessage_LogsCorrectly()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act
            context.LogInfo("Test message");

            // Assert
            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains($"[{context.OperationId}]") && 
                    s.Contains("TestOperation") &&
                    s.Contains("Test message"))),
                Times.Once);
        }

        [Test]
        public void LogInfo_WithNullMessage_ThrowsArgumentException()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() =>
                context.LogInfo(null!));

            Assert.That(ex.ParamName, Is.EqualTo("message"));
        }

        [Test]
        public void LogInfo_WithEmptyMessage_ThrowsArgumentException()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() =>
                context.LogInfo(""));

            Assert.That(ex.ParamName, Is.EqualTo("message"));
        }

        [Test]
        public void LogInfo_AfterDispose_ThrowsObjectDisposedException()
        {
            // Arrange
            var context = _operationLogger.StartOperation("TestOperation");
            context.Dispose();

            // Act & Assert
            Assert.Throws<ObjectDisposedException>(() =>
                context.LogInfo("Test message"));
        }

        #endregion

        #region LogWarning Tests

        [Test]
        public void LogWarning_WithValidMessage_LogsCorrectly()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act
            context.LogWarning("Warning message");

            // Assert
            _mockLoggingService.Verify(
                l => l.LogWarning(It.Is<string>(s => 
                    s.Contains($"[{context.OperationId}]") && 
                    s.Contains("TestOperation") &&
                    s.Contains("Warning message"))),
                Times.Once);
        }

        [Test]
        public void LogWarning_WithNullMessage_ThrowsArgumentException()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() =>
                context.LogWarning(null!));

            Assert.That(ex.ParamName, Is.EqualTo("message"));
        }

        #endregion

        #region LogError Tests

        [Test]
        public void LogError_WithValidMessage_LogsCorrectly()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act
            context.LogError("Error message");

            // Assert
            _mockLoggingService.Verify(
                l => l.LogError(
                    It.Is<string>(s =>
                        s.Contains($"[{context.OperationId}]") &&
                        s.Contains("TestOperation") &&
                        s.Contains("Error message")),
                    It.IsAny<Exception>()),
                Times.Once);
        }

        [Test]
        public void LogError_WithMessageAndException_LogsCorrectly()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");
            var exception = new Exception("Test exception");

            // Act
            context.LogError("Error message", exception);

            // Assert
            _mockLoggingService.Verify(
                l => l.LogError(
                    It.Is<string>(s =>
                        s.Contains($"[{context.OperationId}]") &&
                        s.Contains("TestOperation") &&
                        s.Contains("Error message")),
                    exception),
                Times.Once);
        }

        [Test]
        public void LogError_WithNullMessage_ThrowsArgumentException()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() =>
                context.LogError(null!));

            Assert.That(ex.ParamName, Is.EqualTo("message"));
        }

        #endregion

        #region Complete Tests

        [Test]
        public void Complete_WithoutResult_LogsCompletion()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act
            context.Complete();

            // Assert
            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains($"[{context.OperationId}]") && 
                    s.Contains("TestOperation") &&
                    s.Contains("Opération terminée avec succès") &&
                    s.Contains("ms"))),
                Times.Once);
        }

        [Test]
        public void Complete_WithResult_LogsCompletionWithResult()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act
            context.Complete("Success result");

            // Assert
            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains($"[{context.OperationId}]") && 
                    s.Contains("TestOperation") &&
                    s.Contains("Opération terminée avec succès") &&
                    s.Contains("avec résultat: Success result") &&
                    s.Contains("ms"))),
                Times.Once);
        }

        [Test]
        public void Complete_CalledTwice_LogsOnlyOnce()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act
            context.Complete();
            context.Complete(); // Deuxième appel

            // Assert
            _mockLoggingService.Verify(
                l => l.LogInfo(It.Is<string>(s => 
                    s.Contains("Opération terminée avec succès"))),
                Times.Once);
        }

        #endregion

        #region Fail Tests

        [Test]
        public void Fail_WithValidError_LogsFailure()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act
            context.Fail("Operation failed");

            // Assert
            _mockLoggingService.Verify(
                l => l.LogError(It.Is<string>(s =>
                    s.Contains($"[{context.OperationId}]") &&
                    s.Contains("TestOperation") &&
                    s.Contains("Opération échouée") &&
                    s.Contains("Operation failed") &&
                    s.Contains("ms")), It.IsAny<Exception>()),
                Times.Once);
        }

        [Test]
        public void Fail_WithNullError_ThrowsArgumentException()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() =>
                context.Fail(null!));

            Assert.That(ex.ParamName, Is.EqualTo("error"));
        }

        [Test]
        public void Fail_CalledTwice_LogsOnlyOnce()
        {
            // Arrange
            using var context = _operationLogger.StartOperation("TestOperation");

            // Act
            context.Fail("First error");
            context.Fail("Second error"); // Deuxième appel

            // Assert
            _mockLoggingService.Verify(
                l => l.LogError(It.Is<string>(s =>
                    s.Contains("Opération échouée")), It.IsAny<Exception>()),
                Times.Once);
        }

        #endregion

        #region Dispose Tests

        [Test]
        public void Dispose_WithoutCompletion_LogsWarning()
        {
            // Arrange
            var context = _operationLogger.StartOperation("TestOperation");

            // Act
            context.Dispose();

            // Assert
            _mockLoggingService.Verify(
                l => l.LogWarning(It.Is<string>(s => 
                    s.Contains($"[{context.OperationId}]") && 
                    s.Contains("TestOperation") &&
                    s.Contains("Opération non terminée explicitement") &&
                    s.Contains("ms"))),
                Times.Once);
        }

        [Test]
        public void Dispose_AfterCompletion_DoesNotLogWarning()
        {
            // Arrange
            var context = _operationLogger.StartOperation("TestOperation");
            context.Complete();

            // Act
            context.Dispose();

            // Assert
            _mockLoggingService.Verify(
                l => l.LogWarning(It.IsAny<string>()),
                Times.Never);
        }

        [Test]
        public void Dispose_CalledTwice_DoesNotThrow()
        {
            // Arrange
            var context = _operationLogger.StartOperation("TestOperation");

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                context.Dispose();
                context.Dispose(); // Deuxième appel
            });
        }

        #endregion

        #region Integration Tests

        [Test]
        public void UsingPattern_WithCompletion_WorksCorrectly()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                using var context = _operationLogger.StartOperation("TestOperation");
                context.LogInfo("Starting work");
                context.LogInfo("Work in progress");
                context.Complete("Work done");
            });
        }

        [Test]
        public void UsingPattern_WithFailure_WorksCorrectly()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                using var context = _operationLogger.StartOperation("TestOperation");
                context.LogInfo("Starting work");
                context.LogError("Something went wrong");
                context.Fail("Work failed");
            });
        }

        #endregion
    }
}
