using System;
using System.IO;

namespace ClipboardPlus.Core.Services.Logging
{
    /// <summary>
    /// Configuration par défaut pour le système de logging.
    /// Implémente ILoggingConfiguration avec des valeurs par défaut sensées.
    /// </summary>
    public class LoggingConfiguration : ILoggingConfiguration
    {
        /// <summary>
        /// Chemin du fichier de log principal.
        /// </summary>
        private string _logFilePath = GetDefaultLogFilePath();
        public string LogFilePath
        {
            get
            {
                // Note: On ne peut pas utiliser ILoggingService ici car cela créerait une dépendance circulaire
                System.Diagnostics.Debug.WriteLine("[VERIFICATION_TEST_2025] LoggingConfiguration.LogFilePath ACCÉDÉE");
                return _logFilePath;
            }
            set { _logFilePath = value; }
        }
        
        /// <summary>
        /// Indique si la sortie console est activée.
        /// </summary>
        private bool _consoleOutputEnabled = true;
        public bool ConsoleOutputEnabled
        {
            get
            {
                // Note: On ne peut pas utiliser ILoggingService ici car cela créerait une dépendance circulaire
                System.Diagnostics.Debug.WriteLine("[VERIFICATION_TEST_2025] LoggingConfiguration.ConsoleOutputEnabled ACCÉDÉE");
                return _consoleOutputEnabled;
            }
            set { _consoleOutputEnabled = value; }
        }
        
        /// <summary>
        /// Indique si la sortie debug est activée.
        /// </summary>
        public bool DebugOutputEnabled { get; set; } = true;
        
        /// <summary>
        /// Niveau minimum de log à enregistrer.
        /// </summary>
        public string MinimumLevel { get; set; } = "DEBUG";
        
        /// <summary>
        /// Taille maximale du buffer avant flush automatique.
        /// </summary>
        public int MaxBufferSize { get; set; } = 100;
        
        /// <summary>
        /// Intervalle de flush automatique.
        /// </summary>
        public TimeSpan FlushInterval { get; set; } = TimeSpan.FromSeconds(5);
        
        /// <summary>
        /// Taille maximale du fichier de log en MB.
        /// </summary>
        public int MaxLogSizeMB { get; set; } = 10;

        /// <summary>
        /// Constructeur par défaut.
        /// </summary>
        public LoggingConfiguration()
        {
            // Note: On ne peut pas utiliser ILoggingService ici car cela créerait une dépendance circulaire
            System.Diagnostics.Debug.WriteLine("[VERIFICATION_TEST_2025] LoggingConfiguration CONSTRUCTEUR APPELÉ");
        }

        /// <summary>
        /// Génère le chemin par défaut du fichier de log.
        /// </summary>
        private static string GetDefaultLogFilePath()
        {
            try
            {
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var logsDirectory = Path.Combine(baseDirectory, "logs");
                var fileName = $"clipboard_plus_{DateTime.Now:yyyyMMdd}.log";
                return Path.Combine(logsDirectory, fileName);
            }
            catch
            {
                // Fallback en cas d'erreur
                return Path.Combine(Path.GetTempPath(), "clipboard_plus.log");
            }
        }
    }
    
    /// <summary>
    /// Configuration optimisée pour l'environnement de développement.
    /// </summary>
    public class DevelopmentLoggingConfiguration : LoggingConfiguration
    {
        public DevelopmentLoggingConfiguration()
        {
            ConsoleOutputEnabled = true;
            DebugOutputEnabled = true;
            MinimumLevel = "DEBUG";
            MaxBufferSize = 50; // Buffer plus petit pour dev
            FlushInterval = TimeSpan.FromSeconds(2); // Flush plus fréquent
        }
    }
    
    /// <summary>
    /// Configuration optimisée pour l'environnement de production.
    /// </summary>
    public class ProductionLoggingConfiguration : LoggingConfiguration
    {
        public ProductionLoggingConfiguration()
        {
            ConsoleOutputEnabled = false;
            DebugOutputEnabled = false;
            MinimumLevel = "INFO";
            MaxBufferSize = 200; // Buffer plus grand pour prod
            FlushInterval = TimeSpan.FromSeconds(10); // Flush moins fréquent
        }
    }
}
