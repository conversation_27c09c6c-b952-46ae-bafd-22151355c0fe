using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.Views;
using ClipboardPlus.Utils;
using ClipboardPlus.UI.Controls;
using WpfMessageBox = System.Windows.MessageBox;

namespace ClipboardPlus.Core.Services.Windows
{
    /// <summary>
    /// Implémentation du service responsable de l'affichage et de la gestion de la fenêtre des paramètres de l'application.
    /// Cette classe encapsule la logique d'ouverture de la fenêtre des paramètres qui était précédemment
    /// dans SystemTrayService.OpenSettingsWindow().
    /// </summary>
    public class SettingsWindowService : ISettingsWindowService
    {
        private readonly ILoggingService? _loggingService;
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// Initialise une nouvelle instance de SettingsWindowService.
        /// </summary>
        /// <param name="loggingService">Service de logging pour tracer les opérations.</param>
        /// <param name="serviceProvider">Fournisseur de services pour résoudre les dépendances (comme AppSettingsWindow).</param>
        public SettingsWindowService(ILoggingService? loggingService, IServiceProvider serviceProvider)
        {
            _loggingService = loggingService;
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        /// <summary>
        /// Ouvre la fenêtre des paramètres de l'application.
        /// Cette méthode gère l'activation de la fenêtre existante ou la création d'une nouvelle instance.
        /// </summary>
        /// <returns>Une tâche représentant l'opération asynchrone d'ouverture de la fenêtre.</returns>
        public async Task OpenSettingsWindowAsync()
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            try
            {
                _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Début de l'ouverture des paramètres");
                
                // Vérifier si la fenêtre des paramètres existe déjà
                var existingWindow = System.Windows.Application.Current.Windows.OfType<AppSettingsWindow>().FirstOrDefault();
                
                if (existingWindow != null)
                {
                    // Activer la fenêtre existante
                    _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Activation de la fenêtre existante");
                    
                    // S'assurer que la fenêtre n'est pas minimisée
                    if (existingWindow.WindowState == WindowState.Minimized)
                    {
                        existingWindow.WindowState = WindowState.Normal;
                    }
                    
                    existingWindow.Activate();
                    existingWindow.Focus();
                    _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Fenêtre existante activée");
                }
                else
                {
                    // Créer et afficher la fenêtre des paramètres de manière asynchrone
                    _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Création d'une nouvelle fenêtre de paramètres");
                    
                    // Utiliser un SemaphoreSlim pour éviter les problèmes de synchronisation
                    using (var semaphore = new SemaphoreSlim(1, 1))
                    {
                        await semaphore.WaitAsync();
                        try
                        {
                            var settingsWindow = await AppSettingsWindow.CreateAsync(_serviceProvider);
                            
                            // Afficher la fenêtre en mode modal
                            _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Affichage de la fenêtre");
                            bool? result = settingsWindow.ShowDialog();
                            
                            // Actions après fermeture si nécessaire
                            if (result == true)
                            {
                                _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Paramètres appliqués avec succès");
                            }
                            else
                            {
                                _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Fermeture sans appliquer les paramètres");
                            }
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] SettingsWindowService: Erreur lors de l'ouverture des paramètres: {ex.Message}", ex);
                WpfMessageBox.Show($"Erreur lors de l'ouverture des paramètres: {ex.Message}",
                               "ClipboardPlus - Erreur",
                               MessageBoxButton.OK,
                               MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Ouvre la fenêtre des paramètres avec un identifiant d'opération pour le logging.
        /// Version améliorée qui retourne un résultat structuré.
        /// </summary>
        public async Task<SettingsWindowResult> OpenSettingsAsync(string operationId)
        {
            try
            {
                _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Début de l'ouverture des paramètres");

                // Vérifier si la fenêtre des paramètres existe déjà
                var existingWindow = System.Windows.Application.Current.Windows.OfType<AppSettingsWindow>().FirstOrDefault();

                if (existingWindow != null)
                {
                    // Activer la fenêtre existante
                    _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Activation de la fenêtre existante");

                    // S'assurer que la fenêtre n'est pas minimisée
                    if (existingWindow.WindowState == WindowState.Minimized)
                    {
                        existingWindow.WindowState = WindowState.Normal;
                    }

                    existingWindow.Activate();
                    existingWindow.Focus();
                    _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Fenêtre existante activée");

                    return SettingsWindowResult.CreateAlreadyOpen();
                }
                else
                {
                    // Créer et afficher la fenêtre des paramètres de manière asynchrone
                    _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Création d'une nouvelle fenêtre de paramètres");

                    // Utiliser un SemaphoreSlim pour éviter les problèmes de synchronisation
                    using (var semaphore = new SemaphoreSlim(1, 1))
                    {
                        await semaphore.WaitAsync();
                        try
                        {
                            var settingsWindow = await AppSettingsWindow.CreateAsync(_serviceProvider);

                            // Afficher la fenêtre en mode modal
                            _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: Affichage de la fenêtre");
                            bool? result = settingsWindow.ShowDialog();

                            // Traiter le résultat
                            var status = result switch
                            {
                                true => "Paramètres appliqués avec succès",
                                false => "Fermeture sans appliquer les paramètres",
                                null => "Fermeture inattendue"
                            };

                            _loggingService?.LogInfo($"[{operationId}] SettingsWindowService: {status}");
                            return SettingsWindowResult.CreateSuccess(status);
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Erreur lors de l'ouverture des paramètres: {ex.Message}";
                _loggingService?.LogError($"[{operationId}] SettingsWindowService: {errorMessage}", ex);
                return SettingsWindowResult.CreateFailure(errorMessage);
            }
        }
    }
}
