using System;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Tests.Unit.UI.ViewModels
{
    [TestFixture]
    public class ClipboardItemIdComparerFunctionalTests
    {
        private class TestClipboardItemIdComparer : IEqualityComparer<ClipboardItem>
        {
            public bool Equals(ClipboardItem? x, ClipboardItem? y)
            {
                if (ReferenceEquals(x, y)) return true;
                if (x is null || y is null) return false;
                return x.Id == y.Id;
            }

            public int GetHashCode(ClipboardItem obj)
            {
                if (obj is null) return 0;
                return obj.Id.GetHashCode();
            }
        }

        private IEqualityComparer<ClipboardItem> _comparer = null!;

        [SetUp]
        public void SetUp()
        {
            _comparer = new TestClipboardItemIdComparer();
        }

        [Test]
        public void Equals_WithSameId_ReturnsTrue()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1 };
            var item2 = new ClipboardItem { Id = 1 };

            // Act
            var result = _comparer.Equals(item1, item2);

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public void Equals_WithDifferentId_ReturnsFalse()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1 };
            var item2 = new ClipboardItem { Id = 2 };

            // Act
            var result = _comparer.Equals(item1, item2);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void Equals_WithOneNull_ReturnsFalse()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1 };

            // Act
            var result1 = _comparer.Equals(item1, null);
            var result2 = _comparer.Equals(null, item1);

            // Assert
            Assert.IsFalse(result1);
            Assert.IsFalse(result2);
        }

        [Test]
        public void Equals_WithBothNull_ReturnsTrue()
        {
            // Arrange & Act
            var result = _comparer.Equals(null, null);

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public void GetHashCode_WithSameId_ReturnsSameHashCode()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1 };
            var item2 = new ClipboardItem { Id = 1 };

            // Act
            var hashCode1 = _comparer.GetHashCode(item1);
            var hashCode2 = _comparer.GetHashCode(item2);

            // Assert
            Assert.AreEqual(hashCode1, hashCode2);
        }

        [Test]
        public void GetHashCode_WithDifferentId_ReturnsDifferentHashCode()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1 };
            var item2 = new ClipboardItem { Id = 2 };

            // Act
            var hashCode1 = _comparer.GetHashCode(item1);
            var hashCode2 = _comparer.GetHashCode(item2);

            // Assert
            Assert.AreNotEqual(hashCode1, hashCode2);
        }

        [Test]
        public void ClipboardItemIdComparer_WorksWithLinqSequenceEqual()
        {
            // Arrange - Créer des listes avec des items équivalents par ID (VRAI CODE EXÉCUTÉ)
            var list1 = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, TextPreview = "Item 1 version A" },
                new ClipboardItem { Id = 2, TextPreview = "Item 2 version A" },
                new ClipboardItem { Id = 3, TextPreview = "Item 3 version A" }
            };

            var list2 = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, TextPreview = "Item 1 version B" },
                new ClipboardItem { Id = 2, TextPreview = "Item 2 version B" },
                new ClipboardItem { Id = 3, TextPreview = "Item 3 version B" }
            };

            // Act - Exécuter le VRAI code avec LINQ SequenceEqual
            var areEqual = list1.SequenceEqual(list2, _comparer);

            // Assert
            Assert.IsTrue(areEqual, "Listes avec les mêmes IDs dans le même ordre devraient être égales");
        }

        [Test]
        public void ClipboardItemIdComparer_WorksWithLinqSequenceEqual_DifferentOrder()
        {
            // Arrange - Créer des listes avec les mêmes IDs mais dans un ordre différent (VRAI CODE EXÉCUTÉ)
            var list1 = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1 },
                new ClipboardItem { Id = 2 },
                new ClipboardItem { Id = 3 }
            };

            var list2 = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 3 },
                new ClipboardItem { Id = 1 },
                new ClipboardItem { Id = 2 }
            };

            // Act - Exécuter le VRAI code avec LINQ SequenceEqual
            var areEqual = list1.SequenceEqual(list2, _comparer);

            // Assert
            Assert.IsFalse(areEqual, "Listes avec les mêmes IDs mais dans un ordre différent ne devraient pas être égales");
        }

        [Test]
        public void ClipboardItemIdComparer_WorksWithLinqSequenceEqual_DifferentIds()
        {
            // Arrange - Créer des listes avec des IDs différents (VRAI CODE EXÉCUTÉ)
            var list1 = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1 },
                new ClipboardItem { Id = 2 }
            };

            var list2 = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1 },
                new ClipboardItem { Id = 3 }
            };

            // Act - Exécuter le VRAI code avec LINQ SequenceEqual
            var areEqual = list1.SequenceEqual(list2, _comparer);

            // Assert
            Assert.IsFalse(areEqual, "Listes avec des IDs différents ne devraient pas être égales");
        }

        [Test]
        public void ClipboardItemIdComparer_WorksWithHashSet()
        {
            // Arrange - Créer des items avec des IDs uniques et dupliqués (VRAI CODE EXÉCUTÉ)
            var items = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, TextPreview = "First" },
                new ClipboardItem { Id = 2, TextPreview = "Second" },
                new ClipboardItem { Id = 1, TextPreview = "Duplicate of first" }, // Même ID que le premier
                new ClipboardItem { Id = 3, TextPreview = "Third" }
            };

            // Act - Exécuter le VRAI code avec HashSet
            var uniqueItems = new HashSet<ClipboardItem>(items, _comparer);

            // Assert
            Assert.AreEqual(3, uniqueItems.Count, "HashSet devrait contenir seulement 3 items uniques par ID");
            
            var uniqueIds = uniqueItems.Select(item => item.Id).OrderBy(id => id).ToList();
            CollectionAssert.AreEqual(new List<long> { 1, 2, 3 }, uniqueIds, "Les IDs uniques devraient être 1, 2, 3");
        }

        [Test]
        public void ClipboardItemIdComparer_WorksWithDictionary()
        {
            // Arrange - Créer des items pour utiliser comme clés (VRAI CODE EXÉCUTÉ)
            var item1 = new ClipboardItem { Id = 10, TextPreview = "Key 1" };
            var item2 = new ClipboardItem { Id = 20, TextPreview = "Key 2" };
            var item1Duplicate = new ClipboardItem { Id = 10, TextPreview = "Duplicate key" };

            // Act - Exécuter le VRAI code avec Dictionary
            var dictionary = new Dictionary<ClipboardItem, string>(_comparer)
            {
                { item1, "Value 1" },
                { item2, "Value 2" }
            };

            // Test que le duplicate est reconnu comme la même clé
            var containsKey = dictionary.ContainsKey(item1Duplicate);
            var value = dictionary.TryGetValue(item1Duplicate, out var retrievedValue);

            // Assert
            Assert.IsTrue(containsKey, "Dictionary devrait reconnaître l'item duplicate comme une clé existante");
            Assert.IsTrue(value, "TryGetValue devrait réussir avec l'item duplicate");
            Assert.AreEqual("Value 1", retrievedValue, "La valeur récupérée devrait correspondre");
        }

        [Test]
        public void ClipboardItemIdComparer_HandlesEdgeCases()
        {
            // Arrange - Créer des items avec des IDs edge cases (VRAI CODE EXÉCUTÉ)
            var itemZero = new ClipboardItem { Id = 0 };
            var itemNegative = new ClipboardItem { Id = -1 };
            var itemMaxValue = new ClipboardItem { Id = long.MaxValue };
            var itemMinValue = new ClipboardItem { Id = long.MinValue };

            // Act & Assert - Tester les cas limites
            Assert.IsTrue(_comparer.Equals(itemZero, new ClipboardItem { Id = 0 }), "ID 0 devrait fonctionner");
            Assert.IsTrue(_comparer.Equals(itemNegative, new ClipboardItem { Id = -1 }), "ID négatif devrait fonctionner");
            Assert.IsTrue(_comparer.Equals(itemMaxValue, new ClipboardItem { Id = long.MaxValue }), "ID MaxValue devrait fonctionner");
            Assert.IsTrue(_comparer.Equals(itemMinValue, new ClipboardItem { Id = long.MinValue }), "ID MinValue devrait fonctionner");

            // Test hash codes pour les cas limites
            var hashZero = _comparer.GetHashCode(itemZero);
            var hashNegative = _comparer.GetHashCode(itemNegative);
            var hashMax = _comparer.GetHashCode(itemMaxValue);
            var hashMin = _comparer.GetHashCode(itemMinValue);

            // Note: Les hash codes peuvent être identiques pour certaines valeurs (0 et -1 par exemple)
            // Testons juste que les hash codes sont calculés sans erreur
            Assert.IsTrue(hashZero.GetType() == typeof(int), "Hash code devrait être un int");
            Assert.IsTrue(hashNegative.GetType() == typeof(int), "Hash code devrait être un int");
            Assert.IsTrue(hashMax.GetType() == typeof(int), "Hash code devrait être un int");
            Assert.IsTrue(hashMin.GetType() == typeof(int), "Hash code devrait être un int");
        }
    }
}
