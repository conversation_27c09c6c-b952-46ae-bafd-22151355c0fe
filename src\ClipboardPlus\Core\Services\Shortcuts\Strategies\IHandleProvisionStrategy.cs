using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services.Shortcuts.Models;

namespace ClipboardPlus.Core.Services.Shortcuts.Strategies
{
    /// <summary>
    /// Interface pour les stratégies de provision de handles de fenêtre.
    /// Permet différentes approches selon l'environnement (production, test, etc.).
    /// </summary>
    public interface IHandleProvisionStrategy
    {
        /// <summary>
        /// Le nom de la stratégie pour identification et logging.
        /// </summary>
        string StrategyName { get; }

        /// <summary>
        /// Indique si cette stratégie peut être utilisée dans l'environnement actuel.
        /// </summary>
        /// <returns>True si la stratégie est applicable, false sinon.</returns>
        bool CanProvideHandle();

        /// <summary>
        /// Fournit un handle de fenêtre pour l'enregistrement de raccourcis.
        /// </summary>
        /// <param name="context">Le contexte d'initialisation.</param>
        /// <returns>Le handle de fenêtre ou IntPtr.Zero en cas d'échec.</returns>
        Task<IntPtr> ProvideHandleAsync(InitializationContext context);

        /// <summary>
        /// Nettoie les ressources créées par cette stratégie.
        /// </summary>
        void Cleanup();
    }
}
