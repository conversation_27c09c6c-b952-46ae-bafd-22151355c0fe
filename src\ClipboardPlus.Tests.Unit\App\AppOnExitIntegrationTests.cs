using System;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;

namespace ClipboardPlus.Tests.Unit.App
{
    /// <summary>
    /// Tests d'intégration pour valider l'intégration réelle entre App.OnExit et ApplicationExitService
    /// Ces tests utilisent de vrais services pour valider l'intégration complète
    /// </summary>
    [TestFixture]
    public class AppOnExitIntegrationTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IApplicationLifetimeManager> _mockLifetimeManager = null!;
        private ServiceCollection _services = null!;
        private ServiceProvider _serviceProvider = null!;
        private OnExitIntegrationTester _integrationTester = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockLifetimeManager = new Mock<IApplicationLifetimeManager>();
            
            // Créer un vrai conteneur DI avec le vrai ApplicationExitService
            _services = new ServiceCollection();
            _services.AddSingleton(_mockLoggingService.Object);
            _services.AddSingleton(_mockSettingsManager.Object);
            _services.AddSingleton(_mockLifetimeManager.Object);
            _services.AddSingleton<IApplicationExitService, ApplicationExitService>();
            
            _serviceProvider = _services.BuildServiceProvider();
            
            _integrationTester = new OnExitIntegrationTester();
            _integrationTester.SetServices(_serviceProvider);
        }

        [TearDown]
        public void TearDown()
        {
            _serviceProvider?.Dispose();
        }

        private static ExitEventArgs CreateExitEventArgs(int exitCode = 0)
        {
            // ExitEventArgs n'a pas de constructeur public, utilisons la réflexion
            var constructor = typeof(ExitEventArgs).GetConstructor(
                BindingFlags.NonPublic | BindingFlags.Instance, 
                null, 
                new[] { typeof(int) }, 
                null);
            
            if (constructor != null)
            {
                return (ExitEventArgs)constructor.Invoke(new object[] { exitCode });
            }
            
            // Fallback : utiliser Activator.CreateInstance
            return (ExitEventArgs)Activator.CreateInstance(typeof(ExitEventArgs), 
                BindingFlags.NonPublic | BindingFlags.Instance, 
                null, 
                new object[] { exitCode }, 
                null)!;
        }

        [Test]
        public void OnExit_WithRealApplicationExitService_ShouldExecuteCompleteSequence()
        {
            // Arrange
            _mockSettingsManager.Setup(sm => sm.SettingsWindowWidth).Returns(800.0);
            _mockSettingsManager.Setup(sm => sm.SettingsWindowHeight).Returns(600.0);
            _mockSettingsManager.Setup(sm => sm.SettingsWindowTop).Returns(100.0);
            _mockSettingsManager.Setup(sm => sm.SettingsWindowLeft).Returns(200.0);
            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .Returns(Task.CompletedTask);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _integrationTester.ExecuteOnExitWithRealService(exitEventArgs);

            // Assert - Vérifier que App.OnExit a appelé ApplicationExitService qui a appelé les services sous-jacents

            // App.OnExit doit logger avec operationId
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(msg => msg.Contains("] OnExit: Début de la fermeture de l'application"))), Times.Once);

            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(msg => msg.Contains("] OnExit: Séquence de fermeture terminée avec succès"))), Times.Once);

            // ApplicationExitService doit logger sans operationId
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Début de la fermeture de l'application"), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("[APP_EXIT] Sauvegarde finale des paramètres..."), Times.Once);
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Fermeture de l'application terminée"), Times.Once);

            // Vérifier les appels aux services sous-jacents
            _mockSettingsManager.Verify(sm => sm.SaveSettingsToPersistenceAsync(), Times.Once);
            _mockLifetimeManager.Verify(lm => lm.Shutdown(_serviceProvider, null, null, false), Times.Once);
            _mockLoggingService.Verify(ls => ls.ForceFlush(), Times.AtLeastOnce);

            // Vérifier que les appels WPF sont effectués
            Assert.IsTrue(_integrationTester.BaseOnExitCalled);
            Assert.IsTrue(_integrationTester.EnvironmentExitCalled);
        }

        [Test]
        public void OnExit_WithRealServiceAndException_ShouldHandleErrorsCorrectly()
        {
            // Arrange
            var testException = new InvalidOperationException("Test settings save error");
            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .ThrowsAsync(testException);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _integrationTester.ExecuteOnExitWithRealService(exitEventArgs);

            // Assert - Vérifier que les erreurs sont gérées à tous les niveaux

            // App.OnExit doit logger le succès car ApplicationExitService gère l'exception en interne
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(msg => msg.Contains("] OnExit: Séquence de fermeture terminée avec succès"))), Times.Once);

            // ApplicationExitService doit logger l'exception spécifique
            _mockLoggingService.Verify(ls => ls.LogCritical(
                "OnExit: EXCEPTION CRITIQUE lors de la sauvegarde des paramètres.", testException), Times.Once);

            // L'exécution doit continuer malgré l'exception
            _mockLifetimeManager.Verify(lm => lm.Shutdown(_serviceProvider, null, null, false), Times.Once);
            Assert.IsTrue(_integrationTester.BaseOnExitCalled);
            Assert.IsTrue(_integrationTester.EnvironmentExitCalled);
        }

        [Test]
        public void OnExit_WithRealServiceAndLifetimeManagerException_ShouldHandleGlobalError()
        {
            // Arrange
            var testException = new InvalidOperationException("Test lifetime manager error");
            _mockLifetimeManager.Setup(lm => lm.Shutdown(It.IsAny<IServiceProvider>(), null, null, false))
                .Throws(testException);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _integrationTester.ExecuteOnExitWithRealService(exitEventArgs);

            // Assert - Vérifier que l'erreur globale est gérée
            
            // ApplicationExitService doit logger l'exception globale
            _mockLoggingService.Verify(ls => ls.LogCritical(
                $"OnExit: Exception non gérée lors de la fermeture: {testException.Message}", testException), Times.Once);

            // App.OnExit doit logger le succès car il ne voit pas l'exception interne
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(msg => msg.Contains("] OnExit: Séquence de fermeture terminée avec succès"))), Times.Once);

            // L'exécution doit continuer malgré l'exception
            Assert.IsTrue(_integrationTester.BaseOnExitCalled);
            Assert.IsTrue(_integrationTester.EnvironmentExitCalled);
        }

        [Test]
        public void OnExit_WithRealService_ShouldUseCorrectServiceResolution()
        {
            // Arrange
            var exitEventArgs = CreateExitEventArgs();

            // Act
            _integrationTester.ExecuteOnExitWithRealService(exitEventArgs);

            // Assert - Vérifier que les services sont résolus correctement
            
            // Le vrai ApplicationExitService doit résoudre les services via DI
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(msg => msg.Contains("OnExit: Début de la fermeture de l'application"))), Times.AtLeastOnce);

            // Vérifier que tous les services sont appelés dans le bon ordre
            var logCalls = new List<string>();
            _mockLoggingService.Setup(ls => ls.LogInfo(It.IsAny<string>()))
                .Callback<string>(msg => logCalls.Add(msg));

            // Réexécuter pour capturer l'ordre
            _integrationTester.ExecuteOnExitWithRealService(CreateExitEventArgs());

            // Vérifier l'ordre d'exécution
            Assert.IsTrue(logCalls.Any(msg => msg.Contains("OnExit: Début de la fermeture de l'application")));
            Assert.IsTrue(logCalls.Any(msg => msg.Contains("[APP_EXIT] Sauvegarde finale des paramètres...")));
            Assert.IsTrue(logCalls.Any(msg => msg.Contains("OnExit: Fermeture de l'application terminée")));
        }

        [Test]
        public void OnExit_WithRealService_ShouldPreserveAsyncBehavior()
        {
            // Arrange
            var taskCompletionSource = new TaskCompletionSource<bool>();
            _mockSettingsManager.Setup(sm => sm.SaveSettingsToPersistenceAsync())
                .Returns(taskCompletionSource.Task);

            var exitEventArgs = CreateExitEventArgs();

            // Act - Démarrer l'exécution dans un thread séparé pour tester l'asynchrone
            var executionTask = Task.Run(() => _integrationTester.ExecuteOnExitWithRealService(exitEventArgs));

            // Attendre un peu pour s'assurer que l'exécution a commencé
            Thread.Sleep(100);

            // Vérifier que l'exécution est bloquée en attendant la sauvegarde
            Assert.IsFalse(executionTask.IsCompleted);

            // Compléter la sauvegarde
            taskCompletionSource.SetResult(true);

            // Attendre la fin de l'exécution
            Assert.IsTrue(executionTask.Wait(5000)); // Timeout de 5 secondes

            // Assert
            _mockLoggingService.Verify(ls => ls.LogInfo("OnExit: Sauvegarde finale des paramètres terminée avec succès."), Times.Once);
            Assert.IsTrue(_integrationTester.BaseOnExitCalled);
            Assert.IsTrue(_integrationTester.EnvironmentExitCalled);
        }
    }

    /// <summary>
    /// Classe qui teste l'intégration réelle entre App.OnExit et ApplicationExitService
    /// </summary>
    public class OnExitIntegrationTester
    {
        public bool BaseOnExitCalled { get; private set; }
        public ExitEventArgs? BaseOnExitEventArgs { get; private set; }
        public bool EnvironmentExitCalled { get; private set; }
        public int EnvironmentExitCode { get; private set; }
        
        private IServiceProvider? _testServices;

        public void SetServices(IServiceProvider? services)
        {
            _testServices = services;
        }

        public void ExecuteOnExitWithRealService(ExitEventArgs e)
        {
            // Réinitialiser les flags
            BaseOnExitCalled = false;
            BaseOnExitEventArgs = null;
            EnvironmentExitCalled = false;
            EnvironmentExitCode = -1;

            // Exécuter la logique exacte de App.OnExit avec le vrai ApplicationExitService
            ExecuteOnExitWithRealServiceInternal(e);
        }

        private void ExecuteOnExitWithRealServiceInternal(ExitEventArgs e)
        {
            // COPIE EXACTE de la méthode App.OnExit refactorisée (lignes 115-142)
            // MAIS utilise le VRAI ApplicationExitService via DI
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            var loggingService = _testServices?.GetService<ILoggingService>();
            loggingService?.LogInfo($"[{operationId}] OnExit: Début de la fermeture de l'application");
            
            try
            {
                // Délégation vers le VRAI service spécialisé (pas un mock)
                var exitService = _testServices?.GetService<IApplicationExitService>();
                exitService?.ExecuteExitSequenceAsync(_testServices).GetAwaiter().GetResult();
                
                loggingService?.LogInfo($"[{operationId}] OnExit: Séquence de fermeture terminée avec succès");
            }
            catch (Exception ex)
            {
                loggingService?.LogCritical($"[{operationId}] OnExit: Erreur critique lors de la fermeture: {ex.Message}", ex);
            }
            finally
            {
                loggingService?.ForceFlush();
            }

            // Simuler l'appel à base.OnExit(e)
            BaseOnExitCalled = true;
            BaseOnExitEventArgs = e;
            
            // Simuler l'appel à Environment.Exit(0)
            EnvironmentExitCalled = true;
            EnvironmentExitCode = 0;
        }
    }
}
