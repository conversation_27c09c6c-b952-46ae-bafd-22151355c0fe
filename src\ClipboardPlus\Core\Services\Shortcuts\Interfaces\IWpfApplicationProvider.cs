using System.Windows;

namespace ClipboardPlus.Core.Services.Shortcuts.Interfaces
{
    /// <summary>
    /// Interface pour abstraire l'accès à WpfApplication.Current.
    /// Permet l'injection de dépendance et la testabilité.
    /// </summary>
    public interface IWpfApplicationProvider
    {
        /// <summary>
        /// Obtient la fenêtre principale de l'application WPF.
        /// </summary>
        /// <returns>La fenêtre principale ou null si non disponible.</returns>
        Window? GetMainWindow();

        /// <summary>
        /// Indique si la fenêtre principale est chargée et prête.
        /// </summary>
        /// <returns>True si la fenêtre principale est chargée, false sinon.</returns>
        bool IsMainWindowLoaded();

        /// <summary>
        /// Indique si l'application WPF est disponible.
        /// </summary>
        /// <returns>True si WpfApplication.Current est disponible, false sinon.</returns>
        bool IsWpfApplicationAvailable();
    }
}
