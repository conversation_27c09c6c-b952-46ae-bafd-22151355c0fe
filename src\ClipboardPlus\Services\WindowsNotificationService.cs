using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using ClipboardPlus.Core.Services;
using WpfApplication = System.Windows.Application;
using WpfProgressBar = System.Windows.Controls.ProgressBar;
using WpfBrush = System.Windows.Media.Brush;
using WpfBrushes = System.Windows.Media.Brushes;
using WpfColors = System.Windows.Media.Colors;
using WpfColor = System.Windows.Media.Color;
using WpfHorizontalAlignment = System.Windows.HorizontalAlignment;
using WpfVerticalAlignment = System.Windows.VerticalAlignment;
using FormsScreen = System.Windows.Forms.Screen;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Service de gestion des notifications pour Windows.
    /// </summary>
    public class WindowsNotificationService : INotificationService, IUserNotificationService
    {
        private readonly ILoggingService _loggingService;
        private readonly IGlobalExceptionManager _exceptionManager;
        private readonly DispatcherTimer _notificationTimer;
        private readonly List<NotificationWindow> _activeNotifications;
        private readonly int _notificationDuration = 5000; // 5 secondes
        private readonly int _notificationSpacing = 10; // Espacement entre les notifications

        /// <summary>
        /// Initialise une nouvelle instance de la classe <see cref="WindowsNotificationService"/>.
        /// </summary>
        /// <param name="loggingService">Le service de journalisation.</param>
        /// <param name="exceptionManager">Le gestionnaire d'exceptions.</param>
        public WindowsNotificationService(ILoggingService loggingService, IGlobalExceptionManager exceptionManager)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _exceptionManager = exceptionManager ?? throw new ArgumentNullException(nameof(exceptionManager));
            _activeNotifications = new List<NotificationWindow>();
            _notificationTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100)
            };
            _notificationTimer.Tick += NotificationTimer_Tick;
            _notificationTimer.Start();

            _loggingService.LogInfo("WindowsNotificationService: Initialisation du service");
        }

        /// <summary>
        /// Affiche une notification d'information.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="onClick">Action à exécuter lorsque l'utilisateur clique sur la notification.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        public Task ShowInformationAsync(string title, string message, Action? onClick = null)
        {
            return ShowCustomAsync(title, message, NotificationIconType.Information, onClick);
        }

        /// <summary>
        /// Affiche une notification de succès.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="onClick">Action à exécuter lorsque l'utilisateur clique sur la notification.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        public Task ShowSuccessAsync(string title, string message, Action? onClick = null)
        {
            return ShowCustomAsync(title, message, NotificationIconType.Success, onClick);
        }

        /// <summary>
        /// Affiche une notification d'avertissement.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="onClick">Action à exécuter lorsque l'utilisateur clique sur la notification.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        public Task ShowWarningAsync(string title, string message, Action? onClick = null)
        {
            return ShowCustomAsync(title, message, NotificationIconType.Warning, onClick);
        }

        /// <summary>
        /// Affiche une notification d'erreur.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="onClick">Action à exécuter lorsque l'utilisateur clique sur la notification.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        public Task ShowErrorAsync(string title, string message, Action? onClick = null)
        {
            return ShowCustomAsync(title, message, NotificationIconType.Error, onClick);
        }

        /// <summary>
        /// Affiche un message d'erreur à l'utilisateur (implémentation synchrone pour IUserNotificationService).
        /// </summary>
        /// <param name="title">Le titre de la boîte de dialogue.</param>
        /// <param name="message">Le message d'erreur à afficher.</param>
        public void ShowError(string title, string message)
        {
            ShowErrorAsync(title, message).Wait(); // Bloque jusqu'à ce que la notification soit affichée
        }

        /// <summary>
        /// Affiche un message d'information à l'utilisateur (implémentation synchrone pour IUserNotificationService).
        /// </summary>
        /// <param name="title">Le titre de la boîte de dialogue.</param>
        /// <param name="message">Le message d'information à afficher.</param>
        public void ShowInformation(string title, string message)
        {
            ShowInformationAsync(title, message).Wait(); // Bloque jusqu'à ce que la notification soit affichée
        }

        /// <summary>
        /// Affiche une notification personnalisée.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="iconType">Le type d'icône à afficher.</param>
        /// <param name="onClick">Action à exécuter lorsque l'utilisateur clique sur la notification.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        public Task ShowCustomAsync(string title, string message, NotificationIconType iconType, Action? onClick = null)
        {
            _loggingService.LogInfo($"WindowsNotificationService: Affichage d'une notification {iconType} - {title}");

            try
            {
                // S'assurer que nous sommes sur le thread de l'interface utilisateur
                if (WpfApplication.Current.Dispatcher.CheckAccess())
                {
                    CreateNotification(title, message, iconType, onClick);
                    return Task.CompletedTask;
                }
                else
                {
                    return WpfApplication.Current.Dispatcher.InvokeAsync(() =>
                    {
                        CreateNotification(title, message, iconType, onClick);
                    }).Task;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"WindowsNotificationService: Erreur lors de l'affichage de la notification: {ex.Message}", ex);
                _exceptionManager.LogUnhandledException("NotificationService", ex, false);
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// Masque toutes les notifications actives.
        /// </summary>
        public void HideAllNotifications()
        {
            _loggingService.LogInfo("WindowsNotificationService: Masquage de toutes les notifications");

            try
            {
                // S'assurer que nous sommes sur le thread de l'interface utilisateur
                if (WpfApplication.Current.Dispatcher.CheckAccess())
                {
                    CloseAllNotifications();
                }
                else
                {
                    WpfApplication.Current.Dispatcher.Invoke(CloseAllNotifications);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"WindowsNotificationService: Erreur lors du masquage des notifications: {ex.Message}", ex);
                _exceptionManager.LogUnhandledException("NotificationService", ex, false);
            }
        }

        /// <summary>
        /// Crée et affiche une notification.
        /// </summary>
        private void CreateNotification(string title, string message, NotificationIconType iconType, Action? onClick)
        {
            var notification = new NotificationWindow(title, message, iconType, onClick, _notificationDuration);
            _activeNotifications.Add(notification);
            PositionNotifications();
            notification.Show();
            notification.Closed += Notification_Closed;
        }

        /// <summary>
        /// Ferme toutes les notifications actives.
        /// </summary>
        private void CloseAllNotifications()
        {
            foreach (var notification in _activeNotifications.ToArray())
            {
                notification.Close();
            }
            _activeNotifications.Clear();
        }

        /// <summary>
        /// Gère l'événement de fermeture d'une notification.
        /// </summary>
        private void Notification_Closed(object? sender, EventArgs e)
        {
            if (sender is NotificationWindow notification)
            {
                _activeNotifications.Remove(notification);
                PositionNotifications();
            }
        }

        /// <summary>
        /// Positionne les notifications à l'écran.
        /// </summary>
        private void PositionNotifications()
        {
            var workArea = FormsScreen.PrimaryScreen?.WorkingArea ?? new System.Drawing.Rectangle(0, 0, 800, 600);
            int x = workArea.Width - 320;
            int y = workArea.Height - 20;

            for (int i = _activeNotifications.Count - 1; i >= 0; i--)
            {
                var notification = _activeNotifications[i];
                y -= (int)notification.Height + _notificationSpacing;
                notification.Left = x;
                notification.Top = y;
            }
        }

        /// <summary>
        /// Gère l'événement Tick du timer pour gérer les notifications.
        /// </summary>
        private void NotificationTimer_Tick(object? sender, EventArgs e)
        {
            foreach (var notification in _activeNotifications.ToArray())
            {
                notification.UpdateRemainingTime();
            }
        }

        /// <summary>
        /// Fenêtre de notification personnalisée.
        /// </summary>
        private class NotificationWindow : Window
        {
            private readonly DateTime _creationTime;
            private readonly int _duration;
            private readonly Action? _onClick;
            private readonly WpfProgressBar _progressBar;
            private readonly DispatcherTimer _fadeOutTimer;

            /// <summary>
            /// Initialise une nouvelle instance de la classe <see cref="NotificationWindow"/>.
            /// </summary>
            public NotificationWindow(string title, string message, NotificationIconType iconType, Action? onClick, int duration)
            {
                _creationTime = DateTime.Now;
                _duration = duration;
                _onClick = onClick;
                _fadeOutTimer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(16) };
                _fadeOutTimer.Tick += FadeOutTimer_Tick;

                // Configuration de la fenêtre
                Width = 300;
                Height = 100;
                WindowStyle = WindowStyle.None;
                ResizeMode = ResizeMode.NoResize;
                ShowInTaskbar = false;
                Topmost = true;
                AllowsTransparency = true;
                Background = WpfBrushes.Transparent;

                // Créer le contenu de la notification
                var grid = new Grid
                {
                    Background = new SolidColorBrush(WpfColor.FromArgb(230, 40, 40, 40)),
                    Margin = new Thickness(0),
                };

                grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });
                grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
                grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(4, GridUnitType.Pixel) });

                // Titre
                var titleTextBlock = new TextBlock
                {
                    Text = title,
                    Foreground = WpfBrushes.White,
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(10, 5, 10, 5),
                    HorizontalAlignment = WpfHorizontalAlignment.Left,
                    VerticalAlignment = WpfVerticalAlignment.Center
                };
                Grid.SetRow(titleTextBlock, 0);
                grid.Children.Add(titleTextBlock);

                // Contenu
                var contentGrid = new Grid();
                contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(40) });
                contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                Grid.SetRow(contentGrid, 1);
                grid.Children.Add(contentGrid);

                // Icône
                var iconPath = GetIconPathForType(iconType);
                if (!string.IsNullOrEmpty(iconPath))
                {
                    var icon = new System.Windows.Controls.Image
                    {
                        Source = new BitmapImage(new Uri(iconPath, UriKind.Relative)),
                        Width = 24,
                        Height = 24,
                        HorizontalAlignment = WpfHorizontalAlignment.Center,
                        VerticalAlignment = WpfVerticalAlignment.Top,
                        Margin = new Thickness(8, 8, 8, 8)
                    };
                    Grid.SetColumn(icon, 0);
                    contentGrid.Children.Add(icon);
                }

                // Message
                var messageTextBlock = new TextBlock
                {
                    Text = message,
                    Foreground = WpfBrushes.White,
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(0, 8, 10, 8),
                    HorizontalAlignment = WpfHorizontalAlignment.Left,
                    VerticalAlignment = WpfVerticalAlignment.Top
                };
                Grid.SetColumn(messageTextBlock, 1);
                contentGrid.Children.Add(messageTextBlock);

                // Barre de progression
                _progressBar = new WpfProgressBar
                {
                    Minimum = 0,
                    Maximum = 100,
                    Value = 100,
                    Height = 4,
                    BorderThickness = new Thickness(0),
                    Background = WpfBrushes.Transparent,
                    Foreground = GetProgressBarBrushForType(iconType),
                    HorizontalAlignment = WpfHorizontalAlignment.Stretch,
                    VerticalAlignment = WpfVerticalAlignment.Bottom,
                    Margin = new Thickness(0)
                };
                Grid.SetRow(_progressBar, 2);
                grid.Children.Add(_progressBar);

                // Ajouter le contenu à la fenêtre
                Content = grid;

                // Gérer les événements
                MouseLeftButtonUp += NotificationWindow_MouseLeftButtonUp;
                MouseEnter += NotificationWindow_MouseEnter;
                MouseLeave += NotificationWindow_MouseLeave;

                // Appliquer des animations
                Opacity = 0;
                BeginAnimation(OpacityProperty, new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(200)));
            }

            /// <summary>
            /// Mise à jour du temps restant de la notification.
            /// </summary>
            public void UpdateRemainingTime()
            {
                TimeSpan elapsed = DateTime.Now - _creationTime;
                double percentRemaining = Math.Max(0, 100 - (elapsed.TotalMilliseconds / _duration) * 100);
                _progressBar.Value = percentRemaining;

                if (percentRemaining <= 0 && !_fadeOutTimer.IsEnabled)
                {
                    StartFadeOut();
                }
            }

            /// <summary>
            /// Démarre l'animation de disparition.
            /// </summary>
            private void StartFadeOut()
            {
                _fadeOutTimer.Start();
            }

            /// <summary>
            /// Gère l'événement Tick du timer de disparition.
            /// </summary>
            private void FadeOutTimer_Tick(object? sender, EventArgs e)
            {
                Opacity -= 0.05;
                if (Opacity <= 0)
                {
                    _fadeOutTimer.Stop();
                    Close();
                }
            }

            /// <summary>
            /// Gère l'événement MouseLeftButtonUp de la fenêtre.
            /// </summary>
            private void NotificationWindow_MouseLeftButtonUp(object sender, System.Windows.Input.MouseButtonEventArgs e)
            {
                try
                {
                    _onClick?.Invoke();
                }
                finally
                {
                    StartFadeOut();
                }
            }

            /// <summary>
            /// Gère l'événement MouseEnter de la fenêtre.
            /// </summary>
            private void NotificationWindow_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
            {
                _fadeOutTimer.Stop();
                BeginAnimation(OpacityProperty, new DoubleAnimation(Opacity, 1, TimeSpan.FromMilliseconds(100)));
            }

            /// <summary>
            /// Gère l'événement MouseLeave de la fenêtre.
            /// </summary>
            private void NotificationWindow_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
            {
                UpdateRemainingTime();
            }

            /// <summary>
            /// Obtient le chemin de l'icône en fonction du type de notification.
            /// </summary>
            private string GetIconPathForType(NotificationIconType iconType)
            {
                return iconType switch
                {
                    NotificationIconType.Information => "/ClipboardPlus;component/Resources/Icons/info.png",
                    NotificationIconType.Success => "/ClipboardPlus;component/Resources/Icons/success.png",
                    NotificationIconType.Warning => "/ClipboardPlus;component/Resources/Icons/warning.png",
                    NotificationIconType.Error => "/ClipboardPlus;component/Resources/Icons/error.png",
                    NotificationIconType.Question => "/ClipboardPlus;component/Resources/Icons/question.png",
                    _ => string.Empty
                };
            }

            /// <summary>
            /// Obtient la couleur de la barre de progression en fonction du type de notification.
            /// </summary>
            private WpfBrush GetProgressBarBrushForType(NotificationIconType iconType)
            {
                return iconType switch
                {
                    NotificationIconType.Information => new SolidColorBrush(WpfColors.DeepSkyBlue),
                    NotificationIconType.Success => new SolidColorBrush(WpfColors.LimeGreen),
                    NotificationIconType.Warning => new SolidColorBrush(WpfColors.Orange),
                    NotificationIconType.Error => new SolidColorBrush(WpfColors.Red),
                    NotificationIconType.Question => new SolidColorBrush(WpfColors.BlueViolet),
                    _ => new SolidColorBrush(WpfColors.Gray)
                };
            }
        }
    }
}