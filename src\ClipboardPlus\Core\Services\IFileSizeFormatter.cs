using System;
using System.Collections.Generic;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour le formatage des tailles de fichiers.
    /// Extrait la logique de formatage de FormatFileSize() pour respecter le SRP.
    /// Respecte le principe de responsabilité unique (SRP).
    /// </summary>
    public interface IFileSizeFormatter
    {
        /// <summary>
        /// Formate une taille en octets en chaîne lisible par l'utilisateur.
        /// </summary>
        /// <param name="bytes">La taille en octets</param>
        /// <returns>La taille formatée (ex: "1.5 KB", "2.3 MB")</returns>
        string FormatSize(long bytes);

        /// <summary>
        /// Formate une taille en octets avec une précision spécifiée.
        /// </summary>
        /// <param name="bytes">La taille en octets</param>
        /// <param name="decimalPlaces">Le nombre de décimales à afficher</param>
        /// <returns>La taille formatée avec la précision spécifiée</returns>
        string FormatSize(long bytes, int decimalPlaces);

        /// <summary>
        /// Obtient l'unité appropriée pour une taille donnée.
        /// </summary>
        /// <param name="bytes">La taille en octets</param>
        /// <returns>L'unité appropriée (B, KB, MB, GB, TB)</returns>
        string GetAppropriateUnit(long bytes);

        /// <summary>
        /// Convertit une taille en octets vers l'unité spécifiée.
        /// </summary>
        /// <param name="bytes">La taille en octets</param>
        /// <param name="unit">L'unité cible (B, KB, MB, GB, TB)</param>
        /// <returns>La valeur convertie dans l'unité spécifiée</returns>
        /// <exception cref="ArgumentException">Levée si l'unité n'est pas reconnue</exception>
        double ConvertToUnit(long bytes, string unit);

        /// <summary>
        /// Valide qu'une unité de taille est supportée.
        /// </summary>
        /// <param name="unit">L'unité à valider</param>
        /// <returns>True si l'unité est supportée, false sinon</returns>
        bool IsSupportedUnit(string unit);

        /// <summary>
        /// Obtient la liste des unités supportées.
        /// </summary>
        /// <returns>Énumération des unités supportées</returns>
        IEnumerable<string> GetSupportedUnits();
    }
}
