using System;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Core.Services.Deletion
{
    /// <summary>
    /// Tests unitaires pour DeletionStateManager basés sur le VRAI code source
    /// </summary>
    [TestFixture]
    public class DeletionStateManagerTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private DeletionStateManager _stateManager = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _stateManager = new DeletionStateManager(_mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new DeletionStateManager(null!));
        }

        [Test]
        public async Task DetermineGlobalResultAsync_WithBothSuccessful_ShouldReturnSuccess()
        {
            // Arrange - Utiliser les VRAIES méthodes statiques
            var testItem = new ClipboardItem { Id = 123, TextPreview = "Test" };
            var memoryResult = MemoryDeletionResult.CreateSuccess(true, testItem, "Supprimé de la mémoire");
            var databaseResult = DatabaseDeletionResult.CreateSuccess(1, false, TimeSpan.FromMilliseconds(100), new[] { "Succès BDD" });
            string operationId = "test-op";

            // Act
            var result = await _stateManager.DetermineGlobalResultAsync(memoryResult, databaseResult, operationId);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.MemoryResult, Is.EqualTo(memoryResult));
            Assert.That(result.DatabaseResult, Is.EqualTo(databaseResult));
            Assert.That(result.ShouldNotify, Is.True);
            Assert.That(result.Consistency.IsConsistent, Is.True);

            // Verify logging
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(operationId) && s.Contains("Analyse du résultat global"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("SUCCÈS"))), Times.Once);
        }

        [Test]
        public async Task DetermineGlobalResultAsync_WithMemorySuccessAndDatabaseFailure_ShouldReturnSuccess()
        {
            // Arrange
            var testItem = new ClipboardItem { Id = 456, TextPreview = "Test2" };
            var memoryResult = MemoryDeletionResult.CreateSuccess(true, testItem, "Supprimé de la mémoire");
            var databaseResult = DatabaseDeletionResult.CreateFailure(3, null, TimeSpan.FromMilliseconds(500), new[] { "Échec BDD" });
            string operationId = "test-op-2";

            // Act
            var result = await _stateManager.DetermineGlobalResultAsync(memoryResult, databaseResult, operationId);

            // Assert
            Assert.That(result.Success, Is.True); // Succès car élément existait en mémoire et a été supprimé
            Assert.That(result.ShouldNotify, Is.True);
            Assert.That(result.Consistency.IsConsistent, Is.False);
            Assert.That(result.Consistency.InconsistencyType, Is.EqualTo(InconsistencyType.MemorySuccessDatabaseFailure));

            // Verify logging
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("SUCCÈS"))), Times.Once);
        }

        [Test]
        public async Task DetermineGlobalResultAsync_WithMemoryFailureAndDatabaseSuccess_ShouldReturnSuccess()
        {
            // Arrange
            var memoryResult = MemoryDeletionResult.CreateFailure("Élément non trouvé en mémoire");
            var databaseResult = DatabaseDeletionResult.CreateSuccess(1, false, TimeSpan.FromMilliseconds(200), new[] { "Succès BDD" });
            string operationId = "test-op-3";

            // Act
            var result = await _stateManager.DetermineGlobalResultAsync(memoryResult, databaseResult, operationId);

            // Assert
            Assert.That(result.Success, Is.True); // Succès car élément n'existait pas en mémoire mais suppression BDD réussie
            Assert.That(result.ShouldNotify, Is.False); // Pas de notification car pas en mémoire
            Assert.That(result.Consistency.IsConsistent, Is.False);
            Assert.That(result.Consistency.InconsistencyType, Is.EqualTo(InconsistencyType.MemoryFailureDatabaseSuccess));

            // Verify logging
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("SUCCÈS"))), Times.Once);
        }

        [Test]
        public async Task DetermineGlobalResultAsync_WithBothFailureButNotInMemory_ShouldReturnSuccess()
        {
            // Arrange
            var memoryResult = MemoryDeletionResult.CreateFailure("Élément non trouvé en mémoire");
            var databaseResult = DatabaseDeletionResult.CreateFailure(3, null, TimeSpan.FromMilliseconds(300), new[] { "Élément non trouvé en BDD" });
            string operationId = "test-op-4";

            // Act
            var result = await _stateManager.DetermineGlobalResultAsync(memoryResult, databaseResult, operationId);

            // Assert
            Assert.That(result.Success, Is.True); // Succès car élément n'existait ni en mémoire ni en BDD
            Assert.That(result.ShouldNotify, Is.False);
            Assert.That(result.Consistency.IsConsistent, Is.True); // Cohérent car les deux ont échoué
            Assert.That(result.Consistency.InconsistencyType, Is.EqualTo(InconsistencyType.BothFailed));

            // Verify logging
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("SUCCÈS"))), Times.Once);
        }

        [Test]
        public void AnalyzeConsistency_WithBothSuccessful_ShouldReturnConsistent()
        {
            // Arrange
            var testItem = new ClipboardItem { Id = 123, TextPreview = "Test" };
            var memoryResult = MemoryDeletionResult.CreateSuccess(true, testItem);
            var databaseResult = DatabaseDeletionResult.CreateSuccess(1, false, TimeSpan.FromMilliseconds(100), new[] { "OK" });

            // Act
            var analysis = _stateManager.AnalyzeConsistency(memoryResult, databaseResult);

            // Assert
            Assert.That(analysis.IsConsistent, Is.True);
            Assert.That(analysis.InconsistencyType, Is.EqualTo(InconsistencyType.None));
            Assert.That(analysis.Severity, Is.EqualTo(SeverityLevel.None));
            Assert.That(analysis.Message, Is.EqualTo("Suppression réussie en mémoire et en BDD"));
        }

        [Test]
        public void AnalyzeConsistency_WithBothFailed_ShouldReturnConsistent()
        {
            // Arrange
            var memoryResult = MemoryDeletionResult.CreateFailure("Échec mémoire");
            var databaseResult = DatabaseDeletionResult.CreateFailure(3, null, TimeSpan.FromMilliseconds(300), new[] { "Échec" });

            // Act
            var analysis = _stateManager.AnalyzeConsistency(memoryResult, databaseResult);

            // Assert
            Assert.That(analysis.IsConsistent, Is.True);
            Assert.That(analysis.InconsistencyType, Is.EqualTo(InconsistencyType.BothFailed));
            Assert.That(analysis.Severity, Is.EqualTo(SeverityLevel.Info));
            Assert.That(analysis.Message, Is.EqualTo("Échec cohérent en mémoire et en BDD"));
        }

        [Test]
        public void AnalyzeConsistency_WithMemorySuccessDatabaseFailure_ShouldReturnInconsistent()
        {
            // Arrange
            var testItem = new ClipboardItem { Id = 456, TextPreview = "Test" };
            var memoryResult = MemoryDeletionResult.CreateSuccess(true, testItem);
            var databaseResult = DatabaseDeletionResult.CreateFailure(3, null, TimeSpan.FromMilliseconds(300), new[] { "Échec BDD" });

            // Act
            var analysis = _stateManager.AnalyzeConsistency(memoryResult, databaseResult);

            // Assert
            Assert.That(analysis.IsConsistent, Is.False);
            Assert.That(analysis.InconsistencyType, Is.EqualTo(InconsistencyType.MemorySuccessDatabaseFailure));
            Assert.That(analysis.Severity, Is.EqualTo(SeverityLevel.Warning));
            Assert.That(analysis.Message, Is.EqualTo("Incohérence: suppression mémoire réussie mais BDD échouée"));
        }

        [Test]
        public void AnalyzeConsistency_WithMemoryFailureDatabaseSuccess_ShouldReturnInconsistent()
        {
            // Arrange
            var memoryResult = MemoryDeletionResult.CreateFailure("Échec mémoire");
            var databaseResult = DatabaseDeletionResult.CreateSuccess(1, false, TimeSpan.FromMilliseconds(100), new[] { "Succès BDD" });

            // Act
            var analysis = _stateManager.AnalyzeConsistency(memoryResult, databaseResult);

            // Assert
            Assert.That(analysis.IsConsistent, Is.False);
            Assert.That(analysis.InconsistencyType, Is.EqualTo(InconsistencyType.MemoryFailureDatabaseSuccess));
            Assert.That(analysis.Severity, Is.EqualTo(SeverityLevel.Error));
            Assert.That(analysis.Message, Is.EqualTo("Incohérence: suppression BDD réussie mais mémoire échouée"));
        }
    }
}
