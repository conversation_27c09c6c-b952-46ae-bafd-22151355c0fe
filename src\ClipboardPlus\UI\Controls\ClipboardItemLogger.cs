using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace ClipboardPlus.UI.Controls
{
    /// <summary>
    /// Logger spécifique pour le débogage des problèmes liés au ClipboardItemControl
    /// </summary>
    public static class ClipboardItemLogger
    {
        private static readonly string LogFilePath = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory, "logs", "ClipboardItemControl.log");
            
        private static readonly object LockObject = new object();
        private static bool _initialized = false;
        
        /// <summary>
        /// Initialise le logger
        /// </summary>
        public static void Initialize()
        {
            if (_initialized)
                return;
                
            try
            {
                // Créer le dossier si nécessaire
                string? directory = Path.GetDirectoryName(LogFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    Directory.CreateDirectory(directory);
                    
                // Écrire l'en-tête du fichier de log
                string header = $"=== ClipboardItemControl Logger démarré le {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===\r\n" +
                                $"Version de l'application: {GetApplicationVersion()}\r\n" +
                                $"Système d'exploitation: {Environment.OSVersion}\r\n" +
                                $"=== Début des logs ===\r\n\r\n";
                                
                File.AppendAllText(LogFilePath, header, Encoding.UTF8);
                
                _initialized = true;
                
                Log("INFO", "Logger initialisé avec succès");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Erreur lors de l'initialisation du logger: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Écrit un message de log avec le niveau spécifié
        /// </summary>
        public static void Log(string level, string message)
        {
            try
            {
                if (!_initialized)
                    Initialize();
                    
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                string threadId = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString();
                string logEntry = $"[{timestamp}] [{threadId}] [{level}] {message}\r\n";
                
                lock (LockObject)
                {
                    File.AppendAllText(LogFilePath, logEntry, Encoding.UTF8);
                }
                
                // Également écrire dans Debug.WriteLine pour la console de débogage
                Debug.WriteLine($"[{level}] {message}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Erreur lors de l'écriture du log: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Log de niveau Information
        /// </summary>
        public static void Info(string message)
        {
            Log("INFO", message);
        }
        
        /// <summary>
        /// Log de niveau Debug
        /// </summary>
        public static void LogDebug(string message)
        {
            Log("DEBUG", message);
        }
        
        /// <summary>
        /// Log de niveau Warning
        /// </summary>
        public static void Warning(string message)
        {
            Log("WARNING", message);
        }
        
        /// <summary>
        /// Log de niveau Error
        /// </summary>
        public static void Error(string message, Exception? ex = null)
        {
            string errorMessage = message;
            if (ex != null)
            {
                errorMessage += $"\r\nException: {ex.GetType().Name}\r\nMessage: {ex.Message}\r\nStack Trace: {ex.StackTrace}";
            }
            
            Log("ERROR", errorMessage);
        }
        
        /// <summary>
        /// Log de niveau Critical
        /// </summary>
        public static void Critical(string message, Exception? ex = null)
        {
            string errorMessage = message;
            if (ex != null)
            {
                errorMessage += $"\r\nException: {ex.GetType().Name}\r\nMessage: {ex.Message}\r\nStack Trace: {ex.StackTrace}";
            }
            
            Log("CRITICAL", errorMessage);
        }
        
        /// <summary>
        /// Journalise les détails d'un élément du presse-papiers
        /// </summary>
        public static void LogClipboardItem(string context, Core.DataModels.ClipboardItem? item)
        {
            if (item == null)
            {
                Warning($"{context}: ClipboardItem est null");
                return;
            }
            
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"{context}: ClipboardItem Details:");
            sb.AppendLine($"  ID: {item.Id}");
            sb.AppendLine($"  DataType: {item.DataType}");
            sb.AppendLine($"  CustomName: {item.CustomName ?? "(null)"}");
            sb.AppendLine($"  IsPinned: {item.IsPinned}");
            sb.AppendLine($"  OrderIndex: {item.OrderIndex}");
            sb.AppendLine($"  Timestamp: {item.Timestamp}");
            sb.AppendLine($"  TextPreview: {(item.TextPreview?.Length > 50 ? item.TextPreview.Substring(0, 50) + "..." : item.TextPreview ?? "(null)")}");
            sb.AppendLine($"  RawData: {(item.RawData != null ? $"{item.RawData.Length} bytes" : "(null)")}");
            
            Info(sb.ToString());
        }
        
        /// <summary>
        /// Journalise les détails d'une commande
        /// </summary>
        public static void LogCommand(string context, System.Windows.Input.ICommand command, object? parameter)
        {
            if (command == null)
            {
                Warning($"{context}: Command est null");
                return;
            }
            
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"{context}: Command Details:");
            sb.AppendLine($"  Type: {command.GetType().FullName}");
            sb.AppendLine($"  CanExecute: {command.CanExecute(parameter)}");
            sb.AppendLine($"  Parameter: {parameter?.ToString() ?? "(null)"}");
            
            Info(sb.ToString());
        }
        
        /// <summary>
        /// Journalise les détails d'un événement
        /// </summary>
        public static void LogEvent(string context, RoutedEventArgs? e)
        {
            if (e == null)
            {
                Warning($"{context}: RoutedEventArgs est null");
                return;
            }
            
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"{context}: Event Details:");
            sb.AppendLine($"  Source: {e.Source?.GetType().Name ?? "(null)"}");
            sb.AppendLine($"  OriginalSource: {e.OriginalSource?.GetType().Name ?? "(null)"}");
            sb.AppendLine($"  Handled: {e.Handled}");
            sb.AppendLine($"  RoutedEvent: {e.RoutedEvent?.Name ?? "(null)"}");
            
            Info(sb.ToString());
        }
        
        /// <summary>
        /// Obtient la version de l'application
        /// </summary>
        private static string GetApplicationVersion()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }
    }
} 