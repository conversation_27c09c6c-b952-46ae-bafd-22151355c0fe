using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.Services.Windows;
using ClipboardPlus.Diagnostics;
using ClipboardPlus.UI.ViewModels.Construction.Interfaces;
using ClipboardPlus.UI.ViewModels.Construction.Models;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.UI.ViewModels.Construction.Implementations
{
    /// <summary>
    /// Implémentation du Builder pour ClipboardHistoryViewModel selon le pattern Builder.
    /// Responsabilité unique : Orchestration de la construction du ViewModel avec architecture SOLID.
    /// </summary>
    public class ClipboardHistoryViewModelBuilder : IClipboardHistoryViewModelBuilder
    {
        private readonly IParameterValidator _parameterValidator;
        private readonly IServiceResolver _serviceResolver;
        private readonly IEventConfigurationService _eventConfigurationService;
        private readonly ICommandInitializer _commandInitializer;
        private readonly IOrchestrationService _orchestrationService;

        // État du Builder pour le pattern fluide
        private IClipboardHistoryManager? _historyManager;
        private IClipboardInteractionService? _clipboardService;
        private ISettingsManager? _settingsManager;
        private IUserNotificationService? _notificationService;
        private IUserInteractionService? _userInteractionService;
        private IServiceProvider? _serviceProvider;
        private IRenameService? _renameService;

        private IDeletionResultLogger? _deletionLogger;
        private ICollectionHealthService? _healthService;
        private IVisibilityStateManager? _visibilityManager;
        private INewItemCreationOrchestrator? _newItemOrchestrator;
        private ITestEnvironmentDetector? _testDetector;
        private ClipboardPlus.Core.Services.Windows.ISettingsWindowService? _settingsWindowService;

        /// <summary>
        /// Initialise le Builder avec tous les services SOLID nécessaires.
        /// </summary>
        /// <param name="parameterValidator">Service de validation des paramètres</param>
        /// <param name="serviceResolver">Service de résolution des dépendances</param>
        /// <param name="eventConfigurationService">Service de configuration des événements</param>
        /// <param name="commandInitializer">Service d'initialisation des commandes</param>
        /// <param name="orchestrationService">Service d'orchestration des composants complexes</param>
        public ClipboardHistoryViewModelBuilder(
            IParameterValidator parameterValidator,
            IServiceResolver serviceResolver,
            IEventConfigurationService eventConfigurationService,
            ICommandInitializer commandInitializer,
            IOrchestrationService orchestrationService)
        {
            _parameterValidator = parameterValidator ?? throw new ArgumentNullException(nameof(parameterValidator));
            _serviceResolver = serviceResolver ?? throw new ArgumentNullException(nameof(serviceResolver));
            _eventConfigurationService = eventConfigurationService ?? throw new ArgumentNullException(nameof(eventConfigurationService));
            _commandInitializer = commandInitializer ?? throw new ArgumentNullException(nameof(commandInitializer));
            _orchestrationService = orchestrationService ?? throw new ArgumentNullException(nameof(orchestrationService));
        }

        /// <summary>
        /// Configure les dépendances obligatoires du ViewModel.
        /// Implémentation du pattern Builder fluide pour les paramètres requis.
        /// </summary>
        /// <param name="historyManager">Gestionnaire de l'historique du presse-papiers</param>
        /// <param name="clipboardService">Service d'interaction avec le presse-papiers</param>
        /// <param name="settingsManager">Gestionnaire des paramètres de l'application</param>
        /// <param name="notificationService">Service de notifications utilisateur</param>
        /// <param name="userInteractionService">Service d'interaction utilisateur</param>
        /// <param name="serviceProvider">Fournisseur de services pour l'injection de dépendances</param>
        /// <param name="renameService">Service de renommage des éléments</param>
        /// <returns>Instance du Builder pour chaînage fluide</returns>
        public IClipboardHistoryViewModelBuilder WithRequiredDependencies(
            IClipboardHistoryManager historyManager,
            IClipboardInteractionService clipboardService,
            ISettingsManager settingsManager,
            IUserNotificationService notificationService,
            IUserInteractionService userInteractionService,
            IServiceProvider serviceProvider,
            IRenameService renameService)
        {
            _historyManager = historyManager;
            _clipboardService = clipboardService;
            _settingsManager = settingsManager;
            _notificationService = notificationService;
            _userInteractionService = userInteractionService;
            _serviceProvider = serviceProvider;
            _renameService = renameService;

            return this;
        }

        /// <summary>
        /// Configure les dépendances optionnelles du ViewModel.
        /// Implémentation du pattern Builder fluide pour les paramètres optionnels.
        /// </summary>
        /// <param name="deletionLogger">Logger pour les opérations de suppression (optionnel)</param>
        /// <param name="healthService">Service de santé des collections (optionnel)</param>
        /// <param name="visibilityManager">Gestionnaire de visibilité SOLID (optionnel)</param>
        /// <param name="newItemOrchestrator">Orchestrateur de création d'éléments (optionnel)</param>
        /// <param name="testDetector">Détecteur d'environnement de test (optionnel)</param>
        /// <param name="settingsWindowService">Service de gestion des fenêtres de paramètres (optionnel)</param>
        /// <returns>Instance du Builder pour chaînage fluide</returns>
        public IClipboardHistoryViewModelBuilder WithOptionalDependencies(
            IDeletionResultLogger? deletionLogger = null,
            ICollectionHealthService? healthService = null,
            IVisibilityStateManager? visibilityManager = null,
            INewItemCreationOrchestrator? newItemOrchestrator = null,
            ITestEnvironmentDetector? testDetector = null,
            ClipboardPlus.Core.Services.Windows.ISettingsWindowService? settingsWindowService = null)
        {
            _deletionLogger = deletionLogger;
            _healthService = healthService;
            _visibilityManager = visibilityManager;
            _newItemOrchestrator = newItemOrchestrator;
            _testDetector = testDetector;
            _settingsWindowService = settingsWindowService;

            return this;
        }

        /// <summary>
        /// Construit l'instance finale de ClipboardHistoryViewModel avec architecture SOLID.
        /// Cette méthode utilise le nouveau constructeur simplifié + InitializeAsync() pour une
        /// approche modulaire respectant les principes SOLID (SRP, OCP, LSP, ISP, DIP).
        /// </summary>
        /// <returns>Instance configurée de ClipboardHistoryViewModel</returns>
        public async Task<ClipboardHistoryViewModel> BuildAsync()
        {
            // Validation que toutes les dépendances obligatoires sont configurées
            if (_historyManager == null || _clipboardService == null || _settingsManager == null ||
                _notificationService == null || _userInteractionService == null ||
                _serviceProvider == null || _renameService == null)
            {
                throw new InvalidOperationException(
                    "Les dépendances obligatoires doivent être configurées avec WithRequiredDependencies() avant d'appeler Build().");
            }

            // Étape 1 : Validation des paramètres obligatoires (SRP)
            _parameterValidator.ValidateRequiredParameters(
                _historyManager, _clipboardService, _settingsManager,
                _notificationService, _userInteractionService, _serviceProvider, _renameService);

            // Étape 2 : Résolution des services optionnels et complexes (DIP)
            var resolvedServices = _serviceResolver.ResolveOptionalServices(
                _serviceProvider, _deletionLogger, _healthService,
                _visibilityManager, _newItemOrchestrator, _testDetector);

            // Étape 3 : Construction du ViewModel avec le nouveau constructeur DTO (PHASE 5 - PERFECTIONNEMENT)
            // Résolution des modules depuis le ServiceProvider
            var historyModule = _serviceProvider.GetRequiredService<ClipboardPlus.Modules.History.IHistoryModule>();
            var commandModule = _serviceProvider.GetRequiredService<ClipboardPlus.Modules.Commands.ICommandModule>();
            var creationModule = _serviceProvider.GetRequiredService<ClipboardPlus.Modules.Creation.ICreationModule>();

            var dependencies = new ViewModelDependencies(
                _historyManager,
                _clipboardService,
                _settingsManager,
                _notificationService,
                _userInteractionService,
                _renameService,
                historyModule,
                commandModule,
                creationModule,
                _serviceProvider
            );

            var optionalServices = new OptionalServicesDependencies(
                DeletionResultLogger: resolvedServices.DeletionLogger,
                CollectionHealthService: resolvedServices.HealthService,
                VisibilityStateManager: resolvedServices.VisibilityManager,
                NewItemCreationOrchestrator: resolvedServices.NewItemOrchestrator,
                TestEnvironmentDetector: resolvedServices.TestDetector,
                SettingsWindowService: _settingsWindowService
            );

            var viewModel = new ClipboardHistoryViewModel(dependencies, optionalServices);

            // Étape 4 : Initialisation des commandes via CommandInitializer (PHASE 4 - PURE SOLID)
            _commandInitializer.InitializeAllCommands(viewModel);

            // Étape 5 : Initialisation asynchrone complète via la nouvelle architecture SOLID
            await viewModel.InitializeAsync();

            return viewModel;
        }

        /// <summary>
        /// Construit l'instance finale de ClipboardHistoryViewModel de manière synchrone.
        /// Cette méthode est conservée pour la compatibilité ascendante mais utilise l'ancienne approche
        /// avec ConfigurePostConstruction(). Il est recommandé d'utiliser BuildAsync() pour la nouvelle architecture.
        /// </summary>
        /// <returns>Instance configurée de ClipboardHistoryViewModel</returns>
        public ClipboardHistoryViewModel Build()
        {
            // Validation que toutes les dépendances obligatoires sont configurées
            if (_historyManager == null || _clipboardService == null || _settingsManager == null ||
                _notificationService == null || _userInteractionService == null ||
                _serviceProvider == null || _renameService == null)
            {
                throw new InvalidOperationException(
                    "Les dépendances obligatoires doivent être configurées avec WithRequiredDependencies() avant d'appeler Build().");
            }

            // Étape 1 : Validation des paramètres obligatoires (SRP)
            _parameterValidator.ValidateRequiredParameters(
                _historyManager, _clipboardService, _settingsManager,
                _notificationService, _userInteractionService, _serviceProvider, _renameService);

            // Étape 2 : Résolution des services optionnels et complexes (DIP)
            var resolvedServices = _serviceResolver.ResolveOptionalServices(
                _serviceProvider, _deletionLogger, _healthService,
                _visibilityManager, _newItemOrchestrator, _testDetector);

            // Étape 3 : Construction du ViewModel avec le nouveau constructeur DTO (PHASE 5 - PERFECTIONNEMENT)
            // Résolution des modules depuis le ServiceProvider
            var historyModule = _serviceProvider.GetRequiredService<ClipboardPlus.Modules.History.IHistoryModule>();
            var commandModule = _serviceProvider.GetRequiredService<ClipboardPlus.Modules.Commands.ICommandModule>();
            var creationModule = _serviceProvider.GetRequiredService<ClipboardPlus.Modules.Creation.ICreationModule>();

            var dependencies = new ViewModelDependencies(
                _historyManager,
                _clipboardService,
                _settingsManager,
                _notificationService,
                _userInteractionService,
                _renameService,
                historyModule,
                commandModule,
                creationModule,
                _serviceProvider
            );

            var optionalServices = new OptionalServicesDependencies(
                DeletionResultLogger: resolvedServices.DeletionLogger,
                CollectionHealthService: resolvedServices.HealthService,
                VisibilityStateManager: resolvedServices.VisibilityManager,
                NewItemCreationOrchestrator: resolvedServices.NewItemOrchestrator,
                TestEnvironmentDetector: resolvedServices.TestDetector,
                SettingsWindowService: _settingsWindowService,
                SkipCommandInitialization: true // Le Builder gère l'initialisation des commandes
            );

            var viewModel = new ClipboardHistoryViewModel(dependencies, optionalServices);

            // Étape 4 : Résolution des services complexes via l'architecture unifiée (indépendante du ViewModel)
            var complexServices = _serviceResolver.ResolveComplexServices(_serviceProvider);

            // Étape 5 : Configuration post-construction (ISP) - Ancienne approche pour compatibilité
            ConfigurePostConstruction(viewModel, resolvedServices, complexServices);

            return viewModel;
        }

        /// <summary>
        /// Configure le ViewModel après sa construction initiale.
        /// Cette méthode respecte l'ordre exact du constructeur original pour préserver le comportement.
        ///
        /// ORDRE CRITIQUE PRÉSERVÉ (lignes 269-300 du constructeur original) :
        /// 1. Configuration visibilité (269-273)
        /// 2. HistoryCollectionSynchronizer (275-282) - DÉJÀ CRÉÉ dans le constructeur
        /// 3. Commandes (284-286)
        /// 4. Migration services (289)
        /// 5. History change orchestrator (292-295)
        /// 6. Collection health service (298)
        /// 7. LoadHistoryAsync (300)
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel à configurer</param>
        /// <param name="resolvedServices">Services résolus</param>
        /// <param name="complexServices">Services complexes</param>
        private void ConfigurePostConstruction(
            ClipboardHistoryViewModel viewModel,
            ResolvedServices resolvedServices,
            ComplexServices complexServices)
        {
            // 1. Configuration des événements de visibilité (lignes 269-273)
            _eventConfigurationService.ConfigureVisibilityEvents(
                viewModel, resolvedServices.VisibilityManager, complexServices.LoggingService);

            // 2. HistoryCollectionSynchronizer (lignes 275-282) - DÉJÀ CRÉÉ dans le constructeur principal

            // 3. Initialisation des commandes (lignes 284-286)
            _commandInitializer.InitializeAllCommands(viewModel);

            // 4. Services de migration progressive (ligne 289)
            _orchestrationService.InitializeMigrationServices(viewModel);

            // 5. Orchestrateur de changements d'historique (lignes 292-295)
            _orchestrationService.InitializeHistoryChangeOrchestrator(viewModel);

            // 6. Service de santé des collections (ligne 298)
            _orchestrationService.InitializeCollectionHealthService(viewModel);

            // 7. Déclenchement du chargement asynchrone (ligne 300)
            _ = _orchestrationService.TriggerHistoryLoadAsync(viewModel);
        }
    }
}
