using NUnit.Framework;
using System.Reflection;
using ClipboardPlus.UI.Controls;
using System.Windows;
using System.Linq;
using System;

namespace ClipboardPlus.Tests.Unit.UI.Controls
{
    /// <summary>
    /// Tests fonctionnels pour ControlExtensions sans utiliser de thread STA
    /// Ces tests se concentrent sur la logique et la structure des méthodes d'extension
    /// </summary>
    [TestFixture]
    public class ControlExtensionsFunctionalTests
    {
        [Test]
        public void ControlExtensions_IsStaticClass()
        {
            // Arrange & Act
            var extensionsType = typeof(ControlExtensions);

            // Assert
            Assert.That(extensionsType.IsClass, Is.True, "ControlExtensions devrait être une classe");
            Assert.That(extensionsType.IsSealed, Is.True, "ControlExtensions devrait être sealed");
            Assert.That(extensionsType.IsAbstract, Is.True, "ControlExtensions devrait être abstract (classe statique)");
        }

        [Test]
        public void ControlExtensions_HasCorrectNamespace()
        {
            // Arrange & Act
            var extensionsType = typeof(ControlExtensions);

            // Assert
            Assert.That(extensionsType.Namespace, Is.EqualTo("ClipboardPlus.UI.Controls"),
                "ControlExtensions devrait être dans le bon namespace");
        }

        [Test]
        public void FindParentOfType_Method_Exists()
        {
            // Arrange
            var extensionsType = typeof(ControlExtensions);

            // Act
            var methods = extensionsType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.Name == "FindParentOfType" && m.IsGenericMethod)
                .ToArray();

            // Assert
            Assert.That(methods.Length > 0, Is.True, "La méthode FindParentOfType<T> devrait exister");

            var method = methods.First();
            Assert.That(method.IsStatic, Is.True, "FindParentOfType devrait être statique");
            Assert.That(method.IsPublic, Is.True, "FindParentOfType devrait être publique");
            Assert.That(method.IsGenericMethod, Is.True, "FindParentOfType devrait être générique");
        }

        [Test]
        public void FindParentOfType_HasCorrectParameters()
        {
            // Arrange
            var extensionsType = typeof(ControlExtensions);

            // Act
            var method = extensionsType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.Name == "FindParentOfType" && m.IsGenericMethod)
                .FirstOrDefault();

            // Assert
            Assert.That(method, Is.Not.Null, "FindParentOfType devrait exister");

            var parameters = method!.GetParameters();
            Assert.That(parameters.Length, Is.EqualTo(1), "FindParentOfType devrait avoir 1 paramètre");
            Assert.That(parameters[0].ParameterType, Is.EqualTo(typeof(DependencyObject)),
                "Le paramètre devrait être de type DependencyObject");

            // Vérifier que c'est une méthode d'extension en vérifiant la signature
            Assert.That(method.IsDefined(typeof(System.Runtime.CompilerServices.ExtensionAttribute), false), Is.True,
                "La méthode devrait être marquée comme méthode d'extension");
        }

        [Test]
        public void FindParentOfType_HasGenericConstraint()
        {
            // Arrange
            var extensionsType = typeof(ControlExtensions);

            // Act
            var method = extensionsType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.Name == "FindParentOfType" && m.IsGenericMethod)
                .FirstOrDefault();

            // Assert
            Assert.That(method, Is.Not.Null, "FindParentOfType devrait exister");

            var genericArguments = method!.GetGenericArguments();
            Assert.That(genericArguments.Length, Is.EqualTo(1), "FindParentOfType devrait avoir 1 argument générique");

            var constraints = genericArguments[0].GetGenericParameterConstraints();
            Assert.That(constraints.Length > 0, Is.True, "L'argument générique devrait avoir des contraintes");
            Assert.That(constraints.Contains(typeof(DependencyObject)), Is.True,
                "L'argument générique devrait être contraint à DependencyObject");
        }

        [Test]
        public void FindParentOfType_Logic_WithNullInput_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert - La méthode devrait lever ArgumentNullException avec null
            // car VisualTreeHelper.GetParent ne peut pas accepter null

            try
            {
                // Essayer d'appeler la méthode avec null - cela devrait lever une exception
                var result = ControlExtensions.FindParentOfType<DependencyObject>(null!);
                Assert.Fail("FindParentOfType avec null devrait lever ArgumentNullException");
            }
            catch (ArgumentNullException)
            {
                // C'est le comportement attendu
                Assert.That(true, Is.True, "ArgumentNullException levée comme attendu");
            }
            catch (System.InvalidOperationException ex) when (ex.Message.Contains("STA"))
            {
                // Si on a une erreur STA, on teste au moins que la méthode existe
                Assert.Inconclusive("Test ignoré car nécessite un thread STA pour l'exécution");
            }
        }

        [Test]
        public void ControlExtensions_AllMethods_AreExtensionMethods()
        {
            // Arrange
            var extensionsType = typeof(ControlExtensions);

            // Act
            var publicMethods = extensionsType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.DeclaringType == extensionsType)
                .ToArray();

            // Assert
            Assert.That(publicMethods.Length > 0, Is.True, "ControlExtensions devrait avoir des méthodes publiques");

            foreach (var method in publicMethods)
            {
                // Vérifier que la méthode elle-même est marquée comme extension
                var hasExtensionAttribute = method.IsDefined(typeof(System.Runtime.CompilerServices.ExtensionAttribute), false);
                Assert.That(hasExtensionAttribute, Is.True, $"La méthode {method.Name} devrait être une méthode d'extension");
            }
        }

        [Test]
        public void ControlExtensions_Methods_ReturnCorrectTypes()
        {
            // Arrange
            var extensionsType = typeof(ControlExtensions);

            // Act
            var findParentMethod = extensionsType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.Name == "FindParentOfType" && m.IsGenericMethod)
                .FirstOrDefault();

            // Assert
            Assert.That(findParentMethod, Is.Not.Null, "FindParentOfType devrait exister");

            // Le type de retour devrait être le type générique T
            var returnType = findParentMethod!.ReturnType;
            Assert.That(returnType.IsGenericParameter, Is.True, "FindParentOfType devrait retourner le type générique T");
        }

        [Test]
        public void ControlExtensions_HasCorrectUsings()
        {
            // Arrange & Act - Vérifier que la classe peut accéder aux types nécessaires
            var extensionsType = typeof(ControlExtensions);

            // Vérifier que les types utilisés sont accessibles
            Assert.That(typeof(DependencyObject), Is.Not.Null, "DependencyObject devrait être accessible");
            Assert.That(typeof(FrameworkElement), Is.Not.Null, "FrameworkElement devrait être accessible");

            // Assert
            Assert.That(true, Is.True, "Les types WPF nécessaires sont accessibles");
        }

        [Test]
        public void ControlExtensions_CanBeUsedAsExtension()
        {
            // Arrange & Act - Tester que la syntaxe d'extension est possible
            var extensionsType = typeof(ControlExtensions);

            // Vérifier que la classe est dans un assembly qui permet les extensions
            var assembly = extensionsType.Assembly;
            Assert.That(assembly, Is.Not.Null, "L'assembly devrait être accessible");

            // Vérifier que la classe est marquée comme contenant des méthodes d'extension
            var hasExtensionAttribute = extensionsType.IsDefined(typeof(System.Runtime.CompilerServices.ExtensionAttribute), false);
            Assert.That(hasExtensionAttribute, Is.True, "ControlExtensions devrait avoir l'attribut Extension");
        }

        [Test]
        public void ControlExtensions_Methods_AreWellDocumented()
        {
            // Arrange
            var extensionsType = typeof(ControlExtensions);

            // Act
            var methods = extensionsType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.DeclaringType == extensionsType)
                .ToArray();

            // Assert
            Assert.That(methods.Length > 0, Is.True, "ControlExtensions devrait avoir des méthodes");

            foreach (var method in methods)
            {
                // Vérifier que les méthodes ont des noms descriptifs
                Assert.That(string.IsNullOrEmpty(method.Name), Is.False, $"La méthode devrait avoir un nom: {method.Name}");
                Assert.That(method.Name.Length > 3, Is.True, $"Le nom de méthode devrait être descriptif: {method.Name}");
            }
        }
    }
}
