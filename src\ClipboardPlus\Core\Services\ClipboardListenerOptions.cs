namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Options de configuration pour le service d'écoute du presse-papier
    /// </summary>
    public class ClipboardListenerOptions
    {
        /// <summary>
        /// Intervalle en millisecondes pour le debounce des événements
        /// </summary>
        public int DebounceIntervalMs { get; set; } = 300;

        /// <summary>
        /// Nombre de tentatives en cas d'échec du démarrage (augmenté pour plus de robustesse)
        /// </summary>
        public int StartupRetryCount { get; set; } = 10;

        /// <summary>
        /// Délai de base entre les tentatives de démarrage en millisecondes (pour backoff exponentiel)
        /// </summary>
        public int StartupRetryDelayMs { get; set; } = 500;

        /// <summary>
        /// Délai maximum entre les tentatives en millisecondes (pour limiter le backoff exponentiel)
        /// </summary>
        public int MaxRetryDelayMs { get; set; } = 30000;

        /// <summary>
        /// Active le backoff exponentiel pour les tentatives de retry
        /// </summary>
        public bool UseExponentialBackoff { get; set; } = true;

        /// <summary>
        /// Indique si les opérations UI doivent être bloquantes (Invoke) ou non (InvokeAsync)
        /// </summary>
        public bool UseBlockingUIOperations { get; set; } = true;

        /// <summary>
        /// Active le monitoring de santé automatique
        /// </summary>
        public bool EnableHealthMonitoring { get; set; } = true;

        /// <summary>
        /// Intervalle en millisecondes pour les vérifications de santé
        /// </summary>
        public int HealthCheckIntervalMs { get; set; } = 5000;

        /// <summary>
        /// Nombre d'échecs consécutifs avant de déclencher une récupération automatique
        /// </summary>
        public int HealthCheckFailureThreshold { get; set; } = 3;
    }
}