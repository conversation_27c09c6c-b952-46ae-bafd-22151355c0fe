using System;
using System.Windows;
using System.Windows.Input;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Windows.Threading;
using WpfApplication = System.Windows.Application;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.UI.Views;
using WpfMessageBox = System.Windows.MessageBox;
using System.Linq;
using ClipboardPlus.UI.Helpers;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Media;
using ClipboardPlus.Core.Services.WindowDeactivation;

namespace ClipboardPlus.UI.Windows
{
    /// <summary>
    /// Logique d'interaction pour ClipboardHistoryWindow.xaml
    /// </summary>
    public partial class ClipboardHistoryWindow : Window
    {
        private bool _isClosing = false;
        private ILoggingService? _loggingService;
        private ClipboardHistoryViewModel? _viewModel;
        private IWindowDeactivationOrchestrator? _windowDeactivationOrchestrator;

        /// <summary>
        /// Initialise une nouvelle instance de la fenêtre d'historique du presse-papiers.
        /// Ce constructeur est utilisé par le designer XAML ou si la fenêtre est créée sans ViewModel initial.
        /// </summary>
        public ClipboardHistoryWindow()
        {
            InitializeComponent();
            _loggingService = GetLoggingService();
            _loggingService?.LogInfo("ClipboardHistoryWindow - Constructeur par défaut initialisé.");
            SetupWindow();
        }

        /// <summary>
        /// Initialise une nouvelle instance de la fenêtre d'historique du presse-papiers avec un ViewModel spécifié.
        /// </summary>
        /// <param name="viewModel">Le ViewModel à utiliser pour cette fenêtre.</param>
        public ClipboardHistoryWindow(ClipboardHistoryViewModel viewModel)
        {
            InitializeComponent();
            _loggingService = GetLoggingService();
            _viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
            DataContext = _viewModel;
            _loggingService?.LogInfo("ClipboardHistoryWindow - Constructeur avec ViewModel initialisé.");
            SetupWindow();
        }

        /// <summary>
        /// Configure les aspects communs de la fenêtre, appelés par les deux constructeurs.
        /// </summary>
        private void SetupWindow()
        {
            // Définir la position de la fenêtre au centre de l'écran
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // Initialiser le système de désactivation de fenêtre refactorisé
            InitializeWindowDeactivationOrchestrator();

            // Gérer l'événement KeyDown pour permettre de fermer la fenêtre avec Echap
            KeyDown += (s, e) => {
                if (e.Key == Key.Escape)
                {
                    _loggingService?.LogInfo("ClipboardHistoryWindow: Touche Echap pressée, fermeture de la fenêtre.");
                    this.Close(); // Devrait être géré par OnClosing pour masquer
                }
            };

            // S'assurer que le DataContext est assigné si ce n'est pas fait par le constructeur avec ViewModel
            if (DataContext == null && _viewModel != null)
            {
                DataContext = _viewModel;
            }
            else if (DataContext is ClipboardHistoryViewModel vm)
            {
                _viewModel = vm;
            }
            // Si DataContext est null et _viewModel est null, il sera probablement défini plus tard ou par XAML.
        }

        /// <summary>
        /// Initialise l'orchestrateur de désactivation de fenêtre refactorisé.
        /// MIGRATION TERMINÉE - Utilise directement les services refactorisés.
        /// </summary>
        private void InitializeWindowDeactivationOrchestrator()
        {
            try
            {
                if (_loggingService == null)
                {
                    return; // Pas de logging disponible, on ne peut pas initialiser
                }

                _loggingService.LogInfo("🔧 [REFACTORISATION TERMINÉE] Initialisation directe de l'orchestrateur Window_Deactivated");

                // Créer les services directement (plus de système de migration)
                var stateValidator = new WindowStateValidator(_loggingService);
                var diagnosticService = new WindowDiagnosticService(_loggingService);
                var windowClassifier = new ApplicationWindowClassifier(_loggingService);
                var decisionService = new WindowVisibilityDecisionService(_loggingService);
                var deactivationLoggingService = new WindowDeactivationLoggingService(_loggingService);

                // Créer l'orchestrateur directement
                _windowDeactivationOrchestrator = new WindowDeactivationOrchestrator(
                    stateValidator,
                    diagnosticService,
                    windowClassifier,
                    decisionService,
                    deactivationLoggingService,
                    _loggingService);

                _loggingService.LogInfo("✅ [ClipboardHistoryWindow] Orchestrateur Window_Deactivated initialisé avec succès");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"❌ [ClipboardHistoryWindow] Erreur lors de l'initialisation de l'orchestrateur: {ex.Message}", ex);
                throw; // Re-lancer car sans orchestrateur, la fenêtre ne peut pas fonctionner correctement
            }
        }

        /// <summary>
        /// Donne le focus à l'élément principal de la fenêtre (par exemple, la barre de recherche).
        /// </summary>
        public void FocusPrimaryElement()
        {
            _loggingService?.LogInfo("ClipboardHistoryWindow: Tentative de donner le focus à SearchFilterTextBox.");
            // Assurez-vous que SearchFilterTextBox est le x:Name de votre TextBox de recherche dans ClipboardHistoryWindow.xaml
            if (SearchFilterTextBox != null) // SearchFilterTextBox est défini dans le fichier XAML généré (partial class)
            {
                SearchFilterTextBox.Focus();
                _loggingService?.LogInfo("ClipboardHistoryWindow: Focus donné à SearchFilterTextBox.");
            }
            else
            {
                _loggingService?.LogWarning("ClipboardHistoryWindow: SearchFilterTextBox non trouvée pour le focus.");
            }
        }

        /// <summary>
        /// Gère l'événement MouseDoubleClick de la ListBox des éléments de l'historique
        /// </summary>
        private void HistoryListBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            _loggingService?.LogInfo($"[DÉBUT] HistoryListBox_MouseDoubleClick - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            Debug.WriteLine($"[DÉBUT] HistoryListBox_MouseDoubleClick - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");

            // Générer un ID d'opération pour un suivi facile
            string operationId = Guid.NewGuid().ToString("N").Substring(0, 8);

            // Journaliser le début de l'opération
            _loggingService?.LogInfo($"HistoryListBox_MouseDoubleClick [{operationId}] - Début de l'événement.");
            Debug.WriteLine($"HistoryListBox_MouseDoubleClick [{operationId}] - Début de l'événement.");

            if (DataContext is ClipboardHistoryViewModel viewModel && viewModel.SelectedClipboardItem != null)
            {
                var selectedItem = viewModel.SelectedClipboardItem;
                _loggingService?.LogInfo($"HistoryListBox_MouseDoubleClick [{operationId}] - Élément sélectionné: {selectedItem.Id} - '{selectedItem.CustomName ?? "(sans nom)"}'");
                Debug.WriteLine($"HistoryListBox_MouseDoubleClick [{operationId}] - Élément sélectionné: {selectedItem.Id} - '{selectedItem.CustomName ?? "(sans nom)"}'");

                // Exécuter directement la commande du ViewModel
                if (viewModel.PasteSelectedItemCommand.CanExecute(selectedItem))
                {
                    _loggingService?.LogInfo($"HistoryListBox_MouseDoubleClick [{operationId}] - Exécution de PasteSelectedItemCommand.");
                    Debug.WriteLine($"HistoryListBox_MouseDoubleClick [{operationId}] - Exécution de PasteSelectedItemCommand.");
                    viewModel.PasteSelectedItemCommand.Execute(selectedItem);
                }
                else
                {
                    _loggingService?.LogInfo($"HistoryListBox_MouseDoubleClick [{operationId}] - PasteSelectedItemCommand ne peut pas être exécuté.");
                    Debug.WriteLine($"HistoryListBox_MouseDoubleClick [{operationId}] - PasteSelectedItemCommand ne peut pas être exécuté.");
                }
            }
            else
            {
                _loggingService?.LogWarning($"HistoryListBox_MouseDoubleClick [{operationId}] - ViewModel ou SelectedClipboardItem est null.");
                Debug.WriteLine($"HistoryListBox_MouseDoubleClick [{operationId}] - ViewModel ou SelectedClipboardItem est null.");
            }

            // Marquer l'événement comme géré pour éviter d'autres actions
            e.Handled = true;
            _loggingService?.LogInfo($"[FIN] HistoryListBox_MouseDoubleClick [{operationId}] - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            Debug.WriteLine($"[FIN] HistoryListBox_MouseDoubleClick [{operationId}] - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
        }



        /// <summary>
        /// Gère l'événement Loaded de la fenêtre
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            _loggingService?.LogInfo("ClipboardHistoryWindow: Fenêtre chargée (Window_Loaded).");
            // S'assurer que la fenêtre est visible et a le focus
            // Activate(); // Activer peut être agressif ici, Show() dans SystemTrayService devrait suffire
            FocusPrimaryElement(); // Donner le focus à la barre de recherche
        }

        /// <summary>
        /// Gère l'événement Deactivated de la fenêtre avec l'architecture refactorisée.
        /// REFACTORISATION TERMINÉE - Utilise directement l'orchestrateur sans système de migration.
        /// </summary>
        private void Window_Deactivated(object sender, EventArgs e)
        {
            try
            {
                // ARCHITECTURE REFACTORISÉE - Appel direct à l'orchestrateur
                if (_windowDeactivationOrchestrator != null)
                {
                    var result = _windowDeactivationOrchestrator.HandleWindowDeactivation(this, e);

                    // Log uniquement en cas d'erreur
                    if (!result.IsSuccessful)
                    {
                        _loggingService?.LogError($"❌ [Window_Deactivated] Erreur: {result.Details}");
                    }
                }
                else
                {
                    _loggingService?.LogError("❌ [ClipboardHistoryWindow] Orchestrateur non initialisé - Erreur critique");
                    throw new InvalidOperationException("L'orchestrateur Window_Deactivated n'est pas initialisé");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"❌ [ClipboardHistoryWindow] Erreur critique dans Window_Deactivated: {ex.Message}", ex);
                throw; // Re-lancer l'exception car l'orchestrateur est essentiel
            }
        }

        // REFACTORISATION TERMINÉE - Plus de système de migration
        // L'architecture refactorisée utilise directement l'orchestrateur sans couche de migration

        /// <summary>
        /// Obtient les métriques de performance de l'orchestrateur.
        /// </summary>
        /// <returns>Métriques de performance ou null si non disponible</returns>
        public WindowDeactivationPerformanceMetrics? GetPerformanceMetrics()
        {
            try
            {
                return _windowDeactivationOrchestrator?.GetPerformanceMetrics();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"❌ [ClipboardHistoryWindow] Erreur lors de la récupération des métriques: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// Vérifie si le focus est toujours dans l'application
        /// </summary>
        private bool IsFocusWithinApplication()
        {
            try
            {
                // Obtenir toutes les fenêtres de l'application
                var appWindows = System.Windows.Application.Current.Windows.OfType<Window>().ToList();

                // Vérifier si une fenêtre de l'application a le focus
                foreach (var window in appWindows)
                {
                    if (window.IsActive)
                    {
                return true;
            }

                    // Vérifier si un élément de la fenêtre a le focus
                    var focusedElement = Keyboard.FocusedElement as DependencyObject;
                    if (focusedElement != null)
                    {
                        // Remonter la hiérarchie visuelle pour trouver la fenêtre parente
                        Window? parentWindow = Window.GetWindow(focusedElement);
                        if (parentWindow != null && parentWindow == window)
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"Erreur lors de la vérification du focus dans IsFocusWithinApplication: {ex.Message}. Comportement par défaut : focus non dans l'application.");
                // En cas d'erreur, supposer que le focus n'est pas dans l'application
                return false;
            }
        }

        /// <summary>
        /// Obtient le service de journalisation de l'application
        /// </summary>
        private ILoggingService? GetLoggingService()
        {
            try
            {
                var app = System.Windows.Application.Current;
                if (app != null)
                {
                    var services = app.GetType().GetProperty("Services")?.GetValue(app);
                    if (services != null)
                    {
                        var getServiceMethod = services.GetType().GetMethod("GetService", new Type[] { typeof(Type) });
                        if (getServiceMethod != null)
                        {
                            return getServiceMethod.Invoke(services, new object[] { typeof(ILoggingService) }) as ILoggingService;
                        }
                    }
                }
            }
            catch
            {
                // Ignorer les erreurs, retourner null en cas d'échec
            }
            return null;
        }

        /// <summary>
        /// Gère la fermeture de la fenêtre
        /// </summary>
        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            _loggingService?.LogInfo($"[DÉBUT] OnClosing - _isClosing: {_isClosing}, Heure: {DateTime.Now:HH:mm:ss.fff}");

            if (_isClosing) // _isClosing est mis à true pour une vraie fermeture (par ex. via Quitter)
            {
                _loggingService?.LogInfo("OnClosing: Vraie fermeture demandée (_isClosing=true). Nettoyage et fermeture.");
                // S'assurer que les événements sont détachés pour éviter les fuites de mémoire
                if (_viewModel != null)
                {
                    // Ex: _viewModel.SomeEvent -= ViewModel_SomeEventHandler;
                }
                base.OnClosing(e); // Ne pas annuler, laisser la fenêtre se fermer
            }
            else
            {
                _loggingService?.LogInfo("OnClosing: Fermeture standard interceptée. Annulation et masquage de la fenêtre.");
                e.Cancel = true; // Annuler la fermeture
                this.Hide();     // Masquer la fenêtre à la place
            }
            _loggingService?.LogInfo($"[FIN] OnClosing - e.Cancel: {e.Cancel}, Heure: {DateTime.Now:HH:mm:ss.fff}");
        }

        /// <summary>
        /// Méthode pour initier une "vraie" fermeture de la fenêtre, par exemple, lors de la fermeture de l'application.
        /// </summary>
        public void PerformTrueClose()
        {
            _loggingService?.LogInfo("PerformTrueClose: Vraie fermeture demandée pour ClipboardHistoryWindow.");
            _isClosing = true;
            this.Close();
        }


    }
}