using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using NUnit.Framework;

namespace ClipboardPlus.Tests.STA.UI.ViewModels
{
    /// <summary>
    /// Tests de validation simple pour la migration progressive.
    ///
    /// Ces tests valident que les composants de base de la migration fonctionnent
    /// sans dépendre de mocks complexes.
    /// </summary>
    [TestFixture]
    [Apartment(System.Threading.ApartmentState.STA)]
    public class HistoryChanged_MigrationValidation
    {
        private IFeatureFlagService _featureFlagService = null!;
        private ILoggingService _mockLoggingService = null!;

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Moq.Mock<ILoggingService>().Object;
            _featureFlagService = new FeatureFlagService(_mockLoggingService);
        }

        /// <summary>
        /// Test de base : Le service de feature flags fonctionne.
        /// </summary>
        [Test]
        [Description("MIGRATION: Service de feature flags opérationnel")]
        public void FeatureFlagService_BasicFunctionality_ShouldWork()
        {
            // Arrange & Act
            bool initialState = _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);

            // Assert - MIGRATION FINALISÉE
            Assert.That(initialState, Is.True, "La nouvelle méthode devrait être activée (migration finalisée)");

            // Test d'activation
            _featureFlagService.SetFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, true);
            _featureFlagService.SetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, 100);

            bool activatedState = _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);
            Assert.That(activatedState, Is.True, "La nouvelle méthode devrait être activée après configuration");

            TestContext.WriteLine("✅ Service de feature flags opérationnel");
        }

        /// <summary>
        /// Test de rollout progressif.
        /// </summary>
        [Test]
        [Description("MIGRATION: Rollout progressif fonctionnel")]
        public void FeatureFlagService_ProgressiveRollout_ShouldWork()
        {
            // Arrange
            _featureFlagService.SetFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, true);

            // Test avec 0% de rollout
            _featureFlagService.SetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, 0);
            bool zeroPercent = _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);

            // Test avec 100% de rollout
            _featureFlagService.SetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, 100);
            bool hundredPercent = _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);

            // Assert
            Assert.That(zeroPercent, Is.False, "0% de rollout devrait désactiver la fonctionnalité");
            Assert.That(hundredPercent, Is.True, "100% de rollout devrait activer la fonctionnalité");

            TestContext.WriteLine("✅ Rollout progressif fonctionnel");
        }

        /// <summary>
        /// Test des helpers de migration.
        /// </summary>
        [Test]
        [Description("MIGRATION: Migration HistoryChanged terminée - Validation de l'état final")]
        public void MigrationCompleted_HistoryChangedRefactored_ShouldBeActive()
        {
            // La migration est terminée - la nouvelle méthode doit être active par défaut
            bool useRefactored = _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);
            int rolloutPercentage = _featureFlagService.GetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);

            Assert.That(useRefactored, Is.True, "La nouvelle méthode HistoryChanged doit être activée (migration terminée)");
            Assert.That(rolloutPercentage, Is.EqualTo(100), "Le rollout doit être à 100% (migration terminée)");

            // Test de configuration manuelle (toujours possible)
            _featureFlagService.SetFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, true);
            _featureFlagService.SetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, 75);

            bool manuallyEnabled = _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);
            int manualPercentage = _featureFlagService.GetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);

            Assert.That(manuallyEnabled, Is.True, "La fonctionnalité peut être configurée manuellement");
            Assert.That(manualPercentage, Is.EqualTo(75), "Le pourcentage peut être configuré manuellement");

            // Restaurer l'état final de migration
            _featureFlagService.SetFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, true);
            _featureFlagService.SetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, 100);

            bool finalState = _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);
            int finalPercentage = _featureFlagService.GetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);

            Assert.That(finalState, Is.True, "État final : nouvelle méthode activée");
            Assert.That(finalPercentage, Is.EqualTo(100), "État final : rollout à 100%");

            TestContext.WriteLine("✅ Migration HistoryChanged terminée et validée");
        }

        /// <summary>
        /// Test de monitoring.
        /// </summary>
        [Test]
        [Description("MIGRATION: Flags de monitoring opérationnels")]
        public void MonitoringFlags_BasicOperations_ShouldWork()
        {
            // Vérifier que les flags de monitoring peuvent être configurés
            _featureFlagService.SetFeatureEnabled(HistoryChangedFeatureFlags.ENABLE_HISTORY_CHANGED_MONITORING, true);
            _featureFlagService.SetFeatureEnabled(HistoryChangedFeatureFlags.ENABLE_MIGRATION_LOGGING, true);

            bool monitoring = _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.ENABLE_HISTORY_CHANGED_MONITORING);
            bool logging = _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.ENABLE_MIGRATION_LOGGING);

            Assert.That(monitoring, Is.True, "Le monitoring devrait pouvoir être activé");
            Assert.That(logging, Is.True, "Le logging de migration devrait pouvoir être activé");

            // Test de désactivation
            _featureFlagService.SetFeatureEnabled(HistoryChangedFeatureFlags.ENABLE_HISTORY_CHANGED_MONITORING, false);
            bool monitoringOff = _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.ENABLE_HISTORY_CHANGED_MONITORING);
            Assert.That(monitoringOff, Is.False, "Le monitoring devrait pouvoir être désactivé");

            TestContext.WriteLine("✅ Flags de monitoring opérationnels");
        }

        /// <summary>
        /// Test de performance du service de feature flags.
        /// </summary>
        [Test]
        [Description("MIGRATION: Performance du service de feature flags")]
        public void FeatureFlagService_Performance_ShouldBeFast()
        {
            // Arrange
            const int iterations = 1000;
            _featureFlagService.SetFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, true);
            _featureFlagService.SetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, 50);

            // Act
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            for (int i = 0; i < iterations; i++)
            {
                _featureFlagService.IsFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED);
            }
            stopwatch.Stop();

            // Assert
            double averageTimeMs = (double)stopwatch.ElapsedMilliseconds / iterations;
            Assert.That(averageTimeMs, Is.LessThan(1.0),
                $"Chaque appel devrait prendre moins de 1ms (actuel: {averageTimeMs:F3}ms)");

            TestContext.WriteLine($"✅ Performance: {averageTimeMs:F3}ms par appel sur {iterations} itérations");
        }

        [TearDown]
        public void TearDown()
        {
            // Cleanup si nécessaire
        }
    }
}
