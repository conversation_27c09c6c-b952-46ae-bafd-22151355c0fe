using NUnit.Framework;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Services.Configuration;
using ClipboardPlus.UI.ViewModels.Managers.Interfaces;

namespace ClipboardPlus.Tests.Unit
{
    [TestFixture]
    public class ManagerActivationTest
    {
        private ServiceProvider _serviceProvider;

        [SetUp]
        public void Setup()
        {
            _serviceProvider = HostConfiguration.ConfigureServices();
        }

        [TearDown]
        public void TearDown()
        {
            _serviceProvider?.Dispose();
        }

        [Test]
        public void ManagersAreRegisteredInDI()
        {
            // Test que tous les 6 managers sont enregistrés dans l'injection de dépendance
            
            var historyManager = _serviceProvider.GetService<IHistoryViewModelManager>();
            var commandManager = _serviceProvider.GetService<ICommandViewModelManager>();
            var itemCreationManager = _serviceProvider.GetService<IItemCreationManager>();
            var eventManager = _serviceProvider.GetService<IEventViewModelManager>();
            var visibilityManager = _serviceProvider.GetService<IVisibilityViewModelManager>();
            var dragDropManager = _serviceProvider.GetService<IDragDropViewModelManager>();

            Assert.That(historyManager, Is.Not.Null, "HistoryViewModelManager should be registered");
            Assert.That(commandManager, Is.Not.Null, "CommandViewModelManager should be registered");
            Assert.That(itemCreationManager, Is.Not.Null, "ItemCreationManager should be registered");
            Assert.That(eventManager, Is.Not.Null, "EventViewModelManager should be registered");
            Assert.That(visibilityManager, Is.Not.Null, "VisibilityViewModelManager should be registered");
            Assert.That(dragDropManager, Is.Not.Null, "DragDropViewModelManager should be registered");

            TestContext.WriteLine("✅ Tous les 6 managers sont correctement enregistrés dans l'injection de dépendance");
        }

        [Test]
        public void ManagersAreCorrectImplementations()
        {
            // Test que les managers résolus sont bien les bonnes implémentations
            
            var historyManager = _serviceProvider.GetService<IHistoryViewModelManager>();
            var commandManager = _serviceProvider.GetService<ICommandViewModelManager>();
            var itemCreationManager = _serviceProvider.GetService<IItemCreationManager>();
            var eventManager = _serviceProvider.GetService<IEventViewModelManager>();
            var visibilityManager = _serviceProvider.GetService<IVisibilityViewModelManager>();
            var dragDropManager = _serviceProvider.GetService<IDragDropViewModelManager>();

            Assert.That(historyManager.GetType().Name, Is.EqualTo("HistoryViewModelManager"));
            Assert.That(commandManager.GetType().Name, Is.EqualTo("CommandViewModelManager"));
            Assert.That(itemCreationManager.GetType().Name, Is.EqualTo("ItemCreationManager"));
            Assert.That(eventManager.GetType().Name, Is.EqualTo("EventViewModelManager"));
            Assert.That(visibilityManager.GetType().Name, Is.EqualTo("VisibilityViewModelManager"));
            Assert.That(dragDropManager.GetType().Name, Is.EqualTo("DragDropViewModelManager"));

            TestContext.WriteLine("✅ Tous les managers sont les bonnes implémentations concrètes");
        }
    }
}
