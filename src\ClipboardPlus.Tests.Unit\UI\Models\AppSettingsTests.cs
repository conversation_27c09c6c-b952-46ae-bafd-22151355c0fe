using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.Models;
using NUnit.Framework;
using Moq;
using System;

namespace ClipboardPlus.Tests.Unit.UI.Models
{
    [TestFixture]
    public class AppSettingsTests
    {
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IVisibilityStateManager> _mockVisibilityStateManager = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private AppSettings _appSettings = null!;

        [SetUp]
        public void TestInitialize()
        {
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockVisibilityStateManager = new Mock<IVisibilityStateManager>();
            _mockLoggingService = new Mock<ILoggingService>();
            
            // Configurer les valeurs initiales
            _mockSettingsManager.Setup(m => m.HideItemTitle).Returns(false);
            _mockSettingsManager.Setup(m => m.HideTimestamp).Returns(false);
            
            _appSettings = new AppSettings(
                _mockSettingsManager.Object,
                _mockVisibilityStateManager.Object,
                _mockLoggingService.Object);
        }

        [Test]
        public void Constructor_InitializesPropertiesFromSettingsManager()
        {
            // Arrange
            bool expectedHideItemTitle = true;
            bool expectedHideTimestamp = true;
            
            _mockSettingsManager.Setup(m => m.HideItemTitle).Returns(expectedHideItemTitle);
            _mockSettingsManager.Setup(m => m.HideTimestamp).Returns(expectedHideTimestamp);
            
            // Act
            var appSettings = new AppSettings(
                _mockSettingsManager.Object,
                _mockVisibilityStateManager.Object,
                _mockLoggingService.Object);
            
            // Assert
            Assert.AreEqual(expectedHideItemTitle, appSettings.HideItemTitle);
            Assert.AreEqual(expectedHideTimestamp, appSettings.HideTimestamp);
        }

        [Test]
        public void HideItemTitle_UpdatesVisibilityRuleService_WhenChanged()
        {
            // Arrange
            bool newValue = true;
            
            // Act
            _appSettings.HideItemTitle = newValue;
            
            // Assert
            _mockVisibilityStateManager.Verify(m => m.UpdateTitleVisibilityFromSettings(newValue), Times.Once);
        }

        [Test]
        public void HideTimestamp_UpdatesSettingsManager_WhenChanged()
        {
            // Arrange
            bool newValue = true;
            
            // Act
            _appSettings.HideTimestamp = newValue;
            
            // Assert
            _mockSettingsManager.VerifySet(m => m.HideTimestamp = newValue, Times.Once);
        }

        [Test]
        public void HideTimestamp_UpdatesFromSettingsManager_WhenSettingChanged()
        {
            // Arrange
            bool initialValue = false;
            bool newValue = true;

            _mockSettingsManager.Setup(m => m.HideTimestamp).Returns(initialValue);

            var appSettings = new AppSettings(
                _mockSettingsManager.Object,
                _mockVisibilityStateManager.Object,
                _mockLoggingService.Object);

            Assert.AreEqual(initialValue, appSettings.HideTimestamp);

            // Simuler un changement de paramètre
            bool propertyChangedRaised = false;
            appSettings.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(AppSettings.HideTimestamp))
                {
                    propertyChangedRaised = true;
                }
            };

            // Mettre à jour la valeur dans le mock
            _mockSettingsManager.Setup(m => m.HideTimestamp).Returns(newValue);

            // Act
            _mockSettingsManager.Raise(m => m.SettingChanged += null, "HideTimestamp");

            // Assert
            Assert.AreEqual(newValue, appSettings.HideTimestamp);
            Assert.IsTrue(propertyChangedRaised, "PropertyChanged event should have been raised");
        }
    }
} 