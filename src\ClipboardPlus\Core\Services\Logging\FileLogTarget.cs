using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ClipboardPlus.Core.Services.Logging
{
    /// <summary>
    /// Cible de log pour l'écriture dans un fichier avec buffer et gestion d'erreurs.
    /// Implémente la logique de buffer et de flush de l'ancien WriteToLog.
    /// </summary>
    public class FileLogTarget : ILogTarget, IDisposable
    {
        private readonly string _logFilePath;
        private readonly List<string> _buffer;
        private readonly object _lockObject;
        private DateTime _lastFlushTime;
        private int _pendingEntries;
        private bool _disposed;
        
        private const int MAX_BUFFER_SIZE = 100;
        
        /// <summary>
        /// Initialise une nouvelle instance de FileLogTarget.
        /// </summary>
        /// <param name="logFilePath">Le chemin du fichier de log</param>
        public FileLogTarget(string logFilePath)
        {
            _logFilePath = logFilePath ?? throw new ArgumentNullException(nameof(logFilePath));
            _buffer = new List<string>();
            _lockObject = new object();
            _lastFlushTime = DateTime.Now;
            _pendingEntries = 0;
            _disposed = false;
        }
        
        /// <summary>
        /// Écrit une entrée de log dans le buffer.
        /// </summary>
        /// <param name="entry">L'entrée de log à écrire</param>
        public void Write(LogEntry entry)
        {
            if (_disposed) return;
            
            try
            {
                lock (_lockObject)
                {
                    // Ajouter les lignes formatées au buffer
                    var formattedLines = entry.ToFormattedLines();
                    _buffer.AddRange(formattedLines);
                    _pendingEntries += formattedLines.Length;
                    
                    // Vérifier si un flush est nécessaire
                    if (ShouldFlush(entry))
                    {
                        FlushInternal();
                    }
                }
            }
            catch (Exception ex)
            {
                // Gestion d'erreur avec fallback comme dans l'original
                HandleWriteError(ex, entry);
            }
        }
        
        /// <summary>
        /// Écrit plusieurs entrées en une seule opération (optimisation).
        /// </summary>
        /// <param name="entries">Les entrées de log à écrire</param>
        public void WriteBatch(IEnumerable<LogEntry> entries)
        {
            if (_disposed) return;
            
            try
            {
                lock (_lockObject)
                {
                    foreach (var entry in entries)
                    {
                        var formattedLines = entry.ToFormattedLines();
                        _buffer.AddRange(formattedLines);
                        _pendingEntries += formattedLines.Length;
                    }
                    
                    // Flush après le batch
                    FlushInternal();
                }
            }
            catch (Exception ex)
            {
                // Gestion d'erreur globale pour le batch
                HandleBatchWriteError(ex, entries);
            }
        }
        
        /// <summary>
        /// Force l'écriture immédiate du buffer.
        /// </summary>
        public void Flush()
        {
            if (_disposed) return;
            
            try
            {
                lock (_lockObject)
                {
                    FlushInternal();
                }
            }
            catch (Exception ex)
            {
                HandleFlushError(ex);
            }
        }
        
        /// <summary>
        /// Détermine si un flush est nécessaire selon les conditions de l'original.
        /// </summary>
        /// <param name="entry">L'entrée de log qui vient d'être ajoutée</param>
        /// <returns>True si un flush est nécessaire</returns>
        private bool ShouldFlush(LogEntry entry)
        {
            // Condition 1: Buffer plein
            if (_pendingEntries >= MAX_BUFFER_SIZE)
            {
                return true;
            }
            
            // Condition 2: Niveau critique nécessitant un flush immédiat
            if (entry.RequiresImmediateFlush)
            {
                return true;
            }
            
            // Condition 3: Timeout (5 secondes depuis le dernier flush)
            if ((DateTime.Now - _lastFlushTime).TotalSeconds >= 5)
            {
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// Effectue le flush interne (doit être appelé dans un lock).
        /// </summary>
        private void FlushInternal()
        {
            if (_buffer.Count == 0) return;
            
            try
            {
                // Écrire toutes les lignes du buffer
                File.AppendAllLines(_logFilePath, _buffer, Encoding.UTF8);
                
                // Nettoyer le buffer
                _buffer.Clear();
                _pendingEntries = 0;
                _lastFlushTime = DateTime.Now;
                
                // Délai minimal pour s'assurer de l'écriture (comme dans l'original)
                System.Threading.Thread.Sleep(5);
            }
            catch (Exception ex)
            {
                // En cas d'erreur, essayer le mécanisme de récupération
                CreateRecoveryFile(ex);
            }
        }
        
        /// <summary>
        /// Gère les erreurs d'écriture avec mécanisme de récupération.
        /// </summary>
        /// <param name="ex">L'exception qui s'est produite</param>
        /// <param name="entry">L'entrée qui causait l'erreur</param>
        private void HandleWriteError(Exception ex, LogEntry entry)
        {
            try
            {
                // Créer un fichier de récupération
                CreateRecoveryFile(ex);
                
                // Logs de fallback comme dans l'original
                System.Diagnostics.Debug.WriteLine($"Erreur WriteToLog: {ex.Message}");
                Console.WriteLine($"Erreur WriteToLog: {ex.Message}");
            }
            catch
            {
                // Si même le fallback échoue, ignorer silencieusement
            }
        }
        
        /// <summary>
        /// Gère les erreurs d'écriture en batch.
        /// </summary>
        private void HandleBatchWriteError(Exception ex, IEnumerable<LogEntry> entries)
        {
            HandleWriteError(ex, entries.FirstOrDefault() ?? new LogEntry("ERROR", "Batch write error", DateTime.Now, "0", "Unknown", "Unknown"));
        }
        
        /// <summary>
        /// Gère les erreurs de flush.
        /// </summary>
        private void HandleFlushError(Exception ex)
        {
            HandleWriteError(ex, new LogEntry("ERROR", "Flush error", DateTime.Now, "0", "Unknown", "Unknown"));
        }
        
        /// <summary>
        /// Crée un fichier de récupération en cas d'erreur d'écriture.
        /// </summary>
        /// <param name="originalException">L'exception originale</param>
        private void CreateRecoveryFile(Exception originalException)
        {
            try
            {
                var recoveryPath = Path.Combine(
                    Path.GetTempPath(),
                    $"clipboard_plus_recovery_{DateTime.Now:yyyyMMdd_HHmmss}.log"
                );
                
                var recoveryContent = new List<string>
                {
                    $"=== FICHIER DE RÉCUPÉRATION CLIPBOARD PLUS ===",
                    $"Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    $"Erreur originale: {originalException.Message}",
                    $"Fichier original: {_logFilePath}",
                    $"",
                    $"=== CONTENU DU BUFFER ==="
                };
                
                recoveryContent.AddRange(_buffer);
                
                File.WriteAllLines(recoveryPath, recoveryContent, Encoding.UTF8);
                
                Console.WriteLine($"Logs écrits dans le fichier de récupération: {recoveryPath}");
                Console.WriteLine($"Erreur originale: {originalException.Message}");
            }
            catch
            {
                // Si même la récupération échoue, ignorer
            }
        }
        
        /// <summary>
        /// Libère les ressources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    Flush(); // Flush final
                }
                catch
                {
                    // Ignorer les erreurs lors du dispose
                }
                
                _disposed = true;
            }
        }
    }
}
