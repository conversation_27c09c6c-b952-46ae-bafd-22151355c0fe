using System;
using System.Linq;
using System.Text;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using ClipboardPlus.Services.ContentHandlers;

namespace ClipboardPlus.Tests.Unit.Services
{
    /// <summary>
    /// Tests unitaires pour ContentPreviewLoader.
    /// Valide l'orchestration principale du chargement de contenu.
    /// </summary>
    [TestFixture]
    public class ContentPreviewLoaderTests
    {
        private ContentPreviewLoader _loader;
        private IContentHandlerFactory _factory;

        [SetUp]
        public void Setup()
        {
            // Créer une factory avec des handlers réels pour les tests
            var handlers = new IContentHandler[]
            {
                new TextContentHandler(),
                new HtmlContentHandler(),
                new RtfContentHandler()
            };

            _factory = new ContentHandlerFactory(handlers);
            _loader = new ContentPreviewLoader(_factory);
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithNullFactory_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ContentPreviewLoader(null!));
        }

        [Test]
        public void Constructor_WithValidFactory_InitializesCorrectly()
        {
            // Act & Assert - Constructor should not throw
            Assert.DoesNotThrow(() => new ContentPreviewLoader(_factory));
        }

        #endregion

        #region LoadPreviewContent Tests

        [Test]
        public void LoadPreviewContent_WithTextItem_ReturnsProcessedContent()
        {
            // Arrange
            string originalText = "Test text content";
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(originalText),
                TextPreview = "Preview"
            };

            // Act
            var result = _loader.LoadPreviewContent(item);

            // Assert
            Assert.AreEqual(originalText, result);
        }

        [Test]
        public void LoadPreviewContent_WithHtmlItem_ReturnsProcessedContent()
        {
            // Arrange
            string htmlContent = "<html><body>Test HTML</body></html>";
            var item = new ClipboardItem
            {
                Id = 2,
                DataType = ClipboardDataType.Html,
                RawData = Encoding.UTF8.GetBytes(htmlContent),
                TextPreview = "HTML Preview"
            };

            // Act
            var result = _loader.LoadPreviewContent(item);

            // Assert
            Assert.AreEqual(htmlContent, result);
        }

        [Test]
        public void LoadPreviewContent_WithRtfItem_ReturnsProcessedContent()
        {
            // Arrange
            string rtfContent = @"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Hello, \b World\b0!}";
            var item = new ClipboardItem
            {
                Id = 3,
                DataType = ClipboardDataType.Rtf,
                RawData = Encoding.UTF8.GetBytes(rtfContent),
                TextPreview = "RTF Preview"
            };

            // Act
            var result = _loader.LoadPreviewContent(item);

            // Assert
            Assert.AreEqual(rtfContent, result);
        }

        [Test]
        public void LoadPreviewContent_WithUnsupportedType_ReturnsUnsupportedMessage()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 4,
                DataType = ClipboardDataType.Image, // Non supporté dans notre setup de test
                TextPreview = "Image Preview"
            };

            // Act
            var result = _loader.LoadPreviewContent(item);

            // Assert
            Assert.AreEqual("Prévisualisation non disponible pour ce type de contenu.", result);
        }

        [Test]
        public void LoadPreviewContent_WithNullItem_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _loader.LoadPreviewContent(null!));
        }

        [Test]
        public void LoadPreviewContent_WithItemWithoutData_ReturnsFallbackContent()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 5,
                DataType = ClipboardDataType.Text,
                RawData = null,
                TextPreview = "Fallback text preview"
            };

            // Act
            var result = _loader.LoadPreviewContent(item);

            // Assert
            Assert.AreEqual("Fallback text preview", result);
        }

        [Test]
        public void LoadPreviewContent_WithItemWithoutDataAndPreview_ReturnsDefaultMessage()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 6,
                DataType = ClipboardDataType.Text,
                RawData = null,
                TextPreview = null
            };

            // Act
            var result = _loader.LoadPreviewContent(item);

            // Assert
            Assert.AreEqual("Contenu texte non disponible.", result);
        }

        #endregion

        #region IsDataTypeSupported Tests

        [Test]
        public void IsDataTypeSupported_WithSupportedTypes_ReturnsTrue()
        {
            // Act & Assert
            Assert.IsTrue(_loader.IsDataTypeSupported(ClipboardDataType.Text));
            Assert.IsTrue(_loader.IsDataTypeSupported(ClipboardDataType.Html));
            Assert.IsTrue(_loader.IsDataTypeSupported(ClipboardDataType.Rtf));
        }

        [Test]
        public void IsDataTypeSupported_WithUnsupportedTypes_ReturnsFalse()
        {
            // Act & Assert
            Assert.IsFalse(_loader.IsDataTypeSupported(ClipboardDataType.Image));
            Assert.IsFalse(_loader.IsDataTypeSupported(ClipboardDataType.FilePath));
        }

        [Test]
        public void IsDataTypeSupported_WithUnknownType_ReturnsFalse()
        {
            // Act
            bool result = _loader.IsDataTypeSupported((ClipboardDataType)999);

            // Assert
            Assert.IsFalse(result);
        }

        #endregion

        #region GetSupportedDataTypes Tests

        [Test]
        public void GetSupportedDataTypes_ReturnsCorrectTypes()
        {
            // Act
            var result = _loader.GetSupportedDataTypes().ToList();

            // Assert
            Assert.AreEqual(3, result.Count);
            Assert.Contains(ClipboardDataType.Text, result);
            Assert.Contains(ClipboardDataType.Html, result);
            Assert.Contains(ClipboardDataType.Rtf, result);
        }

        [Test]
        public void GetSupportedDataTypes_DoesNotReturnUnsupportedTypes()
        {
            // Act
            var result = _loader.GetSupportedDataTypes().ToList();

            // Assert
            Assert.IsFalse(result.Contains(ClipboardDataType.Image));
            Assert.IsFalse(result.Contains(ClipboardDataType.FilePath));
        }

        [Test]
        public void GetSupportedDataTypes_ReturnsConsistentResults()
        {
            // Act - Appeler plusieurs fois
            var result1 = _loader.GetSupportedDataTypes().ToList();
            var result2 = _loader.GetSupportedDataTypes().ToList();

            // Assert - Les résultats doivent être identiques
            CollectionAssert.AreEquivalent(result1, result2);
        }

        #endregion

        #region Integration Tests

        [Test]
        public void LoadPreviewContent_WithMultipleItemTypes_HandlesAllCorrectly()
        {
            // Arrange
            var items = new[]
            {
                new ClipboardItem
                {
                    Id = 1,
                    DataType = ClipboardDataType.Text,
                    RawData = Encoding.UTF8.GetBytes("Text content"),
                    TextPreview = "Text preview"
                },
                new ClipboardItem
                {
                    Id = 2,
                    DataType = ClipboardDataType.Html,
                    RawData = Encoding.UTF8.GetBytes("<html>HTML content</html>"),
                    TextPreview = "HTML preview"
                },
                new ClipboardItem
                {
                    Id = 3,
                    DataType = ClipboardDataType.Rtf,
                    RawData = Encoding.UTF8.GetBytes(@"{\rtf1 RTF content}"),
                    TextPreview = "RTF preview"
                }
            };

            // Act & Assert
            foreach (var item in items)
            {
                var result = _loader.LoadPreviewContent(item);
                Assert.IsNotNull(result, $"Le résultat ne doit pas être null pour le type {item.DataType}");
                Assert.IsInstanceOf<string>(result, $"Le résultat doit être une chaîne pour le type {item.DataType}");
            }
        }

        [Test]
        public void LoadPreviewContent_WithLargeContent_HandlesEfficiently()
        {
            // Arrange
            string largeText = new string('A', 100000); // 100KB de texte
            var item = new ClipboardItem
            {
                Id = 7,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(largeText),
                TextPreview = "Large text preview"
            };

            // Act
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = _loader.LoadPreviewContent(item);
            stopwatch.Stop();

            // Assert
            Assert.AreEqual(largeText, result);
            Assert.Less(stopwatch.ElapsedMilliseconds, 100, "Le traitement de contenu volumineux doit être rapide");
        }

        [Test]
        public void LoadPreviewContent_WithSpecialCharacters_PreservesContent()
        {
            // Arrange
            string specialText = "Émojis: 🎉🚀💻 Symboles: ©®™ Accents: àáâãäåæçèéêë";
            var item = new ClipboardItem
            {
                Id = 8,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(specialText),
                TextPreview = "Special chars preview"
            };

            // Act
            var result = _loader.LoadPreviewContent(item);

            // Assert
            Assert.AreEqual(specialText, result);
        }

        #endregion

        #region Error Handling Tests

        [Test]
        public void LoadPreviewContent_WithCorruptedData_ReturnsGracefulFallback()
        {
            // Arrange - Item avec des données corrompues mais TextPreview valide
            var item = new ClipboardItem
            {
                Id = 9,
                DataType = ClipboardDataType.Text,
                RawData = new byte[] { 0xFF, 0xFE, 0xFD }, // Données invalides
                TextPreview = "Fallback content"
            };

            // Act
            var result = _loader.LoadPreviewContent(item);

            // Assert
            // Le handler devrait gérer gracieusement les données corrompues
            Assert.IsNotNull(result);
            Assert.IsInstanceOf<string>(result);
        }

        #endregion
    }
}
