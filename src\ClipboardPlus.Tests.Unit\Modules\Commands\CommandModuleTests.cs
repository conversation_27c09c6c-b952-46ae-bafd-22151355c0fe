using System;
using System.Threading.Tasks;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Modules.Commands;
using ClipboardPlus.Modules.Core.Events;
using Prism.Events;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Modules.Commands
{
    /// <summary>
    /// Tests unitaires pour le CommandModule.
    /// 
    /// Ces tests valident le comportement du module de commandes,
    /// incluant l'enregistrement, l'exécution et la gestion des erreurs.
    /// </summary>
    [TestFixture]
    public class CommandModuleTests
    {
        private Mock<IClipboardHistoryManager> _mockHistoryManager;
        private Mock<IClipboardInteractionService> _mockClipboardService;
        private Mock<ILoggingService> _mockLoggingService;
        private Mock<IEventAggregator> _mockEventAggregator;
        private CommandModule _commandModule;
        private ClipboardItem _testItem;

        [SetUp]
        public void SetUp()
        {
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockClipboardService = new Mock<IClipboardInteractionService>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockEventAggregator = new Mock<IEventAggregator>();

            // Configuration du mock EventAggregator pour retourner des mocks d'événements
            var mockModuleStateEvent = new Mock<ModuleStateChangedPrismEvent>();
            var mockCommandEvent = new Mock<CommandModulePrismEvent>();
            _mockEventAggregator.Setup(ea => ea.GetEvent<ModuleStateChangedPrismEvent>())
                               .Returns(mockModuleStateEvent.Object);
            _mockEventAggregator.Setup(ea => ea.GetEvent<CommandModulePrismEvent>())
                               .Returns(mockCommandEvent.Object);

            _commandModule = new CommandModule(
                _mockHistoryManager.Object,
                _mockClipboardService.Object,
                _mockLoggingService.Object,
                _mockEventAggregator.Object);

            _testItem = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.Now,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                TextPreview = "Test content"
            };
        }

        [TearDown]
        public void TearDown()
        {
            _commandModule?.Dispose();
        }

        #region Tests d'initialisation

        [Test]
        public async Task InitializeAsync_ShouldInitializeSuccessfully()
        {
            // Act
            await _commandModule.InitializeAsync();

            // Assert
            Assert.That(_commandModule.State, Is.EqualTo(ClipboardPlus.Modules.Core.ModuleState.Initialized));
            Assert.That(_commandModule.CommandRegistry.Commands.Count, Is.GreaterThan(0));
            _mockLoggingService.Verify(l => l.LogInfo(It.IsAny<string>()), Times.AtLeastOnce);
        }

        [Test]
        public async Task StartAsync_ShouldStartSuccessfully()
        {
            // Arrange
            await _commandModule.InitializeAsync();

            // Act
            await _commandModule.StartAsync();

            // Assert
            Assert.That(_commandModule.State, Is.EqualTo(ClipboardPlus.Modules.Core.ModuleState.Running));
            _mockEventAggregator.Verify(e => e.GetEvent<ModuleStateChangedPrismEvent>().Publish(It.IsAny<ModuleStateChangedEvent>()), Times.AtLeastOnce);
        }

        #endregion

        #region Tests du registre de commandes

        [Test]
        public async Task CommandRegistry_ShouldContainDefaultCommands()
        {
            // Arrange
            await _commandModule.InitializeAsync();

            // Act & Assert
            Assert.That(_commandModule.GetCommand("DeleteSelectedItem"), Is.Not.Null);
            Assert.That(_commandModule.GetCommand("ClearHistory"), Is.Not.Null);
            Assert.That(_commandModule.GetCommand("RenameItem"), Is.Not.Null);
            Assert.That(_commandModule.GetCommand("CopyToClipboard"), Is.Not.Null);
            Assert.That(_commandModule.GetCommand("FinalizeAndSaveNewItem"), Is.Not.Null);
            Assert.That(_commandModule.GetCommand("CancelNewItem"), Is.Not.Null);
            Assert.That(_commandModule.GetCommand("ConfirmRename"), Is.Not.Null);
            Assert.That(_commandModule.GetCommand("CancelRename"), Is.Not.Null);
        }

        [Test]
        public async Task RegisterCommand_ShouldAddCommandToRegistry()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            var mockCommand = new Mock<System.Windows.Input.ICommand>();

            // Act
            _commandModule.RegisterCommand("TestCommand", mockCommand.Object);

            // Assert
            Assert.That(_commandModule.GetCommand("TestCommand"), Is.EqualTo(mockCommand.Object));
            Assert.That(_commandModule.CommandRegistry.Contains("TestCommand"), Is.True);
        }

        [Test]
        public async Task UnregisterCommand_ShouldRemoveCommandFromRegistry()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            var mockCommand = new Mock<System.Windows.Input.ICommand>();
            _commandModule.RegisterCommand("TestCommand", mockCommand.Object);

            // Act
            _commandModule.UnregisterCommand("TestCommand");

            // Assert
            Assert.That(_commandModule.GetCommand("TestCommand"), Is.Null);
            Assert.That(_commandModule.CommandRegistry.Contains("TestCommand"), Is.False);
        }

        #endregion

        #region Tests du contexte de commandes

        [Test]
        public async Task CommandContext_ShouldBeAccessible()
        {
            // Arrange
            await _commandModule.InitializeAsync();

            // Act & Assert
            Assert.That(_commandModule.CommandContext, Is.Not.Null);
            Assert.That(_commandModule.CommandContext.SelectedItem, Is.Null);
            Assert.That(_commandModule.CommandContext.IsCreatingNewItem, Is.False);
            Assert.That(_commandModule.CommandContext.IsRenamingItem, Is.False);
        }

        [Test]
        public async Task CommandContext_ShouldSupportProperties()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            var context = _commandModule.CommandContext;

            // Act
            context.SetProperty("TestKey", "TestValue");
            context.SetProperty("TestNumber", 42);

            // Assert
            Assert.That(context.GetProperty<string>("TestKey"), Is.EqualTo("TestValue"));
            Assert.That(context.GetProperty<int>("TestNumber"), Is.EqualTo(42));
            Assert.That(context.GetProperty<string>("NonExistent"), Is.Null);
        }

        #endregion

        #region Tests des commandes principales

        [Test]
        public async Task DeleteSelectedItemCommand_WithSelectedItem_ShouldExecute()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();
            _commandModule.CommandContext.SelectedItem = _testItem;
            _mockHistoryManager.Setup(m => m.DeleteItemAsync(_testItem.Id)).ReturnsAsync(true);

            // Act
            var canExecute = _commandModule.DeleteSelectedItemCommand.CanExecute(null);

            // Assert
            Assert.That(canExecute, Is.True);
        }

        [Test]
        public async Task DeleteSelectedItemCommand_WithoutSelectedItem_ShouldNotExecute()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();
            _commandModule.CommandContext.SelectedItem = null;

            // Act
            var canExecute = _commandModule.DeleteSelectedItemCommand.CanExecute(null);

            // Assert
            Assert.That(canExecute, Is.False);
        }

        [Test]
        public async Task ClearHistoryCommand_ShouldExecute()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            // Act
            var canExecute = _commandModule.ClearHistoryCommand.CanExecute(null);

            // Assert
            Assert.That(canExecute, Is.True);
            _mockHistoryManager.Setup(m => m.ClearHistoryAsync(true)).Returns(Task.CompletedTask);
        }

        [Test]
        public async Task CopyToClipboardCommand_WithValidItem_ShouldExecute()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            // Act
            var canExecute = _commandModule.CopyToClipboardCommand.CanExecute(_testItem);

            // Assert
            Assert.That(canExecute, Is.True);
            _mockClipboardService.Setup(s => s.SetClipboardContentAsync(It.IsAny<string>()))
                .ReturnsAsync(true);
        }

        [Test]
        public async Task FinalizeAndSaveNewItemCommand_WithContent_ShouldExecute()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();
            _commandModule.CommandContext.IsCreatingNewItem = true;
            _commandModule.CommandContext.NewItemContent = "Test content";

            // Act
            var canExecute = _commandModule.FinalizeAndSaveNewItemCommand.CanExecute(null);

            // Assert
            Assert.That(canExecute, Is.True);
            _mockHistoryManager.Setup(m => m.AddItemAsync(It.IsAny<ClipboardItem>()))
                .ReturnsAsync(1L);
        }

        [Test]
        public async Task FinalizeAndSaveNewItemCommand_WithoutContent_ShouldNotExecute()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();
            _commandModule.CommandContext.IsCreatingNewItem = true;
            _commandModule.CommandContext.NewItemContent = null;

            // Act
            var canExecute = _commandModule.FinalizeAndSaveNewItemCommand.CanExecute(null);

            // Assert
            Assert.That(canExecute, Is.False);
        }

        #endregion

        #region Tests d'exécution de commandes

        [Test]
        public async Task ExecuteCommandAsync_WithValidCommand_ShouldExecute()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();
            var commandExecuted = false;
            
            _commandModule.CommandExecuted += (sender, args) =>
            {
                commandExecuted = true;
                Assert.That(args.CommandName, Is.EqualTo("ClearHistory"));
            };

            _mockHistoryManager.Setup(m => m.ClearHistoryAsync(true)).Returns(Task.CompletedTask);

            // Act
            await _commandModule.ExecuteCommandAsync("ClearHistory");

            // Assert
            Assert.That(commandExecuted, Is.True);
            _mockHistoryManager.Verify(m => m.ClearHistoryAsync(true), Times.Once);
        }

        [Test]
        public async Task ExecuteCommandAsync_WithInvalidCommand_ShouldThrowException()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            // Act & Assert
            var ex = Assert.ThrowsAsync<InvalidOperationException>(
                async () => await _commandModule.ExecuteCommandAsync("NonExistentCommand"));
            
            Assert.That(ex.Message, Does.Contain("Command 'NonExistentCommand' not found"));
        }

        [Test]
        public async Task CanExecuteCommand_WithValidCommand_ShouldReturnCorrectResult()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            // Act
            var canExecute = _commandModule.CanExecuteCommand("ClearHistory");

            // Assert
            Assert.That(canExecute, Is.True);
        }

        [Test]
        public async Task CanExecuteCommand_WithInvalidCommand_ShouldReturnFalse()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            // Act
            var canExecute = _commandModule.CanExecuteCommand("NonExistentCommand");

            // Assert
            Assert.That(canExecute, Is.False);
        }

        #endregion

        #region Tests d'événements

        [Test]
        public async Task CommandExecution_ShouldTriggerEvents()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();
            
            var executingTriggered = false;
            var executedTriggered = false;
            
            _commandModule.CommandExecuting += (sender, args) =>
            {
                executingTriggered = true;
                Assert.That(args.CommandName, Is.EqualTo("ClearHistory"));
            };
            
            _commandModule.CommandExecuted += (sender, args) =>
            {
                executedTriggered = true;
                Assert.That(args.CommandName, Is.EqualTo("ClearHistory"));
            };

            _mockHistoryManager.Setup(m => m.ClearHistoryAsync(true)).Returns(Task.CompletedTask);

            // Act
            await _commandModule.ExecuteCommandAsync("ClearHistory");

            // Assert
            Assert.That(executingTriggered, Is.True);
            Assert.That(executedTriggered, Is.True);
        }

        #endregion

        #region Tests d'erreurs

        [Test]
        public void Constructor_WithNullHistoryManager_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new CommandModule(null, _mockClipboardService.Object, _mockLoggingService.Object, _mockEventAggregator.Object));
        }

        [Test]
        public void Constructor_WithNullClipboardService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new CommandModule(_mockHistoryManager.Object, null, _mockLoggingService.Object, _mockEventAggregator.Object));
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new CommandModule(_mockHistoryManager.Object, _mockClipboardService.Object, null, _mockEventAggregator.Object));
        }

        [Test]
        public void Constructor_WithNullEventBus_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new CommandModule(_mockHistoryManager.Object, _mockClipboardService.Object, _mockLoggingService.Object, null));
        }

        #endregion

        #region Tests de couverture fonctionnelle des commandes

        [Test]
        public async Task DeleteSelectedItemCommand_WithSelectedItem_ShouldCallDeleteItemAsync()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            _commandModule.CommandContext.SelectedItem = _testItem;
            _mockHistoryManager.Setup(m => m.DeleteItemAsync(_testItem.Id))
                .ReturnsAsync(true);

            // Act
            _commandModule.DeleteSelectedItemCommand.Execute(null);
            await Task.Delay(100); // Attendre l'exécution asynchrone

            // Assert
            _mockHistoryManager.Verify(m => m.DeleteItemAsync(_testItem.Id), Times.Once);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Item deleted"))), Times.Once);
        }

        [Test]
        public async Task ClearHistoryCommand_ShouldPreservePinnedItems()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            // Act
            _commandModule.ClearHistoryCommand.Execute(null);
            await Task.Delay(100); // Attendre l'exécution asynchrone

            // Assert
            _mockHistoryManager.Verify(m => m.ClearHistoryAsync(true), Times.Once);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("History cleared"))), Times.Once);
            Assert.That(_commandModule.CommandContext.SelectedItem, Is.Null);
        }

        [Test]
        public void RenameItemCommand_WithValidItem_ShouldStartRenaming()
        {
            // Arrange
            _commandModule.InitializeAsync().Wait();
            _commandModule.StartAsync().Wait();

            // Act
            _commandModule.RenameItemCommand.Execute(_testItem);

            // Assert
            Assert.That(_commandModule.CommandContext.IsRenamingItem, Is.True);
            Assert.That(_commandModule.CommandContext.RenamingItemId, Is.EqualTo(_testItem.Id));
            Assert.That(_commandModule.CommandContext.SelectedItem, Is.EqualTo(_testItem));
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Started renaming"))), Times.Once);
        }

        [Test]
        public async Task CopyToClipboardCommand_WithValidItem_ShouldCopyToClipboard()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            // Act
            _commandModule.CopyToClipboardCommand.Execute(_testItem);
            await Task.Delay(100); // Attendre l'exécution asynchrone

            // Assert
            var expectedText = System.Text.Encoding.UTF8.GetString(_testItem.RawData);
            _mockClipboardService.Verify(s => s.SetClipboardContentAsync(expectedText), Times.Once);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Item copied to clipboard"))), Times.Once);
        }

        // Test supprimé car PasteSelectedItemCommand utilise SendKeys.SendWait() qui cause un crash dans les tests

        [Test]
        public async Task TogglePinCommand_WithUnpinnedItem_ShouldPinItem()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            _testItem.IsPinned = false;
            _mockHistoryManager.Setup(m => m.UpdateItemAsync(It.IsAny<ClipboardItem>()))
                .Returns(Task.CompletedTask);

            // Act
            _commandModule.TogglePinCommand.Execute(_testItem);
            await Task.Delay(100); // Attendre l'exécution asynchrone

            // Assert
            _mockHistoryManager.Verify(m => m.UpdateItemAsync(It.Is<ClipboardItem>(item =>
                item.Id == _testItem.Id && item.IsPinned == true)), Times.Once);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("DÉBUT") && s.Contains("TogglePin"))), Times.Once);
        }

        [Test]
        public async Task FinalizeAndSaveNewItemCommand_WithValidContent_ShouldCreateItem()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            _commandModule.CommandContext.NewItemContent = "Test content";
            _mockHistoryManager.Setup(m => m.AddItemAsync(It.IsAny<ClipboardItem>()))
                .ReturnsAsync(1L);

            // Act
            _commandModule.FinalizeAndSaveNewItemCommand.Execute(null);
            await Task.Delay(100); // Attendre l'exécution asynchrone

            // Assert
            _mockHistoryManager.Verify(m => m.AddItemAsync(It.Is<ClipboardItem>(item =>
                item.DataType == ClipboardDataType.Text &&
                System.Text.Encoding.UTF8.GetString(item.RawData) == "Test content")), Times.Once);
            Assert.That(_commandModule.CommandContext.IsCreatingNewItem, Is.False);
            Assert.That(_commandModule.CommandContext.NewItemContent, Is.Null);
        }

        [Test]
        public void CancelNewItemCommand_ShouldCancelCreation()
        {
            // Arrange
            _commandModule.InitializeAsync().Wait();
            _commandModule.StartAsync().Wait();
            _commandModule.CommandContext.IsCreatingNewItem = true;
            _commandModule.CommandContext.NewItemContent = "Test content";

            // Act
            _commandModule.CancelNewItemCommand.Execute(null);

            // Assert
            Assert.That(_commandModule.CommandContext.IsCreatingNewItem, Is.False);
            Assert.That(_commandModule.CommandContext.NewItemContent, Is.Null);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("New item creation cancelled"))), Times.Once);
        }

        [Test]
        public async Task ConfirmRenameCommand_WithValidContext_ShouldConfirmRename()
        {
            // Arrange
            await _commandModule.InitializeAsync();
            await _commandModule.StartAsync();

            _commandModule.CommandContext.IsRenamingItem = true;
            _commandModule.CommandContext.RenamingItemId = _testItem.Id;
            _commandModule.CommandContext.SelectedItem = _testItem;

            // Configuration du mock pour AddItemAsync (utilisé par ConfirmRename)
            _mockHistoryManager.Setup(m => m.AddItemAsync(It.IsAny<ClipboardItem>()))
                .ReturnsAsync(123L);

            // Act
            _commandModule.ConfirmRenameCommand.Execute(null);
            await Task.Delay(100); // Attendre l'exécution asynchrone

            // Assert
            Assert.That(_commandModule.CommandContext.IsRenamingItem, Is.False);
            Assert.That(_commandModule.CommandContext.RenamingItemId, Is.Null);
            // Le message réel est "Item renamed: {item.Id}"
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Item renamed"))), Times.Once);
        }

        [Test]
        public void CancelRenameCommand_ShouldCancelRename()
        {
            // Arrange
            _commandModule.InitializeAsync().Wait();
            _commandModule.StartAsync().Wait();
            _commandModule.CommandContext.IsRenamingItem = true;
            _commandModule.CommandContext.RenamingItemId = _testItem.Id;

            // Act
            _commandModule.CancelRenameCommand.Execute(null);

            // Assert
            Assert.That(_commandModule.CommandContext.IsRenamingItem, Is.False);
            Assert.That(_commandModule.CommandContext.RenamingItemId, Is.Null);
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Rename operation cancelled"))), Times.Once);
        }

        #endregion
    }
}
