using System;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.UI;
using WpfApplication = System.Windows.Application;

namespace ClipboardPlus.Services.UI
{
    /// <summary>
    /// Implémentation du service de validation du conteneur de services.
    /// Responsabilité unique : Valider l'état et la disponibilité du conteneur de services.
    /// </summary>
    public class ServiceContainerValidator : IServiceContainerValidator
    {
        private readonly ILoggingService? _loggingService;

        public ServiceContainerValidator(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Valide que le conteneur de services est correctement initialisé.
        /// </summary>
        public ClipboardPlus.Core.Services.UI.ValidationResult ValidateServiceContainer()
        {
            try
            {
                // Vérifier que l'application WPF est disponible
                if (WpfApplication.Current == null)
                {
                    var message = "Application WPF non disponible";
                    _loggingService?.LogError($"ServiceContainerValidator: {message}");
                    return ClipboardPlus.Core.Services.UI.ValidationResult.CreateFailure(message);
                }

                // Vérifier que l'application est de type App
                if (WpfApplication.Current is not App app)
                {
                    var message = "L'application n'est pas de type App";
                    _loggingService?.LogError($"ServiceContainerValidator: {message}");
                    return ClipboardPlus.Core.Services.UI.ValidationResult.CreateFailure(message);
                }

                // Vérifier que le conteneur de services est initialisé
                if (app.Services == null)
                {
                    var message = "Le conteneur de services n'a pas été initialisé correctement";
                    _loggingService?.LogError($"ServiceContainerValidator: {message}");
                    return ClipboardPlus.Core.Services.UI.ValidationResult.CreateFailure(message);
                }

                _loggingService?.LogInfo("ServiceContainerValidator: Validation réussie");
                return ClipboardPlus.Core.Services.UI.ValidationResult.CreateSuccess(app.Services);
            }
            catch (Exception ex)
            {
                var message = $"Erreur lors de la validation du conteneur: {ex.Message}";
                _loggingService?.LogError($"ServiceContainerValidator: {message}", ex);
                return ClipboardPlus.Core.Services.UI.ValidationResult.CreateFailure(message);
            }
        }

        /// <summary>
        /// Obtient le fournisseur de services validé.
        /// </summary>
        public IServiceProvider? GetValidatedServiceProvider()
        {
            var result = ValidateServiceContainer();
            return result.IsValid ? result.ServiceProvider : null;
        }
    }
}
