using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using ClipboardPlus.Core.Services.Shortcuts.Interfaces;
using WpfApp = System.Windows.Application;

namespace ClipboardPlus.Core.Services.Shortcuts.Implementations
{
    /// <summary>
    /// Implémentation de production pour IDispatcherProvider.
    /// Utilise le Dispatcher WPF pour l'exécution sur le thread UI.
    /// </summary>
    public class DispatcherProvider : IDispatcherProvider
    {
        private readonly Dispatcher? _dispatcher;

        public DispatcherProvider()
        {
            try
            {
                _dispatcher = WpfApp.Current?.Dispatcher;
            }
            catch
            {
                _dispatcher = null;
            }
        }

        /// <inheritdoc />
        public async Task InvokeAsync(Action action)
        {
            if (_dispatcher == null || !IsDispatcherAvailable())
            {
                // Fallback : exécuter directement si pas de dispatcher
                action();
                return;
            }

            if (CheckAccess())
            {
                // Déjà sur le thread UI
                action();
            }
            else
            {
                // Dispatcher vers le thread UI
                await _dispatcher.InvokeAsync(action);
            }
        }

        /// <inheritdoc />
        public async Task<T> InvokeAsync<T>(Func<T> function)
        {
            if (_dispatcher == null || !IsDispatcherAvailable())
            {
                // Fallback : exécuter directement si pas de dispatcher
                return function();
            }

            if (CheckAccess())
            {
                // Déjà sur le thread UI
                return function();
            }
            else
            {
                // Dispatcher vers le thread UI
                return await _dispatcher.InvokeAsync(function);
            }
        }

        /// <inheritdoc />
        public bool IsDispatcherAvailable()
        {
            try
            {
                return _dispatcher != null && !_dispatcher.HasShutdownStarted;
            }
            catch
            {
                return false;
            }
        }

        /// <inheritdoc />
        public bool CheckAccess()
        {
            try
            {
                return _dispatcher?.CheckAccess() ?? true;
            }
            catch
            {
                return true; // Fallback sécurisé
            }
        }
    }
}
