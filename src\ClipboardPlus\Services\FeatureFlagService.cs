using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation du service de feature flags pour la migration progressive.
    ///
    /// Cette implémentation utilise un système de hachage déterministe pour
    /// assurer une répartition cohérente des utilisateurs dans les rollouts.
    /// </summary>
    public class FeatureFlagService : IFeatureFlagService
    {
        private readonly ILoggingService _loggingService;
        private readonly Dictionary<string, bool> _featureStates;
        private readonly Dictionary<string, int> _rolloutPercentages;
        private readonly string _machineId;

        /// <summary>
        /// Initialise une nouvelle instance du service de feature flags.
        /// </summary>
        public FeatureFlagService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _featureStates = new Dictionary<string, bool>();
            _rolloutPercentages = new Dictionary<string, int>();
            _machineId = Environment.MachineName + Environment.UserName;

            // Configuration par défaut pour la migration HistoryChanged
            InitializeDefaultFeatureFlags();
        }

        /// <summary>
        /// Initialise les feature flags par défaut.
        /// Architecture finalisée : Nouvelle méthode activée à 100%
        /// </summary>
        private void InitializeDefaultFeatureFlags()
        {
            // Architecture finalisée : Nouvelle méthode activée à 100%
            SetFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, true);
            SetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, 100);

            // Activer le monitoring par défaut
            SetFeatureEnabled(HistoryChangedFeatureFlags.ENABLE_HISTORY_CHANGED_MONITORING, true);
            SetFeatureEnabled(HistoryChangedFeatureFlags.ENABLE_MIGRATION_LOGGING, true);

            // Note: Le rollback legacy a été supprimé car la migration est terminée

            _loggingService.LogInfo("[FeatureFlagService] Feature flags initialisés pour la migration HistoryChanged");
        }

        /// <summary>
        /// Vérifie si une fonctionnalité est activée.
        /// </summary>
        public bool IsFeatureEnabled(string featureName)
        {
            if (string.IsNullOrEmpty(featureName))
                return false;

            // Vérifier d'abord si la fonctionnalité est explicitement activée/désactivée
            if (_featureStates.TryGetValue(featureName, out bool explicitState))
            {
                if (!explicitState)
                    return false;

                // Pour certains flags de monitoring, pas de rollout - juste on/off
                if (featureName == HistoryChangedFeatureFlags.ENABLE_HISTORY_CHANGED_MONITORING ||
                    featureName == HistoryChangedFeatureFlags.ENABLE_MIGRATION_LOGGING)
                {
                    return explicitState;
                }
            }

            // Vérifier le rollout si la fonctionnalité est activée
            return IsUserInRollout(featureName);
        }

        /// <summary>
        /// Active ou désactive une fonctionnalité.
        /// </summary>
        public void SetFeatureEnabled(string featureName, bool enabled)
        {
            if (string.IsNullOrEmpty(featureName))
                return;

            _featureStates[featureName] = enabled;
            _loggingService.LogInfo($"[FeatureFlagService] Feature '{featureName}' définie à: {enabled}");
        }

        /// <summary>
        /// Obtient le pourcentage de rollout pour une fonctionnalité.
        /// </summary>
        public int GetRolloutPercentage(string featureName)
        {
            if (string.IsNullOrEmpty(featureName))
                return 0;

            return _rolloutPercentages.TryGetValue(featureName, out int percentage) ? percentage : 0;
        }

        /// <summary>
        /// Définit le pourcentage de rollout pour une fonctionnalité.
        /// </summary>
        public void SetRolloutPercentage(string featureName, int percentage)
        {
            if (string.IsNullOrEmpty(featureName))
                return;

            percentage = Math.Max(0, Math.Min(100, percentage)); // Clamp entre 0 et 100
            _rolloutPercentages[featureName] = percentage;
            _loggingService.LogInfo($"[FeatureFlagService] Rollout pour '{featureName}' défini à: {percentage}%");
        }

        /// <summary>
        /// Vérifie si l'utilisateur actuel fait partie du rollout.
        /// </summary>
        public bool IsUserInRollout(string featureName, string? userId = null)
        {
            if (string.IsNullOrEmpty(featureName))
                return false;

            int rolloutPercentage = GetRolloutPercentage(featureName);

            // Si rollout à 0%, personne n'est inclus
            if (rolloutPercentage <= 0)
                return false;

            // Si rollout à 100%, tout le monde est inclus
            if (rolloutPercentage >= 100)
                return true;

            // Utiliser un hash déterministe pour la répartition
            string hashInput = userId ?? _machineId;
            int hash = GetDeterministicHash(featureName + hashInput);
            int userPercentile = Math.Abs(hash) % 100;

            bool inRollout = userPercentile < rolloutPercentage;

            if (_featureStates.TryGetValue(HistoryChangedFeatureFlags.ENABLE_MIGRATION_LOGGING, out bool loggingEnabled) && loggingEnabled)
            {
                _loggingService.LogInfo($"[FeatureFlagService] Rollout check pour '{featureName}': " +
                    $"percentile={userPercentile}, rollout={rolloutPercentage}%, inclus={inRollout}");
            }

            return inRollout;
        }

        /// <summary>
        /// Génère un hash déterministe pour assurer une répartition cohérente.
        /// </summary>
        private int GetDeterministicHash(string input)
        {
            using (var sha256 = SHA256.Create())
            {
                byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
                return BitConverter.ToInt32(hashBytes, 0);
            }
        }

        /// <summary>
        /// Méthodes utilitaires pour la migration HistoryChanged.
        /// </summary>
        public static class MigrationHelpers
        {
            /// <summary>
            /// Active progressivement la nouvelle méthode HistoryChanged.
            /// </summary>
            public static void EnableRefactoredHistoryChanged(IFeatureFlagService featureFlagService, int percentage = 10)
            {
                featureFlagService.SetFeatureEnabled(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, true);
                featureFlagService.SetRolloutPercentage(HistoryChangedFeatureFlags.USE_REFACTORED_HISTORY_CHANGED, percentage);
            }


        }
    }
}
