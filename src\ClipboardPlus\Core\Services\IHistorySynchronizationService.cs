using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service responsable de la synchronisation intelligente des collections d'historique.
    ///
    /// Ce service extrait la logique complexe de synchronisation entre les collections
    /// UI et Manager de la méthode ClipboardHistoryManager_HistoryChanged originale.
    /// </summary>
    public interface IHistorySynchronizationService
    {
        /// <summary>
        /// Synchronise les collections UI et Manager si nécessaire.
        ///
        /// Cette méthode reproduit la logique de synchronisation complexe de la méthode originale,
        /// incluant la gestion des tentatives multiples et la décision de rechargement complet.
        /// </summary>
        /// <param name="context">Contexte de synchronisation contenant les collections et paramètres</param>
        /// <returns>Résultat de la synchronisation indiquant l'action effectuée</returns>
        Task<SynchronizationResult> SynchronizeIfNeededAsync(HistorySynchronizationContext context);

        /// <summary>
        /// Compare deux collections pour déterminer si elles sont synchronisées.
        /// </summary>
        /// <param name="context">Contexte contenant les collections à comparer</param>
        /// <returns>Résultat de la comparaison avec détails</returns>
        CollectionComparison CompareCollections(HistorySynchronizationContext context);
    }
}
