using System;
using CommunityToolkit.Mvvm.ComponentModel;
using System.Reflection;

namespace ClipboardPlus.Core.DataModels
{
    /// <summary>
    /// Représente les paramètres de l'application.
    /// </summary>
    public class ApplicationSettings : ObservableObject
    {
        private int _maxHistoryItems = 50;
        /// <summary>
        /// Nombre maximum d'éléments dans l'historique du presse-papiers.
        /// </summary>
        public int MaxHistoryItems
        {
            get => _maxHistoryItems;
            set => SetProperty(ref _maxHistoryItems, value);
        }

        private string _activeThemePath;
        /// <summary>
        /// Chemin vers le fichier de thème actif.
        /// </summary>
        public string ActiveThemePath
        {
            get => _activeThemePath;
            set => SetProperty(ref _activeThemePath, value);
        }

        private string _shortcutKeyCombination = "Ctrl+Alt+V";
        /// <summary>
        /// Combinaison de touches pour activer l'application.
        /// </summary>
        public string ShortcutKeyCombination
        {
            get => _shortcutKeyCombination;
            set => SetProperty(ref _shortcutKeyCombination, value);
        }

        private bool _startWithWindows = false;
        /// <summary>
        /// Indique si l'application doit démarrer avec Windows.
        /// </summary>
        public bool StartWithWindows
        {
            get => _startWithWindows;
            set => SetProperty(ref _startWithWindows, value);
        }

        private int _maxImageDimensionForThumbnail = 256;
        /// <summary>
        /// Dimension maximale pour les miniatures d'images (en pixels).
        /// </summary>
        public int MaxImageDimensionForThumbnail
        {
            get => _maxImageDimensionForThumbnail;
            set => SetProperty(ref _maxImageDimensionForThumbnail, value);
        }

        private int _maxTextPreviewLength = 30;
        /// <summary>
        /// Longueur maximale de l'aperçu du texte.
        /// </summary>
        public int MaxTextPreviewLength
        {
            get => _maxTextPreviewLength;
            set => SetProperty(ref _maxTextPreviewLength, value);
        }

        private long _maxStorableItemSizeBytes = 10 * 1024 * 1024;
        /// <summary>
        /// Taille maximale d'un élément stockable (en octets, 10 Mo par défaut).
        /// </summary>
        public long MaxStorableItemSizeBytes
        {
            get => _maxStorableItemSizeBytes;
            set => SetProperty(ref _maxStorableItemSizeBytes, value);
        }

        private bool _hideTimestamp = false;
        /// <summary>
        /// Indique si l'horodatage doit être masqué dans l'interface.
        /// </summary>
        public bool HideTimestamp
        {
            get => _hideTimestamp;
            set => SetProperty(ref _hideTimestamp, value);
        }

        private bool _hideItemTitle = false;
        /// <summary>
        /// Indique si le titre des éléments doit être masqué dans l'interface.
        /// </summary>
        public bool HideItemTitle
        {
            get => _hideItemTitle;
            set => SetProperty(ref _hideItemTitle, value);
        }

        private double _settingsWindowWidth = 525;
        /// <summary>
        /// Largeur de la fenêtre des paramètres.
        /// </summary>
        public double SettingsWindowWidth
        {
            get => _settingsWindowWidth;
            set => SetProperty(ref _settingsWindowWidth, value);
        }

        private double _settingsWindowHeight = 480;
        /// <summary>
        /// Hauteur de la fenêtre des paramètres.
        /// </summary>
        public double SettingsWindowHeight
        {
            get => _settingsWindowHeight;
            set => SetProperty(ref _settingsWindowHeight, value);
        }

        private double _settingsWindowTop = -1;
        /// <summary>
        /// Position Y (Top) de la fenêtre des paramètres.
        /// </summary>
        public double SettingsWindowTop
        {
            get => _settingsWindowTop;
            set => SetProperty(ref _settingsWindowTop, value);
        }

        private double _settingsWindowLeft = -1;
        /// <summary>
        /// Position X (Left) de la fenêtre des paramètres.
        /// </summary>
        public double SettingsWindowLeft
        {
            get => _settingsWindowLeft;
            set => SetProperty(ref _settingsWindowLeft, value);
        }

        /// <summary>
        /// Constructeur par défaut initialisant les paramètres avec leurs valeurs par défaut.
        /// </summary>
        public ApplicationSettings()
        {
            // Initialiser le chemin du thème avec le format pack URI
            var assemblyName = Assembly.GetExecutingAssembly().GetName().Name ?? "ClipboardPlus";
            _activeThemePath = $"pack://application:,,,/{assemblyName};component/UI/Themes/Default.xaml";
        }
    }
} 