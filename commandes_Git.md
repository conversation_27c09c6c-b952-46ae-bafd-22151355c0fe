**Votre Flux de Travail Quotidien (Simple) :**

Imaginez que vous avez modifié des fichiers dans votre projet.

1.  **Voir les Changements :**
    *   Ouvrez un terminal (Git Bash, PowerShell, etc.) dans le dossier racine de votre projet (`clipboard-plus/`).

```bash
git status
```

*   Cela vous montre :
    *   Les fichiers que vous avez modifiés (`modified:`).
    *   Les nouveaux fichiers que vous avez créés (non suivis ou `untracked files:`).
    *   Les fichiers supprimés.

2.  **Ajouter les Changements à la "Zone de Préparation" (Staging Area) :**
    *   C'est comme dire à Git : "Je veux inclure ces changements spécifiques dans mon prochain 'enregistrement' (commit)".
    *   **Pour ajouter tous les fichiers modifiés et les nouveaux fichiers :**
```bash
git add .
```
            *   **Pour ajouter un fichier spécifique :**
        ```bash
        git add chemin/vers/le/fichier.cs
        ```
            *   Après `git add`, si vous refaites `git status`, les fichiers ajoutés apparaîtront sous "Changes to be committed".

3.  **"Enregistrer" les Changements (Commit) :**
    *   C'est l'action de sauvegarder vos changements préparés dans l'historique de votre dépôt local. Chaque commit doit avoir un message qui décrit ce que vous avez fait.
    *   Commande :
```bash
git commit -m "Votre message descriptif ici"
```
            *Exemples de messages :*
        ```bash
        git commit -m "Implement ClipboardItem data model"
        git commit -m "Fix bug in history display"
        git commit -m "Add initial styling for settings window"
        ```

4.  **Envoyer les Changements vers GitHub (Push) :**
    *   Cela met à jour le dépôt distant (sur GitHub) avec les commits que vous avez faits localement. C'est comme partager votre travail.
    *   Commande (en supposant que votre branche s'appelle `main` et votre dépôt distant `origin`) :
```bash
git push origin main
```
    *   Si vous travaillez sur une autre branche (par exemple, `ma-fonctionnalite`), vous feriez :
```bash
git push origin ma-fonctionnalite
```
    *   La première fois que vous pushez une nouvelle branche, vous devrez peut-être faire :
```bash
git push -u origin nom-de-la-branche
```
        (L'option `-u` configure la branche locale pour suivre la branche distante).

**Résumé des 4 Étapes Clés :**

```bash
# 1. Voir ce qui a changé
git status

# 2. Préparer les changements pour le commit
git add . # (ou des fichiers spécifiques)

# 3. Enregistrer les changements localement
git commit -m "Description de vos changements"

# 4. Envoyer les changements vers GitHub
git push origin main # (ou le nom de votre branche)
```

**Autres Commandes Utiles (Un Peu Moins Fréquentes au Début) :**

*   **Récupérer les Changements de GitHub (Pull) :**
    *   Si d'autres personnes ont travaillé sur le projet (ou si vous avez fait des changements depuis une autre machine), vous voudrez récupérer ces changements.
    *   Commande :
```bash
git pull origin main
```
    *   Cela récupère les changements du dépôt distant et essaie de les fusionner avec votre travail local.

*   **Voir l'Historique des Commits :**
    *   Commande :
```bash
git log
```
    *   (Appuyez sur `q` pour quitter la vue du log).

*   **Créer une Nouvelle Branche (pour travailler sur une fonctionnalité sans affecter `main`) :**
```bash
git branch nom-de-la-nouvelle-branche
```
    ou directement créer et se placer dessus :
```bash
git checkout -b nom-de-la-nouvelle-branche
```

*   **Changer de Branche :**
```bash
git checkout nom-de-la-branche
```

*   **Supprimer une Branche locale :**
Utiliser -d si la branche a été fusionnée :
```bash
git branch -d nom-de-la-branche
```
ou -D (majuscules) pour forcer la suppression même si elle ne l’est pas :
```bash
git branch -D nom-de-la-branche
```

*   **Supprimer une Branche Github :**
```bash
git push origin --delete nom-de-la-branche
```

*   **Fusionner une Branche dans votre Branche Actuelle (par exemple, fusionner `ma-fonctionnalite` dans `main`) :**
    1.  Allez sur la branche de destination : `git checkout main`
    2.  Fusionnez : `git merge nom-de-la-branche-a-fusionner`

**Conseil :**

*   Visual Studio a une excellente intégration Git. Beaucoup de ces commandes peuvent être effectuées via son interface graphique (l'onglet "Modifications Git" ou "Git Changes"). C'est souvent plus intuitif pour les débutants. Vous verrez des boutons pour "Stage", "Commit", "Push", "Pull", gérer les branches, etc.
*   N'ayez pas peur de faire `git status` souvent, cela vous aide à comprendre où vous en êtes.

Commencez par les 4 étapes clés. Une fois que vous êtes à l'aise avec celles-ci, vous pourrez explorer les autres commandes et les branches. Bonne chance !


********

Script automatique de push vers GitHub :

```bash
.\Start-GitPushAll.ps1
```