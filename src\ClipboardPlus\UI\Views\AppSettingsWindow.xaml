<Window x:Class="ClipboardPlus.UI.Views.AppSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ClipboardPlus.UI.Views"
        xmlns:viewmodels="clr-namespace:ClipboardPlus.UI.ViewModels"
        xmlns:controls="clr-namespace:ClipboardPlus.UI.Controls"
        xmlns:converters="clr-namespace:ClipboardPlus.UI.Converters"
        mc:Ignorable="d"
        Title="Paramètres de ClipboardPlus"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Width="525" Height="480"
        MinWidth="500" MinHeight="400"
        MaxWidth="800" MaxHeight="700">

    <Window.Resources>
        <Style x:Key="TabItemStyle" TargetType="TabItem">
            <Setter Property="Padding" Value="10,5" />
            <Setter Property="FontSize" Value="14" />
        </Style>
        
        <Style x:Key="GroupBoxStyle" TargetType="GroupBox">
            <Setter Property="Margin" Value="10" />
            <Setter Property="Padding" Value="10" />
            <Setter Property="BorderThickness" Value="1" />
        </Style>
        
        <Style x:Key="SettingsLabelStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="0,5,10,5" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
        
        <Style x:Key="SettingsControlStyle" TargetType="Control">
            <Setter Property="Margin" Value="0,5" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>

        <converters:StringToKeyCombinationConverter x:Key="StringToKeyCombinationConverter" />
        <!-- Utilisation du convertisseur WPF natif -->
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Contenu principal -->
        <TabControl Margin="10" BorderThickness="1">
            <TabItem Header="Général" Style="{StaticResource TabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <GroupBox Header="Historique" Style="{StaticResource GroupBoxStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                
                                <TextBlock Text="Nombre maximum d'éléments:" Style="{StaticResource SettingsLabelStyle}" />
                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Slider Minimum="10" Maximum="200" 
                                            Value="{Binding MaxHistoryItemsVM}" 
                                            Width="200"
                                            TickFrequency="10" 
                                            IsSnapToTickEnabled="True"
                                            TickPlacement="BottomRight" />
                                    <TextBlock Text="{Binding MaxHistoryItemsVM}" 
                                               Margin="10,0,0,0" 
                                               VerticalAlignment="Center" />
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                        
                        <GroupBox Header="Démarrage" Style="{StaticResource GroupBoxStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Text="Démarrer avec Windows:" Style="{StaticResource SettingsLabelStyle}" />
                                <CheckBox Grid.Column="1" 
                                          IsChecked="{Binding StartWithWindowsVM}" 
                                          VerticalAlignment="Center" />
                            </Grid>
                        </GroupBox>
                        
                        <GroupBox Header="Stockage" Style="{StaticResource GroupBoxStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                
                                <!-- Taille maximale des éléments stockables -->
                                <TextBlock Grid.Row="0" Grid.Column="0" 
                                           Text="Taille maximale des éléments (Mo):" 
                                           Style="{StaticResource SettingsLabelStyle}" />
                                <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                                    <Slider Minimum="1" Maximum="50" 
                                            Value="{Binding MaxStorableItemSizeMBVM}" 
                                            Width="200"
                                            TickFrequency="5" 
                                            IsSnapToTickEnabled="True"
                                            TickPlacement="BottomRight" />
                                    <TextBlock Text="{Binding MaxStorableItemSizeMBVM}" 
                                               Margin="10,0,0,0" 
                                               VerticalAlignment="Center" />
                                    <TextBlock Text=" Mo" 
                                               VerticalAlignment="Center" />
                                </StackPanel>
                                
                                <!-- Dimension maximale pour les miniatures -->
                                <TextBlock Grid.Row="1" Grid.Column="0" 
                                           Text="Dimension max. des miniatures (px):" 
                                           Style="{StaticResource SettingsLabelStyle}" />
                                <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal">
                                    <Slider Minimum="64" Maximum="512" 
                                            Value="{Binding MaxImageDimensionForThumbnailVM}" 
                                            Width="200"
                                            TickFrequency="64" 
                                            IsSnapToTickEnabled="True"
                                            TickPlacement="BottomRight" />
                                    <TextBlock Text="{Binding MaxImageDimensionForThumbnailVM}" 
                                               Margin="10,0,0,0" 
                                               VerticalAlignment="Center" />
                                    <TextBlock Text=" px" 
                                               VerticalAlignment="Center" />
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <TabItem Header="Raccourcis" Style="{StaticResource TabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <GroupBox Header="Raccourci global" Style="{StaticResource GroupBoxStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Text="Raccourci pour afficher l'historique:" Style="{StaticResource SettingsLabelStyle}" />

                                <!-- Grid pour le contrôle de raccourci + bouton Modifier -->
                                <Grid Grid.Column="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <controls:ShortcutCaptureTextBox
                                        x:Name="ShortcutTextBox"
                                        Grid.Column="0"
                                        CurrentShortcut="{Binding ShortcutTextVM,
                                                         Converter={StaticResource StringToKeyCombinationConverter},
                                                         Mode=OneWay}"
                                        Style="{StaticResource SettingsControlStyle}"
                                        ToolTip="Utilisez le bouton 'Modifier...' pour changer le raccourci clavier"
                                        Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"
                                        Foreground="{DynamicResource {x:Static SystemColors.WindowTextBrushKey}}"
                                        BorderBrush="{DynamicResource {x:Static SystemColors.ActiveBorderBrushKey}}" />

                                    <Button Grid.Column="1"
                                            Content="Modifier..."
                                            Command="{Binding OpenShortcutCaptureCommand}"
                                            Margin="5,0,0,0"
                                            Padding="10,2"
                                            Height="{Binding ElementName=ShortcutTextBox, Path=ActualHeight}"
                                            VerticalAlignment="Center"
                                            ToolTip="Ouvrir une fenêtre dédiée pour modifier le raccourci clavier"
                                            MinWidth="80" />
                                </Grid>
                            </Grid>
                        </GroupBox>
                        
                        <TextBlock Margin="10,5" TextWrapping="Wrap">
                            <Run FontStyle="Italic">💡 Utilisez le bouton "Modifier..." pour personnaliser le raccourcis :</Run>
                            <LineBreak />
                            <Run FontStyle="Italic">Touches disponibles: Ctrl, Alt, Shift + lettre/chiffre/caractères spéciaux</Run>
                        </TextBlock>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <TabItem Header="Apparence" Style="{StaticResource TabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <GroupBox Header="Thèmes" Style="{StaticResource GroupBoxStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Text="Sélectionnez un thème:" 
                                           Style="{StaticResource SettingsLabelStyle}" />
                                <ComboBox Grid.Column="1"
                                          ItemsSource="{Binding AvailableThemes}"
                                          SelectedItem="{Binding SelectedThemeVM, Mode=TwoWay}"
                                          DisplayMemberPath="Name"
                                          Style="{StaticResource SettingsControlStyle}"
                                          Width="Auto"
                                          HorizontalAlignment="Stretch" />
                            </Grid>
                        </GroupBox>
                        
                        <GroupBox Header="Options d'affichage" Style="{StaticResource GroupBoxStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Masquer l'horodatage (timestamp):" 
                                           Style="{StaticResource SettingsLabelStyle}" />
                                <CheckBox Grid.Row="0" Grid.Column="1" 
                                          IsChecked="{Binding HideTimestampVM}" 
                                          VerticalAlignment="Center"
                                          ToolTip="Masque l'affichage de la date et l'heure dans les éléments de l'historique" />
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Masquer le titre des éléments:" 
                                           Style="{StaticResource SettingsLabelStyle}" />
                                <CheckBox Grid.Row="1" Grid.Column="1" 
                                          IsChecked="{Binding HideItemTitleVM}" 
                                          VerticalAlignment="Center"
                                          ToolTip="Masque l'affichage du titre dans les éléments de l'historique" />
                            </Grid>
                        </GroupBox>
                        
                        <TextBlock Margin="10,5" TextWrapping="Wrap" FontStyle="Italic">
                            <Run>Le changement de thème est appliqué immédiatement.</Run>
                            <LineBreak />
                            <Run>Cliquez sur "OK" ou "Appliquer" pour confirmer votre choix.</Run>
                        </TextBlock>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <TabItem Header="À propos" Style="{StaticResource TabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <TextBlock Text="ClipboardPlus" FontSize="24" FontWeight="Bold" />
                        <TextBlock x:Name="VersionTextBlock" Text="{Binding ApplicationDisplayVersion, StringFormat=Version {0}, Mode=OneWay}" Margin="0,5,0,20" />
                        
                        <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                            <Run>ClipboardPlus est un gestionnaire d'historique de presse-papiers avancé pour Windows.</Run>
                            <LineBreak />
                            <Run>Il vous permet de stocker et de réutiliser facilement des éléments copiés précédemment.</Run>
                        </TextBlock>
                        
                        <TextBlock TextWrapping="Wrap" Margin="0,0,0,20">
                            <Run>Fonctionnalités principales :</Run>
                            <LineBreak />
                            <Run>• Historique étendu du presse-papiers</Run>
                            <LineBreak />
                            <Run>• Recherche d'éléments</Run>
                            <LineBreak />
                            <Run>• Détection et gestion intelligente des doublons</Run>
                            <LineBreak />
                            <Run>• Prévisualisation du contenu</Run>
                            <LineBreak />
                            <Run>• Nommage personnalisé des éléments</Run>
                            <LineBreak />
                            <Run>• Réorganisation par glissé déposé</Run>
                            <LineBreak />
                            <Run>• Création manuelle d'éléments</Run>
                            <LineBreak />
                            <Run>• Persistance des données localement</Run>
                            <LineBreak />
                            <Run>• Épinglage d'éléments importants</Run>
                            <LineBreak />
                            <Run>• Raccourcis clavier personnalisable</Run>
                            <LineBreak />
                            <Run>• Personnalisation de l'UI via XAML</Run>
                            <LineBreak />
                            <Run>• Thème clair et sombre disponibles</Run>
                            <LineBreak />
                            <Run>• Et bien d'autres !</Run>
                        </TextBlock>
                        
                        <TextBlock Margin="0,0,0,10">
                            <Run>Site du projet : </Run>
                            <Hyperlink NavigateUri="https://github.com/User/ClipboardPlus" RequestNavigate="Hyperlink_RequestNavigate">
                                https://github.com/User/ClipboardPlus
                            </Hyperlink>
                        </TextBlock>
                        
                        <TextBlock Margin="0,0,0,10">
                            <Run>Licence : </Run>
                            <Hyperlink NavigateUri="https://opensource.org/licenses/MIT" RequestNavigate="Hyperlink_RequestNavigate">
                                MIT License
                            </Hyperlink>
                        </TextBlock>
                        
                        <Border BorderThickness="0,1,0,0" BorderBrush="#DDDDDD" Margin="0,10,0,10" />
                        
                        <TextBlock TextWrapping="Wrap">
                            <Run>© </Run>
                            <Run Text="{Binding CurrentYear, Mode=OneWay}" />
                            <Run> ClipboardPlus. Tous droits réservés.</Run>
                        </TextBlock>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
        
        <!-- Barre de statut et boutons -->
        <Grid Grid.Row="1" Background="#F0F0F0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            
            <!-- Barre de statut -->
            <Border BorderThickness="0,1,0,0" BorderBrush="#DDDDDD">
                <Grid Margin="10,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Text="{Binding StatusMessage}" 
                               Margin="5,0" 
                               VerticalAlignment="Center" />
                    
                    <ProgressBar Grid.Column="1" 
                                 IsIndeterminate="{Binding IsBusy}" 
                                 Height="5" 
                                 Margin="5,0" 
                                 Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}" />
                </Grid>
            </Border>
            
            <!-- Boutons d'action -->
            <StackPanel Grid.Column="1" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right" 
                        Margin="10,10,10,10">
                <Button Content="Appliquer"
                        x:Name="ApplyButton"
                        Width="80"
                        MinHeight="25"
                        Margin="5,0"
                        Click="ApplyButton_Click" />
                <Button Content="OK"
                        x:Name="OkButton"
                        Width="80"
                        MinHeight="25"
                        Margin="5,0"
                        Click="OkButton_Click" />
                <Button Content="Annuler"
                        x:Name="CancelButton"
                        IsCancel="True"
                        Width="80"
                        MinHeight="25"
                        Margin="5,0"
                        Click="CancelButton_Click" />
            </StackPanel>
        </Grid>
    </Grid>
</Window> 