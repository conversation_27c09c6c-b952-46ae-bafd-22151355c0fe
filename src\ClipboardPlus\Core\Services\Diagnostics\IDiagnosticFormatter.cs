namespace ClipboardPlus.Core.Services.Diagnostics
{
    /// <summary>
    /// Interface pour le formatage des informations de diagnostic
    /// Respecte le Single Responsibility Principle (SRP)
    /// </summary>
    public interface IDiagnosticFormatter
    {
        /// <summary>
        /// Formate l'en-tête de diagnostic avec message et timestamp
        /// </summary>
        /// <param name="message">Message de l'opération</param>
        /// <param name="timestamp">Timestamp de l'opération</param>
        /// <returns>En-tête formaté</returns>
        string FormatDiagnosticHeader(string message, DateTime timestamp);

        /// <summary>
        /// Formate les informations d'un élément du presse-papiers
        /// </summary>
        /// <param name="itemData">Données de l'élément</param>
        /// <returns>Informations formatées de l'élément</returns>
        string FormatItemInformation(DiagnosticData itemData);

        /// <summary>
        /// Formate les informations du ViewModel
        /// </summary>
        /// <param name="viewModelData">Donn<PERSON> du ViewModel</param>
        /// <returns>Informations formatées du ViewModel</returns>
        string FormatViewModelInformation(DiagnosticData viewModelData);

        /// <summary>
        /// Formate les informations système
        /// </summary>
        /// <param name="systemData">Données système</param>
        /// <returns>Informations formatées du système</returns>
        string FormatSystemInformation(DiagnosticData systemData);

        /// <summary>
        /// Assemble toutes les sections formatées en un rapport complet
        /// </summary>
        /// <param name="header">En-tête formaté</param>
        /// <param name="itemInfo">Informations de l'élément formatées</param>
        /// <param name="viewModelInfo">Informations du ViewModel formatées</param>
        /// <param name="systemInfo">Informations système formatées</param>
        /// <returns>Rapport de diagnostic complet</returns>
        string AssembleDiagnosticReport(string header, string itemInfo, string viewModelInfo, string systemInfo);
    }
}
