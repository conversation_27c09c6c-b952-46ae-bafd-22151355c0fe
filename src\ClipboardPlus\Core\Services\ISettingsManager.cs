using System;
using System.Linq.Expressions;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface définissant les opérations de gestion des paramètres de l'application.
    /// </summary>
    public interface ISettingsManager
    {
        /// <summary>
        /// Nombre maximum d'éléments dans l'historique du presse-papiers.
        /// </summary>
        int MaxHistoryItems { get; set; }

        /// <summary>
        /// Chemin vers le fichier de thème actif.
        /// </summary>
        string ActiveThemePath { get; set; }

        /// <summary>
        /// Combinaison de touches pour activer l'application.
        /// </summary>
        string ShortcutKeyCombination { get; set; }

        /// <summary>
        /// Indique si l'application doit démarrer avec Windows.
        /// </summary>
        bool StartWithWindows { get; set; }

        /// <summary>
        /// Dimension maximale pour les miniatures d'images.
        /// </summary>
        int MaxImageDimensionForThumbnail { get; set; }

        /// <summary>
        /// Taille des miniatures en pixels.
        /// </summary>
        int ThumbnailSize { get; }

        /// <summary>
        /// Taille maximale d'un élément stockable (en octets).
        /// </summary>
        long MaxStorableItemSizeBytes { get; set; }

        /// <summary>
        /// Longueur maximale de l'aperçu du texte.
        /// </summary>
        int MaxTextPreviewLength { get; set; }

        /// <summary>
        /// Indique si l'horodatage doit être masqué dans l'interface.
        /// </summary>
        bool HideTimestamp { get; set; }

        /// <summary>
        /// Indique si le titre des éléments doit être masqué dans l'interface.
        /// </summary>
        bool HideItemTitle { get; set; }

        /// <summary>
        /// Largeur de la fenêtre des paramètres.
        /// </summary>
        double SettingsWindowWidth { get; set; }

        /// <summary>
        /// Hauteur de la fenêtre des paramètres.
        /// </summary>
        double SettingsWindowHeight { get; set; }

        /// <summary>
        /// Position Y (Top) de la fenêtre des paramètres.
        /// </summary>
        double SettingsWindowTop { get; set; }

        /// <summary>
        /// Position X (Left) de la fenêtre des paramètres.
        /// </summary>
        double SettingsWindowLeft { get; set; }

        /// <summary>
        /// Sauvegarde tous les paramètres actuels dans la source de persistance.
        /// </summary>
        /// <returns>Tâche asynchrone.</returns>
        Task SaveSettingsToPersistenceAsync();

        /// <summary>
        /// Événement déclenché lorsqu'un paramètre est modifié.
        /// </summary>
        event Action<string>? SettingChanged;

        /// <summary>
        /// Charge les paramètres depuis la source de persistance.
        /// </summary>
        /// <returns>Tâche asynchrone.</returns>
        Task LoadSettingsAsync();

        /// <summary>
        /// Sauvegarde un paramètre dans la source de persistance.
        /// </summary>
        /// <typeparam name="T">Type de la valeur du paramètre.</typeparam>
        /// <param name="propertySelector">Expression pour sélectionner la propriété à sauvegarder.</param>
        /// <param name="value">Nouvelle valeur du paramètre.</param>
        /// <returns>Tâche asynchrone.</returns>
        Task SaveSettingAsync<T>(Expression<Func<ApplicationSettings, T>> propertySelector, T value);
    }
} 