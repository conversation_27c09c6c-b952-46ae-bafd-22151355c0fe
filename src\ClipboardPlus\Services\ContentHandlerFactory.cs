using System;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation de la factory pour créer les handlers de contenu appropriés.
    /// Utilise l'injection de dépendances pour obtenir tous les handlers disponibles.
    /// Respecte le principe ouvert/fermé (OCP) - facilement extensible pour de nouveaux types.
    /// </summary>
    public class ContentHandlerFactory : IContentHandlerFactory
    {
        private readonly IEnumerable<IContentHandler> _handlers;
        private readonly ILoggingService? _loggingService;
        private readonly Dictionary<ClipboardDataType, IContentHandler> _handlerCache;

        /// <summary>
        /// Initialise une nouvelle instance de ContentHandlerFactory.
        /// </summary>
        /// <param name="handlers">Tous les handlers disponibles (injectés par DI)</param>
        /// <param name="loggingService">Service de logging optionnel</param>
        public ContentHandlerFactory(IEnumerable<IContentHandler> handlers, ILoggingService? loggingService = null)
        {
            _handlers = handlers ?? throw new ArgumentNullException(nameof(handlers));
            _loggingService = loggingService;
            _handlerCache = new Dictionary<ClipboardDataType, IContentHandler>();

            InitializeHandlerCache();
        }

        /// <inheritdoc />
        public IContentHandler? CreateHandler(ClipboardDataType dataType)
        {
            try
            {
                _loggingService?.LogInfo($"ContentHandlerFactory.CreateHandler - Recherche d'un handler pour le type: {dataType}");

                if (_handlerCache.TryGetValue(dataType, out IContentHandler? handler))
                {
                    _loggingService?.LogInfo($"ContentHandlerFactory.CreateHandler - Handler trouvé: {handler.GetType().Name}");
                    return handler;
                }

                _loggingService?.LogWarning($"ContentHandlerFactory.CreateHandler - Aucun handler trouvé pour le type: {dataType}");
                return null;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ContentHandlerFactory.CreateHandler - Erreur lors de la création du handler pour {dataType}: {ex.Message}", ex);
                return null;
            }
        }

        /// <inheritdoc />
        public IContentHandler? CreateHandler(ClipboardItem item)
        {
            ArgumentNullException.ThrowIfNull(item);

            try
            {
                _loggingService?.LogInfo($"ContentHandlerFactory.CreateHandler - Recherche d'un handler pour l'élément ID {item.Id} (type {item.DataType})");

                // Utilise d'abord la méthode par type de données
                var handler = CreateHandler(item.DataType);
                
                // Vérification supplémentaire que le handler peut effectivement traiter cet élément
                if (handler != null && handler.CanHandle(item))
                {
                    _loggingService?.LogInfo($"ContentHandlerFactory.CreateHandler - Handler {handler.GetType().Name} peut traiter l'élément ID {item.Id}");
                    return handler;
                }

                if (handler != null)
                {
                    _loggingService?.LogWarning($"ContentHandlerFactory.CreateHandler - Handler {handler.GetType().Name} trouvé mais ne peut pas traiter l'élément ID {item.Id}");
                }

                return null;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ContentHandlerFactory.CreateHandler - Erreur lors de la création du handler pour l'élément ID {item.Id}: {ex.Message}", ex);
                return null;
            }
        }

        /// <inheritdoc />
        public IEnumerable<IContentHandler> GetAllHandlers()
        {
            _loggingService?.LogInfo($"ContentHandlerFactory.GetAllHandlers - Retour de {_handlers.Count()} handlers");
            return _handlers;
        }

        /// <inheritdoc />
        public bool HasHandler(ClipboardDataType dataType)
        {
            bool hasHandler = _handlerCache.ContainsKey(dataType);
            
            _loggingService?.LogInfo($"ContentHandlerFactory.HasHandler - Handler disponible pour {dataType}: {hasHandler}");
            
            return hasHandler;
        }

        /// <inheritdoc />
        public IEnumerable<ClipboardDataType> GetSupportedDataTypes()
        {
            var supportedTypes = _handlerCache.Keys.ToList();
            
            _loggingService?.LogInfo($"ContentHandlerFactory.GetSupportedDataTypes - Types supportés: {string.Join(", ", supportedTypes)}");
            
            return supportedTypes;
        }

        /// <summary>
        /// Initialise le cache des handlers pour améliorer les performances.
        /// </summary>
        private void InitializeHandlerCache()
        {
            try
            {
                _loggingService?.LogInfo("ContentHandlerFactory.InitializeHandlerCache - Initialisation du cache des handlers");

                _handlerCache.Clear();

                foreach (var handler in _handlers)
                {
                    if (handler == null)
                    {
                        _loggingService?.LogWarning("ContentHandlerFactory.InitializeHandlerCache - Handler null détecté, ignoré");
                        continue;
                    }

                    var dataType = handler.SupportedDataType;
                    
                    if (_handlerCache.ContainsKey(dataType))
                    {
                        _loggingService?.LogWarning($"ContentHandlerFactory.InitializeHandlerCache - Handler en double pour le type {dataType}: {handler.GetType().Name} remplace {_handlerCache[dataType].GetType().Name}");
                    }

                    _handlerCache[dataType] = handler;
                    _loggingService?.LogInfo($"ContentHandlerFactory.InitializeHandlerCache - Handler enregistré: {handler.GetType().Name} pour {dataType}");
                }

                _loggingService?.LogInfo($"ContentHandlerFactory.InitializeHandlerCache - Cache initialisé avec {_handlerCache.Count} handlers");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ContentHandlerFactory.InitializeHandlerCache - Erreur lors de l'initialisation du cache: {ex.Message}", ex);
                throw;
            }
        }
    }
}
