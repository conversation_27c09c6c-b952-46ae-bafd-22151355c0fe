<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- Couleurs de base du thème clair pastel -->
    <Color x:Key="PrimaryColor">#87CEEB</Color>
    <Color x:Key="PrimaryLightColor">#6BB6FF</Color>
    <Color x:Key="PrimaryDarkColor">#4A90E2</Color>
    <Color x:Key="AccentColor">#FF9800</Color>
    <Color x:Key="TextPrimaryColor">#212121</Color>
    <Color x:Key="TextSecondaryColor">#757575</Color>
    <Color x:Key="BackgroundColor">#F8F8F8</Color>
    <Color x:Key="SurfaceColor">#EEEEEE</Color>
    <Color x:Key="ErrorColor">#F44336</Color>
    
    <!-- Brosses dérivées des couleurs -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    
    <!-- Styles de base -->
    <Style x:Key="DefaultWindowStyle" TargetType="Window">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
    </Style>
    
    <Style x:Key="DefaultButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Height" Value="28"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="Margin" Value="8,0,0,0"/>
        <Setter Property="Padding" Value="16,0"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="0">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="#CCCCCC"/>
                            <Setter Property="Foreground" Value="#888888"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style x:Key="DefaultTextBlockStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="12"/>
    </Style>
    
    <Style x:Key="DefaultListBoxStyle" TargetType="ListBox">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryLightBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>
    
    <Style x:Key="DefaultTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryLightBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="5,3"/>
    </Style>
    
    <Style x:Key="DefaultComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryLightBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>
    
    <!-- Styles par défaut pour les contrôles communs -->
    <Style TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}"/>
    <Style TargetType="TextBlock" BasedOn="{StaticResource DefaultTextBlockStyle}"/>
    <Style TargetType="ListBox" BasedOn="{StaticResource DefaultListBoxStyle}"/>
    <Style TargetType="TextBox" BasedOn="{StaticResource DefaultTextBoxStyle}"/>
    <Style TargetType="ComboBox" BasedOn="{StaticResource DefaultComboBoxStyle}"/>
    
</ResourceDictionary> 