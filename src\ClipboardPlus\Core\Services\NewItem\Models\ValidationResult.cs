namespace ClipboardPlus.Core.Services.NewItem.Models
{
    /// <summary>
    /// Résultat d'une opération de validation pour la création de nouveaux éléments.
    /// </summary>
    public class NewItemValidationResult
    {
        /// <summary>
        /// Indique si la validation a réussi.
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Message d'erreur en cas d'échec de validation.
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// Crée un résultat de validation réussie.
        /// </summary>
        /// <returns>Résultat de validation réussie</returns>
        public static NewItemValidationResult Success() => new NewItemValidationResult { IsValid = true };

        /// <summary>
        /// Crée un résultat de validation échouée avec un message d'erreur.
        /// </summary>
        /// <param name="errorMessage">Message d'erreur</param>
        /// <returns>Résultat de validation échouée</returns>
        public static NewItemValidationResult Failure(string errorMessage) => new NewItemValidationResult
        { 
            IsValid = false, 
            ErrorMessage = errorMessage 
        };
    }
}
