namespace ClipboardPlus.UI.ViewModels.Construction.Interfaces
{
    /// <summary>
    /// Service d'initialisation de toutes les commandes du ViewModel.
    /// Responsabilité : Initialisation centralisée de toutes les commandes selon le principe SRP.
    /// </summary>
    public interface ICommandInitializer
    {
        /// <summary>
        /// Initialise toutes les commandes du ViewModel.
        /// Cette méthode centralise l'initialisation de toutes les commandes qui étaient
        /// précédemment dispersées dans plusieurs méthodes du constructeur :
        /// - InitializeRenamingCommands()
        /// - InitializeNewItemCommands()
        /// - Autres commandes spécialisées
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel dont les commandes doivent être initialisées</param>
        void InitializeAllCommands(ClipboardHistoryViewModel viewModel);
    }
}
