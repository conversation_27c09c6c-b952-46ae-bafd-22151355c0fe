using System;
using System.Windows;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using WpfMessageBox = System.Windows.MessageBox;
using WpfApplication = System.Windows.Application;
using ClipboardPlus.Core.Extensions;
using System.Windows.Input;

namespace ClipboardPlus.UI.Dialogs
{
    /// <summary>
    /// Boîte de dialogue pour l'édition d'un nouvel élément
    /// </summary>
    public partial class NewItemEditorDialog : Window
    {
        private readonly ClipboardHistoryViewModel? _viewModel;
        private readonly ILoggingService? _logger;
        private bool _isClosing = false;

        /// <summary>
        /// Constructeur par défaut (principalement pour le designer)
        /// </summary>
        public NewItemEditorDialog()
        {
            InitializeComponent();
            DataContextChanged += NewItemEditorDialog_DataContextChanged;
            // S'assurer de se désabonner lors de la fermeture pour éviter les fuites de mémoire
            Closed += (s, e) =>
            {
                if (DataContext is ClipboardHistoryViewModel vm)
                {
                    vm.RequestCloseDialog -= ViewModel_RequestCloseDialog;
                }
            };
            
            try
            {
                _logger = GetLoggingService();
                _logger?.LogDebug("NewItemEditorDialog: Constructeur par défaut appelé");
                
                // Dans le constructeur par défaut, nous n'avons pas de ViewModel
                // Ce constructeur est principalement utilisé par le designer
            }
            catch (Exception ex)
            {
                // Utiliser MessageBox directement car le logger pourrait être null
                WpfMessageBox.Show(
                    $"Erreur lors de l'initialisation de la boîte de dialogue : {ex.Message}", 
                    "Erreur", 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Constructeur avec ViewModel pour l'édition
        /// </summary>
        public NewItemEditorDialog(ClipboardHistoryViewModel viewModel)
        {
            if (viewModel == null)
            {
                throw new ArgumentNullException(nameof(viewModel));
            }
            
            try
            {
                InitializeComponent();
                
                _logger = GetLoggingService();
                _logger?.LogDebug("NewItemEditorDialog: Constructeur avec ViewModel appelé");
                
                _viewModel = viewModel;
                DataContext = _viewModel;
                
                // S'abonner aux changements de propriétés pour détecter quand fermer la boîte de dialogue
                _viewModel.PropertyChanged += ViewModel_PropertyChanged;
                
                // Ajouter un gestionnaire d'événement pour le changement de texte
                ContentTextBox.TextChanged += ContentTextBox_TextChanged;
                
                // Définir le focus sur la zone de texte après le chargement de la fenêtre
                Loaded += (s, e) => 
                {
                    try
                    {
                        _logger?.LogDebug("NewItemEditorDialog: Fenêtre chargée, définition du focus sur ContentTextBox");
                        
                        // Utiliser le Dispatcher pour s'assurer que le focus est défini après le rendu complet
                        Dispatcher.BeginInvoke(new Action(() =>
                        {
                            try
                            {
                                ContentTextBox.Focus();
                            }
                            catch (Exception focusEx)
                            {
                                _logger?.LogError($"Impossible de définir le focus sur ContentTextBox : {focusEx.Message}", focusEx);
                            }
                        }));
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError($"Erreur lors du chargement de la fenêtre : {ex.Message}", ex);
                    }
                };
            }
            catch (Exception ex)
            {
                _logger?.LogCritical($"Erreur critique lors de l'initialisation de NewItemEditorDialog : {ex.Message}", ex);
                WpfMessageBox.Show(
                    $"Erreur lors de l'initialisation de la boîte de dialogue : {ex.Message}", 
                    "Erreur", 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Error);
                
                throw; // Propager l'exception car nous ne pouvons pas continuer
            }
        }

        /// <summary>
        /// Obtient le service de journalisation à partir du fournisseur de services de l'application
        /// </summary>
        private ILoggingService? GetLoggingService()
        {
            try
            {
                return WpfApplication.Current?.Services()?.GetService(typeof(ILoggingService)) as ILoggingService;
            }
            catch (Exception)
            {
                // Ne pas journaliser ici pour éviter une récursion infinie
                return null;
            }
        }

        /// <summary>
        /// Gère les changements de propriétés du ViewModel
        /// </summary>
        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            try
            {
                // Si la propriété IsItemCreationActive passe à false, fermer la boîte de dialogue
                if (e.PropertyName == nameof(ClipboardHistoryViewModel.IsItemCreationActive) &&
                    _viewModel != null &&
                    !_viewModel.IsItemCreationActive)
                {
                    _logger?.LogDebug("ViewModel_PropertyChanged: IsItemCreationActive est passé à false, fermeture de la boîte de dialogue");
                    CloseDialogSafely();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Erreur lors du traitement du changement de propriété '{e.PropertyName}' : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Ferme la boîte de dialogue de manière sécurisée
        /// </summary>
        private void CloseDialogSafely()
        {
            try
            {
                if (_isClosing)
                {
                    _logger?.LogDebug("CloseDialogSafely: La boîte de dialogue est déjà en cours de fermeture");
                    return;
                }
                
                _isClosing = true;
                _logger?.LogDebug("CloseDialogSafely: Début de la fermeture de la boîte de dialogue");
                
                // Désabonner des événements pour éviter les fuites de mémoire
                if (_viewModel != null)
                {
                    _viewModel.PropertyChanged -= ViewModel_PropertyChanged;
                }
                
                // Définir le DialogResult avant de fermer
                if (_viewModel != null && this.DialogResult == null)
                {
                    // Si IsItemCreationActive est false, c'est probablement un succès ou une annulation propre
                    this.DialogResult = !_viewModel.IsItemCreationActive;
                    _logger?.LogDebug($"CloseDialogSafely: DialogResult défini à {this.DialogResult} basé sur IsItemCreationActive={_viewModel.IsItemCreationActive}");
                }

                // Utiliser le Dispatcher pour fermer la fenêtre sur le thread UI
                if (Dispatcher.CheckAccess())
                {
                    _logger?.LogDebug("CloseDialogSafely: Fermeture de la boîte de dialogue sur le thread UI");
                    Close();
                }
                else
                {
                    _logger?.LogDebug("CloseDialogSafely: Programmation de la fermeture de la boîte de dialogue via le Dispatcher");
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        _logger?.LogDebug("CloseDialogSafely via Dispatcher: Fermeture de la boîte de dialogue");
                        Close();
                    }));
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Erreur lors de la fermeture de la boîte de dialogue : {ex.Message}", ex);
                
                // Tenter de fermer la boîte de dialogue malgré l'erreur
                try
                {
                    Close();
                }
                catch
                {
                    // Ignorer les erreurs à ce stade
                }
            }
        }

        /// <summary>
        /// Appelé lorsque la fenêtre est fermée
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                _logger?.LogDebug($"OnClosed: La boîte de dialogue a été fermée avec DialogResult = {this.DialogResult}");

                // Si le DialogResult n'a pas été défini explicitement et que le ViewModel est toujours en mode création,
                // cela signifie que l'utilisateur a fermé la fenêtre sans utiliser les boutons (X, Escape, etc.)
                if (this.DialogResult == null && _viewModel != null && _viewModel.IsItemCreationActive)
                {
                    _logger?.LogDebug("OnClosed: Fermeture sans DialogResult explicite, annulation de la création d'élément");
                    this.DialogResult = false; // Définir comme annulation
                    _viewModel.DiscardNewItemCreationCommand.Execute(null);
                }

                base.OnClosed(e);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Erreur lors de la fermeture de la boîte de dialogue : {ex.Message}", ex);
                base.OnClosed(e);
            }
        }

        private void NewItemEditorDialog_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.OldValue is ClipboardHistoryViewModel oldVm)
            {
                oldVm.RequestCloseDialog -= ViewModel_RequestCloseDialog;
            }
            if (e.NewValue is ClipboardHistoryViewModel newVm)
            {
                newVm.RequestCloseDialog += ViewModel_RequestCloseDialog;
            }
        }

        private void ViewModel_RequestCloseDialog(object? sender, System.EventArgs e)
        {
            // Il est plus sûr de vérifier si la fenêtre n'est pas déjà en train de se fermer
            // et de s'assurer que cela s'exécute sur le thread UI si nécessaire.
            // ShowDialog s'exécute de manière synchrone, donc this.Close() devrait être sûr.
            if (this.IsLoaded) // Ne pas essayer de fermer si pas encore chargé ou déjà fermé
            {
                try
                {
                    // Déterminer le DialogResult basé sur l'état du ViewModel
                    if (_viewModel != null)
                    {
                        // Si IsItemCreationActive est false et qu'il y avait du contenu, c'est un succès
                        // Si IsItemCreationActive est false et qu'il n'y avait pas de contenu, c'est une annulation
                        bool wasSuccessful = !_viewModel.IsItemCreationActive && !string.IsNullOrWhiteSpace(_viewModel.NewItemTextContent);
                        this.DialogResult = wasSuccessful;
                        _logger?.LogDebug($"ViewModel_RequestCloseDialog: DialogResult défini à {this.DialogResult}");
                    }

                    this.Close();
                }
                catch (System.InvalidOperationException)
                {
                    // Peut arriver si la fenêtre est déjà en train de se fermer, ignorer.
                }
            }
        }

        private void ContentTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            try
            {
                if (_viewModel != null)
                {
                    _logger?.LogDebug($"ContentTextBox_TextChanged: Texte actuel = '{ContentTextBox.Text}'");
                    _logger?.LogDebug($"ContentTextBox_TextChanged: NewItemTextContent = '{_viewModel.NewItemTextContent}'");
                    
                    // Si la propriété NewItemTextContent n'est pas mise à jour, forcer sa mise à jour manuellement
                    if (_viewModel.NewItemTextContent != ContentTextBox.Text)
                    {
                        _logger?.LogDebug($"ContentTextBox_TextChanged: Mise à jour manuelle de NewItemTextContent");
                        _viewModel.NewItemTextContent = ContentTextBox.Text;
                    }
                    
                    // Forcer la mise à jour des commandes
                    _viewModel.RefreshItemCreationCommands();
                    
                    // Vérifier si le bouton devrait être activé
                    _logger?.LogDebug($"ContentTextBox_TextChanged: CanExecute après refresh = {_viewModel.FinalizeAndSaveNewItemCommand.CanExecute(null)}");
                    
                    // Si le bouton devrait être actif mais ne l'est pas, forcer sa mise à jour
                    if (!string.IsNullOrWhiteSpace(ContentTextBox.Text) && !SaveButton.IsEnabled)
                    {
                        _logger?.LogDebug($"ContentTextBox_TextChanged: Activation forcée du bouton SaveButton");
                        SaveButton.IsEnabled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Erreur lors du traitement du changement de texte : {ex.Message}", ex);
            }
        }

        // Note: Méthodes de gestion RequestCloseDialog supprimées - nous n'utilisons plus les événements du ViewModel

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            // Désabonnement final pour être sûr
            if (DataContext is ClipboardHistoryViewModel vm)
            {
                vm.RequestCloseDialog -= ViewModel_RequestCloseDialog;
            }
            base.OnClosing(e);
        }
    }
} 