using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ClipboardPlus.Tests.Unit.Core.Services.Visibility
{
    /// <summary>
    /// Tests d'intégration pour l'architecture SOLID de visibilité complète
    /// Couvre les scénarios complexes et la prévention de régression
    /// </summary>
    [TestFixture]
    public class VisibilityIntegrationTests
    {
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private TitleVisibilityRule _titleRule = null!;
        private TimestampVisibilityRule _timestampRule = null!;
        private VisibilityStateManager _visibilityManager = null!;


        [SetUp]
        public void SetUp()
        {
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockLoggingService = new Mock<ILoggingService>();

            
            // Créer les vraies règles pour tests d'intégration
            _titleRule = new TitleVisibilityRule(_mockLoggingService.Object);
            _timestampRule = new TimestampVisibilityRule(_mockLoggingService.Object);

            // Configuration par défaut
            _mockSettingsManager.Setup(s => s.HideItemTitle).Returns(false);
            _mockSettingsManager.Setup(s => s.HideTimestamp).Returns(false);

            _visibilityManager = new VisibilityStateManager(
                _titleRule,
                _timestampRule,
                _mockSettingsManager.Object,
                _mockLoggingService.Object
            );
        }

        #region Scénarios de Régression Critiques

        [Test]
        public void Scenario_RenameWithHiddenTitle_MustStayHidden()
        {
            // Arrange - Reproduit le bug de régression critique
            var item = new ClipboardItem 
            { 
                Id = 1, 
                CustomName = "Nom original",
                TextPreview = "Contenu test"
            };

            // Utilisateur choisit de cacher les titres
            _visibilityManager.UpdateGlobalTitleVisibility(false);
            
            // Vérifier état initial
            Assert.IsFalse(_visibilityManager.ShouldShowTitle(item), "Titre devrait être caché initialement");

            // Act - Simuler renommage
            item.CustomName = "Nouveau nom après renommage";
            var resultAfterRename = _visibilityManager.ShouldShowTitle(item);

            // Assert - CRITIQUE : Le titre doit rester caché
            Assert.IsFalse(resultAfterRename, 
                "RÉGRESSION CRITIQUE: Le titre doit rester caché après renommage si l'utilisateur a choisi de cacher les titres");
        }

        [Test]
        public void Scenario_MultipleRenames_ConsistentVisibility()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, CustomName = "Nom 1" };
            _visibilityManager.UpdateGlobalTitleVisibility(false); // Cacher

            // Act - Multiples renommages
            var results = new List<bool>();
            
            item.CustomName = "Nom 2";
            results.Add(_visibilityManager.ShouldShowTitle(item));
            
            item.CustomName = "Nom 3";
            results.Add(_visibilityManager.ShouldShowTitle(item));
            
            item.CustomName = null; // Suppression du nom
            results.Add(_visibilityManager.ShouldShowTitle(item));
            
            item.CustomName = "Nom 4";
            results.Add(_visibilityManager.ShouldShowTitle(item));

            // Assert
            Assert.IsTrue(results.All(r => r == false), 
                "Tous les renommages devraient maintenir la visibilité cachée");
        }

        [Test]
        public void Scenario_ToggleVisibilityDuringRename_CorrectBehavior()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, CustomName = "Nom original" };

            // Act & Assert - Séquence complexe
            
            // 1. État initial : visible
            Assert.IsTrue(_visibilityManager.ShouldShowTitle(item));
            
            // 2. Cacher pendant renommage
            _visibilityManager.UpdateGlobalTitleVisibility(false);
            item.CustomName = "Nom pendant cache";
            Assert.IsFalse(_visibilityManager.ShouldShowTitle(item));
            
            // 3. Réafficher après renommage
            _visibilityManager.UpdateGlobalTitleVisibility(true);
            Assert.IsTrue(_visibilityManager.ShouldShowTitle(item));
            
            // 4. Nouveau renommage avec visibilité activée
            item.CustomName = "Nom final";
            Assert.IsTrue(_visibilityManager.ShouldShowTitle(item));
        }

        #endregion

        #region Scénarios de Cohérence

        [Test]
        public void Scenario_MixedItemsWithDifferentStates()
        {
            // Arrange - Collection d'items avec états différents
            var items = new[]
            {
                new ClipboardItem { Id = 1, CustomName = "Titre 1" },
                new ClipboardItem { Id = 2, CustomName = null },
                new ClipboardItem { Id = 3, CustomName = "" },
                new ClipboardItem { Id = 4, CustomName = "   " },
                new ClipboardItem { Id = 5, CustomName = "Titre 5" }
            };

            // Test avec visibilité activée
            _visibilityManager.UpdateGlobalTitleVisibility(true);
            var visibleWhenEnabled = items.Select(i => _visibilityManager.ShouldShowTitle(i)).ToArray();
            
            // Test avec visibilité désactivée
            _visibilityManager.UpdateGlobalTitleVisibility(false);
            var visibleWhenDisabled = items.Select(i => _visibilityManager.ShouldShowTitle(i)).ToArray();

            // Assert
            Assert.AreEqual(new[] { true, false, false, false, true }, visibleWhenEnabled,
                "Seuls les items avec vrais titres devraient être visibles quand activé");
            Assert.AreEqual(new[] { false, false, false, false, false }, visibleWhenDisabled,
                "Aucun item ne devrait être visible quand désactivé");
        }

        [Test]
        public void Scenario_TimestampVisibility_IndependentOfTitle()
        {
            // Arrange
            var item = new ClipboardItem 
            { 
                Id = 1, 
                CustomName = "Titre test",
                Timestamp = DateTime.Now
            };

            // Test toutes les combinaisons
            var combinations = new[]
            {
                new { Title = true, Timestamp = true },
                new { Title = true, Timestamp = false },
                new { Title = false, Timestamp = true },
                new { Title = false, Timestamp = false }
            };

            foreach (var combo in combinations)
            {
                // Act
                _visibilityManager.UpdateGlobalTitleVisibility(combo.Title);
                _visibilityManager.UpdateGlobalTimestampVisibility(combo.Timestamp);
                
                var titleResult = _visibilityManager.ShouldShowTitle(item);
                var timestampResult = _visibilityManager.ShouldShowTimestamp(item);

                // Assert
                Assert.AreEqual(combo.Title, titleResult, 
                    $"Titre devrait être {combo.Title} pour combinaison Title={combo.Title}, Timestamp={combo.Timestamp}");
                Assert.AreEqual(combo.Timestamp, timestampResult,
                    $"Horodatage devrait être {combo.Timestamp} pour combinaison Title={combo.Title}, Timestamp={combo.Timestamp}");
            }
        }

        #endregion

        #region Scénarios d'Événements

        [Test]
        public void Scenario_EventSequence_CorrectOrder()
        {
            // Arrange
            var eventLog = new List<string>();
            
            _visibilityManager.VisibilityChanged += (sender, args) =>
            {
                eventLog.Add($"{args.Type}:{args.IsVisible}");
            };

            // Act - Séquence de changements
            _visibilityManager.UpdateGlobalTitleVisibility(false);
            _visibilityManager.UpdateGlobalTimestampVisibility(false);
            _visibilityManager.UpdateGlobalTitleVisibility(true);
            _visibilityManager.UpdateGlobalTimestampVisibility(true);

            // Assert
            var expectedSequence = new[]
            {
                "Title:False",
                "Timestamp:False", 
                "Title:True",
                "Timestamp:True"
            };
            
            Assert.AreEqual(expectedSequence, eventLog.ToArray(),
                "Séquence d'événements devrait être dans l'ordre correct");
        }

        [Test]
        public void Scenario_NoRedundantEvents()
        {
            // Arrange
            var eventCount = 0;
            _visibilityManager.VisibilityChanged += (sender, args) => eventCount++;

            // Act - Tentatives de changements redondants
            _visibilityManager.UpdateGlobalTitleVisibility(true); // Déjà true
            _visibilityManager.UpdateGlobalTitleVisibility(true); // Déjà true
            _visibilityManager.UpdateGlobalTimestampVisibility(true); // Déjà true
            _visibilityManager.UpdateGlobalTimestampVisibility(true); // Déjà true

            // Assert
            Assert.AreEqual(0, eventCount, "Aucun événement ne devrait être déclenché pour des valeurs inchangées");
        }

        #endregion

        #region Scénarios de Performance

        [Test]
        public void Scenario_HighVolumeOperations_MaintainPerformance()
        {
            // Arrange
            var items = Enumerable.Range(1, 1000)
                .Select(i => new ClipboardItem { Id = i, CustomName = $"Item {i}" })
                .ToArray();

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act - Opérations en volume
            foreach (var item in items)
            {
                _visibilityManager.ShouldShowTitle(item);
                _visibilityManager.ShouldShowTimestamp(item);
            }

            // Changements d'état
            _visibilityManager.UpdateGlobalTitleVisibility(false);
            _visibilityManager.UpdateGlobalTimestampVisibility(false);

            // Nouvelle vérification
            foreach (var item in items)
            {
                _visibilityManager.ShouldShowTitle(item);
                _visibilityManager.ShouldShowTimestamp(item);
            }

            stopwatch.Stop();

            // Assert
            Assert.Less(stopwatch.ElapsedMilliseconds, 1000, 
                "Opérations en volume devraient prendre moins de 1 seconde");
        }

        #endregion

        #region Scénarios de Synchronisation Settings

        [Test]
        public void Scenario_SettingsSync_BidirectionalConsistency()
        {
            // Test 1: Manager → Settings
            _visibilityManager.UpdateGlobalTitleVisibility(false);
            _mockSettingsManager.VerifySet(s => s.HideItemTitle = true, Times.Once);

            _visibilityManager.UpdateGlobalTimestampVisibility(false);
            _mockSettingsManager.VerifySet(s => s.HideTimestamp = true, Times.Once);

            // Test 2: Settings → Manager
            _visibilityManager.UpdateTitleVisibilityFromSettings(true); // Hide = true
            Assert.IsFalse(_visibilityManager.GlobalTitleVisibility);

            _visibilityManager.UpdateTimestampVisibilityFromSettings(true); // Hide = true
            Assert.IsFalse(_visibilityManager.GlobalTimestampVisibility);

            // Test 3: Cohérence après cycles
            _visibilityManager.UpdateGlobalTitleVisibility(true);
            _visibilityManager.UpdateTitleVisibilityFromSettings(false); // Hide = false
            Assert.IsTrue(_visibilityManager.GlobalTitleVisibility);
        }

        #endregion



        #region Scénarios de Robustesse

        [Test]
        public void Scenario_ExceptionResilience_ContinuesOperation()
        {
            // Arrange - Simuler exception dans logging
            _mockLoggingService.Setup(l => l.LogInfo(It.IsAny<string>()))
                              .Throws(new Exception("Logging failed"));

            var item = new ClipboardItem { Id = 1, CustomName = "Test" };

            // Act & Assert - Opérations devraient continuer malgré les exceptions
            Assert.DoesNotThrow(() => _visibilityManager.ShouldShowTitle(item));
            Assert.DoesNotThrow(() => _visibilityManager.UpdateGlobalTitleVisibility(false));
            Assert.DoesNotThrow(() => _visibilityManager.ShouldShowTitle(item));
        }

        [Test]
        public void Scenario_StateConsistency_AfterExceptions()
        {
            // Arrange
            var item = new ClipboardItem { Id = 1, CustomName = "Test" };
            
            // Simuler exception dans settings
            _mockSettingsManager.SetupSet(s => s.HideItemTitle = It.IsAny<bool>())
                               .Throws(new Exception("Settings failed"));

            // Act - Opération qui devrait échouer en interne
            _visibilityManager.UpdateGlobalTitleVisibility(false);

            // Assert - État interne devrait rester cohérent
            var result = _visibilityManager.ShouldShowTitle(item);
            Assert.IsFalse(result, "État interne devrait être mis à jour malgré l'exception settings");
        }

        #endregion

        #region Scénarios de Validation Complète

        [Test]
        public void Scenario_CompleteWorkflow_AllFeatures()
        {
            // Arrange - Workflow complet d'utilisation
            var items = new[]
            {
                new ClipboardItem { Id = 1, CustomName = "Document 1", Timestamp = DateTime.Now.AddMinutes(-10) },
                new ClipboardItem { Id = 2, CustomName = null, Timestamp = DateTime.Now.AddMinutes(-5) },
                new ClipboardItem { Id = 3, CustomName = "Image copie", Timestamp = DateTime.Now }
            };

            var eventLog = new List<string>();
            _visibilityManager.VisibilityChanged += (s, e) => eventLog.Add($"{e.Type}:{e.IsVisible}");

            // Act & Assert - Workflow complet

            // 1. État initial - tout visible
            Assert.IsTrue(items.Where(i => !string.IsNullOrWhiteSpace(i.CustomName)).All(i => _visibilityManager.ShouldShowTitle(i)));
            Assert.IsTrue(items.All(i => _visibilityManager.ShouldShowTimestamp(i)));

            // 2. Cacher les titres
            _visibilityManager.UpdateGlobalTitleVisibility(false);
            Assert.IsTrue(items.All(i => !_visibilityManager.ShouldShowTitle(i)));
            Assert.IsTrue(items.All(i => _visibilityManager.ShouldShowTimestamp(i)));

            // 3. Cacher les horodatages
            _visibilityManager.UpdateGlobalTimestampVisibility(false);
            Assert.IsTrue(items.All(i => !_visibilityManager.ShouldShowTitle(i)));
            Assert.IsTrue(items.All(i => !_visibilityManager.ShouldShowTimestamp(i)));

            // 4. Renommer un élément
            items[1].CustomName = "Nouveau nom";
            Assert.IsFalse(_visibilityManager.ShouldShowTitle(items[1])); // Reste caché

            // 5. Réactiver les titres
            _visibilityManager.UpdateGlobalTitleVisibility(true);
            Assert.IsTrue(items.Where(i => !string.IsNullOrWhiteSpace(i.CustomName)).All(i => _visibilityManager.ShouldShowTitle(i)));

            // 6. Vérifier les événements
            Assert.AreEqual(new[] { "Title:False", "Timestamp:False", "Title:True" }, eventLog.ToArray());
        }

        #endregion
    }
}
