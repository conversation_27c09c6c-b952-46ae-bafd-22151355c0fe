using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using CommunityToolkit.Mvvm.ComponentModel;

namespace ClipboardPlus.Core.DataModels
{
    /// <summary>
    /// Type de données contenues dans un élément du presse-papiers.
    /// </summary>
    public enum ClipboardDataType
    {
        /// <summary>
        /// Texte brut
        /// </summary>
        Text,
        
        /// <summary>
        /// Image
        /// </summary>
        Image,
        
        /// <summary>
        /// Format HTML
        /// </summary>
        Html,
        
        /// <summary>
        /// Format RTF (Rich Text Format)
        /// </summary>
        Rtf,
        
        /// <summary>
        /// Chemin de fichier
        /// </summary>
        FilePath,
        
        /// <summary>
        /// Autres types non spécifiés
        /// </summary>
        Other
    }

    /// <summary>
    /// Représente un élément stocké dans l'historique du presse-papiers.
    /// </summary>
    public class ClipboardItem : ObservableObject
    {
        private long _id;
        /// <summary>
        /// Identifiant unique de l'élément (clé primaire en base de données).
        /// </summary>
        public long Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private DateTime _timestamp = DateTime.Now;
        /// <summary>
        /// Horodatage de la création/capture de l'élément.
        /// </summary>
        public DateTime Timestamp
        {
            get => _timestamp;
            set => SetProperty(ref _timestamp, value);
        }

        private ClipboardDataType _dataType;
        /// <summary>
        /// Type de données contenues dans l'élément.
        /// </summary>
        public ClipboardDataType DataType
        {
            get => _dataType;
            set => SetProperty(ref _dataType, value);
        }

        private string? _customName;
        /// <summary>
        /// Nom personnalisé attribué par l'utilisateur (facultatif).
        /// </summary>
        public string? CustomName
        {
            get => _customName;
            set => SetProperty(ref _customName, value);
        }

        private bool _isPinned;
        /// <summary>
        /// Indique si l'élément est épinglé (ne sera pas supprimé lors des purges automatiques).
        /// </summary>
        public bool IsPinned
        {
            get => _isPinned;
            set => SetProperty(ref _isPinned, value);
        }

        private int _orderIndex;
        /// <summary>
        /// Index d'ordre personnalisé pour le tri.
        /// </summary>
        public int OrderIndex
        {
            get => _orderIndex;
            set => SetProperty(ref _orderIndex, value);
        }

        private byte[]? _rawData;
        /// <summary>
        /// Données brutes du contenu du presse-papiers.
        /// </summary>
        public byte[]? RawData
        {
            get => _rawData;
            set => SetProperty(ref _rawData, value);
        }

        private string? _sourceApplication;
        /// <summary>
        /// Nom de l'application source (si disponible).
        /// </summary>
        public string? SourceApplication
        {
            get => _sourceApplication;
            set => SetProperty(ref _sourceApplication, value);
        }

        private string? _textPreview;
        /// <summary>
        /// Aperçu textuel du contenu pour l'affichage dans la liste.
        /// </summary>
        public string? TextPreview
        {
            get => _textPreview;
            set => SetProperty(ref _textPreview, value);
        }

        private System.Windows.Media.Imaging.BitmapSource? _thumbnailSource;
        /// <summary>
        /// Source de l'image miniature pour les éléments de type image.
        /// Non persisté directement en DB ; converti en byte[] lors de la sauvegarde/chargement.
        /// </summary>
        public System.Windows.Media.Imaging.BitmapSource? ThumbnailSource
        {
            get => _thumbnailSource;
            set => SetProperty(ref _thumbnailSource, value);
        }

        private bool _isTimestampVisible = true;
        /// <summary>
        /// Détermine si l'horodatage doit être visible dans l'interface pour cet élément.
        /// Cette propriété est contrôlée par le ViewModel parent.
        /// </summary>
        public bool IsTimestampVisible
        {
            get => _isTimestampVisible;
            set => SetProperty(ref _isTimestampVisible, value);
        }
        
        private bool _isTitleVisible = true;
        /// <summary>
        /// Détermine si le titre de l'élément doit être visible dans l'interface.
        /// Cette propriété est mise à jour en fonction des paramètres globaux et de la présence d'un titre.
        /// </summary>
        public bool IsTitleVisible
        {
            get
            {
                return _isTitleVisible;
            }
            set
            {
                if (SetProperty(ref _isTitleVisible, value))
                {
                    // Propriété mise à jour
                }
            }
        }

        /// <summary>
        /// Initialise une nouvelle instance de la classe ClipboardItem.
        /// </summary>
        public ClipboardItem()
        {
            Timestamp = DateTime.Now;
            DataType = ClipboardDataType.Text;
            OrderIndex = 0;
            IsPinned = false;
        }

        /// <summary>
        /// Crée une représentation textuelle de cet élément du presse-papiers.
        /// </summary>
        /// <returns>Une chaîne représentant l'élément.</returns>
        public override string ToString()
        {
            string displayName = !string.IsNullOrEmpty(CustomName) ? CustomName : $"Item {Id}";
            return $"{displayName} ({DataType}) - {Timestamp:g}";
        }

        /// <summary>
        /// Force la notification PropertyChanged pour une propriété spécifique.
        /// CORRECTION CRITIQUE : Permet de forcer la mise à jour de l'interface WPF.
        /// </summary>
        /// <param name="propertyName">Nom de la propriété à notifier</param>
        public void ForcePropertyChanged(string propertyName)
        {
            OnPropertyChanged(propertyName);
        }


    }
}