using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;

namespace ClipboardPlus.UI.Helpers
{
    /// <summary>
    /// Classe utilitaire pour les animations et effets visuels dans l'application
    /// </summary>
    public static class AnimationHelper
    {
        /// <summary>
        /// Méthode vide - plus d'animation de drop
        /// </summary>
        public static void ApplyDropAnimation(FrameworkElement element)
        {
            // Animation supprimée intentionnellement
            // Ne fait rien pour éviter les effets visuels intrusifs
        }

        /// <summary>
        /// Méthode vide - plus d'animation de hauteur
        /// </summary>
        public static void AnimateItemHeight(ItemsControl itemsControl, int itemIndex, bool isInsertion)
        {
            // Animation supprimée intentionnellement
            // Ne fait rien pour éviter les effets visuels intrusifs
        }

        /// <summary>
        /// Méthode vide - plus d'animation de pulsation
        /// </summary>
        public static void ApplyPulseAnimation(FrameworkElement element)
        {
            // Animation supprimée intentionnellement
            // Ne fait rien pour éviter les effets visuels intrusifs
        }
    }
} 