using System;
using System.Windows.Input;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests unitaires pour la nouvelle architecture de GlobalShortcutService.
    /// Ces tests valident le nouveau constructeur fusionné et les méthodes privées.
    /// </summary>
    [TestFixture]
    public class GlobalShortcutServiceNewArchitectureTests
    {
        private MockWindowsHotkeyApi _mockHotkeyApi = null!;
        private KeyCombination _validShortcut = null!;

        [SetUp]
        public void Initialize()
        {
            _mockHotkeyApi = new MockWindowsHotkeyApi();
            _validShortcut = new KeyCombination(ModifierKeys.Control | ModifierKeys.Alt, Key.V);
        }

        #region Tests du Nouveau Constructeur

        [Test]
        [Description("NOUVEAU: Constructeur avec raccourci par défaut doit initialiser immédiatement")]
        public void Constructor_WithDefaultShortcut_InitializesImmediately()
        {
            // Act
            var service = new GlobalShortcutService(_validShortcut, _mockHotkeyApi);

            // Assert
            Assert.That(service, Is.Not.Null);
            // Le service devrait être créé sans exception
        }

        [Test]
        [Description("NOUVEAU: Constructeur sans raccourci doit utiliser le mode legacy")]
        public void Constructor_WithoutShortcut_UsesLegacyMode()
        {
            // Act
            var service = new GlobalShortcutService(null, _mockHotkeyApi);

            // Assert
            Assert.That(service, Is.Not.Null);
            // Le service devrait être créé en mode legacy
        }

        [Test]
        [Description("NOUVEAU: Constructeur avec raccourci null doit utiliser le mode legacy")]
        public void Constructor_WithNullShortcut_UsesLegacyMode()
        {
            // Act
            var service = new GlobalShortcutService(null, _mockHotkeyApi);

            // Assert
            Assert.That(service, Is.Not.Null);
            // Pas d'exception, mode legacy activé
        }

        [Test]
        [Description("NOUVEAU: Constructeur avec DI doit fonctionner")]
        public void Constructor_WithDependencyInjection_Works()
        {
            // Act & Assert - Le constructeur moderne avec DI
            Assert.DoesNotThrow(() => new GlobalShortcutService(_validShortcut, _mockHotkeyApi));
        }

        #endregion

        #region Tests de Compatibilité

        [Test]
        [Description("COMPATIBILITÉ: InitializeAsync doit toujours fonctionner")]
        public void InitializeAsync_WithValidShortcut_StillWorks()
        {
            // Arrange
            var service = new GlobalShortcutService(null, _mockHotkeyApi);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await service.InitializeAsync(_validShortcut));
        }

        [Test]
        [Description("COMPATIBILITÉ: InitializeAsync avec null doit lever ArgumentNullException")]
        public void InitializeAsync_WithNullShortcut_ThrowsArgumentNullException()
        {
            // Arrange
            var service = new GlobalShortcutService(null, _mockHotkeyApi);

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentNullException>(() => service.InitializeAsync(null!));
            Assert.That(ex!.ParamName, Is.EqualTo("defaultShortcut"));
        }

        #endregion

        #region Tests de Robustesse

        [Test]
        [Description("ROBUSTESSE: Constructeur avec API null doit créer API par défaut")]
        public void Constructor_WithNullApi_CreatesDefaultApi()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new GlobalShortcutService(_validShortcut, null));
        }

        [Test]
        [Description("ROBUSTESSE: Constructeur doit gérer les erreurs d'initialisation")]
        public void Constructor_WithInitializationError_DoesNotThrow()
        {
            // Arrange - Utiliser un raccourci qui pourrait causer des problèmes
            var problematicShortcut = new KeyCombination(ModifierKeys.None, Key.None);

            // Act & Assert
            Assert.DoesNotThrow(() => new GlobalShortcutService(problematicShortcut, _mockHotkeyApi));
        }

        #endregion

        #region Tests de Performance

        [Test]
        [Description("PERFORMANCE: Constructeur doit se terminer rapidement")]
        [Timeout(1000)] // 1 seconde maximum
        public void Constructor_WithValidShortcut_CompletesQuickly()
        {
            // Act
            var service = new GlobalShortcutService(_validShortcut, _mockHotkeyApi);

            // Assert
            Assert.That(service, Is.Not.Null);
            Assert.Pass("Constructeur terminé dans le délai imparti");
        }

        #endregion

        #region Tests de Validation

        [Test]
        [Description("VALIDATION: Service créé avec raccourci doit être prêt")]
        public void Constructor_WithShortcut_ServiceIsReady()
        {
            // Act
            var service = new GlobalShortcutService(_validShortcut, _mockHotkeyApi);

            // Assert
            Assert.That(service, Is.Not.Null);
            // Le service devrait être prêt à utiliser immédiatement
            Assert.That(service.IsValidShortcut(_validShortcut), Is.True);
        }

        [Test]
        [Description("VALIDATION: Méthodes publiques doivent toujours fonctionner")]
        public void PublicMethods_AfterConstruction_Work()
        {
            // Arrange
            var service = new GlobalShortcutService(_validShortcut, _mockHotkeyApi);

            // Act & Assert
            Assert.DoesNotThrow(() => service.IsValidShortcut(_validShortcut));
            Assert.DoesNotThrow(() => service.UnregisterShortcut());
        }

        #endregion

        #region Tests d'Intégration

        [Test]
        [Description("INTÉGRATION: Nouveau constructeur + TryRegisterShortcutAsync")]
        public void Constructor_ThenTryRegister_Works()
        {
            // Arrange
            var service = new GlobalShortcutService(null, _mockHotkeyApi); // Mode legacy
            var newShortcut = new KeyCombination(ModifierKeys.Control, Key.C);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await service.TryRegisterShortcutAsync(newShortcut));
        }

        #endregion
    }

    /// <summary>
    /// Mock amélioré de IWindowsHotkeyApi pour les tests de la nouvelle architecture.
    /// </summary>
    public class MockWindowsHotkeyApiEnhanced : IWindowsHotkeyApi
    {
        public bool RegisterHotKeyCalled { get; private set; }
        public bool UnregisterHotKeyCalled { get; private set; }
        public int RegisterCallCount { get; private set; }
        public int UnregisterCallCount { get; private set; }

        public bool RegisterHotKey(IntPtr hWnd, int id, uint fsModifiers, uint vk)
        {
            RegisterHotKeyCalled = true;
            RegisterCallCount++;
            return true; // Simuler un succès
        }

        public bool UnregisterHotKey(IntPtr hWnd, int id)
        {
            UnregisterHotKeyCalled = true;
            UnregisterCallCount++;
            return true; // Simuler un succès
        }

        public void Reset()
        {
            RegisterHotKeyCalled = false;
            UnregisterHotKeyCalled = false;
            RegisterCallCount = 0;
            UnregisterCallCount = 0;
        }
    }
}
