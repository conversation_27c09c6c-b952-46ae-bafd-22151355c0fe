using System;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Interface pour permettre au DeletionService de gérer l'état IsOperationInProgress du ViewModel.
    /// Respecte le principe SOLID d'inversion de dépendance.
    /// </summary>
    public interface IViewModelOperationState
    {
        /// <summary>
        /// Indique si une opération est actuellement en cours.
        /// </summary>
        bool IsOperationInProgress { get; set; }
    }
}
