using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Implémentation du service de diagnostic des fenêtres système.
    /// </summary>
    public class WindowDiagnosticService : IWindowDiagnosticService
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du service de diagnostic des fenêtres.
        /// </summary>
        /// <param name="loggingService">Service de journalisation</param>
        public WindowDiagnosticService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <inheritdoc />
        public WindowDiagnosticResult AnalyzeCurrentWindowState()
        {
            _loggingService?.LogInfo("[VERIFICATION_TEST_2025] WindowDiagnosticService.AnalyzeCurrentWindowState APPELÉE");
            var stopwatch = Stopwatch.StartNew();
            var result = new WindowDiagnosticResult
            {
                Timestamp = DateTime.Now
            };

            try
            {
                _loggingService?.LogInfo("🔍 [WindowDiagnosticService] Début du diagnostic des fenêtres système");

                // Obtenir toutes les fenêtres WPF
                var allWindows = GetAllWpfWindows();
                var windowInfos = new List<WindowInfo>();

                foreach (var window in allWindows)
                {
                    try
                    {
                        var windowInfo = GetWindowInfo(window);
                        windowInfos.Add(windowInfo);
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogWarning($"⚠️ [WindowDiagnosticService] Erreur lors de l'analyse de la fenêtre {window?.GetType().Name}: {ex.Message}");
                    }
                }

                result.AllWindows = windowInfos.AsReadOnly();

                // Identifier la fenêtre active
                result.ActiveWindow = allWindows.SingleOrDefault(x => x.IsActive);
                if (result.ActiveWindow != null)
                {
                    result.ActiveWindowInfo = windowInfos.FirstOrDefault(w => w.Window == result.ActiveWindow);
                }

                stopwatch.Stop();
                result.DiagnosticDurationMs = stopwatch.Elapsed.TotalMilliseconds;
                result.IsSuccessful = true;

                _loggingService?.LogInfo($"🔍 [WindowDiagnosticService] DIAGNOSTIC: {result.TotalWindowCount} fenêtres trouvées en {result.DiagnosticDurationMs:F1}ms:");
                
                foreach (var windowInfo in windowInfos)
                {
                    _loggingService?.LogInfo($"   - {windowInfo.WindowTypeName}: '{windowInfo.Title}' (IsActive: {windowInfo.IsActive}, Owner: {windowInfo.OwnerTypeName ?? "null"})");
                }

                if (result.ActiveWindow != null)
                {
                    _loggingService?.LogInfo($"🎯 [WindowDiagnosticService] Fenêtre active détectée: {result.ActiveWindowInfo?.WindowTypeName ?? "UNKNOWN"} - '{result.ActiveWindow.Title}'");
                }
                else
                {
                    _loggingService?.LogInfo("🎯 [WindowDiagnosticService] Aucune fenêtre active détectée");
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.DiagnosticDurationMs = stopwatch.Elapsed.TotalMilliseconds;
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;

                _loggingService?.LogError($"❌ [WindowDiagnosticService] Erreur lors du diagnostic des fenêtres: {ex.Message}", ex);
                return result;
            }
        }

        /// <inheritdoc />
        public async Task<WindowDiagnosticResult> AnalyzeCurrentWindowStateAsync()
        {
            return await Task.Run(() => AnalyzeCurrentWindowState());
        }

        /// <inheritdoc />
        public WindowInfo GetWindowInfo(Window window)
        {
            if (window == null)
            {
                throw new ArgumentNullException(nameof(window));
            }

            try
            {
                var windowInfo = new WindowInfo
                {
                    Window = window,
                    WindowTypeName = window.GetType().Name,
                    WindowTypeFullName = window.GetType().FullName ?? window.GetType().Name,
                    Title = window.Title ?? string.Empty,
                    IsActive = window.IsActive,
                    IsVisible = window.IsVisible,
                    IsLoaded = window.IsLoaded,
                    WindowState = window.WindowState,
                    Timestamp = DateTime.Now
                };

                // Informations sur le propriétaire
                if (window.Owner != null)
                {
                    windowInfo.OwnerTypeName = window.Owner.GetType().Name;
                    windowInfo.OwnerTypeFullName = window.Owner.GetType().FullName ?? window.Owner.GetType().Name;
                }

                return windowInfo;
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"⚠️ [WindowDiagnosticService] Erreur lors de la collecte d'informations pour la fenêtre: {ex.Message}");
                
                // Retourner des informations minimales en cas d'erreur
                return new WindowInfo
                {
                    Window = window,
                    WindowTypeName = window?.GetType().Name ?? "UNKNOWN",
                    WindowTypeFullName = window?.GetType().FullName ?? "UNKNOWN",
                    Title = "ERROR",
                    Timestamp = DateTime.Now
                };
            }
        }

        /// <summary>
        /// Obtient toutes les fenêtres WPF actuellement ouvertes.
        /// </summary>
        /// <returns>Liste des fenêtres WPF</returns>
        private List<Window> GetAllWpfWindows()
        {
            try
            {
                // Vérifier si Application.Current est disponible (peut être null dans les tests)
                if (System.Windows.Application.Current?.Windows == null)
                {
                    _loggingService?.LogWarning("⚠️ [WindowDiagnosticService] Application.Current.Windows est null - probablement dans un contexte de test");
                    return new List<Window>();
                }

                return System.Windows.Application.Current.Windows.OfType<Window>().ToList();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"❌ [WindowDiagnosticService] Erreur lors de l'énumération des fenêtres: {ex.Message}", ex);
                return new List<Window>();
            }
        }
    }
}
