using System;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Implementations;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.Implementations
{
    [TestFixture]
    public class ClipboardItemProcessorTests
    {
        private ClipboardItemProcessor _processor;
        private Mock<IPersistenceService> _mockPersistenceService;
        private Mock<ILoggingService> _mockLoggingService;

        [SetUp]
        public void Setup()
        {
            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockLoggingService = new Mock<ILoggingService>();
            _processor = new ClipboardItemProcessor(_mockPersistenceService.Object, _mockLoggingService.Object);
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithNullPersistenceService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new ClipboardItemProcessor(null!, _mockLoggingService.Object));
        }

        [Test]
        public void Constructor_WithNullLoggingService_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => 
                new ClipboardItemProcessor(_mockPersistenceService.Object, null));
        }

        #endregion

        #region ProcessNewItemAsync Tests

        [Test]
        public void ProcessNewItemAsync_WithNullItem_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentNullException>(() =>
                _processor.ProcessNewItemAsync(null!));

            Assert.That(ex.ParamName, Is.EqualTo("item"));
        }

        [Test]
        public async Task ProcessNewItemAsync_WithValidItem_ReturnsGeneratedId()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };
            var expectedId = 42L;

            _mockPersistenceService.Setup(m => m.InsertClipboardItemAsync(item))
                .ReturnsAsync(expectedId);

            // Act
            var result = await _processor.ProcessNewItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(expectedId));
            Assert.That(item.Id, Is.EqualTo(expectedId), "L'ID doit être assigné à l'élément");
            
            _mockPersistenceService.Verify(m => m.InsertClipboardItemAsync(item), Times.Once);
        }

        [Test]
        public async Task ProcessNewItemAsync_WhenInsertionFails_ReturnsZero()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            _mockPersistenceService.Setup(m => m.InsertClipboardItemAsync(item))
                .ReturnsAsync(0); // Simulation d'échec

            // Act
            var result = await _processor.ProcessNewItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(0));
            
            _mockPersistenceService.Verify(m => m.InsertClipboardItemAsync(item), Times.Once);
        }

        [Test]
        public async Task ProcessNewItemAsync_WhenExceptionThrown_ReturnsZero()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            _mockPersistenceService.Setup(m => m.InsertClipboardItemAsync(item))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _processor.ProcessNewItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(0));
            
            _mockPersistenceService.Verify(m => m.InsertClipboardItemAsync(item), Times.Once);
        }

        #endregion

        #region ProcessExistingItemAsync Tests

        [Test]
        public void ProcessExistingItemAsync_WithNullItem_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentNullException>(() =>
                _processor.ProcessExistingItemAsync(null!, DateTime.Now));

            Assert.That(ex.ParamName, Is.EqualTo("existingItem"));
        }

        [Test]
        public void ProcessExistingItemAsync_WithInvalidId_ThrowsArgumentException()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 0, // ID invalide
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now.AddMinutes(-10)
            };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _processor.ProcessExistingItemAsync(item, DateTime.Now));

            Assert.That(ex.ParamName, Is.EqualTo("existingItem"));
            Assert.That(ex.Message, Does.Contain("ID valide"));
        }

        [Test]
        public async Task ProcessExistingItemAsync_WithValidItem_UpdatesTimestampAndReturnsId()
        {
            // Arrange
            var originalTimestamp = DateTime.Now.AddMinutes(-10);
            var newTimestamp = DateTime.Now;
            var item = new ClipboardItem
            {
                Id = 42,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = originalTimestamp
            };

            _mockPersistenceService.Setup(m => m.UpdateClipboardItemAsync(item))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _processor.ProcessExistingItemAsync(item, newTimestamp);

            // Assert
            Assert.That(result, Is.EqualTo(item.Id));
            Assert.That(item.Timestamp, Is.EqualTo(newTimestamp), "Le timestamp doit être mis à jour");
            
            _mockPersistenceService.Verify(m => m.UpdateClipboardItemAsync(item), Times.Once);
        }

        [Test]
        public async Task ProcessExistingItemAsync_WhenUpdateFails_ReturnsZero()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 42,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now.AddMinutes(-10)
            };

            _mockPersistenceService.Setup(m => m.UpdateClipboardItemAsync(item))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _processor.ProcessExistingItemAsync(item, DateTime.Now);

            // Assert
            Assert.That(result, Is.EqualTo(0));
            
            _mockPersistenceService.Verify(m => m.UpdateClipboardItemAsync(item), Times.Once);
        }

        [Test]
        public async Task ProcessExistingItemAsync_PreservesOriginalTimestampOnFailure()
        {
            // Arrange
            var originalTimestamp = DateTime.Now.AddMinutes(-10);
            var newTimestamp = DateTime.Now;
            var item = new ClipboardItem
            {
                Id = 42,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = originalTimestamp
            };

            _mockPersistenceService.Setup(m => m.UpdateClipboardItemAsync(item))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            await _processor.ProcessExistingItemAsync(item, newTimestamp);

            // Assert
            // Note: Le timestamp est modifié avant l'appel à la base de données,
            // donc il sera toujours mis à jour même en cas d'échec
            Assert.That(item.Timestamp, Is.EqualTo(newTimestamp));
        }

        #endregion

        #region Logging Tests

        [Test]
        public async Task ProcessNewItemAsync_LogsOperationStart()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            _mockPersistenceService.Setup(m => m.InsertClipboardItemAsync(item))
                .ReturnsAsync(42L);

            // Act
            await _processor.ProcessNewItemAsync(item);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("ClipboardItemProcessor.ProcessNewItemAsync - Début traitement"))),
                Times.Once);
        }

        [Test]
        public async Task ProcessNewItemAsync_LogsSuccessfulInsertion()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            _mockPersistenceService.Setup(m => m.InsertClipboardItemAsync(item))
                .ReturnsAsync(42L);

            // Act
            await _processor.ProcessNewItemAsync(item);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("inséré avec succès"))),
                Times.Once);
        }

        [Test]
        public async Task ProcessExistingItemAsync_LogsOperationStart()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 42,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now.AddMinutes(-10)
            };

            _mockPersistenceService.Setup(m => m.UpdateClipboardItemAsync(item))
                .Returns(Task.CompletedTask);

            // Act
            await _processor.ProcessExistingItemAsync(item, DateTime.Now);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("ClipboardItemProcessor.ProcessExistingItemAsync - Début traitement"))),
                Times.Once);
        }

        [Test]
        public async Task ProcessExistingItemAsync_LogsSuccessfulUpdate()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 42,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now.AddMinutes(-10)
            };

            _mockPersistenceService.Setup(m => m.UpdateClipboardItemAsync(item))
                .Returns(Task.CompletedTask);

            // Act
            await _processor.ProcessExistingItemAsync(item, DateTime.Now);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("mis à jour avec succès"))),
                Times.Once);
        }

        #endregion
    }
}
