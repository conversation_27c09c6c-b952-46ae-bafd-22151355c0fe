using System;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Interface pour l'orchestrateur principal du système de notification.
    /// Responsabilité unique : orchestrer l'initialisation complète du système tray et gérer son cycle de vie.
    /// </summary>
    public interface ISystemTrayOrchestrator : IDisposable
    {
        /// <summary>
        /// Initialise complètement le système de notification en orchestrant tous les composants.
        /// </summary>
        void Initialize();

        /// <summary>
        /// Version V2 de Initialize qui utilise l'injection de dépendances explicite
        /// au lieu du Service Locator pattern.
        /// Cette méthode utilise les services IHistoryWindowService et ISettingsWindowService injectés.
        /// </summary>
        void Initialize_V2();

        /// <summary>
        /// Vérifie si le système de notification est actuellement initialisé.
        /// </summary>
        /// <returns>True si initialisé, false sinon.</returns>
        bool IsInitialized { get; }

        /// <summary>
        /// Affiche une notification via le système initialisé.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="iconType">Le type d'icône à afficher.</param>
        void ShowNotification(string title, string message, System.Windows.Forms.ToolTipIcon iconType);
    }
}
