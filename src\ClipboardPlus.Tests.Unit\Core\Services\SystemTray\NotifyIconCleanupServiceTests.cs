using System;
using System.Threading;
using System.Windows.Forms;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;

namespace ClipboardPlus.Tests.Unit.Core.Services.SystemTray
{
    /// <summary>
    /// Tests unitaires pour NotifyIconCleanupService.
    /// Vérifie la responsabilité unique : nettoyage sécurisé des instances NotifyIcon.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class NotifyIconCleanupServiceTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private NotifyIconCleanupService _cleanupService;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _cleanupService = new NotifyIconCleanupService(_mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new NotifyIconCleanupService(null));
        }

        [Test]
        public void CleanupExistingIcon_WithNullIcon_LogsNoCleanupNeeded()
        {
            // Act
            _cleanupService.CleanupExistingIcon(null);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo("NotifyIconCleanupService: Aucune instance existante à nettoyer."),
                Times.Once,
                "Should log that no cleanup is needed for null icon");
        }

        [Test]
        public void CleanupExistingIcon_WithValidIcon_DisposesIcon()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();
            var originalVisible = notifyIcon.Visible;

            // Act
            _cleanupService.CleanupExistingIcon(notifyIcon);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogWarning("NotifyIconCleanupService: Instance NotifyIcon existante détectée - Nettoyage en cours..."),
                Times.Once,
                "Should log cleanup warning");

            _mockLoggingService.Verify(
                x => x.LogInfo("NotifyIconCleanupService: Nettoyage terminé avec succès."),
                Times.Once,
                "Should log successful cleanup");
        }

        [Test]
        public void CleanupExistingIcon_WithVisibleIcon_HidesIconBeforeDisposal()
        {
            // Arrange
            var notifyIcon = new NotifyIcon { Visible = true };

            // Act
            _cleanupService.CleanupExistingIcon(notifyIcon);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo("NotifyIconCleanupService: Masquage de l'icône existante..."),
                Times.Once,
                "Should log icon hiding when icon is visible");
        }

        [Test]
        public void CleanupExistingIcon_WithInvisibleIcon_DoesNotLogHiding()
        {
            // Arrange
            var notifyIcon = new NotifyIcon { Visible = false };

            // Act
            _cleanupService.CleanupExistingIcon(notifyIcon);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo("NotifyIconCleanupService: Masquage de l'icône existante..."),
                Times.Never,
                "Should not log icon hiding when icon is already invisible");
        }

        [Test]
        public void CleanupExistingIcon_LogsEventCleanup()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act
            _cleanupService.CleanupExistingIcon(notifyIcon);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo("NotifyIconCleanupService: Nettoyage des gestionnaires d'événements..."),
                Times.Once,
                "Should log event handler cleanup");
        }

        [Test]
        public void CleanupExistingIcon_LogsDisposal()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act
            _cleanupService.CleanupExistingIcon(notifyIcon);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo("NotifyIconCleanupService: Disposition de l'instance NotifyIcon..."),
                Times.Once,
                "Should log disposal operation");
        }

        [Test]
        public void CleanupExistingIcon_WithException_LogsErrorButDoesNotThrow()
        {
            // Arrange - Créer un service qui lève une exception lors du logging
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo("NotifyIconCleanupService: Disposition de l'instance NotifyIcon..."))
                             .Throws(new InvalidOperationException("Test exception"));

            var cleanupService = new NotifyIconCleanupService(mockLoggingService.Object);
            var notifyIcon = new NotifyIcon();

            // Act & Assert
            Assert.DoesNotThrow(() => cleanupService.CleanupExistingIcon(notifyIcon),
                "CleanupExistingIcon should not throw exception even if logging fails");

            // Vérifier que l'erreur a été loggée
            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors du nettoyage")),
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when cleanup fails");
        }

        [Test]
        public void RequiresCleanup_WithNullIcon_ReturnsFalse()
        {
            // Act
            bool result = _cleanupService.RequiresCleanup(null);

            // Assert
            Assert.That(result, Is.False, "Should return false for null icon");
        }

        [Test]
        public void RequiresCleanup_WithValidIcon_ReturnsTrue()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act
            bool result = _cleanupService.RequiresCleanup(notifyIcon);

            // Assert
            Assert.That(result, Is.True, "Should return true for valid icon");
            
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Vérification du besoin de nettoyage") && s.Contains("True"))),
                Times.Once,
                "Should log cleanup requirement check");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void RequiresCleanup_WithException_ReturnsTrueAndLogsError()
        {
            // Arrange - Créer un mock qui lève une exception
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo(It.IsAny<string>()))
                             .Throws(new InvalidOperationException("Test exception"));

            var cleanupService = new NotifyIconCleanupService(mockLoggingService.Object);
            var notifyIcon = new NotifyIcon();

            // Act
            bool result = cleanupService.RequiresCleanup(notifyIcon);

            // Assert
            Assert.That(result, Is.True, "Should return true when exception occurs (safe default)");
            
            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de la vérification du besoin de nettoyage")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when exception occurs");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void CleanupService_IsStateless()
        {
            // Arrange
            var notifyIcon1 = new NotifyIcon();
            var notifyIcon2 = new NotifyIcon();

            // Act - Appeler plusieurs fois les méthodes
            bool requires1 = _cleanupService.RequiresCleanup(notifyIcon1);
            bool requires2 = _cleanupService.RequiresCleanup(notifyIcon2);

            _cleanupService.CleanupExistingIcon(notifyIcon1);
            _cleanupService.CleanupExistingIcon(notifyIcon2);

            // Assert - Le service doit être sans état
            Assert.That(requires1, Is.EqualTo(requires2), "Service should be stateless and return consistent results");
        }

        [TearDown]
        public void TearDown()
        {
            // Nettoyer les ressources si nécessaire
        }
    }
}
