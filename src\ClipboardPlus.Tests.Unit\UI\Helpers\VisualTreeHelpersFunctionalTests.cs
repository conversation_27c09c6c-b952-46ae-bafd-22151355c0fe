using System;
using NUnit.Framework;
using ClipboardPlus.UI.Helpers;
using System.Windows;
using System.Windows.Controls;
using System.Reflection;
using System.Linq;
using System.Collections.Generic;

namespace ClipboardPlus.Tests.Unit.UI.Helpers
{
    [TestFixture]
    public class VisualTreeHelpersFunctionalTests
    {
        [Test]
        public void VisualTreeHelpers_FindAncestor_WithNullInput_ReturnsNull()
        {
            // Arrange & Act - Tester avec input null (VRAI CODE EXÉCUTÉ)
            try
            {
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
                var result = VisualTreeHelpers.FindAncestor<Button>(null);
#pragma warning restore CS8625
                Assert.IsNull(result, "FindAncestor avec null devrait retourner null");
            }
            catch (ArgumentNullException)
            {
                // Acceptable si la méthode valide les paramètres
                Assert.IsTrue(true, "ArgumentNullException est acceptable pour null input");
            }
            catch (Exception ex)
            {
                // Acceptable si nécessite un contexte UI
                Assert.IsTrue(ex.Message.Contains("UI") || ex.Message.Contains("thread") || ex.Message.Contains("STA"), 
                    $"Exception inattendue: {ex.Message}");
            }
        }

        [Test]
        public void VisualTreeHelpers_FindDescendant_WithNullInput_ReturnsNull()
        {
            // Arrange & Act - Tester avec input null (VRAI CODE EXÉCUTÉ)
            try
            {
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
                var result = VisualTreeHelpers.FindDescendant<Button>(null);
#pragma warning restore CS8625
                Assert.IsNull(result, "FindDescendant avec null devrait retourner null");
            }
            catch (ArgumentNullException)
            {
                // Acceptable si la méthode valide les paramètres
                Assert.IsTrue(true, "ArgumentNullException est acceptable pour null input");
            }
            catch (Exception ex)
            {
                // Acceptable si nécessite un contexte UI
                Assert.IsTrue(ex.Message.Contains("UI") || ex.Message.Contains("thread") || ex.Message.Contains("STA"), 
                    $"Exception inattendue: {ex.Message}");
            }
        }

        [Test]
        public void VisualTreeHelpers_FindAllDescendants_WithNullInput_HandlesGracefully()
        {
            // Arrange & Act - Tester avec input null (VRAI CODE EXÉCUTÉ)
            try
            {
                var results = new List<Button>();
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
                VisualTreeHelpers.FindAllDescendants<Button>(null, results);
#pragma warning restore CS8625

                // La liste devrait rester vide
                Assert.AreEqual(0, results.Count,
                    "FindAllDescendants avec null ne devrait rien ajouter à la liste");
            }
            catch (ArgumentNullException)
            {
                // Acceptable si la méthode valide les paramètres
                Assert.IsTrue(true, "ArgumentNullException est acceptable pour null input");
            }
            catch (Exception ex)
            {
                // Acceptable si nécessite un contexte UI
                Assert.IsTrue(ex.Message.Contains("UI") || ex.Message.Contains("thread") || ex.Message.Contains("STA"),
                    $"Exception inattendue: {ex.Message}");
            }
        }

        [Test]
        public void VisualTreeHelpers_HasCorrectMethodSignatures()
        {
            // Arrange & Act - Vérifier les signatures des méthodes (VRAI CODE EXÉCUTÉ)
            var helpersType = typeof(VisualTreeHelpers);
            
            // Vérifier FindAncestor
            var findAncestorMethod = helpersType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .FirstOrDefault(m => m.Name == "FindAncestor" && m.IsGenericMethod);
            
            Assert.IsNotNull(findAncestorMethod, "FindAncestor method should exist");
            Assert.IsTrue(findAncestorMethod!.IsStatic, "FindAncestor should be static");
            Assert.IsTrue(findAncestorMethod.IsGenericMethod, "FindAncestor should be generic");
            
            var findAncestorParams = findAncestorMethod.GetParameters();
            Assert.AreEqual(1, findAncestorParams.Length, "FindAncestor should have 1 parameter");
            Assert.AreEqual(typeof(DependencyObject), findAncestorParams[0].ParameterType, 
                "FindAncestor parameter should be DependencyObject");
            
            // Vérifier FindDescendant
            var findDescendantMethod = helpersType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .FirstOrDefault(m => m.Name == "FindDescendant" && m.IsGenericMethod);
            
            Assert.IsNotNull(findDescendantMethod, "FindDescendant method should exist");
            Assert.IsTrue(findDescendantMethod!.IsStatic, "FindDescendant should be static");
            Assert.IsTrue(findDescendantMethod.IsGenericMethod, "FindDescendant should be generic");
            
            // Vérifier FindAllDescendants
            var findAllDescendantsMethod = helpersType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .FirstOrDefault(m => m.Name == "FindAllDescendants" && m.IsGenericMethod);
            
            Assert.IsNotNull(findAllDescendantsMethod, "FindAllDescendants method should exist");
            Assert.IsTrue(findAllDescendantsMethod!.IsStatic, "FindAllDescendants should be static");
            Assert.IsTrue(findAllDescendantsMethod.IsGenericMethod, "FindAllDescendants should be generic");
        }

        [Test]
        public void VisualTreeHelpers_IsStaticClass()
        {
            // Arrange & Act - Vérifier que c'est une classe statique (VRAI CODE EXÉCUTÉ)
            var helpersType = typeof(VisualTreeHelpers);
            
            Assert.IsTrue(helpersType.IsClass, "VisualTreeHelpers should be a class");
            Assert.IsTrue(helpersType.IsAbstract && helpersType.IsSealed, 
                "VisualTreeHelpers should be static (abstract + sealed)");
        }

        [Test]
        public void VisualTreeHelpers_GenericConstraints_AreCorrect()
        {
            // Arrange & Act - Vérifier les contraintes génériques (VRAI CODE EXÉCUTÉ)
            var helpersType = typeof(VisualTreeHelpers);
            var methods = helpersType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.IsGenericMethod);
            
            foreach (var method in methods)
            {
                var genericArgs = method.GetGenericArguments();
                Assert.AreEqual(1, genericArgs.Length, $"Method {method.Name} should have 1 generic argument");
                
                var constraints = genericArgs[0].GetGenericParameterConstraints();
                Assert.IsTrue(constraints.Length > 0, $"Method {method.Name} should have generic constraints");
                
                // Vérifier que la contrainte inclut DependencyObject ou une classe dérivée
                bool hasDependencyObjectConstraint = constraints.Any(c => 
                    c == typeof(DependencyObject) || c.IsSubclassOf(typeof(DependencyObject)));
                
                Assert.IsTrue(hasDependencyObjectConstraint, 
                    $"Method {method.Name} should have DependencyObject constraint");
            }
        }

        [Test]
        public void VisualTreeHelpers_MethodsDoNotThrow_WithValidTypes()
        {
            // Arrange & Act - Tester que les méthodes ne plantent pas avec des types valides
            try
            {
                var helpersType = typeof(VisualTreeHelpers);
                
                // Tester que les méthodes génériques peuvent être construites avec des types valides
                var findAncestorMethod = helpersType.GetMethod("FindAncestor", BindingFlags.Public | BindingFlags.Static);
                if (findAncestorMethod != null)
                {
                    var buttonMethod = findAncestorMethod.MakeGenericMethod(typeof(Button));
                    Assert.IsNotNull(buttonMethod, "Should be able to create FindAncestor<Button>");
                    
                    var panelMethod = findAncestorMethod.MakeGenericMethod(typeof(Panel));
                    Assert.IsNotNull(panelMethod, "Should be able to create FindAncestor<Panel>");
                }
                
                var findDescendantMethod = helpersType.GetMethod("FindDescendant", BindingFlags.Public | BindingFlags.Static);
                if (findDescendantMethod != null)
                {
                    var textBoxMethod = findDescendantMethod.MakeGenericMethod(typeof(TextBox));
                    Assert.IsNotNull(textBoxMethod, "Should be able to create FindDescendant<TextBox>");
                }
                
                Assert.IsTrue(true, "All method constructions succeeded");
            }
            catch (Exception ex)
            {
                // Acceptable si nécessite un contexte UI
                Assert.IsTrue(ex.Message.Contains("UI") || ex.Message.Contains("thread") || ex.Message.Contains("STA"), 
                    $"Exception inattendue: {ex.Message}");
            }
        }

        [Test]
        public void VisualTreeHelpers_MethodsRejectInvalidTypes()
        {
            // Arrange & Act - Tester que les méthodes rejettent les types invalides
            try
            {
                var helpersType = typeof(VisualTreeHelpers);
                var findAncestorMethod = helpersType.GetMethod("FindAncestor", BindingFlags.Public | BindingFlags.Static);
                
                if (findAncestorMethod != null)
                {
                    // Tenter de créer avec un type qui n'hérite pas de DependencyObject
                    try
                    {
                        var invalidMethod = findAncestorMethod.MakeGenericMethod(typeof(string));
                        Assert.Fail("Should not be able to create FindAncestor<string>");
                    }
                    catch (ArgumentException)
                    {
                        // Attendu - string n'hérite pas de DependencyObject
                        Assert.IsTrue(true, "Correctly rejected invalid type");
                    }
                    
                    try
                    {
                        var invalidMethod = findAncestorMethod.MakeGenericMethod(typeof(int));
                        Assert.Fail("Should not be able to create FindAncestor<int>");
                    }
                    catch (ArgumentException)
                    {
                        // Attendu - int n'hérite pas de DependencyObject
                        Assert.IsTrue(true, "Correctly rejected invalid type");
                    }
                }
                
                Assert.IsTrue(true, "Type validation works correctly");
            }
            catch (Exception ex)
            {
                // Acceptable si nécessite un contexte UI
                Assert.IsTrue(ex.Message.Contains("UI") || ex.Message.Contains("thread") || ex.Message.Contains("STA"), 
                    $"Exception inattendue: {ex.Message}");
            }
        }

        [Test]
        public void VisualTreeHelpers_HasCorrectNamespace()
        {
            // Arrange & Act - Vérifier le namespace (VRAI CODE EXÉCUTÉ)
            var helpersType = typeof(VisualTreeHelpers);
            
            Assert.AreEqual("ClipboardPlus.UI.Helpers", helpersType.Namespace, 
                "VisualTreeHelpers should be in correct namespace");
        }

        [Test]
        public void VisualTreeHelpers_AllMethods_ArePublicAndStatic()
        {
            // Arrange & Act - Vérifier que toutes les méthodes sont publiques et statiques
            var helpersType = typeof(VisualTreeHelpers);
            var publicMethods = helpersType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.DeclaringType == helpersType)
                .ToArray();
            
            Assert.IsTrue(publicMethods.Length > 0, "Should have public methods");
            
            foreach (var method in publicMethods)
            {
                Assert.IsTrue(method.IsStatic, $"Method {method.Name} should be static");
                Assert.IsTrue(method.IsPublic, $"Method {method.Name} should be public");
            }
        }
    }
}
