using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services.ContentHandlers
{
    /// <summary>
    /// Handler pour le contenu RTF (Rich Text Format).
    /// Remplace la logique LoadRtfContent() du ContentPreviewViewModel.
    /// </summary>
    public class RtfContentHandler : BaseContentHandler
    {
        /// <summary>
        /// Initialise une nouvelle instance de RtfContentHandler.
        /// </summary>
        /// <param name="loggingService">Service de logging optionnel</param>
        public RtfContentHandler(ILoggingService? loggingService = null) 
            : base(loggingService)
        {
        }

        /// <inheritdoc />
        public override ClipboardDataType SupportedDataType => ClipboardDataType.Rtf;

        /// <inheritdoc />
        public override string GetDefaultUnavailableMessage()
        {
            return "Contenu RTF non disponible.";
        }

        /// <inheritdoc />
        protected override object HandleContentInternal(ClipboardItem item)
        {
            _loggingService?.LogInfo($"RtfContentHandler.HandleContentInternal - Traitement du contenu RTF pour l'élément ID {item.Id}");

            // Priorité 1: Utiliser RawData si disponible
            if (item.RawData != null && item.RawData.Length > 0)
            {
                _loggingService?.LogInfo($"RtfContentHandler - Données brutes disponibles, taille: {item.RawData.Length} octets");
                
                string? decodedRtf = DecodeRawDataAsText(item.RawData);
                if (decodedRtf != null)
                {
                    _loggingService?.LogInfo($"RtfContentHandler - RTF décodé avec succès, longueur: {decodedRtf.Length} caractères");
                    
                    // Validation basique du format RTF
                    if (IsValidRtfContent(decodedRtf))
                    {
                        // Log d'un aperçu du RTF pour le débogage (limité pour éviter les logs trop longs)
                        string rtfPreview = decodedRtf.Length <= 150 ? decodedRtf : decodedRtf.Substring(0, 150) + "...";
                        _loggingService?.LogInfo($"RtfContentHandler - Aperçu du RTF: '{rtfPreview}'");
                        
                        return decodedRtf;
                    }
                    else
                    {
                        _loggingService?.LogWarning("RtfContentHandler - Le contenu décodé ne semble pas être du RTF valide");
                    }
                }
                else
                {
                    _loggingService?.LogWarning("RtfContentHandler - Échec du décodage des données brutes, utilisation du TextPreview");
                }
            }
            else
            {
                _loggingService?.LogWarning("RtfContentHandler - Données brutes non disponibles, utilisation du TextPreview");
            }

            // Priorité 2: Utiliser TextPreview si RawData n'est pas disponible
            if (!string.IsNullOrEmpty(item.TextPreview))
            {
                _loggingService?.LogInfo($"RtfContentHandler - Utilisation du TextPreview: '{item.TextPreview}'");
                return item.TextPreview;
            }

            // Priorité 3: Message par défaut si aucune donnée n'est disponible
            string defaultMessage = GetDefaultUnavailableMessage();
            _loggingService?.LogInfo($"RtfContentHandler - Aucune donnée disponible, utilisation du message par défaut: '{defaultMessage}'");
            
            return defaultMessage;
        }

        /// <summary>
        /// Valide basiquement qu'un contenu est du RTF.
        /// </summary>
        /// <param name="content">Le contenu à valider</param>
        /// <returns>True si le contenu semble être du RTF valide</returns>
        private bool IsValidRtfContent(string content)
        {
            if (string.IsNullOrWhiteSpace(content))
            {
                return false;
            }

            // Validation basique: le RTF commence généralement par {\rtf
            bool startsWithRtf = content.TrimStart().StartsWith("{\\rtf", StringComparison.OrdinalIgnoreCase);
            
            _loggingService?.LogInfo($"RtfContentHandler.IsValidRtfContent - Validation RTF: {startsWithRtf}");
            
            return startsWithRtf;
        }
    }
}
