using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Implementations
{
    /// <summary>
    /// Implémentation du collecteur de métriques pour les opérations de suppression.
    /// Collecte et analyse les performances et la fiabilité du système.
    /// </summary>
    public class DeletionMetricsCollector : IDeletionMetricsCollector
    {
        private readonly ILoggingService _loggingService;
        private readonly ConcurrentQueue<DeletionResultContext> _deletionAttempts;
        private readonly ConcurrentQueue<DeletionLoggingResult> _loggingResults;
        private readonly ConcurrentQueue<OperationPerformanceMetrics> _performanceMetrics;
        private readonly ConcurrentQueue<DeletionLoggingError> _errors;
        private readonly object _lockObject = new object();
        private DateTime _collectionStartTime;

        public DeletionMetricsCollector(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _deletionAttempts = new ConcurrentQueue<DeletionResultContext>();
            _loggingResults = new ConcurrentQueue<DeletionLoggingResult>();
            _performanceMetrics = new ConcurrentQueue<OperationPerformanceMetrics>();
            _errors = new ConcurrentQueue<DeletionLoggingError>();
            _collectionStartTime = DateTime.Now;
        }

        /// <summary>
        /// Enregistre une tentative de suppression.
        /// </summary>
        public void RecordDeletionAttempt(DeletionResultContext context)
        {
            if (context == null)
            {
                _loggingService.LogWarning("⚠️ [METRICS] Tentative d'enregistrement d'un contexte null");
                return;
            }

            try
            {
                _deletionAttempts.Enqueue(context);
                
                // Log périodique des statistiques
                if (_deletionAttempts.Count % 25 == 0)
                {
                    LogPeriodicStatistics();
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [METRICS] Erreur lors de l'enregistrement de la tentative: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Enregistre le résultat d'une opération de logging.
        /// </summary>
        public void RecordLoggingResult(DeletionLoggingResult result)
        {
            if (result == null)
            {
                _loggingService.LogWarning("⚠️ [METRICS] Tentative d'enregistrement d'un résultat null");
                return;
            }

            try
            {
                _loggingResults.Enqueue(result);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [METRICS] Erreur lors de l'enregistrement du résultat: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Enregistre les métriques de performance d'une opération.
        /// </summary>
        public void RecordPerformanceMetrics(OperationPerformanceMetrics metrics)
        {
            if (metrics == null)
            {
                _loggingService.LogWarning("⚠️ [METRICS] Tentative d'enregistrement de métriques null");
                return;
            }

            try
            {
                _performanceMetrics.Enqueue(metrics);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [METRICS] Erreur lors de l'enregistrement des métriques: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Enregistre une erreur survenue pendant le logging.
        /// </summary>
        public void RecordError(DeletionLoggingError error)
        {
            if (error == null)
            {
                _loggingService.LogWarning("⚠️ [METRICS] Tentative d'enregistrement d'une erreur null");
                return;
            }

            try
            {
                _errors.Enqueue(error);
                
                // Log immédiat pour les erreurs critiques
                if (error.Severity == ErrorSeverity.Critical)
                {
                    _loggingService.LogError($"🚨 [METRICS] Erreur critique enregistrée: {error.Message}");
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [METRICS] Erreur lors de l'enregistrement de l'erreur: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Obtient les métriques globales du système.
        /// </summary>
        public DeletionMetrics GetMetrics()
        {
            lock (_lockObject)
            {
                try
                {
                    var attempts = _deletionAttempts.ToArray();
                    var results = _loggingResults.ToArray();
                    var errors = _errors.ToArray();

                    var metrics = new DeletionMetrics
                    {
                        CollectionStartTime = _collectionStartTime,
                        LastUpdateTime = DateTime.Now,
                        TotalDeletions = attempts.Length,
                        SuccessfulDeletions = attempts.Count(a => a.Success),
                        FailedDeletions = attempts.Count(a => !a.Success),
                        TotalErrors = errors.Length
                    };

                    // Métriques de logging
                    if (results.Length > 0)
                    {
                        metrics.AverageLoggingDuration = TimeSpan.FromMilliseconds(
                            results.Average(r => r.Duration.TotalMilliseconds));
                        metrics.AverageLogSize = results.Average(r => r.LogSize);
                    }

                    // Répartition des erreurs par type
                    metrics.ErrorsByType = errors
                        .GroupBy(e => e.ErrorType)
                        .ToDictionary(g => g.Key, g => g.Count());

                    return metrics;
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"❌ [METRICS] Erreur lors du calcul des métriques: {ex.Message}", ex);
                    return new DeletionMetrics
                    {
                        CollectionStartTime = _collectionStartTime,
                        LastUpdateTime = DateTime.Now
                    };
                }
            }
        }

        /// <summary>
        /// Obtient les métriques de performance.
        /// </summary>
        public DeletionLoggerMetrics GetPerformanceMetrics()
        {
            lock (_lockObject)
            {
                try
                {
                    var results = _loggingResults.ToArray();
                    var performanceData = _performanceMetrics.ToArray();

                    var metrics = new DeletionLoggerMetrics
                    {
                        CollectionStartTime = _collectionStartTime,
                        LastUpdateTime = DateTime.Now,
                        TotalLoggingOperations = results.Length,
                        SuccessfulOperations = results.Count(r => r.IsSuccessful),
                        FailedOperations = results.Count(r => !r.IsSuccessful)
                    };

                    if (results.Length > 0)
                    {
                        var durations = results.Select(r => r.Duration).ToArray();
                        metrics.MinDuration = durations.Min();
                        metrics.MaxDuration = durations.Max();
                        metrics.AverageDuration = TimeSpan.FromMilliseconds(
                            durations.Average(d => d.TotalMilliseconds));

                        metrics.TotalLogSize = results.Sum(r => r.LogSize);
                    }

                    if (performanceData.Length > 0)
                    {
                        metrics.AverageMemoryUsage = (long)performanceData.Average(p => p.MemoryUsed);
                        metrics.MaxMemoryUsage = performanceData.Max(p => p.MemoryUsed);
                    }

                    return metrics;
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"❌ [METRICS] Erreur lors du calcul des métriques de performance: {ex.Message}", ex);
                    return new DeletionLoggerMetrics
                    {
                        CollectionStartTime = _collectionStartTime,
                        LastUpdateTime = DateTime.Now
                    };
                }
            }
        }

        /// <summary>
        /// Obtient les métriques d'erreurs.
        /// </summary>
        public ErrorMetrics GetErrorMetrics()
        {
            lock (_lockObject)
            {
                try
                {
                    var errors = _errors.ToArray();
                    var totalOperations = _loggingResults.Count;

                    var metrics = new ErrorMetrics
                    {
                        TotalErrors = errors.Length,
                        ErrorRate = totalOperations > 0 ? (double)errors.Length / totalOperations * 100 : 0,
                        LastErrorTime = errors.Length > 0 ? errors.Max(e => e.Timestamp) : null
                    };

                    // Erreurs par type
                    metrics.ErrorsByType = errors
                        .GroupBy(e => e.ErrorType)
                        .ToDictionary(g => g.Key, g => g.Count());

                    // Erreurs par sévérité
                    metrics.ErrorsBySeverity = errors
                        .GroupBy(e => e.Severity)
                        .ToDictionary(g => g.Key, g => g.Count());

                    // Erreurs par composant
                    metrics.ErrorsByComponent = errors
                        .Where(e => !string.IsNullOrEmpty(e.Component))
                        .GroupBy(e => e.Component!)
                        .ToDictionary(g => g.Key, g => g.Count());

                    // Erreurs récentes (dernières 24h)
                    var yesterday = DateTime.Now.AddDays(-1);
                    metrics.RecentErrors = errors
                        .Where(e => e.Timestamp >= yesterday)
                        .OrderByDescending(e => e.Timestamp)
                        .Take(50)
                        .ToList();

                    return metrics;
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"❌ [METRICS] Erreur lors du calcul des métriques d'erreurs: {ex.Message}", ex);
                    return new ErrorMetrics();
                }
            }
        }

        /// <summary>
        /// Réinitialise toutes les métriques.
        /// </summary>
        public void ResetMetrics()
        {
            lock (_lockObject)
            {
                try
                {
                    // Vider toutes les queues
                    while (_deletionAttempts.TryDequeue(out _)) { }
                    while (_loggingResults.TryDequeue(out _)) { }
                    while (_performanceMetrics.TryDequeue(out _)) { }
                    while (_errors.TryDequeue(out _)) { }

                    _collectionStartTime = DateTime.Now;
                    _loggingService.LogInfo("🧹 [METRICS] Toutes les métriques réinitialisées");
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"❌ [METRICS] Erreur lors de la réinitialisation: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Réinitialise uniquement les métriques de performance.
        /// </summary>
        public void ResetPerformanceMetrics()
        {
            throw new NotSupportedException("Méthode désactivée - code mort détecté.");
        }

        /// <summary>
        /// Génère un rapport détaillé des métriques.
        /// </summary>
        public string GenerateMetricsReport()
        {
            throw new NotSupportedException("Méthode désactivée - code mort détecté.");
        }

        #region Méthodes privées

        private void LogPeriodicStatistics()
        {
            try
            {
                var metrics = GetMetrics();
                _loggingService.LogInfo($"📊 [METRICS] Statistiques: {metrics.TotalDeletions} suppressions, " +
                                      $"Succès: {metrics.SuccessRate:F1}%, " +
                                      $"Erreurs: {metrics.TotalErrors}");
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [METRICS] Erreur lors du log périodique: {ex.Message}");
            }
        }

        private string FormatBytes(long bytes)
        {
            if (bytes == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:F1} {sizes[order]}";
        }

        #endregion
    }
}
