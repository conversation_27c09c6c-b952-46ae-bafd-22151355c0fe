using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Implémentation de la gestion des tentatives de suppression en base de données avec retry logic
    /// Responsabilité : Gérer la suppression en BDD avec logique de retry et gestion d'erreurs
    /// </summary>
    public class DeletionRetryService : IDeletionRetryService
    {
        private readonly IPersistenceService _persistenceService;
        private readonly ILoggingService _loggingService;
        
        private int _maxRetries = 3;
        private int _baseDelayMs = 100;

        /// <summary>
        /// Constructeur avec injection de dépendances
        /// </summary>
        /// <param name="persistenceService">Service de persistance</param>
        /// <param name="loggingService">Service de logging</param>
        public DeletionRetryService(IPersistenceService persistenceService, ILoggingService loggingService)
        {
            _persistenceService = persistenceService ?? throw new ArgumentNullException(nameof(persistenceService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Configure les paramètres de retry
        /// </summary>
        /// <param name="maxRetries">Nombre maximum de tentatives</param>
        /// <param name="baseDelayMs">Délai de base entre les tentatives en millisecondes</param>
        public void ConfigureRetry(int maxRetries, int baseDelayMs)
        {
            _maxRetries = Math.Max(1, maxRetries);
            _baseDelayMs = Math.Max(50, baseDelayMs);
        }

        /// <summary>
        /// Tente de supprimer un élément de la base de données avec logique de retry
        /// </summary>
        /// <param name="id">L'ID de l'élément à supprimer</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Résultat de la suppression avec détails des tentatives</returns>
        public async Task<DatabaseDeletionResult> DeleteFromDatabaseWithRetryAsync(long id, string operationId)
        {
            var stopwatch = Stopwatch.StartNew();
            var logMessages = new List<string>();
            Exception? lastException = null;
            bool hadErrors = false;

            _loggingService.LogDebug($"[{operationId}] Début suppression BDD avec retry pour ID: {id} (max {_maxRetries} tentatives)");

            for (int attempt = 1; attempt <= _maxRetries; attempt++)
            {
                try
                {
                    var message = $"Tentative {attempt}/{_maxRetries} de suppression BDD pour ID {id}";
                    logMessages.Add(message);
                    _loggingService.LogDebug($"[{operationId}] {message}");

                    // Tentative de suppression
                    bool success = await _persistenceService.DeleteClipboardItemAsync(id);
                    
                    if (success)
                    {
                        stopwatch.Stop();
                        var successMessage = $"Suppression BDD réussie pour ID {id} à la tentative {attempt}";
                        logMessages.Add(successMessage);
                        _loggingService.LogInfo($"[{operationId}] {successMessage}");
                        
                        return DatabaseDeletionResult.CreateSuccess(attempt, hadErrors, stopwatch.Elapsed, logMessages.ToArray());
                    }
                    else
                    {
                        hadErrors = true;
                        var errorMessage = $"Suppression BDD échouée pour ID {id} à la tentative {attempt} (retour false)";
                        logMessages.Add(errorMessage);
                        _loggingService.LogWarning($"[{operationId}] {errorMessage}");
                        
                        if (attempt < _maxRetries)
                        {
                            await DelayBeforeRetry(attempt, operationId);
                        }
                    }
                }
                catch (Exception ex)
                {
                    hadErrors = true;
                    lastException = ex;
                    var errorMessage = $"Exception lors de la tentative {attempt}/{_maxRetries} pour ID {id}: {ex.Message}";
                    logMessages.Add(errorMessage);
                    _loggingService.LogError($"[{operationId}] {errorMessage}", ex);
                    
                    if (attempt < _maxRetries)
                    {
                        await DelayBeforeRetry(attempt, operationId);
                    }
                }
            }

            stopwatch.Stop();
            var finalMessage = $"Échec définitif de la suppression BDD pour ID {id} après {_maxRetries} tentatives";
            logMessages.Add(finalMessage);
            _loggingService.LogError($"[{operationId}] {finalMessage}");

            return DatabaseDeletionResult.CreateFailure(_maxRetries, lastException, stopwatch.Elapsed, logMessages.ToArray());
        }

        /// <summary>
        /// Applique un délai avant la prochaine tentative (exponential backoff)
        /// </summary>
        private async Task DelayBeforeRetry(int attempt, string operationId)
        {
            var delay = _baseDelayMs * (int)Math.Pow(2, attempt - 1); // Exponential backoff
            _loggingService.LogDebug($"[{operationId}] Attente de {delay}ms avant la prochaine tentative");
            await Task.Delay(delay);
        }
    }
}
