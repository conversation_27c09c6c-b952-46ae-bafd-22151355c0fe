using System;
using System.Threading.Tasks;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.WindowDeactivation;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.STA
{
    /// <summary>
    /// Tests d'intégration STA pour WindowDeactivationOrchestrator.
    /// Ces tests utilisent de vraies classes Window et nécessitent STA.
    /// </summary>
    [TestFixture]
    [Apartment(System.Threading.ApartmentState.STA)]
    public class WindowDeactivationOrchestratorIntegrationTests
    {
        private Mock<IWindowStateValidator>? _mockStateValidator;
        private Mock<IWindowDiagnosticService>? _mockDiagnosticService;
        private Mock<IApplicationWindowClassifier>? _mockWindowClassifier;
        private Mock<IWindowVisibilityDecisionService>? _mockDecisionService;
        private Mock<IWindowDeactivationLoggingService>? _mockLoggingService;
        private Mock<ILoggingService>? _mockBaseLoggingService;
        private WindowDeactivationOrchestrator? _orchestrator;

        /// <summary>
        /// Fenêtre de test STA.
        /// </summary>
        private class STATestWindow : Window
        {
            public bool IsHidden { get; private set; } = false;

            public STATestWindow()
            {
                Title = "STATestWindow";
                Width = 100;
                Height = 100;
                WindowStyle = WindowStyle.None;
                ShowInTaskbar = false;
                Visibility = Visibility.Hidden; // Créer cachée pour les tests
            }

            protected override void OnSourceInitialized(EventArgs e)
            {
                // Override pour intercepter les appels à Hide()
                base.OnSourceInitialized(e);
            }

            // Override de la méthode Hide pour tracker les appels
            public new void Hide()
            {
                IsHidden = true;
                base.Hide();
            }

            // Propriété pour vérifier si la fenêtre est vraiment cachée
            public bool IsActuallyHidden => Visibility == Visibility.Hidden || Visibility == Visibility.Collapsed;
        }

        [SetUp]
        public void SetUp()
        {
            _mockStateValidator = new Mock<IWindowStateValidator>();
            _mockDiagnosticService = new Mock<IWindowDiagnosticService>();
            _mockWindowClassifier = new Mock<IApplicationWindowClassifier>();
            _mockDecisionService = new Mock<IWindowVisibilityDecisionService>();
            _mockLoggingService = new Mock<IWindowDeactivationLoggingService>();
            _mockBaseLoggingService = new Mock<ILoggingService>();

            _orchestrator = new WindowDeactivationOrchestrator(
                _mockStateValidator.Object,
                _mockDiagnosticService.Object,
                _mockWindowClassifier.Object,
                _mockDecisionService.Object,
                _mockLoggingService.Object,
                _mockBaseLoggingService.Object);
        }

        [Test]
        public void HandleWindowDeactivation_WithSTAWindow_ShouldWork()
        {
            // Arrange
            var validationResult = new WindowStateValidationResult
            {
                ShouldIgnore = true,
                ValidationType = WindowStateValidationType.WindowClosing,
                Reason = "Fenêtre en cours de fermeture"
            };

            _mockStateValidator!.Setup(x => x.ValidateWindowState(It.IsAny<WindowStateValidationContext>()))
                .Returns(validationResult);

            var testWindow = new STATestWindow();

            // Act
            var result = _orchestrator!.HandleWindowDeactivation(testWindow, EventArgs.Empty);

            // Assert
            Assert.That(result.IsSuccessful, Is.True);
            Assert.That(result.ActionTaken, Is.EqualTo(WindowDeactivationAction.IgnoredWindowClosing));
            Assert.That(result.Reason, Is.EqualTo("Fenêtre en cours de fermeture"));

            // Cleanup
            testWindow.Close();
        }

        [Test]
        public void HandleWindowDeactivation_WithRealWindowHiding_ShouldExecuteHide()
        {
            // Arrange
            var validationResult = new WindowStateValidationResult
            {
                ShouldIgnore = false,
                ValidationType = WindowStateValidationType.Normal
            };

            var diagnosticResult = new WindowDiagnosticResult
            {
                IsSuccessful = true,
                ActiveWindow = null // Pas de fenêtre active
            };

            var visibilityDecision = new WindowVisibilityDecision
            {
                ShouldHide = true,
                DecisionType = WindowVisibilityDecisionType.HideNoActiveWindow,
                RecommendedAction = WindowVisibilityAction.HideImmediately,
                Reason = "Aucune fenêtre active détectée"
            };

            _mockStateValidator!.Setup(x => x.ValidateWindowState(It.IsAny<WindowStateValidationContext>()))
                .Returns(validationResult);
            _mockDiagnosticService!.Setup(x => x.AnalyzeCurrentWindowState())
                .Returns(diagnosticResult);
            _mockDecisionService!.Setup(x => x.EvaluateVisibilityDecision(It.IsAny<WindowVisibilityDecisionContext>()))
                .Returns(visibilityDecision);

            var testWindow = new STATestWindow();

            // Act
            var result = _orchestrator!.HandleWindowDeactivation(testWindow, EventArgs.Empty);

            // Assert
            Assert.That(result.IsSuccessful, Is.True);
            Assert.That(result.ActionTaken, Is.EqualTo(WindowDeactivationAction.WindowHiddenNoActiveWindow));
            Assert.That(result.Reason, Is.EqualTo("Aucune fenêtre active détectée"));
            Assert.That(testWindow.IsActuallyHidden, Is.True, "La fenêtre devrait être masquée après l'opération");

            // Cleanup
            testWindow.Close();
        }

        [Test]
        public async Task HandleWindowDeactivationAsync_WithSTAWindow_ShouldWork()
        {
            // Arrange
            var validationResult = new WindowStateValidationResult
            {
                ShouldIgnore = true,
                ValidationType = WindowStateValidationType.WindowClosing
            };

            _mockStateValidator!.Setup(x => x.ValidateWindowState(It.IsAny<WindowStateValidationContext>()))
                .Returns(validationResult);

            var testWindow = new STATestWindow();

            // Act
            var result = await _orchestrator!.HandleWindowDeactivationAsync(testWindow, EventArgs.Empty);

            // Assert
            Assert.That(result.IsSuccessful, Is.True);
            Assert.That(result.ActionTaken, Is.EqualTo(WindowDeactivationAction.IgnoredWindowClosing));

            // Cleanup
            testWindow.Close();
        }

        [Test]
        public void HandleWindowDeactivation_ShouldLogAllStepsWithRealWindow()
        {
            // Arrange
            var validationResult = new WindowStateValidationResult
            {
                ShouldIgnore = false,
                ValidationType = WindowStateValidationType.Normal
            };

            var diagnosticResult = new WindowDiagnosticResult
            {
                IsSuccessful = true,
                ActiveWindow = null
            };

            var visibilityDecision = new WindowVisibilityDecision
            {
                ShouldHide = true,
                DecisionType = WindowVisibilityDecisionType.HideNoActiveWindow
            };

            _mockStateValidator!.Setup(x => x.ValidateWindowState(It.IsAny<WindowStateValidationContext>()))
                .Returns(validationResult);
            _mockDiagnosticService!.Setup(x => x.AnalyzeCurrentWindowState())
                .Returns(diagnosticResult);
            _mockDecisionService!.Setup(x => x.EvaluateVisibilityDecision(It.IsAny<WindowVisibilityDecisionContext>()))
                .Returns(visibilityDecision);

            var testWindow = new STATestWindow();

            // Act
            _orchestrator!.HandleWindowDeactivation(testWindow, EventArgs.Empty);

            // Assert - Vérifier que tous les logs sont appelés
            _mockLoggingService!.Verify(x => x.LogDeactivationStart(It.IsAny<Window>(), It.IsAny<string>()), Times.Once);
            _mockLoggingService.Verify(x => x.LogDiagnosticResults(It.IsAny<WindowDiagnosticResult>(), It.IsAny<string>()), Times.Once);
            _mockLoggingService.Verify(x => x.LogVisibilityDecision(It.IsAny<WindowVisibilityDecision>(), It.IsAny<string>()), Times.Once);
            _mockLoggingService.Verify(x => x.LogDeactivationEnd(It.IsAny<Window>(), It.IsAny<string>()), Times.Once);

            // Cleanup
            testWindow.Close();
        }

        [Test]
        public void GetPerformanceMetrics_ShouldReturnCurrentMetrics()
        {
            // Arrange - Exécuter quelques opérations pour générer des métriques
            var validationResult = new WindowStateValidationResult
            {
                ShouldIgnore = true,
                ValidationType = WindowStateValidationType.WindowClosing
            };

            _mockStateValidator!.Setup(x => x.ValidateWindowState(It.IsAny<WindowStateValidationContext>()))
                .Returns(validationResult);

            var testWindow1 = new STATestWindow();
            var testWindow2 = new STATestWindow();

            // Act
            _orchestrator!.HandleWindowDeactivation(testWindow1, EventArgs.Empty);
            _orchestrator.HandleWindowDeactivation(testWindow2, EventArgs.Empty);

            var metrics = _orchestrator.GetPerformanceMetrics();

            // Assert
            Assert.That(metrics.TotalOperations, Is.EqualTo(2));
            Assert.That(metrics.SuccessfulOperations, Is.EqualTo(2));
            Assert.That(metrics.FailedOperations, Is.EqualTo(0));
            Assert.That(metrics.SuccessRate, Is.EqualTo(100.0));

            // Cleanup
            testWindow1.Close();
            testWindow2.Close();
        }
    }
}
