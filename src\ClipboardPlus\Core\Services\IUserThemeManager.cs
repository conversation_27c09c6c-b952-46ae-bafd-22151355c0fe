using System.Collections.Generic;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface définissant les opérations de gestion des thèmes de l'application.
    /// </summary>
    public interface IUserThemeManager
    {
        /// <summary>
        /// Récupère la liste des thèmes disponibles.
        /// </summary>
        /// <returns>Liste des thèmes disponibles.</returns>
        IEnumerable<ThemeInfo> GetAvailableThemes();

        /// <summary>
        /// Applique un thème à l'application.
        /// </summary>
        /// <param name="theme">Le thème à appliquer.</param>
        /// <returns>Tâche asynchrone.</returns>
        Task ApplyThemeAsync(ThemeInfo theme);

        /// <summary>
        /// Récupère le thème actif.
        /// </summary>
        /// <returns>Le thème actif.</returns>
        ThemeInfo GetActiveTheme();

        /// <summary>
        /// Charge le thème actif au démarrage de l'application.
        /// </summary>
        /// <param name="themePath">Chemin du thème à charger.</param>
        /// <returns>Tâche asynchrone.</returns>
        Task LoadActiveThemeAsync(string themePath);
    }
} 