using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services;

/// <summary>
/// Fournit une abstraction pour afficher des notifications à l'utilisateur.
/// </summary>
public interface IUserNotificationService
{
    /// <summary>
    /// Affiche un message d'erreur à l'utilisateur.
    /// </summary>
    /// <param name="title">Le titre de la boîte de dialogue.</param>
    /// <param name="message">Le message d'erreur à afficher.</param>
    void ShowError(string title, string message);

    /// <summary>
    /// Affiche un message d'information à l'utilisateur.
    /// </summary>
    /// <param name="title">Le titre de la boîte de dialogue.</param>
    /// <param name="message">Le message d'information à afficher.</param>
    void ShowInformation(string title, string message);
} 