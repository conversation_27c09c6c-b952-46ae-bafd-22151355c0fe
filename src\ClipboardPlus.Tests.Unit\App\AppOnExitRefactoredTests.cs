using System;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.App
{
    /// <summary>
    /// Tests pour la méthode App.OnExit REFACTORISÉE (nouvelle version avec délégation)
    /// Ces tests complètent AppOnExitCharacterizationTests en testant la vraie méthode OnExit
    /// </summary>
    [TestFixture]
    public class AppOnExitRefactoredTests
    {
        private Mock<IServiceProvider> _mockServiceProvider = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IApplicationExitService> _mockExitService = null!;
        private OnExitRefactoredTester _onExitTester = null!;

        [SetUp]
        public void SetUp()
        {
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockExitService = new Mock<IApplicationExitService>();

            _onExitTester = new OnExitRefactoredTester();
            _onExitTester.SetServices(_mockServiceProvider.Object);
        }

        private static ExitEventArgs CreateExitEventArgs(int exitCode = 0)
        {
            // ExitEventArgs n'a pas de constructeur public, utilisons la réflexion
            var constructor = typeof(ExitEventArgs).GetConstructor(
                BindingFlags.NonPublic | BindingFlags.Instance, 
                null, 
                new[] { typeof(int) }, 
                null);
            
            if (constructor != null)
            {
                return (ExitEventArgs)constructor.Invoke(new object[] { exitCode });
            }
            
            // Fallback : utiliser Activator.CreateInstance
            return (ExitEventArgs)Activator.CreateInstance(typeof(ExitEventArgs), 
                BindingFlags.NonPublic | BindingFlags.Instance, 
                null, 
                new object[] { exitCode }, 
                null)!;
        }

        [Test]
        public void OnExit_WithNullApplicationExitService_ShouldNotThrow()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns((IApplicationExitService?)null);

            var exitEventArgs = CreateExitEventArgs();

            // Act & Assert
            Assert.DoesNotThrow(() => _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs));

            // Vérifier que base.OnExit et Environment.Exit sont appelés même sans service
            Assert.IsTrue(_onExitTester.BaseOnExitCalled);
            Assert.IsTrue(_onExitTester.EnvironmentExitCalled);
        }

        [Test]
        public void OnExit_WithApplicationExitServiceException_ShouldLogCriticalAndContinue()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            var testException = new InvalidOperationException("Test exit service error");
            _mockExitService.Setup(es => es.ExecuteExitSequenceAsync(It.IsAny<IServiceProvider>()))
                .ThrowsAsync(testException);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogCritical(
                It.Is<string>(msg => msg.Contains("OnExit: Erreur critique lors de la fermeture") && msg.Contains(testException.Message)),
                testException), Times.Once);

            // Vérifier que l'exécution continue malgré l'exception
            Assert.IsTrue(_onExitTester.BaseOnExitCalled);
            Assert.IsTrue(_onExitTester.EnvironmentExitCalled);
        }

        [Test]
        public void OnExit_ShouldGenerateUniqueOperationId()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs);

            // Assert - Vérifier que les messages contiennent un ID d'opération unique
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(msg => msg.Contains("[") && msg.Contains("] OnExit: Début de la fermeture de l'application"))),
                Times.Once);

            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(msg => msg.Contains("[") && msg.Contains("] OnExit: Séquence de fermeture terminée avec succès"))),
                Times.Once);
        }

        [Test]
        public void OnExit_ShouldCallApplicationExitServiceWithCorrectParameters()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            _mockExitService.Setup(es => es.ExecuteExitSequenceAsync(It.IsAny<IServiceProvider>()))
                .Returns(Task.CompletedTask);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs);

            // Assert
            _mockExitService.Verify(es => es.ExecuteExitSequenceAsync(_mockServiceProvider.Object), Times.Once);
        }

        [Test]
        public void OnExit_ShouldAlwaysForceFlushInFinally()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            // Simuler une exception pour vérifier que ForceFlush est appelé dans finally
            var testException = new InvalidOperationException("Test exception");
            _mockExitService.Setup(es => es.ExecuteExitSequenceAsync(It.IsAny<IServiceProvider>()))
                .ThrowsAsync(testException);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs);

            // Assert
            _mockLoggingService.Verify(ls => ls.ForceFlush(), Times.Once);
        }

        [Test]
        public void OnExit_WithSuccessfulExecution_ShouldLogSuccessMessage()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            _mockExitService.Setup(es => es.ExecuteExitSequenceAsync(It.IsAny<IServiceProvider>()))
                .Returns(Task.CompletedTask);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs);

            // Assert
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(msg => msg.Contains("OnExit: Séquence de fermeture terminée avec succès"))),
                Times.Once);
        }

        [Test]
        public void OnExit_ShouldAlwaysCallBaseOnExitAndEnvironmentExit()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs);

            // Assert
            Assert.IsTrue(_onExitTester.BaseOnExitCalled);
            Assert.AreSame(exitEventArgs, _onExitTester.BaseOnExitEventArgs);
            Assert.IsTrue(_onExitTester.EnvironmentExitCalled);
            Assert.AreEqual(0, _onExitTester.EnvironmentExitCode);
        }

        [Test]
        public void OnExit_WithNullLoggingService_ShouldStillExecuteSequence()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns((ILoggingService?)null);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            _mockExitService.Setup(es => es.ExecuteExitSequenceAsync(It.IsAny<IServiceProvider>()))
                .Returns(Task.CompletedTask);

            var exitEventArgs = CreateExitEventArgs();

            // Act & Assert
            Assert.DoesNotThrow(() => _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs));

            // Vérifier que le service est appelé même sans logging
            _mockExitService.Verify(es => es.ExecuteExitSequenceAsync(_mockServiceProvider.Object), Times.Once);
            Assert.IsTrue(_onExitTester.BaseOnExitCalled);
            Assert.IsTrue(_onExitTester.EnvironmentExitCalled);
        }

        [Test]
        public void OnExit_ShouldGenerateUniqueOperationIdForEachCall()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            var exitEventArgs1 = CreateExitEventArgs();
            var exitEventArgs2 = CreateExitEventArgs();

            var capturedOperationIds = new List<string>();
            _mockLoggingService.Setup(ls => ls.LogInfo(It.IsAny<string>()))
                .Callback<string>(msg =>
                {
                    if (msg.Contains("OnExit: Début de la fermeture"))
                    {
                        var match = System.Text.RegularExpressions.Regex.Match(msg, @"\[([a-f0-9]{8})\]");
                        if (match.Success)
                        {
                            capturedOperationIds.Add(match.Groups[1].Value);
                        }
                    }
                });

            // Act
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs1);
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs2);

            // Assert
            Assert.AreEqual(2, capturedOperationIds.Count);
            Assert.AreNotEqual(capturedOperationIds[0], capturedOperationIds[1]);
            Assert.IsTrue(capturedOperationIds.All(id => id.Length == 8));
        }

        [Test]
        public void OnExit_ShouldUseConsistentOperationIdInAllMessages()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            _mockExitService.Setup(es => es.ExecuteExitSequenceAsync(It.IsAny<IServiceProvider>()))
                .Returns(Task.CompletedTask);

            var exitEventArgs = CreateExitEventArgs();
            var loggedMessages = new List<string>();

            _mockLoggingService.Setup(ls => ls.LogInfo(It.IsAny<string>()))
                .Callback<string>(msg => loggedMessages.Add(msg));

            // Act
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs);

            // Assert
            var startMessage = loggedMessages.FirstOrDefault(m => m.Contains("OnExit: Début de la fermeture"));
            var endMessage = loggedMessages.FirstOrDefault(m => m.Contains("OnExit: Séquence de fermeture terminée"));

            Assert.IsNotNull(startMessage);
            Assert.IsNotNull(endMessage);

            // Extraire l'operationId des deux messages
            var startIdMatch = System.Text.RegularExpressions.Regex.Match(startMessage, @"\[([a-f0-9]{8})\]");
            var endIdMatch = System.Text.RegularExpressions.Regex.Match(endMessage, @"\[([a-f0-9]{8})\]");

            Assert.IsTrue(startIdMatch.Success);
            Assert.IsTrue(endIdMatch.Success);
            Assert.AreEqual(startIdMatch.Groups[1].Value, endIdMatch.Groups[1].Value);
        }

        [Test]
        public void OnExit_WithException_ShouldUseConsistentOperationIdInErrorMessage()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            var testException = new InvalidOperationException("Test error");
            _mockExitService.Setup(es => es.ExecuteExitSequenceAsync(It.IsAny<IServiceProvider>()))
                .ThrowsAsync(testException);

            var exitEventArgs = CreateExitEventArgs();
            var loggedMessages = new List<string>();
            var criticalMessages = new List<string>();

            _mockLoggingService.Setup(ls => ls.LogInfo(It.IsAny<string>()))
                .Callback<string>(msg => loggedMessages.Add(msg));
            _mockLoggingService.Setup(ls => ls.LogCritical(It.IsAny<string>(), It.IsAny<Exception>()))
                .Callback<string, Exception>((msg, ex) => criticalMessages.Add(msg));

            // Act
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs);

            // Assert
            var startMessage = loggedMessages.FirstOrDefault(m => m.Contains("OnExit: Début de la fermeture"));
            var errorMessage = criticalMessages.FirstOrDefault(m => m.Contains("OnExit: Erreur critique"));

            Assert.IsNotNull(startMessage);
            Assert.IsNotNull(errorMessage);

            // Extraire l'operationId des deux messages
            var startIdMatch = System.Text.RegularExpressions.Regex.Match(startMessage, @"\[([a-f0-9]{8})\]");
            var errorIdMatch = System.Text.RegularExpressions.Regex.Match(errorMessage, @"\[([a-f0-9]{8})\]");

            Assert.IsTrue(startIdMatch.Success);
            Assert.IsTrue(errorIdMatch.Success);
            Assert.AreEqual(startIdMatch.Groups[1].Value, errorIdMatch.Groups[1].Value);
        }

        [Test]
        public void OnExit_ShouldLogExactMessageFormats()
        {
            // Arrange
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IApplicationExitService)))
                .Returns(_mockExitService.Object);

            _mockExitService.Setup(es => es.ExecuteExitSequenceAsync(It.IsAny<IServiceProvider>()))
                .Returns(Task.CompletedTask);

            var exitEventArgs = CreateExitEventArgs();

            // Act
            _onExitTester.ExecuteOnExitRefactoredLogic(exitEventArgs);

            // Assert - Vérifier les formats exacts des messages
            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(msg => System.Text.RegularExpressions.Regex.IsMatch(msg,
                    @"^\[[a-f0-9]{8}\] OnExit: Début de la fermeture de l'application$"))),
                Times.Once);

            _mockLoggingService.Verify(ls => ls.LogInfo(
                It.Is<string>(msg => System.Text.RegularExpressions.Regex.IsMatch(msg,
                    @"^\[[a-f0-9]{8}\] OnExit: Séquence de fermeture terminée avec succès$"))),
                Times.Once);
        }
    }

    /// <summary>
    /// Classe qui extrait et teste la logique de App.OnExit refactorisée sans hériter de Application
    /// </summary>
    public class OnExitRefactoredTester
    {
        public bool BaseOnExitCalled { get; private set; }
        public ExitEventArgs? BaseOnExitEventArgs { get; private set; }
        public bool EnvironmentExitCalled { get; private set; }
        public int EnvironmentExitCode { get; private set; }

        private IServiceProvider? _testServices;

        public void SetServices(IServiceProvider? services)
        {
            _testServices = services;
        }

        public void ExecuteOnExitRefactoredLogic(ExitEventArgs e)
        {
            // Réinitialiser les flags
            BaseOnExitCalled = false;
            BaseOnExitEventArgs = null;
            EnvironmentExitCalled = false;
            EnvironmentExitCode = -1;

            // Exécuter la logique exacte de App.OnExit refactorisée
            ExecuteOnExitRefactoredLogicWithInterception(e);
        }

        private void ExecuteOnExitRefactoredLogicWithInterception(ExitEventArgs e)
        {
            // COPIE EXACTE de la méthode App.OnExit refactorisée (lignes 115-142)
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            var loggingService = _testServices?.GetService<ILoggingService>();
            loggingService?.LogInfo($"[{operationId}] OnExit: Début de la fermeture de l'application");

            try
            {
                // Délégation DIRECTE vers le service spécialisé
                var exitService = _testServices?.GetService<IApplicationExitService>();
                exitService?.ExecuteExitSequenceAsync(_testServices).GetAwaiter().GetResult();

                loggingService?.LogInfo($"[{operationId}] OnExit: Séquence de fermeture terminée avec succès");
            }
            catch (Exception ex)
            {
                loggingService?.LogCritical($"[{operationId}] OnExit: Erreur critique lors de la fermeture: {ex.Message}", ex);
            }
            finally
            {
                loggingService?.ForceFlush();
            }

            // Simuler l'appel à base.OnExit(e)
            BaseOnExitCalled = true;
            BaseOnExitEventArgs = e;

            // Simuler l'appel à Environment.Exit(0)
            EnvironmentExitCalled = true;
            EnvironmentExitCode = 0;
        }
    }
}
