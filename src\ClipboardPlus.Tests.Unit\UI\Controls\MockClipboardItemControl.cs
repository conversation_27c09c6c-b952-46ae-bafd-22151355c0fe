using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.Controls;

namespace ClipboardPlus.Tests.Unit.UI.Controls
{
    /// <summary>
    /// Version simplifiée de ClipboardItemControl pour les tests, 
    /// qui expose certains champs privés pour faciliter le test.
    /// </summary>
    public class MockClipboardItemControl : Control
    {
        /// <summary>
        /// Élément en cours d'affichage
        /// </summary>
        public ClipboardItem? CurrentItem { get; set; }
        
        /// <summary>
        /// ViewModel associé
        /// </summary>
        public ITestClipboardHistoryViewModel? ViewModel { get; set; }
        
        /// <summary>
        /// Flag indiquant si une recherche de ViewModel a déjà été tentée
        /// </summary>
        public bool ViewModelSearchAttempted { get; set; }
        
        /// <summary>
        /// A<PERSON>ée lorsque le DataContext du contrôle change
        /// </summary>
        public void ClipboardItemControl_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.NewValue is ClipboardItem item)
            {
                CurrentItem = item;
            }
        }
        
        /// <summary>
        /// Méthode directe pour le gestionnaire d'événement RenameMenuItem_Click
        /// </summary>
        public void RenameMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentItem == null || ViewModel == null) return;
            
            ViewModel.DemarrerRenommageCommand.Execute(CurrentItem);
        }
        
        /// <summary>
        /// Méthode directe pour le gestionnaire d'événement PinMenuItem_Click
        /// </summary>
        public void PinMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentItem == null || ViewModel == null) return;
            
            ViewModel.BasculerEpinglageCommand.Execute(CurrentItem);
        }
        
        /// <summary>
        /// Méthode directe pour le gestionnaire d'événement PreviewMenuItem_Click
        /// </summary>
        public void PreviewMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentItem == null || ViewModel == null) return;
            
            ViewModel.AfficherPreviewCommand.Execute(CurrentItem);
        }
        
        /// <summary>
        /// Méthode directe pour le gestionnaire d'événement DeleteMenuItem_Click
        /// </summary>
        public void DeleteMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentItem == null || ViewModel == null) return;
            
            ViewModel.SupprimerElementCommand.Execute(CurrentItem);
        }
        
        /// <summary>
        /// Méthode directe pour le gestionnaire d'événement EditNameTextBox_KeyDown
        /// </summary>
        public void EditNameTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (ViewModel == null) return;
            
            if (e.Key == Key.Enter)
            {
                ViewModel.ConfirmerRenommageCommand.Execute(null);
                e.Handled = true;
            }
            else if (e.Key == Key.Escape)
            {
                ViewModel.AnnulerRenommageCommand.Execute(null);
                e.Handled = true;
            }
        }
        
        /// <summary>
        /// Méthode directe pour le gestionnaire d'événement EditNameTextBox_LostFocus
        /// </summary>
        public void EditNameTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (ViewModel == null) return;
            
            ViewModel.ConfirmerRenommageCommand.Execute(null);
        }
        
        /// <summary>
        /// Méthode qui recherche le ViewModel dans l'arbre visuel ou le DataContext
        /// </summary>
        public ITestClipboardHistoryViewModel? FindViewModel()
        {
            if (ViewModel != null) return ViewModel;
            if (ViewModelSearchAttempted) return null;

            ViewModelSearchAttempted = true;
            return null;
        }

        /// <summary>
        /// Simule l'appui sur une touche pour les tests
        /// </summary>
        public void SimulateKeyDown(Key key)
        {
            if (ViewModel == null) return;

            switch (key)
            {
                case Key.Enter:
                    ViewModel.ConfirmerRenommageCommand.Execute(null);
                    break;
                case Key.Escape:
                    ViewModel.AnnulerRenommageCommand.Execute(null);
                    break;
            }
        }
    }
} 