using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation du service de maintenance périodique de l'historique.
    ///
    /// Ce service reproduit la logique de maintenance aléatoire de la méthode originale
    /// tout en la rendant testable et configurable.
    /// </summary>
    public class HistoryMaintenanceService : IHistoryMaintenanceService
    {
        private readonly ILoggingService _loggingService;
        private readonly IClipboardHistoryManager _historyManager;
        private readonly Random _random;
        private int _maintenanceProbabilityPercent = 5; // 5% par défaut comme dans l'original

        /// <summary>
        /// Initialise une nouvelle instance du service de maintenance.
        /// </summary>
        /// <param name="loggingService">Service de logging</param>
        /// <param name="historyManager">Gestionnaire d'historique contenant la méthode PurgeOrphanedItemsAsync</param>
        public HistoryMaintenanceService(ILoggingService loggingService, IClipboardHistoryManager historyManager)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _historyManager = historyManager ?? throw new ArgumentNullException(nameof(historyManager));
            _random = new Random();
        }

        /// <summary>
        /// Constructeur pour les tests permettant d'injecter un Random personnalisé.
        /// </summary>
        /// <param name="loggingService">Service de logging</param>
        /// <param name="historyManager">Gestionnaire d'historique</param>
        /// <param name="random">Instance Random personnalisée (pour les tests)</param>
        internal HistoryMaintenanceService(ILoggingService loggingService, IClipboardHistoryManager historyManager, Random random)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _historyManager = historyManager ?? throw new ArgumentNullException(nameof(historyManager));
            _random = random ?? throw new ArgumentNullException(nameof(random));
        }

        /// <summary>
        /// Déclenche les tâches de maintenance si nécessaire selon la probabilité configurée.
        ///
        /// Reproduit exactement la logique originale :
        /// Random random = new Random();
        /// if (random.Next(100) < 5) // 5% de chance
        /// {
        ///     _ = Task.Run(async () => await PurgeOrphanedItemsAsync().ConfigureAwait(false));
        /// }
        /// </summary>
        public Task<bool> TriggerMaintenanceIfNeededAsync(string eventId)
        {
            try
            {
                // Reproduire la logique originale exactement
                int randomValue = _random.Next(100);
                bool shouldTriggerMaintenance = randomValue < _maintenanceProbabilityPercent;

                _loggingService.LogInfo($"[HistoryMaintenanceService] [{eventId}] Vérification maintenance: random={randomValue}, seuil={_maintenanceProbabilityPercent}, déclencher={shouldTriggerMaintenance}");

                if (shouldTriggerMaintenance)
                {
                    _loggingService.LogInfo($"[HistoryMaintenanceService] [{eventId}] Déclenchement de la purge périodique (probabilité {_maintenanceProbabilityPercent}%)");

                    // Reproduire la logique originale : Task.Run avec ConfigureAwait(false)
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await _historyManager.PurgeOrphanedItemsAsync().ConfigureAwait(false);
                            _loggingService.LogInfo($"[HistoryMaintenanceService] [{eventId}] Purge périodique terminée avec succès");
                        }
                        catch (Exception ex)
                        {
                            _loggingService.LogError($"[HistoryMaintenanceService] [{eventId}] Erreur lors de la purge périodique: {ex.Message}", ex);
                        }
                    });

                    return Task.FromResult(true);
                }

                return Task.FromResult(false);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"[HistoryMaintenanceService] [{eventId}] Erreur lors de la vérification de maintenance: {ex.Message}", ex);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Configure la probabilité de déclenchement de la maintenance.
        /// </summary>
        /// <param name="probabilityPercent">Probabilité en pourcentage (0-100)</param>
        public void SetMaintenanceProbability(int probabilityPercent)
        {
            if (probabilityPercent < 0 || probabilityPercent > 100)
            {
                throw new ArgumentOutOfRangeException(nameof(probabilityPercent), "La probabilité doit être entre 0 et 100");
            }

            _maintenanceProbabilityPercent = probabilityPercent;
            _loggingService.LogInfo($"[HistoryMaintenanceService] Probabilité de maintenance configurée à {probabilityPercent}%");
        }

        /// <summary>
        /// Obtient la probabilité actuelle de déclenchement de la maintenance.
        /// </summary>
        /// <returns>Probabilité en pourcentage (0-100)</returns>
        public int GetMaintenanceProbability()
        {
            return _maintenanceProbabilityPercent;
        }
    }
}
