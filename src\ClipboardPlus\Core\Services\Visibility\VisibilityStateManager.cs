using System;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Visibility
{
    /// <summary>
    /// Gestionnaire centralisé des états de visibilité
    /// Respecte le Single Responsibility Principle : coordonne les règles de visibilité
    /// Respecte le Dependency Inversion Principle : dépend d'abstractions (interfaces)
    /// </summary>
    public class VisibilityStateManager : IVisibilityStateManager
    {
        private readonly IVisibilityRule<ClipboardItem> _titleRule;
        private readonly IVisibilityRule<ClipboardItem> _timestampRule;
        private readonly ISettingsManager _settingsManager;
        private readonly ILoggingService _loggingService;

        private VisibilityContext _context = null!; // Initialisé dans le constructeur

        /// <summary>
        /// Événement déclenché lors d'un changement d'état de visibilité
        /// </summary>
        public event EventHandler<VisibilityChangedEventArgs>? VisibilityChanged;

        /// <summary>
        /// État actuel de la visibilité globale des titres
        /// </summary>
        public bool GlobalTitleVisibility => _context.GlobalTitleVisibility;

        /// <summary>
        /// État actuel de la visibilité globale des horodatages
        /// </summary>
        public bool GlobalTimestampVisibility => _context.GlobalTimestampVisibility;

        /// <summary>
        /// Initialise une nouvelle instance du gestionnaire de visibilité
        /// </summary>
        /// <param name="titleRule">Règle de visibilité pour les titres</param>
        /// <param name="timestampRule">Règle de visibilité pour les horodatages</param>
        /// <param name="settingsManager">Gestionnaire des paramètres</param>
        /// <param name="loggingService">Service de logging</param>
        /// <exception cref="ArgumentNullException">Si un paramètre requis est null</exception>
        public VisibilityStateManager(
            IVisibilityRule<ClipboardItem> titleRule,
            IVisibilityRule<ClipboardItem> timestampRule,
            ISettingsManager settingsManager,
            ILoggingService loggingService)
        {
            _titleRule = titleRule ?? throw new ArgumentNullException(nameof(titleRule));
            _timestampRule = timestampRule ?? throw new ArgumentNullException(nameof(timestampRule));
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));

            _loggingService.LogInfo("[VISIBILITY_STATE_MANAGER] Constructeur - Initialisation");
            InitializeContext();
            _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] Initialisé - TitleVisibility: {_context.GlobalTitleVisibility}, TimestampVisibility: {_context.GlobalTimestampVisibility}");
        }

        /// <summary>
        /// Détermine si le titre d'un élément spécifique doit être affiché
        /// </summary>
        /// <param name="item">Élément à évaluer</param>
        /// <returns>True si le titre doit être visible</returns>
        public bool ShouldShowTitle(ClipboardItem item)
        {
            try
            {
                bool result = _titleRule.ShouldBeVisible(item, _context);
                // ⚡ OPTIMISATION RADICALE : Logs désactivés car tous identiques (GlobalTitleVisibility: True, Result: False)
                // _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] ShouldShowTitle - Item ID: {item.Id}, GlobalTitleVisibility: {_context.GlobalTitleVisibility}, Result: {result}");
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"[VISIBILITY_STATE_MANAGER] Error in ShouldShowTitle for item {item.Id}. Defaulting to TRUE.", ex);
                return true; // Default to visible to avoid hiding important info on error
            }
        }

        /// <summary>
        /// Détermine si l'horodatage d'un élément spécifique doit être affiché
        /// </summary>
        /// <param name="item">Élément à évaluer</param>
        /// <returns>True si l'horodatage doit être visible</returns>
        public bool ShouldShowTimestamp(ClipboardItem item)
        {
            try
            {
                bool result = _timestampRule.ShouldBeVisible(item, _context);
                // ⚡ OPTIMISATION RADICALE : Logs désactivés car tous identiques (GlobalTimestampVisibility: True, Result: True)
                // _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] ShouldShowTimestamp - Item ID: {item.Id}, GlobalTimestampVisibility: {_context.GlobalTimestampVisibility}, Result: {result}");
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"[VISIBILITY_STATE_MANAGER] Error in ShouldShowTimestamp for item {item.Id}. Defaulting to TRUE.", ex);
                return true; // Default to visible to avoid hiding important info on error
            }
        }

        /// <summary>
        /// Met à jour la visibilité globale des titres
        /// Ne déclenche l'événement que si la valeur change réellement
        /// </summary>
        /// <param name="isVisible">Nouvel état de visibilité</param>
        public void UpdateGlobalTitleVisibility(bool isVisible)
        {
            try
            {
                _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] UpdateGlobalTitleVisibility - Ancien: {_context.GlobalTitleVisibility}, Nouveau: {isVisible}");

                if (_context.GlobalTitleVisibility != isVisible)
                {
                    _context.GlobalTitleVisibility = isVisible;
                    _settingsManager.HideItemTitle = !isVisible;

                    _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] TitleVisibility changée - Nouvelle valeur: {isVisible}, HideItemTitle mis à: {!isVisible}");

                    VisibilityChanged?.Invoke(this,
                        new VisibilityChangedEventArgs(VisibilityType.Title, isVisible));

                    _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] Événement VisibilityChanged déclenché pour Title");
                }
                else
                {
                    _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] TitleVisibility inchangée - Aucune action");
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("[VISIBILITY_STATE_MANAGER] Error in UpdateGlobalTitleVisibility.", ex);
                // Do not rethrow, as this is not a critical failure.
            }
        }

        /// <summary>
        /// Met à jour la visibilité globale des horodatages
        /// Ne déclenche l'événement que si la valeur change réellement
        /// </summary>
        /// <param name="isVisible">Nouvel état de visibilité</param>
        public void UpdateGlobalTimestampVisibility(bool isVisible)
        {
            _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] UpdateGlobalTimestampVisibility - Ancien: {_context.GlobalTimestampVisibility}, Nouveau: {isVisible}");

            if (_context.GlobalTimestampVisibility != isVisible)
            {
                _context.GlobalTimestampVisibility = isVisible;
                _settingsManager.HideTimestamp = !isVisible;

                _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] TimestampVisibility changée - Nouvelle valeur: {isVisible}, HideTimestamp mis à: {!isVisible}");

                VisibilityChanged?.Invoke(this,
                    new VisibilityChangedEventArgs(VisibilityType.Timestamp, isVisible));

                _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] Événement VisibilityChanged déclenché pour Timestamp");
            }
            else
            {
                _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] TimestampVisibility inchangée - Aucune action");
            }
        }

        /// <summary>
        /// Initialise le contexte de visibilité à partir des paramètres actuels
        /// </summary>
        private void InitializeContext()
        {
            bool hideItemTitle = _settingsManager.HideItemTitle;
            bool hideTimestamp = _settingsManager.HideTimestamp;

            _context = new VisibilityContext
            {
                GlobalTitleVisibility = !hideItemTitle,
                GlobalTimestampVisibility = !hideTimestamp
            };

            _loggingService?.LogInfo($"[VISIBILITY_STATE_MANAGER] InitializeContext - HideItemTitle: {hideItemTitle} -> GlobalTitleVisibility: {_context.GlobalTitleVisibility}");
            _loggingService?.LogInfo($"[VISIBILITY_STATE_MANAGER] InitializeContext - HideTimestamp: {hideTimestamp} -> GlobalTimestampVisibility: {_context.GlobalTimestampVisibility}");
        }

        /// <summary>
        /// Met à jour la visibilité globale des titres
        /// CORRECTION CRITIQUE : Méthode publique pour synchronisation depuis le ViewModel
        /// </summary>
        /// <param name="hideItemTitle">True pour masquer les titres</param>
        public void UpdateTitleVisibilityFromSettings(bool hideItemTitle)
        {
            bool newVisibility = !hideItemTitle;
            _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] UpdateTitleVisibilityFromSettings - HideItemTitle: {hideItemTitle}, NewVisibility: {newVisibility}, Current: {_context.GlobalTitleVisibility}");

            if (_context.GlobalTitleVisibility != newVisibility)
            {
                _context.GlobalTitleVisibility = newVisibility;
                _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] TitleVisibility mise à jour depuis Settings - Nouvelle valeur: {newVisibility}");

                VisibilityChanged?.Invoke(this, new VisibilityChangedEventArgs(VisibilityType.Title, newVisibility));
                _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] Événement VisibilityChanged déclenché depuis Settings pour Title");
            }
            else
            {
                _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] TitleVisibility depuis Settings - Aucun changement nécessaire");
            }
        }

        /// <summary>
        /// Met à jour la visibilité globale des horodatages
        /// CORRECTION CRITIQUE : Méthode publique pour synchronisation depuis le ViewModel
        /// </summary>
        /// <param name="hideTimestamp">True pour masquer les horodatages</param>
        public void UpdateTimestampVisibilityFromSettings(bool hideTimestamp)
        {
            bool newVisibility = !hideTimestamp;
            _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] UpdateTimestampVisibilityFromSettings - HideTimestamp: {hideTimestamp}, NewVisibility: {newVisibility}, Current: {_context.GlobalTimestampVisibility}");

            if (_context.GlobalTimestampVisibility != newVisibility)
            {
                _context.GlobalTimestampVisibility = newVisibility;
                _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] TimestampVisibility mise à jour depuis Settings - Nouvelle valeur: {newVisibility}");

                VisibilityChanged?.Invoke(this, new VisibilityChangedEventArgs(VisibilityType.Timestamp, newVisibility));
                _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] Événement VisibilityChanged déclenché depuis Settings pour Timestamp");
            }
            else
            {
                _loggingService.LogInfo($"[VISIBILITY_STATE_MANAGER] TimestampVisibility depuis Settings - Aucun changement nécessaire");
            }
        }
    }
}
