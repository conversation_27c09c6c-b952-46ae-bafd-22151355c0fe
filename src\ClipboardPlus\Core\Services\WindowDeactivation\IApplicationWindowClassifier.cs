using System;
using System.Collections.Generic;
using System.Windows;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Service de classification des fenêtres de l'application.
    /// Extrait de la méthode Window_Deactivated pour améliorer la testabilité et la maintenabilité.
    /// </summary>
    public interface IApplicationWindowClassifier
    {
        /// <summary>
        /// Détermine si une fenêtre appartient à l'application.
        /// </summary>
        /// <param name="window">Fenêtre à classifier</param>
        /// <param name="referenceWindow">Fenêtre de référence (généralement la fenêtre d'historique)</param>
        /// <returns>True si la fenêtre appartient à l'application, False sinon</returns>
        bool IsApplicationWindow(Window window, Window referenceWindow);

        /// <summary>
        /// Classifie une fenêtre avec des informations détaillées.
        /// </summary>
        /// <param name="window">Fenêtre à classifier</param>
        /// <param name="referenceWindow">Fenêtre de référence</param>
        /// <returns>Résultat détaillé de la classification</returns>
        WindowClassificationResult ClassifyWindow(Window window, Window referenceWindow);




    }

    /// <summary>
    /// Résultat de la classification d'une fenêtre.
    /// </summary>
    public class WindowClassificationResult
    {
        /// <summary>
        /// Indique si la fenêtre appartient à l'application.
        /// </summary>
        public bool IsApplicationWindow { get; set; }

        /// <summary>
        /// Type de relation avec la fenêtre de référence.
        /// </summary>
        public WindowRelationType RelationType { get; set; }

        /// <summary>
        /// Nom du type de la fenêtre.
        /// </summary>
        public string WindowTypeName { get; set; } = string.Empty;

        /// <summary>
        /// Nom complet du type de la fenêtre.
        /// </summary>
        public string WindowTypeFullName { get; set; } = string.Empty;

        /// <summary>
        /// Nom convivial du type de fenêtre (si enregistré).
        /// </summary>
        public string? FriendlyName { get; set; }

        /// <summary>
        /// Résultats détaillés de tous les tests de classification.
        /// </summary>
        public Dictionary<string, bool> ClassificationTests { get; set; } = new Dictionary<string, bool>();

        /// <summary>
        /// Raison de la classification (pour les logs).
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// Détails supplémentaires pour le diagnostic.
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// Horodatage de la classification.
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Durée de la classification en millisecondes.
        /// </summary>
        public double ClassificationDurationMs { get; set; }
    }

    /// <summary>
    /// Types de relation entre fenêtres.
    /// </summary>
    public enum WindowRelationType
    {
        /// <summary>
        /// Aucune relation détectée.
        /// </summary>
        None,

        /// <summary>
        /// Même propriétaire que la fenêtre de référence.
        /// </summary>
        SameOwner,

        /// <summary>
        /// La fenêtre de référence est le propriétaire.
        /// </summary>
        ReferenceIsOwner,

        /// <summary>
        /// Fenêtre de paramètres de l'application.
        /// </summary>
        SettingsWindow,

        /// <summary>
        /// Fenêtre de nettoyage avancé.
        /// </summary>
        CleanupWindow,

        /// <summary>
        /// Dialogue de création d'élément.
        /// </summary>
        NewItemDialog,

        /// <summary>
        /// Fenêtre de prévisualisation de contenu.
        /// </summary>
        PreviewWindow,

        /// <summary>
        /// Type de fenêtre personnalisé enregistré.
        /// </summary>
        CustomRegistered,

        /// <summary>
        /// Fenêtre externe à l'application.
        /// </summary>
        External
    }

    /// <summary>
    /// Configuration pour la classification des fenêtres.
    /// </summary>
    public class WindowClassificationConfig
    {
        /// <summary>
        /// Types de fenêtres reconnus par défaut avec leurs noms conviviaux.
        /// </summary>
        public static readonly Dictionary<string, string> DefaultWindowTypes = new Dictionary<string, string>
        {
            { "AppSettingsWindow", "Paramètres" },
            { "AdvancedCleanupWindow", "Nettoyage avancé" },
            { "NewItemEditorDialog", "Éditeur d'élément" },
            { "ItemContentPreviewWindow", "Prévisualisation" }
        };

        /// <summary>
        /// Indique si les tests de classification doivent être loggés en détail.
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = true;

        /// <summary>
        /// Indique si les métriques de performance doivent être collectées.
        /// </summary>
        public bool EnablePerformanceMetrics { get; set; } = false;
    }
}
