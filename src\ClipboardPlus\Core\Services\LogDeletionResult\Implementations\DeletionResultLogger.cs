using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Implementations
{
    /// <summary>
    /// Implémentation principale du logger de résultats de suppression.
    /// Orchestre tous les composants pour produire un logging complet et fiable.
    /// </summary>
    public class DeletionResultLogger : IDeletionResultLogger
    {
        private readonly ILoggingService _loggingService;
        private readonly IDeletionResultValidator _validator;
        private readonly IDeletionResultFormatter _formatter;
        private readonly ICollectionStateAnalyzer _collectionAnalyzer;
        private readonly IDeletionMetricsCollector _metricsCollector;
        
        private static readonly string LogFilePath = Path.Combine(
            GetProjectRootDirectory(), "logs", "deletion_diagnostic.log");

        public DeletionResultLogger(
            ILoggingService loggingService,
            IDeletionResultValidator validator,
            IDeletionResultFormatter formatter,
            ICollectionStateAnalyzer collectionAnalyzer,
            IDeletionMetricsCollector metricsCollector)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _formatter = formatter ?? throw new ArgumentNullException(nameof(formatter));
            _collectionAnalyzer = collectionAnalyzer ?? throw new ArgumentNullException(nameof(collectionAnalyzer));
            _metricsCollector = metricsCollector ?? throw new ArgumentNullException(nameof(metricsCollector));

            EnsureLogDirectoryExists();
        }

        /// <summary>
        /// Enregistre le résultat d'une suppression de manière asynchrone.
        /// </summary>
        public async Task<DeletionLoggingResult> LogDeletionResultAsync(DeletionResultContext context)
        {
            return await Task.Run(() => LogDeletionResult(context));
        }

        /// <summary>
        /// Enregistre le résultat d'une suppression de manière synchrone.
        /// </summary>
        public DeletionLoggingResult LogDeletionResult(DeletionResultContext context)
        {
            var stopwatch = Stopwatch.StartNew();
            var startMemory = GC.GetTotalMemory(false);

            try
            {
                _loggingService.LogDebug($"🚀 [LOGGER] Début logging suppression - Operation ID: {context.OperationId}");

                // Enregistrer la tentative dans les métriques
                _metricsCollector.RecordDeletionAttempt(context);

                // Phase 1: Validation complète
                var validationStopwatch = Stopwatch.StartNew();
                var validation = _validator.ValidateComplete(context);
                validationStopwatch.Stop();

                // Phase 2: Analyse de l'état des collections
                var analysisStopwatch = Stopwatch.StartNew();
                var collectionState = context.ViewModel != null 
                    ? _collectionAnalyzer.AnalyzeCollectionState(context.ViewModel)
                    : new CollectionStateInfo { AnalysisSuccessful = false, ErrorMessage = "ViewModel non disponible" };
                analysisStopwatch.Stop();

                // Phase 3: Formatage du log complet
                var formattingStopwatch = Stopwatch.StartNew();
                var logContent = _formatter.FormatCompleteLog(context, validation, collectionState);
                formattingStopwatch.Stop();

                // Phase 4: Écriture du log
                var writingStopwatch = Stopwatch.StartNew();
                WriteLogToFile(logContent);
                writingStopwatch.Stop();

                stopwatch.Stop();

                // Créer les métriques de performance
                var performanceMetrics = new OperationPerformanceMetrics
                {
                    TotalDuration = stopwatch.Elapsed,
                    ValidationDuration = validationStopwatch.Elapsed,
                    FormattingDuration = formattingStopwatch.Elapsed,
                    WritingDuration = writingStopwatch.Elapsed,
                    MemoryUsed = GC.GetTotalMemory(false) - startMemory,
                    DataSize = logContent.Length,
                    StartTime = context.Timestamp,
                    EndTime = DateTime.Now,
                    ThreadId = Environment.CurrentManagedThreadId
                };

                // Enregistrer les métriques
                _metricsCollector.RecordPerformanceMetrics(performanceMetrics);

                // Créer le résultat de succès
                var result = DeletionLoggingResult.Success(
                    "Logging de suppression terminé avec succès",
                    logContent.Length,
                    stopwatch.Elapsed);

                result.LogFilePath = LogFilePath;
                result.PerformanceMetrics = performanceMetrics;

                // Enregistrer le résultat
                _metricsCollector.RecordLoggingResult(result);

                _loggingService.LogDebug($"✅ [LOGGER] Logging terminé - Durée: {stopwatch.ElapsedMilliseconds}ms, " +
                                       $"Taille: {logContent.Length} caractères");

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                _loggingService.LogError($"❌ [LOGGER] Erreur lors du logging de suppression: {ex.Message}", ex);

                // Enregistrer l'erreur dans les métriques
                var error = new DeletionLoggingError
                {
                    ErrorType = ClassifyError(ex),
                    Message = ex.Message,
                    Exception = ex,
                    Context = context,
                    Component = "DeletionResultLogger",
                    Severity = DetermineErrorSeverity(ex),
                    IsRecoverable = IsRecoverableError(ex)
                };

                _metricsCollector.RecordError(error);

                // Créer le résultat d'échec
                var failureResult = DeletionLoggingResult.Failure(ex);
                failureResult.Duration = stopwatch.Elapsed;

                // Enregistrer le résultat d'échec
                _metricsCollector.RecordLoggingResult(failureResult);

                return failureResult;
            }
        }

        /// <summary>
        /// Obtient les métriques de performance du logger.
        /// </summary>
        public DeletionLoggerMetrics GetPerformanceMetrics()
        {
            try
            {
                return _metricsCollector.GetPerformanceMetrics();
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [LOGGER] Erreur lors de l'obtention des métriques: {ex.Message}", ex);
                return new DeletionLoggerMetrics();
            }
        }

        /// <summary>
        /// Réinitialise les métriques de performance.
        /// </summary>
        public void ResetMetrics()
        {
            try
            {
                _metricsCollector.ResetMetrics();
                _loggingService.LogInfo("🧹 [LOGGER] Métriques réinitialisées");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [LOGGER] Erreur lors de la réinitialisation des métriques: {ex.Message}", ex);
            }
        }

        #region Méthodes privées

        private void EnsureLogDirectoryExists()
        {
            try
            {
                var logDirectory = Path.GetDirectoryName(LogFilePath);
                if (logDirectory != null && !Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                    _loggingService.LogDebug($"📁 [LOGGER] Répertoire de logs créé: {logDirectory}");
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [LOGGER] Erreur lors de la création du répertoire de logs: {ex.Message}", ex);
            }
        }

        private void WriteLogToFile(string logContent)
        {
            try
            {
                // Écriture thread-safe avec retry
                const int maxRetries = 3;
                for (int attempt = 1; attempt <= maxRetries; attempt++)
                {
                    try
                    {
                        File.AppendAllText(LogFilePath, logContent + Environment.NewLine);
                        return;
                    }
                    catch (IOException) when (attempt < maxRetries)
                    {
                        // Attendre un peu avant de réessayer
                        System.Threading.Thread.Sleep(10 * attempt);
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [LOGGER] Erreur lors de l'écriture du fichier de log: {ex.Message}", ex);
                
                // Fallback: essayer d'écrire dans un fichier temporaire
                try
                {
                    var tempLogPath = Path.Combine(Path.GetTempPath(), $"clipboard_deletion_log_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                    File.WriteAllText(tempLogPath, logContent);
                    _loggingService.LogWarning($"⚠️ [LOGGER] Log écrit dans fichier temporaire: {tempLogPath}");
                }
                catch (Exception tempEx)
                {
                    _loggingService.LogError($"❌ [LOGGER] Échec écriture fichier temporaire: {tempEx.Message}", tempEx);
                    throw; // Re-lancer l'exception originale
                }
            }
        }

        private LoggingErrorType ClassifyError(Exception ex)
        {
            return ex switch
            {
                ArgumentNullException => LoggingErrorType.ValidationError,
                InvalidOperationException => LoggingErrorType.ValidationError,
                FormatException => LoggingErrorType.FormattingError,
                IOException => LoggingErrorType.FileWriteError,
                UnauthorizedAccessException => LoggingErrorType.PermissionError,
                OutOfMemoryException => LoggingErrorType.OutOfMemoryError,
                TimeoutException => LoggingErrorType.TimeoutError,
                _ => LoggingErrorType.UnknownError
            };
        }

        private ErrorSeverity DetermineErrorSeverity(Exception ex)
        {
            return ex switch
            {
                OutOfMemoryException => ErrorSeverity.Critical,
                UnauthorizedAccessException => ErrorSeverity.High,
                IOException => ErrorSeverity.Medium,
                ArgumentNullException => ErrorSeverity.Medium,
                FormatException => ErrorSeverity.Low,
                _ => ErrorSeverity.Medium
            };
        }

        private bool IsRecoverableError(Exception ex)
        {
            return ex switch
            {
                OutOfMemoryException => false,
                UnauthorizedAccessException => false,
                IOException => true,
                ArgumentNullException => true,
                FormatException => true,
                TimeoutException => true,
                _ => true
            };
        }

        /// <summary>
        /// Trouve le répertoire racine du projet en remontant depuis le répertoire de l'exécutable.
        /// </summary>
        /// <returns>Le chemin du répertoire racine du projet.</returns>
        private static string GetProjectRootDirectory()
        {
            try
            {
                // Commencer par le répertoire de l'assembly en cours
                var currentDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);

                if (string.IsNullOrEmpty(currentDirectory))
                {
                    currentDirectory = Directory.GetCurrentDirectory();
                }

                // Remonter jusqu'à trouver le fichier .sln ou le dossier src
                var directory = new DirectoryInfo(currentDirectory);
                while (directory != null && directory.Parent != null)
                {
                    // Chercher un fichier .sln ou un dossier src
                    if (directory.GetFiles("*.sln").Length > 0 ||
                        directory.GetDirectories("src").Length > 0)
                    {
                        return directory.FullName;
                    }
                    directory = directory.Parent;
                }

                // Fallback : utiliser le répertoire courant
                return Directory.GetCurrentDirectory();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la recherche du répertoire racine du projet: {ex.Message}");
                // Fallback : utiliser le répertoire courant
                return Directory.GetCurrentDirectory();
            }
        }

        #endregion
    }
}
