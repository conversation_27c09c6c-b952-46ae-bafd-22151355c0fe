using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Services.Settings;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.DataModels.Settings;

namespace ClipboardPlus.Tests.Unit.Services.Settings
{
    [TestFixture]
    public class SettingsApplicationServiceTests
    {
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IGlobalShortcutService> _mockGlobalShortcutService = null!;
        private Mock<IUserNotificationService> _mockUserNotificationService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private SettingsApplicationService _service = null!;

        [SetUp]
        public void Setup()
        {
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockSettingsManager.SetupAllProperties(); // Permet la lecture ET l'écriture des propriétés
            _mockGlobalShortcutService = new Mock<IGlobalShortcutService>();
            _mockUserNotificationService = new Mock<IUserNotificationService>();
            _mockLoggingService = new Mock<ILoggingService>();

            _service = new SettingsApplicationService(
                _mockSettingsManager.Object,
                _mockGlobalShortcutService.Object,
                _mockUserNotificationService.Object,
                _mockLoggingService.Object);
        }

        [Test]
        public async Task ApplyBasicSettingsAsync_ValidSettings_Success()
        {
            // Arrange
            var settings = new BasicSettingsData(
                MaxHistoryItems: 100,
                StartWithWindows: true,
                ShortcutKeyCombination: "Ctrl+Alt+V",
                MaxImageDimensionForThumbnail: 256,
                MaxStorableItemSizeBytes: 10485760L
            );

            _mockSettingsManager.Setup(x => x.ShortcutKeyCombination).Returns("Ctrl+Alt+C"); // Différent pour déclencher le changement
            _mockGlobalShortcutService.Setup(x => x.TryRegisterShortcutAsync(It.IsAny<KeyCombination>())).ReturnsAsync(true);

            // Act
            var result = await _service.ApplyBasicSettingsAsync(settings);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.AppliedSettings.Count, Is.GreaterThan(0));
            
            // Vérifier que les paramètres ont été appliqués au SettingsManager
            _mockSettingsManager.VerifySet(x => x.MaxHistoryItems = 100, Times.Once);
            _mockSettingsManager.VerifySet(x => x.StartWithWindows = true, Times.Once);
            _mockSettingsManager.VerifySet(x => x.MaxImageDimensionForThumbnail = 256, Times.Once);
            _mockSettingsManager.VerifySet(x => x.MaxStorableItemSizeBytes = 10485760L, Times.Once);
        }

        [Test]
        public async Task ApplyBasicSettingsAsync_ShortcutRegistrationFails_PartialSuccess()
        {
            // Arrange
            var settings = new BasicSettingsData(
                MaxHistoryItems: 100,
                StartWithWindows: true,
                ShortcutKeyCombination: "Ctrl+Alt+V",
                MaxImageDimensionForThumbnail: 256,
                MaxStorableItemSizeBytes: 10485760L
            );

            _mockSettingsManager.Setup(x => x.ShortcutKeyCombination).Returns("Ctrl+Alt+C"); // Différent
            _mockGlobalShortcutService.Setup(x => x.TryRegisterShortcutAsync(It.IsAny<KeyCombination>())).ReturnsAsync(false);

            // Act
            var result = await _service.ApplyBasicSettingsAsync(settings);

            // Assert
            Assert.That(result.Success, Is.False); // Échec à cause du raccourci
            Assert.That(result.Errors.Count, Is.GreaterThan(0));
            Assert.That(result.AppliedSettings.ContainsKey("MaxHistoryItems"), Is.True); // Autres paramètres appliqués
            
            // Vérifier la notification d'erreur
            _mockUserNotificationService.Verify(x => x.ShowError("Erreur raccourci", It.IsAny<string>()), Times.Once);
        }

        [Test]
        public async Task ApplyAdvancedSettingsAsync_ValidSettings_Success()
        {
            // Arrange
            var theme = new ThemeInfo("Dark", "Themes/Dark.xaml");
            var settings = new AdvancedSettingsData(
                HideTimestamp: true,
                HideItemTitle: false,
                SelectedTheme: theme
            );

            // Act
            var result = await _service.ApplyAdvancedSettingsAsync(settings);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.AppliedSettings.Count, Is.EqualTo(3));
            
            // Vérifier que les paramètres ont été appliqués
            _mockSettingsManager.VerifySet(x => x.HideTimestamp = true, Times.Once);
            _mockSettingsManager.VerifySet(x => x.HideItemTitle = false, Times.Once);
            _mockSettingsManager.VerifySet(x => x.ActiveThemePath = "Themes/Dark.xaml", Times.Once);
        }

        [Test]
        public async Task ApplyAdvancedSettingsAsync_NoTheme_Success()
        {
            // Arrange
            var settings = new AdvancedSettingsData(
                HideTimestamp: true,
                HideItemTitle: false,
                SelectedTheme: null
            );

            // Act
            var result = await _service.ApplyAdvancedSettingsAsync(settings);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.AppliedSettings.Count, Is.EqualTo(2)); // Pas de thème
            
            // Vérifier que le thème n'a pas été modifié
            _mockSettingsManager.VerifySet(x => x.ActiveThemePath = It.IsAny<string>(), Times.Never);
        }
    }
}
