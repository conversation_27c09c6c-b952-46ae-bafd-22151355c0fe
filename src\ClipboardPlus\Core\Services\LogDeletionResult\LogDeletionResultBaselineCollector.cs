using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.LogDeletionResult
{
    /// <summary>
    /// Collecteur de métriques baseline pour LogDeletionResult.
    /// Enregistre les performances et comportements de référence.
    /// </summary>
    public class LogDeletionResultBaselineCollector
    {
        private readonly ConcurrentQueue<LogDeletionResultExecution> _executions;
        private readonly ILoggingService _loggingService;
        private readonly object _lockObject = new object();
        private DateTime _collectionStartTime;

        public LogDeletionResultBaselineCollector(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _executions = new ConcurrentQueue<LogDeletionResultExecution>();
            _collectionStartTime = DateTime.Now;
        }

        /// <summary>
        /// Enregistre une exécution de LogDeletionResult.
        /// </summary>
        /// <param name="execution">Détails de l'exécution</param>
        public void RecordExecution(LogDeletionResultExecution execution)
        {
            if (execution == null)
            {
                _loggingService.LogWarning("⚠️ [BASELINE] Tentative d'enregistrement d'une exécution null");
                return;
            }

            try
            {
                _executions.Enqueue(execution);
                
                // Log périodique des statistiques
                if (_executions.Count % 10 == 0)
                {
                    LogPeriodicStatistics();
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [BASELINE] Erreur lors de l'enregistrement: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Obtient les métriques baseline actuelles.
        /// </summary>
        /// <returns>Métriques de baseline</returns>
        public LogDeletionResultBaselineMetrics GetBaseline()
        {
            lock (_lockObject)
            {
                try
                {
                    var executions = _executions.ToArray();
                    
                    if (executions.Length == 0)
                    {
                        return new LogDeletionResultBaselineMetrics
                        {
                            CollectionStartTime = _collectionStartTime,
                            LastUpdateTime = DateTime.Now,
                            TotalExecutions = 0
                        };
                    }

                    var successfulExecutions = executions.Where(e => e.IsSuccessful).ToArray();
                    var failedExecutions = executions.Where(e => !e.IsSuccessful).ToArray();

                    return new LogDeletionResultBaselineMetrics
                    {
                        CollectionStartTime = _collectionStartTime,
                        LastUpdateTime = DateTime.Now,
                        TotalExecutions = executions.Length,
                        SuccessfulExecutions = successfulExecutions.Length,
                        FailedExecutions = failedExecutions.Length,
                        SuccessRate = executions.Length > 0 ? (double)successfulExecutions.Length / executions.Length * 100 : 0,
                        
                        // Métriques de performance
                        AverageExecutionTimeMs = successfulExecutions.Length > 0 ? successfulExecutions.Average(e => e.ExecutionTimeMs) : 0,
                        MinExecutionTimeMs = successfulExecutions.Length > 0 ? successfulExecutions.Min(e => e.ExecutionTimeMs) : 0,
                        MaxExecutionTimeMs = successfulExecutions.Length > 0 ? successfulExecutions.Max(e => e.ExecutionTimeMs) : 0,
                        MedianExecutionTimeMs = CalculateMedian(successfulExecutions.Select(e => e.ExecutionTimeMs).ToArray()),
                        
                        // Métriques mémoire
                        AverageMemoryUsageBytes = successfulExecutions.Length > 0 ? successfulExecutions.Average(e => e.MemoryUsedBytes) : 0,
                        MaxMemoryUsageBytes = successfulExecutions.Length > 0 ? successfulExecutions.Max(e => e.MemoryUsedBytes) : 0,
                        
                        // Répartition par type d'opération
                        SuccessfulDeletions = executions.Count(e => e.Success && e.IsSuccessful),
                        FailedDeletions = executions.Count(e => !e.Success && e.IsSuccessful),
                        NullItemExecutions = executions.Count(e => !e.ItemId.HasValue),
                        
                        // Threads utilisés
                        UniqueThreadsUsed = executions.Select(e => e.ThreadId).Distinct().Count(),
                        
                        // Erreurs communes
                        CommonErrors = GetCommonErrors(failedExecutions),
                        
                        // Tendances temporelles
                        ExecutionsPerHour = CalculateExecutionsPerHour(executions),
                        
                        // Dernières exécutions pour analyse
                        RecentExecutions = executions.TakeLast(10).ToList()
                    };
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"❌ [BASELINE] Erreur lors du calcul des métriques: {ex.Message}", ex);
                    return new LogDeletionResultBaselineMetrics
                    {
                        CollectionStartTime = _collectionStartTime,
                        LastUpdateTime = DateTime.Now,
                        HasError = true,
                        ErrorMessage = ex.Message
                    };
                }
            }
        }

        /// <summary>
        /// Réinitialise les métriques collectées.
        /// </summary>
        public void ResetMetrics()
        {
            lock (_lockObject)
            {
                try
                {
                    // Vider la queue
                    while (_executions.TryDequeue(out _)) { }
                    
                    _collectionStartTime = DateTime.Now;
                    _loggingService.LogInfo("🧹 [BASELINE] Métriques réinitialisées");
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"❌ [BASELINE] Erreur lors de la réinitialisation: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Calcule la médiane d'un tableau de valeurs.
        /// </summary>
        private double CalculateMedian(long[] values)
        {
            if (values.Length == 0) return 0;
            
            Array.Sort(values);
            int middle = values.Length / 2;
            
            if (values.Length % 2 == 0)
            {
                return (values[middle - 1] + values[middle]) / 2.0;
            }
            else
            {
                return values[middle];
            }
        }

        /// <summary>
        /// Obtient les erreurs les plus communes.
        /// </summary>
        private Dictionary<string, int> GetCommonErrors(LogDeletionResultExecution[] failedExecutions)
        {
            return failedExecutions
                .Where(e => !string.IsNullOrEmpty(e.ErrorMessage))
                .GroupBy(e => e.ErrorMessage!)
                .ToDictionary(g => g.Key, g => g.Count())
                .OrderByDescending(kvp => kvp.Value)
                .Take(5)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// Calcule le nombre d'exécutions par heure.
        /// </summary>
        private double CalculateExecutionsPerHour(LogDeletionResultExecution[] executions)
        {
            if (executions.Length == 0) return 0;
            
            var timeSpan = DateTime.Now - _collectionStartTime;
            if (timeSpan.TotalHours < 0.01) return 0; // Éviter division par zéro
            
            return executions.Length / timeSpan.TotalHours;
        }

        /// <summary>
        /// Log périodique des statistiques.
        /// </summary>
        private void LogPeriodicStatistics()
        {
            try
            {
                var metrics = GetBaseline();
                _loggingService.LogInfo($"📊 [BASELINE] Statistiques: {metrics.TotalExecutions} exécutions, " +
                                      $"Succès: {metrics.SuccessRate:F1}%, " +
                                      $"Temps moyen: {metrics.AverageExecutionTimeMs:F1}ms");
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [BASELINE] Erreur lors du log périodique: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Détails d'une exécution de LogDeletionResult.
    /// </summary>
    public class LogDeletionResultExecution
    {
        public Guid ExecutionId { get; set; }
        public bool Success { get; set; }
        public int? ItemId { get; set; }
        public string? Message { get; set; }
        public long ExecutionTimeMs { get; set; }
        public long MemoryUsedBytes { get; set; }
        public int ThreadId { get; set; }
        public DateTime Timestamp { get; set; }
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ErrorStackTrace { get; set; }
    }

    /// <summary>
    /// Métriques baseline de LogDeletionResult.
    /// </summary>
    public class LogDeletionResultBaselineMetrics
    {
        public DateTime CollectionStartTime { get; set; }
        public DateTime LastUpdateTime { get; set; }
        public int TotalExecutions { get; set; }
        public int SuccessfulExecutions { get; set; }
        public int FailedExecutions { get; set; }
        public double SuccessRate { get; set; }
        
        // Performance
        public double AverageExecutionTimeMs { get; set; }
        public long MinExecutionTimeMs { get; set; }
        public long MaxExecutionTimeMs { get; set; }
        public double MedianExecutionTimeMs { get; set; }
        
        // Mémoire
        public double AverageMemoryUsageBytes { get; set; }
        public long MaxMemoryUsageBytes { get; set; }
        
        // Opérations
        public int SuccessfulDeletions { get; set; }
        public int FailedDeletions { get; set; }
        public int NullItemExecutions { get; set; }
        
        // Threading
        public int UniqueThreadsUsed { get; set; }
        
        // Erreurs
        public Dictionary<string, int> CommonErrors { get; set; } = new();
        
        // Tendances
        public double ExecutionsPerHour { get; set; }
        
        // Données récentes
        public List<LogDeletionResultExecution> RecentExecutions { get; set; } = new();
        
        // État d'erreur
        public bool HasError { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
