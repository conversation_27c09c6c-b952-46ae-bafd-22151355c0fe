using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Tests.STA.Mocks;

namespace ClipboardPlus.Tests.STA
{
    /// <summary>
    /// Tests de régression pour l'API Windows de gestion des raccourcis.
    /// Ces tests vérifient que la logique d'enregistrement des raccourcis fonctionne correctement.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class WindowsHotkeyApiTests
    {
        private MockWindowsHotkeyApi _mockApi = null!;
        private GlobalShortcutService _shortcutService = null!;

        [SetUp]
        public void Setup()
        {
            _mockApi = new MockWindowsHotkeyApi();
            _shortcutService = new GlobalShortcutService(null, _mockApi);
        }

        [TearDown]
        public void Teardown()
        {
            _mockApi.Reset();
        }

        /// <summary>
        /// TEST DE RÉGRESSION CRITIQUE - Vérifie que l'enregistrement d'un raccourci valide réussit.
        /// </summary>
        [Test]
        [Timeout(1000)]
        public async Task TryRegisterShortcutAsync_WithValidShortcut_ShouldSucceed()
        {
            // Arrange
            _mockApi.ShouldRegisterSucceed = true;
            var shortcut = new KeyCombination(ModifierKeys.Control, Key.F1);

            TestContext.WriteLine("=== TEST DE RÉGRESSION - ENREGISTREMENT RÉUSSI ===");
            TestContext.WriteLine($"Raccourci à tester: {shortcut}");

            // Act
            var result = await _shortcutService.TryRegisterShortcutAsync(shortcut);

            // Assert
            TestContext.WriteLine($"Résultat de l'enregistrement: {result}");
            Assert.IsTrue(result, "L'enregistrement du raccourci devrait réussir");

            Assert.AreEqual(1, _mockApi.RegisteredHotkeys.Count, "Un seul raccourci devrait être enregistré");

            var registered = _mockApi.RegisteredHotkeys[0];
            TestContext.WriteLine($"Raccourci enregistré - ID: {registered.Id}, Modifiers: {registered.Modifiers}, VK: {registered.VirtualKey}");

            // Vérifier que les modificateurs sont correctement convertis
            const uint MOD_CONTROL = 0x0002;
            Assert.AreEqual(MOD_CONTROL, registered.Modifiers, "Les modificateurs devraient être correctement convertis");

            TestContext.WriteLine("✅ Test d'enregistrement réussi terminé avec succès");
        }

        /// <summary>
        /// TEST DE RÉGRESSION CRITIQUE - Vérifie la gestion des échecs d'enregistrement.
        /// </summary>
        [Test]
        [Timeout(1000)]
        public async Task TryRegisterShortcutAsync_WhenWindowsApiFails_ShouldReturnFalse()
        {
            // Arrange
            _mockApi.ShouldRegisterSucceed = false;
            var shortcut = new KeyCombination(ModifierKeys.Control, Key.C);

            TestContext.WriteLine("=== TEST DE RÉGRESSION - ÉCHEC D'ENREGISTREMENT ===");
            TestContext.WriteLine($"Raccourci à tester: {shortcut}");

            // Act
            var result = await _shortcutService.TryRegisterShortcutAsync(shortcut);

            // Assert
            TestContext.WriteLine($"Résultat de l'enregistrement: {result}");
            Assert.IsFalse(result, "L'enregistrement devrait échouer quand l'API Windows échoue");

            Assert.AreEqual(0, _mockApi.RegisteredHotkeys.Count, "Aucun raccourci ne devrait être enregistré en cas d'échec");

            TestContext.WriteLine("✅ Test d'échec d'enregistrement terminé avec succès");
        }

        /// <summary>
        /// TEST DE RÉGRESSION CRITIQUE - Vérifie la conversion correcte des modificateurs.
        /// </summary>
        [Test]
        [Timeout(1000)]
        public async Task TryRegisterShortcutAsync_ShouldConvertModifiersCorrectly()
        {
            // Arrange
            _mockApi.ShouldRegisterSucceed = true;
            var shortcut = new KeyCombination(
                ModifierKeys.Control | ModifierKeys.Alt | ModifierKeys.Shift,
                Key.F12);

            TestContext.WriteLine("=== TEST DE RÉGRESSION - CONVERSION DES MODIFICATEURS ===");
            TestContext.WriteLine($"Raccourci à tester: {shortcut}");

            // Act
            await _shortcutService.TryRegisterShortcutAsync(shortcut);

            // Assert
            Assert.AreEqual(1, _mockApi.RegisteredHotkeys.Count, "Un raccourci devrait être enregistré");

            var registered = _mockApi.RegisteredHotkeys[0];

            // Vérifier la conversion des modificateurs
            const uint MOD_CONTROL = 0x0002;
            const uint MOD_ALT = 0x0001;
            const uint MOD_SHIFT = 0x0004;
            var expectedModifiers = MOD_CONTROL | MOD_ALT | MOD_SHIFT;

            TestContext.WriteLine($"Modificateurs attendus: {expectedModifiers:X}");
            TestContext.WriteLine($"Modificateurs reçus: {registered.Modifiers:X}");

            Assert.AreEqual(expectedModifiers, registered.Modifiers,
                "Les modificateurs combinés devraient être correctement convertis");

            TestContext.WriteLine("✅ Test de conversion des modificateurs terminé avec succès");
        }

        /// <summary>
        /// TEST DE RÉGRESSION CRITIQUE - Vérifie le désenregistrement des raccourcis.
        /// </summary>
        [Test]
        [Timeout(1000)]
        public async Task UnregisterShortcut_ShouldCallWindowsApi()
        {
            // Arrange - Enregistrer d'abord un raccourci
            _mockApi.ShouldRegisterSucceed = true;
            _mockApi.ShouldUnregisterSucceed = true;
            var shortcut = new KeyCombination(ModifierKeys.Control, Key.F2);

            TestContext.WriteLine("=== TEST DE RÉGRESSION - DÉSENREGISTREMENT ===");
            TestContext.WriteLine($"Raccourci à tester: {shortcut}");

            var registerResult = await _shortcutService.TryRegisterShortcutAsync(shortcut);
            Assert.IsTrue(registerResult, "L'enregistrement devrait réussir");
            Assert.AreEqual(1, _mockApi.RegisteredHotkeys.Count, "Le raccourci devrait être enregistré");

            // Act
            _shortcutService.UnregisterShortcut();

            // Assert
            TestContext.WriteLine($"Nombre de désenregistrements: {_mockApi.UnregisteredHotkeyIds.Count}");
            Assert.AreEqual(1, _mockApi.UnregisteredHotkeyIds.Count,
                "Un désenregistrement devrait avoir été effectué");

            var registeredId = _mockApi.RegisteredHotkeys[0].Id;
            var unregisteredId = _mockApi.UnregisteredHotkeyIds[0];

            TestContext.WriteLine($"ID enregistré: {registeredId}");
            TestContext.WriteLine($"ID désenregistré: {unregisteredId}");

            Assert.AreEqual(registeredId, unregisteredId,
                "L'ID désenregistré devrait correspondre à l'ID enregistré");

            TestContext.WriteLine("✅ Test de désenregistrement terminé avec succès");
        }

        /// <summary>
        /// TEST DE RÉGRESSION - Vérifie la gestion des raccourcis null.
        /// </summary>
        [Test]
        public void TryRegisterShortcutAsync_WithNullShortcut_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(() =>
                _shortcutService.TryRegisterShortcutAsync(null));

            TestContext.WriteLine("✅ Test de validation null terminé avec succès");
        }
    }
}
