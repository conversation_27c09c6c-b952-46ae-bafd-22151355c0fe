using System;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Service de validation des conditions préalables pour la désactivation de fenêtre.
    /// Extrait de la méthode Window_Deactivated pour améliorer la testabilité et la maintenabilité.
    /// </summary>
    public interface IWindowStateValidator
    {
        /// <summary>
        /// Détermine si la désactivation de fenêtre doit être ignorée en fonction des conditions préalables.
        /// </summary>
        /// <param name="isClosing">Indique si la fenêtre est en cours de fermeture</param>
        /// <param name="isOperationInProgress">Indique si une opération ViewModel est en cours</param>
        /// <returns>True si la désactivation doit être ignorée, False sinon</returns>
        bool ShouldIgnoreDeactivation(bool isClosing, bool isOperationInProgress);

        /// <summary>
        /// Valide l'état d'une fenêtre avec des informations détaillées.
        /// </summary>
        /// <param name="validationContext">Contexte de validation contenant tous les paramètres nécessaires</param>
        /// <returns>Résultat de validation avec détails</returns>
        WindowStateValidationResult ValidateWindowState(WindowStateValidationContext validationContext);
    }

    /// <summary>
    /// Contexte de validation pour l'état d'une fenêtre.
    /// </summary>
    public class WindowStateValidationContext
    {
        /// <summary>
        /// Indique si la fenêtre est en cours de fermeture.
        /// </summary>
        public bool IsClosing { get; set; }

        /// <summary>
        /// Indique si une opération ViewModel est en cours.
        /// </summary>
        public bool IsOperationInProgress { get; set; }

        /// <summary>
        /// Nom de la fenêtre pour les logs.
        /// </summary>
        public string WindowName { get; set; } = string.Empty;

        /// <summary>
        /// Indique si la fenêtre est actuellement active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Horodatage de la validation.
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Résultat de la validation de l'état d'une fenêtre.
    /// </summary>
    public class WindowStateValidationResult
    {
        /// <summary>
        /// Indique si la désactivation doit être ignorée.
        /// </summary>
        public bool ShouldIgnore { get; set; }

        /// <summary>
        /// Raison de la décision (pour les logs).
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// Type de validation qui a déterminé le résultat.
        /// </summary>
        public WindowStateValidationType ValidationType { get; set; }

        /// <summary>
        /// Détails supplémentaires pour le diagnostic.
        /// </summary>
        public string Details { get; set; } = string.Empty;
    }

    /// <summary>
    /// Types de validation d'état de fenêtre.
    /// </summary>
    public enum WindowStateValidationType
    {
        /// <summary>
        /// Validation normale - aucune condition d'arrêt précoce.
        /// </summary>
        Normal,

        /// <summary>
        /// Arrêt précoce - fenêtre en cours de fermeture.
        /// </summary>
        WindowClosing,

        /// <summary>
        /// Arrêt précoce - opération ViewModel en cours.
        /// </summary>
        OperationInProgress,

        /// <summary>
        /// Erreur de validation.
        /// </summary>
        ValidationError
    }
}
