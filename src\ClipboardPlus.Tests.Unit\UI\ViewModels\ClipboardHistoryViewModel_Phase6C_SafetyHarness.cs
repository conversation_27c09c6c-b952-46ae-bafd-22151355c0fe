// ============================================================================
// HARNAIS DE SÉCURITÉ - PHASE 6C EXTRACTION DES MANAGERS
// ============================================================================
//
// 🛡️ OBJECTIF : Verrouiller le comportement fonctionnel actuel avant refactoring
// 📋 RÉFÉRENCE : docs/fonctions.md - Source de vérité fonctionnelle
// 🎯 CIBLE : ClipboardHistoryViewModel (3031 lignes → < 200 lignes)
//
// ⚠️ RÈGLE CRITIQUE : Ces tests valident le COMPORTEMENT EXTERNE, pas l'implémentation
// Si un test échoue après refactoring, c'est le TEST qui doit être adapté, pas le code.
//
// ============================================================================

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Tests.Unit.TestHelpers;
using NUnit.Framework;
using Moq;

namespace ClipboardPlus.Tests.Unit.UI.ViewModels
{
    /// <summary>
    /// Harnais de sécurité pour la Phase 6C - Extraction des Managers.
    ///
    /// Ces tests capturent le comportement fonctionnel actuel du ClipboardHistoryViewModel
    /// avec l'architecture fragmentée (8 fichiers partiels) pour garantir qu'aucune
    /// régression ne sera introduite lors de l'extraction vers l'architecture managériale.
    /// </summary>
    [TestFixture]
    public class ClipboardHistoryViewModel_Phase6C_SafetyHarness
    {
        private ClipboardHistoryViewModel _viewModel;
        private TestViewModelFactory _factory;

        [SetUp]
        public void Setup()
        {
            _factory = new TestViewModelFactory();
            _viewModel = _factory.CreateClipboardHistoryViewModel();
        }

        [TearDown]
        public void Cleanup()
        {
            _viewModel?.Dispose();
            _factory?.Dispose();
        }

        #region HARNAIS 1 : Comportement de LoadHistoryAsync

        /// <summary>
        /// HARNAIS CRITIQUE : Valide que LoadHistoryAsync charge les éléments correctement.
        ///
        /// RÉFÉRENCE fonctions.md : "L'historique doit être chargé automatiquement au démarrage
        /// et afficher tous les éléments non supprimés dans l'ordre chronologique inverse."
        /// </summary>
        [Test]
        public async Task LoadHistoryAsync_WithValidData_ShouldLoadItemsInReverseChronologicalOrder()
        {
            // Arrange
            var testItems = CreateTestHistoryItems(5);
            _factory.SetupHistoryManagerWithItems(testItems);

            // Act
            await _viewModel.LoadHistoryAsync("SafetyHarness_LoadTest");

            // Assert - Comportement externe attendu
            Assert.That(_viewModel.HistoryItems.Count, Is.GreaterThan(0), "L'historique doit contenir des éléments après chargement");
            Assert.That(_viewModel.IsLoading, Is.False, "IsLoading doit être false après chargement réussi");

            // Vérifier l'ordre chronologique inverse (plus récent en premier)
            if (_viewModel.HistoryItems.Count > 1)
            {
                var firstItem = _viewModel.HistoryItems[0];
                var secondItem = _viewModel.HistoryItems[1];
                Assert.That(firstItem.Timestamp, Is.GreaterThanOrEqualTo(secondItem.Timestamp),
                    "Les éléments doivent être triés par date décroissante (plus récent en premier)");
            }
        }

        /// <summary>
        /// HARNAIS CRITIQUE : Valide que LoadHistoryAsync gère les erreurs sans crash.
        /// </summary>
        [Test]
        public async Task LoadHistoryAsync_WithError_ShouldNotCrashAndMaintainStableState()
        {
            // Arrange
            _factory.SetupHistoryManagerToThrowException();

            // Act & Assert - Ne doit pas lever d'exception
            try
            {
                await _viewModel.LoadHistoryAsync("SafetyHarness_ErrorTest");
                
                // L'état doit rester stable même en cas d'erreur
                Assert.That(_viewModel.IsLoading, Is.False, "IsLoading doit être false même après erreur");
                Assert.That(_viewModel.HistoryItems, Is.Not.Null, "HistoryItems ne doit jamais être null");
            }
            catch (Exception ex)
            {
                Assert.Fail($"LoadHistoryAsync ne doit pas lever d'exception. Exception: {ex.Message}");
            }
        }

        #endregion

        #region HARNAIS 2 : Comportement des Commandes Principales

        /// <summary>
        /// HARNAIS CRITIQUE : Valide que SupprimerToutCommand préserve les éléments épinglés.
        /// 
        /// RÉFÉRENCE fonctions.md : "La suppression en lot doit préserver les éléments épinglés
        /// et ne supprimer que les éléments non épinglés."
        /// </summary>
        [Test]
        public async Task SupprimerToutCommand_WithPinnedItems_ShouldPreservePinnedItems()
        {
            // Arrange
            var testItems = new List<ClipboardItem>
            {
                CreateTestItem(1, "Item Normal 1", isPinned: false),
                CreateTestItem(2, "Item Épinglé", isPinned: true),
                CreateTestItem(3, "Item Normal 2", isPinned: false)
            };
            _factory.SetupHistoryManagerWithItems(testItems);
            await _viewModel.LoadHistoryAsync("SafetyHarness_SupprimerTout");

            var initialPinnedCount = _viewModel.HistoryItems.Count(i => i.IsPinned);
            var initialTotalCount = _viewModel.HistoryItems.Count;

            // Act
            if (_viewModel.SupprimerToutCommand.CanExecute(null))
            {
                _viewModel.SupprimerToutCommand.Execute(null);
                
                // Attendre que l'opération asynchrone se termine
                await Task.Delay(100);
            }

            // Assert - Comportement fonctionnel critique
            var finalPinnedCount = _viewModel.HistoryItems.Count(i => i.IsPinned);
            Assert.That(finalPinnedCount, Is.EqualTo(initialPinnedCount),
                "Les éléments épinglés doivent être préservés lors de SupprimerTout");

            Assert.That(_viewModel.HistoryItems.Count < initialTotalCount || initialPinnedCount == initialTotalCount, Is.True,
                "Des éléments non épinglés doivent être supprimés, ou tous les éléments étaient épinglés");
        }

        /// <summary>
        /// HARNAIS CRITIQUE : Valide que PasteSelectedItemCommand fonctionne avec un élément sélectionné.
        /// </summary>
        [Test]
        public void PasteSelectedItemCommand_WithSelectedItem_ShouldBeExecutable()
        {
            // Arrange
            var testItem = CreateTestItem(1, "Test Content");
            _viewModel.SelectedClipboardItem = testItem;

            // Act & Assert
            Assert.That(_viewModel.PasteSelectedItemCommand.CanExecute(null), Is.True,
                "PasteSelectedItemCommand doit être exécutable quand un élément est sélectionné");
        }

        /// <summary>
        /// HARNAIS CRITIQUE : Valide que BasculerEpinglageCommand change l'état d'épinglage.
        /// </summary>
        [Test]
        public void BasculerEpinglageCommand_WithSelectedItem_ShouldTogglePinState()
        {
            // Arrange
            var testItem = CreateTestItem(1, "Test Content", isPinned: false);
            _viewModel.SelectedClipboardItem = testItem;
            var initialPinState = testItem.IsPinned;

            // Act
            if (_viewModel.BasculerEpinglageCommand.CanExecute(null))
            {
                _viewModel.BasculerEpinglageCommand.Execute(null);
            }

            // Assert
            Assert.That(testItem.IsPinned, Is.Not.EqualTo(initialPinState),
                "BasculerEpinglageCommand doit changer l'état d'épinglage de l'élément");
        }

        #endregion

        #region HARNAIS 3 : Comportement de Recherche et Filtrage

        /// <summary>
        /// HARNAIS CRITIQUE : Valide que la recherche filtre correctement les éléments.
        /// 
        /// RÉFÉRENCE fonctions.md : "La recherche doit filtrer les éléments en temps réel
        /// et afficher uniquement ceux qui correspondent au critère."
        /// </summary>
        [Test]
        public async Task SearchText_WithValidFilter_ShouldFilterItemsCorrectly()
        {
            // Arrange
            var testItems = new List<ClipboardItem>
            {
                CreateTestItem(1, "Document important"),
                CreateTestItem(2, "Image de vacances"),
                CreateTestItem(3, "Document confidentiel")
            };
            _factory.SetupHistoryManagerWithItems(testItems);
            await _viewModel.LoadHistoryAsync("SafetyHarness_Search");

            var initialCount = _viewModel.HistoryItems.Count;

            // Act
            _viewModel.SearchText = "Document";

            // Assert
            var filteredCount = _viewModel.HistoryItems.Count;
            Assert.That(filteredCount, Is.LessThanOrEqualTo(initialCount),
                "Le filtrage doit réduire ou maintenir le nombre d'éléments affichés");

            // Vérifier que tous les éléments visibles contiennent le terme recherché
            foreach (var item in _viewModel.HistoryItems)
            {
                Assert.That(item.TextPreview?.Contains("Document", StringComparison.OrdinalIgnoreCase) == true, Is.True,
                    $"L'élément '{item.TextPreview}' doit contenir le terme recherché 'Document'");
            }
        }

        #endregion

        #region HARNAIS 4 : Comportement de Création/Renommage

        /// <summary>
        /// HARNAIS CRITIQUE : Valide que la création d'un nouvel élément fonctionne.
        /// </summary>
        [Test]
        public void PrepareNewItemCommand_WhenExecuted_ShouldEnterCreationMode()
        {
            // Act
            if (_viewModel.PrepareNewItemCommand.CanExecute(null))
            {
                _viewModel.PrepareNewItemCommand.Execute(null);
            }

            // Assert
            Assert.That(_viewModel.IsItemCreationActive, Is.True,
                "IsItemCreationActive doit être true après PrepareNewItemCommand");
            Assert.That(_viewModel.NewItemTextContent, Is.Not.Null,
                "NewItemTextContent doit être initialisé après PrepareNewItemCommand");
        }

        #endregion

        #region Méthodes d'aide pour les tests

        private List<ClipboardItem> CreateTestHistoryItems(int count)
        {
            var items = new List<ClipboardItem>();
            var baseDate = DateTime.Now.AddHours(-count);

            for (int i = 0; i < count; i++)
            {
                items.Add(CreateTestItem(i + 1, $"Test Item {i + 1}", 
                    createdAt: baseDate.AddHours(i)));
            }

            return items;
        }

        private ClipboardItem CreateTestItem(long id, string content, bool isPinned = false, 
            DateTime? createdAt = null)
        {
            return new ClipboardItem
            {
                Id = id,
                TextPreview = content,
                RawData = System.Text.Encoding.UTF8.GetBytes(content ?? ""),
                IsPinned = isPinned,
                Timestamp = createdAt ?? DateTime.Now,
                DataType = ClipboardDataType.Text,
                CustomName = $"Title for {content}"
            };
        }

        #endregion
    }
}
