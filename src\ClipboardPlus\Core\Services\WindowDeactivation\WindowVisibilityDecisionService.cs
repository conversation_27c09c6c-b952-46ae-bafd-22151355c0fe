using System;
using System.Diagnostics;
using System.Windows;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Implémentation du service de prise de décision pour la visibilité des fenêtres.
    /// </summary>
    public class WindowVisibilityDecisionService : IWindowVisibilityDecisionService
    {
        private readonly ILoggingService _loggingService;
        private WindowVisibilityDecisionConfig _config;

        /// <summary>
        /// Initialise une nouvelle instance du service de décision de visibilité.
        /// </summary>
        /// <param name="loggingService">Service de journalisation</param>
        /// <param name="config">Configuration des règles de décision (optionnel)</param>
        public WindowVisibilityDecisionService(ILoggingService loggingService, WindowVisibilityDecisionConfig? config = null)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _config = config ?? new WindowVisibilityDecisionConfig();
        }

        /// <inheritdoc />
        public WindowVisibilityDecision ShouldHideWindow(
            WindowDiagnosticResult diagnostic,
            WindowClassificationResult classification,
            Window targetWindow)
        {
            var context = new WindowVisibilityDecisionContext
            {
                Diagnostic = diagnostic,
                Classification = classification,
                TargetWindow = targetWindow,
                TargetWindowName = targetWindow?.GetType().Name ?? "Unknown",
                IsTargetWindowActive = targetWindow?.IsActive ?? false,
                Config = _config
            };

            return EvaluateVisibilityDecision(context);
        }

        /// <inheritdoc />
        public WindowVisibilityDecision EvaluateVisibilityDecision(WindowVisibilityDecisionContext decisionContext)
        {
            if (decisionContext == null)
            {
                throw new ArgumentNullException(nameof(decisionContext));
            }

            var stopwatch = Stopwatch.StartNew();
            var decision = new WindowVisibilityDecision
            {
                Timestamp = DateTime.Now
            };

            try
            {
                // Utiliser la configuration du contexte si disponible, sinon celle du service
                var config = decisionContext.Config ?? _config;

                if (config.EnableDetailedLogging)
                {
                    _loggingService.LogInfo($"🎯 [WindowVisibilityDecisionService] Évaluation de la décision de visibilité pour '{decisionContext.TargetWindowName}'");
                }

                // Règle 1: Si la fenêtre cible est toujours active, pas de masquage
                if (config.ActiveWindowPreventsHiding && decisionContext.IsTargetWindowActive)
                {
                    decision.ShouldHide = false;
                    decision.DecisionType = WindowVisibilityDecisionType.IgnoredStillActive;
                    decision.Reason = "La fenêtre est toujours considérée comme active";
                    decision.RecommendedAction = WindowVisibilityAction.KeepVisible;
                    decision.ConfidenceLevel = 1.0;
                    decision.Details = $"La fenêtre '{decisionContext.TargetWindowName}' est toujours active, pas de masquage nécessaire";

                    if (config.EnableDetailedLogging)
                    {
                        _loggingService.LogInfo($"✅ [WindowVisibilityDecisionService] {decision.Reason}");
                    }

                    stopwatch.Stop();
                    decision.EvaluationDurationMs = stopwatch.Elapsed.TotalMilliseconds;
                    return decision;
                }

                // Règle 2: Si une fenêtre de l'application est active, pas de masquage
                if (config.ApplicationWindowsPreventsHiding &&
                    decisionContext.Classification != null &&
                    decisionContext.Classification.IsApplicationWindow)
                {
                    decision.ShouldHide = false;
                    decision.DecisionType = WindowVisibilityDecisionType.IgnoredApplicationWindow;
                    decision.Reason = "Fenêtre de l'application détectée";
                    decision.RecommendedAction = WindowVisibilityAction.KeepVisible;
                    decision.ConfidenceLevel = 0.95;
                    decision.Details = $"Fenêtre de l'application active: {decisionContext.Classification.WindowTypeName} ({decisionContext.Classification.RelationType})";

                    if (config.EnableDetailedLogging)
                    {
                        _loggingService.LogInfo($"✅ [WindowVisibilityDecisionService] {decision.Reason}: '{decisionContext.Classification.WindowTypeName}'");
                    }

                    stopwatch.Stop();
                    decision.EvaluationDurationMs = stopwatch.Elapsed.TotalMilliseconds;
                    return decision;
                }

                // Règle 3: Fenêtre externe active - masquage autorisé
                if (decisionContext.Classification != null && !decisionContext.Classification.IsApplicationWindow)
                {
                    decision.ShouldHide = true;
                    decision.DecisionType = WindowVisibilityDecisionType.HideExternalWindow;
                    decision.Reason = "Fenêtre externe active";
                    decision.RecommendedAction = WindowVisibilityAction.HideImmediately;
                    decision.ConfidenceLevel = 0.9;
                    decision.Details = $"Fenêtre externe détectée: {decisionContext.Classification.WindowTypeName}";

                    if (config.EnableDetailedLogging)
                    {
                        _loggingService.LogInfo($"🚨 [WindowVisibilityDecisionService] {decision.Reason}: '{decisionContext.Classification.WindowTypeName}' - MASQUAGE autorisé");
                    }

                    stopwatch.Stop();
                    decision.EvaluationDurationMs = stopwatch.Elapsed.TotalMilliseconds;
                    return decision;
                }

                // Règle 4: Si aucune fenêtre active n'est détectée
                if (decisionContext.Diagnostic?.ActiveWindow == null)
                {
                    decision.ShouldHide = true;
                    decision.DecisionType = WindowVisibilityDecisionType.HideNoActiveWindow;
                    decision.Reason = "Aucune fenêtre active détectée";
                    decision.RecommendedAction = WindowVisibilityAction.HideImmediately;
                    decision.ConfidenceLevel = 0.8;
                    decision.Details = "Aucune fenêtre active n'a été détectée dans le système";

                    if (config.EnableDetailedLogging)
                    {
                        _loggingService.LogInfo($"🚨 [WindowVisibilityDecisionService] {decision.Reason} - MASQUAGE autorisé");
                    }

                    stopwatch.Stop();
                    decision.EvaluationDurationMs = stopwatch.Elapsed.TotalMilliseconds;
                    return decision;
                }

                // Règle par défaut: pas de masquage
                decision.ShouldHide = false;
                decision.DecisionType = WindowVisibilityDecisionType.DefaultNoHide;
                decision.Reason = "Décision par défaut - pas de masquage";
                decision.RecommendedAction = WindowVisibilityAction.KeepVisible;
                decision.ConfidenceLevel = 0.5;
                decision.Details = "Aucune règle spécifique ne s'applique, conservation de l'état actuel";

                if (config.EnableDetailedLogging)
                {
                    _loggingService.LogInfo($"✅ [WindowVisibilityDecisionService] {decision.Reason}");
                }

                stopwatch.Stop();
                decision.EvaluationDurationMs = stopwatch.Elapsed.TotalMilliseconds;
                return decision;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                decision.EvaluationDurationMs = stopwatch.Elapsed.TotalMilliseconds;
                decision.ShouldHide = false; // En cas d'erreur, ne pas masquer par sécurité
                decision.DecisionType = WindowVisibilityDecisionType.DecisionError;
                decision.Reason = "Erreur lors de l'évaluation";
                decision.RecommendedAction = WindowVisibilityAction.KeepVisible;
                decision.ConfidenceLevel = 0.0;
                decision.Details = $"Erreur: {ex.Message}";

                _loggingService.LogError($"❌ [WindowVisibilityDecisionService] Erreur lors de l'évaluation de la décision: {ex.Message}", ex);
                return decision;
            }
        }

        /// <inheritdoc />
        public void ConfigureDecisionRules(WindowVisibilityDecisionConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _loggingService.LogInfo($"🔧 [WindowVisibilityDecisionService] Configuration des règles mise à jour");
        }
    }
}
