using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;
using System.Reflection;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    [TestFixture]
    public class UserThemeManagerTests
    {
        private UserThemeManager _themeManager = null!;

        [SetUp]
        public void Initialize()
        {
            // Créer une instance de UserThemeManager
            _themeManager = new UserThemeManager();
        }

        [Test]
        public void GetAvailableThemes_ReturnsListOfThemes()
        {
            // Act
            var themes = _themeManager.GetAvailableThemes();

            // Assert
            Assert.That(themes, Is.Not.Null);
            Assert.That(themes.Count(), Is.GreaterThan(0));
            // Vérifier que les thèmes standard sont présents
            Assert.That(themes.Any(t => t.Name == "Default"), Is.True);
            Assert.That(themes.Any(t => t.Name == "Light"), Is.True);
            Assert.That(themes.Any(t => t.Name == "Dark"), Is.True);
        }

        [Test]
        public void GetActiveTheme_ReturnsActiveTheme()
        {
            // Act
            var activeTheme = _themeManager.GetActiveTheme();

            // Assert
            Assert.That(activeTheme, Is.Not.Null);
            Assert.That(activeTheme.Name, Is.EqualTo("Default"));
        }

        [Test]
        public void ApplyThemeAsync_WithNullTheme_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () => await _themeManager.ApplyThemeAsync(null!));
        }

        [Test]
        public void ThemeInfo_Properties_AreCorrectlySet()
        {
            // Arrange
            string name = "Dark";
            string filePath = "pack://application:,,,/ClipboardPlus;component/UI/Themes/Dark.xaml";
            var themeInfo = new ThemeInfo(name, filePath);

            // Act & Assert
            Assert.That(themeInfo.Name, Is.EqualTo(name));
            Assert.That(themeInfo.FilePath, Is.EqualTo(filePath));
        }

        [Test]
        public void Constructor_SetsCorrectInitialState()
        {
            // Arrange & Act - Le constructeur est appelé dans Initialize()

            // Accéder aux champs privés via réflexion
            var type = typeof(UserThemeManager);
            var availableThemesField = type.GetField("_availableThemes", BindingFlags.NonPublic | BindingFlags.Instance);
            var activeThemeField = type.GetField("_activeTheme", BindingFlags.NonPublic | BindingFlags.Instance);
            var assemblyNameField = type.GetField("_assemblyName", BindingFlags.NonPublic | BindingFlags.Instance);

            Assert.That(availableThemesField, Is.Not.Null, "Le champ _availableThemes n'existe pas");
            Assert.That(activeThemeField, Is.Not.Null, "Le champ _activeTheme n'existe pas");
            Assert.That(assemblyNameField, Is.Not.Null, "Le champ _assemblyName n'existe pas");

            var availableThemes = availableThemesField?.GetValue(_themeManager) as List<ThemeInfo>;
            var activeTheme = activeThemeField?.GetValue(_themeManager) as ThemeInfo;
            var assemblyName = assemblyNameField?.GetValue(_themeManager) as string;

            // Assert
            Assert.That(availableThemes, Is.Not.Null, "_availableThemes ne devrait pas être null");
            Assert.That(activeTheme, Is.Not.Null, "_activeTheme ne devrait pas être null");
            Assert.That(assemblyName, Is.Not.Null, "_assemblyName ne devrait pas être null");
            Assert.That(availableThemes!.Count, Is.EqualTo(3), "Il devrait y avoir 3 thèmes disponibles par défaut");
            Assert.That(activeTheme!.Name, Is.EqualTo("Default"), "Le thème actif par défaut devrait être 'Default'");
            Assert.That(assemblyName!.Contains("ClipboardPlus"), Is.True, "Le nom de l'assembly devrait contenir 'ClipboardPlus'");
        }
        
        [Test]
        public void LoadActiveThemeAsync_WithValidThemePath_FindsCorrectTheme()
        {
            // Arrange
            const string themeFileName = "Dark.xaml";

            // Mock de ApplyThemeAsync via réflexion
            // Au lieu d'exécuter réellement ApplyThemeAsync, nous voulons vérifier que la méthode identifie le bon thème

            // Accéder au champ _availableThemes
            var availableThemesField = typeof(UserThemeManager).GetField("_availableThemes", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(availableThemesField, Is.Not.Null, "Le champ _availableThemes n'existe pas");

            var availableThemes = availableThemesField!.GetValue(_themeManager) as List<ThemeInfo>;
            Assert.That(availableThemes, Is.Not.Null, "_availableThemes ne devrait pas être null");

            // Vérifier que le thème "Dark" existe dans la liste
            var darkTheme = availableThemes!.FirstOrDefault(t => t.Name == "Dark");
            Assert.That(darkTheme, Is.Not.Null, "Le thème 'Dark' devrait exister dans la liste des thèmes disponibles");

            // Vérifier que le chemin du thème correspond à ce que nous attendons
            Assert.That(darkTheme!.FilePath.EndsWith(themeFileName), Is.True,
                $"Le chemin du thème Dark devrait se terminer par '{themeFileName}'");
        }

        [Test]
        public void LoadActiveThemeAsync_WithInvalidThemePath_UsesDefaultTheme()
        {
            // Arrange
            string invalidThemePath = "NonExistent.xaml";

            // Vérifier que le thème "NonExistent" n'existe pas dans la liste des thèmes disponibles
            var themes = _themeManager.GetAvailableThemes();
            Assert.That(themes.Any(t => t.FilePath.EndsWith(invalidThemePath)), Is.False,
                "Le thème 'NonExistent' ne devrait pas exister dans la liste des thèmes disponibles");

            // Vérifier que le thème "Default" existe dans la liste
            Assert.That(themes.Any(t => t.Name == "Default"), Is.True,
                "Le thème 'Default' devrait exister dans la liste des thèmes disponibles");
        }

        [Test]
        public void UserThemeManager_LoadAvailableThemes_LoadsCorrectThemes()
        {
            // Arrange - Accéder à la méthode privée LoadAvailableThemes via réflexion
            var method = typeof(UserThemeManager).GetMethod("LoadAvailableThemes", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(method, Is.Not.Null, "La méthode LoadAvailableThemes n'existe pas");

            // Accéder au champ _availableThemes via réflexion
            var availableThemesField = typeof(UserThemeManager).GetField("_availableThemes", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(availableThemesField, Is.Not.Null, "Le champ _availableThemes n'existe pas");

            // Réinitialiser la liste des thèmes disponibles pour vérifier qu'elle est correctement remplie
            availableThemesField!.SetValue(_themeManager, new List<ThemeInfo>());

            // Act
            method!.Invoke(_themeManager, null);

            // Assert
            var availableThemes = availableThemesField.GetValue(_themeManager) as List<ThemeInfo>;
            Assert.That(availableThemes, Is.Not.Null, "_availableThemes ne devrait pas être null après appel de LoadAvailableThemes");
            Assert.That(availableThemes!.Count, Is.EqualTo(3), "Il devrait y avoir 3 thèmes disponibles après chargement");

            // Vérifier que les 3 thèmes standard sont présents
            Assert.That(availableThemes.Any(t => t.Name == "Default"), Is.True, "Le thème 'Default' devrait être disponible");
            Assert.That(availableThemes.Any(t => t.Name == "Light"), Is.True, "Le thème 'Light' devrait être disponible");
            Assert.That(availableThemes.Any(t => t.Name == "Dark"), Is.True, "Le thème 'Dark' devrait être disponible");
        }
    }
} 