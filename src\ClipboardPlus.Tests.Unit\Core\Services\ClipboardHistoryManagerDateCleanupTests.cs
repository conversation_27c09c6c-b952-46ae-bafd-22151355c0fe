using NUnit.Framework;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests pour la fonctionnalité de nettoyage par date dans ClipboardHistoryManager.
    /// </summary>
    [TestFixture]
    public class ClipboardHistoryManagerDateCleanupTests
    {
        private Mock<IPersistenceService> _mockPersistenceService = null!;
        private Mock<IClipboardInteractionService> _mockClipboardService = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private ClipboardHistoryManager _historyManager = null!;
        private List<ClipboardItem> _testItems = null!;

        [SetUp]
        public void SetUp()
        {
            // Initialiser les mocks
            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockClipboardService = new Mock<IClipboardInteractionService>();
            _mockSettingsManager = new Mock<ISettingsManager>();

            // Configuration des mocks
            _mockSettingsManager.Setup(x => x.MaxHistoryItems).Returns(100);

            // Créer des éléments de test avec différentes dates (nouvelles instances à chaque test)
            _testItems = new List<ClipboardItem>
            {
                // Éléments anciens (plus de 2 jours)
                new ClipboardItem
                {
                    Id = 1,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Ancien élément 1",
                    Timestamp = DateTime.Now.AddDays(-3),
                    IsPinned = false,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Ancien élément 1")
                },
                new ClipboardItem
                {
                    Id = 2,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Ancien élément 2",
                    Timestamp = DateTime.Now.AddDays(-5),
                    IsPinned = false,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Ancien élément 2")
                },
                // Élément ancien mais épinglé (ne doit pas être supprimé)
                new ClipboardItem
                {
                    Id = 3,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Ancien élément épinglé",
                    Timestamp = DateTime.Now.AddDays(-4),
                    IsPinned = true,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Ancien élément épinglé")
                },
                // Éléments récents (moins de 2 jours)
                new ClipboardItem
                {
                    Id = 4,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Élément récent 1",
                    Timestamp = DateTime.Now.AddHours(-12),
                    IsPinned = false,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Élément récent 1")
                },
                new ClipboardItem
                {
                    Id = 5,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Élément récent 2",
                    Timestamp = DateTime.Now.AddMinutes(-30),
                    IsPinned = false,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Élément récent 2")
                }
            };

            // Configuration du mock de persistance
            _mockPersistenceService.Setup(x => x.GetAllClipboardItemsAsync())
                .ReturnsAsync(() => _testItems.ToList()); // Retourner une nouvelle liste à chaque appel

            _mockPersistenceService.Setup(x => x.DeleteClipboardItemAsync(It.IsAny<long>()))
                .ReturnsAsync(true);

            // Créer le manager avec les mocks
            _historyManager = new ClipboardHistoryManager(
                _mockPersistenceService.Object,
                _mockSettingsManager.Object,
                _mockClipboardService.Object
            );

            // Vider la liste et ajouter les éléments de test (réinitialisation complète)
            _historyManager.HistoryItems.Clear();
            _historyManager.HistoryItems.AddRange(_testItems.Select(item => new ClipboardItem
            {
                Id = item.Id,
                DataType = item.DataType,
                TextPreview = item.TextPreview,
                Timestamp = item.Timestamp,
                IsPinned = item.IsPinned,
                RawData = item.RawData
            }));
        }

        [Test]
        public async Task ClearItemsOlderThanAsync_ShouldRemoveOldNonPinnedItems()
        {
            // Arrange
            var initialCount = _historyManager.HistoryItems.Count;
            var olderThan = TimeSpan.FromDays(2);

            // Act
            var removedCount = await _historyManager.ClearItemsOlderThanAsync(olderThan);

            // Assert
            Assert.AreEqual(2, removedCount, "Devrait supprimer exactement 2 éléments anciens non épinglés");
            Assert.AreEqual(initialCount - 2, _historyManager.HistoryItems.Count, "Le nombre d'éléments restants devrait être correct");

            // Vérifier que les bons éléments ont été supprimés
            var remainingIds = _historyManager.HistoryItems.Select(i => i.Id).ToList();
            Assert.IsFalse(remainingIds.Contains(1), "L'élément ID=1 (ancien) devrait être supprimé");
            Assert.IsFalse(remainingIds.Contains(2), "L'élément ID=2 (ancien) devrait être supprimé");
            Assert.IsTrue(remainingIds.Contains(3), "L'élément ID=3 (ancien mais épinglé) devrait être préservé");
            Assert.IsTrue(remainingIds.Contains(4), "L'élément ID=4 (récent) devrait être préservé");
            Assert.IsTrue(remainingIds.Contains(5), "L'élément ID=5 (récent) devrait être préservé");
        }

        [Test]
        public async Task ClearItemsOlderThanAsync_ShouldPreservePinnedItems()
        {
            // Arrange
            var olderThan = TimeSpan.FromDays(1); // Très restrictif pour tester la préservation des épinglés

            // Act
            var removedCount = await _historyManager.ClearItemsOlderThanAsync(olderThan);

            // Assert
            var pinnedItem = _historyManager.HistoryItems.FirstOrDefault(i => i.Id == 3);
            Assert.IsNotNull(pinnedItem, "L'élément épinglé devrait être préservé");
            Assert.IsTrue(pinnedItem!.IsPinned, "L'élément préservé devrait être épinglé");
        }

        [Test]
        public async Task ClearItemsOlderThanAsync_ShouldPreserveRecentItems()
        {
            // Arrange
            var olderThan = TimeSpan.FromDays(2);

            // Act
            var removedCount = await _historyManager.ClearItemsOlderThanAsync(olderThan);

            // Assert
            var recentItems = _historyManager.HistoryItems.Where(i => i.Id == 4 || i.Id == 5).ToList();
            Assert.AreEqual(2, recentItems.Count, "Les éléments récents devraient être préservés");
            Assert.IsTrue(recentItems.All(i => i.Timestamp > DateTime.Now - olderThan), "Tous les éléments préservés devraient être récents");
        }

        [Test]
        public async Task ClearItemsOlderThanAsync_WithNoOldItems_ShouldReturnZero()
        {
            // Arrange
            var olderThan = TimeSpan.FromDays(10); // Très large, aucun élément ne devrait être supprimé

            // Act
            var removedCount = await _historyManager.ClearItemsOlderThanAsync(olderThan);

            // Assert
            Assert.AreEqual(0, removedCount, "Aucun élément ne devrait être supprimé");
            Assert.AreEqual(_testItems.Count, _historyManager.HistoryItems.Count, "Tous les éléments devraient être préservés");
        }

        [Test]
        public async Task ClearItemsOlderThanAsync_WithDatabaseError_ShouldHandleGracefully()
        {
            // Arrange
            _mockPersistenceService.Setup(x => x.DeleteClipboardItemAsync(1))
                .ThrowsAsync(new Exception("Erreur de base de données"));
            _mockPersistenceService.Setup(x => x.DeleteClipboardItemAsync(2))
                .ReturnsAsync(true);

            var olderThan = TimeSpan.FromDays(2);

            // Act
            var removedCount = await _historyManager.ClearItemsOlderThanAsync(olderThan);

            // Assert
            Assert.AreEqual(1, removedCount, "Devrait supprimer seulement l'élément sans erreur");
        }

        [Test]
        public async Task ClearItemsOlderThanAsync_ShouldCallDeleteForCorrectItems()
        {
            // Arrange
            var olderThan = TimeSpan.FromDays(2);

            // Act
            await _historyManager.ClearItemsOlderThanAsync(olderThan);

            // Assert
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(1), Times.Once, "Devrait supprimer l'élément ID=1");
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(2), Times.Once, "Devrait supprimer l'élément ID=2");
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(3), Times.Never, "Ne devrait pas supprimer l'élément épinglé ID=3");
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(4), Times.Never, "Ne devrait pas supprimer l'élément récent ID=4");
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(5), Times.Never, "Ne devrait pas supprimer l'élément récent ID=5");
        }
    }
}
