<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <!-- Configuration STA supprimée - tests STA interdits -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="NUnit" Version="3.14.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="Prism.Core" Version="9.0.537" />
    <PackageReference Include="System.Reactive" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="coverlet.collector" Version="6.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ClipboardPlus\ClipboardPlus.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Core\DataModels\" />
    <Folder Include="Core\Services\" />
    <Folder Include="TestData\" />
    <Folder Include="UI\ViewModels\" />
  </ItemGroup>

</Project>