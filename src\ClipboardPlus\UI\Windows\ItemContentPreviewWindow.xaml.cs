using System;
using System.Windows;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;
using System.Diagnostics;
using ClipboardPlus.UI.Helpers;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Extensions;
using WpfApplication = System.Windows.Application;

namespace ClipboardPlus.UI.Windows
{
    /// <summary>
    /// Logique d'interaction pour ItemContentPreviewWindow.xaml
    /// </summary>
    public partial class ItemContentPreviewWindow : Window
    {
        private readonly ContentPreviewViewModel _viewModel;
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Constructeur.
        /// </summary>
        public ItemContentPreviewWindow()
        {
            // Récupérer le service de logging
            _loggingService = GetLoggingService();
            
            _loggingService?.LogInfo($"[DÉBUT] ItemContentPreviewWindow.Constructeur (sans paramètre) - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            
            try
            {
                InitializeComponent();
                
                _loggingService?.LogInfo("ItemContentPreviewWindow - InitializeComponent terminé avec succès");
                
                _viewModel = new ContentPreviewViewModel();
                DataContext = _viewModel;
                
                _loggingService?.LogInfo("ItemContentPreviewWindow - ViewModel créé et assigné comme DataContext");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ItemContentPreviewWindow.Constructeur - EXCEPTION: {ex.Message}", ex);
                Debug.WriteLine($"ItemContentPreviewWindow.Constructeur - StackTrace: {ex.StackTrace}");
                
                // Utiliser ErrorMessageHelper pour afficher l'erreur
                ErrorMessageHelper.ShowError(
                    "Erreur lors de l'initialisation de la fenêtre de prévisualisation.",
                    "ClipboardPlus - Erreur",
                    ex,
                    "ItemContentPreviewWindow.Constructeur");
                
                // Rethrow pour que l'appelant puisse gérer l'erreur
                throw;
            }
            
            _loggingService?.LogInfo($"[FIN] ItemContentPreviewWindow.Constructeur (sans paramètre) - Heure: {DateTime.Now:HH:mm:ss.fff}");
        }

        /// <summary>
        /// Constructeur avec élément à prévisualiser.
        /// </summary>
        /// <param name="itemToPreview">Élément à prévisualiser.</param>
        public ItemContentPreviewWindow(ClipboardItem itemToPreview) : this()
        {
            _loggingService?.LogInfo($"[DÉBUT] ItemContentPreviewWindow.Constructeur (avec item) - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            
            try
            {
                if (itemToPreview == null)
                {
                    _loggingService?.LogError("ItemContentPreviewWindow - ERREUR: itemToPreview est null");
                    
                    // Utiliser ErrorMessageHelper pour afficher l'erreur
                    ErrorMessageHelper.ShowError(
                        "Impossible de prévisualiser : aucun élément spécifié.",
                        "ClipboardPlus - Erreur de prévisualisation",
                        new ArgumentNullException(nameof(itemToPreview)),
                        "ItemContentPreviewWindow.Constructeur");
                    
                    throw new ArgumentNullException(nameof(itemToPreview));
                }
                
                _loggingService?.LogInfo($"ItemContentPreviewWindow - Chargement du contenu de l'élément ID: {itemToPreview.Id}, Type: {itemToPreview.DataType}");
                
                _viewModel.LoadItemContent(itemToPreview);
                
                _loggingService?.LogInfo("ItemContentPreviewWindow - LoadItemContent terminé avec succès");
                
                // Mettre à jour le titre de la fenêtre
                if (!string.IsNullOrEmpty(itemToPreview.CustomName))
                {
                    Title = $"Prévisualisation - {itemToPreview.CustomName}";
                    _loggingService?.LogInfo($"ItemContentPreviewWindow - Titre défini avec le nom personnalisé: '{itemToPreview.CustomName}'");
                }
                else
                {
                    Title = $"Prévisualisation - {itemToPreview.DataType}";
                    _loggingService?.LogInfo($"ItemContentPreviewWindow - Titre défini avec le type: {itemToPreview.DataType}");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ItemContentPreviewWindow.Constructeur (avec item) - EXCEPTION: {ex.Message}", ex);
                Debug.WriteLine($"ItemContentPreviewWindow.Constructeur (avec item) - StackTrace: {ex.StackTrace}");
                
                // Utiliser ErrorMessageHelper pour afficher l'erreur
                ErrorMessageHelper.ShowError(
                    "Erreur lors du chargement de l'élément à prévisualiser.",
                    "ClipboardPlus - Erreur de prévisualisation",
                    ex,
                    "ItemContentPreviewWindow.Constructeur");
                
                // Rethrow pour que l'appelant puisse gérer l'erreur
                throw;
            }
            
            _loggingService?.LogInfo($"[FIN] ItemContentPreviewWindow.Constructeur (avec item) - Heure: {DateTime.Now:HH:mm:ss.fff}");
        }

        /// <summary>
        /// Gère l'événement de clic sur le bouton Fermer.
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            _loggingService?.LogInfo($"[DÉBUT] CloseButton_Click - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            
            try
            {
                _loggingService?.LogInfo("CloseButton_Click - Fermeture de la fenêtre");
                Close();
                _loggingService?.LogInfo("CloseButton_Click - Fenêtre fermée avec succès");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"CloseButton_Click - EXCEPTION: {ex.Message}", ex);
                Debug.WriteLine($"CloseButton_Click - StackTrace: {ex.StackTrace}");
                
                // Utiliser ErrorMessageHelper pour afficher l'erreur
                ErrorMessageHelper.ShowError(
                    "Erreur lors de la fermeture de la fenêtre de prévisualisation.",
                    "ClipboardPlus - Erreur",
                    ex,
                    "CloseButton_Click");
                
                // Tenter de fermer la fenêtre d'une autre manière
                try
                {
                    _loggingService?.LogInfo("CloseButton_Click - Tentative alternative de fermeture");
                    this.DialogResult = false;
                }
                catch (Exception altEx)
                {
                    _loggingService?.LogError($"CloseButton_Click - Échec de la tentative alternative: {altEx.Message}", altEx);
                }
            }
            
            _loggingService?.LogInfo($"[FIN] CloseButton_Click - Heure: {DateTime.Now:HH:mm:ss.fff}");
        }
        
        /// <summary>
        /// Obtient le service de journalisation de l'application de manière sécurisée
        /// </summary>
        private ILoggingService? GetLoggingService()
        {
            try
            {
                return WpfApplication.Current?.Services()?.GetService(typeof(ILoggingService)) as ILoggingService;
            }
            catch (Exception)
            {
                // Ne pas journaliser ici pour éviter une récursion infinie
                return null;
            }
        }
        
        /// <summary>
        /// Gère l'événement de chargement de la fenêtre.
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            _loggingService?.LogInfo($"[DÉBUT] Window_Loaded - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            
            try
            {
                // Vérifier que le DataContext est correctement défini
                if (DataContext == null)
                {
                    _loggingService?.LogWarning("Window_Loaded - DataContext est null, tentative de réinitialisation");
                    
                    if (_viewModel != null)
                    {
                        DataContext = _viewModel;
                        _loggingService?.LogInfo("Window_Loaded - DataContext réinitialisé avec succès");
                    }
                    else
                    {
                        _loggingService?.LogError("Window_Loaded - Impossible de réinitialiser le DataContext car _viewModel est null");
                        
                        // Utiliser ErrorMessageHelper pour afficher l'erreur
                        ErrorMessageHelper.ShowError(
                            "Erreur lors du chargement de la prévisualisation : ViewModel non disponible.",
                            "ClipboardPlus - Erreur de prévisualisation",
                            null,
                            "Window_Loaded");
                    }
                }
                
                // Vérifier que la fenêtre est bien positionnée à l'écran
                if (Left < 0 || Top < 0 || Left > SystemParameters.VirtualScreenWidth || Top > SystemParameters.VirtualScreenHeight)
                {
                    _loggingService?.LogWarning($"Window_Loaded - Fenêtre mal positionnée (Left={Left}, Top={Top}), recentrage");
                    
                    WindowStartupLocation = WindowStartupLocation.CenterScreen;
                }
                
                _loggingService?.LogInfo($"Window_Loaded - Fenêtre chargée avec succès, dimensions: {Width}x{Height}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Window_Loaded - EXCEPTION: {ex.Message}", ex);
                Debug.WriteLine($"Window_Loaded - StackTrace: {ex.StackTrace}");
                
                // Utiliser ErrorMessageHelper pour afficher l'erreur
                ErrorMessageHelper.ShowError(
                    "Erreur lors du chargement de la fenêtre de prévisualisation.",
                    "ClipboardPlus - Erreur de prévisualisation",
                    ex,
                    "Window_Loaded");
            }
            
            _loggingService?.LogInfo($"[FIN] Window_Loaded - Heure: {DateTime.Now:HH:mm:ss.fff}");
        }
        
        /// <summary>
        /// Gère l'événement de fermeture de la fenêtre.
        /// </summary>
        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            _loggingService?.LogInfo($"[DÉBUT] Window_Closing - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");
            
            try
            {
                _loggingService?.LogInfo("Window_Closing - Nettoyage des ressources avant fermeture");
                
                // Nettoyer les ressources si nécessaire
                // (par exemple, libérer les images, fermer les flux, etc.)
                
                _loggingService?.LogInfo("Window_Closing - Nettoyage terminé avec succès");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Window_Closing - EXCEPTION: {ex.Message}", ex);
                Debug.WriteLine($"Window_Closing - StackTrace: {ex.StackTrace}");
                
                // Utiliser ErrorMessageHelper pour afficher l'erreur
                ErrorMessageHelper.ShowError(
                    "Erreur lors du nettoyage des ressources de prévisualisation.",
                    "ClipboardPlus - Avertissement",
                    ex,
                    "Window_Closing");
                
                // Ne pas annuler la fermeture même en cas d'erreur
            }
            
            _loggingService?.LogInfo($"[FIN] Window_Closing - Heure: {DateTime.Now:HH:mm:ss.fff}");
        }
    }
} 
