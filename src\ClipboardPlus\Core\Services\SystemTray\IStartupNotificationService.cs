using System;
using System.Windows.Forms;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Interface pour l'affichage des notifications de démarrage.
    /// Responsabilité unique : gérer l'affichage des notifications lors de l'initialisation.
    /// </summary>
    public interface IStartupNotificationService
    {
        /// <summary>
        /// Affiche la notification de démarrage standard.
        /// </summary>
        /// <param name="notifyIcon">L'instance NotifyIcon à utiliser pour afficher la notification.</param>
        void ShowStartupNotification(NotifyIcon notifyIcon);

        /// <summary>
        /// Affiche une notification personnalisée.
        /// </summary>
        /// <param name="notifyIcon">L'instance NotifyIcon à utiliser.</param>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="iconType">Le type d'icône à afficher.</param>
        void ShowCustomNotification(NotifyIcon notifyIcon, string title, string message, ToolTipIcon iconType);

        /// <summary>
        /// Vérifie si les notifications sont supportées sur le système actuel.
        /// </summary>
        /// <returns>True si les notifications sont supportées, false sinon.</returns>
        bool AreNotificationsSupported();
    }
}
