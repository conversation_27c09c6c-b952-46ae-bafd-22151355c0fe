using System;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour un service de gestion des notifications utilisateur.
    /// </summary>
    public interface INotificationService
    {
        /// <summary>
        /// Affiche une notification d'information.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="onClick">Action à exécuter lorsque l'utilisateur clique sur la notification.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        Task ShowInformationAsync(string title, string message, Action? onClick = null);

        /// <summary>
        /// Affiche une notification de succès.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="onClick">Action à exécuter lorsque l'utilisateur clique sur la notification.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        Task ShowSuccessAsync(string title, string message, Action? onClick = null);

        /// <summary>
        /// Affiche une notification d'avertissement.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="onClick">Action à exécuter lorsque l'utilisateur clique sur la notification.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        Task ShowWarningAsync(string title, string message, Action? onClick = null);

        /// <summary>
        /// Affiche une notification d'erreur.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="onClick">Action à exécuter lorsque l'utilisateur clique sur la notification.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        Task ShowErrorAsync(string title, string message, Action? onClick = null);

        /// <summary>
        /// Affiche une notification personnalisée.
        /// </summary>
        /// <param name="title">Le titre de la notification.</param>
        /// <param name="message">Le message de la notification.</param>
        /// <param name="iconType">Le type d'icône à afficher.</param>
        /// <param name="onClick">Action à exécuter lorsque l'utilisateur clique sur la notification.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        Task ShowCustomAsync(string title, string message, NotificationIconType iconType, Action? onClick = null);

        /// <summary>
        /// Masque toutes les notifications actives.
        /// </summary>
        void HideAllNotifications();
    }

    /// <summary>
    /// Types d'icônes pour les notifications.
    /// </summary>
    public enum NotificationIconType
    {
        /// <summary>
        /// Icône d'information.
        /// </summary>
        Information,

        /// <summary>
        /// Icône de succès.
        /// </summary>
        Success,

        /// <summary>
        /// Icône d'avertissement.
        /// </summary>
        Warning,

        /// <summary>
        /// Icône d'erreur.
        /// </summary>
        Error,

        /// <summary>
        /// Icône de question.
        /// </summary>
        Question,

        /// <summary>
        /// Aucune icône.
        /// </summary>
        None
    }
}