using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Implementations
{
    /// <summary>
    /// Orchestrateur principal des opérations sur les éléments du presse-papiers.
    /// Responsabilité unique : Coordonner les différents services selon le pattern SOLID.
    /// </summary>
    public class ClipboardItemOrchestrator : IClipboardItemOrchestrator
    {
        private readonly IClipboardItemValidator _validator;
        private readonly IDuplicateDetector _duplicateDetector;
        private readonly IClipboardItemProcessor _processor;
        private readonly IHistoryManager _historyManager;
        private readonly IEventNotifier _eventNotifier;
        private readonly IOperationLogger _operationLogger;
        private readonly ISettingsManager _settingsManager;

        /// <summary>
        /// Initialise une nouvelle instance de l'orchestrateur.
        /// </summary>
        /// <param name="validator">Service de validation des éléments</param>
        /// <param name="duplicateDetector">Service de détection des doublons</param>
        /// <param name="processor">Service de traitement des éléments</param>
        /// <param name="historyManager">Service de gestion de l'historique</param>
        /// <param name="eventNotifier">Service de notification d'événements</param>
        /// <param name="operationLogger">Service de journalisation des opérations</param>
        /// <param name="settingsManager">Gestionnaire des paramètres</param>
        public ClipboardItemOrchestrator(
            IClipboardItemValidator validator,
            IDuplicateDetector duplicateDetector,
            IClipboardItemProcessor processor,
            IHistoryManager historyManager,
            IEventNotifier eventNotifier,
            IOperationLogger operationLogger,
            ISettingsManager settingsManager)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _duplicateDetector = duplicateDetector ?? throw new ArgumentNullException(nameof(duplicateDetector));
            _processor = processor ?? throw new ArgumentNullException(nameof(processor));
            _historyManager = historyManager ?? throw new ArgumentNullException(nameof(historyManager));
            _eventNotifier = eventNotifier ?? throw new ArgumentNullException(nameof(eventNotifier));
            _operationLogger = operationLogger ?? throw new ArgumentNullException(nameof(operationLogger));
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
        }

        /// <summary>
        /// Ajoute un élément au presse-papiers en orchestrant toutes les étapes nécessaires.
        /// </summary>
        /// <param name="item">L'élément à ajouter</param>
        /// <returns>L'ID de l'élément ajouté ou mis à jour</returns>
        /// <exception cref="ArgumentNullException">Si l'élément est null</exception>
        /// <exception cref="ArgumentException">Si l'élément ne respecte pas les règles de validation</exception>
        public async Task<long> AddItemAsync(ClipboardItem item)
        {
            // Démarrer l'opération avec logging
            using var operationContext = _operationLogger.StartOperation("AddItemAsync", item);
            
            try
            {
                operationContext.LogInfo($"Début de l'ajout - Type: {item?.DataType}, Nom: '{item?.CustomName ?? "N/A"}', IsPinned: {item?.IsPinned}");

                // Étape 1 : Validation de l'élément
                operationContext.LogInfo("Étape 1: Validation de l'élément");
                var validationResult = await _validator.ValidateAsync(item, _settingsManager.MaxStorableItemSizeBytes);
                if (!validationResult.IsValid)
                {
                    var errorMessage = $"Validation échouée: {validationResult.ErrorMessage}";
                    operationContext.LogError(errorMessage);
                    operationContext.Fail(errorMessage);
                    throw new ArgumentException(validationResult.ErrorMessage, nameof(item));
                }
                operationContext.LogInfo("Validation réussie");

                // Étape 2 : Détection des doublons
                operationContext.LogInfo("Étape 2: Détection des doublons");
                var historyItems = _historyManager.GetHistoryItems();
                var existingDuplicate = await _duplicateDetector.FindDuplicateAsync(item, historyItems);
                
                if (existingDuplicate != null)
                {
                    operationContext.LogInfo($"Doublon trouvé (ID: {existingDuplicate.Id}). Mise à jour du timestamp et déplacement en première position.");

                    try
                    {
                        var updateResult = await _processor.ProcessExistingItemAsync(existingDuplicate, item.Timestamp);
                        if (updateResult > 0)
                        {
                            operationContext.LogInfo($"Timestamp de l'élément existant ID={existingDuplicate.Id} mis à jour avec succès");

                            // CORRECTION : Déplacer l'élément en première position (comportement attendu pour les duplicatas)
                            _historyManager.UpdateExistingItem(existingDuplicate);
                            operationContext.LogInfo($"Élément ID={existingDuplicate.Id} déplacé en première position");

                            // Notifier du changement
                            _eventNotifier.NotifyHistoryChanged(existingDuplicate, HistoryChangeType.ItemUpdated);

                            operationContext.Complete(existingDuplicate.Id);
                            return existingDuplicate.Id;
                        }
                        else
                        {
                            operationContext.LogWarning($"Échec de la mise à jour du doublon ID={existingDuplicate.Id}");
                            operationContext.Complete(0);
                            return 0;
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorMessage = $"Erreur lors de la mise à jour du doublon ID={existingDuplicate.Id}: {ex.Message}";
                        operationContext.LogError(errorMessage, ex);
                        operationContext.Fail(errorMessage);
                        return 0;
                    }
                }

                operationContext.LogInfo("Aucun doublon trouvé. Procédure d'ajout normale.");

                // Étape 3 : Insertion du nouvel élément
                operationContext.LogInfo("Étape 3: Insertion du nouvel élément");
                var insertResult = await _processor.ProcessNewItemAsync(item);
                
                if (insertResult <= 0)
                {
                    var errorMessage = $"Échec de l'insertion en base de données (ID retourné={insertResult})";
                    operationContext.LogError(errorMessage);
                    operationContext.Fail(errorMessage);
                    return 0;
                }

                // Mettre à jour l'ID de l'élément
                item.Id = insertResult;
                operationContext.LogInfo($"Nouvel élément inséré avec succès. ID: {insertResult}");

                // Étape 4 : Ajout à l'historique en mémoire
                operationContext.LogInfo("Étape 4: Ajout à l'historique en mémoire");
                _historyManager.AddToHistory(item);
                operationContext.LogInfo($"Élément ID={insertResult} ajouté à l'historique en mémoire");

                // Étape 5 : Application de la limite d'historique
                operationContext.LogInfo("Étape 5: Application de la limite d'historique");
                var removedCount = await _historyManager.EnforceMaxHistoryItemsAsync(_settingsManager.MaxHistoryItems);
                if (removedCount > 0)
                {
                    operationContext.LogInfo($"Limite d'historique appliquée. {removedCount} élément(s) supprimé(s)");
                    _eventNotifier.NotifyHistoryChanged(item, HistoryChangeType.ItemsRemoved);
                }

                // Étape 6 : Notification des changements
                operationContext.LogInfo("Étape 6: Notification des changements");
                _eventNotifier.NotifyHistoryChanged(item, HistoryChangeType.ItemAdded);
                operationContext.LogInfo("Notification des changements envoyée");

                operationContext.Complete(insertResult);
                return insertResult;
            }
            catch (ArgumentNullException)
            {
                // Les exceptions de validation sont déjà loggées et doivent être propagées
                throw;
            }
            catch (ArgumentException)
            {
                // Les exceptions de validation sont déjà loggées et doivent être propagées
                throw;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Erreur inattendue lors de l'ajout de l'élément: {ex.Message}";
                operationContext.LogError(errorMessage, ex);
                operationContext.Fail(errorMessage);
                return 0;
            }
        }
    }
}
