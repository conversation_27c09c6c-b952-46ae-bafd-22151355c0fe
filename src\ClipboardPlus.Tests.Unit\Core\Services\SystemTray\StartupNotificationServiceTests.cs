using System;
using System.Threading;
using System.Windows.Forms;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;

namespace ClipboardPlus.Tests.Unit.Core.Services.SystemTray
{
    /// <summary>
    /// Tests unitaires pour StartupNotificationService.
    /// Vérifie la responsabilité unique : gestion de l'affichage des notifications lors de l'initialisation.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class StartupNotificationServiceTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private StartupNotificationService _notificationService;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _notificationService = new StartupNotificationService(_mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new StartupNotificationService(null));
        }

        [Test]
        public void ShowStartupNotification_WithValidNotifyIcon_ShowsNotification()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act
            _notificationService.ShowStartupNotification(notifyIcon);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo("StartupNotificationService: Affichage de la notification de démarrage..."),
                Times.Once,
                "Should log startup notification start");

            _mockLoggingService.Verify(
                x => x.LogInfo("StartupNotificationService: Notification de démarrage affichée avec succès"),
                Times.Once,
                "Should log successful startup notification");

            // Vérifier que ShowCustomNotification a été appelée avec les bons paramètres
            _mockLoggingService.Verify(
                x => x.LogInfo("StartupNotificationService: Tentative d'affichage d'une notification: 'ClipboardPlus - Démarrage'"),
                Times.Once,
                "Should call ShowCustomNotification with startup title");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ShowStartupNotification_WithNullNotifyIcon_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _notificationService.ShowStartupNotification(null));
            Assert.That(ex.ParamName, Is.EqualTo("notifyIcon"));
        }

        [Test]
        public void ShowCustomNotification_WithValidParameters_ShowsNotification()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();
            const string title = "Test Title";
            const string message = "Test Message";
            const ToolTipIcon iconType = ToolTipIcon.Info;

            // Act
            _notificationService.ShowCustomNotification(notifyIcon, title, message, iconType);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo($"StartupNotificationService: Tentative d'affichage d'une notification: '{title}'"),
                Times.Once,
                "Should log notification attempt");

            _mockLoggingService.Verify(
                x => x.LogInfo($"StartupNotificationService: Notification '{title}' affichée avec succès"),
                Times.Once,
                "Should log successful notification");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ShowCustomNotification_WithNullNotifyIcon_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => 
                _notificationService.ShowCustomNotification(null, "Title", "Message", ToolTipIcon.Info));
            Assert.That(ex.ParamName, Is.EqualTo("notifyIcon"));
        }

        [Test]
        public void ShowCustomNotification_WithNullTitle_ThrowsArgumentException()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => 
                _notificationService.ShowCustomNotification(notifyIcon, null, "Message", ToolTipIcon.Info));
            Assert.That(ex.ParamName, Is.EqualTo("title"));

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ShowCustomNotification_WithEmptyTitle_ThrowsArgumentException()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => 
                _notificationService.ShowCustomNotification(notifyIcon, "", "Message", ToolTipIcon.Info));
            Assert.That(ex.ParamName, Is.EqualTo("title"));

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ShowCustomNotification_WithWhitespaceTitle_ThrowsArgumentException()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => 
                _notificationService.ShowCustomNotification(notifyIcon, "   ", "Message", ToolTipIcon.Info));
            Assert.That(ex.ParamName, Is.EqualTo("title"));

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ShowCustomNotification_WithNullMessage_ThrowsArgumentException()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => 
                _notificationService.ShowCustomNotification(notifyIcon, "Title", null, ToolTipIcon.Info));
            Assert.That(ex.ParamName, Is.EqualTo("message"));

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ShowCustomNotification_WithEmptyMessage_ThrowsArgumentException()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => 
                _notificationService.ShowCustomNotification(notifyIcon, "Title", "", ToolTipIcon.Info));
            Assert.That(ex.ParamName, Is.EqualTo("message"));

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ShowCustomNotification_WithWhitespaceMessage_ThrowsArgumentException()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => 
                _notificationService.ShowCustomNotification(notifyIcon, "Title", "   ", ToolTipIcon.Info));
            Assert.That(ex.ParamName, Is.EqualTo("message"));

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void AreNotificationsSupported_OnWindows_ReturnsTrue()
        {
            // Act
            bool result = _notificationService.AreNotificationsSupported();

            // Assert
            // Sur Windows, les notifications devraient être supportées
            Assert.That(result, Is.True, "Notifications should be supported on Windows");

            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Vérification du support des notifications") && s.Contains("True"))),
                Times.Once,
                "Should log notification support check");
        }

        [Test]
        public void ShowCustomNotification_WhenNotificationsNotSupported_LogsWarningAndReturns()
        {
            // Arrange - Créer un mock qui retourne false pour AreNotificationsSupported
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo(It.Is<string>(s => s.Contains("Vérification du support des notifications"))))
                             .Callback(() => { }); // Ne pas interférer avec le test

            // Simuler un environnement où les notifications ne sont pas supportées
            // (difficile à faire directement, donc on teste le comportement de logging)
            var notifyIcon = new NotifyIcon();

            // Act
            _notificationService.ShowCustomNotification(notifyIcon, "Title", "Message", ToolTipIcon.Info);

            // Assert - Vérifier que la vérification du support a été appelée
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Vérification du support des notifications"))),
                Times.Once,
                "Should check notification support");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ShowCustomNotification_WithException_LogsErrorButDoesNotThrow()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo(It.IsAny<string>()))
                             .Throws(new InvalidOperationException("Test exception"));

            var service = new StartupNotificationService(mockLoggingService.Object);

            // Act & Assert
            Assert.DoesNotThrow(() => service.ShowCustomNotification(notifyIcon, "Title", "Message", ToolTipIcon.Info),
                "ShowCustomNotification should not throw exception even if logging fails");

            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de l'affichage de la notification")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when notification fails");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ShowStartupNotification_WithException_LogsErrorButDoesNotThrow()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo("StartupNotificationService: Affichage de la notification de démarrage..."))
                             .Throws(new InvalidOperationException("Test exception"));

            var service = new StartupNotificationService(mockLoggingService.Object);

            // Act & Assert
            Assert.DoesNotThrow(() => service.ShowStartupNotification(notifyIcon),
                "ShowStartupNotification should not throw exception even if logging fails");

            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de l'affichage de la notification de démarrage")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when startup notification fails");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void AreNotificationsSupported_WithException_ReturnsFalseAndLogsError()
        {
            // Arrange
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo(It.IsAny<string>()))
                             .Throws(new InvalidOperationException("Test exception"));

            var service = new StartupNotificationService(mockLoggingService.Object);

            // Act
            bool result = service.AreNotificationsSupported();

            // Assert
            Assert.That(result, Is.False, "Should return false when exception occurs (safe default)");

            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de la vérification du support des notifications")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when support check fails");
        }

        [Test]
        public void NotificationService_IsStateless()
        {
            // Arrange
            var icon1 = new NotifyIcon();
            var icon2 = new NotifyIcon();

            // Act - Appeler plusieurs fois les méthodes
            bool supported1 = _notificationService.AreNotificationsSupported();
            bool supported2 = _notificationService.AreNotificationsSupported();

            _notificationService.ShowStartupNotification(icon1);
            _notificationService.ShowStartupNotification(icon2);

            // Assert - Les résultats doivent être cohérents
            Assert.That(supported1, Is.EqualTo(supported2), "AreNotificationsSupported should return consistent results");

            // Cleanup
            icon1.Dispose();
            icon2.Dispose();
        }

        [TearDown]
        public void TearDown()
        {
            // Nettoyer les ressources si nécessaire
        }
    }
}
