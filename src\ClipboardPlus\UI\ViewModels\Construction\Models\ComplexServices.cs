using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Diagnostics;

namespace ClipboardPlus.UI.ViewModels.Construction.Models
{
    /// <summary>
    /// DTO pour les services complexes nécessitant une résolution spéciale.
    /// Ce record encapsule les services qui nécessitent une logique de résolution complexe
    /// comme GetLoggingService(), GetDeletionDiagnostic() et GetPersistenceService() qui étaient dans le constructeur original.
    ///
    /// [PHASE 1] Étendu pour inclure IPersistenceService dans la nouvelle architecture V2.
    /// </summary>
    /// <param name="LoggingService">Service de logging résolu via GetLoggingService() (peut être null)</param>
    /// <param name="DeletionDiagnostic">Service de diagnostic de suppression résolu via GetDeletionDiagnostic() (peut être null)</param>
    /// <param name="PersistenceService">Service de persistance résolu via GetPersistenceService() (peut être null, Phase 1 uniquement)</param>
    public record ComplexServices(
        ILoggingService? LoggingService,
        IDeletionDiagnostic? DeletionDiagnostic,
        IPersistenceService? PersistenceService = null);
}
