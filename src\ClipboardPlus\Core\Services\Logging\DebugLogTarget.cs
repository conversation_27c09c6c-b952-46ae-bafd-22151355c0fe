using System.Diagnostics;

namespace ClipboardPlus.Core.Services.Logging
{
    /// <summary>
    /// Cible de log pour Debug.WriteLine.
    /// Simple et efficace pour le débogage en développement.
    /// </summary>
    public class DebugLogTarget : ILogTarget
    {
        /// <summary>
        /// Écrit une entrée de log vers Debug.WriteLine.
        /// </summary>
        /// <param name="entry">L'entrée de log à écrire</param>
        public void Write(LogEntry entry)
        {
            try
            {
                // Écrire le message original (pas le format complet) pour Debug
                Debug.WriteLine(entry.Message);
            }
            catch
            {
                // Ignorer les erreurs de debug output
            }
        }
        
        /// <summary>
        /// Force l'écriture immédiate (pas nécessaire pour Debug).
        /// </summary>
        public void Flush()
        {
            // Debug.WriteLine n'a pas de buffer, rien à faire
        }
    }
}
