using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Interfaces
{
    /// <summary>
    /// Interface pour la validation des éléments du presse-papiers.
    /// Responsabilité unique : Valider les éléments selon les règles métier.
    /// </summary>
    public interface IClipboardItemValidator
    {
        /// <summary>
        /// Valide un élément du presse-papiers selon les règles métier.
        /// </summary>
        /// <param name="item">L'élément à valider</param>
        /// <param name="maxSizeBytes">Taille maximale autorisée en octets</param>
        /// <returns>Résultat de la validation</returns>
        /// <exception cref="ArgumentNullException">Si l'élément est null</exception>
        /// <exception cref="ArgumentException">Si l'élément ne respecte pas les règles de validation</exception>
        Task<ValidationResult> ValidateAsync(ClipboardItem item, long maxSizeBytes);
    }

    /// <summary>
    /// Résultat d'une validation d'élément.
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// Indique si la validation a réussi.
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Message d'erreur en cas d'échec de validation.
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Code d'erreur spécifique pour le diagnostic.
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// Crée un résultat de validation réussie.
        /// </summary>
        public static ValidationResult Success() => new() { IsValid = true };

        /// <summary>
        /// Crée un résultat de validation échouée.
        /// </summary>
        /// <param name="errorMessage">Message d'erreur</param>
        /// <param name="errorCode">Code d'erreur optionnel</param>
        public static ValidationResult Failure(string errorMessage, string? errorCode = null) => 
            new() { IsValid = false, ErrorMessage = errorMessage, ErrorCode = errorCode };
    }
}
