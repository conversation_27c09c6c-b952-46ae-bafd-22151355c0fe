using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Implementations;
using ClipboardPlus.Core.Services.Interfaces;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    [TestFixture]
    public class DuplicateDetectionRealWorldTests
    {
        private ClipboardHistoryManager _historyManager;
        private Mock<IPersistenceService> _mockPersistenceService;
        private Mock<ISettingsManager> _mockSettingsManager;
        private Mock<IClipboardInteractionService> _mockClipboardInteractionService;

        [SetUp]
        public void SetUp()
        {
            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockClipboardInteractionService = new Mock<IClipboardInteractionService>();

            // Configurer les mocks pour retourner des valeurs valides
            var idCounter = 1L;
            _mockPersistenceService.Setup(p => p.InsertClipboardItemAsync(It.IsAny<ClipboardItem>()))
                .Returns(() => Task.FromResult(idCounter++));
            
            _mockPersistenceService.Setup(p => p.UpdateClipboardItemAsync(It.IsAny<ClipboardItem>()))
                .Returns(Task.FromResult(1));
                
            _mockPersistenceService.Setup(p => p.GetAllClipboardItemsAsync())
                .Returns(Task.FromResult(new List<ClipboardItem>()));

            _mockSettingsManager.Setup(s => s.MaxHistoryItems).Returns(100);
            _mockSettingsManager.Setup(s => s.MaxStorableItemSizeBytes).Returns(1024 * 1024); // 1MB

            _historyManager = new ClipboardHistoryManager(
                _mockPersistenceService.Object,
                _mockSettingsManager.Object,
                _mockClipboardInteractionService.Object);
        }

        [Test]
        public async Task AddItemAsync_WithIdenticalContent_ShouldDetectDuplicate()
        {
            // Arrange
            var originalContent = "Test de détection de doublons - Contenu identique";
            var originalRawData = Encoding.UTF8.GetBytes(originalContent);

            var firstItem = new ClipboardItem
            {
                TextPreview = originalContent,
                DataType = ClipboardDataType.Text,
                RawData = originalRawData,
                Timestamp = DateTime.Now.AddMinutes(-1)
            };

            var duplicateItem = new ClipboardItem
            {
                TextPreview = originalContent, // Contenu identique
                DataType = ClipboardDataType.Text,
                RawData = originalRawData, // Données identiques
                Timestamp = DateTime.Now
            };

            // Configurer le mock pour retourner l'élément existant lors de la recherche de doublons
            _mockPersistenceService.Setup(p => p.GetAllClipboardItemsAsync())
                .Returns(Task.FromResult(new List<ClipboardItem> { firstItem }));

            // Act
            // Ajouter le premier élément
            var firstResult = await _historyManager.AddItemAsync(firstItem);
            
            // Réinitialiser le mock pour inclure le premier élément dans la recherche
            var existingItems = new List<ClipboardItem> { firstItem };
            existingItems[0].Id = firstResult; // Assigner l'ID retourné
            _mockPersistenceService.Setup(p => p.GetAllClipboardItemsAsync())
                .Returns(Task.FromResult(existingItems));

            // Ajouter le doublon
            var duplicateResult = await _historyManager.AddItemAsync(duplicateItem);

            // Assert
            Assert.AreEqual(firstResult, duplicateResult, 
                "Le doublon devrait retourner l'ID de l'élément existant");
            
            // Vérifier qu'aucun nouvel élément n'a été inséré pour le doublon
            _mockPersistenceService.Verify(p => p.InsertClipboardItemAsync(It.IsAny<ClipboardItem>()), 
                Times.Once, "Un seul élément devrait être inséré (pas le doublon)");
            
            // Vérifier que l'élément existant a été mis à jour
            _mockPersistenceService.Verify(p => p.UpdateClipboardItemAsync(It.IsAny<ClipboardItem>()), 
                Times.Once, "L'élément existant devrait être mis à jour avec le nouveau timestamp");

            // Vérifier qu'il n'y a qu'un seul élément dans l'historique
            Assert.AreEqual(1, _historyManager.HistoryItems.Count, 
                "Il ne devrait y avoir qu'un seul élément dans l'historique (pas de doublon)");
        }

        [Test]
        public async Task AddItemAsync_WithDifferentContent_ShouldNotDetectDuplicate()
        {
            // Arrange
            var firstContent = "Premier contenu unique";
            var secondContent = "Deuxième contenu différent";

            var firstItem = new ClipboardItem
            {
                TextPreview = firstContent,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(firstContent),
                Timestamp = DateTime.Now.AddMinutes(-1)
            };

            var secondItem = new ClipboardItem
            {
                TextPreview = secondContent,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(secondContent),
                Timestamp = DateTime.Now
            };

            // Act
            var firstResult = await _historyManager.AddItemAsync(firstItem);
            var secondResult = await _historyManager.AddItemAsync(secondItem);

            // Assert
            Assert.AreNotEqual(firstResult, secondResult, 
                "Les contenus différents devraient avoir des IDs différents");
            
            // Vérifier que deux éléments ont été insérés
            _mockPersistenceService.Verify(p => p.InsertClipboardItemAsync(It.IsAny<ClipboardItem>()), 
                Times.Exactly(2), "Deux éléments distincts devraient être insérés");
            
            // Vérifier qu'il y a deux éléments dans l'historique
            Assert.AreEqual(2, _historyManager.HistoryItems.Count, 
                "Il devrait y avoir deux éléments distincts dans l'historique");
        }

        [Test]
        public async Task AddItemAsync_WithSimilarButNotIdenticalContent_ShouldNotDetectDuplicate()
        {
            // Arrange
            var firstContent = "Contenu de test";
            var secondContent = "Contenu de test "; // Espace supplémentaire à la fin

            var firstItem = new ClipboardItem
            {
                TextPreview = firstContent,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(firstContent),
                Timestamp = DateTime.Now.AddMinutes(-1)
            };

            var secondItem = new ClipboardItem
            {
                TextPreview = secondContent,
                DataType = ClipboardDataType.Text,
                RawData = Encoding.UTF8.GetBytes(secondContent),
                Timestamp = DateTime.Now
            };

            // Act
            var firstResult = await _historyManager.AddItemAsync(firstItem);
            var secondResult = await _historyManager.AddItemAsync(secondItem);

            // Assert
            Assert.AreNotEqual(firstResult, secondResult, 
                "Les contenus similaires mais non identiques devraient avoir des IDs différents");
            
            Assert.AreEqual(2, _historyManager.HistoryItems.Count, 
                "Il devrait y avoir deux éléments distincts dans l'historique");
        }
    }
}
