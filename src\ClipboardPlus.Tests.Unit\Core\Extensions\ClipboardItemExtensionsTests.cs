using System;
using System.IO;
using System.Text;
using System.Windows;
using System.Windows.Media.Imaging;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Extensions;

namespace ClipboardPlus.Tests.Unit.Core.Extensions
{
    /// <summary>
    /// Tests unitaires pour la classe ClipboardItemExtensions
    /// </summary>
    [TestFixture]
    public class ClipboardItemExtensionsTests
    {
        private ClipboardItem _clipboardItem = null!;

        [SetUp]
        public void Setup()
        {
            _clipboardItem = new ClipboardItem();
        }

        [TearDown]
        public void TearDown()
        {
            _clipboardItem = null!;
        }

        #region ToDataObject - Null/Empty Tests

        [Test]
        [Description("Vérifie que ToDataObject retourne un DataObject vide quand RawData est null")]
        public void ToDataObject_WithNullRawData_ReturnsEmptyDataObject()
        {
            // Arrange
            _clipboardItem.RawData = null;
            _clipboardItem.DataType = ClipboardDataType.Text;

            // Act
            var result = _clipboardItem.ToDataObject();

            // Assert
            Assert.That(result, Is.Not.Null, "ToDataObject ne devrait jamais retourner null");
            Assert.That(result, Is.TypeOf<DataObject>(), "Le résultat devrait être un DataObject");

            // Vérifier que le DataObject est vide (pas de données texte)
            Assert.That(result.GetDataPresent(DataFormats.UnicodeText), Is.False, "Le DataObject ne devrait pas contenir de texte");
            Assert.That(result.GetDataPresent(DataFormats.Bitmap), Is.False, "Le DataObject ne devrait pas contenir d'image");
        }

        [Test]
        [Description("Vérifie que ToDataObject fonctionne avec un ClipboardItem par défaut")]
        public void ToDataObject_WithDefaultClipboardItem_ReturnsEmptyDataObject()
        {
            // Arrange - ClipboardItem par défaut (RawData = null)

            // Act
            var result = _clipboardItem.ToDataObject();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.TypeOf<DataObject>());
            Assert.That(result.GetDataPresent(DataFormats.UnicodeText), Is.False);
        }

        #endregion

        #region ToDataObject - Text Tests

        [Test]
        [Description("Vérifie que ToDataObject convertit correctement un ClipboardItem de type Text")]
        public void ToDataObject_WithTextData_SetsTextCorrectly()
        {
            // Arrange
            const string testText = "Test clipboard text content";
            _clipboardItem.DataType = ClipboardDataType.Text;
            _clipboardItem.RawData = Encoding.UTF8.GetBytes(testText);

            // Act
            var result = _clipboardItem.ToDataObject();

            // Assert
            Assert.That(result, Is.Not.Null);

            // Le DataObject utilise le format UnicodeText
            Assert.That(result.GetDataPresent(DataFormats.UnicodeText), Is.True, "Le DataObject devrait contenir du texte Unicode");

            var retrievedText = result.GetData(DataFormats.UnicodeText) as string;

            Assert.That(retrievedText, Is.EqualTo(testText), "Le texte récupéré devrait correspondre au texte original");
        }

        [Test]
        [Description("Vérifie que ToDataObject gère correctement le texte avec des caractères spéciaux")]
        public void ToDataObject_WithSpecialCharactersText_HandlesCorrectly()
        {
            // Arrange
            const string specialText = "Texte avec accents: àéèùç, symboles: €£$, émojis: 🎉✅";
            _clipboardItem.DataType = ClipboardDataType.Text;
            _clipboardItem.RawData = Encoding.UTF8.GetBytes(specialText);

            // Act
            var result = _clipboardItem.ToDataObject();

            // Assert
            Assert.That(result.GetDataPresent(DataFormats.UnicodeText), Is.True);
            var retrievedText = result.GetData(DataFormats.UnicodeText) as string;
            Assert.That(retrievedText, Is.EqualTo(specialText));
        }

        [Test]
        [Description("Vérifie que ToDataObject gère correctement le texte vide")]
        public void ToDataObject_WithEmptyText_SetsEmptyText()
        {
            // Arrange
            _clipboardItem.DataType = ClipboardDataType.Text;
            _clipboardItem.RawData = Encoding.UTF8.GetBytes(string.Empty);

            // Act
            var result = _clipboardItem.ToDataObject();

            // Assert
            Assert.That(result.GetDataPresent(DataFormats.UnicodeText), Is.True);
            var retrievedText = result.GetData(DataFormats.UnicodeText) as string;
            Assert.That(retrievedText, Is.EqualTo(string.Empty));
        }

        [Test]
        [Description("Vérifie que ToDataObject gère correctement le texte multiligne")]
        public void ToDataObject_WithMultilineText_PreservesLineBreaks()
        {
            // Arrange
            const string multilineText = "Ligne 1\nLigne 2\r\nLigne 3\rLigne 4";
            _clipboardItem.DataType = ClipboardDataType.Text;
            _clipboardItem.RawData = Encoding.UTF8.GetBytes(multilineText);

            // Act
            var result = _clipboardItem.ToDataObject();

            // Assert
            Assert.That(result.GetDataPresent(DataFormats.UnicodeText), Is.True);
            var retrievedText = result.GetData(DataFormats.UnicodeText) as string;
            Assert.That(retrievedText, Is.EqualTo(multilineText));
        }

        #endregion

        #region ToDataObject - Image Tests

        [Test]
        [Description("Vérifie que ToDataObject convertit correctement un ClipboardItem de type Image")]
        public void ToDataObject_WithImageData_SetsImageCorrectly()
        {
            // Arrange
            var testBitmap = CreateTestBitmapImage();
            var imageBytes = BitmapToByteArray(testBitmap);

            _clipboardItem.DataType = ClipboardDataType.Image;
            _clipboardItem.RawData = imageBytes;

            // Act
            var result = _clipboardItem.ToDataObject();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.GetDataPresent(DataFormats.Bitmap), Is.True, "Le DataObject devrait contenir une image");

            var retrievedImage = result.GetData(DataFormats.Bitmap) as BitmapSource;
            Assert.That(retrievedImage, Is.Not.Null, "L'image récupérée ne devrait pas être null");
            Assert.That(retrievedImage!.PixelWidth, Is.GreaterThan(0), "L'image devrait avoir une largeur valide");
            Assert.That(retrievedImage!.PixelHeight, Is.GreaterThan(0), "L'image devrait avoir une hauteur valide");
        }

        [Test]
        [Description("Vérifie que ToDataObject lève une exception avec des données d'image corrompue")]
        public void ToDataObject_WithCorruptedImageData_ThrowsException()
        {
            // Arrange
            _clipboardItem.DataType = ClipboardDataType.Image;
            _clipboardItem.RawData = new byte[] { 0x00, 0x01, 0x02, 0x03 }; // Données invalides

            // Act & Assert
            Assert.Throws<NotSupportedException>(() =>
            {
                _clipboardItem.ToDataObject();
            }, "ToDataObject devrait lever une exception avec des données d'image invalides");
        }

        #endregion

        #region ToDataObject - Other DataTypes Tests

        [Test]
        [Description("Vérifie que ToDataObject ignore les types de données non supportés")]
        public void ToDataObject_WithUnsupportedDataType_ReturnsEmptyDataObject()
        {
            // Arrange
            _clipboardItem.DataType = ClipboardDataType.Html; // Type non supporté par l'extension
            _clipboardItem.RawData = Encoding.UTF8.GetBytes("<html><body>Test</body></html>");

            // Act
            var result = _clipboardItem.ToDataObject();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.GetDataPresent(DataFormats.UnicodeText), Is.False, "Le DataObject ne devrait pas contenir de texte pour un type non supporté");
            Assert.That(result.GetDataPresent(DataFormats.Bitmap), Is.False, "Le DataObject ne devrait pas contenir d'image pour un type non supporté");
        }

        [Test]
        [Description("Vérifie que ToDataObject gère tous les types d'énumération ClipboardDataType")]
        public void ToDataObject_WithAllDataTypes_HandlesCorrectly()
        {
            // Arrange & Act & Assert
            var dataTypes = Enum.GetValues<ClipboardDataType>();

            foreach (var dataType in dataTypes)
            {
                _clipboardItem.DataType = dataType;
                _clipboardItem.RawData = Encoding.UTF8.GetBytes("test data");

                if (dataType == ClipboardDataType.Image)
                {
                    // Pour les images, on s'attend à une exception avec des données invalides
                    Assert.Throws<NotSupportedException>(() =>
                    {
                        _clipboardItem.ToDataObject();
                    }, $"ToDataObject devrait lever une exception pour le type Image avec des données invalides");
                }
                else
                {
                    Assert.DoesNotThrow(() =>
                    {
                        var result = _clipboardItem.ToDataObject();
                        Assert.That(result, Is.Not.Null, $"ToDataObject devrait fonctionner pour le type {dataType}");
                    }, $"ToDataObject ne devrait pas lever d'exception pour le type {dataType}");
                }
            }
        }

        #endregion

        #region Integration Tests

        [Test]
        [Description("Vérifie que ToDataObject peut être utilisé dans un scénario de copie-coller")]
        public void ToDataObject_IntegrationTest_CanBeUsedForClipboard()
        {
            // Arrange
            const string testText = "Integration test text";
            _clipboardItem.DataType = ClipboardDataType.Text;
            _clipboardItem.RawData = Encoding.UTF8.GetBytes(testText);

            // Act
            var dataObject = _clipboardItem.ToDataObject();

            // Assert - Simuler l'utilisation avec le presse-papiers
            Assert.That(dataObject, Is.Not.Null);
            Assert.That(dataObject.GetDataPresent(DataFormats.UnicodeText), Is.True);

            // Vérifier que le DataObject peut être utilisé comme source de données
            var formats = dataObject.GetFormats();
            Assert.That(formats, Is.Not.Null);
            Assert.That(formats.Length, Is.GreaterThan(0));
        }

        [Test]
        [Description("Vérifie que ToDataObject préserve l'intégrité des données lors de conversions multiples")]
        public void ToDataObject_MultipleConversions_PreservesDataIntegrity()
        {
            // Arrange
            const string originalText = "Test data integrity";
            _clipboardItem.DataType = ClipboardDataType.Text;
            _clipboardItem.RawData = Encoding.UTF8.GetBytes(originalText);

            // Act - Convertir plusieurs fois
            var dataObject1 = _clipboardItem.ToDataObject();
            var dataObject2 = _clipboardItem.ToDataObject();

            // Assert
            var text1 = dataObject1.GetData(DataFormats.UnicodeText) as string;
            var text2 = dataObject2.GetData(DataFormats.UnicodeText) as string;

            Assert.That(text1, Is.EqualTo(originalText));
            Assert.That(text2, Is.EqualTo(originalText));
            Assert.That(text1, Is.EqualTo(text2), "Les conversions multiples devraient donner le même résultat");
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Crée une image de test pour les tests
        /// </summary>
        /// <returns>Un BitmapImage de test</returns>
        private static BitmapImage CreateTestBitmapImage()
        {
            // Créer une image de test simple (1x1 pixel blanc)
            var bitmap = new WriteableBitmap(1, 1, 96, 96, System.Windows.Media.PixelFormats.Bgr32, null);

            // Convertir en BitmapImage
            var bitmapImage = new BitmapImage();
            using (var stream = new MemoryStream())
            {
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(bitmap));
                encoder.Save(stream);
                stream.Position = 0;

                bitmapImage.BeginInit();
                bitmapImage.StreamSource = stream;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();
            }

            bitmapImage.Freeze();
            return bitmapImage;
        }

        /// <summary>
        /// Convertit un BitmapImage en tableau de bytes
        /// </summary>
        /// <param name="bitmapImage">L'image à convertir</param>
        /// <returns>Le tableau de bytes représentant l'image</returns>
        private static byte[] BitmapToByteArray(BitmapImage bitmapImage)
        {
            using var stream = new MemoryStream();
            var encoder = new PngBitmapEncoder();
            encoder.Frames.Add(BitmapFrame.Create(bitmapImage));
            encoder.Save(stream);
            return stream.ToArray();
        }

        #endregion
    }
}
