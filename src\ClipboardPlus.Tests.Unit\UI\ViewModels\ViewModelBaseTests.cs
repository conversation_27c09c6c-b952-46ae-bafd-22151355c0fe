using System;
using System.ComponentModel;
using NUnit.Framework;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Tests.Unit.UI.ViewModels
{
    [TestFixture]
    public class ViewModelBaseTests
    {
        private class TestViewModel : ViewModelBase
        {
            private string _testProperty = string.Empty;
            public string TestProperty
            {
                get => _testProperty;
                set => SetProperty(ref _testProperty, value);
            }

            private int _testIntProperty;
            public int TestIntProperty
            {
                get => _testIntProperty;
                set => SetProperty(ref _testIntProperty, value);
            }

            private bool _testBoolProperty;
            public bool TestBoolProperty
            {
                get => _testBoolProperty;
                set => SetProperty(ref _testBoolProperty, value);
            }
        }

        [Test]
        public void SetProperty_DifferentValue_ReturnsTrue_AndRaisesPropertyChanged()
        {
            // Arrange
            var viewModel = new TestViewModel();
            string newValue = "Nouvelle valeur";
            string? propertyChangedName = null;
            
            viewModel.PropertyChanged += (sender, e) => {
                propertyChangedName = e.PropertyName;
            };

            // Act
            viewModel.TestProperty = newValue;

            // Assert
            Assert.AreEqual(newValue, viewModel.TestProperty);
            Assert.AreEqual("TestProperty", propertyChangedName);
        }

        [Test]
        public void SetProperty_SameValue_ReturnsFalse_AndDoesNotRaisePropertyChanged()
        {
            // Arrange
            var viewModel = new TestViewModel();
            string initialValue = "Valeur initiale";
            viewModel.TestProperty = initialValue;
            
            bool eventRaised = false;
            viewModel.PropertyChanged += (sender, e) => {
                eventRaised = true;
            };

            // Act
            viewModel.TestProperty = initialValue;

            // Assert
            Assert.AreEqual(initialValue, viewModel.TestProperty);
            Assert.IsFalse(eventRaised);
        }

        [Test]
        public void SetProperty_MultipleProperties_RaisesCorrectPropertyChangedEvents()
        {
            // Arrange
            var viewModel = new TestViewModel();
            var propertyChangedEvents = new System.Collections.Generic.List<string>();
            
            viewModel.PropertyChanged += (sender, e) => {
                if (e.PropertyName != null)
                {
                    propertyChangedEvents.Add(e.PropertyName);
                }
            };

            // Act
            viewModel.TestProperty = "Test";
            viewModel.TestIntProperty = 42;
            viewModel.TestBoolProperty = true;

            // Assert
            Assert.AreEqual(3, propertyChangedEvents.Count);
            Assert.AreEqual("TestProperty", propertyChangedEvents[0]);
            Assert.AreEqual("TestIntProperty", propertyChangedEvents[1]);
            Assert.AreEqual("TestBoolProperty", propertyChangedEvents[2]);
        }

        [Test]
        public void SetProperty_ChangeIntValue_RaisesPropertyChanged()
        {
            // Arrange
            var viewModel = new TestViewModel();
            int newValue = 123;
            string? propertyChangedName = null;
            
            viewModel.PropertyChanged += (sender, e) => {
                propertyChangedName = e.PropertyName;
            };

            // Act
            viewModel.TestIntProperty = newValue;

            // Assert
            Assert.AreEqual(newValue, viewModel.TestIntProperty);
            Assert.AreEqual("TestIntProperty", propertyChangedName);
        }

        [Test]
        public void SetProperty_ChangeBoolValue_RaisesPropertyChanged()
        {
            // Arrange
            var viewModel = new TestViewModel();
            bool newValue = true;
            string? propertyChangedName = null;
            
            viewModel.PropertyChanged += (sender, e) => {
                propertyChangedName = e.PropertyName;
            };

            // Act
            viewModel.TestBoolProperty = newValue;

            // Assert
            Assert.AreEqual(newValue, viewModel.TestBoolProperty);
            Assert.AreEqual("TestBoolProperty", propertyChangedName);
        }
    }
} 