using System;
using System.Threading;
using System.Windows;

namespace ClipboardPlus.Core.Services.Logging
{
    /// <summary>
    /// Implémentation de ILogEntryFactory qui collecte les informations de contexte.
    /// Optimisée pour la performance avec détection efficace du contexte UI.
    /// </summary>
    public class LogEntryFactory : ILogEntryFactory
    {
        /// <summary>
        /// Crée une entrée de log avec toutes les informations de contexte.
        /// </summary>
        /// <param name="level">Le niveau de log</param>
        /// <param name="message">Le message à logger</param>
        /// <param name="callerInfo">Informations sur l'appelant (fourni par Caller Attributes)</param>
        /// <returns>Une entrée de log complète</returns>
        public LogEntry Create(string level, string message, string callerInfo)
        {
            var timestamp = DateTime.Now;
            var threadId = Thread.CurrentThread.ManagedThreadId.ToString();
            var context = GetExecutionContext();
            
            // Appliquer les transformations spéciales selon le niveau
            var processedMessage = ProcessMessageByLevel(level, message);
            
            return new LogEntry(
                Level: level,
                Message: processedMessage,
                Timestamp: timestamp,
                ThreadId: threadId,
                Context: context,
                CallerInfo: callerInfo
            );
        }
        
        /// <summary>
        /// Détermine le contexte d'exécution (UI-Thread, Background, etc.).
        /// Optimisé pour éviter les exceptions coûteuses.
        /// </summary>
        private string GetExecutionContext()
        {
            try
            {
                // Vérifier si on est sur le thread UI de WPF
                if (System.Windows.Application.Current?.Dispatcher != null)
                {
                    if (System.Windows.Application.Current.Dispatcher.CheckAccess())
                    {
                        return "UI-Thread";
                    }
                    else
                    {
                        return "Background";
                    }
                }
                else
                {
                    return "Non-UI";
                }
            }
            catch
            {
                // En cas d'erreur, retourner un contexte par défaut
                return "Inconnu";
            }
        }
        
        /// <summary>
        /// Applique les transformations spéciales selon le niveau de log.
        /// </summary>
        /// <param name="level">Le niveau de log</param>
        /// <param name="message">Le message original</param>
        /// <returns>Le message transformé si nécessaire</returns>
        private string ProcessMessageByLevel(string level, string message)
        {
            return level switch
            {
                "CRITIQUE" => $"!!! CRITIQUE !!! {message}",
                _ => message
            };
        }
    }
}
