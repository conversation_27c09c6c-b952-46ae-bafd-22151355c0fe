using System;
using System.Diagnostics;
using System.Linq;
using System.Windows;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services.NewItem.Models;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.Helpers;

namespace ClipboardPlus.Core.Services.NewItem.Implementations
{
    /// <summary>
    /// Service de validation pour la création de nouveaux éléments.
    /// Implémente le principe SRP : responsabilité unique de validation.
    /// </summary>
    public class NewItemValidationService : INewItemValidationService
    {
        private readonly ILoggingService? _loggingService;

        public NewItemValidationService(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        public NewItemValidationResult ValidateCanCreateNewItem(bool isOperationInProgress, bool isItemCreationActive)
        {
            if (isOperationInProgress)
            {
                _loggingService?.LogWarning("Validation échouée: Une autre opération est en cours");
                return NewItemValidationResult.Failure("Une autre opération est en cours. Veuillez patienter.");
            }

            if (isItemCreationActive)
            {
                _loggingService?.LogWarning("Validation échouée: La création d'élément est déjà active");
                return NewItemValidationResult.Failure("La création d'élément est déjà active.");
            }

            _loggingService?.LogInfo("Validation réussie: Création d'élément autorisée");
            return NewItemValidationResult.Success();
        }
    }

    /// <summary>
    /// Service de configuration des dialogues.
    /// Implémente le principe SRP : responsabilité unique de configuration de dialogues.
    /// </summary>
    public class DialogConfigurationService : IDialogConfigurationService
    {
        private readonly ILoggingService? _loggingService;

        public DialogConfigurationService(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        public void ConfigureNewItemDialog(object dialog, object dataContext)
        {
            if (dialog is Window window)
            {
                window.DataContext = dataContext;

                var ownerWindow = FindOwnerWindow();
                if (ownerWindow != null)
                {
                    window.Owner = ownerWindow;
                    _loggingService?.LogInfo("Owner défini comme ClipboardHistoryWindow pour centrage correct.");
                }
                else
                {
                    _loggingService?.LogWarning("Aucune ClipboardHistoryWindow trouvée, utilisation du centrage écran.");
                }
            }
            else
            {
                _loggingService?.LogWarning($"Le dialogue fourni n'est pas une Window: {dialog?.GetType().Name}");
            }
        }

        public Window? FindOwnerWindow()
        {
            try
            {
                return System.Windows.Application.Current?.Windows
                    ?.OfType<ClipboardPlus.UI.Windows.ClipboardHistoryWindow>()
                    ?.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors de la recherche de la fenêtre propriétaire: {ex.Message}", ex);
                return null;
            }
        }
    }

    /// <summary>
    /// Service de gestion du mode test.
    /// Implémente le principe SRP : responsabilité unique de détection et gestion du mode test.
    /// REFACTORISÉ : Utilise maintenant ITestEnvironmentDetector pour la détection.
    /// </summary>
    public class TestModeHandler : ITestModeHandler
    {
        private readonly ILoggingService? _loggingService;
        private readonly ITestEnvironmentDetector? _testEnvironmentDetector;

        public TestModeHandler(ILoggingService? loggingService = null, ITestEnvironmentDetector? testEnvironmentDetector = null)
        {
            _loggingService = loggingService;
            _testEnvironmentDetector = testEnvironmentDetector;
        }

        public bool IsInTestMode()
        {
            try
            {
                // REFACTORISÉ : Utiliser ITestEnvironmentDetector injecté si disponible
                if (_testEnvironmentDetector != null)
                {
                    return _testEnvironmentDetector.IsInTestEnvironment();
                }

                // Fallback : logique originale si aucun service n'est injecté
                if (System.Windows.Application.Current == null) return true;
                if (System.Windows.Application.Current.Dispatcher == null) return true;

                if (Debugger.IsAttached)
                {
                    var stackTrace = new StackTrace();
                    var frames = stackTrace.GetFrames();
                    if (frames != null)
                    {
                        foreach (var frame in frames)
                        {
                            var method = frame.GetMethod();
                            if (method?.DeclaringType?.FullName?.Contains("Test", StringComparison.OrdinalIgnoreCase) == true ||
                                method?.Name?.Contains("Test", StringComparison.OrdinalIgnoreCase) == true)
                            {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors de la détection du mode test: {ex.Message}", ex);
                return false; // En cas d'erreur, considérer comme mode normal
            }
        }

        public void HandleTestMode(IOperationStateManager stateManager)
        {
            if (stateManager == null)
                throw new ArgumentNullException(nameof(stateManager));

            try
            {
                _loggingService?.LogInfo("Mode test détecté - Configuration du contenu de test");
                stateManager.NewItemTextContent = "Élément de test";
                stateManager.IsItemCreationActive = true;
                stateManager.RefreshItemCreationCommands();
                _loggingService?.LogInfo("Mode test configuré avec succès");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors de la gestion du mode test: {ex.Message}", ex);
                throw; // Re-lancer l'exception car c'est un problème critique
            }
        }
    }

    /// <summary>
    /// Service de gestion d'erreurs.
    /// Implémente le principe SRP : responsabilité unique de gestion d'erreurs.
    /// </summary>
    public class ErrorHandlingService : IErrorHandlingService
    {
        public void HandleError(string message, string title, Exception? exception = null, string context = "", object? viewModel = null)
        {
            // Déléguer à ErrorMessageHelper existant pour maintenir la compatibilité
            ErrorMessageHelper.ShowError(message, title, exception, context, viewModel);
        }
    }

    /// <summary>
    /// Service d'affichage de dialogues.
    /// Implémente le principe SRP : responsabilité unique d'affichage de dialogues.
    /// </summary>
    public class DialogService : ClipboardPlus.Core.Services.NewItem.Interfaces.IDialogService
    {
        private readonly ILoggingService? _loggingService;

        public DialogService(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        public bool? ShowDialog(object dialog)
        {
            try
            {
                if (dialog is Window window)
                {
                    _loggingService?.LogInfo($"Affichage du dialogue: {window.GetType().Name}");
                    var result = window.ShowDialog();
                    _loggingService?.LogInfo($"Dialogue fermé avec résultat: {result}");
                    return result;
                }
                else
                {
                    _loggingService?.LogError($"L'objet fourni n'est pas une Window: {dialog?.GetType().Name}");
                    throw new ArgumentException("Le dialogue doit être une Window", nameof(dialog));
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors de l'affichage du dialogue: {ex.Message}", ex);
                throw; // Re-lancer l'exception car l'affichage du dialogue est critique
            }
        }
    }

    /// <summary>
    /// Orchestrateur principal pour la création de nouveaux éléments.
    /// Implémente le principe SRP : responsabilité unique de coordination du processus complet.
    /// Respecte le principe DIP : dépend d'abstractions, pas de concrétions.
    /// </summary>
    public class NewItemCreationOrchestrator : INewItemCreationOrchestrator
    {
        private readonly INewItemValidationService _validationService;
        private readonly ITestModeHandler _testModeHandler;
        private readonly IOperationStateManager _stateManager;
        private readonly IDialogConfigurationService _dialogConfigService;
        private readonly ClipboardPlus.Core.Services.NewItem.Interfaces.IDialogService _dialogService;
        private readonly IErrorHandlingService _errorHandlingService;
        private readonly ILoggingService? _loggingService;
        private readonly object? _originalViewModel; // Référence au ViewModel original pour le DataContext

        public NewItemCreationOrchestrator(
            INewItemValidationService validationService,
            ITestModeHandler testModeHandler,
            IOperationStateManager stateManager,
            IDialogConfigurationService dialogConfigService,
            ClipboardPlus.Core.Services.NewItem.Interfaces.IDialogService dialogService,
            IErrorHandlingService errorHandlingService,
            ILoggingService? loggingService = null,
            object? originalViewModel = null)
        {
            _validationService = validationService ?? throw new ArgumentNullException(nameof(validationService));
            _testModeHandler = testModeHandler ?? throw new ArgumentNullException(nameof(testModeHandler));
            _stateManager = stateManager ?? throw new ArgumentNullException(nameof(stateManager));
            _dialogConfigService = dialogConfigService ?? throw new ArgumentNullException(nameof(dialogConfigService));
            _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            _errorHandlingService = errorHandlingService ?? throw new ArgumentNullException(nameof(errorHandlingService));
            _loggingService = loggingService;
            _originalViewModel = originalViewModel;
        }

        public void PrepareNewItem()
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            bool isInTestMode = _testModeHandler.IsInTestMode();
            _loggingService?.LogInfo($"[{operationId}][PRÉPARATION] PrepareNewItem - Mode test: {isInTestMode}");

            // 1. Validation (SRP)
            var validation = _validationService.ValidateCanCreateNewItem(
                _stateManager.IsOperationInProgress,
                _stateManager.IsItemCreationActive);

            if (!validation.IsValid)
            {
                _loggingService?.LogWarning($"[{operationId}] PrepareNewItem: Opération déjà en cours.");
                _errorHandlingService.HandleError(validation.ErrorMessage, "ClipboardPlus", null,
                    $"[{operationId}] PrepareNewItem.OperationInProgress", _stateManager);
                return;
            }

            try
            {
                _stateManager.IsOperationInProgress = true;
                _loggingService?.LogInfo($"[{operationId}] PrepareNewItem: IsOperationInProgress défini à true");

                // 2. Gestion mode test (SRP)
                if (isInTestMode)
                {
                    _testModeHandler.HandleTestMode(_stateManager);
                    _loggingService?.LogInfo($"[{operationId}][FIN] PrepareNewItem (Mode Test) - Création activée.");
                    return;
                }

                // 3. Préparation dialogue (SRP)
                _loggingService?.LogInfo($"[{operationId}] PrepareNewItem: Activation du mode de création d'élément.");
                _stateManager.IsItemCreationActive = true;
                _stateManager.NewItemTextContent = string.Empty;

                var newItemDialog = new ClipboardPlus.UI.Dialogs.NewItemEditorDialog();
                // Utiliser le ViewModel original comme DataContext pour que les commandes fonctionnent
                var dataContext = _originalViewModel ?? _stateManager;
                _dialogConfigService.ConfigureNewItemDialog(newItemDialog, dataContext);

                // 4. Affichage dialogue (SRP)
                _loggingService?.LogInfo($"🚀 [{operationId}] AVANT newItemDialog.ShowDialog()");
                bool? dialogResult = _dialogService.ShowDialog(newItemDialog);
                _loggingService?.LogInfo($"✅ [{operationId}] newItemDialog.ShowDialog() terminé. Résultat: {dialogResult}");

                // 5. Traitement résultat (SRP)
                if (dialogResult == true)
                {
                    _loggingService?.LogInfo($"[{operationId}] PrepareNewItem: Dialogue fermé avec succès - Nouvel élément créé");
                }
                else
                {
                    _loggingService?.LogInfo($"[{operationId}] PrepareNewItem: Dialogue annulé ou fermé sans sauvegarde");
                    _stateManager.IsItemCreationActive = false;
                    _stateManager.NewItemTextContent = string.Empty;
                }

                _stateManager.RefreshItemCreationCommands();
                _loggingService?.LogInfo($"[{operationId}][FIN] PrepareNewItem - Dialogue modal fermé, état réinitialisé");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] PrepareNewItem: Erreur lors de l'ouverture du dialogue: {ex.Message}", ex);
                _errorHandlingService.HandleError($"Erreur lors de l'ouverture du dialogue de création: {ex.Message}",
                    "ClipboardPlus - Erreur", ex, $"[{operationId}] PrepareNewItem.Exception", _stateManager);
            }
            finally
            {
                _stateManager.IsOperationInProgress = false;
                _loggingService?.LogInfo($"[{operationId}] PrepareNewItem: IsOperationInProgress réinitialisé dans finally");
            }
        }
    }
}
