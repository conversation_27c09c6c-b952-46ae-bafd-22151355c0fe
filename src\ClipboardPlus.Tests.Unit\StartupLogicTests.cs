using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;

namespace ClipboardPlus.Tests.Unit
{
    [TestFixture]
    public class StartupLogicTests
    {
        private Mock<ILoggingService> _mockLogger = null!;
        private Mock<ISingleInstanceService> _mockSingleInstance = null!;
        private Mock<ICommandLineParser> _mockCommandLine = null!;
        private Mock<IStartupOrchestrator> _mockOrchestrator = null!;
        private Mock<ISystemTrayService> _mockSystemTray = null!;
        private Mock<IApplicationLifetimeManager> _mockLifetimeManager = null!;
        private ServiceProvider _provider = null!;
        private StartupLogic _startupLogic = null!;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockSingleInstance = new Mock<ISingleInstanceService>();
            _mockCommandLine = new Mock<ICommandLineParser>();
            _mockOrchestrator = new Mock<IStartupOrchestrator>();
            _mockSystemTray = new Mock<ISystemTrayService>();
            _mockLifetimeManager = new Mock<IApplicationLifetimeManager>();

            // AUCUN PATCH ! Ce test unitaire doit utiliser des mocks, pas la vraie configuration
            var services = new ServiceCollection();
            services.AddSingleton(_mockLogger.Object);
            services.AddSingleton(_mockSingleInstance.Object);
            services.AddSingleton(_mockCommandLine.Object);
            services.AddSingleton(_mockOrchestrator.Object);
            services.AddSingleton(_mockSystemTray.Object);
            services.AddSingleton(_mockLifetimeManager.Object);
            _provider = services.BuildServiceProvider();
            
            // Configuration par défaut pour le cas nominal
            _mockSingleInstance.Setup(s => s.IsFirstInstance).Returns(true);
            _mockCommandLine.Setup(c => c.ProcessCommandLineArguments(It.IsAny<string[]>(), It.IsAny<IServiceProvider>())).Returns(true);
            _mockLifetimeManager.Setup(l => l.ProcessClipboardContentAsync()).Returns(Task.CompletedTask);
            _mockOrchestrator.Setup(o => o.StartApplication(
                It.IsAny<IServiceProvider>(),
                It.IsAny<EventHandler>(),
                It.IsAny<EventHandler>()))
                .ReturnsAsync(_mockSystemTray.Object);
                
            _startupLogic = new StartupLogic(_provider);
        }

        [Test]
        [Description("Démarrage nominal : la séquence complète s'exécute et retourne Success")]
        public async Task ExecuteAsync_Nominal_ReturnsSuccess()
        {
            // Act
            var (status, trayService) = await _startupLogic.ExecuteAsync(new string[0], (s, e) => {}, (s, e) => {});

            // Assert
            Assert.That(status, Is.EqualTo(StartupStatus.Success));
            Assert.That(trayService, Is.EqualTo(_mockSystemTray.Object));
            
            // Vérification de l'ordre d'appel des méthodes
            _mockSingleInstance.Verify(s => s.IsFirstInstance, Times.AtLeastOnce);
            _mockCommandLine.Verify(c => c.ProcessCommandLineArguments(It.IsAny<string[]>(), It.IsAny<IServiceProvider>()), Times.Once);
            _mockOrchestrator.Verify(o => o.StartApplication(
                It.IsAny<IServiceProvider>(),
                It.IsAny<EventHandler>(),
                It.IsAny<EventHandler>()), Times.Once);
            _mockSingleInstance.Verify(s => s.RegisterWindowMessageHandler(null), Times.Once);
        }

        [Test]
        [Description("Seconde instance détectée : NotifyExistingInstance est appelé et ShutdownRequested retourné")]
        public async Task ExecuteAsync_SecondInstance_ReturnsShutdownRequested()
        {
            // Arrange
            _mockSingleInstance.Setup(s => s.IsFirstInstance).Returns(false);

            // Act
            var (status, trayService) = await _startupLogic.ExecuteAsync(new string[0], (s, e) => {}, (s, e) => {});

            // Assert
            _mockSingleInstance.Verify(s => s.NotifyExistingInstance(), Times.Once);
            Assert.That(status, Is.EqualTo(StartupStatus.ShutdownRequested));
            Assert.That(trayService, Is.Null);
            
            // Vérifier que les étapes suivantes n'ont pas été exécutées
            _mockCommandLine.Verify(c => c.ProcessCommandLineArguments(It.IsAny<string[]>(), It.IsAny<IServiceProvider>()), Times.Never);
            _mockOrchestrator.Verify(o => o.StartApplication(
                It.IsAny<IServiceProvider>(),
                It.IsAny<EventHandler>(),
                It.IsAny<EventHandler>()), Times.Never);
        }

        [Test]
        [Description("Arrêt par ligne de commande : ProcessCommandLineArguments retourne false, ShutdownRequested")]
        public async Task ExecuteAsync_CommandLineShutdown_ReturnsShutdownRequested()
        {
            // Arrange
            _mockCommandLine.Setup(c => c.ProcessCommandLineArguments(It.IsAny<string[]>(), It.IsAny<IServiceProvider>())).Returns(false);

            // Act
            var (status, trayService) = await _startupLogic.ExecuteAsync(new string[0], (s, e) => {}, (s, e) => {});

            // Assert
            Assert.That(status, Is.EqualTo(StartupStatus.ShutdownRequested));
            Assert.That(trayService, Is.Null);
            
            // Vérifier que les étapes suivantes n'ont pas été exécutées
            _mockOrchestrator.Verify(o => o.StartApplication(
                It.IsAny<IServiceProvider>(),
                It.IsAny<EventHandler>(),
                It.IsAny<EventHandler>()), Times.Never);
        }
        
        [Test]
        [Description("Échec de l'orchestration : StartApplication retourne null, CriticalError")]
        public async Task ExecuteAsync_OrchestratorFails_ReturnsCriticalError()
        {
            // Arrange
            _mockOrchestrator.Setup(o => o.StartApplication(
                It.IsAny<IServiceProvider>(),
                It.IsAny<EventHandler>(),
                It.IsAny<EventHandler>()))
                .ReturnsAsync((ISystemTrayService?)null);

            // Act
            var (status, trayService) = await _startupLogic.ExecuteAsync(new string[0], (s, e) => {}, (s, e) => {});

            // Assert
            Assert.That(status, Is.EqualTo(StartupStatus.CriticalError));
            Assert.That(trayService, Is.Null);
            
            // Vérifier que l'étape de finalisation n'a pas été exécutée
            _mockSingleInstance.Verify(s => s.RegisterWindowMessageHandler(null), Times.Never);
        }
        
        [Test]
        [Description("Orchestration réussie avec arguments de ligne de commande")]
        public async Task ExecuteAsync_WithCommandLineArgs_ProcessesArgsCorrectly()
        {
            // Arrange
            string[] args = new[] { "--test", "--param=value" };
            
            // Act
            var (status, trayService) = await _startupLogic.ExecuteAsync(args, (s, e) => {}, (s, e) => {});
            
            // Assert
            Assert.That(status, Is.EqualTo(StartupStatus.Success));
            _mockCommandLine.Verify(c => c.ProcessCommandLineArguments(args, It.IsAny<IServiceProvider>()), Times.Once);
        }
    }
} 