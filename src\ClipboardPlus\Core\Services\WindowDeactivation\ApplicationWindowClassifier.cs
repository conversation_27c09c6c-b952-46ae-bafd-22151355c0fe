using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Windows;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Implémentation du service de classification des fenêtres de l'application.
    /// </summary>
    public class ApplicationWindowClassifier : IApplicationWindowClassifier
    {
        private readonly ILoggingService _loggingService;
        private readonly Dictionary<Type, string> _registeredWindowTypes;
        private readonly WindowClassificationConfig _config;

        /// <summary>
        /// Initialise une nouvelle instance du classificateur de fenêtres d'application.
        /// </summary>
        /// <param name="loggingService">Service de journalisation</param>
        /// <param name="config">Configuration de classification (optionnel)</param>
        public ApplicationWindowClassifier(ILoggingService loggingService, WindowClassificationConfig? config = null)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _config = config ?? new WindowClassificationConfig();
            _registeredWindowTypes = new Dictionary<Type, string>();

            // Enregistrer les types par défaut
            InitializeDefaultWindowTypes();
        }

        /// <inheritdoc />
        public bool IsApplicationWindow(Window window, Window referenceWindow)
        {
            var result = ClassifyWindow(window, referenceWindow);
            return result.IsApplicationWindow;
        }

        /// <inheritdoc />
        public WindowClassificationResult ClassifyWindow(Window window, Window referenceWindow)
        {
            if (window == null)
            {
                throw new ArgumentNullException(nameof(window));
            }

            if (referenceWindow == null)
            {
                throw new ArgumentNullException(nameof(referenceWindow));
            }

            var stopwatch = Stopwatch.StartNew();
            var result = new WindowClassificationResult
            {
                WindowTypeName = window.GetType().Name,
                WindowTypeFullName = window.GetType().FullName ?? window.GetType().Name,
                Timestamp = DateTime.Now
            };

            try
            {
                if (_config.EnableDetailedLogging)
                {
                    _loggingService.LogInfo($"🔍 [ApplicationWindowClassifier] Analyse de la fenêtre active: {result.WindowTypeFullName}");
                }

                // Test 1: Même propriétaire
                bool sameOwner = window.Owner == referenceWindow.Owner;
                result.ClassificationTests["sameOwner"] = sameOwner;

                // Test 2: La fenêtre de référence est le propriétaire
                bool referenceIsOwner = window.Owner == referenceWindow;
                result.ClassificationTests["referenceIsOwner"] = referenceIsOwner;

                // Test 3: Types de fenêtres spécifiques de l'application
                bool isSettings = IsWindowOfType(window, "AppSettingsWindow");
                result.ClassificationTests["isSettings"] = isSettings;

                bool isCleanup = IsWindowOfType(window, "AdvancedCleanupWindow");
                result.ClassificationTests["isCleanup"] = isCleanup;

                bool isNewItem = IsWindowOfType(window, "NewItemEditorDialog");
                result.ClassificationTests["isNewItem"] = isNewItem;

                bool isPreview = IsWindowOfType(window, "ItemContentPreviewWindow");
                result.ClassificationTests["isPreview"] = isPreview;

                // Test 4: Types personnalisés enregistrés
                bool isCustomRegistered = _registeredWindowTypes.ContainsKey(window.GetType());
                result.ClassificationTests["isCustomRegistered"] = isCustomRegistered;

                if (_config.EnableDetailedLogging)
                {
                    foreach (var test in result.ClassificationTests)
                    {
                        _loggingService.LogInfo($"   {test.Key}: {test.Value}");
                    }
                }

                // Déterminer le résultat final
                result.IsApplicationWindow = sameOwner || referenceIsOwner || isSettings || isCleanup || isNewItem || isPreview || isCustomRegistered;

                // Déterminer le type de relation
                if (sameOwner)
                {
                    result.RelationType = WindowRelationType.SameOwner;
                    result.Reason = "Même propriétaire que la fenêtre de référence";
                }
                else if (referenceIsOwner)
                {
                    result.RelationType = WindowRelationType.ReferenceIsOwner;
                    result.Reason = "La fenêtre de référence est le propriétaire";
                }
                else if (isSettings)
                {
                    result.RelationType = WindowRelationType.SettingsWindow;
                    result.Reason = "Fenêtre de paramètres de l'application";
                    result.FriendlyName = "Paramètres";
                }
                else if (isCleanup)
                {
                    result.RelationType = WindowRelationType.CleanupWindow;
                    result.Reason = "Fenêtre de nettoyage avancé";
                    result.FriendlyName = "Nettoyage avancé";
                }
                else if (isNewItem)
                {
                    result.RelationType = WindowRelationType.NewItemDialog;
                    result.Reason = "Dialogue de création d'élément";
                    result.FriendlyName = "Éditeur d'élément";
                }
                else if (isPreview)
                {
                    result.RelationType = WindowRelationType.PreviewWindow;
                    result.Reason = "Fenêtre de prévisualisation";
                    result.FriendlyName = "Prévisualisation";
                }
                else if (isCustomRegistered)
                {
                    result.RelationType = WindowRelationType.CustomRegistered;
                    result.Reason = "Type de fenêtre personnalisé enregistré";
                    result.FriendlyName = _registeredWindowTypes[window.GetType()];
                }
                else
                {
                    result.RelationType = WindowRelationType.External;
                    result.Reason = "Fenêtre externe à l'application";
                }

                stopwatch.Stop();
                result.ClassificationDurationMs = stopwatch.Elapsed.TotalMilliseconds;

                if (_config.EnableDetailedLogging)
                {
                    _loggingService.LogInfo($"🎯 [ApplicationWindowClassifier] isOurAppWindow: {result.IsApplicationWindow} ({result.Reason})");
                }

                if (_config.EnablePerformanceMetrics)
                {
                    _loggingService.LogInfo($"⏱️ [ApplicationWindowClassifier] Classification terminée en {result.ClassificationDurationMs:F1}ms");
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.ClassificationDurationMs = stopwatch.Elapsed.TotalMilliseconds;
                result.IsApplicationWindow = false; // En cas d'erreur, considérer comme externe par sécurité
                result.RelationType = WindowRelationType.External;
                result.Reason = $"Erreur de classification: {ex.Message}";
                result.Details = ex.ToString();

                _loggingService.LogError($"❌ [ApplicationWindowClassifier] Erreur lors de la classification: {ex.Message}", ex);
                return result;
            }
        }





        /// <summary>
        /// Vérifie si une fenêtre est d'un type spécifique en utilisant le nom du type.
        /// </summary>
        /// <param name="window">Fenêtre à vérifier</param>
        /// <param name="typeName">Nom du type à rechercher</param>
        /// <returns>True si la fenêtre est du type spécifié</returns>
        private bool IsWindowOfType(Window window, string typeName)
        {
            return window.GetType().Name.Contains(typeName, StringComparison.OrdinalIgnoreCase) ||
                   window.GetType().FullName?.Contains(typeName, StringComparison.OrdinalIgnoreCase) == true;
        }

        /// <summary>
        /// Initialise les types de fenêtres par défaut.
        /// </summary>
        private void InitializeDefaultWindowTypes()
        {
            // Les types par défaut sont gérés par la logique IsWindowOfType
            // Cette méthode peut être étendue pour enregistrer des types spécifiques si nécessaire
            _loggingService.LogInfo("📝 [ApplicationWindowClassifier] Types de fenêtres par défaut initialisés");
        }
    }
}
