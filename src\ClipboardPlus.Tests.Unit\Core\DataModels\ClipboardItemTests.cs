using System;
using System.ComponentModel;
using System.Text;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using System.Windows.Media.Imaging;

namespace ClipboardPlus.Tests.Unit.Core.DataModels
{
    [TestFixture]
    public class ClipboardItemTests
    {
        [Test]
        public void Constructor_InitializesDefaultValues()
        {
            // Act
            var item = new ClipboardItem();

            // Assert
            Assert.That(item.Id, Is.EqualTo(0));
            Assert.That(item.DataType, Is.EqualTo(ClipboardDataType.Text));
            Assert.That(item.IsPinned, Is.False);
            Assert.That(item.OrderIndex, Is.EqualTo(0));
            Assert.That(item.CustomName, Is.Null);
            Assert.That(item.RawData, Is.Null);
            Assert.That(item.TextPreview, Is.Null);
            Assert.That(item.Timestamp, Is.Not.EqualTo(DateTime.MinValue));
        }

        [Test]
        public void IsPinned_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var item = new ClipboardItem();
            bool propertyChangedRaised = false;
            string? propertyName = null;

            item.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };

            // Act
            item.IsPinned = true;

            // Assert
            Assert.That(propertyChangedRaised, Is.True);
            Assert.That(propertyName, Is.EqualTo(nameof(ClipboardItem.IsPinned)));
            Assert.That(item.IsPinned, Is.True);
        }

        [Test]
        public void CustomName_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var item = new ClipboardItem();
            bool propertyChangedRaised = false;
            string? propertyName = null;

            item.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };

            // Act
            item.CustomName = "Nom de test";

            // Assert
            Assert.That(propertyChangedRaised, Is.True);
            Assert.That(propertyName, Is.EqualTo(nameof(ClipboardItem.CustomName)));
            Assert.That(item.CustomName, Is.EqualTo("Nom de test"));
        }

        [Test]
        public void OrderIndex_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var item = new ClipboardItem();
            bool propertyChangedRaised = false;
            string? propertyName = null;

            item.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };

            // Act
            item.OrderIndex = 5;

            // Assert
            Assert.That(propertyChangedRaised, Is.True);
            Assert.That(propertyName, Is.EqualTo(nameof(ClipboardItem.OrderIndex)));
            Assert.That(item.OrderIndex, Is.EqualTo(5));
        }

        [Test]
        public void Id_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var item = new ClipboardItem();
            bool propertyChangedRaised = false;
            string? propertyName = null;

            item.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };

            // Act
            item.Id = 123;

            // Assert
            Assert.That(propertyChangedRaised, Is.True);
            Assert.That(propertyName, Is.EqualTo(nameof(ClipboardItem.Id)));
            Assert.That(item.Id, Is.EqualTo(123));
        }

        [Test]
        public void Timestamp_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var item = new ClipboardItem();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            DateTime newTimestamp = DateTime.Now.AddDays(1);

            item.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };

            // Act
            item.Timestamp = newTimestamp;

            // Assert
            Assert.That(propertyChangedRaised, Is.True);
            Assert.That(propertyName, Is.EqualTo(nameof(ClipboardItem.Timestamp)));
            Assert.That(item.Timestamp, Is.EqualTo(newTimestamp));
        }

        [Test]
        public void DataType_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var item = new ClipboardItem();
            bool propertyChangedRaised = false;
            string? propertyName = null;

            item.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };

            // Act
            item.DataType = ClipboardDataType.Image;

            // Assert
            Assert.That(propertyChangedRaised, Is.True);
            Assert.That(propertyName, Is.EqualTo(nameof(ClipboardItem.DataType)));
            Assert.That(item.DataType, Is.EqualTo(ClipboardDataType.Image));
        }

        [Test]
        public void RawData_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var item = new ClipboardItem();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            byte[] newData = new byte[] { 1, 2, 3 };

            item.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };

            // Act
            item.RawData = newData;

            // Assert
            Assert.That(propertyChangedRaised, Is.True);
            Assert.That(propertyName, Is.EqualTo(nameof(ClipboardItem.RawData)));
            Assert.That(item.RawData, Is.SameAs(newData));
        }

        [Test]
        public void SourceApplication_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var item = new ClipboardItem();
            bool propertyChangedRaised = false;
            string? propertyName = null;

            item.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };

            // Act
            item.SourceApplication = "TestApp";

            // Assert
            Assert.That(propertyChangedRaised, Is.True);
            Assert.That(propertyName, Is.EqualTo(nameof(ClipboardItem.SourceApplication)));
            Assert.That(item.SourceApplication, Is.EqualTo("TestApp"));
        }

        [Test]
        public void TextPreview_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var item = new ClipboardItem();
            bool propertyChangedRaised = false;
            string? propertyName = null;

            item.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };

            // Act
            item.TextPreview = "Nouveau preview";

            // Assert
            Assert.That(propertyChangedRaised, Is.True);
            Assert.That(propertyName, Is.EqualTo(nameof(ClipboardItem.TextPreview)));
            Assert.That(item.TextPreview, Is.EqualTo("Nouveau preview"));
        }

        [Test]
        public void ThumbnailSource_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var item = new ClipboardItem();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            // Créer un BitmapSource factice simple pour le test.
            // Un dummy BitmapImage 1x1 pixel transparent.
            var newThumbnail = BitmapSource.Create(1, 1, 96, 96, System.Windows.Media.PixelFormats.Pbgra32, null, new byte[4], 4);

            item.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };

            // Act
            item.ThumbnailSource = newThumbnail;

            // Assert
            Assert.That(propertyChangedRaised, Is.True);
            Assert.That(propertyName, Is.EqualTo(nameof(ClipboardItem.ThumbnailSource)));
            Assert.That(item.ThumbnailSource, Is.SameAs(newThumbnail));
        }

        [Test]
        public void ToString_WithCustomName_ReturnsFormattedString()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 42,
                CustomName = "Nom personnalisé",
                DataType = ClipboardDataType.Text,
                Timestamp = new DateTime(2023, 1, 1, 12, 0, 0)
            };

            // Act
            string result = item.ToString();

            // Assert
            Assert.That(result, Does.Contain("Nom personnalisé"));
            Assert.That(result, Does.Contain("Text"));
            Assert.That(result, Does.Contain("01/01/2023"));
        }

        [Test]
        public void ToString_WithoutCustomName_ReturnsFormattedStringWithId()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 42,
                CustomName = null,
                DataType = ClipboardDataType.Image,
                Timestamp = new DateTime(2023, 1, 1, 12, 0, 0)
            };

            // Act
            string result = item.ToString();

            // Assert
            Assert.That(result, Does.Contain("Item 42"));
            Assert.That(result, Does.Contain("Image"));
            Assert.That(result, Does.Contain("01/01/2023"));
        }
    }
} 