using System;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Visibility
{
    /// <summary>
    /// Contrat pour la gestion centralisée des états de visibilité
    /// Respecte le Single Responsibility Principle : une seule responsabilité - gérer la visibilité
    /// Respecte le Dependency Inversion Principle : interface abstraite pour découplage
    /// </summary>
    public interface IVisibilityStateManager
    {
        /// <summary>
        /// Événement déclenché lors d'un changement d'état de visibilité
        /// Permet une notification découplée des changements
        /// </summary>
        event EventHandler<VisibilityChangedEventArgs> VisibilityChanged;

        /// <summary>
        /// Détermine si le titre d'un élément spécifique doit être affiché
        /// </summary>
        /// <param name="item">Élément du presse-papiers à évaluer</param>
        /// <returns>True si le titre doit être visible, False sinon</returns>
        bool ShouldShowTitle(ClipboardItem item);

        /// <summary>
        /// Détermine si l'horodatage d'un élément spécifique doit être affiché
        /// </summary>
        /// <param name="item">Élément du presse-papiers à évaluer</param>
        /// <returns>True si l'horodatage doit être visible, False sinon</returns>
        bool ShouldShowTimestamp(ClipboardItem item);

        /// <summary>
        /// Met à jour la visibilité globale des titres
        /// Déclenche l'événement VisibilityChanged si nécessaire
        /// </summary>
        /// <param name="isVisible">Nouvel état de visibilité pour les titres</param>
        void UpdateGlobalTitleVisibility(bool isVisible);

        /// <summary>
        /// Met à jour la visibilité globale des horodatages
        /// Déclenche l'événement VisibilityChanged si nécessaire
        /// </summary>
        /// <param name="isVisible">Nouvel état de visibilité pour les horodatages</param>
        void UpdateGlobalTimestampVisibility(bool isVisible);

        /// <summary>
        /// Obtient l'état actuel de la visibilité globale des titres
        /// </summary>
        bool GlobalTitleVisibility { get; }

        /// <summary>
        /// Obtient l'état actuel de la visibilité globale des horodatages
        /// </summary>
        bool GlobalTimestampVisibility { get; }

        /// <summary>
        /// Met à jour la visibilité des titres à partir des paramètres
        /// CORRECTION CRITIQUE : Synchronisation avec SettingsManager
        /// </summary>
        /// <param name="hideItemTitle">True pour masquer les titres</param>
        void UpdateTitleVisibilityFromSettings(bool hideItemTitle);

        /// <summary>
        /// Met à jour la visibilité des horodatages à partir des paramètres
        /// CORRECTION CRITIQUE : Synchronisation avec SettingsManager
        /// </summary>
        /// <param name="hideTimestamp">True pour masquer les horodatages</param>
        void UpdateTimestampVisibilityFromSettings(bool hideTimestamp);
    }
}
