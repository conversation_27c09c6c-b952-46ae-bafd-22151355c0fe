using System;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface d'abstraction pour l'API Windows de gestion des raccourcis globaux.
    /// Permet l'injection de dépendance et les tests unitaires.
    /// </summary>
    public interface IWindowsHotkeyApi
    {
        /// <summary>
        /// Enregistre un raccourci clavier global avec Windows.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre qui recevra les messages</param>
        /// <param name="id">Identifiant unique du raccourci</param>
        /// <param name="modifiers">Modificateurs (Ctrl, Alt, Shift, Win)</param>
        /// <param name="vk">Code de la touche virtuelle</param>
        /// <returns>True si l'enregistrement réussit, False sinon</returns>
        bool RegisterHotKey(IntPtr hWnd, int id, uint modifiers, uint vk);

        /// <summary>
        /// Désenregistre un raccourci clavier global.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre</param>
        /// <param name="id">Identifiant du raccourci à désenregistrer</param>
        /// <returns>True si le désenregistrement réussit, False sinon</returns>
        bool UnregisterHotKey(IntPtr hWnd, int id);
    }
}
