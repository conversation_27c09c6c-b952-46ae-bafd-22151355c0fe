using System;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Shortcuts.Models
{
    /// <summary>
    /// Résultat de l'initialisation des raccourcis globaux.
    /// Encapsule le succès/échec et les métadonnées associées.
    /// </summary>
    public class InitializationResult
    {
        /// <summary>
        /// Initialise une nouvelle instance de InitializationResult pour un succès.
        /// </summary>
        /// <param name="registeredShortcut">Le raccourci qui a été enregistré.</param>
        /// <param name="windowHandle">Le handle de fenêtre utilisé.</param>
        /// <param name="strategy">La stratégie utilisée pour l'initialisation.</param>
        public InitializationResult(KeyCombination registeredShortcut, IntPtr windowHandle, string strategy)
        {
            IsSuccess = true;
            RegisteredShortcut = registeredShortcut;
            WindowHandle = windowHandle;
            Strategy = strategy;
            ErrorMessage = null;
            Exception = null;
        }

        /// <summary>
        /// Initialise une nouvelle instance de InitializationResult pour un échec.
        /// </summary>
        /// <param name="errorMessage">Le message d'erreur.</param>
        /// <param name="exception">L'exception qui a causé l'échec (optionnel).</param>
        public InitializationResult(string errorMessage, Exception? exception = null)
        {
            IsSuccess = false;
            RegisteredShortcut = null;
            WindowHandle = IntPtr.Zero;
            Strategy = "Failed";
            ErrorMessage = errorMessage;
            Exception = exception;
        }

        /// <summary>
        /// Indique si l'initialisation a réussi.
        /// </summary>
        public bool IsSuccess { get; }

        /// <summary>
        /// Le raccourci qui a été enregistré (null en cas d'échec).
        /// </summary>
        public KeyCombination? RegisteredShortcut { get; }

        /// <summary>
        /// Le handle de fenêtre utilisé pour l'enregistrement.
        /// </summary>
        public IntPtr WindowHandle { get; }

        /// <summary>
        /// La stratégie utilisée pour l'initialisation.
        /// </summary>
        public string Strategy { get; }

        /// <summary>
        /// Le message d'erreur en cas d'échec.
        /// </summary>
        public string? ErrorMessage { get; }

        /// <summary>
        /// L'exception qui a causé l'échec (optionnel).
        /// </summary>
        public Exception? Exception { get; }

        /// <summary>
        /// Timestamp de l'initialisation.
        /// </summary>
        public DateTime Timestamp { get; } = DateTime.UtcNow;

        /// <summary>
        /// Crée un résultat de succès.
        /// </summary>
        public static InitializationResult Success(KeyCombination shortcut, IntPtr handle, string strategy)
            => new(shortcut, handle, strategy);

        /// <summary>
        /// Crée un résultat d'échec.
        /// </summary>
        public static InitializationResult Failure(string errorMessage, Exception? exception = null)
            => new(errorMessage, exception);
    }
}
