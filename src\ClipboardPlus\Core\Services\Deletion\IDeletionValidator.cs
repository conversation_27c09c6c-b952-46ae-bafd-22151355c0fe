using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Interface pour la validation des paramètres de suppression d'éléments
    /// Responsabilité : Valider les paramètres d'entrée avant traitement
    /// </summary>
    public interface IDeletionValidator
    {
        /// <summary>
        /// Valide si l'ID fourni est valide pour une opération de suppression
        /// </summary>
        /// <param name="id">L'ID de l'élément à valider</param>
        /// <returns>True si l'ID est valide, False sinon</returns>
        bool IsValidId(long id);

        /// <summary>
        /// Valide les paramètres d'une requête de suppression
        /// </summary>
        /// <param name="id">L'ID de l'élément à supprimer</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Résultat de la validation avec détails</returns>
        Task<DeletionValidationResult> ValidateRequestAsync(long id, string operationId);
    }

    /// <summary>
    /// Résultat de la validation d'une requête de suppression
    /// </summary>
    public class DeletionValidationResult
    {
        /// <summary>
        /// Indique si la validation a réussi
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Message d'erreur en cas d'échec de validation
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Code d'erreur spécifique
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// Crée un résultat de validation réussie
        /// </summary>
        public static DeletionValidationResult CreateSuccess() => new() { IsValid = true };

        /// <summary>
        /// Crée un résultat de validation échouée
        /// </summary>
        public static DeletionValidationResult CreateFailure(string errorMessage, string? errorCode = null) =>
            new() { IsValid = false, ErrorMessage = errorMessage, ErrorCode = errorCode };
    }
}
