using System;
using System.Windows;
using System.IO;

namespace ClipboardPlus.Tests.Unit.TestHelpers
{
    /// <summary>
    /// Initialise les ressources nécessaires pour les tests d'interface utilisateur
    /// </summary>
    public static class TestResourceInitializer
    {
        private static bool _isInitialized = false;
        private static readonly object _lock = new object();

        /// <summary>
        /// Initialise les ressources pour les tests
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized)
                return;
                
            lock (_lock)
            {
                if (_isInitialized)
                    return;
                    
                try
                {
                    var resourceDictionary = new ResourceDictionary
                    {
                        Source = new Uri("/ClipboardPlus.Tests.Unit;component/TestResources/TestResourceDictionary.xaml", UriKind.Relative)
                    };
                    
                    Application.Current.Resources.MergedDictionaries.Add(resourceDictionary);
                    _isInitialized = true;
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Impossible d'initialiser les ressources de test: {ex.Message}", ex);
                }
            }
        }
    }
} 