using System.Windows;
using System.Linq;
using WpfApplication = System.Windows.Application;
using WpfMessageBox = System.Windows.MessageBox;

namespace ClipboardPlus.Core.Services;

/// <summary>
/// Implémentation de <see cref="IUserNotificationService"/> utilisant WPF MessageBox.
/// </summary>
public class WpfUserNotificationService : IUserNotificationService
{
    public void ShowError(string title, string message)
    {
        // Obtenir la fenêtre d'historique comme Owner pour maintenir l'activité
        var historyWindow = WpfApplication.Current.Windows.OfType<ClipboardPlus.UI.Windows.ClipboardHistoryWindow>().FirstOrDefault();
        if (historyWindow != null)
        {
            WpfMessageBox.Show(historyWindow, message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }
        else
        {
            WpfMessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    public void ShowInformation(string title, string message)
    {
        // Obtenir la fenêtre d'historique comme Owner pour maintenir l'activité
        var historyWindow = WpfApplication.Current.Windows.OfType<ClipboardPlus.UI.Windows.ClipboardHistoryWindow>().FirstOrDefault();
        if (historyWindow != null)
        {
            WpfMessageBox.Show(historyWindow, message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }
        else
        {
            WpfMessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}