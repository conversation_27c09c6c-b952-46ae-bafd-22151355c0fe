using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface définissant les opérations de gestion des raccourcis clavier globaux.
    /// </summary>
    public interface IGlobalShortcutService
    {
        /// <summary>
        /// Événement déclenché lorsque le raccourci global est activé.
        /// </summary>
        event EventHandler ShortcutActivated;

        /// <summary>
        /// Récupère le raccourci clavier actuellement enregistré.
        /// </summary>
        /// <returns>Le raccourci clavier actuellement enregistré.</returns>
        KeyCombination GetCurrentRegisteredShortcut();

        /// <summary>
        /// Désenregistre le raccourci clavier actuel.
        /// </summary>
        void UnregisterShortcut();

        /// <summary>
        /// Tente d'enregistrer un nouveau raccourci clavier.
        /// </summary>
        /// <param name="keyCombination">La combinaison de touches à enregistrer.</param>
        /// <returns>True si l'enregistrement a réussi, False sinon.</returns>
        Task<bool> TryRegisterShortcutAsync(KeyCombination? keyCombination);

        /// <summary>
        /// Valide si une combinaison de touches est syntaxiquement correcte.
        /// </summary>
        /// <param name="keyCombination">La combinaison de touches à valider.</param>
        /// <returns>True si la combinaison est valide.</returns>
        bool IsValidShortcut(KeyCombination keyCombination);

        /// <summary>
        /// Vérifie si une combinaison de touches est déjà enregistrée par une autre application.
        /// </summary>
        /// <param name="keyCombination">La combinaison de touches à vérifier.</param>
        /// <returns>True si la combinaison est déjà enregistrée.</returns>
        bool IsShortcutAlreadyRegistered(KeyCombination keyCombination);

        /// <summary>
        /// Initialise le service avec le raccourci par défaut.
        /// </summary>
        /// <param name="defaultShortcut">Raccourci par défaut à utiliser.</param>
        /// <returns>Tâche asynchrone.</returns>
        Task InitializeAsync(KeyCombination? defaultShortcut);

        /// <summary>
        /// Traite un message de raccourci clavier et détermine s'il doit être géré.
        /// Cette méthode est publique pour permettre les tests.
        /// </summary>
        /// <param name="messageType">Type de message Windows</param>
        /// <param name="hotkeyId">ID du raccourci dans le message</param>
        /// <returns>True si le message a été géré, False sinon</returns>
        bool ProcessHotkeyMessage(int messageType, int hotkeyId);
    }
}