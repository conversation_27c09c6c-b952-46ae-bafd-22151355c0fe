using System;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services.Diagnostics;
using ClipboardPlus.UI.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.Diagnostics
{
    [TestFixture]
    public class DiagnosticDataCollectorTests
    {
        private DiagnosticDataCollector _collector = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private ClipboardHistoryViewModel _testViewModel = null!;
        private IServiceProvider _serviceProvider = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _collector = new DiagnosticDataCollector(_mockLoggingService.Object);

            // Créer un vrai ViewModel pour les tests
            _serviceProvider = ClipboardPlus.Services.Configuration.HostConfiguration.ConfigureServices();
            _testViewModel = _serviceProvider.GetRequiredService<ClipboardHistoryViewModel>();
        }

        [TearDown]
        public void TearDown()
        {
            if (_serviceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }

        [Test]
        public void CollectItemData_WithNullItem_ReturnsDataWithNullFlag()
        {
            // Act
            var result = _collector.CollectItemData(null);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Properties.ContainsKey("IsNull"), Is.True);
            Assert.That(result.Properties["IsNull"], Is.EqualTo(true));
            Assert.That(result.Warnings, Contains.Item("L'élément à supprimer est null"));
        }

        [Test]
        public void CollectItemData_WithValidItem_ReturnsCompleteData()
        {
            // Arrange
            var testItem = new ClipboardItem
            {
                Id = 123,
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.Now,
                IsPinned = true,
                IsTitleVisible = false,
                CustomName = "Test Item",
                TextPreview = "Test content for unit test"
            };

            // Act
            var result = _collector.CollectItemData(testItem);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Properties["Id"], Is.EqualTo(123));
            Assert.That(result.Properties["DataType"], Is.EqualTo("Text"));
            Assert.That(result.Properties["IsPinned"], Is.EqualTo(true));
            Assert.That(result.Properties["IsTitleVisible"], Is.EqualTo(false));
            Assert.That(result.Properties["CustomName"], Is.EqualTo("Test Item"));
            Assert.That(result.Properties["TextPreview"], Does.Contain("Test content"));
        }

        [Test]
        public void CollectItemData_WithInvalidId_AddsWarning()
        {
            // Arrange
            var testItem = new ClipboardItem
            {
                Id = -1, // ID invalide
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.Now
            };

            // Act
            var result = _collector.CollectItemData(testItem);

            // Assert
            Assert.That(result.Warnings, Contains.Item("ID invalide: -1"));
        }

        [Test]
        public void CollectViewModelData_WithNullViewModel_ReturnsErrorData()
        {
            // Act
            var result = _collector.CollectViewModelData(null);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Errors, Contains.Item("ViewModel est null"));
        }

        [Test]
        public void CollectViewModelData_WithValidViewModel_ReturnsCompleteData()
        {
            // Act
            var result = _collector.CollectViewModelData(_testViewModel);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Properties.ContainsKey("HistoryItemsCount"), Is.True);
            Assert.That(result.Properties.ContainsKey("HasSelectedItem"), Is.True);
            Assert.That(result.Properties.ContainsKey("IsLoading"), Is.True);
            Assert.That(result.Properties.ContainsKey("HasDeleteCommand"), Is.True);
            Assert.That(result.Properties.ContainsKey("HasClearAllCommand"), Is.True);
            Assert.That(result.Properties.ContainsKey("HasPasteCommand"), Is.True);
        }

        [Test]
        public void CollectSystemData_ReturnsSystemInformation()
        {
            // Act
            var result = _collector.CollectSystemData();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Properties.ContainsKey("ThreadId"), Is.True);
            Assert.That(result.Properties.ContainsKey("Timestamp"), Is.True);
            Assert.That(result.Properties.ContainsKey("ApplicationVersion"), Is.True);
            Assert.That(result.Properties.ContainsKey("WorkingSet"), Is.True);
            Assert.That(result.Properties.ContainsKey("ProcessorCount"), Is.True);
            Assert.That(result.Properties.ContainsKey("OSVersion"), Is.True);
            Assert.That(result.Properties.ContainsKey("Is64BitProcess"), Is.True);
        }

        [Test]
        public void CollectSystemData_WithHighMemoryUsage_AddsWarning()
        {
            // Act
            var result = _collector.CollectSystemData();

            // Assert - Vérifier que si la mémoire est élevée, un avertissement est ajouté
            var workingSet = (long)result.Properties["WorkingSet"];
            if (workingSet > 500 * 1024 * 1024) // 500MB
            {
                Assert.That(result.Warnings, Has.Some.Matches<string>(w => w.Contains("Utilisation mémoire élevée")));
            }
        }

        [Test]
        public void Constructor_WithNullLoggingService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new DiagnosticDataCollector(null!));
        }
    }
}
