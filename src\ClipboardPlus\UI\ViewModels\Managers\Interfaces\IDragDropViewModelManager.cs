// ============================================================================
// INTERFACE DRAG DROP VIEWMODEL MANAGER - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Interface pour la gestion du drag & drop dans le ViewModel
// 📋 RESPONSABILITÉ : Gestion IDropTarget et opérations de glisser-déposer
// 🏗️ ARCHITECTURE : Extraction de DragDrop.cs (159 lignes)
//
// ============================================================================

using System;
using System.Windows;
using ClipboardPlus.Core.DataModels;
using WinFormsDataObject = System.Windows.Forms.IDataObject;
using WpfDataObject = System.Windows.IDataObject;
using WinFormsDragDropEffects = System.Windows.Forms.DragDropEffects;
using WpfDragDropEffects = System.Windows.DragDropEffects;

namespace ClipboardPlus.UI.ViewModels.Managers.Interfaces
{
    /// <summary>
    /// Interface pour le manager de gestion du drag & drop dans le ViewModel.
    /// 
    /// Ce manager est responsable de :
    /// - L'implémentation de IDropTarget
    /// - La gestion des opérations de glisser-déposer
    /// - La validation des données déposées
    /// - L'intégration avec l'historique
    /// </summary>
    public interface IDragDropViewModelManager : IDisposable
    {
        #region Propriétés d'État

        /// <summary>
        /// Indique si une opération de drag & drop est en cours.
        /// </summary>
        bool IsDragDropActive { get; }

        /// <summary>
        /// Type de données actuellement en cours de drag & drop.
        /// </summary>
        ClipboardDataType? CurrentDragDataType { get; }

        /// <summary>
        /// Indique si le drop est autorisé à la position actuelle.
        /// </summary>
        bool IsDropAllowed { get; }

        #endregion

        #region Événements

        /// <summary>
        /// Événement déclenché lorsqu'une opération de drag commence.
        /// </summary>
        event EventHandler<DragStartedEventArgs>? DragStarted;

        /// <summary>
        /// Événement déclenché lorsqu'une opération de drop est effectuée.
        /// </summary>
        event EventHandler<DropCompletedEventArgs>? DropCompleted;

        /// <summary>
        /// Événement déclenché lorsqu'une opération de drag & drop échoue.
        /// </summary>
        event EventHandler<DragDropErrorEventArgs>? DragDropError;

        #endregion

        #region Méthodes IDropTarget

        /// <summary>
        /// Appelée lorsque des données sont glissées au-dessus de la zone de drop.
        /// </summary>
        /// <param name="dropInfo">Informations sur l'opération de drop</param>
        void DragOver(IDropInfo dropInfo);

        /// <summary>
        /// Appelée lorsque des données sont déposées dans la zone de drop.
        /// </summary>
        /// <param name="dropInfo">Informations sur l'opération de drop</param>
        void Drop(IDropInfo dropInfo);

        /// <summary>
        /// Appelée lorsque le drag quitte la zone de drop.
        /// </summary>
        /// <param name="dropInfo">Informations sur l'opération de drop</param>
        void DragLeave(IDropInfo dropInfo);

        #endregion

        #region Méthodes de Validation

        /// <summary>
        /// Valide si les données peuvent être acceptées.
        /// </summary>
        /// <param name="dataObject">Objet de données à valider</param>
        /// <returns>True si les données peuvent être acceptées</returns>
        bool CanAcceptData(System.Windows.IDataObject dataObject);

        /// <summary>
        /// Détermine le type de données contenues dans l'objet de données.
        /// </summary>
        /// <param name="dataObject">Objet de données à analyser</param>
        /// <returns>Type de données détecté</returns>
        ClipboardDataType DetermineDataType(System.Windows.IDataObject dataObject);

        /// <summary>
        /// Valide si une opération de drop est autorisée à la position donnée.
        /// </summary>
        /// <param name="targetIndex">Index de destination</param>
        /// <param name="dataType">Type de données</param>
        /// <returns>True si le drop est autorisé</returns>
        bool IsDropAllowedAt(int targetIndex, ClipboardDataType dataType);

        #endregion

        #region Méthodes de Traitement des Données

        /// <summary>
        /// Extrait les données texte de l'objet de données.
        /// </summary>
        /// <param name="dataObject">Objet de données</param>
        /// <returns>Données texte extraites</returns>
        string? ExtractTextData(System.Windows.IDataObject dataObject);

        /// <summary>
        /// Extrait les données image de l'objet de données.
        /// </summary>
        /// <param name="dataObject">Objet de données</param>
        /// <returns>Données image extraites</returns>
        byte[]? ExtractImageData(System.Windows.IDataObject dataObject);

        /// <summary>
        /// Extrait les chemins de fichiers de l'objet de données.
        /// </summary>
        /// <param name="dataObject">Objet de données</param>
        /// <returns>Chemins de fichiers extraits</returns>
        string[]? ExtractFilePathsData(System.Windows.IDataObject dataObject);

        /// <summary>
        /// Crée un ClipboardItem à partir des données déposées.
        /// </summary>
        /// <param name="dataObject">Objet de données</param>
        /// <param name="dataType">Type de données</param>
        /// <returns>ClipboardItem créé ou null en cas d'échec</returns>
        ClipboardItem? CreateItemFromDroppedData(System.Windows.IDataObject dataObject, ClipboardDataType dataType);

        #endregion

        #region Méthodes d'Intégration

        /// <summary>
        /// Ajoute un élément créé par drag & drop à l'historique.
        /// </summary>
        /// <param name="item">Élément à ajouter</param>
        /// <returns>True si l'ajout a réussi</returns>
        bool AddDroppedItemToHistory(ClipboardItem item);

        /// <summary>
        /// Insère un élément à une position spécifique dans l'historique.
        /// </summary>
        /// <param name="item">Élément à insérer</param>
        /// <param name="index">Index de destination</param>
        /// <returns>True si l'insertion a réussi</returns>
        bool InsertDroppedItemAt(ClipboardItem item, int index);

        #endregion

        #region Méthodes d'Initialisation et Nettoyage

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        void Initialize();

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        void Cleanup();

        #endregion
    }

    #region Interfaces et Classes Auxiliaires

    /// <summary>
    /// Interface pour les informations de drop.
    /// </summary>
    public interface IDropInfo
    {
        System.Windows.IDataObject Data { get; }
        System.Windows.DragDropEffects Effects { get; set; }
        int InsertIndex { get; }
        object? TargetItem { get; }
        object? TargetCollection { get; }
    }

    /// <summary>
    /// Arguments d'événement pour le début d'une opération de drag.
    /// </summary>
    public class DragStartedEventArgs : EventArgs
    {
        public ClipboardDataType DataType { get; }
        public object? SourceData { get; }

        public DragStartedEventArgs(ClipboardDataType dataType, object? sourceData)
        {
            DataType = dataType;
            SourceData = sourceData;
        }
    }

    /// <summary>
    /// Arguments d'événement pour la completion d'une opération de drop.
    /// </summary>
    public class DropCompletedEventArgs : EventArgs
    {
        public ClipboardItem CreatedItem { get; }
        public int InsertIndex { get; }
        public ClipboardDataType DataType { get; }

        public DropCompletedEventArgs(ClipboardItem createdItem, int insertIndex, ClipboardDataType dataType)
        {
            CreatedItem = createdItem;
            InsertIndex = insertIndex;
            DataType = dataType;
        }
    }

    /// <summary>
    /// Arguments d'événement pour une erreur de drag & drop.
    /// </summary>
    public class DragDropErrorEventArgs : EventArgs
    {
        public string Operation { get; }
        public string ErrorMessage { get; }
        public Exception? Exception { get; }

        public DragDropErrorEventArgs(string operation, string errorMessage, Exception? exception = null)
        {
            Operation = operation;
            ErrorMessage = errorMessage;
            Exception = exception;
        }
    }

    #endregion
}
