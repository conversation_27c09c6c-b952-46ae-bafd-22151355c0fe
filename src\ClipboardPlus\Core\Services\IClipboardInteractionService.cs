using System;
using System.Collections.Specialized;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Fournit une abstraction pour interagir avec le presse-papiers du système.
    /// </summary>
    public interface IClipboardInteractionService
    {
        /// <summary>
        /// Récupère le contenu actuel du presse-papiers de manière asynchrone.
        /// </summary>
        /// <returns>Un objet <see cref="ClipboardData"/> contenant les données du presse-papiers, ou null si le presse-papiers est vide ou inaccessible.</returns>
        Task<ClipboardData?> GetClipboardContentAsync();

        /// <summary>
        /// Définit le contenu texte du presse-papiers de manière asynchrone.
        /// </summary>
        /// <param name="text">Le texte à placer dans le presse-papiers.</param>
        /// <returns>True si l'opération a réussi, sinon False.</returns>
        Task<bool> SetClipboardContentAsync(string text);
        
        /// <summary>
        /// Vérifie si le presse-papiers contient du texte.
        /// </summary>
        /// <returns>True si le presse-papiers contient du texte, sinon False.</returns>
        bool ContainsText();
        
        /// <summary>
        /// Récupère le texte du presse-papiers de manière asynchrone.
        /// </summary>
        /// <returns>Le texte contenu dans le presse-papiers, ou null si le presse-papiers ne contient pas de texte.</returns>
        Task<string?> GetTextAsync();
        
        /// <summary>
        /// Vérifie si le presse-papiers contient une image.
        /// </summary>
        /// <returns>True si le presse-papiers contient une image, sinon False.</returns>
        bool ContainsImage();
        
        /// <summary>
        /// Récupère l'image du presse-papiers de manière asynchrone.
        /// </summary>
        /// <returns>L'image contenue dans le presse-papiers, ou null si le presse-papiers ne contient pas d'image.</returns>
        Task<BitmapSource?> GetImageAsync();
        
        /// <summary>
        /// Vérifie si le presse-papiers contient une liste de fichiers.
        /// </summary>
        /// <returns>True si le presse-papiers contient une liste de fichiers, sinon False.</returns>
        bool ContainsFileDropList();
        
        /// <summary>
        /// Récupère la liste des fichiers du presse-papiers de manière asynchrone.
        /// </summary>
        /// <returns>La liste des fichiers contenue dans le presse-papiers, ou null si le presse-papiers ne contient pas de liste de fichiers.</returns>
        Task<StringCollection?> GetFileDropListAsync();
    }
} 