using System;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services.Deletion
{
    [TestFixture]
    public class DeletionUIValidatorTests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private DeletionUIValidator _validator = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _validator = new DeletionUIValidator(_mockLoggingService.Object);
        }

        [Test]
        public void ValidateItem_ValidItem_ReturnsSuccess()
        {
            // Arrange
            var item = new ClipboardItem { Id = 123, TextPreview = "Test item" };

            // Act
            var result = _validator.ValidateItem(item);

            // Assert
            Assert.IsTrue(result.IsValid);
            Assert.IsEmpty(result.ErrorMessage);
            Assert.IsEmpty(result.ErrorCode);
        }

        [Test]
        public void ValidateItem_NullItem_ReturnsFailure()
        {
            // Act
            var result = _validator.ValidateItem(null);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual("L'élément à supprimer ne peut pas être null", result.ErrorMessage);
            Assert.AreEqual("NULL_ITEM", result.ErrorCode);
        }

        [Test]
        public void ValidateItem_InvalidId_ReturnsFailure()
        {
            // Arrange
            var item = new ClipboardItem { Id = 0, TextPreview = "Test item" };

            // Act
            var result = _validator.ValidateItem(item);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual("ID d'élément invalide : 0", result.ErrorMessage);
            Assert.AreEqual("INVALID_ID", result.ErrorCode);
        }

        [Test]
        public void ValidateItem_NegativeId_ReturnsFailure()
        {
            // Arrange
            var item = new ClipboardItem { Id = -1, TextPreview = "Test item" };

            // Act
            var result = _validator.ValidateItem(item);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual("ID d'élément invalide : -1", result.ErrorMessage);
            Assert.AreEqual("INVALID_ID", result.ErrorCode);
        }

        [Test]
        public void ValidateHistoryManager_ValidManager_ReturnsSuccess()
        {
            // Arrange
            var mockManager = new Mock<IClipboardHistoryManager>();

            // Act
            var result = _validator.ValidateHistoryManager(mockManager.Object);

            // Assert
            Assert.IsTrue(result.IsValid);
            Assert.IsEmpty(result.ErrorMessage);
            Assert.IsEmpty(result.ErrorCode);
        }

        [Test]
        public void ValidateHistoryManager_NullManager_ReturnsFailure()
        {
            // Act
            var result = _validator.ValidateHistoryManager(null);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual("Le gestionnaire d'historique n'est pas disponible", result.ErrorMessage);
            Assert.AreEqual("NULL_MANAGER", result.ErrorCode);
        }

        [Test]
        public void ValidateOperationState_NoOperationInProgress_ReturnsSuccess()
        {
            // Act
            var result = _validator.ValidateOperationState(false);

            // Assert
            Assert.IsTrue(result.IsValid);
            Assert.IsEmpty(result.ErrorMessage);
            Assert.IsEmpty(result.ErrorCode);
        }

        [Test]
        public void ValidateOperationState_OperationInProgress_ReturnsFailure()
        {
            // Act
            var result = _validator.ValidateOperationState(true);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual("Une opération est déjà en cours", result.ErrorMessage);
            Assert.AreEqual("OPERATION_IN_PROGRESS", result.ErrorCode);
        }

        [Test]
        public void Constructor_WithoutLoggingService_WorksCorrectly()
        {
            // Arrange & Act
            var validator = new DeletionUIValidator();
            var item = new ClipboardItem { Id = 123, TextPreview = "Test" };

            // Act
            var result = validator.ValidateItem(item);

            // Assert
            Assert.IsTrue(result.IsValid);
        }

        [Test]
        public void ValidateItem_LogsDebugOnSuccess()
        {
            // Arrange
            var item = new ClipboardItem { Id = 123, TextPreview = "Test item" };

            // Act
            _validator.ValidateItem(item);

            // Assert
            _mockLoggingService.Verify(l => l.LogDebug(It.Is<string>(s => s.Contains("Validation réussie pour élément ID=123"))), Times.Once);
        }

        [Test]
        public void ValidateItem_LogsWarningOnFailure()
        {
            // Act
            _validator.ValidateItem(null);

            // Assert
            _mockLoggingService.Verify(l => l.LogWarning(It.Is<string>(s => s.Contains("Validation échouée : élément null"))), Times.Once);
        }

        [Test]
        public void ValidateOperationState_LogsInfoOnFailure()
        {
            // Act
            _validator.ValidateOperationState(true);

            // Assert
            _mockLoggingService.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("Validation échouée : opération déjà en cours"))), Times.Once);
        }

        [Test]
        public void ValidateOperationState_LogsDebugOnSuccess()
        {
            // Act
            _validator.ValidateOperationState(false);

            // Assert
            _mockLoggingService.Verify(l => l.LogDebug(It.Is<string>(s => s.Contains("Validation réussie : aucune opération en cours"))), Times.Once);
        }
    }
}
