// ============================================================================
// INTERFACE ITEM CREATION MANAGER - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Interface pour la gestion de création/renommage d'éléments
// 📋 RESPONSABILITÉ : Gestion des 6 commandes de création/renommage + 4 propriétés
// 🏗️ ARCHITECTURE : Délégation vers CreationModule existant
//
// ============================================================================

using System;
using System.Threading.Tasks;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.UI.ViewModels.Managers.Interfaces
{
    /// <summary>
    /// Interface pour le manager de gestion de création et renommage d'éléments.
    /// 
    /// Ce manager est responsable de :
    /// - La gestion des 6 commandes de création/renommage
    /// - La gestion des 4 propriétés d'état (NewItemTextContent, IsItemCreationActive, etc.)
    /// - La délégation vers le CreationModule
    /// - La gestion des modes de création et renommage
    /// </summary>
    public interface IItemCreationManager : IDisposable
    {
        #region Propriétés d'État

        /// <summary>
        /// Contenu textuel du nouvel élément en cours de création.
        /// </summary>
        string? NewItemTextContent { get; set; }

        /// <summary>
        /// Indique si le mode de création d'un nouvel élément est actif.
        /// </summary>
        bool IsItemCreationActive { get; set; }

        /// <summary>
        /// Élément actuellement en cours de renommage.
        /// </summary>
        ClipboardItem? ItemEnRenommage { get; set; }

        /// <summary>
        /// Nouveau nom pour l'élément en cours de renommage.
        /// </summary>
        string? NouveauNom { get; set; }

        #endregion

        #region Commandes de Création (3 commandes)

        /// <summary>
        /// Commande pour préparer la création d'un nouvel élément.
        /// Active le mode de création et initialise les propriétés.
        /// </summary>
        ICommand PrepareNewItemCommand { get; }

        /// <summary>
        /// Commande pour finaliser et sauvegarder le nouvel élément.
        /// Valide le contenu et ajoute l'élément à l'historique.
        /// </summary>
        ICommand FinalizeAndSaveNewItemCommand { get; }

        /// <summary>
        /// Commande pour annuler la création du nouvel élément.
        /// Nettoie l'état et sort du mode de création.
        /// </summary>
        ICommand DiscardNewItemCreationCommand { get; }

        #endregion

        #region Commandes de Renommage (3 commandes)

        /// <summary>
        /// Commande pour démarrer le renommage d'un élément existant.
        /// </summary>
        ICommand DemarrerRenommageCommand { get; }

        /// <summary>
        /// Commande pour confirmer le renommage et sauvegarder le nouveau nom.
        /// </summary>
        ICommand ConfirmerRenommageCommand { get; }

        /// <summary>
        /// Commande pour annuler le renommage et restaurer l'état précédent.
        /// </summary>
        ICommand AnnulerRenommageCommand { get; }

        #endregion

        #region Propriétés de Validation

        /// <summary>
        /// Indique si le contenu du nouvel élément est valide pour la sauvegarde.
        /// </summary>
        bool IsNewItemContentValid { get; }

        /// <summary>
        /// Indique si le nouveau nom pour le renommage est valide.
        /// </summary>
        bool IsNewNameValid { get; }

        /// <summary>
        /// Indique si un processus de création ou renommage est en cours.
        /// </summary>
        bool IsProcessingActive { get; }

        #endregion

        #region Événements

        /// <summary>
        /// Événement déclenché lorsque le mode de création change.
        /// </summary>
        event EventHandler<bool>? CreationModeChanged;

        /// <summary>
        /// Événement déclenché lorsqu'un nouvel élément est créé avec succès.
        /// </summary>
        event EventHandler<ClipboardItem>? ItemCreated;

        /// <summary>
        /// Événement déclenché lorsque le mode de renommage change.
        /// </summary>
        event EventHandler<ClipboardItem?>? RenamingModeChanged;

        /// <summary>
        /// Événement déclenché lorsqu'un élément est renommé avec succès.
        /// </summary>
        event EventHandler<ItemRenamedEventArgs>? ItemRenamed;

        /// <summary>
        /// Événement déclenché lorsqu'une opération de création/renommage échoue.
        /// </summary>
        event EventHandler<CreationErrorEventArgs>? OperationFailed;

        #endregion

        #region Méthodes de Gestion de Création

        /// <summary>
        /// Prépare la création d'un nouvel élément.
        /// </summary>
        /// <param name="initialContent">Contenu initial optionnel</param>
        void PrepareNewItem(string? initialContent = null);

        /// <summary>
        /// Finalise et sauvegarde le nouvel élément.
        /// </summary>
        /// <returns>L'élément créé ou null en cas d'échec</returns>
        ClipboardItem? FinalizeAndSaveNewItem();

        /// <summary>
        /// Annule la création du nouvel élément et nettoie l'état.
        /// </summary>
        void DiscardNewItemCreation();

        /// <summary>
        /// Valide le contenu du nouvel élément.
        /// </summary>
        /// <param name="content">Contenu à valider</param>
        /// <returns>True si le contenu est valide</returns>
        bool ValidateNewItemContent(string? content);

        #endregion

        #region Méthodes de Gestion de Renommage

        /// <summary>
        /// Démarre le renommage d'un élément existant.
        /// </summary>
        /// <param name="item">Élément à renommer</param>
        void StartRenaming(ClipboardItem item);

        /// <summary>
        /// Confirme le renommage et sauvegarde le nouveau nom.
        /// </summary>
        /// <returns>True si le renommage a réussi</returns>
        Task<bool> ConfirmRenaming();

        /// <summary>
        /// Annule le renommage et restaure l'état précédent.
        /// </summary>
        void CancelRenaming();

        /// <summary>
        /// Valide le nouveau nom pour le renommage.
        /// </summary>
        /// <param name="newName">Nouveau nom à valider</param>
        /// <returns>True si le nom est valide</returns>
        bool ValidateNewName(string? newName);

        #endregion

        #region Méthodes Utilitaires

        /// <summary>
        /// Nettoie tous les états de création et renommage.
        /// </summary>
        void ClearAllStates();

        /// <summary>
        /// Vérifie si un élément peut être renommé.
        /// </summary>
        /// <param name="item">Élément à vérifier</param>
        /// <returns>True si l'élément peut être renommé</returns>
        bool CanRenameItem(ClipboardItem? item);

        /// <summary>
        /// Obtient le nom d'affichage actuel d'un élément.
        /// </summary>
        /// <param name="item">Élément dont on veut le nom</param>
        /// <returns>Nom d'affichage de l'élément</returns>
        string GetDisplayName(ClipboardItem item);

        #endregion

        #region Méthodes d'Initialisation et Nettoyage

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        void Initialize();

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        void Cleanup();

        #endregion
    }

    #region Classes d'Arguments d'Événements

    /// <summary>
    /// Arguments d'événement pour un élément renommé.
    /// </summary>
    public class ItemRenamedEventArgs : EventArgs
    {
        public ClipboardItem Item { get; }
        public string? OldName { get; }
        public string? NewName { get; }

        public ItemRenamedEventArgs(ClipboardItem item, string? oldName, string? newName)
        {
            Item = item;
            OldName = oldName;
            NewName = newName;
        }
    }

    /// <summary>
    /// Arguments d'événement pour une erreur de création/renommage.
    /// </summary>
    public class CreationErrorEventArgs : EventArgs
    {
        public string Operation { get; }
        public string ErrorMessage { get; }
        public Exception? Exception { get; }

        public CreationErrorEventArgs(string operation, string errorMessage, Exception? exception = null)
        {
            Operation = operation;
            ErrorMessage = errorMessage;
            Exception = exception;
        }
    }

    #endregion
}
