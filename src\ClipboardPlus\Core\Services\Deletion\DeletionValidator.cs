using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Implémentation de la validation des paramètres de suppression d'éléments
    /// Responsabilité : Valider les paramètres d'entrée avant traitement
    /// </summary>
    public class DeletionValidator : IDeletionValidator
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Constructeur avec injection de dépendances
        /// </summary>
        /// <param name="loggingService">Service de logging</param>
        public DeletionValidator(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new System.ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Valide si l'ID fourni est valide pour une opération de suppression
        /// </summary>
        /// <param name="id">L'ID de l'élément à valider</param>
        /// <returns>True si l'ID est valide, False sinon</returns>
        public bool IsValidId(long id)
        {
            return id > 0;
        }

        /// <summary>
        /// Valide les paramètres d'une requête de suppression
        /// </summary>
        /// <param name="id">L'ID de l'élément à supprimer</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Résultat de la validation avec détails</returns>
        public async Task<DeletionValidationResult> ValidateRequestAsync(long id, string operationId)
        {
            _loggingService.LogDebug($"[{operationId}] Validation de la requête de suppression pour ID: {id}");

            // Validation de l'ID
            if (id <= 0)
            {
                var errorMessage = $"ID invalide: {id}. L'ID doit être supérieur à 0.";
                _loggingService.LogWarning($"[{operationId}] {errorMessage}");
                return DeletionValidationResult.CreateFailure(errorMessage, "INVALID_ID");
            }

            // Validation de l'operationId
            if (string.IsNullOrWhiteSpace(operationId))
            {
                var errorMessage = "OperationId ne peut pas être null ou vide.";
                _loggingService.LogWarning($"Validation échouée: {errorMessage}");
                return DeletionValidationResult.CreateFailure(errorMessage, "INVALID_OPERATION_ID");
            }

            _loggingService.LogDebug($"[{operationId}] Validation réussie pour ID: {id}");
            return await Task.FromResult(DeletionValidationResult.CreateSuccess());
        }
    }
}
