using System;

namespace ClipboardPlus.Core.Services.Logging
{
    /// <summary>
    /// Factory pour créer des objets LogEntry avec contexte complet.
    /// Responsable de la collecte des informations de contexte (timestamp, thread, etc.).
    /// </summary>
    public interface ILogEntryFactory
    {
        /// <summary>
        /// Crée une entrée de log avec toutes les informations de contexte.
        /// </summary>
        /// <param name="level">Le niveau de log (DEBUG, INFO, AVERTISSEMENT, ERREUR, CRITIQUE, SUPPRESSION)</param>
        /// <param name="message">Le message à logger</param>
        /// <param name="callerInfo">Informations sur l'appelant (fourni par Caller Attributes)</param>
        /// <returns>Une entrée de log complète et structurée</returns>
        LogEntry Create(string level, string message, string callerInfo);
    }
}
