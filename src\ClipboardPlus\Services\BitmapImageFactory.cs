using System;
using System.IO;
using System.Windows.Media.Imaging;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation de la factory pour la création de BitmapImage.
    /// Abstrait la création directe de BitmapImage pour améliorer la testabilité.
    /// </summary>
    public class BitmapImageFactory : IBitmapImageFactory
    {
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de BitmapImageFactory.
        /// </summary>
        /// <param name="loggingService">Service de logging optionnel</param>
        public BitmapImageFactory(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <inheritdoc />
        public BitmapImage? CreateFromBytes(byte[] imageData)
        {
            ArgumentNullException.ThrowIfNull(imageData);
            
            if (imageData.Length == 0)
            {
                throw new ArgumentException("Les données d'image ne peuvent pas être vides.", nameof(imageData));
            }

            try
            {
                _loggingService?.LogInfo($"BitmapImageFactory.CreateFromBytes - Création d'une BitmapImage à partir de {imageData.Length} octets");

                using var stream = new MemoryStream(imageData);
                return CreateFromStream(stream);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"BitmapImageFactory.CreateFromBytes - Erreur lors de la création: {ex.Message}", ex);
                return null;
            }
        }

        /// <inheritdoc />
        public BitmapImage? CreateFromStream(Stream stream)
        {
            ArgumentNullException.ThrowIfNull(stream);

            try
            {
                _loggingService?.LogInfo($"BitmapImageFactory.CreateFromStream - Création d'une BitmapImage à partir d'un stream");

                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.StreamSource = stream;
                bitmap.EndInit();
                bitmap.Freeze(); // Optimisation pour le threading

                _loggingService?.LogInfo($"BitmapImageFactory.CreateFromStream - BitmapImage créée avec succès: {bitmap.PixelWidth}x{bitmap.PixelHeight}");

                return bitmap;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"BitmapImageFactory.CreateFromStream - Erreur lors de la création: {ex.Message}", ex);
                return null;
            }
        }

        /// <inheritdoc />
        public BitmapImage? CreateFromUri(Uri uri)
        {
            ArgumentNullException.ThrowIfNull(uri);

            try
            {
                _loggingService?.LogInfo($"BitmapImageFactory.CreateFromUri - Création d'une BitmapImage à partir de l'URI: {uri}");

                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.UriSource = uri;
                bitmap.EndInit();
                bitmap.Freeze(); // Optimisation pour le threading

                _loggingService?.LogInfo($"BitmapImageFactory.CreateFromUri - BitmapImage créée avec succès: {bitmap.PixelWidth}x{bitmap.PixelHeight}");

                return bitmap;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"BitmapImageFactory.CreateFromUri - Erreur lors de la création: {ex.Message}", ex);
                return null;
            }
        }

        /// <inheritdoc />
        public bool IsValidImageData(byte[]? imageData)
        {
            if (imageData == null || imageData.Length == 0)
            {
                return false;
            }

            try
            {
                // Vérification des signatures de fichiers image courantes
                if (imageData.Length < 4)
                {
                    return false;
                }

                // PNG: 89 50 4E 47
                if (imageData[0] == 0x89 && imageData[1] == 0x50 && imageData[2] == 0x4E && imageData[3] == 0x47)
                {
                    _loggingService?.LogInfo("BitmapImageFactory.IsValidImageData - Format PNG détecté");
                    return true;
                }

                // JPEG: FF D8 FF
                if (imageData[0] == 0xFF && imageData[1] == 0xD8 && imageData[2] == 0xFF)
                {
                    _loggingService?.LogInfo("BitmapImageFactory.IsValidImageData - Format JPEG détecté");
                    return true;
                }

                // GIF: 47 49 46 38 ou 47 49 46 39
                if (imageData[0] == 0x47 && imageData[1] == 0x49 && imageData[2] == 0x46 && 
                    (imageData[3] == 0x38 || imageData[3] == 0x39))
                {
                    _loggingService?.LogInfo("BitmapImageFactory.IsValidImageData - Format GIF détecté");
                    return true;
                }

                // BMP: 42 4D
                if (imageData[0] == 0x42 && imageData[1] == 0x4D)
                {
                    _loggingService?.LogInfo("BitmapImageFactory.IsValidImageData - Format BMP détecté");
                    return true;
                }

                // Tentative de création pour validation finale
                using var stream = new MemoryStream(imageData);
                var bitmap = CreateFromStream(stream);
                bool isValid = bitmap != null;

                _loggingService?.LogInfo($"BitmapImageFactory.IsValidImageData - Validation par création: {isValid}");
                
                return isValid;
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"BitmapImageFactory.IsValidImageData - Données d'image invalides: {ex.Message}");
                return false;
            }
        }

        /// <inheritdoc />
        public (int width, int height)? GetImageDimensions(byte[] imageData)
        {
            ArgumentNullException.ThrowIfNull(imageData);

            try
            {
                _loggingService?.LogInfo($"BitmapImageFactory.GetImageDimensions - Obtention des dimensions pour {imageData.Length} octets");

                var bitmap = CreateFromBytes(imageData);
                if (bitmap == null)
                {
                    return null;
                }

                var dimensions = (bitmap.PixelWidth, bitmap.PixelHeight);
                
                _loggingService?.LogInfo($"BitmapImageFactory.GetImageDimensions - Dimensions: {dimensions.PixelWidth}x{dimensions.PixelHeight}");
                
                return dimensions;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"BitmapImageFactory.GetImageDimensions - Erreur lors de l'obtention des dimensions: {ex.Message}", ex);
                return null;
            }
        }
    }
}
