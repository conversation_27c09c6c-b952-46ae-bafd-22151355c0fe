#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.

using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.History
{
    /// <summary>
    /// Tests unitaires pour HistoryMaintenanceService.
    /// </summary>
    [TestFixture]
    public class HistoryMaintenanceServiceTests
    {
        private Mock<ILoggingService>? _mockLoggingService;
        private Mock<IClipboardHistoryManager>? _mockHistoryManager;
        private HistoryMaintenanceService? _maintenanceService;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _maintenanceService = new HistoryMaintenanceService(_mockLoggingService.Object, _mockHistoryManager.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new HistoryMaintenanceService(null!, _mockHistoryManager!.Object));
        }

        [Test]
        public void Constructor_WithNullHistoryManager_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new HistoryMaintenanceService(_mockLoggingService!.Object, null!));
        }

        [Test]
        public void Constructor_WithValidParameters_ShouldCreateInstance()
        {
            // Act
            var service = new HistoryMaintenanceService(_mockLoggingService!.Object, _mockHistoryManager!.Object);

            // Assert
            Assert.That(service, Is.Not.Null);
        }

        [Test]
        public void GetMaintenanceProbability_ShouldReturnDefaultValue()
        {
            // Act
            var probability = _maintenanceService!.GetMaintenanceProbability();

            // Assert
            Assert.That(probability, Is.EqualTo(5)); // 5% par défaut
        }

        [Test]
        public void SetMaintenanceProbability_WithValidValue_ShouldUpdateProbability()
        {
            // Arrange
            var newProbability = 10;

            // Act
            _maintenanceService!.SetMaintenanceProbability(newProbability);

            // Assert
            Assert.That(_maintenanceService.GetMaintenanceProbability(), Is.EqualTo(newProbability));
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains($"Probabilité de maintenance configurée à {newProbability}%"))), Times.Once);
        }

        [Test]
        public void SetMaintenanceProbability_WithNegativeValue_ShouldThrowArgumentOutOfRangeException()
        {
            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() => 
                _maintenanceService!.SetMaintenanceProbability(-1));
        }

        [Test]
        public void SetMaintenanceProbability_WithValueOver100_ShouldThrowArgumentOutOfRangeException()
        {
            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() => 
                _maintenanceService!.SetMaintenanceProbability(101));
        }

        [Test]
        public void SetMaintenanceProbability_WithBoundaryValues_ShouldWork()
        {
            // Test 0%
            _maintenanceService!.SetMaintenanceProbability(0);
            Assert.That(_maintenanceService.GetMaintenanceProbability(), Is.EqualTo(0));

            // Test 100%
            _maintenanceService.SetMaintenanceProbability(100);
            Assert.That(_maintenanceService.GetMaintenanceProbability(), Is.EqualTo(100));
        }

        [Test]
        public async Task TriggerMaintenanceIfNeededAsync_WithZeroProbability_ShouldNeverTrigger()
        {
            // Arrange
            _maintenanceService!.SetMaintenanceProbability(0);
            var eventId = "test-event-zero";

            // Act
            var result = await _maintenanceService.TriggerMaintenanceIfNeededAsync(eventId);

            // Assert
            Assert.That(result, Is.False);
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("déclencher=False"))), Times.Once);
            _mockHistoryManager!.Verify(x => x.PurgeOrphanedItemsAsync(), Times.Never);
        }

        [Test]
        public async Task TriggerMaintenanceIfNeededAsync_With100Probability_ShouldAlwaysTrigger()
        {
            // Arrange
            _maintenanceService!.SetMaintenanceProbability(100);
            var eventId = "test-event-hundred";

            // Act
            var result = await _maintenanceService.TriggerMaintenanceIfNeededAsync(eventId);

            // Assert
            Assert.That(result, Is.True);
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("déclencher=True"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("Déclenchement de la purge périodique"))), Times.Once);
        }

        [Test]
        public async Task TriggerMaintenanceIfNeededAsync_ShouldLogRandomValueAndDecision()
        {
            // Arrange
            var eventId = "test-event-logging";

            // Act
            await _maintenanceService!.TriggerMaintenanceIfNeededAsync(eventId);

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("Vérification maintenance") && 
                s.Contains("random=") && 
                s.Contains("seuil=") && 
                s.Contains("déclencher="))), Times.Once);
        }

        [Test]
        public async Task TriggerMaintenanceIfNeededAsync_WithNullEventId_ShouldHandleGracefully()
        {
            // Act
            var result = await _maintenanceService!.TriggerMaintenanceIfNeededAsync(null);

            // Assert
            Assert.That(result, Is.TypeOf<bool>());
        }

        [Test]
        public async Task TriggerMaintenanceIfNeededAsync_WithEmptyEventId_ShouldHandleGracefully()
        {
            // Act
            var result = await _maintenanceService!.TriggerMaintenanceIfNeededAsync("");

            // Assert
            Assert.That(result, Is.TypeOf<bool>());
        }

        [Test]
        public async Task TriggerMaintenanceIfNeededAsync_ShouldUseProbabilityCorrectly()
        {
            // Arrange - Test avec plusieurs appels pour vérifier la probabilité
            _maintenanceService!.SetMaintenanceProbability(50); // 50% de chance
            var eventId = "test-probability";
            var triggerCount = 0;
            var totalCalls = 100;

            // Act - Faire plusieurs appels pour tester la probabilité
            for (int i = 0; i < totalCalls; i++)
            {
                var result = await _maintenanceService.TriggerMaintenanceIfNeededAsync($"{eventId}-{i}");
                if (result) triggerCount++;
            }

            // Assert - Avec 50% de probabilité, on s'attend à environ 50 déclenchements
            // On accepte une marge d'erreur de 30% (entre 35 et 65)
            Assert.That(triggerCount, Is.GreaterThan(20));
            Assert.That(triggerCount, Is.LessThan(80));
        }

        [Test]
        public async Task TriggerMaintenanceIfNeededAsync_WhenPurgeThrowsException_ShouldLogError()
        {
            // Arrange
            _maintenanceService!.SetMaintenanceProbability(100); // Toujours déclencher
            var expectedException = new InvalidOperationException("Purge failed");
            _mockHistoryManager!.Setup(x => x.PurgeOrphanedItemsAsync()).ThrowsAsync(expectedException);
            var eventId = "test-event-exception";

            // Act
            var result = await _maintenanceService.TriggerMaintenanceIfNeededAsync(eventId);

            // Assert
            Assert.That(result, Is.True); // La maintenance est déclenchée même si elle échoue
            
            // Attendre un peu pour que la tâche en arrière-plan se termine
            await Task.Delay(100);
            
            // Vérifier que l'erreur est loggée (peut prendre un moment car c'est dans Task.Run)
            // Note: Ce test peut être flaky car il dépend du timing
        }

        [Test]
        public async Task TriggerMaintenanceIfNeededAsync_ShouldReturnImmediately()
        {
            // Arrange
            _maintenanceService!.SetMaintenanceProbability(100);
            var eventId = "test-event-immediate";

            // Act
            var startTime = DateTime.Now;
            var result = await _maintenanceService.TriggerMaintenanceIfNeededAsync(eventId);
            var endTime = DateTime.Now;

            // Assert
            Assert.That(result, Is.True);
            Assert.That(endTime - startTime, Is.LessThan(TimeSpan.FromSeconds(1))); // Devrait retourner immédiatement
        }

        [Test]
        public async Task TriggerMaintenanceIfNeededAsync_MultipleCalls_ShouldHandleConcurrency()
        {
            // Arrange
            _maintenanceService!.SetMaintenanceProbability(100);
            var eventIds = new[] { "event-1", "event-2", "event-3" };

            // Act
            var tasks = new Task<bool>[eventIds.Length];
            for (int i = 0; i < eventIds.Length; i++)
            {
                tasks[i] = _maintenanceService.TriggerMaintenanceIfNeededAsync(eventIds[i]);
            }

            var results = await Task.WhenAll(tasks);

            // Assert
            Assert.That(results, Has.All.True);
        }

        [Test]
        public void SetMaintenanceProbability_MultipleCalls_ShouldUpdateCorrectly()
        {
            // Act & Assert
            _maintenanceService!.SetMaintenanceProbability(25);
            Assert.That(_maintenanceService.GetMaintenanceProbability(), Is.EqualTo(25));

            _maintenanceService.SetMaintenanceProbability(75);
            Assert.That(_maintenanceService.GetMaintenanceProbability(), Is.EqualTo(75));

            _maintenanceService.SetMaintenanceProbability(0);
            Assert.That(_maintenanceService.GetMaintenanceProbability(), Is.EqualTo(0));
        }

        [Test]
        public async Task TriggerMaintenanceIfNeededAsync_ShouldUseCurrentProbability()
        {
            // Arrange
            var eventId = "test-probability-change";

            // Test avec probabilité 0
            _maintenanceService!.SetMaintenanceProbability(0);
            var result1 = await _maintenanceService.TriggerMaintenanceIfNeededAsync(eventId);

            // Test avec probabilité 100
            _maintenanceService.SetMaintenanceProbability(100);
            var result2 = await _maintenanceService.TriggerMaintenanceIfNeededAsync(eventId);

            // Assert
            Assert.That(result1, Is.False);
            Assert.That(result2, Is.True);
        }
    }
}
