using CommunityToolkit.Mvvm.ComponentModel;

namespace ClipboardPlus.Core.DataModels
{
    /// <summary>
    /// Représente les informations d'un thème de l'application.
    /// </summary>
    public class ThemeInfo : ObservableObject
    {
        private string _name;
        /// <summary>
        /// Nom descriptif du thème.
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        private string _filePath;
        /// <summary>
        /// Chemin relatif vers le fichier XAML ResourceDictionary du thème.
        /// </summary>
        public string FilePath
        {
            get => _filePath;
            set => SetProperty(ref _filePath, value);
        }

        /// <summary>
        /// Constructeur par défaut.
        /// </summary>
        public ThemeInfo()
        {
            _name = string.Empty;
            _filePath = string.Empty;
        }

        /// <summary>
        /// Constructeur avec paramètres.
        /// </summary>
        /// <param name="name">Nom descriptif du thème.</param>
        /// <param name="filePath">Chemin du fichier XAML ResourceDictionary.</param>
        public ThemeInfo(string name, string filePath)
        {
            _name = name;
            _filePath = filePath;
        }
    }
} 