using System;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using System.ComponentModel; // Ajout pour INotifyPropertyChanged

namespace ClipboardPlus.Tests.Unit.Core.DataModels
{
    [TestFixture]
    public class ApplicationSettingsTests
    {
        [Test]
        public void Constructor_InitializesDefaultValues()
        {
            // Act
            var settings = new ApplicationSettings();

            // Assert
            Assert.That(settings.MaxHistoryItems, Is.EqualTo(50));
            Assert.That(settings.StartWithWindows, Is.EqualTo(false));
            Assert.That(settings.MaxImageDimensionForThumbnail, Is.EqualTo(256));
            Assert.That(settings.MaxStorableItemSizeBytes, Is.EqualTo(10 * 1024 * 1024));
            Assert.That(settings.ShortcutKeyCombination, Is.EqualTo("Ctrl+Alt+V"));
            Assert.That(settings.ActiveThemePath, Is.EqualTo("pack://application:,,,/ClipboardPlus;component/UI/Themes/Default.xaml"));
        }

        [Test]
        public void MaxHistoryItems_SetBelowMinimum_SetsToMinimum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int originalValue = settings.MaxHistoryItems;

            // Act
            settings.MaxHistoryItems = 0;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.That(settings.MaxHistoryItems, Is.EqualTo(0));
        }

        [Test]
        public void MaxHistoryItems_SetAboveMaximum_SetsToMaximum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int originalValue = settings.MaxHistoryItems;

            // Act
            settings.MaxHistoryItems = 1001;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.That(settings.MaxHistoryItems, Is.EqualTo(1001));
        }

        [Test]
        public void MaxHistoryItems_SetValidValue_SetsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act
            settings.MaxHistoryItems = 75;

            // Assert
            Assert.That(settings.MaxHistoryItems, Is.EqualTo(75));
        }

        [Test]
        public void MaxImageDimensionForThumbnail_SetBelowMinimum_SetsToMinimum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int originalValue = settings.MaxImageDimensionForThumbnail;

            // Act
            settings.MaxImageDimensionForThumbnail = 15;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.That(settings.MaxImageDimensionForThumbnail, Is.EqualTo(15));
        }

        [Test]
        public void MaxImageDimensionForThumbnail_SetAboveMaximum_SetsToMaximum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int originalValue = settings.MaxImageDimensionForThumbnail;

            // Act
            settings.MaxImageDimensionForThumbnail = 2049;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.That(settings.MaxImageDimensionForThumbnail, Is.EqualTo(2049));
        }

        [Test]
        public void MaxImageDimensionForThumbnail_SetValidValue_SetsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act
            settings.MaxImageDimensionForThumbnail = 512;

            // Assert
            Assert.That(settings.MaxImageDimensionForThumbnail, Is.EqualTo(512));
        }

        [Test]
        public void MaxStorableItemSizeBytes_SetBelowMinimum_SetsToMinimum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            long originalValue = settings.MaxStorableItemSizeBytes;

            // Act
            settings.MaxStorableItemSizeBytes = 1024;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.That(settings.MaxStorableItemSizeBytes, Is.EqualTo(1024));
        }

        [Test]
        public void MaxStorableItemSizeBytes_SetAboveMaximum_SetsToMaximum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            long originalValue = settings.MaxStorableItemSizeBytes;

            // Act
            settings.MaxStorableItemSizeBytes = 51 * 1024 * 1024;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.That(settings.MaxStorableItemSizeBytes, Is.EqualTo(53477376)); // 51 * 1024 * 1024
        }

        [Test]
        public void MaxStorableItemSizeBytes_SetValidValue_SetsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act
            settings.MaxStorableItemSizeBytes = 5 * 1024 * 1024;

            // Assert
            Assert.That(settings.MaxStorableItemSizeBytes, Is.EqualTo(5 * 1024 * 1024)); // 5 MB
        }

        [Test]
        public void ShortcutKeyCombination_SetValidValue_SetsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act
            settings.ShortcutKeyCombination = "Ctrl+Shift+C";

            // Assert
            Assert.That(settings.ShortcutKeyCombination, Is.EqualTo("Ctrl+Shift+C"));
        }

        [Test]
        public void ShortcutKeyCombination_SetNullOrEmpty_AcceptsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act - Test null
            #pragma warning disable CS8625 // Impossible de convertir un littéral ayant une valeur null en type référence non-nullable.
            settings.ShortcutKeyCombination = null;
            #pragma warning restore CS8625

            // Assert
            Assert.That(settings.ShortcutKeyCombination, Is.Null);

            // Act - Test empty
            settings.ShortcutKeyCombination = string.Empty;

            // Assert
            Assert.That(settings.ShortcutKeyCombination, Is.EqualTo(string.Empty));
        }

        [Test]
        public void ActiveThemePath_SetValidValue_SetsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act
            settings.ActiveThemePath = "pack://application:,,,/ClipboardPlus;component/UI/Themes/Dark.xaml";

            // Assert
            Assert.That(settings.ActiveThemePath, Is.EqualTo("pack://application:,,,/ClipboardPlus;component/UI/Themes/Dark.xaml"));
        }

        [Test]
        public void ActiveThemePath_SetNullOrEmpty_AcceptsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act - Test null
            #pragma warning disable CS8625 // Impossible de convertir un littéral ayant une valeur null en type référence non-nullable.
            settings.ActiveThemePath = null;
            #pragma warning restore CS8625

            // Assert
            Assert.That(settings.ActiveThemePath, Is.Null);

            // Act - Test empty
            settings.ActiveThemePath = string.Empty;

            // Assert
            Assert.That(settings.ActiveThemePath, Is.EqualTo(string.Empty));
        }

        // --- PropertyChanged Tests ---

        [Test]
        public void MaxHistoryItems_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            int newValue = 100;

            // Act
            settings.MaxHistoryItems = newValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.True, "PropertyChanged aurait dû être levé pour MaxHistoryItems.");
            Assert.That(propertyName, Is.EqualTo(nameof(ApplicationSettings.MaxHistoryItems)), "Le nom de la propriété changée devrait être MaxHistoryItems.");
            Assert.That(settings.MaxHistoryItems, Is.EqualTo(newValue), "La propriété MaxHistoryItems aurait dû être mise à jour.");
        }

        [Test]
        public void ActiveThemePath_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            string newValue = "new/theme/path.xaml";

            // Act
            settings.ActiveThemePath = newValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.True, "PropertyChanged aurait dû être levé pour ActiveThemePath.");
            Assert.That(propertyName, Is.EqualTo(nameof(ApplicationSettings.ActiveThemePath)), "Le nom de la propriété changée devrait être ActiveThemePath.");
            Assert.That(settings.ActiveThemePath, Is.EqualTo(newValue), "La propriété ActiveThemePath aurait dû être mise à jour.");
        }

        [Test]
        public void ShortcutKeyCombination_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            string newValue = "Ctrl+Alt+C";

            // Act
            settings.ShortcutKeyCombination = newValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.True, "PropertyChanged aurait dû être levé pour ShortcutKeyCombination.");
            Assert.That(propertyName, Is.EqualTo(nameof(ApplicationSettings.ShortcutKeyCombination)), "Le nom de la propriété changée devrait être ShortcutKeyCombination.");
            Assert.That(settings.ShortcutKeyCombination, Is.EqualTo(newValue), "La propriété ShortcutKeyCombination aurait dû être mise à jour.");
        }

        [Test]
        public void StartWithWindows_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool initialValue = settings.StartWithWindows;
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            bool newValue = !initialValue;

            // Act
            settings.StartWithWindows = newValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.True, "PropertyChanged aurait dû être levé pour StartWithWindows.");
            Assert.That(propertyName, Is.EqualTo(nameof(ApplicationSettings.StartWithWindows)), "Le nom de la propriété changée devrait être StartWithWindows.");
            Assert.That(settings.StartWithWindows, Is.EqualTo(newValue), "La propriété StartWithWindows aurait dû être mise à jour.");
        }

        [Test]
        public void MaxImageDimensionForThumbnail_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            int newValue = 128;

            // Act
            settings.MaxImageDimensionForThumbnail = newValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.True, "PropertyChanged aurait dû être levé pour MaxImageDimensionForThumbnail.");
            Assert.That(propertyName, Is.EqualTo(nameof(ApplicationSettings.MaxImageDimensionForThumbnail)), "Le nom de la propriété changée devrait être MaxImageDimensionForThumbnail.");
            Assert.That(settings.MaxImageDimensionForThumbnail, Is.EqualTo(newValue), "La propriété MaxImageDimensionForThumbnail aurait dû être mise à jour.");
        }

        [Test]
        public void MaxTextPreviewLength_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            int newValue = 60;

            // Act
            settings.MaxTextPreviewLength = newValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.True, "PropertyChanged aurait dû être levé pour MaxTextPreviewLength.");
            Assert.That(propertyName, Is.EqualTo(nameof(ApplicationSettings.MaxTextPreviewLength)), "Le nom de la propriété changée devrait être MaxTextPreviewLength.");
            Assert.That(settings.MaxTextPreviewLength, Is.EqualTo(newValue), "La propriété MaxTextPreviewLength aurait dû être mise à jour.");
        }

        [Test]
        public void MaxStorableItemSizeBytes_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            long newValue = 20 * 1024 * 1024; // 20 MB

            // Act
            settings.MaxStorableItemSizeBytes = newValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.True, "PropertyChanged aurait dû être levé pour MaxStorableItemSizeBytes.");
            Assert.That(propertyName, Is.EqualTo(nameof(ApplicationSettings.MaxStorableItemSizeBytes)), "Le nom de la propriété changée devrait être MaxStorableItemSizeBytes.");
            Assert.That(settings.MaxStorableItemSizeBytes, Is.EqualTo(newValue), "La propriété MaxStorableItemSizeBytes aurait dû être mise à jour.");
        }

        // --- Tests pour les cas où les valeurs ne changent pas (branche SetProperty qui retourne false) ---

        [Test]
        public void MaxHistoryItems_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int currentValue = settings.MaxHistoryItems;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.MaxHistoryItems = currentValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.False, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.That(settings.MaxHistoryItems, Is.EqualTo(currentValue), "La valeur devrait rester inchangée.");
        }

        [Test]
        public void ActiveThemePath_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            string currentValue = settings.ActiveThemePath;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.ActiveThemePath = currentValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.False, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.That(settings.ActiveThemePath, Is.EqualTo(currentValue), "La valeur devrait rester inchangée.");
        }

        [Test]
        public void ShortcutKeyCombination_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            string currentValue = settings.ShortcutKeyCombination;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.ShortcutKeyCombination = currentValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.False, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.That(settings.ShortcutKeyCombination, Is.EqualTo(currentValue), "La valeur devrait rester inchangée.");
        }

        [Test]
        public void StartWithWindows_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool currentValue = settings.StartWithWindows;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.StartWithWindows = currentValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.False, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.That(settings.StartWithWindows, Is.EqualTo(currentValue), "La valeur devrait rester inchangée.");
        }

        [Test]
        public void MaxImageDimensionForThumbnail_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int currentValue = settings.MaxImageDimensionForThumbnail;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.MaxImageDimensionForThumbnail = currentValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.False, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.That(settings.MaxImageDimensionForThumbnail, Is.EqualTo(currentValue), "La valeur devrait rester inchangée.");
        }

        [Test]
        public void MaxTextPreviewLength_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int currentValue = settings.MaxTextPreviewLength;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.MaxTextPreviewLength = currentValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.False, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.That(settings.MaxTextPreviewLength, Is.EqualTo(currentValue), "La valeur devrait rester inchangée.");
        }

        [Test]
        public void MaxStorableItemSizeBytes_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int changeCount = 0;
            settings.PropertyChanged += (sender, e) => changeCount++;

            // Act
            settings.MaxStorableItemSizeBytes = settings.MaxStorableItemSizeBytes;

            // Assert
            Assert.That(changeCount, Is.EqualTo(0), "PropertyChanged ne devrait pas être levé quand la valeur n'est pas modifiée.");
        }

        [Test]
        public void HideItemTitle_DefaultValue_IsFalse()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act & Assert
            Assert.That(settings.HideItemTitle, Is.False, "HideItemTitle doit être false par défaut");
        }

        [Test]
        public void HideItemTitle_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            bool newValue = true;

            // Act
            settings.HideItemTitle = newValue;

            // Assert
            Assert.That(propertyChangedRaised, Is.True, "PropertyChanged aurait dû être levé pour HideItemTitle.");
            Assert.That(propertyName, Is.EqualTo(nameof(ApplicationSettings.HideItemTitle)), "Le nom de la propriété changée devrait être HideItemTitle.");
            Assert.That(settings.HideItemTitle, Is.EqualTo(newValue), "La propriété HideItemTitle aurait dû être mise à jour.");
        }

        [Test]
        public void HideItemTitle_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int changeCount = 0;
            settings.PropertyChanged += (sender, e) => changeCount++;
            
            // Définir une valeur initiale
            settings.HideItemTitle = true;
            changeCount = 0; // Réinitialiser le compteur

            // Act
            settings.HideItemTitle = settings.HideItemTitle;

            // Assert
            Assert.That(changeCount, Is.EqualTo(0), "PropertyChanged ne devrait pas être levé quand la valeur n'est pas modifiée.");
        }

        // --- Tests pour le constructeur et l'initialisation ---

        [Test]
        public void Constructor_InitializesActiveThemePathWithCorrectAssemblyName()
        {
            // Arrange & Act
            var settings = new ApplicationSettings();

            // Assert
            Assert.That(settings.ActiveThemePath, Does.Contain("ClipboardPlus"),
                "ActiveThemePath devrait contenir le nom de l'assembly ClipboardPlus.");
            Assert.That(settings.ActiveThemePath, Does.Contain("component/UI/Themes/Default.xaml"),
                "ActiveThemePath devrait contenir le chemin vers Default.xaml.");
            Assert.That(settings.ActiveThemePath, Does.StartWith("pack://application:,,,/"),
                "ActiveThemePath devrait commencer par le schéma pack URI.");
        }

        [Test]
        public void Constructor_SetsAllPropertiesToExpectedDefaults()
        {
            // Arrange & Act
            var settings = new ApplicationSettings();

            // Assert - Vérifier toutes les valeurs par défaut
            Assert.That(settings.MaxHistoryItems, Is.EqualTo(50), "MaxHistoryItems par défaut devrait être 50.");
            Assert.That(settings.StartWithWindows, Is.EqualTo(false), "StartWithWindows par défaut devrait être false.");
            Assert.That(settings.MaxImageDimensionForThumbnail, Is.EqualTo(256), "MaxImageDimensionForThumbnail par défaut devrait être 256.");
            Assert.That(settings.MaxTextPreviewLength, Is.EqualTo(30), "MaxTextPreviewLength par défaut devrait être 30.");
            Assert.That(settings.MaxStorableItemSizeBytes, Is.EqualTo(10 * 1024 * 1024), "MaxStorableItemSizeBytes par défaut devrait être 10MB.");
            Assert.That(settings.ShortcutKeyCombination, Is.EqualTo("Ctrl+Alt+V"), "ShortcutKeyCombination par défaut devrait être 'Ctrl+Alt+V'.");
            Assert.That(settings.ActiveThemePath, Is.Not.Null, "ActiveThemePath ne devrait pas être null.");
            Assert.That(settings.ActiveThemePath.Length, Is.GreaterThan(0), "ActiveThemePath ne devrait pas être vide.");
        }

        [Test]
        public void ApplicationSettings_ImplementsINotifyPropertyChanged()
        {
            // Arrange & Act
            var settings = new ApplicationSettings();

            // Assert
            Assert.That(settings, Is.InstanceOf<INotifyPropertyChanged>(),
                "ApplicationSettings devrait implémenter INotifyPropertyChanged.");
        }

        [Test]
        public void ApplicationSettings_InheritsFromObservableObject()
        {
            // Arrange & Act
            var settings = new ApplicationSettings();

            // Assert
            Assert.That(typeof(ApplicationSettings).BaseType?.Name.Contains("ObservableObject"), Is.True,
                "ApplicationSettings devrait hériter d'ObservableObject.");
        }
    }
}