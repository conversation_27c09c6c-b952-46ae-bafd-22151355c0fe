using System;
using System.Windows;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.NewItem.Implementations;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.NewItem
{
    /// <summary>
    /// Tests unitaires pour DialogConfigurationService.
    /// Vérifie le respect du principe SRP : responsabilité unique de configuration de dialogues.
    /// </summary>
    [TestFixture]
    [Category("NewItem")]
    [Category("Dialog")]
    [Apartment(System.Threading.ApartmentState.STA)] // Requis pour les tests WPF
    public class DialogConfigurationServiceTests
    {
        private DialogConfigurationService _service = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _service = new DialogConfigurationService(_mockLoggingService.Object);
        }

        #region Tests de Configuration de Dialogue

        [Test]
        [Description("Valide que ConfigureNewItemDialog configure correctement une Window")]
        public void ConfigureNewItemDialog_WithValidWindow_ConfiguresCorrectly()
        {
            // Arrange
            var window = new Window();
            var dataContext = new object();

            // Act
            _service.ConfigureNewItemDialog(window, dataContext);

            // Assert
            Assert.That(window.DataContext, Is.SameAs(dataContext),
                "Le DataContext devrait être défini correctement");
        }

        [Test]
        [Description("Valide que ConfigureNewItemDialog gère les objets non-Window")]
        public void ConfigureNewItemDialog_WithNonWindow_LogsWarning()
        {
            // Arrange
            var nonWindow = new object();
            var dataContext = new object();

            // Act
            _service.ConfigureNewItemDialog(nonWindow, dataContext);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("Le dialogue fourni n'est pas une Window"))),
                Times.Once,
                "Un avertissement devrait être loggé pour un objet non-Window");
        }

        [Test]
        [Description("Valide que ConfigureNewItemDialog gère les objets null")]
        public void ConfigureNewItemDialog_WithNullDialog_LogsWarning()
        {
            // Arrange
            object? nullDialog = null;
            var dataContext = new object();

            // Act
            _service.ConfigureNewItemDialog(nullDialog!, dataContext);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("Le dialogue fourni n'est pas une Window"))),
                Times.Once,
                "Un avertissement devrait être loggé pour un dialogue null");
        }

        #endregion

        #region Tests de Recherche de Fenêtre Propriétaire

        [Test]
        [Description("Valide que FindOwnerWindow ne lève pas d'exception")]
        public void FindOwnerWindow_DoesNotThrowException()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _service.FindOwnerWindow(),
                "FindOwnerWindow ne devrait jamais lever d'exception");
        }

        [Test]
        [Description("Valide que FindOwnerWindow retourne null quand aucune fenêtre n'est trouvée")]
        public void FindOwnerWindow_WhenNoWindowFound_ReturnsNull()
        {
            // Act
            var result = _service.FindOwnerWindow();

            // Assert
            Assert.That(result, Is.Null,
                "FindOwnerWindow devrait retourner null quand aucune ClipboardHistoryWindow n'est trouvée");
        }

        [Test]
        [Description("Valide que FindOwnerWindow gère les exceptions gracieusement")]
        public void FindOwnerWindow_WhenExceptionOccurs_ReturnsNullAndLogs()
        {
            // Note: Il est difficile de forcer une exception dans FindOwnerWindow
            // car elle utilise Application.Current qui est difficile à mocker.
            // Dans un vrai projet, on injecterait une abstraction pour Application.Current.
            
            // Pour l'instant, on vérifie juste que la méthode ne lève pas d'exception
            Assert.DoesNotThrow(() => _service.FindOwnerWindow(),
                "FindOwnerWindow devrait gérer les exceptions gracieusement");
        }

        #endregion

        #region Tests de Configuration Complète avec Owner

        [Test]
        [Description("Valide que ConfigureNewItemDialog configure l'Owner quand une fenêtre est trouvée")]
        public void ConfigureNewItemDialog_WhenOwnerFound_ConfiguresOwner()
        {
            // Arrange
            var window = new Window();
            var dataContext = new object();
            
            // Note: Dans un environnement de test, FindOwnerWindow retournera probablement null
            // car il n'y a pas de ClipboardHistoryWindow active. Ce test vérifie le comportement
            // quand aucune fenêtre n'est trouvée.

            // Act
            _service.ConfigureNewItemDialog(window, dataContext);

            // Assert
            Assert.That(window.DataContext, Is.SameAs(dataContext),
                "Le DataContext devrait être configuré même si aucun Owner n'est trouvé");
            
            // Vérifier le logging approprié
            _mockLoggingService.Verify(
                x => x.LogWarning("Aucune ClipboardHistoryWindow trouvée, utilisation du centrage écran."),
                Times.Once,
                "Un avertissement devrait être loggé quand aucune fenêtre propriétaire n'est trouvée");
        }

        #endregion

        #region Tests de Robustesse

        [Test]
        [Description("Valide que le service fonctionne sans service de logging")]
        public void DialogConfigurationService_WithoutLoggingService_WorksCorrectly()
        {
            // Arrange
            var serviceWithoutLogging = new DialogConfigurationService(null);
            var window = new Window();
            var dataContext = new object();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                serviceWithoutLogging.ConfigureNewItemDialog(window, dataContext);
                Assert.That(window.DataContext, Is.SameAs(dataContext));
            }, "Le service devrait fonctionner sans service de logging");
        }

        [Test]
        [Description("Valide que le service peut être créé sans paramètres")]
        public void Constructor_WithoutParameters_CreatesValidInstance()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var service = new DialogConfigurationService();
                var window = new Window();
                service.ConfigureNewItemDialog(window, new object());
                Assert.That(window.DataContext, Is.Not.Null);
            }, "Le service devrait pouvoir être créé sans paramètres");
        }

        #endregion

        #region Tests de Comportement Spécifique

        [Test]
        [Description("Valide que ConfigureNewItemDialog accepte un DataContext null")]
        public void ConfigureNewItemDialog_WithNullDataContext_WorksCorrectly()
        {
            // Arrange
            var window = new Window();
            object? nullDataContext = null;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _service.ConfigureNewItemDialog(window, nullDataContext!);
                Assert.That(window.DataContext, Is.Null);
            }, "ConfigureNewItemDialog devrait accepter un DataContext null");
        }

        [Test]
        [Description("Valide que ConfigureNewItemDialog peut être appelée plusieurs fois")]
        public void ConfigureNewItemDialog_CalledMultipleTimes_WorksCorrectly()
        {
            // Arrange
            var window = new Window();
            var dataContext1 = new object();
            var dataContext2 = new object();

            // Act
            _service.ConfigureNewItemDialog(window, dataContext1);
            _service.ConfigureNewItemDialog(window, dataContext2);

            // Assert
            Assert.That(window.DataContext, Is.SameAs(dataContext2),
                "Le dernier DataContext devrait être appliqué");
        }

        #endregion

        #region Tests de Logging

        [Test]
        [Description("Valide que les logs appropriés sont générés selon les scénarios")]
        public void ConfigureNewItemDialog_GeneratesAppropriateLogsForDifferentScenarios()
        {
            // Test avec Window valide
            var window = new Window();
            _service.ConfigureNewItemDialog(window, new object());
            
            // Test avec objet non-Window
            _service.ConfigureNewItemDialog(new object(), new object());
            
            // Vérifier les logs
            _mockLoggingService.Verify(
                x => x.LogWarning("Aucune ClipboardHistoryWindow trouvée, utilisation du centrage écran."),
                Times.Once,
                "Un log pour l'absence de fenêtre propriétaire devrait être généré");
            
            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("Le dialogue fourni n'est pas une Window"))),
                Times.Once,
                "Un log pour l'objet non-Window devrait être généré");
        }

        #endregion
    }
}
