using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Diagnostics;
using ClipboardPlus.UI.ViewModels;
using System.Text;
using System.Windows;
using WpfApplication = System.Windows.Application;
using ClipboardPlus.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using System.Threading.Tasks;

namespace ClipboardPlus.UI.Controls
{
    /// <summary>
    /// Classe de diagnostic pour les opérations de suppression d'éléments
    /// </summary>
    public class DeletionDiagnostic : IDeletionDiagnostic
    {
        private static readonly string LogFilePath = GetCorrectLogFilePath();

        /// <summary>
        /// Obtient le chemin correct pour le fichier de diagnostic (même logique que LoggingService)
        /// </summary>
        private static string GetCorrectLogFilePath()
        {
            try
            {
                var projectRoot = GetProjectRootDirectory();
                var logDirectory = Path.Combine(projectRoot, "logs");
                Directory.CreateDirectory(logDirectory); // Créer le dossier si nécessaire
                return Path.Combine(logDirectory, "deletion_diagnostic.log");
            }
            catch (Exception)
            {
                // Fallback vers le répertoire courant
                var fallbackDir = Path.Combine(Directory.GetCurrentDirectory(), "logs");
                Directory.CreateDirectory(fallbackDir);
                return Path.Combine(fallbackDir, "deletion_diagnostic.log");
            }
        }

        /// <summary>
        /// COPIE EXACTE de LoggingService.GetProjectRootDirectory()
        /// </summary>
        private static string GetProjectRootDirectory()
        {
            try
            {
                // Commencer par le répertoire de l'assembly en cours
                var currentDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);

                if (string.IsNullOrEmpty(currentDirectory))
                {
                    currentDirectory = Directory.GetCurrentDirectory();
                }

                // Remonter jusqu'à trouver le fichier .sln ou le dossier src
                var directory = new DirectoryInfo(currentDirectory);
                while (directory != null && directory.Parent != null)
                {
                    // Vérifier si on trouve un fichier .sln
                    if (directory.GetFiles("*.sln").Length > 0)
                    {
                        return directory.FullName;
                    }

                    // Vérifier si on trouve un dossier src
                    if (directory.GetDirectories("src").Length > 0)
                    {
                        return directory.FullName;
                    }

                    directory = directory.Parent;
                }

                // Si on ne trouve pas le projet, utiliser le répertoire courant
                return Directory.GetCurrentDirectory();
            }
            catch (Exception)
            {
                // Fallback vers le répertoire courant
                return Directory.GetCurrentDirectory();
            }
        }

        /// <summary>
        /// Initialise une nouvelle instance de la classe DeletionDiagnostic
        /// </summary>
        public DeletionDiagnostic()
        {
            // S'assurer que le répertoire des logs existe
            var logDirectory = Path.GetDirectoryName(LogFilePath);
            if (logDirectory != null && !Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }
        }



        /// <summary>
        /// Journalise le résultat d'une tentative de suppression (implémentation interface)
        /// </summary>
        /// <param name="success">Indique si la suppression a réussi</param>
        /// <param name="item">L'élément concerné</param>
        /// <param name="message">Message optionnel</param>
        public void LogDeletionResult(bool success, ClipboardItem? item, string? message = null)
        {
            // Redirection vers le nouveau système via le service locator
            try
            {
                if (WpfApplication.Current is App app && app.Services != null)
                {
                    var deletionLogger = app.Services.GetService<IDeletionResultLogger>();
                    if (deletionLogger != null)
                    {
                        var context = success && item != null
                            ? DeletionResultContext.CreateSuccess(item, null, message)
                            : DeletionResultContext.CreateFailure(item, message ?? "Échec", null);

                        _ = Task.Run(async () => await deletionLogger.LogDeletionResultAsync(context));
                        return;
                    }
                }

                // Fallback simple si le nouveau système n'est pas disponible
                LogDeletionResultFallback(success, item, message, null);
            }
            catch (Exception ex)
            {
                var loggingService = GetLoggingService();
                loggingService?.LogError($"[DIAG] Erreur dans LogDeletionResult: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Journalise le résultat d'une tentative de suppression avec ViewModel (implémentation interface)
        /// </summary>
        /// <param name="success">Indique si la suppression a réussi</param>
        /// <param name="item">L'élément concerné</param>
        /// <param name="message">Message optionnel</param>
        /// <param name="viewModel">ViewModel pour validation avancée</param>
        public void LogDeletionResult(bool success, ClipboardItem? item, string? message, ClipboardHistoryViewModel viewModel)
        {
            // Redirection vers le nouveau système avec ViewModel
            try
            {
                if (WpfApplication.Current is App app && app.Services != null)
                {
                    var deletionLogger = app.Services.GetService<IDeletionResultLogger>();
                    if (deletionLogger != null)
                    {
                        var context = success && item != null
                            ? DeletionResultContext.CreateSuccess(item, viewModel, message)
                            : DeletionResultContext.CreateFailure(item, message ?? "Échec", viewModel);

                        _ = Task.Run(async () => await deletionLogger.LogDeletionResultAsync(context));
                        return;
                    }
                }

                // Fallback simple si le nouveau système n'est pas disponible
                LogDeletionResultFallback(success, item, message, viewModel);
            }
            catch (Exception ex)
            {
                var loggingService = GetLoggingService();
                loggingService?.LogError($"[DIAG] Erreur dans LogDeletionResult avec ViewModel: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Méthode de fallback simple pour le logging de résultat de suppression
        /// </summary>
        /// <param name="success">Indique si la suppression a réussi</param>
        /// <param name="item">L'élément concerné</param>
        /// <param name="message">Message optionnel</param>
        /// <param name="viewModel">ViewModel pour contexte</param>
        private void LogDeletionResultFallback(bool success, ClipboardItem? item, string? message, ClipboardHistoryViewModel? viewModel)
        {
            try
            {
                var loggingService = GetLoggingService();
                var status = success ? "réussie" : "échouée";
                var itemInfo = item != null ? $"ID={item.Id}" : "item=null";
                var msg = !string.IsNullOrEmpty(message) ? $" - {message}" : "";

                loggingService?.LogInfo($"[DIAG-FALLBACK] Suppression {status} pour {itemInfo}{msg}");

                // Log simple dans le fichier de diagnostic
                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] SUPPRESSION {status.ToUpper()} - {itemInfo}{msg}";
                AppendToLog(logEntry);
            }
            catch (Exception ex)
            {
                var loggingService = GetLoggingService();
                loggingService?.LogError($"[DIAG-FALLBACK] Erreur lors du logging de fallback: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Journalise une exception survenue pendant la suppression
        /// </summary>
        /// <param name="ex">L'exception</param>
        /// <param name="context">Le contexte dans lequel l'exception s'est produite</param>
        public void LogDeletionException(Exception ex, string context)
        {
            try
            {
                var loggingService = GetLoggingService();
                loggingService?.LogError($"[DIAG] Exception lors de la suppression - Contexte: {context}", ex);

                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"===== EXCEPTION DE SUPPRESSION - {context} - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} =====");
                sb.AppendLine($"Thread ID: {Environment.CurrentManagedThreadId}");
                sb.AppendLine();

                // Détails de l'exception
                sb.AppendLine("DÉTAILS DE L'EXCEPTION:");
                sb.AppendLine($"  Type: {ex.GetType().FullName}");
                sb.AppendLine($"  Message: {ex.Message}");
                sb.AppendLine($"  Source: {ex.Source}");
                sb.AppendLine($"  TargetSite: {ex.TargetSite?.Name ?? "inconnu"}");
                sb.AppendLine($"  HResult: 0x{ex.HResult:X8}");

                // Propriétés spécifiques selon le type d'exception
                if (ex is System.Runtime.InteropServices.COMException comEx)
                {
                    sb.AppendLine($"  ErrorCode: 0x{comEx.ErrorCode:X8}");
                }
                else if (ex is System.ComponentModel.Win32Exception win32Ex)
                {
                    sb.AppendLine($"  NativeErrorCode: {win32Ex.NativeErrorCode}");
                    sb.AppendLine($"  ErrorCode: 0x{win32Ex.ErrorCode:X8}");
                }
                else if (ex is InvalidOperationException)
                {
                    sb.AppendLine("  Type: InvalidOperationException - Peut indiquer un problème de synchronisation ou d'état");
                }
                else if (ex is NullReferenceException)
                {
                    sb.AppendLine("  Type: NullReferenceException - Indique qu'un objet nécessaire est null");
                }
                else if (ex.GetType().FullName?.Contains("SQLite") == true)
                {
                    sb.AppendLine($"  SQLite Exception - Vérifiez les logs pour plus de détails");
                }

                // Stack trace
                sb.AppendLine();
                sb.AppendLine("STACK TRACE:");
                sb.AppendLine(ex.StackTrace ?? "Aucune stack trace disponible");

                // Exceptions internes
                if (ex.InnerException != null)
                {
                    sb.AppendLine();
                    sb.AppendLine("EXCEPTION INTERNE:");
                    sb.AppendLine($"  Type: {ex.InnerException.GetType().FullName}");
                    sb.AppendLine($"  Message: {ex.InnerException.Message}");
                    sb.AppendLine($"  Stack Trace: {ex.InnerException.StackTrace ?? "Aucune stack trace disponible"}");

                    // Si l'exception interne a elle-même une exception interne
                    if (ex.InnerException.InnerException != null)
                    {
                        sb.AppendLine();
                        sb.AppendLine("EXCEPTION INTERNE NIVEAU 2:");
                        sb.AppendLine($"  Type: {ex.InnerException.InnerException.GetType().FullName}");
                        sb.AppendLine($"  Message: {ex.InnerException.InnerException.Message}");
                    }
                }

                // État de l'application
                sb.AppendLine();
                sb.AppendLine("ÉTAT DE L'APPLICATION:");

                // Vérifier si le ViewModel est accessible
                var viewModel = GetViewModelFromApplication();
                if (viewModel != null)
                {
                    sb.AppendLine($"  ViewModel disponible: Oui");
                    sb.AppendLine($"  Nombre d'éléments dans HistoryItems: {viewModel.HistoryItems.Count}");
                    sb.AppendLine($"  IsLoading: {viewModel.IsLoading}");

                    // Vérifier les champs privés importants du ViewModel
                    var clipboardHistoryManager = GetPrivateFieldValue(viewModel, "_clipboardHistoryManager");
                    var isUpdatingItem = GetPrivateFieldValue(viewModel, "_isUpdatingItem");

                    sb.AppendLine($"  _clipboardHistoryManager: {(clipboardHistoryManager != null ? "disponible" : "NULL")}");
                    sb.AppendLine($"  _isUpdatingItem: {isUpdatingItem}");

                    // Vérifier si le thread UI est bloqué
                    bool isOnUiThread = false;
                    if (WpfApplication.Current != null && WpfApplication.Current.Dispatcher != null)
                    {
                        isOnUiThread = WpfApplication.Current.Dispatcher.CheckAccess();
                        sb.AppendLine($"  Sur le thread UI: {isOnUiThread}");
                    }
                    else
                    {
                        sb.AppendLine("  Application.Current ou Dispatcher est null");
                    }
                }
                else
                {
                    sb.AppendLine("  ViewModel non disponible");
                }

                // Informations sur le contexte
                sb.AppendLine();
                sb.AppendLine($"CONTEXTE: {context}");

                sb.AppendLine();
                sb.AppendLine($"===== FIN EXCEPTION DE SUPPRESSION - {context} - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} =====");

                AppendToLog(sb.ToString());
                loggingService?.LogInfo("[DIAG] Diagnostic d'exception de suppression terminé");
            }
            catch (Exception diagEx)
                {
                    var loggingService = GetLoggingService();
                loggingService?.LogError($"[DIAG] Erreur dans LogDeletionException: {diagEx.Message}", diagEx);
                AppendToLog($"ERREUR dans LogDeletionException: {diagEx.Message}\n{diagEx.StackTrace}");
            }
        }

        /// <summary>
        /// Journalise l'état de la collection après une suppression
        /// NOUVELLE VERSION - Utilise les services existants pour respecter les principes SOLID
        /// </summary>
        /// <param name="viewModel">Le ViewModel contenant la collection</param>
        /// <param name="context">Le contexte de la journalisation</param>
        public void LogCollectionState(ClipboardHistoryViewModel viewModel, string context)
        {
            try
            {
                var loggingService = GetLoggingService();
                loggingService?.LogInfo($"[DIAG] Analyse de l'état de la collection - Contexte: {context}");

                // Tenter d'utiliser les services existants (migration SOLID)
                var collectionAnalyzer = GetCollectionStateAnalyzer();
                var formatter = GetDeletionResultFormatter();

                if (collectionAnalyzer != null && formatter != null)
                {
                    // NOUVELLE APPROCHE : Utiliser les services existants
                    LogCollectionStateWithServices(viewModel, context, collectionAnalyzer, formatter, loggingService);
                }
                else
                {
                    // FALLBACK : Utiliser l'ancienne implémentation si les services ne sont pas disponibles
                    loggingService?.LogWarning($"[DIAG] Services non disponibles, utilisation de l'implémentation de fallback");
                    LogCollectionStateFallback(viewModel, context, loggingService);
                }
            }
            catch (Exception ex)
            {
                var loggingService = GetLoggingService();
                loggingService?.LogError($"[DIAG] Erreur dans LogCollectionState: {ex.Message}", ex);
                AppendToLog($"ERREUR dans LogCollectionState: {ex.Message}\n{ex.StackTrace}");
            }
        }

        /// <summary>
        /// Récupère la valeur d'un champ privé via réflexion
        /// </summary>
        /// <param name="obj">L'objet contenant le champ</param>
        /// <param name="fieldName">Le nom du champ</param>
        /// <returns>La valeur du champ</returns>
        private object? GetPrivateFieldValue(object obj, string fieldName)
        {
            if (obj == null)
                return null;

            var field = obj.GetType().GetField(fieldName,
                BindingFlags.Instance | BindingFlags.NonPublic);

            if (field == null)
                return null;

            return field.GetValue(obj);
        }

        /// <summary>
        /// Ajoute du texte au fichier de log
        /// </summary>
        /// <param name="text">Le texte à ajouter</param>
        private void AppendToLog(string text)
        {
            try
            {
                File.AppendAllText(LogFilePath, text + Environment.NewLine);
                Debug.WriteLine(text);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Erreur lors de l'écriture dans le fichier de log: {ex.Message}");
            }
        }

        private ClipboardHistoryViewModel? GetViewModelFromApplication()
        {
            try
            {
                // Essayer de récupérer le ViewModel depuis l'application
                if (WpfApplication.Current?.MainWindow is Windows.ClipboardHistoryWindow historyWindow)
                {
                    return historyWindow.DataContext as ClipboardHistoryViewModel;
                }

                // Essayer de trouver la fenêtre d'historique parmi toutes les fenêtres
                if (WpfApplication.Current?.Windows != null)
                {
                    foreach (Window window in WpfApplication.Current.Windows)
                {
                    if (window is Windows.ClipboardHistoryWindow histWin && window.DataContext is ClipboardHistoryViewModel vm)
                    {
                        return vm;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                AppendToLog($"ERREUR dans GetViewModelFromApplication: {ex.Message}");
            }

            return null;
        }

        private ILoggingService? GetLoggingService()
        {
            try
            {
                // Utiliser le même pattern que GetDeletionDiagnostic() - récupérer depuis le conteneur DI
                if (WpfApplication.Current is App appInstance && appInstance.Services != null)
                {
                    return appInstance.Services.GetService<ILoggingService>();
                }
            }
            catch
            {
                // Ignorer les erreurs
            }

            return null;
        }

        /// <summary>
        /// Obtient le service d'analyse de l'état des collections via le service locator
        /// </summary>
        private ICollectionStateAnalyzer? GetCollectionStateAnalyzer()
        {
            try
            {
                // Accéder au ServiceProvider via App.Services
                if (WpfApplication.Current is App appInstance && appInstance.Services != null)
                {
                    return appInstance.Services.GetService<ICollectionStateAnalyzer>();
                }
            }
            catch
            {
                // Ignorer les erreurs
            }

            return null;
        }

        /// <summary>
        /// Obtient le service de formatage des résultats de suppression via le service locator
        /// </summary>
        private IDeletionResultFormatter? GetDeletionResultFormatter()
        {
            try
            {
                // Accéder au ServiceProvider via App.Services
                if (WpfApplication.Current is App appInstance && appInstance.Services != null)
                {
                    return appInstance.Services.GetService<IDeletionResultFormatter>();
                }
            }
            catch
            {
                // Ignorer les erreurs
            }

            return null;
        }

        /// <summary>
        /// Nouvelle implémentation utilisant les services existants (respecte les principes SOLID)
        /// </summary>
        /// <param name="viewModel">Le ViewModel contenant la collection</param>
        /// <param name="context">Le contexte de la journalisation</param>
        /// <param name="collectionAnalyzer">Service d'analyse de l'état des collections</param>
        /// <param name="formatter">Service de formatage des résultats</param>
        /// <param name="loggingService">Service de logging</param>
        private void LogCollectionStateWithServices(ClipboardHistoryViewModel viewModel, string context,
            ICollectionStateAnalyzer collectionAnalyzer, IDeletionResultFormatter formatter, ILoggingService? loggingService)
        {
            try
            {
                // Analyser l'état de la collection avec le service dédié
                var analysisResult = collectionAnalyzer.AnalyzeCollectionState(viewModel);

                // Formater le résultat avec le service dédié
                var formattedResult = formatter.FormatCollectionState(analysisResult);

                // Ajouter les informations spécifiques à DeletionDiagnostic
                var diagnosticInfo = BuildDiagnosticSpecificInfo(viewModel, context);
                var finalResult = CombineResults(formattedResult, diagnosticInfo, context);

                AppendToLog(finalResult);
                loggingService?.LogInfo($"[DIAG] Analyse de l'état de la collection terminée avec services - Contexte: {context}");
            }
            catch (Exception ex)
            {
                loggingService?.LogError($"[DIAG] Erreur dans LogCollectionStateWithServices: {ex.Message}", ex);
                // Fallback vers l'ancienne implémentation en cas d'erreur
                LogCollectionStateFallback(viewModel, context, loggingService);
            }
        }

        /// <summary>
        /// Implémentation de fallback (ancienne version refactorisée)
        /// </summary>
        /// <param name="viewModel">Le ViewModel contenant la collection</param>
        /// <param name="context">Le contexte de la journalisation</param>
        /// <param name="loggingService">Service de logging</param>
        private void LogCollectionStateFallback(ClipboardHistoryViewModel viewModel, string context, ILoggingService? loggingService)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"===== ÉTAT DE LA COLLECTION - {context} - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} =====");
            sb.AppendLine($"Thread ID: {Environment.CurrentManagedThreadId}");
            sb.AppendLine();

            // Informations générales sur la collection
            var clipboardHistoryManager = AnalyzeGeneralInformation(viewModel, sb);

            // Vérifier si le gestionnaire d'historique est cohérent avec la collection observable
            AnalyzeManagerConsistency(viewModel, clipboardHistoryManager, sb, loggingService);

            // Vérifier l'intégrité de la collection
            sb.AppendLine();
            DetectIntegrityIssues(viewModel, sb, loggingService);

            // Détails des éléments
            sb.AppendLine();
            FormatItemDetails(viewModel, sb);

            sb.AppendLine();
            sb.AppendLine($"===== FIN ÉTAT DE LA COLLECTION - {context} - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} =====");

            AppendToLog(sb.ToString());
            loggingService?.LogInfo($"[DIAG] Analyse de l'état de la collection terminée (fallback) - Contexte: {context}");
        }

        /// <summary>
        /// Construit les informations spécifiques au diagnostic qui ne sont pas couvertes par les services existants
        /// </summary>
        /// <param name="viewModel">Le ViewModel contenant la collection</param>
        /// <param name="context">Le contexte de la journalisation</param>
        /// <returns>Informations spécifiques au diagnostic</returns>
        private string BuildDiagnosticSpecificInfo(ClipboardHistoryViewModel viewModel, string context)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"Thread ID: {Environment.CurrentManagedThreadId}");
            sb.AppendLine($"IsLoading: {viewModel.IsLoading}");
            sb.AppendLine($"SelectedClipboardItem: {(viewModel.SelectedClipboardItem != null ? viewModel.SelectedClipboardItem.Id.ToString() : "null")}");

            // Informations sur les champs privés (spécifique à DeletionDiagnostic)
            var clipboardHistoryManager = GetPrivateFieldValue(viewModel, "_clipboardHistoryManager");
            var isUpdatingItem = GetPrivateFieldValue(viewModel, "_isUpdatingItem");
            sb.AppendLine($"_clipboardHistoryManager: {(clipboardHistoryManager != null ? "disponible" : "NULL")}");
            sb.AppendLine($"_isUpdatingItem: {isUpdatingItem}");

            return sb.ToString();
        }

        /// <summary>
        /// Combine les résultats du service de formatage avec les informations spécifiques au diagnostic
        /// </summary>
        /// <param name="serviceResult">Résultat du service de formatage</param>
        /// <param name="diagnosticInfo">Informations spécifiques au diagnostic</param>
        /// <param name="context">Contexte de l'analyse</param>
        /// <returns>Résultat combiné</returns>
        private string CombineResults(string serviceResult, string diagnosticInfo, string context)
        {
            var sb = new StringBuilder();

            // En-tête personnalisé pour DeletionDiagnostic
            sb.AppendLine($"===== ÉTAT DE LA COLLECTION - {context} - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} =====");
            sb.AppendLine(diagnosticInfo); // Informations diagnostiques spécifiques
            sb.AppendLine();

            // Ajouter le rapport du service (en supprimant son en-tête s'il existe)
            var lines = serviceResult.Split('\n');
            bool skipHeader = false;
            foreach (var line in lines)
            {
                if (line.Contains("ÉTAT DES COLLECTIONS:"))
                {
                    skipHeader = true;
                    continue;
                }
                if (!skipHeader || !string.IsNullOrWhiteSpace(line))
                {
                    sb.AppendLine(line);
                    skipHeader = false;
                }
            }

            // Pied de page
            sb.AppendLine();
            sb.AppendLine($"===== FIN ÉTAT DE LA COLLECTION - {context} - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} =====");

            return sb.ToString();
        }

        /// <summary>
        /// Analyse les informations générales du ViewModel et retourne le gestionnaire d'historique
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <returns>Le gestionnaire d'historique s'il est disponible</returns>
        private object? AnalyzeGeneralInformation(ClipboardHistoryViewModel viewModel, StringBuilder sb)
        {
            sb.AppendLine("INFORMATIONS GÉNÉRALES:");
            sb.AppendLine($"  Nombre d'éléments dans HistoryItems: {viewModel.HistoryItems.Count}");
            sb.AppendLine($"  SelectedClipboardItem: {(viewModel.SelectedClipboardItem != null ? viewModel.SelectedClipboardItem.Id.ToString() : "null")}");
            sb.AppendLine($"  IsLoading: {viewModel.IsLoading}");

            // Vérifier les champs privés importants du ViewModel
            var clipboardHistoryManager = GetPrivateFieldValue(viewModel, "_clipboardHistoryManager");
            var isUpdatingItem = GetPrivateFieldValue(viewModel, "_isUpdatingItem");

            sb.AppendLine($"  _clipboardHistoryManager: {(clipboardHistoryManager != null ? "disponible" : "NULL")}");
            sb.AppendLine($"  _isUpdatingItem: {isUpdatingItem}");

            return clipboardHistoryManager;
        }

        /// <summary>
        /// Analyse la cohérence entre le ViewModel et le gestionnaire d'historique
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <param name="clipboardHistoryManager">Le gestionnaire d'historique</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <param name="loggingService">Le service de logging</param>
        private void AnalyzeManagerConsistency(ClipboardHistoryViewModel viewModel, object? clipboardHistoryManager, StringBuilder sb, ILoggingService? loggingService)
        {
            if (!IsManagerAvailable(clipboardHistoryManager))
                return;

            var historyItems = GetManagerHistoryItems(clipboardHistoryManager!);
            if (!IsHistoryItemsAccessible(historyItems, sb))
                return;

            sb.AppendLine($"  Nombre d'éléments dans _historyItems du gestionnaire: {historyItems!.Count}");
            CompareCollections(viewModel, historyItems, sb, loggingService);
        }

        /// <summary>
        /// Détecte les problèmes d'intégrité dans la collection (doublons, éléments null)
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <param name="loggingService">Le service de logging</param>
        private void DetectIntegrityIssues(ClipboardHistoryViewModel viewModel, StringBuilder sb, ILoggingService? loggingService)
        {
            sb.AppendLine("VÉRIFICATION D'INTÉGRITÉ:");

            CheckForDuplicateIds(viewModel, sb, loggingService);
            CheckForNullItems(viewModel, sb, loggingService);
        }

        /// <summary>
        /// Formate les détails des éléments de la collection (limité aux 15 premiers)
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        private void FormatItemDetails(ClipboardHistoryViewModel viewModel, StringBuilder sb)
        {
            sb.AppendLine("DÉTAILS DES ÉLÉMENTS:");
            int count = 0;
            foreach (var item in viewModel.HistoryItems.Take(15))
            {
                sb.AppendLine($"  [{count}] ID: {item.Id}, Type: {item.DataType}, Nom: {item.CustomName ?? "(non défini)"}, HashCode: {item.GetHashCode()}, Épinglé: {item.IsPinned}");
                count++;
            }

            if (viewModel.HistoryItems.Count > 15)
            {
                sb.AppendLine($"  ... et {viewModel.HistoryItems.Count - 15} autres éléments");
            }
        }

        /// <summary>
        /// Vérifie la présence de doublons d'ID dans la collection
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <param name="loggingService">Le service de logging</param>
        private void CheckForDuplicateIds(ClipboardHistoryViewModel viewModel, StringBuilder sb, ILoggingService? loggingService)
        {
            var duplicateIds = GetDuplicateIds(viewModel);

            if (HasDuplicates(duplicateIds))
            {
                ReportDuplicateIds(duplicateIds, viewModel, sb, loggingService);
            }
            else
            {
                sb.AppendLine("  OK: Pas de doublons d'ID détectés.");
            }
        }

        /// <summary>
        /// Vérifie la présence d'éléments null dans la collection
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <param name="loggingService">Le service de logging</param>
        private void CheckForNullItems(ClipboardHistoryViewModel viewModel, StringBuilder sb, ILoggingService? loggingService)
        {
            var nullItemsCount = CountNullItems(viewModel);

            if (HasNullItems(nullItemsCount))
            {
                ReportNullItems(nullItemsCount, sb, loggingService);
            }
            else
            {
                sb.AppendLine("  OK: Pas d'éléments null détectés.");
            }
        }

        /// <summary>
        /// Obtient la liste des IDs en double
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <returns>Liste des IDs en double</returns>
        private List<long> GetDuplicateIds(ClipboardHistoryViewModel viewModel)
        {
            return viewModel.HistoryItems
                .GroupBy(i => i.Id)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();
        }

        /// <summary>
        /// Détermine s'il y a des doublons
        /// </summary>
        /// <param name="duplicateIds">Liste des IDs en double</param>
        /// <returns>True s'il y a des doublons</returns>
        private bool HasDuplicates(List<long> duplicateIds) => duplicateIds.Any();

        /// <summary>
        /// Compte le nombre d'éléments null
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <returns>Nombre d'éléments null</returns>
        private int CountNullItems(ClipboardHistoryViewModel viewModel) => viewModel.HistoryItems.Where(i => i == null).Count();

        /// <summary>
        /// Détermine s'il y a des éléments null
        /// </summary>
        /// <param name="nullItemsCount">Nombre d'éléments null</param>
        /// <returns>True s'il y a des éléments null</returns>
        private bool HasNullItems(int nullItemsCount) => nullItemsCount > 0;

        /// <summary>
        /// Rapporte les IDs en double trouvés
        /// </summary>
        /// <param name="duplicateIds">Liste des IDs en double</param>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <param name="loggingService">Le service de logging</param>
        private void ReportDuplicateIds(List<long> duplicateIds, ClipboardHistoryViewModel viewModel, StringBuilder sb, ILoggingService? loggingService)
        {
            sb.AppendLine($"  PROBLÈME CRITIQUE: IDs en double détectés: {string.Join(", ", duplicateIds)}");
            loggingService?.LogError($"[DIAG] IDs en double détectés dans la collection: {string.Join(", ", duplicateIds)}");

            // Détailler les éléments en double
            foreach (var dupId in duplicateIds)
            {
                var duplicates = viewModel.HistoryItems.Where(i => i.Id == dupId).ToList();
                sb.AppendLine($"  Détails des doublons pour ID {dupId}:");

                for (int i = 0; i < duplicates.Count; i++)
                {
                    var dup = duplicates[i];
                    sb.AppendLine($"    Doublon #{i+1} - HashCode: {dup.GetHashCode()}, Type: {dup.DataType}, Nom: {dup.CustomName ?? "(non défini)"}");
                }
            }
        }

        /// <summary>
        /// Rapporte les éléments null trouvés
        /// </summary>
        /// <param name="nullItemsCount">Nombre d'éléments null</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <param name="loggingService">Le service de logging</param>
        private void ReportNullItems(int nullItemsCount, StringBuilder sb, ILoggingService? loggingService)
        {
            sb.AppendLine($"  PROBLÈME CRITIQUE: {nullItemsCount} éléments null détectés dans la collection!");
            loggingService?.LogError($"[DIAG] {nullItemsCount} éléments null détectés dans la collection!");
        }

        /// <summary>
        /// Vérifie si le gestionnaire d'historique est disponible
        /// </summary>
        /// <param name="clipboardHistoryManager">Le gestionnaire d'historique</param>
        /// <returns>True si le gestionnaire est disponible</returns>
        private bool IsManagerAvailable(object? clipboardHistoryManager) => clipboardHistoryManager != null;

        /// <summary>
        /// Obtient les éléments d'historique du gestionnaire
        /// </summary>
        /// <param name="clipboardHistoryManager">Le gestionnaire d'historique</param>
        /// <returns>Liste des éléments d'historique ou null</returns>
        private List<ClipboardItem>? GetManagerHistoryItems(object clipboardHistoryManager) =>
            GetPrivateFieldValue(clipboardHistoryManager, "_historyItems") as List<ClipboardItem>;

        /// <summary>
        /// Vérifie si les éléments d'historique sont accessibles
        /// </summary>
        /// <param name="historyItems">Les éléments d'historique</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <returns>True si les éléments sont accessibles</returns>
        private bool IsHistoryItemsAccessible(List<ClipboardItem>? historyItems, StringBuilder sb)
        {
            if (historyItems == null)
            {
                sb.AppendLine("  Impossible d'accéder à _historyItems du gestionnaire.");
                return false;
            }
            return true;
        }

        /// <summary>
        /// Compare les collections du ViewModel et du gestionnaire
        /// </summary>
        /// <param name="viewModel">Le ViewModel à analyser</param>
        /// <param name="historyItems">Les éléments du gestionnaire</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <param name="loggingService">Le service de logging</param>
        private void CompareCollections(ClipboardHistoryViewModel viewModel, List<ClipboardItem> historyItems, StringBuilder sb, ILoggingService? loggingService)
        {
            var viewModelIds = GetSortedIds(viewModel.HistoryItems);
            var managerIds = GetSortedIds(historyItems);

            bool collectionsIdentical = AreCollectionsIdentical(viewModelIds, managerIds);
            sb.AppendLine($"  Collections identiques (par IDs): {collectionsIdentical}");

            if (!collectionsIdentical)
            {
                ReportCollectionDifferences(viewModelIds, managerIds, sb, loggingService);
            }
        }

        /// <summary>
        /// Obtient les IDs triés d'une collection
        /// </summary>
        /// <param name="items">Collection d'éléments</param>
        /// <returns>Liste des IDs triés</returns>
        private List<long> GetSortedIds(IEnumerable<ClipboardItem> items) =>
            items.Select(i => i.Id).OrderBy(i => i).ToList();

        /// <summary>
        /// Vérifie si deux collections d'IDs sont identiques
        /// </summary>
        /// <param name="viewModelIds">IDs du ViewModel</param>
        /// <param name="managerIds">IDs du gestionnaire</param>
        /// <returns>True si les collections sont identiques</returns>
        private bool AreCollectionsIdentical(List<long> viewModelIds, List<long> managerIds) =>
            viewModelIds.SequenceEqual(managerIds);

        /// <summary>
        /// Rapporte les différences entre les collections
        /// </summary>
        /// <param name="viewModelIds">IDs du ViewModel</param>
        /// <param name="managerIds">IDs du gestionnaire</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <param name="loggingService">Le service de logging</param>
        private void ReportCollectionDifferences(List<long> viewModelIds, List<long> managerIds, StringBuilder sb, ILoggingService? loggingService)
        {
            sb.AppendLine("  PROBLÈME DÉTECTÉ: Les collections ne sont pas synchronisées!");
            loggingService?.LogWarning("[DIAG] Les collections ViewModel.HistoryItems et ClipboardHistoryManager._historyItems ne sont pas synchronisées");

            var onlyInViewModel = GetUniqueToFirst(viewModelIds, managerIds);
            var onlyInManager = GetUniqueToFirst(managerIds, viewModelIds);

            ReportUniqueItems(onlyInViewModel, "ViewModel", sb, loggingService);
            ReportUniqueItems(onlyInManager, "Manager", sb, loggingService);
        }

        /// <summary>
        /// Obtient les éléments uniques à la première collection
        /// </summary>
        /// <param name="first">Première collection</param>
        /// <param name="second">Deuxième collection</param>
        /// <returns>Éléments uniques à la première collection</returns>
        private List<long> GetUniqueToFirst(List<long> first, List<long> second) =>
            first.Except(second).ToList();

        /// <summary>
        /// Rapporte les éléments uniques à une collection
        /// </summary>
        /// <param name="uniqueItems">Éléments uniques</param>
        /// <param name="collectionName">Nom de la collection</param>
        /// <param name="sb">Le StringBuilder pour le rapport</param>
        /// <param name="loggingService">Le service de logging</param>
        private void ReportUniqueItems(List<long> uniqueItems, string collectionName, StringBuilder sb, ILoggingService? loggingService)
        {
            if (uniqueItems.Any())
            {
                sb.AppendLine($"  Éléments présents uniquement dans {collectionName}: {string.Join(", ", uniqueItems)}");
                loggingService?.LogWarning($"[DIAG] Éléments présents uniquement dans {collectionName}: {string.Join(", ", uniqueItems)}");
            }
        }

        #region Nouvelle implémentation SOLID - LogDeletionStart_V2

        /// <summary>
        /// Nouvelle implémentation SOLID pour la journalisation du début des suppressions
        /// Remplace LogDeletionStart avec une architecture modulaire et testable
        /// </summary>
        /// <param name="item">L'élément à supprimer</param>
        /// <param name="viewModel">Le ViewModel actuel</param>
        public void LogDeletionStart_V2(ClipboardItem? item, ClipboardHistoryViewModel viewModel)
        {
            LogDeletionStart_V2(item, viewModel, "Suppression d'élément individuel");
        }

        /// <summary>
        /// Nouvelle implémentation SOLID pour la journalisation du début des suppressions avec message personnalisé
        /// Remplace LogDeletionStart avec une architecture modulaire et testable
        /// </summary>
        /// <param name="item">L'élément à supprimer (peut être null pour les opérations en lot)</param>
        /// <param name="viewModel">Le ViewModel actuel</param>
        /// <param name="message">Message personnalisé décrivant l'opération</param>
        public void LogDeletionStart_V2(ClipboardItem? item, ClipboardHistoryViewModel viewModel, string message)
        {
            try
            {
                // Utilisation du nouveau service orchestrateur SOLID
                var deletionStartLogger = GetDeletionStartLogger();
                deletionStartLogger?.LogDeletionStart(item, viewModel, message);
            }
            catch (Exception ex)
            {
                var loggingService = GetLoggingService();
                loggingService?.LogError($"Erreur dans LogDeletionStart_V2: {ex.Message}", ex);
                AppendToLog($"ERREUR dans LogDeletionStart_V2: {ex.Message}\n{ex.StackTrace}");
            }
        }

        /// <summary>
        /// Obtient ou crée l'instance du service de journalisation de début de suppression
        /// </summary>
        private Core.Services.Diagnostics.IDeletionStartLogger GetDeletionStartLogger()
        {
            // Pour l'instant, création manuelle des dépendances
            // TODO: Sera remplacé par l'injection de dépendances dans une version future
            var loggingService = GetLoggingService();
            if (loggingService == null)
            {
                throw new InvalidOperationException("LoggingService non disponible pour le diagnostic");
            }

            var dataCollector = new ClipboardPlus.Services.Diagnostics.DiagnosticDataCollector(loggingService);
            var formatter = new ClipboardPlus.Services.Diagnostics.DiagnosticFormatter();
            var viewModelAnalyzer = new ClipboardPlus.Services.Diagnostics.ViewModelAnalyzer(loggingService);
            var commandValidator = new ClipboardPlus.Services.Diagnostics.CommandValidator(loggingService);

            return new ClipboardPlus.Services.Diagnostics.DeletionStartLogger(
                dataCollector,
                formatter,
                viewModelAnalyzer,
                commandValidator,
                loggingService
            );
        }

        #endregion
    }
}