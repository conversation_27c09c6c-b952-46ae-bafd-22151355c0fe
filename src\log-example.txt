[2024-05-23 10:15:22] [INFO] === Application démarrée ===
[2024-05-23 10:15:22] [INFO] Configuration des services...
[2024-05-23 10:15:22] [DEBUG] Initialisation du ServiceProvider
[2024-05-23 10:15:22] [INFO] Enregistrement du LoggingService
[2024-05-23 10:15:22] [INFO] Enregistrement du ClipboardHistoryManager
[2024-05-23 10:15:23] [INFO] Initialisation du gestionnaire de raccourcis
[2024-05-23 10:15:23] [INFO] Chargement des paramètres depuis la base de données
[2024-05-23 10:15:23] [INFO] Application prête

[2024-05-23 10:16:45] [INFO] Début de PrepareNewItem
[2024-05-23 10:16:45] [INFO] NewItemTextContent réinitialisé
[2024-05-23 10:16:45] [INFO] Activation de l'interface de création (IsItemCreationActive = true)
[2024-05-23 10:16:45] [INFO] Préparation de l'appel InvokeAsync pour la création du dialogue
[2024-05-23 10:16:45] [INFO] InvokeAsync programmé avec succès
[2024-05-23 10:16:45] [INFO] Début de la création du dialogue (InvokeAsync)
[2024-05-23 10:16:45] [INFO] Création de l'instance NewItemEditorDialog
[2024-05-23 10:16:45] [DEBUG] NewItemEditorDialog: Constructeur avec ViewModel appelé
[2024-05-23 10:16:45] [ERREUR] Erreur lors de la création/affichage du dialogue
Exception: NullReferenceException
Message: La référence d'objet n'est pas définie à une instance d'objet.
Stack Trace:   à ClipboardPlus.UI.Dialogs.NewItemEditorDialog.ContentTextBox.Focus() dans E:\Projets\ClipboardPlus\src\ClipboardPlus\UI\Dialogs\NewItemEditorDialog.xaml.cs:ligne 60
[2024-05-23 10:16:45] [INFO] Message d'erreur affiché à l'utilisateur
[2024-05-23 10:16:45] [INFO] Réinitialisation de l'état en cas d'erreur (IsItemCreationActive = false)
[2024-05-23 10:16:45] [INFO] Fin de PrepareNewItem

[2024-05-23 10:20:12] [INFO] Début de PrepareNewItem
[2024-05-23 10:20:12] [INFO] NewItemTextContent réinitialisé
[2024-05-23 10:20:12] [INFO] Activation de l'interface de création (IsItemCreationActive = true)
[2024-05-23 10:20:12] [INFO] Préparation de l'appel InvokeAsync pour la création du dialogue
[2024-05-23 10:20:12] [ERREUR] Erreur avec le Dispatcher
Exception: InvalidOperationException
Message: L'appel au thread a été annulé.
Stack Trace:   à System.Windows.Threading.Dispatcher.ThrowIfNotDispatcher()
   à System.Windows.Threading.Dispatcher.InvokeAsync(Action callback, DispatcherPriority priority, CancellationToken cancellationToken)
   à ClipboardPlus.UI.ViewModels.ClipboardHistoryViewModel.PrepareNewItem() dans E:\Projets\ClipboardPlus\src\ClipboardPlus\UI\ViewModels\ClipboardHistoryViewModel.cs:ligne 546
[2024-05-23 10:20:12] [INFO] Message d'erreur affiché à l'utilisateur
[2024-05-23 10:20:12] [INFO] Réinitialisation de l'état (IsItemCreationActive = false)
[2024-05-23 10:20:12] [INFO] Fin de PrepareNewItem

[2024-05-23 10:25:33] [!!! CRITIQUE !!!] Exception non gérée dans l'application
Exception: System.NullReferenceException
Message: La référence d'objet n'est pas définie à une instance d'objet.
Stack Trace:   à ClipboardPlus.UI.ViewModels.ClipboardHistoryViewModel.PrepareNewItem() dans E:\Projets\ClipboardPlus\src\ClipboardPlus\UI\ViewModels\ClipboardHistoryViewModel.cs:ligne 511
   à ClipboardPlus.UI.ViewModels.ClipboardHistoryViewModel.<>c.<.ctor>b__51_0(Object param) dans E:\Projets\ClipboardPlus\src\ClipboardPlus\UI\ViewModels\ClipboardHistoryViewModel.cs:ligne 173
   à ClipboardPlus.Core.Commands.RelayCommand.Execute(Object parameter) dans E:\Projets\ClipboardPlus\src\ClipboardPlus\Core\Commands\RelayCommand.cs:ligne 28
Inner Exception: System.InvalidOperationException
Message: Le raccourci est déjà enregistré par une autre application 