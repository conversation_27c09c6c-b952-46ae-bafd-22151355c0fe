using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Diagnostics;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services.Deletion
{
    /// <summary>
    /// Tests spécifiques pour vérifier l'ordre de validation dans DeletionService.
    /// Ces tests couvrent la régression où IsOperationInProgress était défini AVANT la validation,
    /// causant l'échec systématique de la validation.
    /// </summary>
    [TestFixture]
    public class DeletionServiceValidationOrderTests
    {
        private Mock<IClipboardHistoryManager> _mockHistoryManager = null!;
        private Mock<IDeletionUIValidator> _mockValidator = null!;
        private Mock<IDeletionUIHandler> _mockUIHandler = null!;
        private Mock<IDeletionUINotificationService> _mockNotificationService = null!;
        private Mock<IDeletionDiagnostic> _mockDiagnosticService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IViewModelOperationState> _mockViewModelState = null!;
        private DeletionService _deletionService = null!;
        private ObservableCollection<ClipboardItem> _testCollection = null!;
        private ClipboardItem _testItem = null!;

        [SetUp]
        public void SetUp()
        {
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockValidator = new Mock<IDeletionUIValidator>();
            _mockUIHandler = new Mock<IDeletionUIHandler>();
            _mockNotificationService = new Mock<IDeletionUINotificationService>();
            _mockDiagnosticService = new Mock<IDeletionDiagnostic>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockViewModelState = new Mock<IViewModelOperationState>();

            _deletionService = new DeletionService(
                _mockHistoryManager.Object,
                _mockValidator.Object,
                _mockUIHandler.Object,
                _mockNotificationService.Object,
                _mockDiagnosticService.Object,
                _mockLoggingService.Object
            );

            _testItem = new ClipboardItem
            {
                Id = 123,
                TextPreview = "Test item",
                CustomName = "Test"
            };

            _testCollection = new ObservableCollection<ClipboardItem> { _testItem };
        }

        [Test]
        public async Task DeleteItemAsync_ShouldValidateBeforeSettingOperationInProgress()
        {
            // Arrange - Simuler un état initial où aucune opération n'est en cours
            _mockViewModelState.SetupProperty(vm => vm.IsOperationInProgress, false);

            // Configurer le validator pour vérifier que IsOperationInProgress est encore false lors de la validation
            bool isOperationInProgressDuringValidation = true; // Valeur par défaut incorrecte

            _mockValidator.Setup(v => v.ValidateItem(_testItem))
                .Returns(UIValidationResult.Success());

            _mockValidator.Setup(v => v.ValidateOperationState(It.IsAny<bool>()))
                .Callback<bool>(isInProgress => isOperationInProgressDuringValidation = isInProgress)
                .Returns(UIValidationResult.Success());

            _mockValidator.Setup(v => v.ValidateHistoryManager(_mockHistoryManager.Object))
                .Returns(UIValidationResult.Success());

            // Configurer les autres services pour réussir
            _mockUIHandler.Setup(h => h.RemoveFromUI(_testCollection, _testItem))
                .Returns(UIRemovalInfo.CreateSuccess(_testItem, 0));

            _mockHistoryManager.Setup(h => h.DeleteItemAsync(_testItem.Id))
                .ReturnsAsync(true);

            // Act
            var result = await _deletionService.DeleteItemAsync(
                _testItem,
                "test-operation",
                _testCollection,
                _mockViewModelState.Object
            );

            // Assert
            Assert.That(result.Success, Is.True, $"La suppression devrait réussir. Erreur: {result.ErrorMessage}");

            // VÉRIFICATION CRITIQUE : La validation doit se faire AVANT que IsOperationInProgress soit défini à true
            Assert.That(isOperationInProgressDuringValidation, Is.False,
                "RÉGRESSION DÉTECTÉE: La validation ValidateOperationState doit être appelée AVANT que IsOperationInProgress soit défini à true");

            // Vérifier que IsOperationInProgress est remis à false à la fin (comportement du finally)
            Assert.That(_mockViewModelState.Object.IsOperationInProgress, Is.False,
                "IsOperationInProgress devrait être remis à false à la fin de l'opération (bloc finally)");
        }

        [Test]
        public async Task DeleteItemAsync_WhenValidationFails_ShouldNotSetOperationInProgress()
        {
            // Arrange - Simuler un état initial où aucune opération n'est en cours
            _mockViewModelState.SetupProperty(vm => vm.IsOperationInProgress, false);

            // Configurer le validator pour échouer
            _mockValidator.Setup(v => v.ValidateItem(_testItem))
                .Returns(UIValidationResult.Success());

            _mockValidator.Setup(v => v.ValidateOperationState(false))
                .Returns(UIValidationResult.Failure("Test validation failure"));

            _mockValidator.Setup(v => v.ValidateHistoryManager(_mockHistoryManager.Object))
                .Returns(UIValidationResult.Success());

            // Act
            var result = await _deletionService.DeleteItemAsync(
                _testItem,
                "test-operation",
                _testCollection,
                _mockViewModelState.Object
            );

            // Assert
            Assert.That(result.Success, Is.False, "La suppression devrait échouer à cause de la validation");
            Assert.That(result.ErrorMessage, Does.Contain("Test validation failure"));

            // VÉRIFICATION CRITIQUE : IsOperationInProgress ne doit PAS être défini si la validation échoue
            Assert.That(_mockViewModelState.Object.IsOperationInProgress, Is.False,
                "IsOperationInProgress ne devrait PAS être défini à true si la validation échoue");
        }

        [Test]
        public async Task DeleteItemAsync_WhenOperationAlreadyInProgress_ShouldFailValidation()
        {
            // Arrange - Simuler un état où une opération est déjà en cours
            _mockViewModelState.SetupProperty(vm => vm.IsOperationInProgress, true);

            // Configurer le validator pour détecter l'opération en cours
            _mockValidator.Setup(v => v.ValidateItem(_testItem))
                .Returns(UIValidationResult.Success());

            _mockValidator.Setup(v => v.ValidateOperationState(true))
                .Returns(UIValidationResult.Failure("Validation échouée : opération déjà en cours, suppression bloquée"));

            _mockValidator.Setup(v => v.ValidateHistoryManager(_mockHistoryManager.Object))
                .Returns(UIValidationResult.Success());

            // Act
            var result = await _deletionService.DeleteItemAsync(
                _testItem,
                "test-operation",
                _testCollection,
                _mockViewModelState.Object
            );

            // Assert
            Assert.That(result.Success, Is.False, "La suppression devrait échouer car une opération est déjà en cours");
            Assert.That(result.ErrorMessage, Does.Contain("opération déjà en cours"));

            // Vérifier que IsOperationInProgress est remis à false à la fin (même en cas d'échec)
            Assert.That(_mockViewModelState.Object.IsOperationInProgress, Is.False,
                "IsOperationInProgress devrait être remis à false même en cas d'échec de validation (bloc finally)");
        }

        [Test]
        public async Task DeleteItemAsync_ValidationOrderSequence_ShouldBeCorrect()
        {
            // Arrange
            _mockViewModelState.SetupProperty(vm => vm.IsOperationInProgress, false);

            var validationCallOrder = new List<string>();
            bool operationStateWhenValidated = true; // Valeur par défaut incorrecte

            // Traquer l'ordre des appels de validation
            _mockValidator.Setup(v => v.ValidateItem(_testItem))
                .Callback(() => {
                    validationCallOrder.Add("ValidateItem");
                    operationStateWhenValidated = _mockViewModelState.Object.IsOperationInProgress;
                })
                .Returns(UIValidationResult.Success());

            _mockValidator.Setup(v => v.ValidateOperationState(It.IsAny<bool>()))
                .Callback<bool>(isInProgress => {
                    validationCallOrder.Add($"ValidateOperationState({isInProgress})");
                })
                .Returns(UIValidationResult.Success());

            _mockValidator.Setup(v => v.ValidateHistoryManager(_mockHistoryManager.Object))
                .Callback(() => validationCallOrder.Add("ValidateHistoryManager"))
                .Returns(UIValidationResult.Success());

            // Configurer les autres services
            _mockUIHandler.Setup(h => h.RemoveFromUI(_testCollection, _testItem))
                .Returns(UIRemovalInfo.CreateSuccess(_testItem, 0));

            _mockHistoryManager.Setup(h => h.DeleteItemAsync(_testItem.Id))
                .ReturnsAsync(true);

            // Act
            var result = await _deletionService.DeleteItemAsync(
                _testItem,
                "test-operation",
                _testCollection,
                _mockViewModelState.Object
            );

            // Assert
            Assert.That(result.Success, Is.True);

            // Vérifier l'ordre des validations
            Assert.That(validationCallOrder, Does.Contain("ValidateItem"));
            Assert.That(validationCallOrder, Does.Contain("ValidateOperationState(False)"));
            Assert.That(validationCallOrder, Does.Contain("ValidateHistoryManager"));

            // VÉRIFICATION CRITIQUE : IsOperationInProgress doit être false pendant la validation
            Assert.That(operationStateWhenValidated, Is.False,
                "RÉGRESSION CRITIQUE: IsOperationInProgress doit être false pendant la validation");
        }

        [Test]
        public async Task DeleteItemAsync_ShouldSetOperationInProgressDuringExecution()
        {
            // Arrange
            _mockViewModelState.SetupProperty(vm => vm.IsOperationInProgress, false);

            bool wasSetToTrueDuringExecution = false;

            // Configurer les validations pour réussir
            _mockValidator.Setup(v => v.ValidateItem(_testItem))
                .Returns(UIValidationResult.Success());

            _mockValidator.Setup(v => v.ValidateOperationState(false))
                .Returns(UIValidationResult.Success());

            _mockValidator.Setup(v => v.ValidateHistoryManager(_mockHistoryManager.Object))
                .Returns(UIValidationResult.Success());

            // Configurer l'UI handler pour capturer l'état pendant l'exécution
            _mockUIHandler.Setup(h => h.RemoveFromUI(_testCollection, _testItem))
                .Callback(() => {
                    // Vérifier que IsOperationInProgress est true pendant l'exécution
                    wasSetToTrueDuringExecution = _mockViewModelState.Object.IsOperationInProgress;
                })
                .Returns(UIRemovalInfo.CreateSuccess(_testItem, 0));

            _mockHistoryManager.Setup(h => h.DeleteItemAsync(_testItem.Id))
                .ReturnsAsync(true);

            // Act
            var result = await _deletionService.DeleteItemAsync(
                _testItem,
                "test-operation",
                _testCollection,
                _mockViewModelState.Object
            );

            // Assert
            Assert.That(result.Success, Is.True);

            // VÉRIFICATION CRITIQUE : IsOperationInProgress doit être true pendant l'exécution
            Assert.That(wasSetToTrueDuringExecution, Is.True,
                "RÉGRESSION CRITIQUE: IsOperationInProgress doit être défini à true pendant l'exécution");

            // Vérifier que IsOperationInProgress est remis à false à la fin
            Assert.That(_mockViewModelState.Object.IsOperationInProgress, Is.False,
                "IsOperationInProgress devrait être remis à false à la fin de l'opération");
        }
    }
}
