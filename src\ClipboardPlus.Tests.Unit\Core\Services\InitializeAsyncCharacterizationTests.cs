using System;
using System.Threading.Tasks;
using System.Windows.Input;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using System.Reflection;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests de caractérisation pour InitializeAsync de GlobalShortcutService.
    /// Ces tests capturent le comportement actuel EXACT avant refactorisation.
    /// </summary>
    [TestFixture]
    public class InitializeAsyncCharacterizationTests
    {
        private GlobalShortcutService _shortcutService = null!;
        private KeyCombination _validShortcut = null!;

        [SetUp]
        public void Initialize()
        {
            _shortcutService = new GlobalShortcutService();
            _validShortcut = new KeyCombination(ModifierKeys.Control | ModifierKeys.Alt, Key.V);
        }

        [TearDown]
        public void Cleanup()
        {
            try
            {
                _shortcutService?.UnregisterShortcut();
                // Note: GlobalShortcutService n'implémente pas IDisposable
                // Ce sera corrigé dans la refactorisation
            }
            catch
            {
                // Ignorer les erreurs de nettoyage dans les tests de caractérisation
            }
        }

        #region Tests de Validation des Paramètres

        [Test]
        [Description("CARACTÉRISATION: InitializeAsync avec null doit lever ArgumentNullException")]
        public void InitializeAsync_WithNullShortcut_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentNullException>(() => _shortcutService.InitializeAsync(null!));
            Assert.That(ex!.ParamName, Is.EqualTo("defaultShortcut"));
        }

        [Test]
        [Description("CARACTÉRISATION: InitializeAsync avec raccourci valide ne doit pas lever d'exception")]
        public void InitializeAsync_WithValidShortcut_DoesNotThrowException()
        {
            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _shortcutService.InitializeAsync(_validShortcut));
        }

        #endregion

        #region Tests de Comportement Actuel

        [Test]
        [Description("CARACTÉRISATION: InitializeAsync doit définir _currentShortcut")]
        public async Task InitializeAsync_WithValidShortcut_SetsCurrentShortcut()
        {
            // Act
            await _shortcutService.InitializeAsync(_validShortcut);

            // CORRECTION : Attendre un peu pour que l'appel asynchrone interne se termine
            await Task.Delay(100);

            // Assert - Utiliser la réflexion pour vérifier l'état interne
            var currentShortcutField = typeof(GlobalShortcutService)
                .GetField("_currentShortcut", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(currentShortcutField, Is.Not.Null);

            var currentShortcut = (KeyCombination)currentShortcutField!.GetValue(_shortcutService)!;
            Assert.That(currentShortcut, Is.Not.Null);

            // CORRECTION : Vérifier aussi que l'enregistrement a réussi
            var isRegisteredField = typeof(GlobalShortcutService)
                .GetField("_isRegistered", BindingFlags.NonPublic | BindingFlags.Instance);
            var isRegistered = (bool)isRegisteredField!.GetValue(_shortcutService)!;

            if (isRegistered)
            {
                // Si l'enregistrement a réussi, _currentShortcut devrait être mis à jour
                Assert.That(currentShortcut.Key, Is.EqualTo(_validShortcut.Key));
                Assert.That(currentShortcut.Modifiers, Is.EqualTo(_validShortcut.Modifiers));
            }
            else
            {
                // Si l'enregistrement a échoué, _currentShortcut garde sa valeur par défaut
                Assert.That(currentShortcut.Modifiers, Is.EqualTo(ModifierKeys.Windows),
                    "Si l'enregistrement échoue, _currentShortcut garde sa valeur par défaut");
            }
        }

        [Test]
        [Description("CARACTÉRISATION: InitializeAsync doit tenter de créer un handle de fenêtre")]
        public async Task InitializeAsync_WithValidShortcut_AttemptsToCreateWindowHandle()
        {
            // Act
            await _shortcutService.InitializeAsync(_validShortcut);

            // Assert - Vérifier que _windowHandle a été défini
            var windowHandleField = typeof(GlobalShortcutService)
                .GetField("_windowHandle", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(windowHandleField, Is.Not.Null);

            var windowHandle = (IntPtr)windowHandleField!.GetValue(_shortcutService)!;
            // Dans un environnement de test, le handle peut être IntPtr.Zero ou une valeur factice
            // On vérifie juste que le champ a été traité
            Assert.That(windowHandle, Is.Not.Null);
        }

        [Test]
        [Description("CARACTÉRISATION: InitializeAsync doit appeler TryRegisterShortcutAsync")]
        public async Task InitializeAsync_WithValidShortcut_CallsTryRegisterShortcutAsync()
        {
            // Arrange - Créer un service avec un mock API pour éviter les appels Windows réels
            var mockApi = new MockWindowsHotkeyApi();
            var serviceWithMock = new GlobalShortcutService(_validShortcut, mockApi);

            // Act
            await serviceWithMock.InitializeAsync(_validShortcut);

            // CORRECTION : Attendre un peu pour que l'appel asynchrone interne se termine
            await Task.Delay(100);

            // Assert - Vérifier que l'API mock a été appelée
            Assert.That(mockApi.RegisterHotKeyCalled, Is.True,
                "TryRegisterShortcutAsync devrait avoir été appelé");
        }

        #endregion

        #region Tests de Gestion d'Erreurs

        [Test]
        [Description("CARACTÉRISATION: InitializeAsync ne doit pas propager les exceptions internes")]
        public void InitializeAsync_WithInternalException_DoesNotPropagateException()
        {
            // Arrange - Utiliser un raccourci qui pourrait causer des problèmes
            var problematicShortcut = new KeyCombination(ModifierKeys.None, Key.None);

            // Act & Assert - L'exception ne doit pas être propagée
            Assert.DoesNotThrowAsync(async () => await _shortcutService.InitializeAsync(problematicShortcut));
        }

        #endregion

        #region Tests de Threading

        [Test]
        [Description("CARACTÉRISATION: InitializeAsync doit gérer l'absence de Dispatcher")]
        public void InitializeAsync_WithoutDispatcher_HandlesGracefully()
        {
            // Note: Ce test est difficile à implémenter sans modifier l'environnement
            // Il documente le comportement attendu pour référence future

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _shortcutService.InitializeAsync(_validShortcut));
        }

        #endregion

        #region Tests de Performance

        [Test]
        [Description("CARACTÉRISATION: InitializeAsync doit se terminer dans un délai raisonnable")]
        [Timeout(5000)] // 5 secondes maximum
        public async Task InitializeAsync_WithValidShortcut_CompletesWithinTimeout()
        {
            // Act & Assert
            await _shortcutService.InitializeAsync(_validShortcut);
            Assert.Pass("InitializeAsync s'est terminé dans le délai imparti");
        }

        #endregion
    }

    /// <summary>
    /// Mock simple de IWindowsHotkeyApi pour les tests de caractérisation
    /// </summary>
    public class MockWindowsHotkeyApi : IWindowsHotkeyApi
    {
        public bool RegisterHotKeyCalled { get; private set; }
        public bool UnregisterHotKeyCalled { get; private set; }

        public bool RegisterHotKey(IntPtr hWnd, int id, uint fsModifiers, uint vk)
        {
            RegisterHotKeyCalled = true;
            return true; // Simuler un succès
        }

        public bool UnregisterHotKey(IntPtr hWnd, int id)
        {
            UnregisterHotKeyCalled = true;
            return true; // Simuler un succès
        }
    }
}
