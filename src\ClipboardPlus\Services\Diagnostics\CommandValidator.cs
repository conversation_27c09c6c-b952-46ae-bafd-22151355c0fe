using System;
using System.Collections.Generic;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Diagnostics;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Services.Diagnostics
{
    /// <summary>
    /// Validateur de commandes qui vérifie leur disponibilité et état
    /// </summary>
    public class CommandValidator : ICommandValidator
    {
        private readonly ILoggingService _loggingService;

        public CommandValidator(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        public CommandValidationResult ValidateCommands(ClipboardHistoryViewModel viewModel, ClipboardItem? item)
        {
            var commandStates = new Dictionary<string, bool>();
            var validationErrors = new List<string>();
            var validationWarnings = new List<string>();

            try
            {
                if (viewModel == null)
                {
                    validationErrors.Add("ViewModel est null");
                    return new CommandValidationResult(commandStates, validationErrors, validationWarnings);
                }

                // Validation des commandes principales
                ValidateDeleteCommand(viewModel, item, commandStates, validationErrors, validationWarnings);
                ValidateClearAllCommand(viewModel, commandStates, validationErrors, validationWarnings);
                ValidatePasteCommand(viewModel, commandStates, validationErrors, validationWarnings);
                ValidateTogglePinCommand(viewModel, item, commandStates, validationErrors, validationWarnings);

            }
            catch (Exception ex)
            {
                validationErrors.Add($"Erreur lors de la validation des commandes: {ex.Message}");
                _loggingService?.LogError($"Erreur CommandValidator.ValidateCommands: {ex.Message}", ex);
            }

            return new CommandValidationResult(commandStates, validationErrors, validationWarnings);
        }

        private void ValidateDeleteCommand(ClipboardHistoryViewModel viewModel, ClipboardItem? item, 
            Dictionary<string, bool> commandStates, List<string> errors, List<string> warnings)
        {
            try
            {
                var command = viewModel.SupprimerElementCommand;
                var isAvailable = command != null;
                var canExecute = isAvailable && command!.CanExecute(item);

                commandStates["SupprimerElement"] = canExecute;

                if (!isAvailable)
                    errors.Add("Commande SupprimerElement non disponible");
                else if (!canExecute)
                    warnings.Add("Commande SupprimerElement ne peut pas être exécutée");

                if (item == null && canExecute)
                    warnings.Add("Commande SupprimerElement peut être exécutée avec un élément null");
            }
            catch (Exception ex)
            {
                errors.Add($"Erreur validation SupprimerElement: {ex.Message}");
                commandStates["SupprimerElement"] = false;
            }
        }

        private void ValidateClearAllCommand(ClipboardHistoryViewModel viewModel, 
            Dictionary<string, bool> commandStates, List<string> errors, List<string> warnings)
        {
            try
            {
                var command = viewModel.SupprimerToutCommand;
                var isAvailable = command != null;
                var canExecute = isAvailable && command!.CanExecute(null);

                commandStates["SupprimerTout"] = canExecute;

                if (!isAvailable)
                    errors.Add("Commande SupprimerTout non disponible");
                else if (!canExecute)
                    warnings.Add("Commande SupprimerTout ne peut pas être exécutée");

                // Vérification de cohérence
                if (canExecute && (viewModel.HistoryItems?.Count ?? 0) == 0)
                    warnings.Add("Commande SupprimerTout disponible mais aucun élément à supprimer");
            }
            catch (Exception ex)
            {
                errors.Add($"Erreur validation SupprimerTout: {ex.Message}");
                commandStates["SupprimerTout"] = false;
            }
        }

        private void ValidatePasteCommand(ClipboardHistoryViewModel viewModel, 
            Dictionary<string, bool> commandStates, List<string> errors, List<string> warnings)
        {
            try
            {
                var command = viewModel.PasteSelectedItemCommand;
                var isAvailable = command != null;
                var canExecute = isAvailable && command!.CanExecute(null);

                commandStates["PasteSelectedItem"] = canExecute;

                if (!isAvailable)
                    errors.Add("Commande PasteSelectedItem non disponible");
                else if (!canExecute)
                    warnings.Add("Commande PasteSelectedItem ne peut pas être exécutée");

                // Vérification de cohérence
                if (canExecute && viewModel.SelectedClipboardItem == null)
                    warnings.Add("Commande PasteSelectedItem disponible mais aucun élément sélectionné");
            }
            catch (Exception ex)
            {
                errors.Add($"Erreur validation PasteSelectedItem: {ex.Message}");
                commandStates["PasteSelectedItem"] = false;
            }
        }

        private void ValidateTogglePinCommand(ClipboardHistoryViewModel viewModel, ClipboardItem? item,
            Dictionary<string, bool> commandStates, List<string> errors, List<string> warnings)
        {
            try
            {
                var command = viewModel.BasculerEpinglageCommand;
                var isAvailable = command != null;
                var canExecute = isAvailable && command!.CanExecute(item);

                commandStates["BasculerEpinglage"] = canExecute;

                if (!isAvailable)
                    errors.Add("Commande BasculerEpinglage non disponible");
                else if (!canExecute)
                    warnings.Add("Commande BasculerEpinglage ne peut pas être exécutée");

                if (item == null && canExecute)
                    warnings.Add("Commande BasculerEpinglage peut être exécutée avec un élément null");
            }
            catch (Exception ex)
            {
                errors.Add($"Erreur validation BasculerEpinglage: {ex.Message}");
                commandStates["BasculerEpinglage"] = false;
            }
        }
    }
}
