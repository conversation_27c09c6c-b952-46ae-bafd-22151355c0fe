// ============================================================================
// VISIBILITY VIEWMODEL MANAGER IMPLEMENTATION - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Implémentation concrète de la gestion de la visibilité
// 📋 RESPONSABILITÉ : Gestion des propriétés ShowTitles, ShowTimestamps, modes
// 🏗️ ARCHITECTURE : Extraction de la logique de visibilité dispersée
//
// ============================================================================

using System;
using ClipboardPlus.UI.ViewModels.Managers.Interfaces;
using CommunityToolkit.Mvvm.ComponentModel;

namespace ClipboardPlus.UI.ViewModels.Managers.Implementations
{
    /// <summary>
    /// Implémentation concrète du manager de gestion de la visibilité.
    /// 
    /// Cette classe gère tous les aspects de visibilité de l'interface utilisateur
    /// et maintient la synchronisation avec les paramètres utilisateur.
    /// </summary>
    public class VisibilityViewModelManager : ObservableObject, IVisibilityViewModelManager
    {
        #region Champs Privés

        private bool _showTitles = true;
        private bool _showTimestamps = true;
        private bool _showContentTypeIcons = true;
        private bool _showPinIndicators = true;
        private bool _showImagePreviews = true;
        private bool _isSearchBarVisible = true;
        private bool _isToolbarVisible = true;
        private bool _isStatusBarVisible = true;
        private bool _isDetailsPanelVisible = false;
        private bool _isCompactModeEnabled = false;
        private ViewMode _currentViewMode = ViewMode.List;
        private ItemSize _currentItemSize = ItemSize.Medium;
        private int _gridColumnCount = 3;
        private bool _isAutoDisplayEnabled = true;
        private bool _isDisposed;

        #endregion

        #region Propriétés de Visibilité Principales (IVisibilityViewModelManager)

        /// <summary>
        /// Indique si les titres des éléments doivent être affichés.
        /// </summary>
        public bool ShowTitles
        {
            get => _showTitles;
            set
            {
                if (SetProperty(ref _showTitles, value))
                {
                    VisibilityChanged?.Invoke(this, new VisibilityChangedEventArgs(
                        nameof(ShowTitles), !value, value));
                }
            }
        }

        /// <summary>
        /// Indique si les timestamps des éléments doivent être affichés.
        /// </summary>
        public bool ShowTimestamps
        {
            get => _showTimestamps;
            set
            {
                if (SetProperty(ref _showTimestamps, value))
                {
                    VisibilityChanged?.Invoke(this, new VisibilityChangedEventArgs(
                        nameof(ShowTimestamps), !value, value));
                }
            }
        }

        /// <summary>
        /// Indique si les icônes de type de contenu doivent être affichées.
        /// </summary>
        public bool ShowContentTypeIcons
        {
            get => _showContentTypeIcons;
            set
            {
                if (SetProperty(ref _showContentTypeIcons, value))
                {
                    VisibilityChanged?.Invoke(this, new VisibilityChangedEventArgs(
                        nameof(ShowContentTypeIcons), !value, value));
                }
            }
        }

        /// <summary>
        /// Indique si les indicateurs d'épinglage doivent être affichés.
        /// </summary>
        public bool ShowPinIndicators
        {
            get => _showPinIndicators;
            set
            {
                if (SetProperty(ref _showPinIndicators, value))
                {
                    VisibilityChanged?.Invoke(this, new VisibilityChangedEventArgs(
                        nameof(ShowPinIndicators), !value, value));
                }
            }
        }

        /// <summary>
        /// Indique si les prévisualisations d'images doivent être affichées.
        /// </summary>
        public bool ShowImagePreviews
        {
            get => _showImagePreviews;
            set
            {
                if (SetProperty(ref _showImagePreviews, value))
                {
                    VisibilityChanged?.Invoke(this, new VisibilityChangedEventArgs(
                        nameof(ShowImagePreviews), !value, value));
                }
            }
        }

        #endregion

        #region Propriétés d'État de l'Interface (IVisibilityViewModelManager)

        /// <summary>
        /// Indique si la barre de recherche est visible.
        /// </summary>
        public bool IsSearchBarVisible
        {
            get => _isSearchBarVisible;
            set => SetProperty(ref _isSearchBarVisible, value);
        }

        /// <summary>
        /// Indique si la barre d'outils est visible.
        /// </summary>
        public bool IsToolbarVisible
        {
            get => _isToolbarVisible;
            set => SetProperty(ref _isToolbarVisible, value);
        }

        /// <summary>
        /// Indique si la barre de statut est visible.
        /// </summary>
        public bool IsStatusBarVisible
        {
            get => _isStatusBarVisible;
            set => SetProperty(ref _isStatusBarVisible, value);
        }

        /// <summary>
        /// Indique si le panneau de détails est visible.
        /// </summary>
        public bool IsDetailsPanelVisible
        {
            get => _isDetailsPanelVisible;
            set => SetProperty(ref _isDetailsPanelVisible, value);
        }

        /// <summary>
        /// Indique si le mode compact est activé.
        /// </summary>
        public bool IsCompactModeEnabled
        {
            get => _isCompactModeEnabled;
            set
            {
                if (SetProperty(ref _isCompactModeEnabled, value))
                {
                    ApplyCompactModeSettings(value);
                }
            }
        }

        #endregion

        #region Propriétés de Mode d'Affichage (IVisibilityViewModelManager)

        /// <summary>
        /// Mode d'affichage actuel de la liste.
        /// </summary>
        public ViewMode CurrentViewMode
        {
            get => _currentViewMode;
            set
            {
                if (SetProperty(ref _currentViewMode, value))
                {
                    ViewModeChanged?.Invoke(this, new ViewModeChangedEventArgs(
                        _currentViewMode, value));
                }
            }
        }

        /// <summary>
        /// Taille des éléments dans la vue.
        /// </summary>
        public ItemSize CurrentItemSize
        {
            get => _currentItemSize;
            set
            {
                if (SetProperty(ref _currentItemSize, value))
                {
                    ItemSizeChanged?.Invoke(this, new ItemSizeChangedEventArgs(
                        _currentItemSize, value));
                }
            }
        }

        /// <summary>
        /// Nombre de colonnes pour l'affichage en grille.
        /// </summary>
        public int GridColumnCount
        {
            get => _gridColumnCount;
            set => SetProperty(ref _gridColumnCount, Math.Max(1, Math.Min(10, value)));
        }

        /// <summary>
        /// Indique si l'affichage automatique est activé.
        /// </summary>
        public bool IsAutoDisplayEnabled
        {
            get => _isAutoDisplayEnabled;
            set => SetProperty(ref _isAutoDisplayEnabled, value);
        }

        #endregion

        #region Événements (IVisibilityViewModelManager)

        /// <summary>
        /// Événement déclenché lorsqu'une propriété de visibilité change.
        /// </summary>
        public event EventHandler<VisibilityChangedEventArgs>? VisibilityChanged;

        /// <summary>
        /// Événement déclenché lorsque le mode d'affichage change.
        /// </summary>
        public event EventHandler<ViewModeChangedEventArgs>? ViewModeChanged;

        /// <summary>
        /// Événement déclenché lorsque la taille des éléments change.
        /// </summary>
        public event EventHandler<ItemSizeChangedEventArgs>? ItemSizeChanged;

        #endregion

        #region Méthodes de Gestion de la Visibilité (IVisibilityViewModelManager)

        /// <summary>
        /// Bascule l'affichage des titres.
        /// </summary>
        public void ToggleShowTitles()
        {
            ShowTitles = !ShowTitles;
        }

        /// <summary>
        /// Bascule l'affichage des timestamps.
        /// </summary>
        public void ToggleShowTimestamps()
        {
            ShowTimestamps = !ShowTimestamps;
        }

        /// <summary>
        /// Bascule l'affichage des icônes de type de contenu.
        /// </summary>
        public void ToggleShowContentTypeIcons()
        {
            ShowContentTypeIcons = !ShowContentTypeIcons;
        }

        /// <summary>
        /// Bascule l'affichage des indicateurs d'épinglage.
        /// </summary>
        public void ToggleShowPinIndicators()
        {
            ShowPinIndicators = !ShowPinIndicators;
        }

        /// <summary>
        /// Bascule l'affichage des prévisualisations d'images.
        /// </summary>
        public void ToggleShowImagePreviews()
        {
            ShowImagePreviews = !ShowImagePreviews;
        }

        /// <summary>
        /// Applique un profil de visibilité prédéfini.
        /// </summary>
        /// <param name="profile">Profil à appliquer</param>
        public void ApplyVisibilityProfile(VisibilityProfile profile)
        {
            if (_isDisposed) return;

            switch (profile)
            {
                case VisibilityProfile.Minimal:
                    ShowTitles = false;
                    ShowTimestamps = false;
                    ShowContentTypeIcons = false;
                    ShowPinIndicators = true; // Toujours visible pour l'épinglage
                    ShowImagePreviews = false;
                    IsCompactModeEnabled = true;
                    break;

                case VisibilityProfile.Standard:
                    ShowTitles = true;
                    ShowTimestamps = true;
                    ShowContentTypeIcons = true;
                    ShowPinIndicators = true;
                    ShowImagePreviews = true;
                    IsCompactModeEnabled = false;
                    break;

                case VisibilityProfile.Detailed:
                    ShowTitles = true;
                    ShowTimestamps = true;
                    ShowContentTypeIcons = true;
                    ShowPinIndicators = true;
                    ShowImagePreviews = true;
                    IsDetailsPanelVisible = true;
                    IsCompactModeEnabled = false;
                    break;

                case VisibilityProfile.Developer:
                    ShowTitles = true;
                    ShowTimestamps = true;
                    ShowContentTypeIcons = true;
                    ShowPinIndicators = true;
                    ShowImagePreviews = true;
                    IsDetailsPanelVisible = true;
                    IsStatusBarVisible = true;
                    IsCompactModeEnabled = false;
                    break;
            }
        }

        #endregion

        #region Méthodes de Gestion des Modes (IVisibilityViewModelManager)

        /// <summary>
        /// Change le mode d'affichage.
        /// </summary>
        /// <param name="newMode">Nouveau mode d'affichage</param>
        public void ChangeViewMode(ViewMode newMode)
        {
            CurrentViewMode = newMode;
        }

        /// <summary>
        /// Change la taille des éléments.
        /// </summary>
        /// <param name="newSize">Nouvelle taille des éléments</param>
        public void ChangeItemSize(ItemSize newSize)
        {
            CurrentItemSize = newSize;
        }

        /// <summary>
        /// Bascule entre les modes d'affichage disponibles.
        /// </summary>
        public void CycleViewModes()
        {
            CurrentViewMode = CurrentViewMode switch
            {
                ViewMode.List => ViewMode.Grid,
                ViewMode.Grid => ViewMode.Tiles,
                ViewMode.Tiles => ViewMode.Details,
                ViewMode.Details => ViewMode.List,
                _ => ViewMode.List
            };
        }

        /// <summary>
        /// Bascule entre les tailles d'éléments disponibles.
        /// </summary>
        public void CycleItemSizes()
        {
            CurrentItemSize = CurrentItemSize switch
            {
                ItemSize.Small => ItemSize.Medium,
                ItemSize.Medium => ItemSize.Large,
                ItemSize.Large => ItemSize.ExtraLarge,
                ItemSize.ExtraLarge => ItemSize.Small,
                _ => ItemSize.Medium
            };
        }

        /// <summary>
        /// Active ou désactive le mode compact.
        /// </summary>
        /// <param name="enabled">True pour activer le mode compact</param>
        public void SetCompactMode(bool enabled)
        {
            IsCompactModeEnabled = enabled;
        }

        #endregion

        #region Méthodes Utilitaires (IVisibilityViewModelManager)

        /// <summary>
        /// Vérifie si un élément doit être visible selon les paramètres actuels.
        /// </summary>
        /// <param name="elementType">Type d'élément à vérifier</param>
        /// <returns>True si l'élément doit être visible</returns>
        public bool ShouldShowElement(UIElementType elementType)
        {
            return elementType switch
            {
                UIElementType.Title => ShowTitles,
                UIElementType.Timestamp => ShowTimestamps,
                UIElementType.ContentTypeIcon => ShowContentTypeIcons,
                UIElementType.PinIndicator => ShowPinIndicators,
                UIElementType.ImagePreview => ShowImagePreviews,
                UIElementType.SearchBar => IsSearchBarVisible,
                UIElementType.Toolbar => IsToolbarVisible,
                UIElementType.StatusBar => IsStatusBarVisible,
                UIElementType.DetailsPanel => IsDetailsPanelVisible,
                _ => true
            };
        }

        /// <summary>
        /// Obtient la configuration de visibilité actuelle.
        /// </summary>
        /// <returns>Configuration de visibilité</returns>
        public VisibilityConfiguration GetCurrentConfiguration()
        {
            return new VisibilityConfiguration
            {
                ShowTitles = ShowTitles,
                ShowTimestamps = ShowTimestamps,
                ShowContentTypeIcons = ShowContentTypeIcons,
                ShowPinIndicators = ShowPinIndicators,
                ShowImagePreviews = ShowImagePreviews,
                ViewMode = CurrentViewMode,
                ItemSize = CurrentItemSize,
                IsCompactModeEnabled = IsCompactModeEnabled
            };
        }

        /// <summary>
        /// Applique une configuration de visibilité.
        /// </summary>
        /// <param name="configuration">Configuration à appliquer</param>
        public void ApplyConfiguration(VisibilityConfiguration configuration)
        {
            if (_isDisposed || configuration == null) return;

            ShowTitles = configuration.ShowTitles;
            ShowTimestamps = configuration.ShowTimestamps;
            ShowContentTypeIcons = configuration.ShowContentTypeIcons;
            ShowPinIndicators = configuration.ShowPinIndicators;
            ShowImagePreviews = configuration.ShowImagePreviews;
            CurrentViewMode = configuration.ViewMode;
            CurrentItemSize = configuration.ItemSize;
            IsCompactModeEnabled = configuration.IsCompactModeEnabled;
        }

        #endregion

        #region Méthodes Privées

        /// <summary>
        /// Applique les paramètres du mode compact.
        /// </summary>
        /// <param name="enabled">True si le mode compact est activé</param>
        private void ApplyCompactModeSettings(bool enabled)
        {
            if (enabled)
            {
                // Mode compact : réduire l'affichage
                CurrentItemSize = ItemSize.Small;
                ShowTimestamps = false;
                ShowContentTypeIcons = false;
                IsDetailsPanelVisible = false;
            }
            else
            {
                // Mode normal : restaurer l'affichage
                CurrentItemSize = ItemSize.Medium;
                ShowTimestamps = true;
                ShowContentTypeIcons = true;
            }
        }

        #endregion

        #region Méthodes de Synchronisation (IVisibilityViewModelManager)

        /// <summary>
        /// Synchronise les paramètres de visibilité avec les paramètres utilisateur.
        /// </summary>
        public void SynchronizeWithUserSettings()
        {
            if (_isDisposed) return;

            // Cette méthode sera implémentée lors de l'intégration avec le système de paramètres
            // Elle chargera les préférences utilisateur depuis la configuration
        }

        /// <summary>
        /// Sauvegarde les paramètres de visibilité actuels.
        /// </summary>
        public void SaveVisibilitySettings()
        {
            if (_isDisposed) return;

            // Cette méthode sera implémentée lors de l'intégration avec le système de paramètres
            // Elle sauvegardera les préférences utilisateur dans la configuration
        }

        /// <summary>
        /// Restaure les paramètres de visibilité par défaut.
        /// </summary>
        public void RestoreDefaultSettings()
        {
            if (_isDisposed) return;

            ApplyVisibilityProfile(VisibilityProfile.Standard);
        }

        #endregion

        #region Méthodes d'Initialisation et Nettoyage (IVisibilityViewModelManager)

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        public void Initialize()
        {
            if (_isDisposed) return;

            // Charger les paramètres utilisateur
            SynchronizeWithUserSettings();
        }

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        public void Cleanup()
        {
            if (_isDisposed) return;

            // Sauvegarder les paramètres avant nettoyage
            SaveVisibilitySettings();
        }

        /// <summary>
        /// Libère les ressources utilisées par le manager.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            Cleanup();
            _isDisposed = true;
        }

        #endregion
    }
}
