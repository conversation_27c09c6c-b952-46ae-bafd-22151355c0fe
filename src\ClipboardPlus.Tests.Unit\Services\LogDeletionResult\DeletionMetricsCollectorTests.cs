using System;
using System.Threading;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Implementations;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Tests.Unit.Helpers;

namespace ClipboardPlus.Tests.Unit.Services.LogDeletionResult
{
    [TestFixture]
    public class DeletionMetricsCollectorTests
    {
        private DeletionMetricsCollector _collector = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _collector = new DeletionMetricsCollector(_mockLoggingService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _collector?.ResetMetrics();
        }

        #region RecordDeletionAttempt Tests

        [Test]
        public void RecordDeletionAttempt_WithValidContext_RecordsAttempt()
        {
            // Arrange
            var context = CreateTestDeletionContext(success: true);

            // Act
            _collector.RecordDeletionAttempt(context);

            // Assert
            var metrics = _collector.GetMetrics();
            Assert.That(metrics, Is.Not.Null);
            Assert.That(metrics.TotalDeletions, Is.EqualTo(1));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(1));
            Assert.That(metrics.FailedDeletions, Is.EqualTo(0));
        }

        [Test]
        public void RecordDeletionAttempt_WithFailedContext_RecordsFailure()
        {
            // Arrange
            var context = CreateTestDeletionContext(success: false);

            // Act
            _collector.RecordDeletionAttempt(context);

            // Assert
            var metrics = _collector.GetMetrics();
            Assert.That(metrics, Is.Not.Null);
            Assert.That(metrics.TotalDeletions, Is.EqualTo(1));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(0));
            Assert.That(metrics.FailedDeletions, Is.EqualTo(1));
        }

        [Test]
        public void RecordDeletionAttempt_WithNullContext_HandlesGracefully()
        {
            // Act & Assert - Should not throw
            Assert.DoesNotThrow(() => _collector.RecordDeletionAttempt(null!));

            // Verify warning was logged
            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("contexte null"))),
                Times.Once);
        }

        [Test]
        public void RecordDeletionAttempt_MultipleAttempts_AccumulatesCorrectly()
        {
            // Arrange
            var successContext1 = CreateTestDeletionContext(success: true);
            var successContext2 = CreateTestDeletionContext(success: true);
            var failureContext = CreateTestDeletionContext(success: false);

            // Act
            _collector.RecordDeletionAttempt(successContext1);
            _collector.RecordDeletionAttempt(successContext2);
            _collector.RecordDeletionAttempt(failureContext);

            // Assert
            var metrics = _collector.GetMetrics();
            Assert.That(metrics.TotalDeletions, Is.EqualTo(3));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(2));
            Assert.That(metrics.FailedDeletions, Is.EqualTo(1));
        }

        #endregion

        #region RecordLoggingResult Tests

        [Test]
        public void RecordLoggingResult_WithValidResult_RecordsResult()
        {
            // Arrange
            var result = CreateTestLoggingResult(success: true, duration: TimeSpan.FromMilliseconds(100));

            // Act
            _collector.RecordLoggingResult(result);

            // Assert
            var performanceMetrics = _collector.GetPerformanceMetrics();
            Assert.That(performanceMetrics, Is.Not.Null);
            Assert.That(performanceMetrics.TotalLoggingOperations, Is.EqualTo(1));
            Assert.That(performanceMetrics.SuccessfulOperations, Is.EqualTo(1));
        }

        [Test]
        public void RecordLoggingResult_WithNullResult_HandlesGracefully()
        {
            // Act & Assert - Should not throw
            Assert.DoesNotThrow(() => _collector.RecordLoggingResult(null!));

            // Verify warning was logged
            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("résultat null"))),
                Times.Once);
        }

        [Test]
        public void RecordLoggingResult_MultipleResults_CalculatesAverages()
        {
            // Arrange
            var result1 = CreateTestLoggingResult(success: true, duration: TimeSpan.FromMilliseconds(100));
            var result2 = CreateTestLoggingResult(success: true, duration: TimeSpan.FromMilliseconds(200));
            var result3 = CreateTestLoggingResult(success: false, duration: TimeSpan.FromMilliseconds(50));

            // Act
            _collector.RecordLoggingResult(result1);
            _collector.RecordLoggingResult(result2);
            _collector.RecordLoggingResult(result3);

            // Assert
            var performanceMetrics = _collector.GetPerformanceMetrics();
            Assert.That(performanceMetrics.TotalLoggingOperations, Is.EqualTo(3));
            Assert.That(performanceMetrics.SuccessfulOperations, Is.EqualTo(2));
            Assert.That(performanceMetrics.FailedOperations, Is.EqualTo(1));
            Assert.That(performanceMetrics.AverageDuration.TotalMilliseconds, Is.EqualTo(116.67).Within(0.1));
        }

        #endregion

        #region RecordPerformanceMetrics Tests

        [Test]
        public void RecordPerformanceMetrics_WithValidMetrics_RecordsMetrics()
        {
            // Arrange
            var metrics = CreateTestPerformanceMetrics();

            // Act
            _collector.RecordPerformanceMetrics(metrics);

            // Assert
            var performanceMetrics = _collector.GetPerformanceMetrics();
            Assert.That(performanceMetrics, Is.Not.Null);
            // Les métriques de performance sont intégrées dans les résultats globaux
        }

        [Test]
        public void RecordPerformanceMetrics_WithNullMetrics_HandlesGracefully()
        {
            // Act & Assert - Should not throw
            Assert.DoesNotThrow(() => _collector.RecordPerformanceMetrics(null!));

            // Verify warning was logged
            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("métriques null"))),
                Times.Once);
        }

        #endregion

        #region RecordError Tests

        [Test]
        public void RecordError_WithValidError_RecordsError()
        {
            // Arrange
            var error = CreateTestDeletionError();

            // Act
            _collector.RecordError(error);

            // Assert
            var errorMetrics = _collector.GetErrorMetrics();
            Assert.That(errorMetrics, Is.Not.Null);
            Assert.That(errorMetrics.TotalErrors, Is.EqualTo(1));
            Assert.That(errorMetrics.ErrorsByType, Is.Not.Null);
            Assert.That(errorMetrics.ErrorsByType.ContainsKey(error.ErrorType), Is.True);
        }

        [Test]
        public void RecordError_WithNullError_HandlesGracefully()
        {
            // Act & Assert - Should not throw
            Assert.DoesNotThrow(() => _collector.RecordError(null!));

            // Verify warning was logged
            _mockLoggingService.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("erreur null"))),
                Times.Once);
        }

        [Test]
        public void RecordError_MultipleErrors_GroupsByType()
        {
            // Arrange
            var error1 = CreateTestDeletionError(LoggingErrorType.ValidationError);
            var error2 = CreateTestDeletionError(LoggingErrorType.ValidationError);
            var error3 = CreateTestDeletionError(LoggingErrorType.FormattingError);

            // Act
            _collector.RecordError(error1);
            _collector.RecordError(error2);
            _collector.RecordError(error3);

            // Assert
            var errorMetrics = _collector.GetErrorMetrics();
            Assert.That(errorMetrics.TotalErrors, Is.EqualTo(3));
            Assert.That(errorMetrics.ErrorsByType[LoggingErrorType.ValidationError], Is.EqualTo(2));
            Assert.That(errorMetrics.ErrorsByType[LoggingErrorType.FormattingError], Is.EqualTo(1));
        }

        #endregion

        #region GetMetrics Tests

        [Test]
        public void GetMetrics_InitialState_ReturnsEmptyMetrics()
        {
            // Act
            var metrics = _collector.GetMetrics();

            // Assert
            Assert.That(metrics, Is.Not.Null);
            Assert.That(metrics.TotalDeletions, Is.EqualTo(0));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(0));
            Assert.That(metrics.FailedDeletions, Is.EqualTo(0));
            Assert.That(metrics.TotalErrors, Is.EqualTo(0));
            Assert.That(metrics.CollectionStartTime, Is.LessThanOrEqualTo(DateTime.Now));
        }

        [Test]
        public void GetMetrics_AfterOperations_ReturnsAccurateMetrics()
        {
            // Arrange
            var context = CreateTestDeletionContext(success: true);
            var result = CreateTestLoggingResult(success: true, duration: TimeSpan.FromMilliseconds(150));
            var error = CreateTestDeletionError();

            // Act
            _collector.RecordDeletionAttempt(context);
            _collector.RecordLoggingResult(result);
            _collector.RecordError(error);

            // Assert
            var metrics = _collector.GetMetrics();
            Assert.That(metrics.TotalDeletions, Is.EqualTo(1));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(1));
            Assert.That(metrics.TotalErrors, Is.EqualTo(1));
            Assert.That(metrics.AverageLoggingDuration, Is.Not.Null);
        }

        #endregion

        #region ResetMetrics Tests

        [Test]
        public void ResetMetrics_AfterOperations_ClearsAllMetrics()
        {
            // Arrange
            var context = CreateTestDeletionContext(success: true);
            var result = CreateTestLoggingResult(success: true, duration: TimeSpan.FromMilliseconds(100));
            _collector.RecordDeletionAttempt(context);
            _collector.RecordLoggingResult(result);

            // Act
            _collector.ResetMetrics();

            // Assert
            var metrics = _collector.GetMetrics();
            Assert.That(metrics.TotalDeletions, Is.EqualTo(0));
            Assert.That(metrics.SuccessfulDeletions, Is.EqualTo(0));
            Assert.That(metrics.FailedDeletions, Is.EqualTo(0));
        }

        // SUPPRIMÉ : Test ResetPerformanceMetrics_OnlyResetsPerformanceData - méthode supprimée

        #endregion

        // SUPPRIMÉ : Tests GenerateMetricsReport - méthode supprimée

        #region Helper Methods

        private DeletionResultContext CreateTestDeletionContext(bool success)
        {
            var item = new ClipboardItem
            {
                Id = 1,
                TextPreview = "Test item",
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content")
            };

            return new DeletionResultContext
            {
                Success = success,
                Item = item,
                Message = success ? "Success" : "Failed",
                Timestamp = DateTime.Now,
                OperationId = Guid.NewGuid()
            };
        }

        private DeletionLoggingResult CreateTestLoggingResult(bool success, TimeSpan duration)
        {
            return new DeletionLoggingResult
            {
                IsSuccessful = success,
                Duration = duration,
                Timestamp = DateTime.Now,
                Message = success ? "Success" : "Test error"
            };
        }

        private OperationPerformanceMetrics CreateTestPerformanceMetrics()
        {
            return new OperationPerformanceMetrics
            {
                TotalDuration = TimeSpan.FromMilliseconds(200),
                ValidationDuration = TimeSpan.FromMilliseconds(50),
                FormattingDuration = TimeSpan.FromMilliseconds(75),
                WritingDuration = TimeSpan.FromMilliseconds(75)
            };
        }

        private DeletionLoggingError CreateTestDeletionError(LoggingErrorType errorType = LoggingErrorType.UnknownError)
        {
            return new DeletionLoggingError
            {
                ErrorType = errorType,
                Message = "Test error message",
                Exception = new InvalidOperationException("Test exception"),
                Timestamp = DateTime.Now
            };
        }

        #endregion
    }
}
