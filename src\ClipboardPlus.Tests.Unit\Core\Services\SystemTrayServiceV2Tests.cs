using System;
using System.Drawing;
using System.Threading;
using System.Windows.Controls;
using System.Windows.Forms;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;
using ClipboardPlus.Core.Services.Windows;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests unitaires pour les nouvelles méthodes V2 de SystemTrayService.
    /// Ces tests vérifient que les nouvelles méthodes utilisent l'injection de dépendances
    /// au lieu du Service Locator pattern.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class SystemTrayServiceV2Tests
    {
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<IServiceProvider> _mockServiceProvider = null!;
        private Mock<ISystemTrayOrchestrator> _mockOrchestrator = null!;
        private SystemTrayService _systemTrayService = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockOrchestrator = new Mock<ISystemTrayOrchestrator>();

            // Configurer le service provider pour retourner les services nécessaires
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService))).Returns(_mockLoggingService.Object);

            // Utiliser des extensions methods pour les services génériques
            var mockThreadValidator = new Mock<IThreadValidator>();
            var mockCleanupService = new Mock<INotifyIconCleanupService>();
            var mockNotifyIconFactory = new Mock<INotifyIconFactory>();
            var mockIconLoader = new Mock<IIconResourceLoader>();
            var mockMenuBuilder = new Mock<IContextMenuBuilder>();
            var mockVisibilityManager = new Mock<IVisibilityManager>();
            var mockNotificationService = new Mock<IStartupNotificationService>();
            var mockHistoryWindowService = new Mock<IHistoryWindowService>();
            var mockSettingsWindowService = new Mock<ISettingsWindowService>();

            // Configurer les mocks pour qu'ils fonctionnent correctement
            var realNotifyIcon = new NotifyIcon();
            var mockIcon = SystemIcons.Application;
            var mockContextMenu = new ContextMenu();

            mockCleanupService.Setup(x => x.RequiresCleanup(It.IsAny<NotifyIcon>())).Returns(false);
            mockNotifyIconFactory.Setup(x => x.CreateNotifyIcon("ClipboardPlus", It.IsAny<bool>())).Returns(realNotifyIcon);
            mockIconLoader.Setup(x => x.LoadIconFromResource("pack://application:,,,/ClipboardPlus;component/Assets/ApplicationIcon.ico")).Returns(mockIcon);
            mockMenuBuilder.Setup(x => x.BuildContextMenu(It.IsAny<Action>(), It.IsAny<Action>(), It.IsAny<Action>())).Returns(mockContextMenu);
            mockVisibilityManager.Setup(x => x.ConfigureInitialVisibility(realNotifyIcon)).Returns(true);

            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IThreadValidator))).Returns(mockThreadValidator.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(INotifyIconCleanupService))).Returns(mockCleanupService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(INotifyIconFactory))).Returns(mockNotifyIconFactory.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IIconResourceLoader))).Returns(mockIconLoader.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IContextMenuBuilder))).Returns(mockMenuBuilder.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IVisibilityManager))).Returns(mockVisibilityManager.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IStartupNotificationService))).Returns(mockNotificationService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IHistoryWindowService))).Returns(mockHistoryWindowService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsWindowService))).Returns(mockSettingsWindowService.Object);

            _systemTrayService = new SystemTrayService(_mockServiceProvider.Object);
        }

        [Test]
        public void Initialize_V2_MethodExists()
        {
            // Act & Assert - Vérifier que la méthode existe et peut être appelée
            // Nous ne testons pas l'implémentation complète ici car elle nécessite un environnement UI complet
            var method = typeof(SystemTrayService).GetMethod("Initialize_V2");
            Assert.IsNotNull(method, "La méthode Initialize_V2 doit exister dans SystemTrayService");
            Assert.AreEqual(typeof(void), method!.ReturnType, "Initialize_V2 doit retourner void");
            Assert.AreEqual(0, method.GetParameters().Length, "Initialize_V2 ne doit pas avoir de paramètres");
        }

        [Test]
        public void Initialize_V2_MethodSignatureIsCorrect()
        {
            // Arrange
            var method = typeof(SystemTrayService).GetMethod("Initialize_V2");

            // Assert
            Assert.IsNotNull(method, "La méthode Initialize_V2 doit exister");
            Assert.IsTrue(method!.IsPublic, "Initialize_V2 doit être publique");
            Assert.IsFalse(method!.IsStatic, "Initialize_V2 ne doit pas être statique");
            Assert.AreEqual(typeof(void), method!.ReturnType, "Initialize_V2 doit retourner void");
        }

        [Test]
        public void Initialize_V2_InterfaceMethodExists()
        {
            // Arrange
            var orchestratorMethod = typeof(ISystemTrayOrchestrator).GetMethod("Initialize_V2");

            // Assert
            Assert.IsNotNull(orchestratorMethod, "La méthode Initialize_V2 doit exister dans ISystemTrayOrchestrator");
            Assert.AreEqual(typeof(void), orchestratorMethod.ReturnType, "Initialize_V2 doit retourner void dans l'interface");
            Assert.AreEqual(0, orchestratorMethod.GetParameters().Length, "Initialize_V2 ne doit pas avoir de paramètres dans l'interface");
        }
    }
}
