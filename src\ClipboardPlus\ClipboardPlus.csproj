<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <OutputType>WinExe</OutputType>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ApplicationIcon>Assets\ApplicationIcon.ico</ApplicationIcon>
    <Authors>ClipboardPlus Team</Authors>
    <Company>ClipboardPlus</Company>
    <Product>ClipboardPlus</Product>
    <Description>Gestionnaire de presse-papiers avancé pour Windows</Description>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>

    <!-- Optimisations pour accélérer le démarrage en développement -->
    <TieredCompilation>true</TieredCompilation>
    <TieredCompilationQuickJit>true</TieredCompilationQuickJit>
    <UseSharedCompilation>true</UseSharedCompilation>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
    <DisableImplicitNuGetFallbackFolder>true</DisableImplicitNuGetFallbackFolder>
    <RestoreNoCache>false</RestoreNoCache>
    <RestoreIgnoreFailedSources>true</RestoreIgnoreFailedSources>
  </PropertyGroup>

  <!-- Permettre aux projets de tests d'accéder aux membres internes -->
  <ItemGroup>
    <InternalsVisibleTo Include="ClipboardPlus.Tests.Unit" />
    <InternalsVisibleTo Include="ClipboardPlus.Tests.STA" />
    <InternalsVisibleTo Include="ClipboardPlus.Tests.Integration" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="8.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="gong-wpf-dragdrop" Version="3.2.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Prism.Core" Version="9.0.537" />
    <PackageReference Include="System.Reactive" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Assets\**\*.ico" />
    <Resource Include="Assets\**\*.png" />
  </ItemGroup>

  <!-- Configuration des thèmes comme ressources -->
  <ItemGroup>
    <Resource Include="UI\Themes\*.xaml" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Assets\" />
    <Folder Include="Core\DataModels\" />
    <Folder Include="Core\Services\" />
    <Folder Include="Native\" />
    <Folder Include="Properties\" />
    <Folder Include="UI\Controls\" />
    <Folder Include="UI\Converters\" />
    <Folder Include="UI\Dialogs\" />
    <Folder Include="UI\Themes\" />
    <Folder Include="UI\ViewModels\" />
    <Folder Include="UI\Windows\" />
    <Folder Include="Utils\" />
  </ItemGroup>

</Project> 