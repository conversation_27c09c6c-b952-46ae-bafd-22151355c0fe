using System;
using System.Collections.Generic;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Models
{
    /// <summary>
    /// Résultat d'une opération de logging de suppression.
    /// </summary>
    public class DeletionLoggingResult
    {
        /// <summary>
        /// Identifiant unique de l'opération de logging.
        /// </summary>
        public Guid LoggingId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Indique si l'opération de logging a réussi.
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Message de résultat.
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// Erreur survenue pendant le logging (si applicable).
        /// </summary>
        public Exception? Error { get; set; }

        /// <summary>
        /// Durée de l'opération de logging.
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Taille du log généré (en caractères).
        /// </summary>
        public int LogSize { get; set; }

        /// <summary>
        /// Chemin du fichier de log.
        /// </summary>
        public string? LogFilePath { get; set; }

        /// <summary>
        /// Timestamp de l'opération.
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Métriques de performance de l'opération.
        /// </summary>
        public OperationPerformanceMetrics? PerformanceMetrics { get; set; }

        /// <summary>
        /// Crée un résultat de succès.
        /// </summary>
        /// <param name="message">Message de succès</param>
        /// <param name="logSize">Taille du log généré</param>
        /// <param name="duration">Durée de l'opération</param>
        /// <returns>Résultat de succès</returns>
        public static DeletionLoggingResult Success(string message, int logSize, TimeSpan duration)
        {
            return new DeletionLoggingResult
            {
                IsSuccessful = true,
                Message = message,
                LogSize = logSize,
                Duration = duration
            };
        }

        /// <summary>
        /// Crée un résultat d'échec.
        /// </summary>
        /// <param name="error">Erreur survenue</param>
        /// <param name="message">Message d'erreur</param>
        /// <returns>Résultat d'échec</returns>
        public static DeletionLoggingResult Failure(Exception error, string? message = null)
        {
            return new DeletionLoggingResult
            {
                IsSuccessful = false,
                Error = error,
                Message = message ?? error.Message
            };
        }
    }

    /// <summary>
    /// Métriques de performance d'une opération.
    /// </summary>
    public class OperationPerformanceMetrics
    {
        /// <summary>
        /// Durée totale de l'opération.
        /// </summary>
        public TimeSpan TotalDuration { get; set; }

        /// <summary>
        /// Durée de la validation.
        /// </summary>
        public TimeSpan ValidationDuration { get; set; }

        /// <summary>
        /// Durée du formatage.
        /// </summary>
        public TimeSpan FormattingDuration { get; set; }

        /// <summary>
        /// Durée de l'écriture du log.
        /// </summary>
        public TimeSpan WritingDuration { get; set; }

        /// <summary>
        /// Mémoire utilisée pendant l'opération (en octets).
        /// </summary>
        public long MemoryUsed { get; set; }

        /// <summary>
        /// Nombre d'allocations mémoire.
        /// </summary>
        public int AllocationCount { get; set; }

        /// <summary>
        /// Taille des données traitées (en octets).
        /// </summary>
        public long DataSize { get; set; }

        /// <summary>
        /// Timestamp de début.
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Timestamp de fin.
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// ID du thread d'exécution.
        /// </summary>
        public int ThreadId { get; set; } = Environment.CurrentManagedThreadId;
    }

    /// <summary>
    /// Erreur survenue pendant le logging de suppression.
    /// </summary>
    public class DeletionLoggingError
    {
        /// <summary>
        /// Identifiant unique de l'erreur.
        /// </summary>
        public Guid ErrorId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Type d'erreur.
        /// </summary>
        public LoggingErrorType ErrorType { get; set; }

        /// <summary>
        /// Message d'erreur.
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Exception associée.
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// Contexte dans lequel l'erreur s'est produite.
        /// </summary>
        public DeletionResultContext? Context { get; set; }

        /// <summary>
        /// Composant qui a généré l'erreur.
        /// </summary>
        public string? Component { get; set; }

        /// <summary>
        /// Niveau de sévérité de l'erreur.
        /// </summary>
        public ErrorSeverity Severity { get; set; }

        /// <summary>
        /// Timestamp de l'erreur.
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Indique si l'erreur est récupérable.
        /// </summary>
        public bool IsRecoverable { get; set; }

        /// <summary>
        /// Actions de récupération suggérées.
        /// </summary>
        public List<string> RecoveryActions { get; set; } = new();
    }

    /// <summary>
    /// Métriques globales du système de suppression.
    /// </summary>
    public class DeletionMetrics
    {
        /// <summary>
        /// Nombre total d'opérations de suppression.
        /// </summary>
        public long TotalDeletions { get; set; }

        /// <summary>
        /// Nombre de suppressions réussies.
        /// </summary>
        public long SuccessfulDeletions { get; set; }

        /// <summary>
        /// Nombre de suppressions échouées.
        /// </summary>
        public long FailedDeletions { get; set; }

        /// <summary>
        /// Taux de succès (en pourcentage).
        /// </summary>
        public double SuccessRate => TotalDeletions > 0 ? (double)SuccessfulDeletions / TotalDeletions * 100 : 0;

        /// <summary>
        /// Durée moyenne d'une opération de logging.
        /// </summary>
        public TimeSpan AverageLoggingDuration { get; set; }

        /// <summary>
        /// Taille moyenne des logs générés.
        /// </summary>
        public double AverageLogSize { get; set; }

        /// <summary>
        /// Nombre total d'erreurs.
        /// </summary>
        public long TotalErrors { get; set; }

        /// <summary>
        /// Répartition des erreurs par type.
        /// </summary>
        public Dictionary<LoggingErrorType, int> ErrorsByType { get; set; } = new();

        /// <summary>
        /// Timestamp de début de collecte.
        /// </summary>
        public DateTime CollectionStartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Timestamp de dernière mise à jour.
        /// </summary>
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Métriques spécifiques au logger de suppression.
    /// </summary>
    public class DeletionLoggerMetrics
    {
        /// <summary>
        /// Nombre total d'opérations de logging.
        /// </summary>
        public long TotalLoggingOperations { get; set; }

        /// <summary>
        /// Nombre d'opérations réussies.
        /// </summary>
        public long SuccessfulOperations { get; set; }

        /// <summary>
        /// Nombre d'opérations échouées.
        /// </summary>
        public long FailedOperations { get; set; }

        /// <summary>
        /// Taux de succès (en pourcentage).
        /// </summary>
        public double SuccessRate => TotalLoggingOperations > 0 ? (double)SuccessfulOperations / TotalLoggingOperations * 100 : 0;

        /// <summary>
        /// Durée minimale d'opération.
        /// </summary>
        public TimeSpan MinDuration { get; set; } = TimeSpan.MaxValue;

        /// <summary>
        /// Durée maximale d'opération.
        /// </summary>
        public TimeSpan MaxDuration { get; set; } = TimeSpan.MinValue;

        /// <summary>
        /// Durée moyenne d'opération.
        /// </summary>
        public TimeSpan AverageDuration { get; set; }

        /// <summary>
        /// Utilisation mémoire moyenne.
        /// </summary>
        public long AverageMemoryUsage { get; set; }

        /// <summary>
        /// Utilisation mémoire maximale.
        /// </summary>
        public long MaxMemoryUsage { get; set; }

        /// <summary>
        /// Taille totale des logs générés.
        /// </summary>
        public long TotalLogSize { get; set; }

        /// <summary>
        /// Timestamp de début de collecte.
        /// </summary>
        public DateTime CollectionStartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Timestamp de dernière mise à jour.
        /// </summary>
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Métriques d'erreurs.
    /// </summary>
    public class ErrorMetrics
    {
        /// <summary>
        /// Nombre total d'erreurs.
        /// </summary>
        public long TotalErrors { get; set; }

        /// <summary>
        /// Erreurs par type.
        /// </summary>
        public Dictionary<LoggingErrorType, int> ErrorsByType { get; set; } = new();

        /// <summary>
        /// Erreurs par sévérité.
        /// </summary>
        public Dictionary<ErrorSeverity, int> ErrorsBySeverity { get; set; } = new();

        /// <summary>
        /// Erreurs par composant.
        /// </summary>
        public Dictionary<string, int> ErrorsByComponent { get; set; } = new();

        /// <summary>
        /// Erreurs récentes (dernières 24h).
        /// </summary>
        public List<DeletionLoggingError> RecentErrors { get; set; } = new();

        /// <summary>
        /// Taux d'erreur (en pourcentage).
        /// </summary>
        public double ErrorRate { get; set; }

        /// <summary>
        /// Timestamp de dernière erreur.
        /// </summary>
        public DateTime? LastErrorTime { get; set; }
    }

    /// <summary>
    /// Types d'erreurs de logging.
    /// </summary>
    public enum LoggingErrorType
    {
        ValidationError,
        FormattingError,
        FileWriteError,
        PermissionError,
        OutOfMemoryError,
        TimeoutError,
        UnknownError
    }

    /// <summary>
    /// Niveaux de sévérité des erreurs.
    /// </summary>
    public enum ErrorSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }
}
