using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Windows
{
    /// <summary>
    /// Service responsable de l'affichage et de la gestion de la fenêtre des paramètres de l'application.
    /// Cette interface encapsule la logique d'ouverture de la fenêtre des paramètres qui était précédemment
    /// dans SystemTrayService.OpenSettingsWindow().
    /// </summary>
    public interface ISettingsWindowService
    {
        /// <summary>
        /// Ouvre la fenêtre des paramètres de l'application.
        /// Cette méthode gère l'activation de la fenêtre existante ou la création d'une nouvelle instance.
        /// </summary>
        /// <returns>Une tâche représentant l'opération asynchrone d'ouverture de la fenêtre.</returns>
        Task OpenSettingsWindowAsync();

        /// <summary>
        /// Ouvre la fenêtre des paramètres avec un identifiant d'opération pour le logging.
        /// Version améliorée qui retourne un résultat structuré.
        /// </summary>
        /// <param name="operationId">Identifiant unique de l'opération pour le logging</param>
        /// <returns>Résultat de l'opération d'ouverture de la fenêtre</returns>
        Task<SettingsWindowResult> OpenSettingsAsync(string operationId);
    }

    /// <summary>
    /// Résultat de l'opération d'ouverture de la fenêtre des paramètres.
    /// </summary>
    public record SettingsWindowResult(
        bool Success,
        string Status,
        string? ErrorMessage = null
    )
    {
        /// <summary>
        /// Crée un résultat de succès.
        /// </summary>
        public static SettingsWindowResult CreateSuccess(string status) =>
            new(true, status);

        /// <summary>
        /// Crée un résultat d'échec.
        /// </summary>
        public static SettingsWindowResult CreateFailure(string errorMessage) =>
            new(false, "Échec", errorMessage);

        /// <summary>
        /// Crée un résultat pour une fenêtre déjà ouverte.
        /// </summary>
        public static SettingsWindowResult CreateAlreadyOpen() =>
            new(true, "Fenêtre déjà ouverte - Activée");
    }
}
