using System;
using System.IO;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation du service de système de fichiers.
    /// Abstrait les opérations File.* pour améliorer la testabilité.
    /// </summary>
    public class FileSystemService : IFileSystemService
    {
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de FileSystemService.
        /// </summary>
        /// <param name="loggingService">Service de logging optionnel</param>
        public FileSystemService(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <inheritdoc />
        public bool FileExists(string path)
        {
            ArgumentNullException.ThrowIfNull(path);
            
            if (string.IsNullOrWhiteSpace(path))
            {
                throw new ArgumentException("Le chemin ne peut pas être vide ou ne contenir que des espaces.", nameof(path));
            }

            try
            {
                _loggingService?.LogInfo($"FileSystemService.FileExists - Vérification de l'existence du fichier: {path}");
                
                bool exists = File.Exists(path);
                
                _loggingService?.LogInfo($"FileSystemService.FileExists - Résultat: {exists} pour {path}");
                
                return exists;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"FileSystemService.FileExists - Erreur lors de la vérification de {path}: {ex.Message}", ex);
                return false;
            }
        }

        /// <inheritdoc />
        public bool DirectoryExists(string path)
        {
            ArgumentNullException.ThrowIfNull(path);
            
            if (string.IsNullOrWhiteSpace(path))
            {
                throw new ArgumentException("Le chemin ne peut pas être vide ou ne contenir que des espaces.", nameof(path));
            }

            try
            {
                _loggingService?.LogInfo($"FileSystemService.DirectoryExists - Vérification de l'existence du répertoire: {path}");
                
                bool exists = Directory.Exists(path);
                
                _loggingService?.LogInfo($"FileSystemService.DirectoryExists - Résultat: {exists} pour {path}");
                
                return exists;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"FileSystemService.DirectoryExists - Erreur lors de la vérification de {path}: {ex.Message}", ex);
                return false;
            }
        }

        /// <inheritdoc />
        public FileInfo? GetFileInfo(string path)
        {
            ArgumentNullException.ThrowIfNull(path);

            try
            {
                _loggingService?.LogInfo($"FileSystemService.GetFileInfo - Obtention des informations pour: {path}");
                
                if (!File.Exists(path))
                {
                    _loggingService?.LogWarning($"FileSystemService.GetFileInfo - Fichier inexistant: {path}");
                    return null;
                }

                var fileInfo = new FileInfo(path);
                
                _loggingService?.LogInfo($"FileSystemService.GetFileInfo - Informations obtenues pour {path}: Taille={fileInfo.Length}, Modifié={fileInfo.LastWriteTime}");
                
                return fileInfo;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"FileSystemService.GetFileInfo - Erreur lors de l'obtention des informations de {path}: {ex.Message}", ex);
                return null;
            }
        }

        /// <inheritdoc />
        public long? GetFileSize(string path)
        {
            ArgumentNullException.ThrowIfNull(path);

            try
            {
                var fileInfo = GetFileInfo(path);
                return fileInfo?.Length;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"FileSystemService.GetFileSize - Erreur lors de l'obtention de la taille de {path}: {ex.Message}", ex);
                return null;
            }
        }

        /// <inheritdoc />
        public DateTime? GetLastWriteTime(string path)
        {
            ArgumentNullException.ThrowIfNull(path);

            try
            {
                var fileInfo = GetFileInfo(path);
                return fileInfo?.LastWriteTime;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"FileSystemService.GetLastWriteTime - Erreur lors de l'obtention de la date de modification de {path}: {ex.Message}", ex);
                return null;
            }
        }

        /// <inheritdoc />
        public bool IsValidPath(string? path)
        {
            if (string.IsNullOrWhiteSpace(path))
            {
                return false;
            }

            try
            {
                // Utilise Path.GetFullPath pour valider la syntaxe
                Path.GetFullPath(path);
                
                // Vérifie les caractères invalides pour le chemin
                char[] invalidPathChars = Path.GetInvalidPathChars();
                if (path.IndexOfAny(invalidPathChars) >= 0)
                {
                    return false;
                }

                // Vérifie aussi les caractères invalides pour les noms de fichiers dans chaque partie du chemin
                char[] invalidFileNameChars = Path.GetInvalidFileNameChars();

                // Séparer le chemin en parties et vérifier chaque partie
                string[] pathParts = path.Split(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);
                for (int i = 0; i < pathParts.Length; i++)
                {
                    string part = pathParts[i];
                    if (!string.IsNullOrEmpty(part))
                    {
                        // Ignorer la vérification pour la racine du lecteur (ex: "C:")
                        if (i == 0 && part.Length == 2 && part[1] == ':')
                        {
                            // C'est une racine de lecteur, vérifier seulement que c'est une lettre
                            if (!char.IsLetter(part[0]))
                            {
                                return false;
                            }
                        }
                        else if (part.IndexOfAny(invalidFileNameChars) >= 0)
                        {
                            return false;
                        }
                    }
                }

                // Vérifie la longueur maximale du chemin
                if (path.Length > 260) // MAX_PATH sur Windows
                {
                    return false;
                }

                _loggingService?.LogInfo($"FileSystemService.IsValidPath - Chemin valide: {path}");
                return true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"FileSystemService.IsValidPath - Chemin invalide {path}: {ex.Message}");
                return false;
            }
        }
    }
}
