// ============================================================================
// ITEM CREATION MANAGER IMPLEMENTATION - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Implémentation concrète de la gestion de création/renommage
// 📋 RESPONSABILITÉ : Délégation vers CreationModule + gestion des 6 commandes
// 🏗️ ARCHITECTURE : Réutilisation du CreationModule existant
//
// ============================================================================

using System;
using System.Threading.Tasks;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Modules.Creation;
using ClipboardPlus.UI.ViewModels.Managers.Interfaces;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace ClipboardPlus.UI.ViewModels.Managers.Implementations
{
    /// <summary>
    /// Implémentation concrète du manager de gestion de création et renommage d'éléments.
    /// 
    /// Cette classe délègue les opérations vers le CreationModule existant
    /// et gère les 6 commandes de création/renommage + 4 propriétés d'état.
    /// </summary>
    public class ItemCreationManager : ObservableObject, IItemCreationManager
    {
        #region Champs Privés

        private readonly ICreationModule _creationModule;
        private readonly IClipboardHistoryManager _historyManager;
        private readonly ILoggingService _loggingService;
        private string? _newItemTextContent;
        private bool _isItemCreationActive;
        private ClipboardItem? _itemEnRenommage;
        private string? _nouveauNom;
        private bool _isDisposed;

        #endregion

        #region Constructeur

        /// <summary>
        /// Initialise une nouvelle instance du ItemCreationManager.
        /// </summary>
        /// <param name="creationModule">Module de création à utiliser</param>
        /// <param name="historyManager">Manager d'historique pour la persistance</param>
        /// <param name="loggingService">Service de logging</param>
        public ItemCreationManager(ICreationModule creationModule, IClipboardHistoryManager historyManager, ILoggingService loggingService)
        {
            _creationModule = creationModule ?? throw new ArgumentNullException(nameof(creationModule));
            _historyManager = historyManager ?? throw new ArgumentNullException(nameof(historyManager));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            
            // Initialiser les commandes
            InitializeCommands();
        }

        #endregion

        #region Propriétés d'État (IItemCreationManager)

        /// <summary>
        /// Contenu textuel du nouvel élément en cours de création.
        /// </summary>
        public string? NewItemTextContent
        {
            get => _newItemTextContent;
            set
            {
                if (SetProperty(ref _newItemTextContent, value))
                {
                    // Invalider les commandes liées à la création
                    (FinalizeAndSaveNewItemCommand as RelayCommand)?.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Indique si le mode de création d'un nouvel élément est actif.
        /// </summary>
        public bool IsItemCreationActive
        {
            get => _isItemCreationActive;
            set
            {
                if (SetProperty(ref _isItemCreationActive, value))
                {
                    CreationModeChanged?.Invoke(this, value);
                    
                    // Invalider les commandes
                    (PrepareNewItemCommand as RelayCommand)?.NotifyCanExecuteChanged();
                    (FinalizeAndSaveNewItemCommand as RelayCommand)?.NotifyCanExecuteChanged();
                    (DiscardNewItemCreationCommand as RelayCommand)?.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Élément actuellement en cours de renommage.
        /// </summary>
        public ClipboardItem? ItemEnRenommage
        {
            get => _itemEnRenommage;
            set
            {
                if (SetProperty(ref _itemEnRenommage, value))
                {
                    RenamingModeChanged?.Invoke(this, value);
                    
                    // Invalider les commandes de renommage
                    (DemarrerRenommageCommand as RelayCommand)?.NotifyCanExecuteChanged();
                    (ConfirmerRenommageCommand as RelayCommand)?.NotifyCanExecuteChanged();
                    (AnnulerRenommageCommand as RelayCommand)?.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Nouveau nom pour l'élément en cours de renommage.
        /// </summary>
        public string? NouveauNom
        {
            get => _nouveauNom;
            set
            {
                if (SetProperty(ref _nouveauNom, value))
                {
                    // Invalider la commande de confirmation
                    (ConfirmerRenommageCommand as RelayCommand)?.NotifyCanExecuteChanged();
                }
            }
        }

        #endregion

        #region Commandes de Création (IItemCreationManager)

        /// <summary>
        /// Commande pour préparer la création d'un nouvel élément.
        /// </summary>
        public ICommand PrepareNewItemCommand { get; private set; } = null!;

        /// <summary>
        /// Commande pour finaliser et sauvegarder le nouvel élément.
        /// </summary>
        public ICommand FinalizeAndSaveNewItemCommand { get; private set; } = null!;

        /// <summary>
        /// Commande pour annuler la création du nouvel élément.
        /// </summary>
        public ICommand DiscardNewItemCreationCommand { get; private set; } = null!;

        #endregion

        #region Commandes de Renommage (IItemCreationManager)

        /// <summary>
        /// Commande pour démarrer le renommage d'un élément existant.
        /// </summary>
        public ICommand DemarrerRenommageCommand { get; private set; } = null!;

        /// <summary>
        /// Commande pour confirmer le renommage et sauvegarder le nouveau nom.
        /// </summary>
        public ICommand ConfirmerRenommageCommand { get; private set; } = null!;

        /// <summary>
        /// Commande pour annuler le renommage et restaurer l'état précédent.
        /// </summary>
        public ICommand AnnulerRenommageCommand { get; private set; } = null!;

        #endregion

        #region Propriétés de Validation (IItemCreationManager)

        /// <summary>
        /// Indique si le contenu du nouvel élément est valide pour la sauvegarde.
        /// </summary>
        public bool IsNewItemContentValid => ValidateNewItemContent(_newItemTextContent);

        /// <summary>
        /// Indique si le nouveau nom pour le renommage est valide.
        /// </summary>
        public bool IsNewNameValid => ValidateNewName(_nouveauNom);

        /// <summary>
        /// Indique si un processus de création ou renommage est en cours.
        /// </summary>
        public bool IsProcessingActive => _isItemCreationActive || _itemEnRenommage != null;

        #endregion

        #region Événements (IItemCreationManager)

        /// <summary>
        /// Événement déclenché lorsque le mode de création change.
        /// </summary>
        public event EventHandler<bool>? CreationModeChanged;

        /// <summary>
        /// Événement déclenché lorsqu'un nouvel élément est créé avec succès.
        /// </summary>
        public event EventHandler<ClipboardItem>? ItemCreated;

        /// <summary>
        /// Événement déclenché lorsque le mode de renommage change.
        /// </summary>
        public event EventHandler<ClipboardItem?>? RenamingModeChanged;

        /// <summary>
        /// Événement déclenché lorsqu'un élément est renommé avec succès.
        /// </summary>
        public event EventHandler<ItemRenamedEventArgs>? ItemRenamed;

        /// <summary>
        /// Événement déclenché lorsqu'une opération de création/renommage échoue.
        /// </summary>
        public event EventHandler<CreationErrorEventArgs>? OperationFailed;

        #endregion

        #region Initialisation des Commandes

        /// <summary>
        /// Initialise toutes les commandes avec leurs implémentations.
        /// </summary>
        private void InitializeCommands()
        {
            // Commandes de création
            PrepareNewItemCommand = new RelayCommand(
                ExecutePrepareNewItem,
                () => !_isItemCreationActive && _itemEnRenommage == null);

            FinalizeAndSaveNewItemCommand = new RelayCommand(
                ExecuteFinalizeAndSaveNewItem,
                () => _isItemCreationActive && IsNewItemContentValid);

            DiscardNewItemCreationCommand = new RelayCommand(
                ExecuteDiscardNewItemCreation,
                () => _isItemCreationActive);

            // Commandes de renommage
            DemarrerRenommageCommand = new RelayCommand<ClipboardItem>(
                ExecuteDemarrerRenommage,
                item =>
                {
                    var canExecute = item != null && !_isItemCreationActive && _itemEnRenommage == null;
                    _loggingService?.LogInfo($"🏷️ [RENAME-CANEXECUTE] item != null: {item != null}, !_isItemCreationActive: {!_isItemCreationActive}, _itemEnRenommage == null: {_itemEnRenommage == null} ==> CanExecute: {canExecute}");
                    return canExecute;
                });

            ConfirmerRenommageCommand = new RelayCommand(
                ExecuteConfirmerRenommage,
                () => _itemEnRenommage != null && IsNewNameValid);

            AnnulerRenommageCommand = new RelayCommand(
                ExecuteAnnulerRenommage,
                () => _itemEnRenommage != null);
        }

        #endregion

        #region Méthodes d'Exécution des Commandes

        private void ExecutePrepareNewItem()
        {
            try
            {
                PrepareNewItem();
            }
            catch (Exception ex)
            {
                OperationFailed?.Invoke(this, new CreationErrorEventArgs(
                    "PrepareNewItem", "Erreur lors de la préparation du nouvel élément", ex));
            }
        }

        private void ExecuteFinalizeAndSaveNewItem()
        {
            try
            {
                var createdItem = FinalizeAndSaveNewItem();
                if (createdItem != null)
                {
                    ItemCreated?.Invoke(this, createdItem);
                }
            }
            catch (Exception ex)
            {
                OperationFailed?.Invoke(this, new CreationErrorEventArgs(
                    "FinalizeAndSaveNewItem", "Erreur lors de la sauvegarde du nouvel élément", ex));
            }
        }

        private void ExecuteDiscardNewItemCreation()
        {
            try
            {
                DiscardNewItemCreation();
            }
            catch (Exception ex)
            {
                OperationFailed?.Invoke(this, new CreationErrorEventArgs(
                    "DiscardNewItemCreation", "Erreur lors de l'annulation de la création", ex));
            }
        }

        private void ExecuteDemarrerRenommage(ClipboardItem? item)
        {
            CancelRenaming();
            _loggingService?.LogInfo($"🏷️ [RENAME-MANAGER] ExecuteDemarrerRenommage appelé avec item: {(item != null ? $"ID={item.Id}" : "null")}");

            try
            {
                if (item != null)
                {
                    StartRenaming(item);
                    _loggingService?.LogInfo($"🏷️ [RENAME-MANAGER] ExecuteDemarrerRenommage réussi pour item ID={item.Id}");
                }
                else
                {
                    _loggingService?.LogWarning("🏷️ [RENAME-MANAGER] ExecuteDemarrerRenommage appelé avec item null");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"🏷️ [RENAME-MANAGER] ExecuteDemarrerRenommage ERREUR: {ex.Message}", ex);
                OperationFailed?.Invoke(this, new CreationErrorEventArgs(
                    "DemarrerRenommage", "Erreur lors du démarrage du renommage", ex));
            }
        }

        private async void ExecuteConfirmerRenommage()
        {
            try
            {
                if (await ConfirmRenaming())
                {
                    // Le renommage a réussi, l'événement ItemRenamed est déclenché dans ConfirmRenaming
                }
            }
            catch (Exception ex)
            {
                OperationFailed?.Invoke(this, new CreationErrorEventArgs(
                    "ConfirmerRenommage", "Erreur lors de la confirmation du renommage", ex));
            }
        }

        private void ExecuteAnnulerRenommage()
        {
            try
            {
                CancelRenaming();
            }
            catch (Exception ex)
            {
                OperationFailed?.Invoke(this, new CreationErrorEventArgs(
                    "AnnulerRenommage", "Erreur lors de l'annulation du renommage", ex));
            }
        }

        #endregion

        #region Méthodes de Gestion de Création (IItemCreationManager)

        /// <summary>
        /// Prépare la création d'un nouvel élément.
        /// </summary>
        /// <param name="initialContent">Contenu initial optionnel</param>
        public void PrepareNewItem(string? initialContent = null)
        {
            if (_isDisposed) return;

            // Déléguer vers le CreationModule (méthode async)
            _ = Task.Run(async () => await _creationModule.StartCreationAsync(initialContent));

            // Mettre à jour l'état local
            NewItemTextContent = initialContent;
            IsItemCreationActive = true;
        }

        /// <summary>
        /// Finalise et sauvegarde le nouvel élément.
        /// </summary>
        /// <returns>L'élément créé ou null en cas d'échec</returns>
        public ClipboardItem? FinalizeAndSaveNewItem()
        {
            if (_isDisposed || !IsNewItemContentValid) return null;

            try
            {
                // Déléguer vers le CreationModule (méthode async)
                var task = _creationModule.FinalizeAndSaveAsync();
                var createdItem = task.GetAwaiter().GetResult();

                if (createdItem != null)
                {
                    // Nettoyer l'état de création
                    DiscardNewItemCreation();
                    return createdItem;
                }

                return null;
            }
            catch
            {
                // En cas d'erreur, maintenir l'état de création pour permettre une nouvelle tentative
                throw;
            }
        }

        /// <summary>
        /// Annule la création du nouvel élément et nettoie l'état.
        /// </summary>
        public void DiscardNewItemCreation()
        {
            if (_isDisposed) return;

            // Déléguer vers le CreationModule (méthode async)
            _ = Task.Run(async () => await _creationModule.CancelCreationAsync("User cancelled"));

            // Nettoyer l'état local
            NewItemTextContent = null;
            IsItemCreationActive = false;
        }

        /// <summary>
        /// Valide le contenu du nouvel élément.
        /// </summary>
        /// <param name="content">Contenu à valider</param>
        /// <returns>True si le contenu est valide</returns>
        public bool ValidateNewItemContent(string? content)
        {
            return !string.IsNullOrWhiteSpace(content) && content.Trim().Length > 0;
        }

        #endregion

        #region Méthodes de Gestion de Renommage (IItemCreationManager)

        /// <summary>
        /// Démarre le renommage d'un élément existant.
        /// </summary>
        /// <param name="item">Élément à renommer</param>
        public void StartRenaming(ClipboardItem item)
        {
            _loggingService?.LogInfo($"🏷️ [RENAME-MANAGER] StartRenaming appelé avec item ID={item?.Id}");

            if (_isDisposed || item == null)
            {
                _loggingService?.LogWarning($"🏷️ [RENAME-MANAGER] StartRenaming abandonné - _isDisposed={_isDisposed}, item={item?.Id ?? 0}");
                return;
            }

            _loggingService?.LogInfo($"🏷️ [RENAME-MANAGER] StartRenaming - Début du renommage pour item ID={item.Id}");

            // Le renommage est géré par le CommandModule via RenameItemCommand
            // Mettre à jour l'état local directement
            ItemEnRenommage = item;
            var displayName = GetDisplayName(item);
            NouveauNom = displayName;

            _loggingService?.LogInfo($"🏷️ [RENAME-MANAGER] StartRenaming SUCCÈS - ItemEnRenommage={item.Id}, NouveauNom='{displayName}'");
        }

        /// <summary>
        /// Confirme le renommage et sauvegarde le nouveau nom.
        /// </summary>
        /// <returns>True si le renommage a réussi</returns>
        public async Task<bool> ConfirmRenaming()
        {
            if (_isDisposed || _itemEnRenommage == null || !IsNewNameValid) return false;

            var renameId = Guid.NewGuid().ToString("N")[..8];
            var oldName = GetDisplayName(_itemEnRenommage);

            _loggingService?.LogInfo($"🏷️ [RENAME-{renameId}] ConfirmRenaming DÉBUT - Item: {_itemEnRenommage.Id}, OldName: '{oldName}', NewName: '{_nouveauNom}'");

            try
            {
                // Appliquer le renommage directement sur l'élément
                _itemEnRenommage.CustomName = _nouveauNom;
                _loggingService?.LogInfo($"🏷️ [RENAME-{renameId}] CustomName modifié en mémoire - Item: {_itemEnRenommage.Id}");

                // CORRECTION CRITIQUE : Sauvegarder en base de données
                await _historyManager.UpdateItemAsync(_itemEnRenommage);
                _loggingService?.LogInfo($"🏷️ [RENAME-{renameId}] Sauvegarde BDD réussie - Item: {_itemEnRenommage.Id}, CustomName: '{_itemEnRenommage.CustomName}'");

                // Déclencher l'événement de renommage réussi
                var abonnes = ItemRenamed?.GetInvocationList()?.Length ?? 0;
                ItemRenamed?.Invoke(this, new ItemRenamedEventArgs(_itemEnRenommage, oldName, _nouveauNom));
                _loggingService?.LogInfo($"🏷️ [RENAME-{renameId}] ItemRenamed événement déclenché - {abonnes} abonnés notifiés");

                // Nettoyer l'état de renommage
                CancelRenaming();
                _loggingService?.LogInfo($"🏷️ [RENAME-{renameId}] ConfirmRenaming SUCCÈS - Renommage terminé");
                return true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"🏷️ [RENAME-{renameId}] ConfirmRenaming ERREUR - {ex.Message}", ex);
                // En cas d'erreur, maintenir l'état de renommage pour permettre une nouvelle tentative
                throw;
            }
        }

        /// <summary>
        /// Annule le renommage et restaure l'état précédent.
        /// </summary>
        public void CancelRenaming()
        {
            if (_isDisposed) return;

            // Nettoyer l'état local directement
            ItemEnRenommage = null;
            NouveauNom = null;
        }

        /// <summary>
        /// Valide le nouveau nom pour le renommage.
        /// </summary>
        /// <param name="newName">Nouveau nom à valider</param>
        /// <returns>True si le nom est valide</returns>
        public bool ValidateNewName(string? newName)
        {
            return !string.IsNullOrWhiteSpace(newName) && newName.Trim().Length > 0;
        }

        #endregion

        #region Méthodes Utilitaires (IItemCreationManager)

        /// <summary>
        /// Nettoie tous les états de création et renommage.
        /// </summary>
        public void ClearAllStates()
        {
            if (_isDisposed) return;

            if (_isItemCreationActive)
            {
                DiscardNewItemCreation();
            }

            if (_itemEnRenommage != null)
            {
                CancelRenaming();
            }
        }

        /// <summary>
        /// Vérifie si un élément peut être renommé.
        /// </summary>
        /// <param name="item">Élément à vérifier</param>
        /// <returns>True si l'élément peut être renommé</returns>
        public bool CanRenameItem(ClipboardItem? item)
        {
            return item != null && !_isItemCreationActive && _itemEnRenommage == null;
        }

        /// <summary>
        /// Obtient le nom d'affichage actuel d'un élément.
        /// </summary>
        /// <param name="item">Élément dont on veut le nom</param>
        /// <returns>Nom d'affichage de l'élément</returns>
        public string GetDisplayName(ClipboardItem item)
        {
            return item?.CustomName ?? item?.TextPreview ?? "Élément sans nom";
        }

        #endregion

        #region Méthodes d'Initialisation et Nettoyage (IItemCreationManager)

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        public void Initialize()
        {
            // Les commandes sont déjà initialisées dans le constructeur
            // Cette méthode peut être utilisée pour une réinitialisation si nécessaire
            if (_isDisposed) return;
        }

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        public void Cleanup()
        {
            if (_isDisposed) return;

            ClearAllStates();
        }

        /// <summary>
        /// Libère les ressources utilisées par le manager.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            Cleanup();
            _isDisposed = true;
        }

        #endregion
    }
}
