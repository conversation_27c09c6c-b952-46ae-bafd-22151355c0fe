using System;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.Core.Services.WindowDeactivation;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.WindowDeactivation
{
    /// <summary>
    /// Tests unitaires pour WindowClassificationConfig.
    /// </summary>
    [TestFixture]
    public class WindowClassificationConfigTests
    {
        private WindowClassificationConfig? _config;

        [SetUp]
        public void SetUp()
        {
            _config = new WindowClassificationConfig();
        }

        [Test]
        public void Constructor_ShouldInitializeWithDefaultValues()
        {
            // Act
            var config = new WindowClassificationConfig();

            // Assert
            Assert.That(config.EnableDetailedLogging, Is.True);
            Assert.That(config.EnablePerformanceMetrics, Is.False);
        }

        [Test]
        public void DefaultWindowTypes_ShouldContainExpectedEntries()
        {
            // Assert
            Assert.That(WindowClassificationConfig.DefaultWindowTypes, Is.Not.Null);
            Assert.That(WindowClassificationConfig.DefaultWindowTypes.Count, Is.GreaterThan(0));
            
            // Vérifier quelques entrées spécifiques
            Assert.That(WindowClassificationConfig.DefaultWindowTypes.ContainsKey("AppSettingsWindow"), Is.True);
            Assert.That(WindowClassificationConfig.DefaultWindowTypes["AppSettingsWindow"], Is.EqualTo("Paramètres"));
            
            Assert.That(WindowClassificationConfig.DefaultWindowTypes.ContainsKey("AdvancedCleanupWindow"), Is.True);
            Assert.That(WindowClassificationConfig.DefaultWindowTypes["AdvancedCleanupWindow"], Is.EqualTo("Nettoyage avancé"));
        }

        [Test]
        public void Config_ShouldSupportPropertyModification()
        {
            // Act
            _config!.EnableDetailedLogging = false;
            _config.EnablePerformanceMetrics = true;

            // Assert
            Assert.That(_config.EnableDetailedLogging, Is.False);
            Assert.That(_config.EnablePerformanceMetrics, Is.True);
        }

        [Test]
        public void EnableDetailedLogging_ShouldBeSettable()
        {
            // Act
            _config!.EnableDetailedLogging = false;

            // Assert
            Assert.That(_config.EnableDetailedLogging, Is.False);
        }

        [Test]
        public void EnablePerformanceMetrics_ShouldBeSettable()
        {
            // Act
            _config!.EnablePerformanceMetrics = true;

            // Assert
            Assert.That(_config.EnablePerformanceMetrics, Is.True);
        }

        [Test]
        public void Config_ShouldSupportBooleanProperties()
        {
            // Test EnableDetailedLogging
            _config!.EnableDetailedLogging = true;
            Assert.That(_config.EnableDetailedLogging, Is.True);

            _config.EnableDetailedLogging = false;
            Assert.That(_config.EnableDetailedLogging, Is.False);

            // Test EnablePerformanceMetrics
            _config.EnablePerformanceMetrics = false;
            Assert.That(_config.EnablePerformanceMetrics, Is.False);

            _config.EnablePerformanceMetrics = true;
            Assert.That(_config.EnablePerformanceMetrics, Is.True);
        }

        [Test]
        public void DefaultWindowTypes_ShouldNotBeNull()
        {
            // Assert - Vérifier que le dictionnaire statique existe
            Assert.That(WindowClassificationConfig.DefaultWindowTypes, Is.Not.Null);
            Assert.That(WindowClassificationConfig.DefaultWindowTypes.Count, Is.GreaterThan(0));
        }

        [Test]
        public void Config_ShouldSupportCloning()
        {
            // Arrange
            _config!.EnableDetailedLogging = false;
            _config.EnablePerformanceMetrics = true;

            // Act - Simuler un clonage
            var clonedConfig = new WindowClassificationConfig
            {
                EnableDetailedLogging = _config.EnableDetailedLogging,
                EnablePerformanceMetrics = _config.EnablePerformanceMetrics
            };

            // Assert
            Assert.That(clonedConfig.EnableDetailedLogging, Is.EqualTo(_config.EnableDetailedLogging));
            Assert.That(clonedConfig.EnablePerformanceMetrics, Is.EqualTo(_config.EnablePerformanceMetrics));
        }

        [Test]
        public void Config_ShouldHandleMultiplePropertyChanges()
        {
            // Act
            _config!.EnableDetailedLogging = false;
            _config.EnablePerformanceMetrics = true;

            // Assert
            Assert.That(_config.EnableDetailedLogging, Is.False);
            Assert.That(_config.EnablePerformanceMetrics, Is.True);

            // Act again
            _config.EnableDetailedLogging = true;
            _config.EnablePerformanceMetrics = false;

            // Assert
            Assert.That(_config.EnableDetailedLogging, Is.True);
            Assert.That(_config.EnablePerformanceMetrics, Is.False);
        }
    }
}
