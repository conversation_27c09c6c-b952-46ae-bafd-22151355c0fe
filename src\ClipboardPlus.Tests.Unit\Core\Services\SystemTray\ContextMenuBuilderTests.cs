using System;
using System.Linq;
using System.Threading;
using System.Windows.Controls;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;

namespace ClipboardPlus.Tests.Unit.Core.Services.SystemTray
{
    /// <summary>
    /// Tests unitaires pour ContextMenuBuilder.
    /// Vérifie la responsabilité unique : construction du menu contextuel avec les éléments appropriés.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class ContextMenuBuilderTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private ContextMenuBuilder _builder;
        private Mock<Action> _mockHistoryAction;
        private Mock<Action> _mockSettingsAction;
        private Mock<Action> _mockExitAction;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _builder = new ContextMenuBuilder(_mockLoggingService.Object);
            _mockHistoryAction = new Mock<Action>();
            _mockSettingsAction = new Mock<Action>();
            _mockExitAction = new Mock<Action>();
        }

        [Test]
        public void Constructor_WithNullLoggingService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ContextMenuBuilder(null));
        }

        [Test]
        public void BuildContextMenu_WithValidActions_CreatesMenuWithFourItems()
        {
            // Act
            var result = _builder.BuildContextMenu(
                _mockHistoryAction.Object,
                _mockSettingsAction.Object,
                _mockExitAction.Object);

            // Assert
            Assert.That(result, Is.Not.Null, "Should create ContextMenu instance");
            Assert.That(result.Items.Count, Is.EqualTo(4), "Should have exactly 4 items");

            // Vérifier les types et l'ordre des éléments
            var items = result.Items.Cast<object>().ToList();
            Assert.That(items[0], Is.TypeOf<MenuItem>(), "First item should be MenuItem");
            Assert.That(items[1], Is.TypeOf<MenuItem>(), "Second item should be MenuItem");
            Assert.That(items[2], Is.TypeOf<Separator>(), "Third item should be Separator");
            Assert.That(items[3], Is.TypeOf<MenuItem>(), "Fourth item should be MenuItem");

            // Vérifier les textes des éléments
            Assert.That(((MenuItem)items[0]).Header, Is.EqualTo("Afficher l'historique"));
            Assert.That(((MenuItem)items[1]).Header, Is.EqualTo("Paramètres"));
            Assert.That(((MenuItem)items[3]).Header, Is.EqualTo("Quitter"));

            _mockLoggingService.Verify(
                x => x.LogInfo("ContextMenuBuilder: Construction du menu contextuel..."),
                Times.Once,
                "Should log construction start");

            _mockLoggingService.Verify(
                x => x.LogInfo("ContextMenuBuilder: Menu contextuel créé avec succès (4 éléments: Historique, Paramètres, Séparateur, Quitter)"),
                Times.Once,
                "Should log successful construction");
        }

        [Test]
        public void BuildContextMenu_WithNullHistoryAction_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _builder.BuildContextMenu(
                null,
                _mockSettingsAction.Object,
                _mockExitAction.Object));
            Assert.That(ex.ParamName, Is.EqualTo("onHistoryClick"));
        }

        [Test]
        public void BuildContextMenu_WithNullSettingsAction_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _builder.BuildContextMenu(
                _mockHistoryAction.Object,
                null,
                _mockExitAction.Object));
            Assert.That(ex.ParamName, Is.EqualTo("onSettingsClick"));
        }

        [Test]
        public void BuildContextMenu_WithNullExitAction_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _builder.BuildContextMenu(
                _mockHistoryAction.Object,
                _mockSettingsAction.Object,
                null));
            Assert.That(ex.ParamName, Is.EqualTo("onExitClick"));
        }

        [Test]
        public void CreateMenuItem_WithValidParameters_CreatesMenuItem()
        {
            // Arrange
            const string header = "Test Item";
            var mockAction = new Mock<Action>();

            // Act
            var result = _builder.CreateMenuItem(header, mockAction.Object);

            // Assert
            Assert.That(result, Is.Not.Null, "Should create MenuItem instance");
            Assert.That(result.Header, Is.EqualTo(header), "Should set correct header");

            _mockLoggingService.Verify(
                x => x.LogInfo($"ContextMenuBuilder: Création de l'élément de menu: '{header}'"),
                Times.Once,
                "Should log item creation");

            _mockLoggingService.Verify(
                x => x.LogInfo($"ContextMenuBuilder: Élément de menu '{header}' créé avec succès"),
                Times.Once,
                "Should log successful creation");
        }

        [Test]
        public void CreateMenuItem_WithNullHeader_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => _builder.CreateMenuItem(null, _mockHistoryAction.Object));
            Assert.That(ex.ParamName, Is.EqualTo("header"));
        }

        [Test]
        public void CreateMenuItem_WithEmptyHeader_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => _builder.CreateMenuItem("", _mockHistoryAction.Object));
            Assert.That(ex.ParamName, Is.EqualTo("header"));
        }

        [Test]
        public void CreateMenuItem_WithWhitespaceHeader_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => _builder.CreateMenuItem("   ", _mockHistoryAction.Object));
            Assert.That(ex.ParamName, Is.EqualTo("header"));
        }

        [Test]
        public void CreateMenuItem_WithNullAction_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _builder.CreateMenuItem("Test", null));
            Assert.That(ex.ParamName, Is.EqualTo("clickAction"));
        }

        [Test]
        public void CreateMenuItem_ClickEvent_ExecutesAction()
        {
            // Arrange
            var actionExecuted = false;
            Action testAction = () => actionExecuted = true;

            // Act
            var menuItem = _builder.CreateMenuItem("Test", testAction);
            
            // Simuler un clic en déclenchant l'événement Click
            menuItem.RaiseEvent(new System.Windows.RoutedEventArgs(MenuItem.ClickEvent));

            // Assert
            Assert.That(actionExecuted, Is.True, "Action should be executed when menu item is clicked");

            _mockLoggingService.Verify(
                x => x.LogInfo("ContextMenuBuilder: Clic sur l'élément de menu: 'Test'"),
                Times.Once,
                "Should log menu item click");
        }

        [Test]
        public void CreateMenuItem_ClickEventWithException_LogsError()
        {
            // Arrange
            Action faultyAction = () => throw new InvalidOperationException("Test exception");

            // Act
            var menuItem = _builder.CreateMenuItem("Test", faultyAction);
            
            // Simuler un clic - ne devrait pas lever d'exception
            Assert.DoesNotThrow(() => menuItem.RaiseEvent(new System.Windows.RoutedEventArgs(MenuItem.ClickEvent)));

            // Assert
            _mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de l'exécution de l'action pour 'Test'")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when action execution fails");
        }

        [Test]
        public void CreateSeparator_CreatesSeparator()
        {
            // Act
            var result = _builder.CreateSeparator();

            // Assert
            Assert.That(result, Is.Not.Null, "Should create Separator instance");
            Assert.That(result, Is.TypeOf<Separator>(), "Should be of type Separator");

            _mockLoggingService.Verify(
                x => x.LogInfo("ContextMenuBuilder: Création d'un séparateur de menu"),
                Times.Once,
                "Should log separator creation");

            _mockLoggingService.Verify(
                x => x.LogInfo("ContextMenuBuilder: Séparateur créé avec succès"),
                Times.Once,
                "Should log successful separator creation");
        }

        [Test]
        public void BuildContextMenu_WithException_LogsErrorAndThrows()
        {
            // Arrange - Créer un mock qui lève une exception lors du logging
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo("ContextMenuBuilder: Construction du menu contextuel..."))
                             .Throws(new InvalidOperationException("Test exception"));

            var builder = new ContextMenuBuilder(mockLoggingService.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => builder.BuildContextMenu(
                _mockHistoryAction.Object,
                _mockSettingsAction.Object,
                _mockExitAction.Object));

            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de la construction du menu contextuel")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when construction fails");
        }

        [Test]
        public void Builder_IsStateless()
        {
            // Act - Créer plusieurs menus
            var menu1 = _builder.BuildContextMenu(_mockHistoryAction.Object, _mockSettingsAction.Object, _mockExitAction.Object);
            var menu2 = _builder.BuildContextMenu(_mockHistoryAction.Object, _mockSettingsAction.Object, _mockExitAction.Object);

            // Assert - Chaque menu doit être indépendant
            Assert.That(menu1, Is.Not.SameAs(menu2), "Builder should create independent instances");
            Assert.That(menu1.Items.Count, Is.EqualTo(menu2.Items.Count), "But with same structure");
        }

        [Test]
        public void BuildContextMenu_MenuItemsHaveCorrectActions()
        {
            // Arrange
            var historyExecuted = false;
            var settingsExecuted = false;
            var exitExecuted = false;

            Action historyAction = () => historyExecuted = true;
            Action settingsAction = () => settingsExecuted = true;
            Action exitAction = () => exitExecuted = true;

            // Act
            var menu = _builder.BuildContextMenu(historyAction, settingsAction, exitAction);
            var items = menu.Items.Cast<object>().ToList();

            // Simuler les clics
            ((MenuItem)items[0]).RaiseEvent(new System.Windows.RoutedEventArgs(MenuItem.ClickEvent)); // Historique
            ((MenuItem)items[1]).RaiseEvent(new System.Windows.RoutedEventArgs(MenuItem.ClickEvent)); // Paramètres
            ((MenuItem)items[3]).RaiseEvent(new System.Windows.RoutedEventArgs(MenuItem.ClickEvent)); // Quitter

            // Assert
            Assert.That(historyExecuted, Is.True, "History action should be executed");
            Assert.That(settingsExecuted, Is.True, "Settings action should be executed");
            Assert.That(exitExecuted, Is.True, "Exit action should be executed");
        }

        [TearDown]
        public void TearDown()
        {
            // Nettoyer les ressources si nécessaire
        }
    }
}
