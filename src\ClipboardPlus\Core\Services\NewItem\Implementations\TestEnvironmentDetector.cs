using System;
using System.Diagnostics;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.Services;
using WpfApplication = System.Windows.Application;

namespace ClipboardPlus.Core.Services.NewItem.Implementations
{
    /// <summary>
    /// Implémentation de ITestEnvironmentDetector qui détecte si l'application
    /// s'exécute dans un environnement de test en analysant le contexte d'exécution.
    /// </summary>
    public class TestEnvironmentDetector : ITestEnvironmentDetector
    {
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de TestEnvironmentDetector.
        /// </summary>
        /// <param name="loggingService">Service de logging optionnel pour tracer les détections.</param>
        public TestEnvironmentDetector(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Détermine si le code s'exécute dans un environnement de test.
        /// Cette implémentation utilise la même logique que l'ancienne méthode IsInTestEnvironment()
        /// pour maintenir la compatibilité comportementale.
        /// </summary>
        /// <returns>true si l'application s'exécute dans un contexte de test, false sinon.</returns>
        public bool IsInTestEnvironment()
        {
            try
            {
                // Si WpfApplication.Current est null (par exemple, dans un test unitaire pur sans App Host),
                // on pourrait considérer que c'est un environnement de test.
                if (WpfApplication.Current == null) return true;

                // Si le Dispatcher est null, cela pourrait aussi indiquer un contexte de test non-UI.
                if (WpfApplication.Current.Dispatcher == null) return true;

                // Si Debugger.IsAttached est vrai, c'est un bon indicateur, mais pas suffisant seul.
                if (Debugger.IsAttached)
                {
                    var stackTrace = new StackTrace();
                    var frames = stackTrace.GetFrames();
                    if (frames != null)
                    {
                        foreach (var frame in frames)
                        {
                            var method = frame.GetMethod();
                            if (method?.DeclaringType?.FullName?.Contains("Test", StringComparison.OrdinalIgnoreCase) == true ||
                                method?.Name?.Contains("Test", StringComparison.OrdinalIgnoreCase) == true)
                            {
                                _loggingService?.LogInfo("TestEnvironmentDetector: Contexte de test détecté via la pile d'appels.");
                                return true; // Contexte de test probable
                            }
                        }
                    }
                }

                return false; // Pas un contexte de test probable
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors de la détection de l'environnement de test: {ex.Message}", ex);
                return false; // En cas d'erreur, considérer comme mode normal
            }
        }
    }
}
