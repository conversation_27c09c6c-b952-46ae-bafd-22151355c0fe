// ============================================================================
// INTERFACE HISTORY VIEWMODEL MANAGER - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Interface pour la gestion de l'historique dans le ViewModel
// 📋 RESPONSABILITÉ : Gestion des collections, recherche, synchronisation
// 🏗️ ARCHITECTURE : Délégation pure vers HistoryModule existant
//
// ============================================================================

using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.UI.ViewModels.Managers.Interfaces
{
    /// <summary>
    /// Interface pour le manager de gestion de l'historique dans le ViewModel.
    /// 
    /// Ce manager est responsable de :
    /// - La gestion des collections observables (HistoryItems)
    /// - La synchronisation avec le HistoryModule
    /// - Le filtrage et la recherche
    /// - La sélection d'éléments
    /// - L'état de chargement
    /// </summary>
    public interface IHistoryViewModelManager : IDisposable
    {
        #region Propriétés Observables

        /// <summary>
        /// Collection observable des éléments d'historique affichés dans l'UI.
        /// Cette collection est synchronisée avec le HistoryModule.
        /// </summary>
        ObservableCollection<ClipboardItem> HistoryItems { get; }

        /// <summary>
        /// Élément actuellement sélectionné dans l'interface utilisateur.
        /// </summary>
        ClipboardItem? SelectedClipboardItem { get; set; }

        /// <summary>
        /// Texte de recherche/filtrage actuel.
        /// </summary>
        string? SearchText { get; set; }

        /// <summary>
        /// Indique si une opération de chargement est en cours.
        /// </summary>
        bool IsLoading { get; }

        /// <summary>
        /// Nombre total d'éléments dans l'historique (avant filtrage).
        /// </summary>
        int TotalItemCount { get; }

        /// <summary>
        /// Nombre d'éléments visibles après filtrage.
        /// </summary>
        int FilteredItemCount { get; }

        #endregion

        #region Événements

        /// <summary>
        /// Événement déclenché lorsque l'historique change.
        /// </summary>
        event EventHandler? HistoryChanged;

        /// <summary>
        /// Événement déclenché lorsque la sélection change.
        /// </summary>
        event EventHandler<ClipboardItem?>? SelectionChanged;

        /// <summary>
        /// Événement déclenché lorsque le filtre de recherche change.
        /// </summary>
        event EventHandler<string?>? SearchFilterChanged;

        /// <summary>
        /// Événement déclenché lorsque l'état de chargement change.
        /// </summary>
        event EventHandler<bool>? LoadingStateChanged;

        #endregion

        #region Méthodes de Gestion de l'Historique

        /// <summary>
        /// Charge l'historique depuis la source de données via le HistoryModule.
        /// </summary>
        /// <param name="callContext">Contexte de l'appel pour le debugging</param>
        /// <returns>Task représentant l'opération de chargement</returns>
        Task LoadHistoryAsync(string callContext = "HistoryViewModelManager");

        /// <summary>
        /// Recharge l'historique en forçant une synchronisation.
        /// </summary>
        /// <param name="reason">Raison du rechargement</param>
        /// <returns>Task représentant l'opération de rechargement</returns>
        Task ReloadHistoryAsync(string reason = "Manual reload");

        /// <summary>
        /// Force une synchronisation des collections avec le HistoryModule.
        /// </summary>
        /// <param name="reason">Raison de la synchronisation forcée</param>
        /// <returns>Task représentant l'opération de synchronisation</returns>
        Task ForceSynchronizationAsync(string reason = "Manual sync");

        #endregion

        #region Méthodes de Filtrage et Recherche

        /// <summary>
        /// Applique un filtre de recherche sur les éléments d'historique.
        /// </summary>
        /// <param name="searchFilter">Filtre à appliquer (null pour effacer)</param>
        void ApplySearchFilter(string? searchFilter);

        /// <summary>
        /// Efface le filtre de recherche actuel.
        /// </summary>
        void ClearSearchFilter();

        #endregion

        #region Méthodes de Sélection

        /// <summary>
        /// Sélectionne un élément spécifique.
        /// </summary>
        /// <param name="item">Élément à sélectionner</param>
        void SelectItem(ClipboardItem? item);

        /// <summary>
        /// Sélectionne l'élément suivant dans la liste filtrée.
        /// </summary>
        void SelectNextItem();

        /// <summary>
        /// Sélectionne l'élément précédent dans la liste filtrée.
        /// </summary>
        void SelectPreviousItem();

        /// <summary>
        /// Sélectionne le premier élément de la liste filtrée.
        /// </summary>
        void SelectFirstItem();

        /// <summary>
        /// Sélectionne le dernier élément de la liste filtrée.
        /// </summary>
        void SelectLastItem();

        #endregion

        #region Méthodes Utilitaires

        /// <summary>
        /// Trouve un élément par son ID.
        /// </summary>
        /// <param name="id">ID de l'élément</param>
        /// <returns>Élément trouvé ou null</returns>
        ClipboardItem? FindItemById(long id);

        /// <summary>
        /// Vérifie si un élément est actuellement visible (après filtrage).
        /// </summary>
        /// <param name="item">Élément à vérifier</param>
        /// <returns>True si l'élément est visible</returns>
        bool IsItemVisible(ClipboardItem item);

        /// <summary>
        /// Obtient l'index d'un élément dans la collection filtrée.
        /// </summary>
        /// <param name="item">Élément dont on veut l'index</param>
        /// <returns>Index de l'élément ou -1 si non trouvé</returns>
        int GetItemIndex(ClipboardItem item);

        /// <summary>
        /// Obtient les statistiques de l'historique.
        /// </summary>
        /// <returns>Statistiques de l'historique</returns>
        HistoryStatistics GetStatistics();

        #endregion

        #region Méthodes d'Initialisation et Nettoyage

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        /// <returns>Task représentant l'initialisation</returns>
        Task InitializeAsync();

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        void Cleanup();

        #endregion
    }
}
