using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Modules.Core;
using ClipboardPlus.Modules.Core.Events;
using Prism.Events;

namespace ClipboardPlus.Modules.History
{
    /// <summary>
    /// Implémentation du module de gestion de l'historique.
    /// 
    /// Ce module encapsule toute la logique de gestion des collections d'historique,
    /// du filtrage, de la synchronisation et des opérations sur les éléments.
    /// </summary>
    public class HistoryModule : ModuleBase, IHistoryModule
    {
        private readonly IClipboardHistoryManager _historyManager;
        private readonly ILoggingService _loggingService;
        private readonly IEventAggregator _eventAggregator;

        private readonly ObservableCollection<ClipboardItem> _historyItems;
        private readonly ObservableCollection<ClipboardItem> _filteredItems;
        
        private ClipboardItem? _selectedItem;
        private string? _searchFilter;
        private bool _isSynchronizing;

        /// <inheritdoc />
        public override string ModuleName => "HistoryModule";

        /// <inheritdoc />
        public override Version ModuleVersion => new Version(1, 0, 0);

        /// <inheritdoc />
        public ObservableCollection<ClipboardItem> HistoryItems => _historyItems;

        /// <inheritdoc />
        public ObservableCollection<ClipboardItem> FilteredItems => _filteredItems;

        /// <inheritdoc />
        public ClipboardItem? SelectedItem
        {
            get => _selectedItem;
            set
            {
                var previousItem = _selectedItem;
                _selectedItem = value;
                OnSelectionChanged(new HistorySelectionChangedEventArgs(previousItem, value));
            }
        }

        /// <inheritdoc />
        public string? SearchFilter
        {
            get => _searchFilter;
            set
            {
                var previousFilter = _searchFilter;
                _searchFilter = value;
                ApplyFilterInternal();
                OnFilterChanged(new HistoryFilterChangedEventArgs(previousFilter, value, FilteredItemCount));
            }
        }

        /// <inheritdoc />
        public bool IsSynchronizing => _isSynchronizing;

        /// <inheritdoc />
        public int TotalItemCount => _historyItems.Count;

        /// <inheritdoc />
        public int FilteredItemCount => _filteredItems.Count;

        /// <inheritdoc />
        public event EventHandler<HistoryChangedEventArgs>? HistoryChanged;

        /// <inheritdoc />
        public event EventHandler<HistorySelectionChangedEventArgs>? SelectionChanged;

        /// <inheritdoc />
        public event EventHandler<HistoryFilterChangedEventArgs>? FilterChanged;

        public HistoryModule(
            IClipboardHistoryManager historyManager,
            ILoggingService loggingService,
            IEventAggregator eventAggregator)
        {
            _historyManager = historyManager ?? throw new ArgumentNullException(nameof(historyManager));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));

            _historyItems = new ObservableCollection<ClipboardItem>();
            _filteredItems = new ObservableCollection<ClipboardItem>();
        }

        /// <inheritdoc />
        protected override async Task OnInitializeAsync()
        {
            _loggingService.LogInfo($"Initializing {ModuleName}...");

            // S'abonner aux événements du gestionnaire d'historique
            _historyManager.HistoryChanged += OnHistoryManagerHistoryChanged;

            // Charger l'historique initial
            await LoadHistoryAsync("Initialization");

            _loggingService.LogInfo($"{ModuleName} initialized successfully with {TotalItemCount} items");
        }

        /// <inheritdoc />
        protected override Task OnStartAsync()
        {
            _loggingService.LogInfo($"Starting {ModuleName}...");
            
            // Publier un événement de démarrage
            _eventAggregator.GetEvent<ModuleStateChangedPrismEvent>().Publish(
                new ModuleStateChangedEvent(ModuleName, ClipboardPlus.Modules.Core.ModuleState.Starting, ClipboardPlus.Modules.Core.ModuleState.Running));
            
            return Task.CompletedTask;
        }

        /// <inheritdoc />
        protected override Task OnStopAsync()
        {
            _loggingService.LogInfo($"Stopping {ModuleName}...");
            
            // Publier un événement d'arrêt
            _eventAggregator.GetEvent<ModuleStateChangedPrismEvent>().Publish(
                new ModuleStateChangedEvent(ModuleName, ClipboardPlus.Modules.Core.ModuleState.Running, ClipboardPlus.Modules.Core.ModuleState.Stopping));
            
            return Task.CompletedTask;
        }

        /// <inheritdoc />
        protected override void OnDispose()
        {
            // Se désabonner des événements
            _historyManager.HistoryChanged -= OnHistoryManagerHistoryChanged;
            
            // Nettoyer les collections
            _historyItems.Clear();
            _filteredItems.Clear();
            
            _loggingService.LogInfo($"{ModuleName} disposed");
        }

        /// <inheritdoc />
        public async Task LoadHistoryAsync(string callContext = "Unknown")
        {
            try
            {
                _isSynchronizing = true;
                _loggingService.LogInfo($"Loading history from context: {callContext}");

                var items = _historyManager.HistoryItems;
                
                _historyItems.Clear();
                foreach (var item in items)
                {
                    _historyItems.Add(item);
                }

                ApplyFilterInternal();
                OnHistoryChanged(new HistoryChangedEventArgs(HistoryChangeType.HistoryReloaded, items, callContext));

                _loggingService.LogInfo($"History loaded successfully: {TotalItemCount} items");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Failed to load history from context {callContext}", ex);
                throw;
            }
            finally
            {
                _isSynchronizing = false;
            }
        }

        /// <inheritdoc />
        public async Task ReloadHistoryAsync(string reason = "Manual reload")
        {
            await LoadHistoryAsync($"Reload: {reason}");
        }

        /// <inheritdoc />
        public async Task ForceSynchronizationAsync(string reason = "Manual sync")
        {
            try
            {
                _isSynchronizing = true;
                _loggingService.LogInfo($"Force synchronization requested: {reason}");

                // Recharger complètement l'historique
                await LoadHistoryAsync($"ForcSync: {reason}");
                
                OnHistoryChanged(new HistoryChangedEventArgs(HistoryChangeType.CollectionsSynchronized, _historyItems, reason));
                
                _loggingService.LogInfo("Force synchronization completed successfully");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Force synchronization failed: {reason}", ex);
                throw;
            }
            finally
            {
                _isSynchronizing = false;
            }
        }

        /// <inheritdoc />
        public async Task AddItemAsync(ClipboardItem item)
        {
            if (item == null) throw new ArgumentNullException(nameof(item));

            try
            {
                await _historyManager.AddItemAsync(item);
                _historyItems.Insert(0, item); // Ajouter en tête
                ApplyFilterInternal();
                OnHistoryChanged(new HistoryChangedEventArgs(HistoryChangeType.ItemAdded, item));
                
                _loggingService.LogInfo($"Item added to history: {item.Id}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Failed to add item {item.Id}", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task RemoveItemAsync(ClipboardItem item)
        {
            if (item == null) throw new ArgumentNullException(nameof(item));

            try
            {
                await _historyManager.DeleteItemAsync(item.Id);
                _historyItems.Remove(item);
                _filteredItems.Remove(item);
                
                // Désélectionner si c'était l'élément sélectionné
                if (SelectedItem?.Id == item.Id)
                {
                    SelectedItem = null;
                }
                
                OnHistoryChanged(new HistoryChangedEventArgs(HistoryChangeType.ItemRemoved, item));
                
                _loggingService.LogInfo($"Item removed from history: {item.Id}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Failed to remove item {item.Id}", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task RemoveItemsAsync(IEnumerable<ClipboardItem> items)
        {
            var itemList = items?.ToList() ?? throw new ArgumentNullException(nameof(items));
            
            foreach (var item in itemList)
            {
                await RemoveItemAsync(item);
            }
        }

        /// <inheritdoc />
        public async Task ClearHistoryAsync()
        {
            try
            {
                // ✅ CORRECTION: Préserver les éléments épinglés lors de "Supprimer Tout"
                await _historyManager.ClearHistoryAsync(preservePinned: true);

                // Mettre à jour les collections locales - garder seulement les éléments épinglés
                var historyItemsCopy = _historyItems.ToList();
                var pinnedItems = historyItemsCopy.Where(item => item.IsPinned).ToList();
                var removedItems = historyItemsCopy.Where(item => !item.IsPinned).ToList();

                _historyItems.Clear();
                _filteredItems.Clear();

                // Remettre les éléments épinglés
                foreach (var pinnedItem in pinnedItems)
                {
                    _historyItems.Add(pinnedItem);
                    _filteredItems.Add(pinnedItem);
                }

                SelectedItem = null;

                OnHistoryChanged(new HistoryChangedEventArgs(HistoryChangeType.HistoryCleared, removedItems));

                _loggingService.LogInfo($"History cleared successfully (kept {pinnedItems.Count} pinned items, removed {removedItems.Count} items)");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Failed to clear history", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task UpdateItemAsync(ClipboardItem item)
        {
            if (item == null) throw new ArgumentNullException(nameof(item));

            try
            {
                // Note: IClipboardHistoryManager n'a pas de UpdateItemAsync,
                // nous devons utiliser AddItemAsync pour mettre à jour
                await _historyManager.AddItemAsync(item);
                
                // Mettre à jour dans les collections locales
                var historyItemsCopy = _historyItems.ToList();
                var existingItem = historyItemsCopy.FirstOrDefault(i => i.Id == item.Id);
                if (existingItem != null)
                {
                    var index = _historyItems.IndexOf(existingItem);
                    _historyItems[index] = item;
                    ApplyFilterInternal();
                }
                
                OnHistoryChanged(new HistoryChangedEventArgs(HistoryChangeType.ItemUpdated, item));
                
                _loggingService.LogInfo($"Item updated in history: {item.Id}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Failed to update item {item.Id}", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public void ApplyFilter(string? filter)
        {
            SearchFilter = filter;
        }

        /// <inheritdoc />
        public void ClearFilter()
        {
            SearchFilter = null;
        }

        /// <inheritdoc />
        public void SelectItem(ClipboardItem? item)
        {
            SelectedItem = item;
        }

        /// <inheritdoc />
        public void SelectNextItem()
        {
            if (_filteredItems.Count == 0) return;

            var currentIndex = SelectedItem != null ? _filteredItems.IndexOf(SelectedItem) : -1;
            var nextIndex = (currentIndex + 1) % _filteredItems.Count;
            SelectedItem = _filteredItems[nextIndex];
        }

        /// <inheritdoc />
        public void SelectPreviousItem()
        {
            if (_filteredItems.Count == 0) return;

            var currentIndex = SelectedItem != null ? _filteredItems.IndexOf(SelectedItem) : 0;
            var previousIndex = currentIndex > 0 ? currentIndex - 1 : _filteredItems.Count - 1;
            SelectedItem = _filteredItems[previousIndex];
        }

        /// <inheritdoc />
        public ClipboardItem? FindItemById(long id)
        {
            var historyItemsCopy = _historyItems.ToList();
            return historyItemsCopy.FirstOrDefault(item => item.Id == id);
        }

        /// <inheritdoc />
        public IEnumerable<ClipboardItem> FindItems(Func<ClipboardItem, bool> predicate)
        {
            var historyItemsCopy = _historyItems.ToList();
            return historyItemsCopy.Where(predicate);
        }

        /// <inheritdoc />
        public HistoryStatistics GetStatistics()
        {
            var historyItemsCopy = _historyItems.ToList();
            return new HistoryStatistics
            {
                TotalItems = TotalItemCount,
                FilteredItems = FilteredItemCount,
                TotalSizeBytes = historyItemsCopy.Sum(item => item.RawData?.Length ?? 0),
                LastAddedDate = historyItemsCopy.FirstOrDefault()?.Timestamp,
                LastSyncDate = DateTime.UtcNow, // TODO: Tracker la vraie date de sync
                SyncCount = 1, // TODO: Tracker le vrai nombre de syncs
                CurrentFilter = SearchFilter
            };
        }

        private void ApplyFilterInternal()
        {
            _filteredItems.Clear();

            // Créer une copie de la collection pour éviter les modifications concurrentes
            var historyItemsCopy = _historyItems.ToList();

            var itemsToShow = string.IsNullOrWhiteSpace(_searchFilter)
                ? historyItemsCopy
                : historyItemsCopy.Where(item =>
                    item.TextPreview?.Contains(_searchFilter, StringComparison.OrdinalIgnoreCase) == true ||
                    item.CustomName?.Contains(_searchFilter, StringComparison.OrdinalIgnoreCase) == true);

            foreach (var item in itemsToShow)
            {
                _filteredItems.Add(item);
            }
        }

        private void OnHistoryManagerHistoryChanged(object? sender, EventArgs e)
        {
            // Recharger l'historique quand le gestionnaire signale un changement
            _ = Task.Run(async () =>
            {
                try
                {
                    await LoadHistoryAsync("HistoryManager notification");
                }
                catch (Exception ex)
                {
                    _loggingService.LogError("Failed to reload history after manager notification", ex);
                }
            });
        }

        protected virtual void OnHistoryChanged(HistoryChangedEventArgs e)
        {
            HistoryChanged?.Invoke(this, e);
            
            // Publier aussi sur le bus d'événements Prism
            _eventAggregator.GetEvent<HistoryModulePrismEvent>().Publish(new HistoryModuleEventData
            {
                ModuleName = ModuleName,
                ChangeType = e.ChangeType.ToString(),
                AffectedItems = e.AffectedItems,
                Context = e.Context ?? string.Empty
            });
        }

        protected virtual void OnSelectionChanged(HistorySelectionChangedEventArgs e)
        {
            SelectionChanged?.Invoke(this, e);
        }

        protected virtual void OnFilterChanged(HistoryFilterChangedEventArgs e)
        {
            FilterChanged?.Invoke(this, e);
        }
    }

    /// <summary>
    /// Événement spécifique au module d'historique.
    /// </summary>
    public class HistoryModuleEvent : ModuleEventBase
    {
        public HistoryChangeType ChangeType { get; }
        public IReadOnlyList<ClipboardItem> AffectedItems { get; }
        public string? Context { get; }

        public HistoryModuleEvent(string sourceModule, HistoryChangeType changeType, 
            IEnumerable<ClipboardItem> affectedItems, string? context = null)
            : base(sourceModule)
        {
            ChangeType = changeType;
            AffectedItems = affectedItems.ToList().AsReadOnly();
            Context = context;
        }
    }
}
