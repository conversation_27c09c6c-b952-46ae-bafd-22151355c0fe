using System;
using System.Linq;
using NUnit.Framework;
using ClipboardPlus.Services;

namespace ClipboardPlus.Tests.Unit.Services
{
    /// <summary>
    /// Tests unitaires pour FileSizeFormatter.
    /// Valide le formatage des tailles de fichiers selon les standards.
    /// </summary>
    [TestFixture]
    public class FileSizeFormatterTests
    {
        private FileSizeFormatter _formatter;

        [SetUp]
        public void Setup()
        {
            _formatter = new FileSizeFormatter();
        }

        #region FormatSize Tests

        [Test]
        public void FormatSize_WithZeroBytes_ReturnsZeroB()
        {
            // Act
            string result = _formatter.FormatSize(0);

            // Assert
            Assert.AreEqual("0 B", result);
        }

        [Test]
        public void FormatSize_WithBytesOnly_ReturnsBytes()
        {
            // Act
            string result = _formatter.FormatSize(512);

            // Assert
            Assert.AreEqual("512.0 B", result);
        }

        [Test]
        public void FormatSize_WithKilobytes_ReturnsKB()
        {
            // Act
            string result = _formatter.FormatSize(1536); // 1.5 KB

            // Assert
            Assert.AreEqual("1.5 KB", result);
        }

        [Test]
        public void FormatSize_WithMegabytes_ReturnsMB()
        {
            // Act
            string result = _formatter.FormatSize(2621440); // 2.5 MB

            // Assert
            Assert.AreEqual("2.5 MB", result);
        }

        [Test]
        public void FormatSize_WithGigabytes_ReturnsGB()
        {
            // Act
            string result = _formatter.FormatSize(3221225472); // 3 GB

            // Assert
            Assert.AreEqual("3.0 GB", result);
        }

        [Test]
        public void FormatSize_WithTerabytes_ReturnsTB()
        {
            // Act
            string result = _formatter.FormatSize(1099511627776); // 1 TB

            // Assert
            Assert.AreEqual("1.0 TB", result);
        }

        [Test]
        public void FormatSize_WithNegativeBytes_ReturnsZeroB()
        {
            // Act
            string result = _formatter.FormatSize(-100);

            // Assert
            Assert.AreEqual("0 B", result);
        }

        [Test]
        public void FormatSize_WithCustomDecimalPlaces_ReturnsCorrectPrecision()
        {
            // Act
            string result = _formatter.FormatSize(1536, 2); // 1.5 KB avec 2 décimales

            // Assert
            Assert.AreEqual("1.50 KB", result);
        }

        [Test]
        public void FormatSize_WithZeroDecimalPlaces_ReturnsWholeNumbers()
        {
            // Act
            string result = _formatter.FormatSize(1536, 0); // 1.5 KB avec 0 décimales

            // Assert
            Assert.AreEqual("2 KB", result); // Arrondi à 2
        }

        #endregion

        #region GetAppropriateUnit Tests

        [Test]
        public void GetAppropriateUnit_WithBytes_ReturnsB()
        {
            // Act
            string result = _formatter.GetAppropriateUnit(512);

            // Assert
            Assert.AreEqual("B", result);
        }

        [Test]
        public void GetAppropriateUnit_WithKilobytes_ReturnsKB()
        {
            // Act
            string result = _formatter.GetAppropriateUnit(1536);

            // Assert
            Assert.AreEqual("KB", result);
        }

        [Test]
        public void GetAppropriateUnit_WithMegabytes_ReturnsMB()
        {
            // Act
            string result = _formatter.GetAppropriateUnit(2621440);

            // Assert
            Assert.AreEqual("MB", result);
        }

        [Test]
        public void GetAppropriateUnit_WithGigabytes_ReturnsGB()
        {
            // Act
            string result = _formatter.GetAppropriateUnit(3221225472);

            // Assert
            Assert.AreEqual("GB", result);
        }

        [Test]
        public void GetAppropriateUnit_WithTerabytes_ReturnsTB()
        {
            // Act
            string result = _formatter.GetAppropriateUnit(1099511627776);

            // Assert
            Assert.AreEqual("TB", result);
        }

        [Test]
        public void GetAppropriateUnit_WithNegativeBytes_ReturnsB()
        {
            // Act
            string result = _formatter.GetAppropriateUnit(-100);

            // Assert
            Assert.AreEqual("B", result);
        }

        #endregion

        #region ConvertToUnit Tests

        [Test]
        public void ConvertToUnit_ToBytesFromBytes_ReturnsOriginalValue()
        {
            // Act
            double result = _formatter.ConvertToUnit(1024, "B");

            // Assert
            Assert.AreEqual(1024.0, result, 0.001);
        }

        [Test]
        public void ConvertToUnit_ToKBFromBytes_ReturnsCorrectConversion()
        {
            // Act
            double result = _formatter.ConvertToUnit(1024, "KB");

            // Assert
            Assert.AreEqual(1.0, result, 0.001);
        }

        [Test]
        public void ConvertToUnit_ToMBFromBytes_ReturnsCorrectConversion()
        {
            // Act
            double result = _formatter.ConvertToUnit(1048576, "MB"); // 1 MB

            // Assert
            Assert.AreEqual(1.0, result, 0.001);
        }

        [Test]
        public void ConvertToUnit_WithUnsupportedUnit_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _formatter.ConvertToUnit(1024, "XB"));
        }

        [Test]
        public void ConvertToUnit_WithNullUnit_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _formatter.ConvertToUnit(1024, null!));
        }

        [Test]
        public void ConvertToUnit_WithCaseInsensitiveUnit_WorksCorrectly()
        {
            // Act
            double result1 = _formatter.ConvertToUnit(1024, "kb");
            double result2 = _formatter.ConvertToUnit(1024, "KB");

            // Assert
            Assert.AreEqual(result1, result2, 0.001, "La conversion doit être insensible à la casse");
        }

        #endregion

        #region IsSupportedUnit Tests

        [Test]
        public void IsSupportedUnit_WithValidUnits_ReturnsTrue()
        {
            // Act & Assert
            Assert.IsTrue(_formatter.IsSupportedUnit("B"));
            Assert.IsTrue(_formatter.IsSupportedUnit("KB"));
            Assert.IsTrue(_formatter.IsSupportedUnit("MB"));
            Assert.IsTrue(_formatter.IsSupportedUnit("GB"));
            Assert.IsTrue(_formatter.IsSupportedUnit("TB"));
        }

        [Test]
        public void IsSupportedUnit_WithInvalidUnit_ReturnsFalse()
        {
            // Act & Assert
            Assert.IsFalse(_formatter.IsSupportedUnit("XB"));
            Assert.IsFalse(_formatter.IsSupportedUnit("PB"));
        }

        [Test]
        public void IsSupportedUnit_WithNullOrEmpty_ReturnsFalse()
        {
            // Act & Assert
            Assert.IsFalse(_formatter.IsSupportedUnit(null));
            Assert.IsFalse(_formatter.IsSupportedUnit(""));
            Assert.IsFalse(_formatter.IsSupportedUnit("   "));
        }

        [Test]
        public void IsSupportedUnit_WithCaseInsensitive_ReturnsTrue()
        {
            // Act & Assert
            Assert.IsTrue(_formatter.IsSupportedUnit("kb"));
            Assert.IsTrue(_formatter.IsSupportedUnit("Mb"));
            Assert.IsTrue(_formatter.IsSupportedUnit("gb"));
        }

        #endregion

        #region GetSupportedUnits Tests

        [Test]
        public void GetSupportedUnits_ReturnsAllExpectedUnits()
        {
            // Act
            var result = _formatter.GetSupportedUnits().ToList();

            // Assert
            Assert.Contains("B", result);
            Assert.Contains("KB", result);
            Assert.Contains("MB", result);
            Assert.Contains("GB", result);
            Assert.Contains("TB", result);
            Assert.AreEqual(5, result.Count, "Doit retourner exactement 5 unités");
        }

        [Test]
        public void GetSupportedUnits_ReturnsUnitsInCorrectOrder()
        {
            // Act
            var result = _formatter.GetSupportedUnits().ToArray();

            // Assert
            Assert.AreEqual("B", result[0]);
            Assert.AreEqual("KB", result[1]);
            Assert.AreEqual("MB", result[2]);
            Assert.AreEqual("GB", result[3]);
            Assert.AreEqual("TB", result[4]);
        }

        #endregion
    }
}
