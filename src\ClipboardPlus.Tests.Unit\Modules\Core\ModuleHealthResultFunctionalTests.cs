using NUnit.Framework;
using ClipboardPlus.Modules.Core;
using System;
using System.Collections.Generic;

namespace ClipboardPlus.Tests.Unit.Modules.Core
{
    /// <summary>
    /// Tests fonctionnels pour ModuleHealthResult - vérifient le comportement métier
    /// dans des scénarios d'usage réels de monitoring de santé des modules
    /// </summary>
    [TestFixture]
    public class ModuleHealthResultFunctionalTests
    {
        #region Scénarios métier de santé positive

        [Test]
        public void ModuleHealthResult_HealthyDatabaseModule_ShouldReportOptimalPerformance()
        {
            // Arrange - Scénario : Module de base de données en parfaite santé
            var isHealthy = true;
            var message = "Database module is operating optimally with excellent performance metrics";
            var details = new Dictionary<string, object>
            {
                ["ConnectionPoolSize"] = 50,
                ["ActiveConnections"] = 12,
                ["AverageQueryTimeMs"] = 15.5,
                ["DatabaseSize"] = "2.3GB",
                ["LastBackup"] = DateTime.UtcNow.AddHours(-2),
                ["IndexOptimizationStatus"] = "Completed",
                ["MemoryUsageMB"] = 256
            };

            // Act
            var healthResult = new ModuleHealthResult(isHealthy, message, details);

            // Assert - Vérifier que la santé optimale est reportée
            Assert.That(healthResult.IsHealthy, Is.True);
            Assert.That(healthResult.Message, Does.Contain("optimally"));
            Assert.That(healthResult.Message, Does.Contain("excellent performance"));
            Assert.That(healthResult.Details.Count, Is.EqualTo(7));
            Assert.That(healthResult.Details["ConnectionPoolSize"], Is.EqualTo(50));
            Assert.That(healthResult.Details["AverageQueryTimeMs"], Is.EqualTo(15.5));
            Assert.That(healthResult.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void ModuleHealthResult_HealthyUIModule_ShouldReportResponsiveInterface()
        {
            // Arrange - Scénario : Module UI réactif et performant
            var isHealthy = true;
            var message = "UI module is responsive with smooth user interactions";
            var details = new Dictionary<string, object>
            {
                ["UIThreadUtilization"] = "15%",
                ["AverageRenderTimeMs"] = 8.2,
                ["PendingUIUpdates"] = 0,
                ["MemoryLeaks"] = false,
                ["UserSatisfactionScore"] = 4.8,
                ["LastUIFreeze"] = "Never",
                ["ActiveWindows"] = 3
            };

            // Act
            var healthResult = new ModuleHealthResult(isHealthy, message, details);

            // Assert - Vérifier que l'interface réactive est reportée
            Assert.That(healthResult.IsHealthy, Is.True);
            Assert.That(healthResult.Message, Does.Contain("responsive"));
            Assert.That(healthResult.Message, Does.Contain("smooth"));
            Assert.That(healthResult.Details["MemoryLeaks"], Is.EqualTo(false));
            Assert.That(healthResult.Details["UserSatisfactionScore"], Is.EqualTo(4.8));
            Assert.That(healthResult.Details["PendingUIUpdates"], Is.EqualTo(0));
        }

        [Test]
        public void ModuleHealthResult_HealthyFactoryMethod_ShouldCreateOptimizedHealthyResult()
        {
            // Arrange - Scénario : Utilisation de la factory method pour santé positive
            var customMessage = "System is running at peak performance";

            // Act
            var healthResult = ModuleHealthResult.Healthy(customMessage);

            // Assert - Vérifier que la factory method crée un résultat sain
            Assert.That(healthResult.IsHealthy, Is.True);
            Assert.That(healthResult.Message, Is.EqualTo(customMessage));
            Assert.That(healthResult.Details, Is.Not.Null);
            Assert.That(healthResult.Details.Count, Is.EqualTo(0)); // Pas de détails par défaut
            Assert.That(healthResult.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void ModuleHealthResult_DefaultHealthyMessage_ShouldUseStandardMessage()
        {
            // Arrange & Act - Scénario : Message de santé par défaut
            var healthResult = ModuleHealthResult.Healthy();

            // Assert - Vérifier que le message par défaut est utilisé
            Assert.That(healthResult.IsHealthy, Is.True);
            Assert.That(healthResult.Message, Is.EqualTo("Module is healthy"));
            Assert.That(healthResult.Details, Is.Not.Null);
            Assert.That(healthResult.Details.Count, Is.EqualTo(0));
        }

        #endregion

        #region Scénarios métier de santé dégradée

        [Test]
        public void ModuleHealthResult_UnhealthyMemoryModule_ShouldReportMemoryPressure()
        {
            // Arrange - Scénario : Module avec pression mémoire critique
            var isHealthy = false;
            var message = "Memory module is experiencing critical pressure with potential performance impact";
            var details = new Dictionary<string, object>
            {
                ["MemoryUsagePercent"] = 95,
                ["AvailableMemoryMB"] = 128,
                ["MemoryLeaksDetected"] = 3,
                ["GCPressure"] = "High",
                ["LastOutOfMemoryException"] = DateTime.UtcNow.AddMinutes(-5),
                ["RecommendedAction"] = "Immediate memory cleanup required",
                ["CriticalityLevel"] = "High"
            };

            // Act
            var healthResult = new ModuleHealthResult(isHealthy, message, details);

            // Assert - Vérifier que la pression mémoire critique est reportée
            Assert.That(healthResult.IsHealthy, Is.False);
            Assert.That(healthResult.Message, Does.Contain("critical pressure"));
            Assert.That(healthResult.Details["MemoryUsagePercent"], Is.EqualTo(95));
            Assert.That(healthResult.Details["MemoryLeaksDetected"], Is.EqualTo(3));
            Assert.That(healthResult.Details["CriticalityLevel"], Is.EqualTo("High"));
            Assert.That(healthResult.Details["RecommendedAction"], Does.Contain("cleanup required"));
        }

        [Test]
        public void ModuleHealthResult_UnhealthyNetworkModule_ShouldReportConnectivityIssues()
        {
            // Arrange - Scénario : Module réseau avec problèmes de connectivité
            var isHealthy = false;
            var message = "Network module is experiencing connectivity issues affecting synchronization";
            var details = new Dictionary<string, object>
            {
                ["ConnectionStatus"] = "Unstable",
                ["PacketLossPercent"] = 15.7,
                ["AverageLatencyMs"] = 2500,
                ["FailedRequests"] = 23,
                ["LastSuccessfulSync"] = DateTime.UtcNow.AddHours(-4),
                ["RetryAttempts"] = 5,
                ["NetworkErrors"] = new[] { "Timeout", "DNS Resolution Failed", "Connection Refused" }
            };

            // Act
            var healthResult = new ModuleHealthResult(isHealthy, message, details);

            // Assert - Vérifier que les problèmes de connectivité sont reportés
            Assert.That(healthResult.IsHealthy, Is.False);
            Assert.That(healthResult.Message, Does.Contain("connectivity issues"));
            Assert.That(healthResult.Details["ConnectionStatus"], Is.EqualTo("Unstable"));
            Assert.That(healthResult.Details["PacketLossPercent"], Is.EqualTo(15.7));
            Assert.That(healthResult.Details["FailedRequests"], Is.EqualTo(23));
        }

        [Test]
        public void ModuleHealthResult_UnhealthyFactoryMethod_ShouldCreateDetailedUnhealthyResult()
        {
            // Arrange - Scénario : Utilisation de la factory method pour santé dégradée
            var message = "Critical system failure detected";
            var details = new Dictionary<string, object>
            {
                ["ErrorCode"] = "SYS_CRITICAL_001",
                ["FailureTime"] = DateTime.UtcNow,
                ["AffectedComponents"] = new[] { "Database", "UI", "Network" },
                ["RecoveryEstimate"] = "15 minutes"
            };

            // Act
            var healthResult = ModuleHealthResult.Unhealthy(message, details);

            // Assert - Vérifier que la factory method crée un résultat malsain détaillé
            Assert.That(healthResult.IsHealthy, Is.False);
            Assert.That(healthResult.Message, Is.EqualTo(message));
            Assert.That(healthResult.Details, Is.EqualTo(details));
            Assert.That(healthResult.Details["ErrorCode"], Is.EqualTo("SYS_CRITICAL_001"));
            Assert.That(healthResult.Details["RecoveryEstimate"], Is.EqualTo("15 minutes"));
        }

        #endregion

        #region Scénarios de monitoring en temps réel

        [Test]
        public void ModuleHealthResult_PerformanceMonitoring_ShouldTrackRealTimeMetrics()
        {
            // Arrange - Scénario : Monitoring de performance en temps réel
            var isHealthy = true;
            var message = "Performance monitoring shows stable operation within acceptable parameters";
            var details = new Dictionary<string, object>
            {
                ["CPUUsagePercent"] = 25.3,
                ["MemoryUsageMB"] = 512,
                ["DiskIOPS"] = 150,
                ["NetworkThroughputMbps"] = 45.7,
                ["ActiveThreads"] = 12,
                ["QueuedTasks"] = 3,
                ["ResponseTimeP95Ms"] = 120,
                ["ErrorRatePercent"] = 0.02,
                ["ThroughputPerSecond"] = 1250
            };

            // Act
            var healthResult = new ModuleHealthResult(isHealthy, message, details);

            // Assert - Vérifier que les métriques temps réel sont trackées
            Assert.That(healthResult.IsHealthy, Is.True);
            Assert.That(healthResult.Message, Does.Contain("stable operation"));
            Assert.That(healthResult.Details["CPUUsagePercent"], Is.EqualTo(25.3));
            Assert.That(healthResult.Details["ResponseTimeP95Ms"], Is.EqualTo(120));
            Assert.That(healthResult.Details["ErrorRatePercent"], Is.EqualTo(0.02));
            Assert.That(healthResult.Details["ThroughputPerSecond"], Is.EqualTo(1250));
        }

        [Test]
        public void ModuleHealthResult_SecurityMonitoring_ShouldTrackSecurityMetrics()
        {
            // Arrange - Scénario : Monitoring de sécurité avec métriques spécialisées
            var isHealthy = false;
            var message = "Security module detected potential threats requiring immediate attention";
            var details = new Dictionary<string, object>
            {
                ["ThreatLevel"] = "Medium",
                ["SuspiciousActivities"] = 7,
                ["FailedAuthenticationAttempts"] = 15,
                ["LastSecurityScan"] = DateTime.UtcNow.AddMinutes(-30),
                ["VulnerabilitiesDetected"] = 2,
                ["SecurityPatchLevel"] = "2024.01.15",
                ["EncryptionStatus"] = "Active",
                ["FirewallStatus"] = "Enabled",
                ["IntrusionAttempts"] = 3
            };

            // Act
            var healthResult = new ModuleHealthResult(isHealthy, message, details);

            // Assert - Vérifier que les métriques de sécurité sont trackées
            Assert.That(healthResult.IsHealthy, Is.False);
            Assert.That(healthResult.Message, Does.Contain("potential threats"));
            Assert.That(healthResult.Details["ThreatLevel"], Is.EqualTo("Medium"));
            Assert.That(healthResult.Details["SuspiciousActivities"], Is.EqualTo(7));
            Assert.That(healthResult.Details["VulnerabilitiesDetected"], Is.EqualTo(2));
            Assert.That(healthResult.Details["EncryptionStatus"], Is.EqualTo("Active"));
        }

        #endregion

        #region Scénarios de diagnostic avancé

        [Test]
        public void ModuleHealthResult_DiagnosticMode_ShouldProvideDetailedSystemAnalysis()
        {
            // Arrange - Scénario : Mode diagnostic avec analyse système détaillée
            var isHealthy = true;
            var message = "Diagnostic analysis completed - system operating within normal parameters";
            var details = new Dictionary<string, object>
            {
                ["DiagnosticMode"] = "Deep Analysis",
                ["SystemUptime"] = TimeSpan.FromDays(15.5),
                ["ModulesLoaded"] = 25,
                ["ModulesHealthy"] = 23,
                ["ModulesWarning"] = 2,
                ["ModulesCritical"] = 0,
                ["PerformanceScore"] = 87.5,
                ["StabilityIndex"] = 0.995,
                ["ResourceEfficiency"] = "Good",
                ["PredictedIssues"] = new[] { "Memory usage trending upward", "Disk space will be low in 30 days" }
            };

            // Act
            var healthResult = new ModuleHealthResult(isHealthy, message, details);

            // Assert - Vérifier que l'analyse diagnostique détaillée est fournie
            Assert.That(healthResult.IsHealthy, Is.True);
            Assert.That(healthResult.Message, Does.Contain("Diagnostic analysis"));
            Assert.That(healthResult.Details["DiagnosticMode"], Is.EqualTo("Deep Analysis"));
            Assert.That(healthResult.Details["ModulesHealthy"], Is.EqualTo(23));
            Assert.That(healthResult.Details["PerformanceScore"], Is.EqualTo(87.5));
            Assert.That(healthResult.Details["StabilityIndex"], Is.EqualTo(0.995));
        }

        [Test]
        public void ModuleHealthResult_PredictiveAnalysis_ShouldForecastPotentialIssues()
        {
            // Arrange - Scénario : Analyse prédictive pour anticiper les problèmes
            var isHealthy = true;
            var message = "Predictive analysis indicates potential issues in the near future";
            var details = new Dictionary<string, object>
            {
                ["AnalysisType"] = "Predictive",
                ["ForecastHorizon"] = "7 days",
                ["PredictedMemoryUsage"] = "85% in 3 days",
                ["PredictedDiskUsage"] = "90% in 5 days",
                ["RecommendedActions"] = new[] 
                { 
                    "Schedule memory cleanup", 
                    "Archive old clipboard data", 
                    "Optimize database indexes" 
                },
                ["ConfidenceLevel"] = 0.87,
                ["TrendAnalysis"] = "Memory usage increasing 2% daily",
                ["RiskLevel"] = "Low"
            };

            // Act
            var healthResult = new ModuleHealthResult(isHealthy, message, details);

            // Assert - Vérifier que l'analyse prédictive anticipe les problèmes
            Assert.That(healthResult.IsHealthy, Is.True);
            Assert.That(healthResult.Message, Does.Contain("Predictive analysis"));
            Assert.That(healthResult.Details["AnalysisType"], Is.EqualTo("Predictive"));
            Assert.That(healthResult.Details["ConfidenceLevel"], Is.EqualTo(0.87));
            Assert.That(healthResult.Details["RiskLevel"], Is.EqualTo("Low"));
        }

        #endregion

        #region Scénarios de gestion des détails null

        [Test]
        public void ModuleHealthResult_NullDetails_ShouldCreateEmptyDetailsDictionary()
        {
            // Arrange - Scénario : Création avec détails null
            var isHealthy = true;
            var message = "Simple health check without detailed metrics";
            Dictionary<string, object>? nullDetails = null;

            // Act
            var healthResult = new ModuleHealthResult(isHealthy, message, nullDetails);

            // Assert - Vérifier que les détails null créent un dictionnaire vide
            Assert.That(healthResult.IsHealthy, Is.True);
            Assert.That(healthResult.Message, Is.EqualTo(message));
            Assert.That(healthResult.Details, Is.Not.Null);
            Assert.That(healthResult.Details.Count, Is.EqualTo(0));
            Assert.That(healthResult.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void ModuleHealthResult_EmptyDetails_ShouldMaintainEmptyDictionary()
        {
            // Arrange - Scénario : Création avec dictionnaire de détails vide
            var isHealthy = false;
            var message = "Health check failed without specific details";
            var emptyDetails = new Dictionary<string, object>();

            // Act
            var healthResult = new ModuleHealthResult(isHealthy, message, emptyDetails);

            // Assert - Vérifier que le dictionnaire vide est maintenu
            Assert.That(healthResult.IsHealthy, Is.False);
            Assert.That(healthResult.Message, Is.EqualTo(message));
            Assert.That(healthResult.Details, Is.Not.Null);
            Assert.That(healthResult.Details.Count, Is.EqualTo(0));
            Assert.That(healthResult.Details, Is.SameAs(emptyDetails));
        }

        #endregion
    }
}
