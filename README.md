# ClipboardPlus (Version WPF)

ClipboardPlus est un gestionnaire de presse-papiers avancé pour Windows, conçu pour remplacer le presse-papiers standard de Windows en offrant des fonctionnalités améliorées pour la gestion, l'organisation et la réutilisation du contenu copié.

## Fonctionnalités principales

- **Historique étendu** : Stocke un nombre configurable d'éléments (texte, images, texte enrichi, HTML) copiés dans le presse-papiers.
- **Interface personnalisable** : Une fenêtre pop-up moderne basée sur XAML pour afficher et interagir avec l'historique du presse-papiers.
- **Réorganisation par glisser-déposer** : Permet aux utilisateurs de réorganiser les éléments du presse-papiers.
- **Épinglage d'éléments** : Permet de marquer des éléments importants pour les conserver en haut de la liste et éviter leur suppression automatique.
- **Nommage personnalisé** : Permet d'attribuer des noms descriptifs aux éléments du presse-papiers.
- **Aperçu du contenu** : Fournit un aperçu clair du contenu des éléments (texte, images, texte enrichi).
- **Personnalisation des thèmes** : Offre un contrôle complet sur l'apparence de l'application via des fichiers ResourceDictionary XAML.
- **Intégration à la barre d'état système** : Fonctionne dans la barre d'état système avec un menu contextuel, activable par le raccourci global Win+V.
- **Persistance des données** : Sauvegarde l'historique du presse-papiers, les métadonnées des éléments et les préférences utilisateur localement dans une base de données SQLite.
- **Logs dans la console** : Possibilité d'afficher les logs en temps réel dans une console pour faciliter le débogage et le diagnostic des problèmes.

## Technologies utilisées

- **C#** (dernière version stable)
- **WPF** (Windows Presentation Foundation) sur **.NET 8**
- **SQLite** via Microsoft.Data.Sqlite
- **CommunityToolkit.Mvvm** pour l'implémentation du modèle MVVM
- **GongSolutions.WPF.DragDrop** pour le glisser-déposer

## Plateformes cibles

- Windows 10 (version 1809 ou ultérieure)
- Windows 11

## Instructions de compilation

*À venir*

## Exécution de l'application

### Exécution en mode développement

Pour exécuter l'application en mode développement avec affichage des logs dans la console :

```
dotnet run --project src/ClipboardPlus/ClipboardPlus.csproj -- --console
```

Ou utilisez les scripts fournis :
- `run_dotnet.bat` (Windows Batch)
- `run_dotnet.ps1` (PowerShell)

### Exécution de l'application compilée

Pour exécuter l'application compilée avec affichage des logs dans la console :

```
ClipboardPlus.exe --console
```

Ou utilisez les scripts fournis :
- `run_with_console.bat` (Windows Batch)
- `run_with_console.ps1` (PowerShell)

Pour plus d'informations sur l'affichage des logs dans la console, consultez [README_CONSOLE_LOGS.md](README_CONSOLE_LOGS.md).

## Contribution

*À venir*

## Licence

Ce projet est sous licence MIT - voir le fichier LICENSE pour plus de détails. 