#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using ClipboardPlus.UI.ViewModels;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.History
{
    /// <summary>
    /// Tests unitaires pour HistorySynchronizationService.
    /// </summary>
    [TestFixture]
    public class HistorySynchronizationServiceTests
    {
        private Mock<ILoggingService>? _mockLoggingService;
        private HistorySynchronizationService? _synchronizationService;
        private HistorySynchronizationContext? _validContext;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _synchronizationService = new HistorySynchronizationService(_mockLoggingService.Object);

            // Créer un contexte valide par défaut pour les tests de comparaison
            _validContext = new HistorySynchronizationContext
            {
                EventId = "test-event-123",
                UIItems = CreateTestItems(3),
                ManagerItems = CreateTestItems(3),
                Synchronizer = null // Pas de synchronizer pour éviter les complications
            };
        }

        private List<ClipboardItem> CreateTestItems(int count)
        {
            var items = new List<ClipboardItem>();
            for (int i = 0; i < count; i++)
            {
                items.Add(new ClipboardItem
                {
                    Id = i + 1, // long ID
                    TextPreview = $"Test content {i}",
                    Timestamp = DateTime.Now.AddMinutes(-i)
                });
            }
            return items;
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new HistorySynchronizationService(null!));
        }

        [Test]
        public void Constructor_WithValidLoggingService_ShouldCreateInstance()
        {
            // Act
            var service = new HistorySynchronizationService(_mockLoggingService!.Object);

            // Assert
            Assert.That(service, Is.Not.Null);
        }

        [Test]
        public async Task SynchronizeIfNeededAsync_WithNullContext_ShouldReturnError()
        {
            // Act
            var result = await _synchronizationService!.SynchronizeIfNeededAsync(null);

            // Assert
            Assert.That(result.Type, Is.EqualTo(SynchronizationResultType.Error));
            Assert.That(result.Message, Is.EqualTo("Contexte de synchronisation null"));
        }

        [Test]
        public async Task SynchronizeIfNeededAsync_WithNullSynchronizer_ShouldReturnError()
        {
            // Arrange
            _validContext!.Synchronizer = null;

            // Act
            var result = await _synchronizationService!.SynchronizeIfNeededAsync(_validContext);

            // Assert
            Assert.That(result.Type, Is.EqualTo(SynchronizationResultType.Error));
            Assert.That(result.Message, Is.EqualTo("Synchronizer null"));
        }

        [Test]
        public void CompareCollections_WithNullContext_ShouldReturnNotSynchronized()
        {
            // Act
            var result = _synchronizationService!.CompareCollections(null);

            // Assert
            Assert.That(result.AreSynchronized, Is.False);
            Assert.That(result.UICount, Is.EqualTo(0));
            Assert.That(result.ManagerCount, Is.EqualTo(0));
            _mockLoggingService!.Verify(x => x.LogWarning(It.Is<string>(s => 
                s.Contains("Collections null lors de la comparaison"))), Times.Once);
        }

        [Test]
        public void CompareCollections_WithNullUIItems_ShouldReturnNotSynchronized()
        {
            // Arrange
            _validContext!.UIItems = null;

            // Act
            var result = _synchronizationService!.CompareCollections(_validContext);

            // Assert
            Assert.That(result.AreSynchronized, Is.False);
            _mockLoggingService!.Verify(x => x.LogWarning(It.Is<string>(s => 
                s.Contains("Collections null lors de la comparaison"))), Times.Once);
        }

        [Test]
        public void CompareCollections_WithNullManagerItems_ShouldReturnNotSynchronized()
        {
            // Arrange
            _validContext!.ManagerItems = null;

            // Act
            var result = _synchronizationService!.CompareCollections(_validContext);

            // Assert
            Assert.That(result.AreSynchronized, Is.False);
            _mockLoggingService!.Verify(x => x.LogWarning(It.Is<string>(s => 
                s.Contains("Collections null lors de la comparaison"))), Times.Once);
        }

        [Test]
        public void CompareCollections_WithSameCountAndIds_ShouldReturnSynchronized()
        {
            // Arrange - Même éléments dans les deux collections
            var items = CreateTestItems(3);
            _validContext!.UIItems = items;
            _validContext.ManagerItems = items.ToList(); // Même contenu

            // Act
            var result = _synchronizationService!.CompareCollections(_validContext);

            // Assert
            Assert.That(result.AreSynchronized, Is.True);
            Assert.That(result.UICount, Is.EqualTo(3));
            Assert.That(result.ManagerCount, Is.EqualTo(3));
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("Synchronisées=True"))), Times.Once);
        }

        [Test]
        public void CompareCollections_WithDifferentCounts_ShouldReturnNotSynchronized()
        {
            // Arrange
            _validContext!.UIItems = CreateTestItems(3);
            _validContext.ManagerItems = CreateTestItems(5);

            // Act
            var result = _synchronizationService!.CompareCollections(_validContext);

            // Assert
            Assert.That(result.AreSynchronized, Is.False);
            Assert.That(result.UICount, Is.EqualTo(3));
            Assert.That(result.ManagerCount, Is.EqualTo(5));
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("Synchronisées=False"))), Times.Once);
        }

        [Test]
        public void CompareCollections_WithSameCountButDifferentIds_ShouldReturnNotSynchronized()
        {
            // Arrange - Créer des collections avec des IDs explicitement différents
            var uiItems = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, TextPreview = "Item 1" },
                new ClipboardItem { Id = 2, TextPreview = "Item 2" },
                new ClipboardItem { Id = 3, TextPreview = "Item 3" }
            };

            var managerItems = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 4, TextPreview = "Item 4" },
                new ClipboardItem { Id = 5, TextPreview = "Item 5" },
                new ClipboardItem { Id = 6, TextPreview = "Item 6" }
            };

            _validContext!.UIItems = uiItems;
            _validContext.ManagerItems = managerItems;

            // Act
            var result = _synchronizationService!.CompareCollections(_validContext);

            // Assert
            Assert.That(result.AreSynchronized, Is.False);
            Assert.That(result.UICount, Is.EqualTo(3));
            Assert.That(result.ManagerCount, Is.EqualTo(3));
        }

        [Test]
        public void CompareCollections_WithEmptyCollections_ShouldReturnSynchronized()
        {
            // Arrange
            _validContext!.UIItems = new List<ClipboardItem>();
            _validContext.ManagerItems = new List<ClipboardItem>();

            // Act
            var result = _synchronizationService!.CompareCollections(_validContext);

            // Assert
            Assert.That(result.AreSynchronized, Is.True);
            Assert.That(result.UICount, Is.EqualTo(0));
            Assert.That(result.ManagerCount, Is.EqualTo(0));
        }

        [Test]
        public void CompareCollections_ShouldLogComparisonDetails()
        {
            // Arrange
            _validContext!.UIItems = CreateTestItems(3);
            _validContext.ManagerItems = CreateTestItems(5);

            // Act
            _synchronizationService!.CompareCollections(_validContext);

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s =>
                s.Contains("Comparaison: UI=3, Manager=5"))), Times.Once);
        }
    }
}
