using System;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using NUnit.Framework;
using ClipboardPlus.UI.Native;
using System.Linq;

namespace ClipboardPlus.Tests.Unit.UI.Native
{
    /// <summary>
    /// Tests fonctionnels pour User32
    /// Ces tests vérifient la structure, les constantes et les signatures des méthodes natives
    /// </summary>
    [TestFixture]
    public class User32Tests
    {
        [Test]
        public void User32_IsStaticClass()
        {
            // Arrange & Act
            var type = typeof(User32);
            
            // Assert
            Assert.IsTrue(type.IsClass, "User32 devrait être une classe");
            Assert.IsTrue(type.IsSealed, "User32 devrait être sealed");
            Assert.IsTrue(type.IsAbstract, "User32 devrait être abstract (classe statique)");
        }

        [Test]
        public void User32_IsPublicClass()
        {
            // Arrange & Act
            var type = typeof(User32);
            
            // Assert
            Assert.IsTrue(type.IsPublic, "User32 devrait être publique");
        }

        [Test]
        public void User32_NamespaceIsCorrect()
        {
            // Arrange & Act
            var type = typeof(User32);
            
            // Assert
            Assert.AreEqual("ClipboardPlus.UI.Native", type.Namespace, 
                "User32 devrait être dans le namespace ClipboardPlus.UI.Native");
        }

        [Test]
        public void GetForegroundWindow_MethodExists()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("GetForegroundWindow", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode GetForegroundWindow devrait exister");
            Assert.IsTrue(method!.IsStatic, "GetForegroundWindow devrait être statique");
            Assert.IsTrue(method.IsPublic, "GetForegroundWindow devrait être publique");
            Assert.AreEqual(typeof(IntPtr), method.ReturnType, "GetForegroundWindow devrait retourner IntPtr");
            Assert.AreEqual(0, method.GetParameters().Length, "GetForegroundWindow ne devrait avoir aucun paramètre");
        }

        [Test]
        public void GetForegroundWindow_HasDllImportAttribute()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("GetForegroundWindow");
            
            // Assert
            Assert.IsNotNull(method, "La méthode GetForegroundWindow devrait exister");
            
            var dllImportAttr = method!.GetCustomAttribute<DllImportAttribute>();
            Assert.IsNotNull(dllImportAttr, "GetForegroundWindow devrait avoir l'attribut DllImport");
            Assert.AreEqual("user32.dll", dllImportAttr!.Value, "GetForegroundWindow devrait être importée de user32.dll");
        }

        [Test]
        public void IsWindowEnabled_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("IsWindowEnabled", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode IsWindowEnabled devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(1, parameters.Length, "IsWindowEnabled devrait avoir 1 paramètre");
            Assert.AreEqual(typeof(IntPtr), parameters[0].ParameterType, "Le paramètre devrait être IntPtr (hWnd)");
            Assert.AreEqual(typeof(bool), method.ReturnType, "IsWindowEnabled devrait retourner bool");
        }

        [Test]
        public void SetForegroundWindow_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("SetForegroundWindow", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode SetForegroundWindow devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(1, parameters.Length, "SetForegroundWindow devrait avoir 1 paramètre");
            Assert.AreEqual(typeof(IntPtr), parameters[0].ParameterType, "Le paramètre devrait être IntPtr (hWnd)");
            Assert.AreEqual(typeof(bool), method.ReturnType, "SetForegroundWindow devrait retourner bool");
        }

        [Test]
        public void GetWindowText_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("GetWindowText", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode GetWindowText devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(3, parameters.Length, "GetWindowText devrait avoir 3 paramètres");
            Assert.AreEqual(typeof(IntPtr), parameters[0].ParameterType, "Le premier paramètre devrait être IntPtr (hWnd)");
            Assert.AreEqual(typeof(StringBuilder), parameters[1].ParameterType, "Le deuxième paramètre devrait être StringBuilder");
            Assert.AreEqual(typeof(int), parameters[2].ParameterType, "Le troisième paramètre devrait être int (nMaxCount)");
            Assert.AreEqual(typeof(int), method.ReturnType, "GetWindowText devrait retourner int");
        }

        [Test]
        public void GetWindowTextLength_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("GetWindowTextLength", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode GetWindowTextLength devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(1, parameters.Length, "GetWindowTextLength devrait avoir 1 paramètre");
            Assert.AreEqual(typeof(IntPtr), parameters[0].ParameterType, "Le paramètre devrait être IntPtr (hWnd)");
            Assert.AreEqual(typeof(int), method.ReturnType, "GetWindowTextLength devrait retourner int");
        }

        [Test]
        public void IsWindowVisible_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("IsWindowVisible", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode IsWindowVisible devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(1, parameters.Length, "IsWindowVisible devrait avoir 1 paramètre");
            Assert.AreEqual(typeof(IntPtr), parameters[0].ParameterType, "Le paramètre devrait être IntPtr (hWnd)");
            Assert.AreEqual(typeof(bool), method.ReturnType, "IsWindowVisible devrait retourner bool");
        }

        [Test]
        public void GetWindowThreadProcessId_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("GetWindowThreadProcessId", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode GetWindowThreadProcessId devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(2, parameters.Length, "GetWindowThreadProcessId devrait avoir 2 paramètres");
            Assert.AreEqual(typeof(IntPtr), parameters[0].ParameterType, "Le premier paramètre devrait être IntPtr (hWnd)");
            Assert.AreEqual(typeof(uint).MakeByRefType(), parameters[1].ParameterType, "Le deuxième paramètre devrait être out uint");
            Assert.AreEqual(typeof(uint), method.ReturnType, "GetWindowThreadProcessId devrait retourner uint");
        }

        [Test]
        public void SendMessage_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("SendMessage", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode SendMessage devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(4, parameters.Length, "SendMessage devrait avoir 4 paramètres");
            Assert.AreEqual(typeof(IntPtr), parameters[0].ParameterType, "Le premier paramètre devrait être IntPtr (hWnd)");
            Assert.AreEqual(typeof(uint), parameters[1].ParameterType, "Le deuxième paramètre devrait être uint (Msg)");
            Assert.AreEqual(typeof(IntPtr), parameters[2].ParameterType, "Le troisième paramètre devrait être IntPtr (wParam)");
            Assert.AreEqual(typeof(IntPtr), parameters[3].ParameterType, "Le quatrième paramètre devrait être IntPtr (lParam)");
            Assert.AreEqual(typeof(IntPtr), method.ReturnType, "SendMessage devrait retourner IntPtr");
        }

        [Test]
        public void PostMessage_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("PostMessage", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode PostMessage devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(4, parameters.Length, "PostMessage devrait avoir 4 paramètres");
            Assert.AreEqual(typeof(IntPtr), parameters[0].ParameterType, "Le premier paramètre devrait être IntPtr (hWnd)");
            Assert.AreEqual(typeof(uint), parameters[1].ParameterType, "Le deuxième paramètre devrait être uint (Msg)");
            Assert.AreEqual(typeof(IntPtr), parameters[2].ParameterType, "Le troisième paramètre devrait être IntPtr (wParam)");
            Assert.AreEqual(typeof(IntPtr), parameters[3].ParameterType, "Le quatrième paramètre devrait être IntPtr (lParam)");
            Assert.AreEqual(typeof(bool), method.ReturnType, "PostMessage devrait retourner bool");
        }

        [Test]
        public void GetKeyState_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("GetKeyState", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode GetKeyState devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(1, parameters.Length, "GetKeyState devrait avoir 1 paramètre");
            Assert.AreEqual(typeof(int), parameters[0].ParameterType, "Le paramètre devrait être int (nVirtKey)");
            Assert.AreEqual(typeof(short), method.ReturnType, "GetKeyState devrait retourner short");
        }

        [Test]
        public void AttachThreadInput_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("AttachThreadInput", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode AttachThreadInput devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(3, parameters.Length, "AttachThreadInput devrait avoir 3 paramètres");
            Assert.AreEqual(typeof(uint), parameters[0].ParameterType, "Le premier paramètre devrait être uint (idAttach)");
            Assert.AreEqual(typeof(uint), parameters[1].ParameterType, "Le deuxième paramètre devrait être uint (idAttachTo)");
            Assert.AreEqual(typeof(bool), parameters[2].ParameterType, "Le troisième paramètre devrait être bool (fAttach)");
            Assert.AreEqual(typeof(bool), method.ReturnType, "AttachThreadInput devrait retourner bool");
        }

        [Test]
        public void GetCurrentThreadId_HasCorrectSignature()
        {
            // Arrange & Act
            var type = typeof(User32);
            var method = type.GetMethod("GetCurrentThreadId", BindingFlags.Public | BindingFlags.Static);
            
            // Assert
            Assert.IsNotNull(method, "La méthode GetCurrentThreadId devrait exister");
            
            var parameters = method!.GetParameters();
            Assert.AreEqual(0, parameters.Length, "GetCurrentThreadId ne devrait avoir aucun paramètre");
            Assert.AreEqual(typeof(uint), method.ReturnType, "GetCurrentThreadId devrait retourner uint");
        }

        [Test]
        public void VirtualKeyConstants_HaveCorrectValues()
        {
            // Arrange & Act & Assert
            Assert.AreEqual(0x11, User32.VK_CONTROL, "VK_CONTROL devrait avoir la valeur 0x11");
            Assert.AreEqual(0x10, User32.VK_SHIFT, "VK_SHIFT devrait avoir la valeur 0x10");
            Assert.AreEqual(0x12, User32.VK_MENU, "VK_MENU devrait avoir la valeur 0x12");
            Assert.AreEqual(0x5B, User32.VK_LWIN, "VK_LWIN devrait avoir la valeur 0x5B");
            Assert.AreEqual(0x5C, User32.VK_RWIN, "VK_RWIN devrait avoir la valeur 0x5C");
        }

        [Test]
        public void WindowsMessageConstants_HaveCorrectValues()
        {
            // Arrange & Act & Assert
            Assert.AreEqual(0x0100u, User32.WM_KEYDOWN, "WM_KEYDOWN devrait avoir la valeur 0x0100");
            Assert.AreEqual(0x0101u, User32.WM_KEYUP, "WM_KEYUP devrait avoir la valeur 0x0101");
            Assert.AreEqual(0x0102u, User32.WM_CHAR, "WM_CHAR devrait avoir la valeur 0x0102");
            Assert.AreEqual(0x0302u, User32.WM_PASTE, "WM_PASTE devrait avoir la valeur 0x0302");
        }

        [Test]
        public void Constants_AreReadOnly()
        {
            // Arrange
            var type = typeof(User32);
            var fields = type.GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy);

            foreach (var field in fields)
            {
                // Assert
                Assert.IsTrue(field.IsLiteral || field.IsInitOnly, 
                    $"{field.Name} devrait être une constante (literal ou init-only)");
            }
        }

        [Test]
        public void AllMethods_HaveDllImportAttribute()
        {
            var methods = typeof(User32).GetMethods(BindingFlags.Public | BindingFlags.Static);
            foreach (var method in methods)
            {
                var dllImportAttr = method.GetCustomAttribute<DllImportAttribute>();
                Assert.IsNotNull(dllImportAttr, $"La méthode {method.Name} devrait avoir l'attribut DllImport");

                if (method.Name == "GetCurrentThreadId")
                {
                    Assert.AreEqual("kernel32.dll", dllImportAttr!.Value, $"La méthode {method.Name} devrait être importée de kernel32.dll");
                }
                else
                {
                    Assert.AreEqual("user32.dll", dllImportAttr!.Value, $"La méthode {method.Name} devrait être importée de user32.dll");
                }
            }
        }

        [Test]
        public void User32_HasExpectedNumberOfMethods()
        {
            // Arrange & Act
            var type = typeof(User32);
            var methods = type.GetMethods(BindingFlags.Public | BindingFlags.Static | BindingFlags.DeclaredOnly);
            
            // Assert
            Assert.AreEqual(12, methods.Length, "User32 devrait avoir exactement 12 méthodes publiques");
        }

        [Test]
        public void User32_HasExpectedNumberOfConstants()
        {
            var constants = typeof(User32).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)
                .Where(fi => fi.IsLiteral && !fi.IsInitOnly).ToList();

            Assert.That(constants.Count, Is.GreaterThanOrEqualTo(9), "Il devrait y avoir au moins 9 constantes publiques dans User32.");
        }
    }
}
