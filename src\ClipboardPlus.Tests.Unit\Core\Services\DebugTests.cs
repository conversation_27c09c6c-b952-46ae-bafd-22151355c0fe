﻿using System;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.Dialogs;
using ClipboardPlus.Tests.Unit.Helpers;
using ClipboardPlus.Tests.Unit.TestHelpers;
using ClipboardPlus.UI.ViewModels.Construction;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.Windows.Threading;
using System.Linq;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests de débogage pour les fonctionnalités critiques
    /// Ces tests sont destinés à vérifier que les fonctionnalités de base ne plantent pas
    /// </summary>
    [TestFixture]
    public class DebugTests
    {
        private Mock<IClipboardHistoryManager> _mockHistoryManager = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IUserThemeManager> _mockThemeManager = null!;
        private Mock<IGlobalShortcutService> _mockShortcutService = null!;
        private Mock<IUserNotificationService> _mockUserNotificationService = null!;
        private Mock<ClipboardPlus.Core.Services.IUserInteractionService> _mockUserInteractionService = null!;

        [SetUp]
        public void Initialize()
        {
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockThemeManager = new Mock<IUserThemeManager>();
            _mockShortcutService = new Mock<IGlobalShortcutService>();
            _mockUserNotificationService = new Mock<IUserNotificationService>();
            _mockUserInteractionService = new Mock<ClipboardPlus.Core.Services.IUserInteractionService>();

            _mockHistoryManager.Setup(m => m.HistoryItems).Returns(new List<ClipboardItem>());
            // AddItemAsync ne sera PAS appelé par le ViewModel en mode test pour la création, donc pas de setup global nécessaire ici.

            // Configuration des paramètres d'application
            _mockSettingsManager.Setup(s => s.MaxTextPreviewLength).Returns(100); // Pour Truncate dans le code source
            _mockSettingsManager.Setup(s => s.LoadSettingsAsync()).Returns(Task.CompletedTask);

            _mockThemeManager.Setup(t => t.GetAvailableThemes()).Returns(new List<ThemeInfo>());
            _mockShortcutService.Setup(s => s.GetCurrentRegisteredShortcut()).Returns(new KeyCombination(System.Windows.Input.ModifierKeys.None, System.Windows.Input.Key.None));
        }

        [Test]
        [Timeout(10000)] // 10 secondes maximum
        public void Debug_NewItemCreation_InTestMode_ShouldNotCrash_And_AddsItemToLocalCollection()
        {
            try
            {
                var mockClipboardInteractionService = new Mock<IClipboardInteractionService>();
                // Utiliser le _mockSettingsManager initialisé dans SetUp

                // Créer un ViewModel avec l'approche legacy qui fonctionne
                var mockHistoryManager = new Mock<IClipboardHistoryManager>();
                var mockClipboardService = new Mock<IClipboardInteractionService>();
                var mockSettingsManager = new Mock<ISettingsManager>();
                var mockNotificationService = new Mock<IUserNotificationService>();
                var mockUserInteractionService = new Mock<IUserInteractionService>();

                var serviceProvider = TestServiceProviderHelper.CreateMockServiceProvider();

                // Configurer les mocks de base
                mockHistoryManager.Setup(m => m.HistoryItems).Returns(new List<ClipboardItem>());
                mockSettingsManager.Setup(m => m.MaxHistoryItems).Returns(50);

                var viewModel = ClipboardHistoryViewModelFactory.CreateWithSOLIDArchitecture(
                    mockHistoryManager.Object,
                    mockClipboardService.Object,
                    mockSettingsManager.Object,
                    mockNotificationService.Object,
                    mockUserInteractionService.Object,
                    serviceProvider
                );

                // Ne pas utiliser Task.Delay().Wait() qui peut causer des deadlocks
                // Le constructeur devrait être synchrone pour les tests

                // Simuler l'activation du mode création (comme le ferait PrepareNewItemCommand en mode test)
                viewModel.IsItemCreationActive = true;
                viewModel.NewItemTextContent = "Élément de test"; // Contenu par défaut en mode test

                string contentForNewItem = "Texte de test spécifique pour Debug";
                viewModel.NewItemTextContent = contentForNewItem; // L'utilisateur modifie le contenu

                int initialItemCount = viewModel.HistoryItems.Count;

                // Act : Exécuter la commande publique pour finaliser et sauvegarder
                viewModel.FinalizeAndSaveNewItemCommand.Execute(null);

                // Assert : Vérifier que l'état a été correctement réinitialisé après la sauvegarde
                Assert.That(viewModel.NewItemTextContent, Is.EqualTo(string.Empty), "NewItemTextContent devrait être réinitialisé après sauvegarde.");
                Assert.That(viewModel.IsItemCreationActive, Is.False, "IsItemCreationActive devrait être false après sauvegarde.");

                // Vérifier que AddItemAsync N'A PAS été appelé sur le mock du manager
                _mockHistoryManager.Verify(m => m.AddItemAsync(It.IsAny<ClipboardItem>()), Times.Never,
                    "AddItemAsync sur IClipboardHistoryManager ne devrait pas être appelé en mode test pour la création.");

                // Vérifier que l'élément a été ajouté directement à la collection HistoryItems du ViewModel
                Assert.That(viewModel.HistoryItems.Count, Is.EqualTo(initialItemCount + 1), "Un élément aurait dû être ajouté à la collection locale HistoryItems.");
                var addedItem = viewModel.HistoryItems.FirstOrDefault(i => i.TextPreview != null && i.TextPreview.StartsWith(contentForNewItem.Substring(0, Math.Min(contentForNewItem.Length, 50))));
                Assert.That(addedItem, Is.Not.Null, "L'élément ajouté n'a pas été trouvé dans viewModel.HistoryItems ou son TextPreview est incorrect.");
                Assert.That(System.Text.Encoding.UTF8.GetString(addedItem!.RawData ?? Array.Empty<byte>()), Is.EqualTo(contentForNewItem), "Le RawData de l'élément ajouté ne correspond pas.");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Le test Debug_NewItemCreation ne devrait pas échouer. Exception: {ex.GetType().Name}: {ex.Message}\nStackTrace: {ex.StackTrace}");
            }
        }
    }
}
