using System;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Interface pour la validation du thread UI.
    /// Responsabilité unique : vérifier si l'exécution se fait dans le thread UI approprié.
    /// </summary>
    public interface IThreadValidator
    {
        /// <summary>
        /// Vérifie si le code s'exécute actuellement dans le thread UI.
        /// </summary>
        /// <returns>True si dans le thread UI, false sinon.</returns>
        bool IsInUIThread();

        /// <summary>
        /// Valide que l'exécution se fait dans le thread UI et logue un avertissement si ce n'est pas le cas.
        /// </summary>
        void ValidateUIThread();
    }
}
