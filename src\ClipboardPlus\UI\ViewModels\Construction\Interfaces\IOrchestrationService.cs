using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Diagnostics;
using ClipboardPlus.UI.Services;

namespace ClipboardPlus.UI.ViewModels.Construction.Interfaces
{
    /// <summary>
    /// Service d'orchestration des composants complexes du ViewModel.
    /// Responsabilité : Création et configuration des orchestrateurs complexes selon le principe SRP.
    /// </summary>
    public interface IOrchestrationService
    {
        /// <summary>
        /// Crée et configure le HistoryCollectionSynchronizer.
        /// Cette méthode extrait la logique complexe de création du synchronizer
        /// qui était précédemment dans le constructeur (lignes 270-277).
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel</param>
        /// <param name="historyManager">Gestionnaire de l'historique</param>
        /// <param name="loggingService">Service de logging (peut être null)</param>
        /// <param name="deletionDiagnostic">Service de diagnostic de suppression (peut être null)</param>
        /// <returns>Instance configurée du HistoryCollectionSynchronizer</returns>
        HistoryCollectionSynchronizer CreateHistoryCollectionSynchronizer(
            ClipboardHistoryViewModel viewModel,
            IClipboardHistoryManager historyManager,
            ILoggingService? loggingService,
            IDeletionDiagnostic? deletionDiagnostic);

        /// <summary>
        /// Initialise les services optionnels.
        /// Cette méthode extrait l'appel à InitializeMigrationServices()
        /// qui était précédemment dans le constructeur (lignes 284-290).
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel à configurer</param>
        void InitializeMigrationServices(ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Initialise l'orchestrateur de changements d'historique.
        /// Cette méthode extrait l'initialisation de l'orchestrateur de changements
        /// qui était précédemment dans le constructeur.
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel à configurer</param>
        void InitializeHistoryChangeOrchestrator(ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Initialise le service de santé des collections.
        /// Cette méthode extrait l'initialisation du service de santé
        /// qui était précédemment dans le constructeur.
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel à configurer</param>
        void InitializeCollectionHealthService(ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Déclenche le chargement asynchrone de l'historique.
        /// Cette méthode extrait l'appel à LoadHistoryAsync()
        /// qui était précédemment dans le constructeur (ligne 295).
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel</param>
        /// <returns>Task représentant l'opération asynchrone</returns>
        Task TriggerHistoryLoadAsync(ClipboardHistoryViewModel viewModel);
    }
}
