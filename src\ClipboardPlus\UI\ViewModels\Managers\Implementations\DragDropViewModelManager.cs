// ============================================================================
// DRAG DROP VIEWMODEL MANAGER IMPLEMENTATION - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Implémentation concrète de la gestion du drag & drop
// 📋 RESPONSABILITÉ : IDropTarget et opérations de glisser-déposer
// 🏗️ ARCHITECTURE : Extraction de DragDrop.cs (159 lignes)
//
// ============================================================================

using System;
using System.Linq;
using System.Windows;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels.Managers.Interfaces;
using CommunityToolkit.Mvvm.ComponentModel;
using WpfDataObject = System.Windows.IDataObject;
using WpfDragDropEffects = System.Windows.DragDropEffects;

namespace ClipboardPlus.UI.ViewModels.Managers.Implementations
{
    /// <summary>
    /// Implémentation concrète du manager de gestion du drag & drop.
    /// 
    /// Cette classe implémente IDropTarget et gère toutes les opérations
    /// de glisser-déposer pour l'interface utilisateur.
    /// </summary>
    public class DragDropViewModelManager : ObservableObject, IDragDropViewModelManager
    {
        #region Champs Privés

        private bool _isDragDropActive;
        private ClipboardDataType? _currentDragDataType;
        private bool _isDropAllowed;
        private bool _isDisposed;

        #endregion

        #region Propriétés d'État (IDragDropViewModelManager)

        /// <summary>
        /// Indique si une opération de drag & drop est en cours.
        /// </summary>
        public bool IsDragDropActive
        {
            get => _isDragDropActive;
            private set => SetProperty(ref _isDragDropActive, value);
        }

        /// <summary>
        /// Type de données actuellement en cours de drag & drop.
        /// </summary>
        public ClipboardDataType? CurrentDragDataType
        {
            get => _currentDragDataType;
            private set => SetProperty(ref _currentDragDataType, value);
        }

        /// <summary>
        /// Indique si le drop est autorisé à la position actuelle.
        /// </summary>
        public bool IsDropAllowed
        {
            get => _isDropAllowed;
            private set => SetProperty(ref _isDropAllowed, value);
        }

        #endregion

        #region Événements (IDragDropViewModelManager)

        /// <summary>
        /// Événement déclenché lorsqu'une opération de drag commence.
        /// </summary>
        public event EventHandler<DragStartedEventArgs>? DragStarted;

        /// <summary>
        /// Événement déclenché lorsqu'une opération de drop est effectuée.
        /// </summary>
        public event EventHandler<DropCompletedEventArgs>? DropCompleted;

        /// <summary>
        /// Événement déclenché lorsqu'une opération de drag & drop échoue.
        /// </summary>
        public event EventHandler<DragDropErrorEventArgs>? DragDropError;

        #endregion

        #region Méthodes IDropTarget (IDragDropViewModelManager)

        /// <summary>
        /// Appelée lorsque des données sont glissées au-dessus de la zone de drop.
        /// </summary>
        /// <param name="dropInfo">Informations sur l'opération de drop</param>
        public void DragOver(IDropInfo dropInfo)
        {
            if (_isDisposed || dropInfo?.Data == null) return;

            try
            {
                // Déterminer le type de données
                var dataType = DetermineDataType(dropInfo.Data);
                CurrentDragDataType = dataType;

                // Vérifier si les données peuvent être acceptées
                var canAccept = CanAcceptData(dropInfo.Data);
                var isDropAllowedAtPosition = IsDropAllowedAt(dropInfo.InsertIndex, dataType);

                IsDropAllowed = canAccept && isDropAllowedAtPosition;
                IsDragDropActive = true;

                // Définir l'effet de drop
                dropInfo.Effects = IsDropAllowed ? WpfDragDropEffects.Copy : WpfDragDropEffects.None;

                // Déclencher l'événement de début de drag si ce n'est pas déjà fait
                if (!_isDragDropActive)
                {
                    DragStarted?.Invoke(this, new DragStartedEventArgs(dataType, dropInfo.Data));
                }
            }
            catch (Exception ex)
            {
                DragDropError?.Invoke(this, new DragDropErrorEventArgs(
                    "DragOver", "Erreur lors du survol de drag & drop", ex));
                
                dropInfo.Effects = WpfDragDropEffects.None;
                IsDropAllowed = false;
            }
        }

        /// <summary>
        /// Appelée lorsque des données sont déposées dans la zone de drop.
        /// </summary>
        /// <param name="dropInfo">Informations sur l'opération de drop</param>
        public void Drop(IDropInfo dropInfo)
        {
            if (_isDisposed || dropInfo?.Data == null || !IsDropAllowed) return;

            try
            {
                // Déterminer le type de données
                var dataType = DetermineDataType(dropInfo.Data);

                // Créer un ClipboardItem à partir des données déposées
                var createdItem = CreateItemFromDroppedData(dropInfo.Data, dataType);

                if (createdItem != null)
                {
                    // Ajouter l'élément à l'historique
                    bool success;
                    if (dropInfo.InsertIndex >= 0)
                    {
                        success = InsertDroppedItemAt(createdItem, dropInfo.InsertIndex);
                    }
                    else
                    {
                        success = AddDroppedItemToHistory(createdItem);
                    }

                    if (success)
                    {
                        // Déclencher l'événement de drop réussi
                        DropCompleted?.Invoke(this, new DropCompletedEventArgs(
                            createdItem, dropInfo.InsertIndex, dataType));
                    }
                    else
                    {
                        DragDropError?.Invoke(this, new DragDropErrorEventArgs(
                            "Drop", "Échec de l'ajout de l'élément à l'historique", null));
                    }
                }
                else
                {
                    DragDropError?.Invoke(this, new DragDropErrorEventArgs(
                        "Drop", "Impossible de créer un élément à partir des données déposées", null));
                }
            }
            catch (Exception ex)
            {
                DragDropError?.Invoke(this, new DragDropErrorEventArgs(
                    "Drop", "Erreur lors du drop", ex));
            }
            finally
            {
                // Nettoyer l'état de drag & drop
                IsDragDropActive = false;
                CurrentDragDataType = null;
                IsDropAllowed = false;
            }
        }

        /// <summary>
        /// Appelée lorsque le drag quitte la zone de drop.
        /// </summary>
        /// <param name="dropInfo">Informations sur l'opération de drop</param>
        public void DragLeave(IDropInfo dropInfo)
        {
            if (_isDisposed) return;

            // Nettoyer l'état de drag & drop
            IsDragDropActive = false;
            CurrentDragDataType = null;
            IsDropAllowed = false;
        }

        #endregion

        #region Méthodes de Validation (IDragDropViewModelManager)

        /// <summary>
        /// Valide si les données peuvent être acceptées.
        /// </summary>
        /// <param name="dataObject">Objet de données à valider</param>
        /// <returns>True si les données peuvent être acceptées</returns>
        public bool CanAcceptData(System.Windows.IDataObject dataObject)
        {
            if (dataObject == null) return false;

            // Vérifier les formats de données supportés
            return dataObject.GetDataPresent(System.Windows.DataFormats.Text) ||
                   dataObject.GetDataPresent(System.Windows.DataFormats.UnicodeText) ||
                   dataObject.GetDataPresent(System.Windows.DataFormats.Bitmap) ||
                   dataObject.GetDataPresent(System.Windows.DataFormats.FileDrop) ||
                   dataObject.GetDataPresent(System.Windows.DataFormats.Html) ||
                   dataObject.GetDataPresent(System.Windows.DataFormats.Rtf);
        }

        /// <summary>
        /// Détermine le type de données contenues dans l'objet de données.
        /// </summary>
        /// <param name="dataObject">Objet de données à analyser</param>
        /// <returns>Type de données détecté</returns>
        public ClipboardDataType DetermineDataType(System.Windows.IDataObject dataObject)
        {
            if (dataObject == null) return ClipboardDataType.Text;

            // Priorité : Fichiers > Images > Texte
            if (dataObject.GetDataPresent(System.Windows.DataFormats.FileDrop))
            {
                return ClipboardDataType.FilePath;
            }

            if (dataObject.GetDataPresent(System.Windows.DataFormats.Bitmap))
            {
                return ClipboardDataType.Image;
            }

            if (dataObject.GetDataPresent(System.Windows.DataFormats.Html))
            {
                return ClipboardDataType.Html;
            }

            if (dataObject.GetDataPresent(System.Windows.DataFormats.Rtf))
            {
                return ClipboardDataType.Rtf;
            }

            // Par défaut, considérer comme du texte
            return ClipboardDataType.Text;
        }

        /// <summary>
        /// Valide si une opération de drop est autorisée à la position donnée.
        /// </summary>
        /// <param name="targetIndex">Index de destination</param>
        /// <param name="dataType">Type de données</param>
        /// <returns>True si le drop est autorisé</returns>
        public bool IsDropAllowedAt(int targetIndex, ClipboardDataType dataType)
        {
            // Toujours autoriser le drop pour les types de données supportés
            // L'index négatif signifie "ajouter à la fin"
            return targetIndex >= -1;
        }

        #endregion

        #region Méthodes de Traitement des Données (IDragDropViewModelManager)

        /// <summary>
        /// Extrait les données texte de l'objet de données.
        /// </summary>
        /// <param name="dataObject">Objet de données</param>
        /// <returns>Données texte extraites</returns>
        public string? ExtractTextData(System.Windows.IDataObject dataObject)
        {
            if (dataObject == null) return null;

            // Essayer différents formats de texte par ordre de préférence
            if (dataObject.GetDataPresent(System.Windows.DataFormats.UnicodeText))
            {
                return dataObject.GetData(System.Windows.DataFormats.UnicodeText) as string;
            }

            if (dataObject.GetDataPresent(System.Windows.DataFormats.Text))
            {
                return dataObject.GetData(System.Windows.DataFormats.Text) as string;
            }

            if (dataObject.GetDataPresent(System.Windows.DataFormats.Html))
            {
                return dataObject.GetData(System.Windows.DataFormats.Html) as string;
            }

            if (dataObject.GetDataPresent(System.Windows.DataFormats.Rtf))
            {
                return dataObject.GetData(System.Windows.DataFormats.Rtf) as string;
            }

            return null;
        }

        /// <summary>
        /// Extrait les données image de l'objet de données.
        /// </summary>
        /// <param name="dataObject">Objet de données</param>
        /// <returns>Données image extraites</returns>
        public byte[]? ExtractImageData(System.Windows.IDataObject dataObject)
        {
            if (dataObject == null) return null;

            // Cette méthode sera implémentée selon les besoins spécifiques
            // de conversion d'image en bytes
            return null;
        }

        /// <summary>
        /// Extrait les chemins de fichiers de l'objet de données.
        /// </summary>
        /// <param name="dataObject">Objet de données</param>
        /// <returns>Chemins de fichiers extraits</returns>
        public string[]? ExtractFilePathsData(System.Windows.IDataObject dataObject)
        {
            if (dataObject == null) return null;

            if (dataObject.GetDataPresent(System.Windows.DataFormats.FileDrop))
            {
                return dataObject.GetData(System.Windows.DataFormats.FileDrop) as string[];
            }

            return null;
        }

        /// <summary>
        /// Crée un ClipboardItem à partir des données déposées.
        /// </summary>
        /// <param name="dataObject">Objet de données</param>
        /// <param name="dataType">Type de données</param>
        /// <returns>ClipboardItem créé ou null en cas d'échec</returns>
        public ClipboardItem? CreateItemFromDroppedData(System.Windows.IDataObject dataObject, ClipboardDataType dataType)
        {
            if (dataObject == null) return null;

            try
            {
                switch (dataType)
                {
                    case ClipboardDataType.Text:
                    case ClipboardDataType.Html:
                    case ClipboardDataType.Rtf:
                        var textData = ExtractTextData(dataObject);
                        if (!string.IsNullOrEmpty(textData))
                        {
                            return new ClipboardItem
                            {
                                TextPreview = textData,
                                DataType = dataType,
                                Timestamp = DateTime.Now,
                                CustomName = $"Drag & Drop - {dataType}"
                            };
                        }
                        break;

                    case ClipboardDataType.FilePath:
                        var filePaths = ExtractFilePathsData(dataObject);
                        if (filePaths != null && filePaths.Length > 0)
                        {
                            var fileList = string.Join(Environment.NewLine, filePaths);
                            return new ClipboardItem
                            {
                                TextPreview = fileList,
                                DataType = dataType,
                                Timestamp = DateTime.Now,
                                CustomName = $"Fichiers ({filePaths.Length})"
                            };
                        }
                        break;

                    case ClipboardDataType.Image:
                        var imageData = ExtractImageData(dataObject);
                        if (imageData != null)
                        {
                            return new ClipboardItem
                            {
                                RawData = imageData,
                                DataType = dataType,
                                Timestamp = DateTime.Now,
                                CustomName = "Image - Drag & Drop"
                            };
                        }
                        break;
                }

                return null;
            }
            catch (Exception ex)
            {
                DragDropError?.Invoke(this, new DragDropErrorEventArgs(
                    "CreateItemFromDroppedData", "Erreur lors de la création de l'élément", ex));
                return null;
            }
        }

        #endregion

        #region Méthodes d'Intégration (IDragDropViewModelManager)

        /// <summary>
        /// Ajoute un élément créé par drag & drop à l'historique.
        /// </summary>
        /// <param name="item">Élément à ajouter</param>
        /// <returns>True si l'ajout a réussi</returns>
        public bool AddDroppedItemToHistory(ClipboardItem item)
        {
            if (_isDisposed || item == null) return false;

            try
            {
                // Cette méthode sera implémentée lors de l'intégration
                // avec le système de gestion de l'historique
                // Pour l'instant, retourner true pour indiquer un succès simulé
                return true;
            }
            catch (Exception ex)
            {
                DragDropError?.Invoke(this, new DragDropErrorEventArgs(
                    "AddDroppedItemToHistory", "Erreur lors de l'ajout à l'historique", ex));
                return false;
            }
        }

        /// <summary>
        /// Insère un élément à une position spécifique dans l'historique.
        /// </summary>
        /// <param name="item">Élément à insérer</param>
        /// <param name="index">Index de destination</param>
        /// <returns>True si l'insertion a réussi</returns>
        public bool InsertDroppedItemAt(ClipboardItem item, int index)
        {
            if (_isDisposed || item == null || index < 0) return false;

            try
            {
                // Cette méthode sera implémentée lors de l'intégration
                // avec le système de gestion de l'historique
                // Pour l'instant, déléguer vers AddDroppedItemToHistory
                return AddDroppedItemToHistory(item);
            }
            catch (Exception ex)
            {
                DragDropError?.Invoke(this, new DragDropErrorEventArgs(
                    "InsertDroppedItemAt", "Erreur lors de l'insertion à l'index spécifié", ex));
                return false;
            }
        }

        #endregion

        #region Méthodes d'Initialisation et Nettoyage (IDragDropViewModelManager)

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        public void Initialize()
        {
            if (_isDisposed) return;

            // Initialisation des paramètres par défaut
            IsDragDropActive = false;
            CurrentDragDataType = null;
            IsDropAllowed = false;
        }

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        public void Cleanup()
        {
            if (_isDisposed) return;

            // Nettoyer l'état
            IsDragDropActive = false;
            CurrentDragDataType = null;
            IsDropAllowed = false;
        }

        /// <summary>
        /// Libère les ressources utilisées par le manager.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            Cleanup();
            _isDisposed = true;
        }

        #endregion
    }
}
