using System;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour le gestionnaire de démarrage automatique avec Windows
    /// </summary>
    public interface ISystemStartupManager
    {
        /// <summary>
        /// Détermine si l'application est configurée pour démarrer avec Windows
        /// </summary>
        /// <returns>True si l'application démarre avec Windows, False sinon</returns>
        bool IsStartupEnabled();

        /// <summary>
        /// Active ou désactive le démarrage automatique avec Windows
        /// </summary>
        /// <param name="enable">True pour activer, False pour désactiver</param>
        /// <returns>True si l'opération a réussi, False sinon</returns>
        bool SetStartupEnabled(bool enable);

        /// <summary>
        /// Synchronise l'état du démarrage automatique avec le paramètre de l'application
        /// </summary>
        /// <param name="shouldStartWithWindows">Valeur du paramètre StartWithWindows</param>
        void SyncStartupWithSettings(bool shouldStartWithWindows);
    }
} 