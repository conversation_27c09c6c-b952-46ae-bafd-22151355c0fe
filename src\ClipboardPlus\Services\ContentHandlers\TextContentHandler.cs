using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services.ContentHandlers
{
    /// <summary>
    /// Handler pour le contenu texte.
    /// Remplace la logique LoadTextContent() du ContentPreviewViewModel.
    /// </summary>
    public class TextContentHandler : BaseContentHandler
    {
        /// <summary>
        /// Initialise une nouvelle instance de TextContentHandler.
        /// </summary>
        /// <param name="loggingService">Service de logging optionnel</param>
        public TextContentHandler(ILoggingService? loggingService = null) 
            : base(loggingService)
        {
        }

        /// <inheritdoc />
        public override ClipboardDataType SupportedDataType => ClipboardDataType.Text;

        /// <inheritdoc />
        public override string GetDefaultUnavailableMessage()
        {
            return "Contenu texte non disponible.";
        }

        /// <inheritdoc />
        protected override object HandleContentInternal(ClipboardItem item)
        {
            _loggingService?.LogInfo($"TextContentHandler.HandleContentInternal - Traitement du contenu texte pour l'élément ID {item.Id}");

            // Priorité 1: Utiliser RawData si disponible
            if (item.RawData != null && item.RawData.Length > 0)
            {
                _loggingService?.LogInfo($"TextContentHandler - Données brutes disponibles, taille: {item.RawData.Length} octets");
                
                string? decodedText = DecodeRawDataAsText(item.RawData);
                if (decodedText != null)
                {
                    _loggingService?.LogInfo($"TextContentHandler - Texte décodé avec succès, longueur: {decodedText.Length} caractères");
                    
                    // Log d'un aperçu du texte pour le débogage
                    string textPreview = decodedText.Length <= 100 ? decodedText : decodedText.Substring(0, 100) + "...";
                    _loggingService?.LogInfo($"TextContentHandler - Aperçu du texte: '{textPreview}'");
                    
                    return decodedText;
                }
                else
                {
                    _loggingService?.LogWarning("TextContentHandler - Échec du décodage des données brutes, utilisation du TextPreview");
                }
            }
            else
            {
                _loggingService?.LogWarning("TextContentHandler - Données brutes non disponibles, utilisation du TextPreview");
            }

            // Priorité 2: Utiliser TextPreview si RawData n'est pas disponible
            if (!string.IsNullOrEmpty(item.TextPreview))
            {
                _loggingService?.LogInfo($"TextContentHandler - Utilisation du TextPreview: '{item.TextPreview}'");
                return item.TextPreview;
            }

            // Priorité 3: Message par défaut si aucune donnée n'est disponible
            string defaultMessage = GetDefaultUnavailableMessage();
            _loggingService?.LogInfo($"TextContentHandler - Aucune donnée disponible, utilisation du message par défaut: '{defaultMessage}'");
            
            return defaultMessage;
        }
    }
}
