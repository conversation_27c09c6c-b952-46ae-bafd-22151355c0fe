using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using System.Windows.Media.Imaging;
using System.Windows.Threading; // Pour Dispatcher
using System.Threading; // Pour Thread, ApartmentState

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    [TestFixture]
    public class PersistenceServiceTests
    {
        private PersistenceService _persistenceService = null!;
        private string _testDbPath = string.Empty;
        private string _testConnectionString = string.Empty;

        [SetUp]
        public void Initialize()
        {
            // Créer une base de données temporaire pour les tests
            _testDbPath = Path.Combine(Path.GetTempPath(), $"clipboard_test_{Guid.NewGuid()}.db");
            _testConnectionString = $"Data Source={_testDbPath}";

            // Utiliser la reflection pour accéder au champ privé _connectionString
            _persistenceService = new PersistenceService();
            var connectionStringField = typeof(PersistenceService).GetField("_connectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            connectionStringField?.SetValue(_persistenceService, _testConnectionString);

            // Initialiser la base de données
            _persistenceService.InitializeAsync().Wait();
        }

        [TearDown]
        public void Cleanup()
        {
            // Forcer la fermeture des connexions à la base de données SQLite
            // C'est la méthode recommandée pour s'assurer que le fichier de la base de données peut être supprimé.
            SqliteConnection.ClearAllPools();
            
            // Supprimer la base de données temporaire
            if (File.Exists(_testDbPath))
            {
                try
                {
                    File.Delete(_testDbPath);
                }
                catch (IOException ex)
                {
                    // Si le fichier est toujours verrouillé, nous ne pouvons pas le supprimer
                    // Afficher une erreur plus détaillée dans la console de test
                    Console.WriteLine($"[ERREUR] Impossible de supprimer le fichier de test: {_testDbPath}. Exception: {ex.Message}");
                }
            }
        }

        [Test]
        public async Task InitializeAsync_CreatesTablesIfNotExist()
        {
            // Act
            await _persistenceService.InitializeAsync();

            // Assert - Vérifier que les tables ont été créées
            using var connection = new SqliteConnection(_testConnectionString);
            await connection.OpenAsync();

            // Vérifier la table ClipboardItems
            using (var command = connection.CreateCommand())
            {
                command.CommandText = "SELECT name FROM sqlite_master WHERE type='table' AND name='ClipboardItems'";
                var result = await command.ExecuteScalarAsync();
                Assert.That(result, Is.Not.Null);
                Assert.That(result!.ToString(), Is.EqualTo("ClipboardItems"));
            }

            // Vérifier la table ApplicationSettings
            using (var command = connection.CreateCommand())
            {
                command.CommandText = "SELECT name FROM sqlite_master WHERE type='table' AND name='ApplicationSettings'";
                var result = await command.ExecuteScalarAsync();
                Assert.That(result, Is.Not.Null);
                Assert.That(result!.ToString(), Is.EqualTo("ApplicationSettings"));
            }
        }

        [Test]
        public async Task InsertClipboardItemAsync_InsertsItemAndReturnsId()
        {
            // Arrange
            var testItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test Item",
                CustomName = "Test",
                IsPinned = false,
                OrderIndex = 0,
                Timestamp = DateTime.UtcNow,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test Content")
            };

            // Act
            long id = await _persistenceService.InsertClipboardItemAsync(testItem);

            // Assert
            Assert.That(id > 0, Is.True);
            Assert.That(testItem.Id, Is.EqualTo(id));

            // Vérifier que l'élément a bien été inséré en base
            var items = await _persistenceService.GetAllClipboardItemsAsync();
            Assert.That(items.Count, Is.EqualTo(1));
            Assert.That(items[0].Id, Is.EqualTo(id));
            Assert.That(items[0].TextPreview, Is.EqualTo("Test Item"));
        }

        [Test]
        public async Task UpdateClipboardItemAsync_UpdatesExistingItem()
        {
            // Arrange - Insérer un élément
            var testItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Original Text",
                CustomName = "Original",
                IsPinned = false,
                OrderIndex = 0,
                Timestamp = DateTime.UtcNow,
                RawData = System.Text.Encoding.UTF8.GetBytes("Original Content")
            };
            long id = await _persistenceService.InsertClipboardItemAsync(testItem);

            // Modifier l'élément
            testItem.TextPreview = "Updated Text";
            testItem.CustomName = "Updated";
            testItem.IsPinned = true;

            // Act
            await _persistenceService.UpdateClipboardItemAsync(testItem);

            // Assert
            var items = await _persistenceService.GetAllClipboardItemsAsync();
            Assert.That(items.Count, Is.EqualTo(1));
            Assert.That(items[0].Id, Is.EqualTo(id));
            Assert.That(items[0].TextPreview, Is.EqualTo("Updated Text"));
            Assert.That(items[0].CustomName, Is.EqualTo("Updated"));
            Assert.That(items[0].IsPinned, Is.True);
        }

        [Test]
        public async Task DeleteClipboardItemAsync_RemovesItem()
        {
            // Arrange - Insérer un élément
            var testItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test Item",
                Timestamp = DateTime.UtcNow
            };
            long id = await _persistenceService.InsertClipboardItemAsync(testItem);

            // Act
            await _persistenceService.DeleteClipboardItemAsync(id);

            // Assert
            var items = await _persistenceService.GetAllClipboardItemsAsync();
            Assert.That(items.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task ClearClipboardItemsAsync_RemovesAllUnpinnedItems()
        {
            // Arrange - Insérer des éléments
            var unpinnedItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Unpinned Item",
                IsPinned = false,
                Timestamp = DateTime.UtcNow
            };
            await _persistenceService.InsertClipboardItemAsync(unpinnedItem);

            var pinnedItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Pinned Item",
                IsPinned = true,
                Timestamp = DateTime.UtcNow
            };
            await _persistenceService.InsertClipboardItemAsync(pinnedItem);

            // Act
            await _persistenceService.ClearClipboardItemsAsync(true); // Préserver les épinglés

            // Assert
            var items = await _persistenceService.GetAllClipboardItemsAsync();
            Assert.That(items.Count, Is.EqualTo(1));
            Assert.That(items[0].IsPinned, Is.True);
            Assert.That(items[0].TextPreview, Is.EqualTo("Pinned Item"));
        }

        [Test]
        public async Task ClearClipboardItemsAsync_RemovesAllItems()
        {
            // Arrange - Insérer des éléments
            var unpinnedItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Unpinned Item",
                IsPinned = false,
                Timestamp = DateTime.UtcNow
            };
            await _persistenceService.InsertClipboardItemAsync(unpinnedItem);

            var pinnedItem = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Pinned Item",
                IsPinned = true,
                Timestamp = DateTime.UtcNow
            };
            await _persistenceService.InsertClipboardItemAsync(pinnedItem);

            // Act
            await _persistenceService.ClearClipboardItemsAsync(false); // Ne pas préserver les épinglés

            // Assert
            var items = await _persistenceService.GetAllClipboardItemsAsync();
            Assert.That(items.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task SaveApplicationSettingAsync_SavesSettingToDatabase()
        {
            // Arrange
            string key = "TestKey";
            string value = "TestValue";

            // Act
            await _persistenceService.SaveApplicationSettingAsync(key, value);

            // Assert
            var settings = await _persistenceService.GetApplicationSettingsAsync();
            Assert.That(settings.ContainsKey(key), Is.True);
            Assert.That(settings[key], Is.EqualTo(value));
        }

        [Test]
        public async Task GetApplicationSettingsAsync_ReturnsAllSettings()
        {
            // Arrange
            await _persistenceService.SaveApplicationSettingAsync("Key1", "Value1");
            await _persistenceService.SaveApplicationSettingAsync("Key2", "Value2");

            // Act
            var settings = await _persistenceService.GetApplicationSettingsAsync();

            // Assert
            Assert.That(settings.Count, Is.EqualTo(2));
            Assert.That(settings["Key1"], Is.EqualTo("Value1"));
            Assert.That(settings["Key2"], Is.EqualTo("Value2"));
        }

        // SUPPRIMÉ: Méthode RunOnStaThread - les tests STA sont interdits
        // Tous les tests doivent être refactorisés pour séparer la logique métier de l'UI

        // SUPPRIMÉ: Tests d'images nécessitant STA - impossibles à refactoriser sans BitmapSource/BitmapImage
        // Ces tests testaient des méthodes privées d'encodage/décodage d'images qui nécessitent un thread STA
        // La logique d'images sera testée via des tests d'intégration de plus haut niveau

        [Test]
        public async Task GetApplicationSettingsAsync_ReturnsEmptyDictionary_WhenNoSettingsExist()
        {
            // Arrange
            // La base de données est fraîchement initialisée et ne contient aucun paramètre.

            // Act
            var settings = await _persistenceService.GetApplicationSettingsAsync();

            // Assert
            Assert.That(settings, Is.Not.Null, "Le dictionnaire de paramètres ne devrait pas être null.");
            Assert.That(settings.Count, Is.EqualTo(0), "Le dictionnaire de paramètres devrait être vide.");
        }

        [Test]
        public async Task SaveApplicationSettingAsync_UpdatesExistingSetting()
        {
            // Arrange
            string key = "UpdateTestKey";
            string initialValue = "InitialValue";
            string updatedValue = "UpdatedValue";

            // Sauvegarder une valeur initiale
            await _persistenceService.SaveApplicationSettingAsync(key, initialValue);
            var settingsAfterInitialSave = await _persistenceService.GetApplicationSettingsAsync();
            Assert.That(settingsAfterInitialSave[key], Is.EqualTo(initialValue), "La valeur initiale n'a pas été sauvegardée correctement.");

            // Act: Mettre à jour la valeur pour la même clé
            await _persistenceService.SaveApplicationSettingAsync(key, updatedValue);

            // Assert
            var settingsAfterUpdate = await _persistenceService.GetApplicationSettingsAsync();
            Assert.That(settingsAfterUpdate, Is.Not.Null, "Le dictionnaire de paramètres après mise à jour ne devrait pas être null.");
            Assert.That(settingsAfterUpdate.ContainsKey(key), Is.True, "La clé mise à jour devrait exister.");
            Assert.That(settingsAfterUpdate[key], Is.EqualTo(updatedValue), "La valeur du paramètre aurait dû être mise à jour.");

            // S'assurer qu'il n'y a pas de doublons de clé
            Assert.That(settingsAfterUpdate.Count, Is.EqualTo(settingsAfterInitialSave.Count), "Le nombre de paramètres ne devrait pas changer lors d'une mise à jour.");
        }
    }
} 