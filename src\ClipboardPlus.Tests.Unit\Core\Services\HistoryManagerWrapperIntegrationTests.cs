using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Interfaces;
using Moq;
using NUnit.Framework;
using System;
using System.Threading.Tasks;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests d'intégration pour vérifier que le HistoryManagerWrapper fonctionne correctement
    /// avec l'orchestrateur SOLID et partage la même collection que l'interface utilisateur.
    /// </summary>
    [TestFixture]
    public class HistoryManagerWrapperIntegrationTests
    {
        private Mock<IPersistenceService> _mockPersistenceService;
        private Mock<ISettingsManager> _mockSettingsManager;
        private Mock<IClipboardInteractionService> _mockClipboardInteractionService;
        private Mock<ILoggingService> _mockLoggingService;
        private ClipboardHistoryManager _historyManager;

        [SetUp]
        public void SetUp()
        {
            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockClipboardInteractionService = new Mock<IClipboardInteractionService>();
            _mockLoggingService = new Mock<ILoggingService>();

            // Configurer les mocks pour retourner des valeurs valides
            var idCounter = 1L;
            _mockPersistenceService.Setup(p => p.InsertClipboardItemAsync(It.IsAny<ClipboardItem>()))
                .Returns(() => Task.FromResult(idCounter++));

            _mockPersistenceService.Setup(p => p.UpdateClipboardItemAsync(It.IsAny<ClipboardItem>()))
                .Returns(Task.FromResult(1));

            _mockPersistenceService.Setup(p => p.GetAllClipboardItemsAsync())
                .Returns(Task.FromResult(new List<ClipboardItem>()));

            _mockSettingsManager.Setup(s => s.MaxHistoryItems).Returns(100);
            _mockSettingsManager.Setup(s => s.MaxStorableItemSizeBytes).Returns(1024 * 1024); // 1MB

            _historyManager = new ClipboardHistoryManager(
                _mockPersistenceService.Object,
                _mockSettingsManager.Object,
                _mockClipboardInteractionService.Object);
        }

        [Test]
        public async Task AddItemAsync_WithMultipleItems_DuplicateDetectorSeesAllItems()
        {
            // Arrange
            var item1 = new ClipboardItem
            {
                TextPreview = "Premier élément",
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Premier élément"),
                Timestamp = DateTime.Now.AddMinutes(-2)
            };

            var item2 = new ClipboardItem
            {
                TextPreview = "Deuxième élément",
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Deuxième élément"),
                Timestamp = DateTime.Now.AddMinutes(-1)
            };

            var item3 = new ClipboardItem
            {
                TextPreview = "Premier élément", // Duplicate du premier
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Premier élément"),
                Timestamp = DateTime.Now
            };

            // Act - Ajouter les éléments un par un
            await _historyManager.AddItemAsync(item1);
            await _historyManager.AddItemAsync(item2);
            
            // Vérifier l'état avant d'ajouter le duplicata
            Assert.AreEqual(2, _historyManager.HistoryItems.Count, "Devrait avoir 2 éléments avant le duplicata");

            // Ajouter le duplicata
            await _historyManager.AddItemAsync(item3);

            // Assert - Vérifier que le duplicata a été détecté et traité correctement
            Assert.AreEqual(2, _historyManager.HistoryItems.Count, "Devrait toujours avoir 2 éléments après détection du duplicata");
            
            // Le premier élément devrait être le duplicata (plus récent)
            Assert.AreEqual("Premier élément", _historyManager.HistoryItems[0].TextPreview);
            Assert.IsTrue(_historyManager.HistoryItems[0].Id > 0, "Le duplicata plus récent devrait avoir un ID valide");

            // Le deuxième élément devrait être le deuxième élément original
            Assert.AreEqual("Deuxième élément", _historyManager.HistoryItems[1].TextPreview);
            Assert.IsTrue(_historyManager.HistoryItems[1].Id > 0, "Le deuxième élément devrait avoir un ID valide");
        }

        [Test]
        public async Task AddItemAsync_VerifyDuplicateDetectorUsesCorrectCollection()
        {
            // Arrange - Ajouter quelques éléments d'abord
            var existingItem = new ClipboardItem
            {
                TextPreview = "Élément existant",
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Élément existant"),
                Timestamp = DateTime.Now.AddMinutes(-5)
            };

            await _historyManager.AddItemAsync(existingItem);
            
            // Vérifier que l'élément a été ajouté
            Assert.AreEqual(1, _historyManager.HistoryItems.Count);

            // Act - Ajouter un duplicata
            var duplicateItem = new ClipboardItem
            {
                TextPreview = "Élément existant", // Même contenu
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Élément existant"),
                Timestamp = DateTime.Now
            };

            await _historyManager.AddItemAsync(duplicateItem);

            // Assert - Le DuplicateDetector devrait avoir vu l'élément existant
            // et traité le duplicata correctement
            Assert.AreEqual(1, _historyManager.HistoryItems.Count, 
                "Le DuplicateDetector devrait avoir détecté le duplicata et maintenu un seul élément");
            
            // L'élément restant devrait être le plus récent (le premier ajouté aura l'ID 1)
            Assert.AreEqual(1, _historyManager.HistoryItems[0].Id,
                "L'élément existant devrait être conservé et mis à jour");
        }

        [Test]
        public async Task AddItemAsync_WithEmptyHistory_AddsItemCorrectly()
        {
            // Arrange
            var newItem = new ClipboardItem
            {
                TextPreview = "Premier élément",
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Premier élément"),
                Timestamp = DateTime.Now
            };

            // Act
            await _historyManager.AddItemAsync(newItem);

            // Assert
            Assert.AreEqual(1, _historyManager.HistoryItems.Count);
            Assert.AreEqual("Premier élément", _historyManager.HistoryItems[0].TextPreview);
            Assert.AreEqual(1, _historyManager.HistoryItems[0].Id);
        }

        [Test]
        public void HistoryItems_ReturnsCorrectCollection()
        {
            // Arrange & Act - La collection devrait être initialisée et vide
            var items = _historyManager.HistoryItems;

            // Assert
            Assert.IsNotNull(items, "La collection HistoryItems ne devrait jamais être null");
            Assert.AreEqual(0, items.Count, "La collection devrait être vide initialement");
        }
    }
}
