using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Interface pour la gestion de l'état global d'une opération de suppression
    /// Responsabilité : Déterminer le résultat final basé sur les résultats mémoire et BDD
    /// </summary>
    public interface IDeletionStateManager
    {
        /// <summary>
        /// Détermine le résultat global d'une opération de suppression
        /// </summary>
        /// <param name="memoryResult">Résultat de la suppression en mémoire</param>
        /// <param name="databaseResult">Résultat de la suppression en base de données</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Résultat global de l'opération</returns>
        Task<GlobalDeletionResult> DetermineGlobalResultAsync(
            MemoryDeletionResult memoryResult, 
            DatabaseDeletionResult databaseResult, 
            string operationId);

        /// <summary>
        /// Analyse la cohérence entre les résultats mémoire et BDD
        /// </summary>
        /// <param name="memoryResult">Résultat de la suppression en mémoire</param>
        /// <param name="databaseResult">Résultat de la suppression en base de données</param>
        /// <returns>Analyse de cohérence</returns>
        ConsistencyAnalysis AnalyzeConsistency(MemoryDeletionResult memoryResult, DatabaseDeletionResult databaseResult);
    }

    /// <summary>
    /// Résultat global d'une opération de suppression
    /// </summary>
    public class GlobalDeletionResult
    {
        /// <summary>
        /// Indique si l'opération globale est considérée comme réussie
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Résultat de la suppression en mémoire
        /// </summary>
        public MemoryDeletionResult MemoryResult { get; set; } = new();

        /// <summary>
        /// Résultat de la suppression en base de données
        /// </summary>
        public DatabaseDeletionResult DatabaseResult { get; set; } = new();

        /// <summary>
        /// Analyse de cohérence entre mémoire et BDD
        /// </summary>
        public ConsistencyAnalysis Consistency { get; set; } = new();

        /// <summary>
        /// Message explicatif du résultat global
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// Indique si une notification d'événement doit être déclenchée
        /// </summary>
        public bool ShouldNotify { get; set; }

        /// <summary>
        /// Crée un résultat global de succès
        /// </summary>
        public static GlobalDeletionResult CreateSuccess(MemoryDeletionResult memoryResult, DatabaseDeletionResult databaseResult,
            ConsistencyAnalysis consistency, string? message = null, bool shouldNotify = true) =>
            new()
            {
                Success = true,
                MemoryResult = memoryResult,
                DatabaseResult = databaseResult,
                Consistency = consistency,
                Message = message,
                ShouldNotify = shouldNotify
            };

        /// <summary>
        /// Crée un résultat global d'échec
        /// </summary>
        public static GlobalDeletionResult CreateFailure(MemoryDeletionResult memoryResult, DatabaseDeletionResult databaseResult,
            ConsistencyAnalysis consistency, string? message = null, bool shouldNotify = false) =>
            new()
            {
                Success = false,
                MemoryResult = memoryResult,
                DatabaseResult = databaseResult,
                Consistency = consistency,
                Message = message,
                ShouldNotify = shouldNotify
            };
    }

    /// <summary>
    /// Analyse de cohérence entre les résultats mémoire et BDD
    /// </summary>
    public class ConsistencyAnalysis
    {
        /// <summary>
        /// Indique si les résultats sont cohérents
        /// </summary>
        public bool IsConsistent { get; set; }

        /// <summary>
        /// Type d'incohérence détectée
        /// </summary>
        public InconsistencyType? InconsistencyType { get; set; }

        /// <summary>
        /// Message explicatif de l'analyse
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// Niveau de gravité de l'incohérence
        /// </summary>
        public SeverityLevel Severity { get; set; } = SeverityLevel.None;
    }

    /// <summary>
    /// Types d'incohérence possibles
    /// </summary>
    public enum InconsistencyType
    {
        None,
        MemorySuccessDatabaseFailure,
        MemoryFailureDatabaseSuccess,
        BothFailed,
        UnexpectedState
    }

    /// <summary>
    /// Niveaux de gravité
    /// </summary>
    public enum SeverityLevel
    {
        None,
        Info,
        Warning,
        Error,
        Critical
    }
}
