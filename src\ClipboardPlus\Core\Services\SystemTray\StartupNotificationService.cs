using System;
using System.Windows.Forms;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Implémentation du service de notification de démarrage.
    /// Responsabilité unique : gérer l'affichage des notifications lors de l'initialisation.
    /// </summary>
    public class StartupNotificationService : IStartupNotificationService
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de StartupNotificationService.
        /// </summary>
        /// <param name="loggingService">Service de logging pour enregistrer les opérations de notification.</param>
        public StartupNotificationService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <inheritdoc />
        public void ShowStartupNotification(NotifyIcon notifyIcon)
        {
            if (notifyIcon == null)
            {
                throw new ArgumentNullException(nameof(notifyIcon));
            }

            try
            {
                _loggingService.LogInfo("StartupNotificationService: Affichage de la notification de démarrage...");

                // Utiliser le message standard de démarrage
                ShowCustomNotification(
                    notifyIcon,
                    "ClipboardPlus - Démarrage",
                    "L'application démarre et est accessible depuis la zone de notification.",
                    ToolTipIcon.Info
                );

                _loggingService.LogInfo("StartupNotificationService: Notification de démarrage affichée avec succès");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"StartupNotificationService: Erreur lors de l'affichage de la notification de démarrage: {ex.Message}", ex);
                // Ne pas remonter l'exception car l'affichage de notification n'est pas critique
            }
        }

        /// <inheritdoc />
        public void ShowCustomNotification(NotifyIcon notifyIcon, string title, string message, ToolTipIcon iconType)
        {
            if (notifyIcon == null)
            {
                throw new ArgumentNullException(nameof(notifyIcon));
            }

            if (string.IsNullOrWhiteSpace(title))
            {
                throw new ArgumentException("Le titre de la notification ne peut pas être vide.", nameof(title));
            }

            if (string.IsNullOrWhiteSpace(message))
            {
                throw new ArgumentException("Le message de la notification ne peut pas être vide.", nameof(message));
            }

            try
            {
                _loggingService.LogInfo($"StartupNotificationService: Tentative d'affichage d'une notification: '{title}'");

                // Vérifier si les notifications sont supportées
                if (!AreNotificationsSupported())
                {
                    _loggingService.LogWarning("StartupNotificationService: Les notifications ne sont pas supportées sur ce système");
                    return;
                }

                // Afficher la notification
                notifyIcon.ShowBalloonTip(
                    3000, // Durée en millisecondes
                    title,
                    message,
                    iconType
                );

                _loggingService.LogInfo($"StartupNotificationService: Notification '{title}' affichée avec succès");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"StartupNotificationService: Erreur lors de l'affichage de la notification '{title}': {ex.Message}", ex);
                // Ne pas remonter l'exception car l'affichage de notification n'est pas critique
            }
        }

        /// <inheritdoc />
        public bool AreNotificationsSupported()
        {
            try
            {
                // Vérifier si les notifications sont supportées sur cette plateforme
                // Cette méthode pourrait être étendue pour vérifier la version de Windows, etc.
                bool supported = Environment.OSVersion.Platform == PlatformID.Win32NT;

                _loggingService.LogInfo($"StartupNotificationService: Vérification du support des notifications - Résultat: {supported}");

                return supported;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"StartupNotificationService: Erreur lors de la vérification du support des notifications: {ex.Message}", ex);
                // En cas d'erreur, considérer que les notifications ne sont pas supportées
                return false;
            }
        }
    }
}
