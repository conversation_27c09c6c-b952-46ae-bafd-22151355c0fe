using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.UI.Controls
{
    /// <summary>
    /// TextBox personnalisé pour capturer les combinaisons de touches.
    /// </summary>
    public class ShortcutCaptureTextBox : System.Windows.Controls.TextBox
    {
        // Variables de capture supprimées en Phase 2 - Plus nécessaires avec la nouvelle UX

        // Événement ShortcutChanged supprimé en Phase 2 - Plus nécessaire avec la nouvelle UX

        /// <summary>
        /// Propriété de dépendance pour la combinaison de touches actuelle.
        /// </summary>
        public static readonly DependencyProperty CurrentShortcutProperty = 
            DependencyProperty.Register(
                "CurrentShortcut", 
                typeof(KeyCombination), 
                typeof(ShortcutCaptureTextBox), 
                new PropertyMetadata(new KeyCombination(ModifierKeys.Control | ModifierKeys.Alt, Key.V), OnCurrentShortcutChanged));

        /// <summary>
        /// Combinaison de touches actuelle.
        /// </summary>
        public KeyCombination CurrentShortcut
        {
            get => (KeyCombination)GetValue(CurrentShortcutProperty);
            set => SetValue(CurrentShortcutProperty, value);
        }

        /// <summary>
        /// Méthode appelée lorsque la propriété CurrentShortcut change.
        /// </summary>
        private static void OnCurrentShortcutChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ShortcutCaptureTextBox textBox && e.NewValue is KeyCombination shortcut)
            {
                textBox.Text = shortcut.ToString();
            }
        }

        /// <summary>
        /// Constructeur par défaut.
        /// </summary>
        public ShortcutCaptureTextBox()
        {
            IsReadOnly = true;
            HorizontalContentAlignment = System.Windows.HorizontalAlignment.Center;
            VerticalContentAlignment = System.Windows.VerticalAlignment.Center;
            FontWeight = FontWeights.SemiBold;
            TabIndex = 0; // Donner un TabIndex élevé pour faciliter la navigation au clavier

            // MODIFIÉ Phase 1 : Nouveau message pour rediriger vers le bouton Modifier
            ToolTip = "Utilisez le bouton 'Modifier...' pour changer le raccourci clavier";
            
            // Définir un style visuel qui indique que le contrôle est interactif
            Cursor = System.Windows.Input.Cursors.IBeam;
            
            // Désactiver le menu contextuel standard pour éviter les couper/copier/coller
            ContextMenu = null;
            
            // Initialiser le texte avec le raccourci actuel
            Loaded += (s, e) => {
                Text = CurrentShortcut?.ToString() ?? "Aucun raccourci";
            };

            // Événements de capture supprimés en Phase 2 - Plus nécessaires avec la nouvelle UX
        }

        private void ShortcutCaptureTextBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            // DÉSACTIVÉ Phase 1 : Ancienne méthode de capture par double-clic
            System.Diagnostics.Debug.WriteLine("ShortcutCaptureTextBox: Capture par double-clic désactivée. Utilisez le bouton Modifier.");
            e.Handled = true;
        }

        // Méthode ShortcutCaptureTextBox_PreviewKeyDown supprimée en Phase 2 - Plus nécessaire

        // Méthode OnPreviewKeyDown supprimée en Phase 2 - Plus nécessaire avec la nouvelle UX

        // Méthode HandleKeyDown supprimée en Phase 2 - Plus nécessaire avec la nouvelle UX

        // Méthode ActivateCaptureMode supprimée en Phase 2 - Plus nécessaire avec la nouvelle UX

        // Méthode DeactivateCaptureMode supprimée en Phase 2 - Plus nécessaire avec la nouvelle UX

        /// <summary>
        /// Gère l'événement GotFocus pour indiquer visuellement que le contrôle est prêt à capturer des touches.
        /// </summary>
        protected override void OnGotFocus(RoutedEventArgs e)
        {
            base.OnGotFocus(e);

            // DÉSACTIVÉ Phase 1 : Ancienne méthode de capture par focus
            System.Diagnostics.Debug.WriteLine("ShortcutCaptureTextBox: Capture par focus désactivée. Utilisez le bouton Modifier.");
        }

        // Méthode OnLostFocus supprimée en Phase 2 - Plus nécessaire avec la nouvelle UX

        // Méthode RestoreVisualState supprimée en Phase 2 - Plus nécessaire avec la nouvelle UX
    }
} 