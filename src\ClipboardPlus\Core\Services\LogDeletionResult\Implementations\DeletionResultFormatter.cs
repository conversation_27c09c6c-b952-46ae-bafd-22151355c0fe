using System;
using System.Linq;
using System.Text;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Implementations
{
    /// <summary>
    /// Implémentation du formateur de logs de suppression.
    /// Sépare la logique de formatage de la logique métier.
    /// </summary>
    public class DeletionResultFormatter : IDeletionResultFormatter
    {
        private readonly ILoggingService _loggingService;

        public DeletionResultFormatter(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Formate le résultat d'une suppression pour le logging.
        /// </summary>
        public string FormatDeletionResult(DeletionResultContext context)
        {
            _loggingService?.LogInfo("[VERIFICATION_TEST_2025] DeletionResultFormatter.FormatDeletionResult APPELÉE");
            try
            {
                var sb = new StringBuilder();

                // Informations de base
                sb.AppendLine($"Succès: {context.Success}");
                
                if (!string.IsNullOrEmpty(context.Message))
                {
                    sb.AppendLine($"Message: {context.Message}");
                }

                sb.AppendLine($"Thread ID: {context.ThreadId}");
                sb.AppendLine($"Operation ID: {context.OperationId}");

                // Informations sur l'appelant
                if (context.CallerInfo != null)
                {
                    sb.AppendLine();
                    sb.AppendLine("INFORMATIONS APPELANT:");
                    sb.AppendLine($"  Méthode: {context.CallerInfo.MethodName}");
                    sb.AppendLine($"  Classe: {context.CallerInfo.ClassName}");
                    if (!string.IsNullOrEmpty(context.CallerInfo.FileName))
                    {
                        sb.AppendLine($"  Fichier: {context.CallerInfo.FileName}:{context.CallerInfo.LineNumber}");
                    }
                }

                // Informations sur le contexte d'exécution
                if (context.ExecutionContext != null)
                {
                    sb.AppendLine();
                    sb.AppendLine("CONTEXTE D'EXÉCUTION:");
                    sb.AppendLine($"  Mémoire utilisée: {FormatBytes(context.ExecutionContext.MemoryUsage)}");
                    sb.AppendLine($"  Process ID: {context.ExecutionContext.ProcessId}");
                    sb.AppendLine($"  Application: {context.ExecutionContext.ApplicationName} v{context.ExecutionContext.ApplicationVersion}");
                }

                return sb.ToString();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"❌ [FORMATTER] Erreur formatage résultat suppression: {ex.Message}", ex);
                return $"Erreur de formatage: {ex.Message}";
            }
        }

        /// <summary>
        /// Formate les résultats de validation pour le logging.
        /// </summary>
        public string FormatValidationResult(ComprehensiveValidationResult validation)
        {
            try
            {
                var sb = new StringBuilder();

                sb.AppendLine("VÉRIFICATION POST-SUPPRESSION:");
                sb.AppendLine($"  Validation ID: {validation.ValidationId}");
                sb.AppendLine($"  Statut global: {(validation.IsFullyValid ? "✅ VALIDE" : "❌ INVALIDE")}");
                sb.AppendLine($"  Durée: {validation.ValidationDuration.TotalMilliseconds:F1}ms");
                sb.AppendLine($"  Problèmes détectés: {validation.TotalIssuesCount}");

                // Validation post-suppression
                if (validation.PostDeletionValidation != null)
                {
                    sb.AppendLine();
                    sb.AppendLine("  Post-suppression:");
                    sb.AppendLine($"    Élément encore dans collection: {(validation.PostDeletionValidation.ItemStillInCollection ? "❌ OUI" : "✅ NON")}");
                    sb.AppendLine($"    Élément encore dans manager: {(validation.PostDeletionValidation.ItemStillInManager ? "❌ OUI" : "✅ NON")}");
                    sb.AppendLine($"    Instances correspondent: {(validation.PostDeletionValidation.InstancesMatch ? "✅ OUI" : "⚠️ NON")}");

                    if (validation.PostDeletionValidation.Issues.Count > 0)
                    {
                        sb.AppendLine("    Problèmes:");
                        foreach (var issue in validation.PostDeletionValidation.Issues)
                        {
                            sb.AppendLine($"      {GetSeverityIcon(issue.Severity)} [{issue.Code}] {issue.Description}");
                        }
                    }
                }

                // Validation des collections
                if (validation.CollectionValidation != null)
                {
                    sb.AppendLine();
                    sb.AppendLine("  Collections:");
                    sb.AppendLine($"    ViewModel: {validation.CollectionValidation.ViewModelItemCount} éléments");
                    sb.AppendLine($"    Manager: {validation.CollectionValidation.ManagerItemCount} éléments");
                    sb.AppendLine($"    Synchronisées: {(validation.CollectionValidation.CollectionsInSync ? "✅ OUI" : "❌ NON")}");
                    sb.AppendLine($"    Éléments null: {validation.CollectionValidation.NullItemCount}");
                    sb.AppendLine($"    Doublons: {validation.CollectionValidation.DuplicateCount}");

                    if (validation.CollectionValidation.Anomalies.Count > 0)
                    {
                        sb.AppendLine("    Anomalies:");
                        foreach (var anomaly in validation.CollectionValidation.Anomalies)
                        {
                            sb.AppendLine($"      {GetAnomalyIcon(anomaly.Type)} {anomaly.Description} (x{anomaly.Count})");
                        }
                    }
                }

                // Validation de cohérence
                if (validation.ConsistencyValidation != null)
                {
                    sb.AppendLine();
                    sb.AppendLine("  Cohérence:");
                    sb.AppendLine($"    État global: {(validation.ConsistencyValidation.IsConsistent ? "✅ COHÉRENT" : "❌ INCOHÉRENT")}");
                    sb.AppendLine($"    Manager accessible: {(validation.ConsistencyValidation.ManagerAccessible ? "✅ OUI" : "❌ NON")}");
                    sb.AppendLine($"    ViewModel valide: {(validation.ConsistencyValidation.ViewModelValid ? "✅ OUI" : "❌ NON")}");
                    sb.AppendLine($"    Mise à jour en cours: {(validation.ConsistencyValidation.UpdateInProgress ? "⚠️ OUI" : "✅ NON")}");

                    if (validation.ConsistencyValidation.Inconsistencies.Count > 0)
                    {
                        sb.AppendLine("    Incohérences:");
                        foreach (var inconsistency in validation.ConsistencyValidation.Inconsistencies)
                        {
                            sb.AppendLine($"      {GetImpactIcon(inconsistency.Impact)} {inconsistency.Description}");
                        }
                    }
                }

                return sb.ToString();
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [FORMATTER] Erreur formatage validation: {ex.Message}", ex);
                return $"Erreur de formatage validation: {ex.Message}";
            }
        }

        /// <summary>
        /// Formate l'état des collections pour le logging.
        /// </summary>
        public string FormatCollectionState(CollectionStateInfo state)
        {
            _loggingService?.LogInfo("[VERIFICATION_TEST_2025] DeletionResultFormatter.FormatCollectionState APPELÉE");
            try
            {
                var sb = new StringBuilder();

                sb.AppendLine("ÉTAT DES COLLECTIONS:");
                sb.AppendLine($"  Analysis ID: {state.AnalysisId}");
                sb.AppendLine($"  Statut: {(state.AnalysisSuccessful ? "✅ SUCCÈS" : "❌ ÉCHEC")}");
                
                if (!state.AnalysisSuccessful)
                {
                    sb.AppendLine($"  Erreur: {state.ErrorMessage}");
                    return sb.ToString();
                }

                sb.AppendLine($"  Durée analyse: {state.AnalysisDuration.TotalMilliseconds:F1}ms");
                sb.AppendLine($"  ViewModel: {state.ViewModelItemCount} éléments");
                sb.AppendLine($"  Manager: {state.ManagerItemCount} éléments");
                sb.AppendLine($"  Synchronisées: {(state.CollectionsInSync ? "✅ OUI" : "❌ NON")}");

                // Statistiques
                if (state.Statistics != null)
                {
                    sb.AppendLine();
                    sb.AppendLine("  Statistiques:");
                    sb.AppendLine($"    Total: {state.Statistics.TotalItems}");
                    sb.AppendLine($"    Épinglés: {state.Statistics.PinnedItems}");
                    sb.AppendLine($"    Avec nom personnalisé: {state.Statistics.ItemsWithCustomName}");
                    sb.AppendLine($"    Sans aperçu: {state.Statistics.ItemsWithoutPreview}");
                    sb.AppendLine($"    Taille moyenne: {FormatBytes((long)state.Statistics.AverageItemSize)}");
                    sb.AppendLine($"    Taille totale: {FormatBytes(state.Statistics.TotalSize)}");

                    if (state.Statistics.TypeDistribution.Count > 0)
                    {
                        sb.AppendLine("    Répartition par type:");
                        foreach (var kvp in state.Statistics.TypeDistribution)
                        {
                            sb.AppendLine($"      {kvp.Key}: {kvp.Value}");
                        }
                    }
                }

                // Anomalies
                if (state.Anomalies.Count > 0)
                {
                    sb.AppendLine();
                    sb.AppendLine("  Anomalies détectées:");
                    foreach (var anomaly in state.Anomalies)
                    {
                        sb.AppendLine($"    {GetAnomalyIcon(anomaly.Type)} {anomaly.Description}");
                    }
                }

                return sb.ToString();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"❌ [FORMATTER] Erreur formatage état collections: {ex.Message}", ex);
                return $"Erreur de formatage état collections: {ex.Message}";
            }
        }

        /// <summary>
        /// Formate les détails d'un élément pour le logging.
        /// </summary>
        public string FormatItemDetails(ClipboardItem? item)
        {
            try
            {
                if (item == null)
                {
                    return "ATTENTION: L'élément est NULL";
                }

                var sb = new StringBuilder();

                sb.AppendLine("DÉTAILS DE L'ÉLÉMENT:");
                sb.AppendLine($"  ID: {item.Id}");
                sb.AppendLine($"  Type: {item.DataType}");
                sb.AppendLine($"  Épinglé: {(item.IsPinned ? "✅ OUI" : "❌ NON")}");
                sb.AppendLine($"  Timestamp: {item.Timestamp:yyyy-MM-dd HH:mm:ss.fff}");
                
                if (!string.IsNullOrEmpty(item.CustomName))
                {
                    sb.AppendLine($"  Nom personnalisé: {item.CustomName}");
                }

                if (!string.IsNullOrEmpty(item.TextPreview))
                {
                    var preview = item.TextPreview.Length > 100 
                        ? item.TextPreview.Substring(0, 100) + "..." 
                        : item.TextPreview;
                    sb.AppendLine($"  Aperçu: {preview}");
                }

                if (item.RawData != null)
                {
                    sb.AppendLine($"  Taille données: {FormatBytes(item.RawData.Length)}");
                }

                // Informations techniques
                sb.AppendLine($"  HashCode: {item.GetHashCode()}");
                sb.AppendLine($"  Type concret: {item.GetType().FullName}");

                return sb.ToString();
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [FORMATTER] Erreur formatage détails élément: {ex.Message}", ex);
                return $"Erreur de formatage détails élément: {ex.Message}";
            }
        }

        /// <summary>
        /// Formate l'en-tête du log.
        /// </summary>
        public string FormatLogHeader(DeletionResultContext context)
        {
            try
            {
                var timestamp = context.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff");
                return $"===== RÉSULTAT DE SUPPRESSION - {timestamp} =====";
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [FORMATTER] Erreur formatage en-tête: {ex.Message}", ex);
                return $"===== RÉSULTAT DE SUPPRESSION - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} =====";
            }
        }

        /// <summary>
        /// Formate le pied de page du log.
        /// </summary>
        public string FormatLogFooter(DeletionResultContext context)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                return $"===== FIN RÉSULTAT DE SUPPRESSION - {timestamp} =====";
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [FORMATTER] Erreur formatage pied de page: {ex.Message}", ex);
                return $"===== FIN RÉSULTAT DE SUPPRESSION - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} =====";
            }
        }

        /// <summary>
        /// Formate un log complet.
        /// </summary>
        public string FormatCompleteLog(
            DeletionResultContext context, 
            ComprehensiveValidationResult validation, 
            CollectionStateInfo collectionState)
        {
            try
            {
                var sb = new StringBuilder();

                // En-tête
                sb.AppendLine(FormatLogHeader(context));
                sb.AppendLine();

                // Résultat de la suppression
                sb.AppendLine(FormatDeletionResult(context));
                sb.AppendLine();

                // Détails de l'élément
                sb.AppendLine(FormatItemDetails(context.Item));
                sb.AppendLine();

                // Validation
                sb.AppendLine(FormatValidationResult(validation));
                sb.AppendLine();

                // État des collections
                sb.AppendLine(FormatCollectionState(collectionState));
                sb.AppendLine();

                // Premiers éléments de la collection
                if (collectionState.FirstTenItems.Count > 0)
                {
                    sb.AppendLine("PREMIERS ÉLÉMENTS DE LA COLLECTION:");
                    for (int i = 0; i < Math.Min(10, collectionState.FirstTenItems.Count); i++)
                    {
                        var item = collectionState.FirstTenItems[i];
                        if (item != null)
                        {
                            var preview = !string.IsNullOrEmpty(item.TextPreview) && item.TextPreview.Length > 50
                                ? item.TextPreview.Substring(0, 50) + "..."
                                : item.TextPreview ?? "[Pas d'aperçu]";
                            sb.AppendLine($"  {i + 1}. ID:{item.Id} - {item.DataType} - {preview}");
                        }
                        else
                        {
                            sb.AppendLine($"  {i + 1}. [ÉLÉMENT NULL]");
                        }
                    }
                    sb.AppendLine();
                }

                // Pied de page
                sb.AppendLine(FormatLogFooter(context));

                return sb.ToString();
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [FORMATTER] Erreur formatage log complet: {ex.Message}", ex);
                return $"Erreur de formatage log complet: {ex.Message}";
            }
        }

        #region Méthodes utilitaires

        private string FormatBytes(long bytes)
        {
            if (bytes == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:F1} {sizes[order]}";
        }

        private string GetSeverityIcon(ValidationSeverity severity)
        {
            return severity switch
            {
                ValidationSeverity.Info => "ℹ️",
                ValidationSeverity.Warning => "⚠️",
                ValidationSeverity.Error => "❌",
                ValidationSeverity.Critical => "🚨",
                _ => "❓"
            };
        }

        private string GetAnomalyIcon(AnomalyType type)
        {
            return type switch
            {
                AnomalyType.NullItem => "⚫",
                AnomalyType.DuplicateId => "🔄",
                AnomalyType.InvalidData => "❌",
                AnomalyType.OrphanedItem => "🔗",
                AnomalyType.CorruptedItem => "💥",
                _ => "❓"
            };
        }

        private string GetImpactIcon(ConsistencyImpact impact)
        {
            return impact switch
            {
                ConsistencyImpact.Low => "🟢",
                ConsistencyImpact.Medium => "🟡",
                ConsistencyImpact.High => "🟠",
                ConsistencyImpact.Critical => "🔴",
                _ => "❓"
            };
        }

        #endregion
    }
}
