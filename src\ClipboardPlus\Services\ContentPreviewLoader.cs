using System;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation du service de chargement de prévisualisation de contenu.
    /// Orchestrateur principal qui remplace la logique LoadItemContent() du ContentPreviewViewModel.
    /// Utilise le pattern Strategy via la factory pour déléguer le traitement aux handlers spécialisés.
    /// </summary>
    public class ContentPreviewLoader : IContentPreviewLoader
    {
        private readonly IContentHandlerFactory _handlerFactory;
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de ContentPreviewLoader.
        /// </summary>
        /// <param name="handlerFactory">Factory pour créer les handlers appropriés</param>
        /// <param name="loggingService">Service de logging optionnel</param>
        public ContentPreviewLoader(IContentHandlerFactory handlerFactory, ILoggingService? loggingService = null)
        {
            _handlerFactory = handlerFactory ?? throw new ArgumentNullException(nameof(handlerFactory));
            _loggingService = loggingService;
        }

        /// <inheritdoc />
        public object? LoadPreviewContent(ClipboardItem item)
        {
            ArgumentNullException.ThrowIfNull(item);

            try
            {
                _loggingService?.LogInfo($"ContentPreviewLoader.LoadPreviewContent - Début du chargement pour l'élément ID {item.Id} (type {item.DataType})");

                // Obtenir le handler approprié via la factory
                var handler = _handlerFactory.CreateHandler(item);
                
                if (handler == null)
                {
                    _loggingService?.LogWarning($"ContentPreviewLoader.LoadPreviewContent - Aucun handler disponible pour l'élément ID {item.Id} (type {item.DataType})");
                    return GetUnsupportedTypeMessage(item.DataType);
                }

                // Déléguer le traitement au handler spécialisé
                _loggingService?.LogInfo($"ContentPreviewLoader.LoadPreviewContent - Utilisation du handler {handler.GetType().Name} pour l'élément ID {item.Id}");
                
                var result = handler.HandleContent(item);
                
                _loggingService?.LogInfo($"ContentPreviewLoader.LoadPreviewContent - Chargement terminé avec succès pour l'élément ID {item.Id}");
                
                return result;
            }
            catch (ArgumentNullException)
            {
                // Re-lancer les ArgumentNullException sans les wrapper
                throw;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ContentPreviewLoader.LoadPreviewContent - Erreur lors du chargement de l'élément ID {item.Id}: {ex.Message}", ex);
                
                // En cas d'erreur, retourner un contenu de secours
                return GetErrorFallbackContent(item, ex);
            }
        }

        /// <inheritdoc />
        public bool IsDataTypeSupported(ClipboardDataType dataType)
        {
            try
            {
                bool isSupported = _handlerFactory.HasHandler(dataType);
                
                _loggingService?.LogInfo($"ContentPreviewLoader.IsDataTypeSupported - Type {dataType} supporté: {isSupported}");
                
                return isSupported;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ContentPreviewLoader.IsDataTypeSupported - Erreur lors de la vérification du support pour {dataType}: {ex.Message}", ex);
                return false;
            }
        }

        /// <inheritdoc />
        public IEnumerable<ClipboardDataType> GetSupportedDataTypes()
        {
            try
            {
                var supportedTypes = _handlerFactory.GetSupportedDataTypes().ToList();
                
                _loggingService?.LogInfo($"ContentPreviewLoader.GetSupportedDataTypes - Types supportés: {string.Join(", ", supportedTypes)}");
                
                return supportedTypes;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ContentPreviewLoader.GetSupportedDataTypes - Erreur lors de l'obtention des types supportés: {ex.Message}", ex);
                return Enumerable.Empty<ClipboardDataType>();
            }
        }

        /// <summary>
        /// Obtient le message à afficher pour un type de données non supporté.
        /// </summary>
        /// <param name="dataType">Le type de données non supporté</param>
        /// <returns>Le message approprié</returns>
        private string GetUnsupportedTypeMessage(ClipboardDataType dataType)
        {
            string message = "Prévisualisation non disponible pour ce type de contenu.";
            
            _loggingService?.LogInfo($"ContentPreviewLoader.GetUnsupportedTypeMessage - Message pour type non supporté {dataType}: '{message}'");
            
            return message;
        }

        /// <summary>
        /// Obtient le contenu de secours en cas d'erreur.
        /// </summary>
        /// <param name="item">L'élément en cours de traitement</param>
        /// <param name="exception">L'exception qui s'est produite</param>
        /// <returns>Le contenu de secours</returns>
        private object GetErrorFallbackContent(ClipboardItem item, Exception exception)
        {
            try
            {
                // Priorité 1: Utiliser TextPreview si disponible
                if (!string.IsNullOrEmpty(item.TextPreview))
                {
                    _loggingService?.LogInfo($"ContentPreviewLoader.GetErrorFallbackContent - Utilisation du TextPreview comme secours: '{item.TextPreview}'");
                    return item.TextPreview;
                }

                // Priorité 2: Obtenir le message par défaut du handler si possible
                var handler = _handlerFactory.CreateHandler(item.DataType);
                if (handler != null)
                {
                    string handlerMessage = handler.GetDefaultUnavailableMessage();
                    _loggingService?.LogInfo($"ContentPreviewLoader.GetErrorFallbackContent - Utilisation du message par défaut du handler: '{handlerMessage}'");
                    return handlerMessage;
                }

                // Priorité 3: Message générique
                string genericMessage = GetUnsupportedTypeMessage(item.DataType);
                _loggingService?.LogInfo($"ContentPreviewLoader.GetErrorFallbackContent - Utilisation du message générique: '{genericMessage}'");
                
                return genericMessage;
            }
            catch (Exception fallbackEx)
            {
                _loggingService?.LogError($"ContentPreviewLoader.GetErrorFallbackContent - Erreur lors de l'obtention du contenu de secours: {fallbackEx.Message}", fallbackEx);
                
                // Dernier recours: message fixe
                return "Erreur lors du chargement du contenu.";
            }
        }
    }
}
