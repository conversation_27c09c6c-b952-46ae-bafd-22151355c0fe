using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service de surveillance de la santé des collections ViewModel-Manager
    /// </summary>
    public interface ICollectionHealthService
    {
        /// <summary>
        /// Vérifie la santé des collections
        /// </summary>
        Task<CollectionHealthReport> CheckHealthAsync();

        /// <summary>
        /// Tente de réparer les incohérences détectées
        /// </summary>
        Task<bool> RepairInconsistenciesAsync();

        /// <summary>
        /// Événement déclenché lors de changement de santé
        /// </summary>
        event EventHandler<CollectionHealthEventArgs> HealthChanged;

        /// <summary>
        /// Démarre la surveillance automatique
        /// </summary>
        void StartMonitoring();

        /// <summary>
        /// Arrête la surveillance automatique
        /// </summary>
        void StopMonitoring();
    }

    /// <summary>
    /// Rapport de santé des collections
    /// </summary>
    public class CollectionHealthReport
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public bool IsHealthy { get; set; }
        public bool ManagerAccessible { get; set; }
        public int ManagerItemCount { get; set; }
        public int ViewModelItemCount { get; set; }
        public List<string> Issues { get; set; } = new();
        public TimeSpan CheckDuration { get; set; }
        public string OperationId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Arguments d'événement pour les changements de santé
    /// </summary>
    public class CollectionHealthEventArgs : EventArgs
    {
        public CollectionHealthReport Report { get; }
        public bool WasHealthy { get; }
        public bool IsHealthy { get; }

        public CollectionHealthEventArgs(CollectionHealthReport report, bool wasHealthy)
        {
            Report = report;
            WasHealthy = wasHealthy;
            IsHealthy = report.IsHealthy;
        }
    }
}
