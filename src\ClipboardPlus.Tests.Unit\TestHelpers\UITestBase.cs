using System;
using System.Threading;
using System.Windows;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.TestHelpers
{
    /// <summary>
    /// Classe de base pour les tests d'interface utilisateur qui nécessitent un thread STA
    /// et des ressources UI
    /// </summary>
    public class UITestBase
    {
        private static bool _applicationCreated = false;
        private static readonly object _lock = new object();

        /// <summary>
        /// Initialise l'environnement de test pour les tests d'interface utilisateur
        /// </summary>
        public static void InitializeUITestEnvironment()
        {
            if (_applicationCreated)
                return;
                
            lock (_lock)
            {
                if (_applicationCreated)
                    return;
                    
                // S'assurer que le thread est en mode STA
                if (Thread.CurrentThread.GetApartmentState() != ApartmentState.STA)
                {
                    throw new InvalidOperationException(
                        "Les tests d'interface utilisateur doivent s'exécuter dans un thread STA. " +
                        "Utilisez l'attribut [STATestMethod] au lieu de [TestMethod].");
                }
                
                // Créer une application si elle n'existe pas
                if (Application.Current == null)
                {
                    new Application();
                }
                
                // Initialiser les ressources
                TestResourceInitializer.Initialize();
                
                _applicationCreated = true;
            }
        }
        
        /// <summary>
        /// Méthode helper pour exécuter une action dans un thread STA
        /// Utilisée uniquement si nécessaire - préférer l'attribut [Apartment(ApartmentState.STA)]
        /// </summary>
        public static void RunInSTA(Action testAction)
        {
            Exception? exception = null;
            var thread = new Thread(() =>
            {
                try
                {
                    // Initialiser l'environnement de test UI
                    InitializeUITestEnvironment();

                    // Exécuter l'action de test
                    testAction();
                }
                catch (Exception ex)
                {
                    exception = ex;
                }
            });

            thread.SetApartmentState(ApartmentState.STA);
            thread.Start();
            thread.Join();

            // Re-lancer l'exception si elle s'est produite
            if (exception != null)
            {
                throw exception;
            }
        }
    }
} 