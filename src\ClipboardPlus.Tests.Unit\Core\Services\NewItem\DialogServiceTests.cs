using System;
using System.Windows;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.NewItem.Implementations;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.NewItem
{
    /// <summary>
    /// Tests unitaires pour DialogService.
    /// Vérifie le respect du principe SRP : responsabilité unique d'affichage de dialogues.
    /// </summary>
    [TestFixture]
    [Category("NewItem")]
    [Category("Dialog")]
    [Apartment(System.Threading.ApartmentState.STA)] // Requis pour les tests WPF
    public class DialogServiceTests
    {
        private DialogService _service = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _service = new DialogService(_mockLoggingService.Object);
        }

        #region Tests d'Affichage de Dialogue

        [Test]
        [Description("Valide que ShowDialog fonctionne avec une Window valide")]
        public void ShowDialog_WithValidWindow_ReturnsResult()
        {
            // Arrange
            var window = new TestWindow();

            // Act
            var result = _service.ShowDialog(window);

            // Assert
            Assert.That(result, Is.Not.Null, "ShowDialog devrait retourner un résultat");
            Assert.That(result, Is.False, "ShowDialog devrait retourner false pour une fenêtre fermée sans résultat");
        }

        [Test]
        [Description("Valide que ShowDialog génère les logs appropriés")]
        public void ShowDialog_WithValidWindow_GeneratesLogs()
        {
            // Arrange
            var window = new TestWindow();

            // Act
            _service.ShowDialog(window);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Affichage du dialogue") && s.Contains("TestWindow"))),
                Times.Once,
                "Un log d'affichage devrait être généré");

            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Dialogue fermé avec résultat"))),
                Times.Once,
                "Un log de fermeture devrait être généré");
        }

        [Test]
        [Description("Valide que ShowDialog lève une exception avec un objet non-Window")]
        public void ShowDialog_WithNonWindow_ThrowsArgumentException()
        {
            // Arrange
            var nonWindow = new object();

            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => _service.ShowDialog(nonWindow),
                "ShowDialog devrait lever ArgumentException avec un objet non-Window");

            Assert.That(exception.ParamName, Is.EqualTo("dialog"),
                "Le nom du paramètre dans l'exception devrait être 'dialog'");
            Assert.That(exception.Message, Contains.Substring("Le dialogue doit être une Window"),
                "Le message d'exception devrait indiquer que le dialogue doit être une Window");
        }

        [Test]
        [Description("Valide que ShowDialog lève une exception avec null")]
        public void ShowDialog_WithNull_ThrowsArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => _service.ShowDialog(null!),
                "ShowDialog devrait lever ArgumentException avec null");

            Assert.That(exception.ParamName, Is.EqualTo("dialog"),
                "Le nom du paramètre dans l'exception devrait être 'dialog'");
        }

        #endregion

        #region Tests de Gestion d'Erreurs

        [Test]
        [Description("Valide que ShowDialog logue les erreurs pour les objets non-Window")]
        public void ShowDialog_WithNonWindow_LogsError()
        {
            // Arrange
            var nonWindow = new object();

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _service.ShowDialog(nonWindow));

            // Note: Le logging d'erreur est vérifié implicitement par le fait que l'exception est levée
            // après que l'erreur ait été loggée dans l'implémentation
        }

        #endregion

        #region Tests de Robustesse

        [Test]
        [Description("Valide que le service fonctionne sans service de logging")]
        public void DialogService_WithoutLoggingService_WorksCorrectly()
        {
            // Arrange
            var serviceWithoutLogging = new DialogService(null);
            var window = new TestWindow();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = serviceWithoutLogging.ShowDialog(window);
                Assert.That(result, Is.Not.Null);
            }, "DialogService devrait fonctionner sans service de logging");
        }

        [Test]
        [Description("Valide que le service peut être créé sans paramètres")]
        public void Constructor_WithoutParameters_CreatesValidInstance()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var service = new DialogService();
                var window = new TestWindow();
                var result = service.ShowDialog(window);
                Assert.That(result, Is.Not.Null);
            }, "DialogService devrait pouvoir être créé sans paramètres");
        }

        #endregion

        #region Tests de Comportement Spécifique

        [Test]
        [Description("Valide que ShowDialog peut être appelée plusieurs fois")]
        public void ShowDialog_CalledMultipleTimes_WorksCorrectly()
        {
            // Arrange
            var window1 = new TestWindow();
            var window2 = new TestWindow();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result1 = _service.ShowDialog(window1);
                var result2 = _service.ShowDialog(window2);
                
                Assert.That(result1, Is.Not.Null);
                Assert.That(result2, Is.Not.Null);
            }, "ShowDialog devrait pouvoir être appelée plusieurs fois");
        }

        [Test]
        [Description("Valide que ShowDialog fonctionne avec différents types de Window")]
        public void ShowDialog_WithDifferentWindowTypes_WorksCorrectly()
        {
            // Arrange
            var basicWindow = new Window();
            var testWindow = new TestWindow();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result1 = _service.ShowDialog(basicWindow);
                var result2 = _service.ShowDialog(testWindow);
                
                Assert.That(result1, Is.Not.Null);
                Assert.That(result2, Is.Not.Null);
            }, "ShowDialog devrait fonctionner avec différents types de Window");
        }

        #endregion

        #region Tests de Conformité Interface

        [Test]
        [Description("Valide que DialogService implémente correctement IDialogService")]
        public void DialogService_ImplementsInterface_Correctly()
        {
            // Act & Assert
            Assert.That(_service, Is.InstanceOf<ClipboardPlus.Core.Services.NewItem.Interfaces.IDialogService>(),
                "DialogService devrait implémenter IDialogService");
        }

        #endregion

        #region Tests de Logging Détaillé

        [Test]
        [Description("Valide que les logs contiennent le nom correct du type de dialogue")]
        public void ShowDialog_LogsCorrectDialogTypeName()
        {
            // Arrange
            var window = new TestWindow();

            // Act
            _service.ShowDialog(window);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("TestWindow"))),
                Times.Once,
                "Le log devrait contenir le nom correct du type de dialogue");
        }

        [Test]
        [Description("Valide que les logs de résultat sont corrects")]
        public void ShowDialog_LogsCorrectResult()
        {
            // Arrange
            var window = new TestWindow();

            // Act
            var result = _service.ShowDialog(window);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains($"Dialogue fermé avec résultat: {result}"))),
                Times.Once,
                "Le log devrait contenir le résultat correct du dialogue");
        }

        #endregion
    }

    /// <summary>
    /// Classe de test pour simuler une Window dans les tests.
    /// </summary>
    public class TestWindow : Window
    {
        public TestWindow()
        {
            // Configuration minimale pour les tests
            Width = 100;
            Height = 100;
            WindowStartupLocation = WindowStartupLocation.Manual;
            Left = -1000; // Hors écran pour éviter l'affichage visuel
            Top = -1000;
            ShowInTaskbar = false;
            WindowStyle = WindowStyle.None;
        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            // Ne pas appeler base.OnSourceInitialized pour éviter l'affichage
            // base.OnSourceInitialized(e);

            // Fermer immédiatement pour les tests
            DialogResult = false;
            Close();
        }
    }
}
