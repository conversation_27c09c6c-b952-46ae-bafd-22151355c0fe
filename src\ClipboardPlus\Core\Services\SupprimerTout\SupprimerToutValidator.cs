using System;
using System.Collections.Generic;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.SupprimerTout
{
    /// <summary>
    /// Validateur pour les opérations de suppression de tous les éléments.
    /// Extrait de la méthode SupprimerTout originale pour séparer les responsabilités.
    /// </summary>
    public class SupprimerToutValidator
    {
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du validateur.
        /// </summary>
        /// <param name="loggingService">Service de logging pour tracer les validations.</param>
        public SupprimerToutValidator(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Valide une requête de suppression de tous les éléments.
        /// Correspond aux vérifications des chemins 1 et 2 de l'analyse.
        /// </summary>
        /// <param name="request">Requête à valider.</param>
        /// <returns>Résultat de la validation avec détails des erreurs.</returns>
        public SupprimerToutValidationResult ValidateRequest(SupprimerToutRequest request)
        {
            if (request == null)
            {
                var error = "La requête ne peut pas être null";
                _loggingService?.LogError($"[SupprimerToutValidator] {error}");
                return SupprimerToutValidationResult.Failure(error);
            }

            _loggingService?.LogInfo($"[SupprimerToutValidator] Validation de la requête {request.OperationId}");

            // Validation de base de la requête
            var basicValidation = request.Validate();
            if (!basicValidation.IsValid)
            {
                _loggingService?.LogError($"[SupprimerToutValidator] Validation de base échouée: {basicValidation.ErrorMessage}");
                return basicValidation;
            }

            // Validation spécifique à l'opération
            var operationValidation = ValidateOperationPreconditions(request);
            if (!operationValidation.IsValid)
            {
                _loggingService?.LogWarning($"[SupprimerToutValidator] Préconditions non remplies: {operationValidation.ErrorMessage}");
                return operationValidation;
            }

            _loggingService?.LogInfo($"[SupprimerToutValidator] Validation réussie pour {request.OperationId}");
            return SupprimerToutValidationResult.Success;
        }

        /// <summary>
        /// Valide les préconditions spécifiques à l'opération de suppression.
        /// Correspond aux chemins d'exécution 1 et 2 de l'analyse originale.
        /// </summary>
        /// <param name="request">Requête à valider.</param>
        /// <returns>Résultat de la validation des préconditions.</returns>
        private SupprimerToutValidationResult ValidateOperationPreconditions(SupprimerToutRequest request)
        {
            var errors = new List<string>();

            // CHEMIN 1 : Vérifier qu'aucune opération n'est déjà en cours
            if (IsOperationInProgress(request.ViewModel))
            {
                var error = "Une opération est déjà en cours";
                errors.Add(error);
                _loggingService?.LogWarning($"[{request.OperationId}] SupprimerToutValidator: {error}");
            }

            // CHEMIN 2 : Vérifier que le gestionnaire d'historique est disponible
            if (!IsHistoryManagerAvailable(request.ViewModel))
            {
                var error = "Le service d'historique n'est pas disponible";
                errors.Add(error);
                _loggingService?.LogError($"[{request.OperationId}] SupprimerToutValidator: {error}");
            }

            return errors.Count == 0 ? SupprimerToutValidationResult.Success : SupprimerToutValidationResult.Failure(errors);
        }

        /// <summary>
        /// Vérifie si une opération est déjà en cours sur le ViewModel.
        /// </summary>
        /// <param name="viewModel">ViewModel à vérifier.</param>
        /// <returns>True si une opération est en cours, false sinon.</returns>
        private static bool IsOperationInProgress(ClipboardHistoryViewModel viewModel)
        {
            return viewModel?.IsOperationInProgress ?? false;
        }

        /// <summary>
        /// Vérifie si le gestionnaire d'historique est disponible.
        /// CORRECTION: Utilise la méthode publique GetClipboardHistoryManager() au lieu de la réflexion.
        /// </summary>
        /// <param name="viewModel">ViewModel à vérifier.</param>
        /// <returns>True si le gestionnaire est disponible, false sinon.</returns>
        private bool IsHistoryManagerAvailable(ClipboardHistoryViewModel viewModel)
        {
            if (viewModel == null)
                return false;

            try
            {
                // CORRECTION: Utilisation de la méthode publique au lieu de la réflexion
                var historyManager = viewModel.GetClipboardHistoryManager();
                var isAvailable = historyManager != null;

                _loggingService?.LogInfo($"[SupprimerToutValidator] ClipboardHistoryManager disponible: {isAvailable}");
                return isAvailable;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[SupprimerToutValidator] Erreur lors de la vérification du gestionnaire d'historique: {ex.Message}", ex);
                return false;
            }
        }


    }
}
