using System;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.Windows;
using WpfMessageBox = System.Windows.MessageBox;
using WpfApplication = System.Windows.Application;
using WpfClipboard = System.Windows.Clipboard;
using System.Windows.Media;
using System.Runtime.InteropServices;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System.Windows.Threading;
using System.Windows.Input;
using System.Windows.Forms;
using WinFormsApplication = System.Windows.Forms.Application;
using System.Diagnostics;
using ClipboardPlus.Core.Extensions;
using ClipboardPlus.Diagnostics;
using System.Windows.Interop;
using ClipboardPlus.Utils;
using System.Linq;
using System.Collections.Generic;
using System.Windows.Media.Imaging;
using System.Text;
using ClipboardPlus.Services.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Services;

namespace ClipboardPlus
{
    /// <summary>
    /// Logique d'interaction pour App.xaml
    /// </summary>
    public partial class App : System.Windows.Application
    {
        private const string APP_NAME = "ClipboardPlus";
        private const string APP_MUTEX_NAME = "ClipboardPlusApplicationMutex";
        private ILoggingService? logger;

        /// <summary>
        /// Fournisseur de services pour l'injection de dépendances.
        /// </summary>
        public IServiceProvider? Services { get; private set; }

        public App()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Configurer les services pour l'injection de dépendances.
        /// </summary>
        private void ConfigureServices()
        {
            Services = HostConfiguration.ConfigureServices();
        }

        /// <summary>
        /// Valide que les services requis pour le démarrage sont disponibles.
        /// </summary>
        /// <param name="lifetimeManager">Service de gestion du cycle de vie de l'application</param>
        /// <param name="startupLogic">Service de logique de démarrage</param>
        /// <returns>True si tous les services sont disponibles, False sinon</returns>
        private bool ValidateRequiredServices(out IApplicationLifetimeManager? lifetimeManager, out IStartupLogic? startupLogic)
        {
            // Validation IApplicationLifetimeManager (reproduction exacte lignes 75-81)
            lifetimeManager = Services?.GetService<IApplicationLifetimeManager>();
            if (lifetimeManager == null)
            {
                logger?.LogCritical("Application_Startup: Le service IApplicationLifetimeManager n'a pas pu être résolu. Arrêt.");
                Current.Shutdown(1);
                throw new InvalidOperationException("IApplicationLifetimeManager non résolu");
            }
            logger?.LogInfo("Application_Startup: IApplicationLifetimeManager résolu avec succès.");

            // Validation IStartupLogic (reproduction exacte lignes 84-90)
            startupLogic = Services?.GetService<IStartupLogic>();
            if (startupLogic == null)
            {
                logger?.LogCritical("Application_Startup: IStartupLogic non résolu !");
                Current.Shutdown(1);
                return false; // Note: return au lieu de throw pour cette validation
            }

            return true;
        }

        /// <summary>
        /// Traite le résultat du démarrage de l'application.
        /// </summary>
        /// <param name="status">Statut retourné par StartupLogic</param>
        /// <param name="trayService">Service de barre système (optionnel)</param>
        private void HandleStartupResult(StartupStatus status, ISystemTrayService? trayService)
        {
            // Reproduction exacte de la logique lignes 92-103
            if (status == StartupStatus.Success)
            {
                // Optionnel : _systemTrayService = trayService;
                // Démarrage réussi, rien de spécial à faire
            }
            else if (status == StartupStatus.ShutdownRequested)
            {
                logger?.LogInfo("Application_Startup: Arrêt demandé par StartupLogic.");
                Current.Shutdown(0);
            }
            else // StartupStatus.CriticalError ou autre
            {
                logger?.LogCritical("Application_Startup: Erreur critique lors du démarrage.");
                Current.Shutdown(1);
            }
        }

        /// <summary>
        /// Centralise le logging des phases de démarrage avec un ID d'opération.
        /// </summary>
        /// <param name="phase">Phase du démarrage (DÉBUT, FIN, etc.)</param>
        /// <param name="operationId">ID d'opération optionnel</param>
        private void LogStartupPhase(string phase, string? operationId = null)
        {
            var id = operationId ?? Guid.NewGuid().ToString().Substring(0, 8);
            logger?.LogInfo($"================= Application_Startup: {phase} =================");
        }

        /// <summary>
        /// Gestionnaire de l'événement de démarrage de l'application
        /// </summary>
        private async void Application_Startup(object sender, StartupEventArgs e)
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);

            // S'assurer que les services sont configurés AVANT toute résolution
            if (Services == null)
            {
                ConfigureServices();
            }
            logger = Services?.GetService<ILoggingService>();

            LogStartupPhase("DÉBUT", operationId);
            try
            {
                // Validation des services requis (extraction des lignes 75-90)
                if (!ValidateRequiredServices(out var lifetimeManager, out var startupLogic))
                    return;

                if (startupLogic == null)
                    return;

                var (status, trayService) = await startupLogic.ExecuteAsync(e.Args, ShortcutService_ShortcutActivated, ClipboardListener_ClipboardContentChanged);

                // Traitement du résultat (extraction des lignes 92-103)
                if (trayService != null)
                {
                    HandleStartupResult(status, trayService);
                }

                LogStartupPhase("FIN", operationId);
            }
            catch (Exception ex)
            {
                logger?.LogCritical($"[{operationId}] Application_Startup: Exception non gérée dans Application_Startup: {ex.Message}", ex);
                var exceptionManager = Services?.GetService<IGlobalExceptionManager>();
                exceptionManager?.LogUnhandledException("UI-Thread-Startup", ex, true);
                Current.Shutdown(1);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            var loggingService = Services?.GetService<ILoggingService>();
            loggingService?.LogInfo($"[{operationId}] OnExit: Début de la fermeture de l'application");

            try
            {
                // Délégation DIRECTE vers le service spécialisé
                var exitService = Services?.GetService<IApplicationExitService>();
                exitService?.ExecuteExitSequenceAsync(Services).GetAwaiter().GetResult();

                loggingService?.LogInfo($"[{operationId}] OnExit: Séquence de fermeture terminée avec succès");
            }
            catch (Exception ex)
            {
                loggingService?.LogCritical($"[{operationId}] OnExit: Erreur critique lors de la fermeture: {ex.Message}", ex);
            }
            finally
            {
                loggingService?.ForceFlush();
            }

            base.OnExit(e);

            // Forcer la fermeture de l'application si nécessaire
            Environment.Exit(0);
        }

        /// <summary>
        /// Méthode appelée au démarrage de l'application
        /// </summary>
        protected override void OnStartup(StartupEventArgs e)
        {
            // IMPORTANT : Le service de log est configuré dans Application_Startup,
            // mais nous utilisons un logger temporaire ici si nécessaire.
            var tempLogger = Services?.GetService<ILoggingService>();
            tempLogger?.LogInfo("================= OnStartup: DÉBUT =================");

            if(Services == null)
            {
                // Les services seront configurés dans Application_Startup
                tempLogger?.LogInfo("OnStartup: Le conteneur de services sera configuré dans Application_Startup.");
            }
            else 
            {
                tempLogger?.LogInfo("OnStartup: Le conteneur de services est déjà configuré.");
                
                // Initialiser le gestionnaire global d'exceptions
                tempLogger?.LogInfo("OnStartup: Initialisation de IGlobalExceptionManager.");
                var exceptionManager = Services.GetService<IGlobalExceptionManager>();
                exceptionManager?.Initialize();
                tempLogger?.LogInfo("OnStartup: IGlobalExceptionManager initialisé.");

                tempLogger?.LogInfo("OnStartup: Vérification de IApplicationLifetimeManager.");
                var lifetimeManager = Services.GetService<IApplicationLifetimeManager>();
                if (lifetimeManager == null)
                {
                    tempLogger?.LogWarning("OnStartup: IApplicationLifetimeManager est NULL.");
                }
            }
            
            tempLogger?.LogInfo("OnStartup: Appel de base.OnStartup(e).");
            base.OnStartup(e);
            tempLogger?.LogInfo("================= OnStartup: FIN =================");
        }
        
        private void ShowHistoryWindowFromService()
        {
            var systemTrayService = Services?.GetService<ISystemTrayService>();
            Dispatcher.BeginInvoke(new Action(async () =>
            {
                if (systemTrayService != null)
                {
                    await systemTrayService.ShowHistoryWindow();
                }
            }));
        }
        
        private async void ShortcutService_ShortcutActivated(object? sender, EventArgs e)
        {
            var loggingService = Services?.GetService<ILoggingService>();
            var systemTrayService = Services?.GetService<ISystemTrayService>();
            loggingService?.LogInfo("ShortcutService_ShortcutActivated: Raccourci global activé");
            try
            {
                if (systemTrayService != null)
                {
                    loggingService?.LogInfo("ShortcutService_ShortcutActivated: Affichage de la fenêtre d'historique");
                    await systemTrayService.ShowHistoryWindow();
                }
                else
                {
                    loggingService?.LogWarning("ShortcutService_ShortcutActivated: SystemTrayService n'est pas disponible");
                }
            }
            catch (Exception ex)
            {
                loggingService?.LogError($"ShortcutService_ShortcutActivated: Erreur lors de l'affichage de la fenêtre d'historique: {ex.Message}", ex);
                var exceptionManager = Services?.GetService<IGlobalExceptionManager>();
                if (exceptionManager != null)
                {
                    exceptionManager.LogUnhandledException("ShortcutActivated", ex, false);
                }
            }
        }
        
        /// <summary>
        /// Gère l'événement de changement du contenu du presse-papiers.
        /// Cette méthode délègue maintenant tout le traitement au service ClipboardEventHandler
        /// pour respecter les principes SOLID et MVVM.
        /// </summary>
        /// <param name="sender">L'objet qui a déclenché l'événement</param>
        /// <param name="e">Les arguments de l'événement</param>
        private async void ClipboardListener_ClipboardContentChanged(object? sender, EventArgs e)
        {
            try
            {
                // Délégation complète vers le service dédié
                var handler = Services?.GetService<IClipboardEventHandler>();
                if (handler != null)
                {
                    await handler.HandleClipboardContentChangedAsync(sender, e);
                }
                else
                {
                    // Fallback critique si le service n'est pas disponible
                    var loggingService = Services?.GetService<ILoggingService>();
                    loggingService?.LogCritical("ClipboardListener_ClipboardContentChanged: ClipboardEventHandler service not available - CRITICAL");
                }
            }
            catch (Exception ex)
            {
                // Gestion d'erreurs de dernier recours pour la méthode App elle-même
                var loggingService = Services?.GetService<ILoggingService>();
                var exceptionManager = Services?.GetService<IGlobalExceptionManager>();

                loggingService?.LogCritical($"ClipboardListener_ClipboardContentChanged: Critical error in clipboard event handling: {ex.Message}", ex);
                exceptionManager?.LogUnhandledException("ClipboardEventHandling", ex, true);
            }
        }
    }
} 