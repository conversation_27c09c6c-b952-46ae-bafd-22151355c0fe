using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Interfaces
{
    /// <summary>
    /// Interface pour la journalisation des opérations.
    /// Responsabilité unique : Enregistrer les logs des opérations.
    /// </summary>
    public interface IOperationLogger
    {
        /// <summary>
        /// Démarre une nouvelle opération et retourne un contexte de logging.
        /// </summary>
        /// <param name="operationName">Nom de l'opération</param>
        /// <param name="item">Élément concerné par l'opération</param>
        /// <returns>Contexte de logging pour cette opération</returns>
        IOperationContext StartOperation(string operationName, ClipboardItem? item = null);
    }

    /// <summary>
    /// Interface pour le contexte d'une opération en cours.
    /// </summary>
    public interface IOperationContext : IDisposable
    {
        /// <summary>
        /// ID unique de l'opération.
        /// </summary>
        string OperationId { get; }

        /// <summary>
        /// Enregistre une information pendant l'opération.
        /// </summary>
        /// <param name="message">Message à enregistrer</param>
        void LogInfo(string message);

        /// <summary>
        /// Enregistre un avertissement pendant l'opération.
        /// </summary>
        /// <param name="message">Message d'avertissement</param>
        void LogWarning(string message);

        /// <summary>
        /// Enregistre une erreur pendant l'opération.
        /// </summary>
        /// <param name="message">Message d'erreur</param>
        /// <param name="exception">Exception optionnelle</param>
        void LogError(string message, Exception? exception = null);

        /// <summary>
        /// Marque l'opération comme terminée avec succès.
        /// </summary>
        /// <param name="result">Résultat de l'opération</param>
        void Complete(object? result = null);

        /// <summary>
        /// Marque l'opération comme échouée.
        /// </summary>
        /// <param name="error">Erreur qui a causé l'échec</param>
        void Fail(string error);
    }
}
