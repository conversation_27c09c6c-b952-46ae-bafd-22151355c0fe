using System;
using System.Runtime.InteropServices;

namespace ClipboardPlus.Native
{
    /// <summary>
    /// Fournit des méthodes d'interopérabilité natives pour l'interaction avec l'API Windows.
    /// </summary>
    internal static class NativeMethods
    {
        // Constantes pour les codes de touches virtuelles
        public const uint KEYEVENTF_EXTENDEDKEY = 0x0001;
        public const uint KEYEVENTF_KEYUP = 0x0002;
        public const int VK_CONTROL = 0x11;
        public const int VK_V = 0x56;

        /// <summary>
        /// Types d'entrée pour la fonction SendInput.
        /// </summary>
        public enum InputType : uint
        {
            Mouse = 0,
            Keyboard = 1,
            Hardware = 2
        }

        /// <summary>
        /// Structure représentant une entrée clavier pour SendInput.
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct KEYBDINPUT
        {
            public ushort wVk;
            public ushort wScan;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        /// <summary>
        /// Structure représentant une entrée de souris pour SendInput.
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct MOUSEINPUT
        {
            public int dx;
            public int dy;
            public uint mouseData;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        /// <summary>
        /// Structure représentant une entrée matérielle pour SendInput.
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct HARDWAREINPUT
        {
            public uint uMsg;
            public ushort wParamL;
            public ushort wParamH;
        }

        /// <summary>
        /// Structure d'union pour les différents types d'entrée.
        /// </summary>
        [StructLayout(LayoutKind.Explicit)]
        public struct InputUnion
        {
            [FieldOffset(0)]
            public MOUSEINPUT mi;
            [FieldOffset(0)]
            public KEYBDINPUT ki;
            [FieldOffset(0)]
            public HARDWAREINPUT hi;
        }

        /// <summary>
        /// Structure représentant une entrée pour SendInput.
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct INPUT
        {
            public InputType type;
            public InputUnion u;
        }

        /// <summary>
        /// Synthétise des événements de frappe de touche, de mouvement de souris et d'entrée de bouton.
        /// </summary>
        /// <param name="nInputs">Nombre d'éléments dans le tableau pInputs.</param>
        /// <param name="pInputs">Tableau de structures INPUT.</param>
        /// <param name="cbSize">Taille en octets de la structure INPUT.</param>
        /// <returns>Le nombre d'événements envoyés avec succès.</returns>
        [DllImport("user32.dll", SetLastError = true)]
        public static extern uint SendInput(uint nInputs, [MarshalAs(UnmanagedType.LPArray), In] INPUT[] pInputs, int cbSize);

        /// <summary>
        /// Récupère un handle vers la fenêtre de premier plan (la fenêtre avec laquelle l'utilisateur travaille actuellement).
        /// </summary>
        /// <returns>Handle de la fenêtre de premier plan.</returns>
        [DllImport("user32.dll")]
        public static extern IntPtr GetForegroundWindow();

        /// <summary>
        /// Définit la fenêtre de premier plan.
        /// </summary>
        /// <param name="hWnd">Handle de la fenêtre à mettre au premier plan.</param>
        /// <returns>True si la fonction réussit, sinon false.</returns>
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool SetForegroundWindow(IntPtr hWnd);
    }
} 