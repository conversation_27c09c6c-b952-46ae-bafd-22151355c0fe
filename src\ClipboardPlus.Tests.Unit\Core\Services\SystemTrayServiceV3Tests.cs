using NUnit.Framework;
using Moq;
using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;
using ClipboardPlus.Core.Services.Windows;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests pour la méthode CreateOrchestratorV2 de SystemTrayService qui utilise
    /// le constructeur V2 de SystemTrayOrchestrator (sans Service Locator).
    /// </summary>
    [TestFixture]
    public class SystemTrayServiceV3Tests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private Mock<IServiceProvider> _mockServiceProvider;
        private Mock<ISystemTrayOrchestrator> _mockOrchestrator;
        private SystemTrayService _systemTrayService;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockOrchestrator = new Mock<ISystemTrayOrchestrator>();

            // Configuration des services mockés pour le constructeur V2 de SystemTrayOrchestrator
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IThreadValidator)))
                .Returns(new Mock<IThreadValidator>().Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(INotifyIconCleanupService)))
                .Returns(new Mock<INotifyIconCleanupService>().Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(INotifyIconFactory)))
                .Returns(new Mock<INotifyIconFactory>().Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IIconResourceLoader)))
                .Returns(new Mock<IIconResourceLoader>().Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IContextMenuBuilder)))
                .Returns(new Mock<IContextMenuBuilder>().Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IVisibilityManager)))
                .Returns(new Mock<IVisibilityManager>().Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IStartupNotificationService)))
                .Returns(new Mock<IStartupNotificationService>().Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(IHistoryWindowService)))
                .Returns(new Mock<IHistoryWindowService>().Object);
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ISettingsWindowService)))
                .Returns(new Mock<ISettingsWindowService>().Object);

            _systemTrayService = new SystemTrayService(_mockServiceProvider.Object);
        }

        [Test]
        public void CreateOrchestratorV2_CreatesOrchestratorWithoutServiceProvider()
        {
            // Act
            var orchestrator = _systemTrayService.CreateOrchestratorV2();

            // Assert
            Assert.IsNotNull(orchestrator);
            _mockLoggingService.Verify(
                x => x.LogInfo("SystemTrayService: Début de la création de l'orchestrateur V2 (sans Service Locator)"),
                Times.Once);
            _mockLoggingService.Verify(
                x => x.LogInfo("SystemTrayService: Orchestrateur V2 créé avec succès (sans Service Locator)"),
                Times.Once);
        }

        [Test]
        public void CreateOrchestratorV2_LogsCorrectMessages()
        {
            // Act
            var orchestrator = _systemTrayService.CreateOrchestratorV2();

            // Assert - Vérifier que l'orchestrateur a été créé et que les logs ont été appelés
            Assert.IsNotNull(orchestrator);

            // Vérifier que les logs ont été appelés correctement
            _mockLoggingService.Verify(
                x => x.LogInfo("SystemTrayService: Début de la création de l'orchestrateur V2 (sans Service Locator)"),
                Times.Once);
            _mockLoggingService.Verify(
                x => x.LogInfo("SystemTrayService: Orchestrateur V2 créé avec succès (sans Service Locator)"),
                Times.Once);
        }

        [Test]
        public void CreateOrchestratorV2_DoesNotPassServiceProviderToOrchestrator()
        {
            // Cette méthode teste indirectement que le constructeur V2 est utilisé
            // car il ne prend pas IServiceProvider en paramètre

            // Act & Assert - Si le constructeur V2 est utilisé, aucune exception ne devrait être levée
            SystemTrayOrchestrator orchestrator = null!;
            Assert.DoesNotThrow(() => orchestrator = _systemTrayService.CreateOrchestratorV2());

            // Vérifier que l'orchestrateur a été créé
            Assert.IsNotNull(orchestrator);
            _mockLoggingService.Verify(
                x => x.LogInfo("SystemTrayService: Orchestrateur V2 créé avec succès (sans Service Locator)"),
                Times.Once);
        }
    }
}
