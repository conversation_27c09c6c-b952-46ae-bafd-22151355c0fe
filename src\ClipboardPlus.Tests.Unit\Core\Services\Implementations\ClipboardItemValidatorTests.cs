using System;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Implementations;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.Implementations
{
    [TestFixture]
    public class ClipboardItemValidatorTests
    {
        private ClipboardItemValidator _validator = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private const long DefaultMaxSize = 10 * 1024 * 1024; // 10 MB

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _validator = new ClipboardItemValidator(_mockLoggingService.Object);
        }

        #region Tests de Validation Null

        [Test]
        public async Task ValidateAsync_WithNullItem_ReturnsFailure()
        {
            // Act
            var result = await _validator.ValidateAsync(null!, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.ErrorMessage, Is.EqualTo("L'élément fourni est null."));
            Assert.That(result.ErrorCode, Is.EqualTo("NULL_ITEM"));
        }

        #endregion

        #region Tests de Validation de Taille

        [Test]
        public async Task ValidateAsync_WithValidSize_ReturnsSuccess()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = new byte[1024], // 1 KB
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.True);
        }

        [Test]
        public async Task ValidateAsync_WithOversizedItem_ReturnsFailure()
        {
            // Arrange
            var oversizedData = new byte[11 * 1024 * 1024]; // 11 MB > 10 MB
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = oversizedData,
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.ErrorMessage, Does.Contain("taille maximale autorisée"));
            Assert.That(result.ErrorCode, Is.EqualTo("SIZE_EXCEEDED"));
        }

        [Test]
        public async Task ValidateAsync_WithNullRawData_ReturnsSuccess()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = null,
                TextPreview = "Test content",
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.True);
        }

        #endregion

        #region Tests de Validation de Type

        [Test]
        public async Task ValidateAsync_WithValidDataType_ReturnsSuccess()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Image,
                RawData = new byte[1024],
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.True);
        }

        [Test]
        public async Task ValidateAsync_WithInvalidDataType_ReturnsFailure()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = (ClipboardDataType)999, // Type invalide
                RawData = new byte[1024],
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.ErrorMessage, Does.Contain("Type de données non supporté"));
            Assert.That(result.ErrorCode, Is.EqualTo("INVALID_DATA_TYPE"));
        }

        #endregion

        #region Tests de Validation de Cohérence

        [Test]
        public async Task ValidateAsync_TextWithRawData_ReturnsSuccess()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.True);
        }

        [Test]
        public async Task ValidateAsync_TextWithTextPreview_ReturnsSuccess()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = null,
                TextPreview = "Test content",
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.True);
        }

        [Test]
        public async Task ValidateAsync_TextWithoutDataOrPreview_ReturnsFailure()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = null,
                TextPreview = null,
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.ErrorMessage, Does.Contain("doit avoir des données brutes ou un aperçu textuel"));
            Assert.That(result.ErrorCode, Is.EqualTo("INCONSISTENT_TEXT_DATA"));
        }

        [Test]
        public async Task ValidateAsync_NonTextWithoutRawData_ReturnsFailure()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Image,
                RawData = null,
                Timestamp = DateTime.Now
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.ErrorMessage, Does.Contain("doit avoir des données brutes"));
            Assert.That(result.ErrorCode, Is.EqualTo("MISSING_RAW_DATA"));
        }

        [Test]
        public async Task ValidateAsync_WithFutureTimestamp_ReturnsFailure()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test"),
                Timestamp = DateTime.Now.AddHours(1) // Dans le futur
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.False);
            Assert.That(result.ErrorMessage, Does.Contain("ne peut pas être dans le futur"));
            Assert.That(result.ErrorCode, Is.EqualTo("FUTURE_TIMESTAMP"));
        }

        [Test]
        public async Task ValidateAsync_WithRecentTimestamp_ReturnsSuccess()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test"),
                Timestamp = DateTime.Now.AddSeconds(30) // Légèrement dans le futur mais dans la tolérance
            };

            // Act
            var result = await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            Assert.That(result.IsValid, Is.True);
        }

        #endregion

        #region Tests de Logging

        [Test]
        public async Task ValidateAsync_LogsOperationStart()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test"),
                Timestamp = DateTime.Now
            };

            // Act
            await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("ClipboardItemValidator.ValidateAsync - Début validation"))),
                Times.Once);
        }

        [Test]
        public async Task ValidateAsync_LogsSuccessfulValidation()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test"),
                Timestamp = DateTime.Now
            };

            // Act
            await _validator.ValidateAsync(item, DefaultMaxSize);

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Validation réussie"))),
                Times.Once);
        }

        #endregion
    }
}
