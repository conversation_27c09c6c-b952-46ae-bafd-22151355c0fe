#!/usr/bin/env powershell
# Script pour exécuter tous les tests et générer un rapport de couverture complet

Write-Host "=== EXECUTION DES TESTS AVEC RAPPORT DE COUVERTURE ===" -ForegroundColor Cyan

# Nettoyer les anciens rapports
if (Test-Path -Path "coverage-report") {
    Write-Host "Suppression de l'ancien rapport de couverture..." -ForegroundColor Yellow
    Remove-Item -Path "coverage-report" -Recurse -Force
}

if (Test-Path -Path "coverage.cobertura.xml") {
    Write-Host "Suppression de l'ancien fichier XML de couverture..." -ForegroundColor Yellow
    Remove-Item -Path "coverage.cobertura.xml" -Force
}



# Vérifier si ReportGenerator est installé
$reportGeneratorInstalled = $null
try {
    $reportGeneratorInstalled = Get-Command reportgenerator -ErrorAction SilentlyContinue
} catch {
    # La commande n'est pas disponible
}

if (-not $reportGeneratorInstalled) {
    Write-Host "Installation de l'outil ReportGenerator..." -ForegroundColor Yellow
    dotnet tool install -g dotnet-reportgenerator-globaltool

    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Impossible d'installer ReportGenerator. Veuillez l'installer manuellement:" -ForegroundColor Red
        Write-Host "   dotnet tool install -g dotnet-reportgenerator-globaltool" -ForegroundColor Red
        exit $LASTEXITCODE
    }
}

# Vérifier si coverlet est installé
$coverletInstalled = $null
try {
    $coverletInstalled = dotnet tool list -g | Select-String "coverlet.console"
} catch {
    # La commande n'est pas disponible
}

if (-not $coverletInstalled) {
    Write-Host "Installation de l'outil Coverlet..." -ForegroundColor Yellow
    dotnet tool install -g coverlet.console

    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Impossible d'installer Coverlet. Veuillez l'installer manuellement:" -ForegroundColor Red
        Write-Host "   dotnet tool install -g coverlet.console" -ForegroundColor Red
        exit $LASTEXITCODE
    }
}

# Créer les dossiers pour les résultats des tests
if (-not (Test-Path -Path "./TestResults/Unit")) {
    New-Item -Path "./TestResults/Unit" -ItemType Directory -Force | Out-Null
}

if (-not (Test-Path -Path "./TestResults/Unit-Problematic")) {
    New-Item -Path "./TestResults/Unit-Problematic" -ItemType Directory -Force | Out-Null
}

if (-not (Test-Path -Path "./TestResults/Integration")) {
    New-Item -Path "./TestResults/Integration" -ItemType Directory -Force | Out-Null
}

if (-not (Test-Path -Path "./TestResults/STA-Normal")) {
    New-Item -Path "./TestResults/STA-Normal" -ItemType Directory -Force | Out-Null
}

if (-not (Test-Path -Path "./TestResults/STA-Problematic")) {
    New-Item -Path "./TestResults/STA-Problematic" -ItemType Directory -Force | Out-Null
}

# Exécuter les tests unitaires avec collecte de couverture et configuration spéciale
Write-Host "Execution des tests unitaires avec collecte de couverture..." -ForegroundColor Green
Write-Host "Phase 1: Tests unitaires normaux (excluant les tests problematiques)..." -ForegroundColor Gray

# Phase 1: Exécuter les tests unitaires normaux (excluant Problematic)
dotnet test src/ClipboardPlus.Tests.Unit/ClipboardPlus.Tests.Unit.csproj `
    --collect:"XPlat Code Coverage" `
    --results-directory ./TestResults/Unit `
    --logger "console;verbosity=minimal" `
    --filter "Category!=Problematic" `
    --blame-hang-timeout 120s `
    --blame-crash

$unitNormalTestsExitCode = $LASTEXITCODE

Write-Host "Phase 2: Tests unitaires problematiques (execution isolee)..." -ForegroundColor Gray

# Phase 2: Exécuter les tests unitaires problématiques séparément avec timeout plus long
dotnet test src/ClipboardPlus.Tests.Unit/ClipboardPlus.Tests.Unit.csproj `
    --collect:"XPlat Code Coverage" `
    --results-directory ./TestResults/Unit-Problematic `
    --logger "console;verbosity=minimal" `
    --filter "Category=Problematic" `
    --blame-hang-timeout 180s `
    --blame-crash

$unitProblematicTestsExitCode = $LASTEXITCODE

# Combiner les codes de sortie des tests unitaires
$unitTestsExitCode = [Math]::Max($unitNormalTestsExitCode, $unitProblematicTestsExitCode)

# Vérifier si les tests unitaires ont réussi
if ($unitTestsExitCode -ne 0) {
    Write-Host "⚠️ Certains tests unitaires ont echoue ou ont timeout (code: $unitTestsExitCode). Continuons avec les tests d'integration..." -ForegroundColor Yellow
} else {
    Write-Host "✅ Tests unitaires reussis!" -ForegroundColor Green
}

# Exécuter les tests d'intégration avec collecte de couverture
Write-Host "Verification des tests d'integration..." -ForegroundColor Green

# Vérifier si le projet de tests d'intégration existe et peut être compilé
if (Test-Path -Path "src/ClipboardPlus.Tests.Integration/ClipboardPlus.Tests.Integration.csproj") {
    # Tester d'abord la compilation
    Write-Host "Compilation des tests d'integration..." -ForegroundColor Cyan
    dotnet build src/ClipboardPlus.Tests.Integration/ClipboardPlus.Tests.Integration.csproj --verbosity quiet
    $buildExitCode = $LASTEXITCODE

    if ($buildExitCode -eq 0) {
        Write-Host "Execution des tests d'integration avec collecte de couverture..." -ForegroundColor Green
        dotnet test src/ClipboardPlus.Tests.Integration/ClipboardPlus.Tests.Integration.csproj `
            --collect:"XPlat Code Coverage" `
            --results-directory ./TestResults/Integration `
            --logger "console;verbosity=minimal" `
            --blame-hang-timeout 120s `
            --blame-crash
        $integrationTestsExitCode = $LASTEXITCODE

        # Vérifier si les tests d'intégration ont réussi
        if ($integrationTestsExitCode -ne 0) {
            Write-Host "⚠️ Certains tests d'integration ont echoue (code: $integrationTestsExitCode). Continuons avec les tests STA..." -ForegroundColor Yellow
        } else {
            Write-Host "✅ Tests d'integration reussis!" -ForegroundColor Green
        }
    } else {
        Write-Host "⚠️ Les tests d'integration ne peuvent pas être compiles (erreurs de compilation). Ignores pour cette execution." -ForegroundColor Yellow
        $integrationTestsExitCode = 0  # Ne pas faire échouer le script pour cela
    }
} else {
    Write-Host "⚠️ Projet de tests d'integration non trouve. Continuons avec les tests STA..." -ForegroundColor Yellow
    $integrationTestsExitCode = 0  # Ne pas faire échouer le script pour cela
}



# Exécuter les tests STA avec collecte de couverture et configuration spéciale
Write-Host "Execution des tests STA avec collecte de couverture..." -ForegroundColor Green
Write-Host "Phase 1: Tests STA normaux (excluant les tests problematiques)..." -ForegroundColor Gray

# Phase 1: Exécuter les tests STA normaux (excluant ProblematicSTA)
dotnet test src/ClipboardPlus.Tests.STA/ClipboardPlus.Tests.STA.csproj `
    --collect:"XPlat Code Coverage" `
    --results-directory ./TestResults/STA-Normal `
    --logger "console;verbosity=minimal" `
    --settings:tests.runsettings `
    --filter "Category!=ProblematicSTA" `
    --blame-hang-timeout 60s `
    --blame-crash

$staNormalTestsExitCode = $LASTEXITCODE

Write-Host "Phase 2: Tests STA problematiques (execution isolee)..." -ForegroundColor Gray

# Phase 2: Exécuter les tests STA problématiques séparément avec timeout plus long
dotnet test src/ClipboardPlus.Tests.STA/ClipboardPlus.Tests.STA.csproj `
    --collect:"XPlat Code Coverage" `
    --results-directory ./TestResults/STA-Problematic `
    --logger "console;verbosity=minimal" `
    --settings:tests.runsettings `
    --filter "Category=ProblematicSTA" `
    --blame-hang-timeout 120s `
    --blame-crash

$staProblematicTestsExitCode = $LASTEXITCODE

# Combiner les codes de sortie des tests STA
$staTestsExitCode = [Math]::Max($staNormalTestsExitCode, $staProblematicTestsExitCode)

# Vérifier si les tests STA ont réussi
if ($staTestsExitCode -ne 0) {
    Write-Host "⚠️ Certains tests STA ont echoue ou ont timeout (code: $staTestsExitCode). Continuons avec la generation du rapport..." -ForegroundColor Yellow
} else {
    Write-Host "✅ Tests STA reussis!" -ForegroundColor Green
}

# Trouver les fichiers de couverture générés
$unitCoverageFiles = Get-ChildItem -Path "./TestResults/Unit" -Recurse -Filter "coverage.cobertura.xml" -ErrorAction SilentlyContinue
$unitProblematicCoverageFiles = Get-ChildItem -Path "./TestResults/Unit-Problematic" -Recurse -Filter "coverage.cobertura.xml" -ErrorAction SilentlyContinue
$integrationCoverageFiles = Get-ChildItem -Path "./TestResults/Integration" -Recurse -Filter "coverage.cobertura.xml" -ErrorAction SilentlyContinue
$staNormalCoverageFiles = Get-ChildItem -Path "./TestResults/STA-Normal" -Recurse -Filter "coverage.cobertura.xml" -ErrorAction SilentlyContinue
$staProblematicCoverageFiles = Get-ChildItem -Path "./TestResults/STA-Problematic" -Recurse -Filter "coverage.cobertura.xml" -ErrorAction SilentlyContinue

# Vérifier si les fichiers de couverture ont été générés
if (($unitCoverageFiles.Count -eq 0) -and ($unitProblematicCoverageFiles.Count -eq 0) -and ($integrationCoverageFiles.Count -eq 0) -and ($staNormalCoverageFiles.Count -eq 0) -and ($staProblematicCoverageFiles.Count -eq 0)) {
    Write-Host "❌ Aucun fichier de couverture n'a ete genere." -ForegroundColor Red
    exit 1
}

# Préparer les chemins pour la fusion
$coverageFiles = @()

if ($unitCoverageFiles.Count -gt 0) {
    $unitCoverageFile = $unitCoverageFiles | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    $coverageFiles += $unitCoverageFile.FullName
    Write-Host "✅ Fichier de couverture des tests unitaires normaux trouve: $($unitCoverageFile.FullName)" -ForegroundColor Green
} else {
    Write-Host "⚠️ Aucun fichier de couverture pour les tests unitaires normaux n'a ete trouve." -ForegroundColor Yellow
}

if ($unitProblematicCoverageFiles.Count -gt 0) {
    $unitProblematicCoverageFile = $unitProblematicCoverageFiles | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    $coverageFiles += $unitProblematicCoverageFile.FullName
    Write-Host "✅ Fichier de couverture des tests unitaires problematiques trouve: $($unitProblematicCoverageFile.FullName)" -ForegroundColor Green
} else {
    Write-Host "⚠️ Aucun fichier de couverture pour les tests unitaires problematiques n'a ete trouve." -ForegroundColor Yellow
}

if ($integrationCoverageFiles.Count -gt 0) {
    $integrationCoverageFile = $integrationCoverageFiles | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    $coverageFiles += $integrationCoverageFile.FullName
    Write-Host "✅ Fichier de couverture des tests d'integration trouve: $($integrationCoverageFile.FullName)" -ForegroundColor Green
} else {
    Write-Host "⚠️ Aucun fichier de couverture pour les tests d'integration n'a ete trouve." -ForegroundColor Yellow
}

if ($staNormalCoverageFiles.Count -gt 0) {
    $staNormalCoverageFile = $staNormalCoverageFiles | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    $coverageFiles += $staNormalCoverageFile.FullName
    Write-Host "✅ Fichier de couverture des tests STA normaux trouve: $($staNormalCoverageFile.FullName)" -ForegroundColor Green
} else {
    Write-Host "⚠️ Aucun fichier de couverture pour les tests STA normaux n'a ete trouve." -ForegroundColor Yellow
}

if ($staProblematicCoverageFiles.Count -gt 0) {
    $staProblematicCoverageFile = $staProblematicCoverageFiles | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    $coverageFiles += $staProblematicCoverageFile.FullName
    Write-Host "✅ Fichier de couverture des tests STA problematiques trouve: $($staProblematicCoverageFile.FullName)" -ForegroundColor Green
} else {
    Write-Host "⚠️ Aucun fichier de couverture pour les tests STA problematiques n'a ete trouve." -ForegroundColor Yellow
}

# Vérifier qu'au moins un fichier de couverture a été trouvé
if ($coverageFiles.Count -eq 0) {
    Write-Host "❌ Aucun fichier de couverture n'a ete trouve. Impossible de generer le rapport." -ForegroundColor Red
    exit 1
}

# Correction des chemins dans les fichiers de couverture
Write-Host "Correction des chemins dans les fichiers de couverture..." -ForegroundColor Yellow

function Fix-CoveragePaths {
    param([string]$CoverageFile)

    if (Test-Path $CoverageFile) {
        $content = Get-Content $CoverageFile -Raw
        $currentPath = (Get-Location).Path

        # Remplacer les anciens chemins absolus par le chemin actuel
        $content = $content -replace 'C:\\AI_Disk\\Projets\\ClipboardPlus\\', ($currentPath + '\')
        $content = $content -replace 'C:/AI_Disk/Projets/ClipboardPlus/', ($currentPath + '/')
        $content = $content -replace 'C:\\AI_Disk\\Projets\\ClipboardPlus/', ($currentPath + '\')

        # Sauvegarder le fichier corrigé
        $content | Set-Content $CoverageFile -Encoding UTF8
        Write-Host "✅ Chemins corriges dans: $(Split-Path $CoverageFile -Leaf)" -ForegroundColor Green
    }
}

# Corriger les chemins dans tous les fichiers de couverture
foreach ($coverageFile in $coverageFiles) {
    Fix-CoveragePaths $coverageFile
}

# Fusionner les rapports de couverture
Write-Host "Fusion des rapports de couverture..." -ForegroundColor Green
$reportsParam = $coverageFiles -join ";"
Write-Host "Fichiers de couverture à fusionner: $reportsParam" -ForegroundColor Gray

reportgenerator "-reports:$reportsParam" "-targetdir:coverage-report" "-reporttypes:Html;Cobertura" "-assemblyfilters:+ClipboardPlus"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ echec lors de la generation du rapport HTML." -ForegroundColor Red
    exit $LASTEXITCODE
}

# Copier le fichier Cobertura fusionné
if (Test-Path -Path "coverage-report\Cobertura.xml") {
    Copy-Item -Path "coverage-report\Cobertura.xml" -Destination "coverage.cobertura.xml" -Force
    Write-Host "✅ Fichier Cobertura.xml copie vers coverage.cobertura.xml" -ForegroundColor Green
}

# Ouvrir le rapport dans le navigateur par défaut
Write-Host "Ouverture du rapport de couverture..." -ForegroundColor Cyan
Start-Process "coverage-report\index.html"

# Attendre que le fichier HTML soit complètement généré
Start-Sleep -Seconds 1

# Afficher un résumé de la couverture
Write-Host "=== RESUME DE LA COUVERTURE ===" -ForegroundColor Magenta

# Lire directement depuis le fichier XML qui est plus fiable
if (Test-Path -Path "coverage.cobertura.xml") {
    [xml]$coverageXml = Get-Content -Path "coverage.cobertura.xml"
    $lineRateValue = [double]$coverageXml.coverage.'line-rate'
    $branchRateValue = [double]$coverageXml.coverage.'branch-rate'

    $lineRate = [math]::Round($lineRateValue * 100, 2)
    $branchRate = [math]::Round($branchRateValue * 100, 2)

    Write-Host "✅ Couverture de lignes: $lineRate%" -ForegroundColor Green
    Write-Host "✅ Couverture de branches: $branchRate%" -ForegroundColor Green

    # Afficher des statistiques sur les tests unitaires, d'intégration et STA séparément si disponibles
    if ($unitCoverageFiles.Count -gt 0) {
        [xml]$unitCoverageXml = Get-Content -Path $unitCoverageFile.FullName
        $unitLineRate = [math]::Round([double]$unitCoverageXml.coverage.'line-rate' * 100, 2)
        $unitBranchRate = [math]::Round([double]$unitCoverageXml.coverage.'branch-rate' * 100, 2)
        Write-Host "   - Tests unitaires normaux: $unitLineRate% lignes, $unitBranchRate% branches" -ForegroundColor Cyan
    }

    if ($unitProblematicCoverageFiles.Count -gt 0) {
        [xml]$unitProblematicCoverageXml = Get-Content -Path $unitProblematicCoverageFile.FullName
        $unitProblematicLineRate = [math]::Round([double]$unitProblematicCoverageXml.coverage.'line-rate' * 100, 2)
        $unitProblematicBranchRate = [math]::Round([double]$unitProblematicCoverageXml.coverage.'branch-rate' * 100, 2)
        Write-Host "   - Tests unitaires problematiques: $unitProblematicLineRate% lignes, $unitProblematicBranchRate% branches" -ForegroundColor Cyan
    }

    if ($integrationCoverageFiles.Count -gt 0) {
        [xml]$integrationCoverageXml = Get-Content -Path $integrationCoverageFile.FullName
        $integrationLineRate = [math]::Round([double]$integrationCoverageXml.coverage.'line-rate' * 100, 2)
        $integrationBranchRate = [math]::Round([double]$integrationCoverageXml.coverage.'branch-rate' * 100, 2)
        Write-Host "   - Tests d'integration: $integrationLineRate% lignes, $integrationBranchRate% branches" -ForegroundColor Cyan
    }

    if ($staNormalCoverageFiles.Count -gt 0) {
        [xml]$staNormalCoverageXml = Get-Content -Path $staNormalCoverageFile.FullName
        $staNormalLineRate = [math]::Round([double]$staNormalCoverageXml.coverage.'line-rate' * 100, 2)
        $staNormalBranchRate = [math]::Round([double]$staNormalCoverageXml.coverage.'branch-rate' * 100, 2)
        Write-Host "   - Tests STA normaux: $staNormalLineRate% lignes, $staNormalBranchRate% branches" -ForegroundColor Cyan
    }

    if ($staProblematicCoverageFiles.Count -gt 0) {
        [xml]$staProblematicCoverageXml = Get-Content -Path $staProblematicCoverageFile.FullName
        $staProblematicLineRate = [math]::Round([double]$staProblematicCoverageXml.coverage.'line-rate' * 100, 2)
        $staProblematicBranchRate = [math]::Round([double]$staProblematicCoverageXml.coverage.'branch-rate' * 100, 2)
        Write-Host "   - Tests STA problematiques: $staProblematicLineRate% lignes, $staProblematicBranchRate% branches" -ForegroundColor Cyan
    }
} else {
    # Fallback sur l'extraction du HTML si le XML n'est pas disponible
    $coverageContent = Get-Content -Path "coverage-report\index.html" -Raw -ErrorAction SilentlyContinue
    if ($coverageContent) {
        $lineMatch = [regex]::Match($coverageContent, '<div class="card-header">Line coverage</div>\s*<div class="card-body">\s*<div class="large cardpercentagebar cardpercentagebar\d+">(\d+)%</div>')
        $branchMatch = [regex]::Match($coverageContent, '<div class="card-header">Branch coverage</div>\s*<div class="card-body">\s*<div class="large cardpercentagebar cardpercentagebar\d+">(\d+)%</div>')

        if ($lineMatch.Success -and $branchMatch.Success) {
            Write-Host "✅ Couverture de lignes: $($lineMatch.Groups[1].Value)%" -ForegroundColor Green
            Write-Host "✅ Couverture de branches: $($branchMatch.Groups[1].Value)%" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Impossible d'extraire les informations de couverture du rapport HTML." -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ Impossible de lire le fichier de rapport HTML." -ForegroundColor Yellow
    }
}

# Nettoyage des fichiers intermédiaires
Write-Host "Nettoyage des fichiers temporaires..." -ForegroundColor Gray
if (Test-Path -Path "./TestResults") {
    Remove-Item -Path "./TestResults" -Recurse -Force
}

Write-Host "=== TERMINE! ===" -ForegroundColor Cyan
Write-Host "Le rapport de couverture est disponible dans le dossier 'coverage-report'." -ForegroundColor White