using System;
using System.IO;
using System.Threading;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.Tests.Unit.Core.Services.Logging
{
    /// <summary>
    /// Tests de validation pour s'assurer que la refactorisation de WriteToLog
    /// maintient la compatibilité et améliore les fonctionnalités.
    /// </summary>
    [TestFixture]
    public class WriteToLogRefactoredValidationTests
    {
        private LoggingService _loggingService = null!;
        private string _tempLogFile = string.Empty;
        private ServiceProvider _serviceProvider = null!;

        [SetUp]
        public void SetUp()
        {
            _tempLogFile = Path.GetTempFileName();

            // Configuration avec DI
            var services = new ServiceCollection();
            var loggingConfig = new LoggingConfiguration
            {
                LogFilePath = _tempLogFile,
                ConsoleOutputEnabled = false,
                DebugOutputEnabled = false,
                MinimumLevel = "DEBUG",
                MaxBufferSize = 50
            };

            services.AddSingleton<ILoggingConfiguration>(loggingConfig);
            services.AddSingleton<ILogEntryFactory, LogEntryFactory>();
            services.AddSingleton<ILogTarget, DebugLogTarget>();
            services.AddSingleton<ILogTarget>(provider =>
            {
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                return new ConsoleLogTarget(config.ConsoleOutputEnabled);
            });
            services.AddSingleton<ILogTarget>(provider =>
            {
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                return new FileLogTarget(config.LogFilePath);
            });

            services.AddSingleton<ILoggingService>(provider =>
            {
                var factory = provider.GetRequiredService<ILogEntryFactory>();
                var targets = provider.GetServices<ILogTarget>();
                var config = provider.GetRequiredService<ILoggingConfiguration>();

                return new LoggingService(factory, targets, config);
            });

            _serviceProvider = services.BuildServiceProvider();
            _loggingService = (LoggingService)_serviceProvider.GetRequiredService<ILoggingService>();
        }

        [TearDown]
        public void TearDown()
        {
            _loggingService?.Dispose();
            _serviceProvider?.Dispose();
            if (File.Exists(_tempLogFile))
            {
                try { File.Delete(_tempLogFile); } catch { }
            }
        }

        [Test]
        public void CallerInformationAttributes_ShouldProvideAccurateInformation()
        {
            // Act - Cette ligne spécifique sera tracée
            _loggingService.LogError("Test error with caller info"); // Ligne 80

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            var content = File.ReadAllText(_tempLogFile);

            // Vérifier que les informations d'appelant sont présentes et correctes
            Assert.That(content, Does.Contain("WriteToLogRefactoredValidationTests.cs"));
            Assert.That(content, Does.Contain("CallerInformationAttributes_ShouldProvideAccurateInformation"));
            Assert.That(content, Does.Contain(":80")); // Numéro de ligne
            Assert.That(content, Does.Contain("Test error with caller info"));
        }

        [Test]
        public void NewArchitecture_ShouldMaintainBackwardCompatibility()
        {
            // Arrange - Utiliser l'interface pour tester la compatibilité
            ILoggingService loggingInterface = _loggingService;

            // Act - Utiliser toutes les méthodes de l'interface originale
            loggingInterface.LogDebug("Debug via interface");
            loggingInterface.LogInfo("Info via interface");
            loggingInterface.LogWarning("Warning via interface");
            loggingInterface.LogError("Error via interface");
            loggingInterface.LogCritical("Critical via interface");
            loggingInterface.LogDeletion("Deletion via interface");

            // Assert - Vérifier que tous les logs sont présents
            Assert.That(File.Exists(_tempLogFile), Is.True);
            var content = File.ReadAllText(_tempLogFile);
            
            Assert.That(content, Does.Contain("Debug via interface"));
            Assert.That(content, Does.Contain("Info via interface"));
            Assert.That(content, Does.Contain("Warning via interface"));
            Assert.That(content, Does.Contain("Error via interface"));
            Assert.That(content, Does.Contain("Critical via interface"));
            Assert.That(content, Does.Contain("Deletion via interface"));
        }

        [Test]
        public void NewArchitecture_ShouldProvideEnhancedCallerInformation()
        {
            // Act
            _loggingService.LogCritical("Critical error for enhanced info test");

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            var content = File.ReadAllText(_tempLogFile);
            
            // Vérifier le format amélioré avec Caller Information
            Assert.That(content, Does.Match(@"\[.*\] \[CRITIQUE\] \[.*\] \[Thread:\d+\] \[.*\.cs:.*:\d+\] !!! CRITIQUE !!! Critical error for enhanced info test"));

            // Vérifier les composants spécifiques
            Assert.That(content, Does.Contain("[CRITIQUE]"));
            Assert.That(content, Does.Contain("WriteToLogRefactoredValidationTests.cs"));
            Assert.That(content, Does.Contain("NewArchitecture_ShouldProvideEnhancedCallerInformation"));
        }

        [Test]
        public void FlushBehavior_ShouldBeConsistentWithOriginal()
        {
            // Act - Tester les niveaux qui forcent un flush immédiat
            _loggingService.LogWarning("Warning should flush");
            Assert.That(File.Exists(_tempLogFile), Is.True, "Warning should flush immediately");
            
            var content1 = File.ReadAllText(_tempLogFile);
            Assert.That(content1, Does.Contain("Warning should flush"));
            
            _loggingService.LogError("Error should flush");
            var content2 = File.ReadAllText(_tempLogFile);
            Assert.That(content2, Does.Contain("Error should flush"));
            
            _loggingService.LogCritical("Critical should flush");
            var content3 = File.ReadAllText(_tempLogFile);
            Assert.That(content3, Does.Contain("Critical should flush"));
            
            _loggingService.LogDeletion("Deletion should flush");
            var content4 = File.ReadAllText(_tempLogFile);
            Assert.That(content4, Does.Contain("Deletion should flush"));
        }

        [Test]
        public void ExceptionHandling_ShouldIncludeCallerInformation()
        {
            // Arrange
            var testException = new InvalidOperationException("Test exception for caller info");

            // Act
            _loggingService.LogError("Error with exception", testException);

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            var content = File.ReadAllText(_tempLogFile);
            
            // Vérifier que l'exception et les informations d'appelant sont présentes
            Assert.That(content, Does.Contain("Error with exception"));
            Assert.That(content, Does.Contain("Test exception for caller info"));
            Assert.That(content, Does.Contain("InvalidOperationException"));
            Assert.That(content, Does.Contain("WriteToLogRefactoredValidationTests.cs"));
            Assert.That(content, Does.Contain("ExceptionHandling_ShouldIncludeCallerInformation"));
        }

        [Test]
        public void ThreadInformation_ShouldBeAccurate()
        {
            // Act
            _loggingService.LogInfo("Thread info test");
            _loggingService.ForceFlush();

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            var content = File.ReadAllText(_tempLogFile);
            
            // Vérifier que l'information de thread est présente
            Assert.That(content, Does.Match(@"\[Thread:\d+\]"));
            Assert.That(content, Does.Contain("Thread info test"));
        }

        [Test]
        public void ContextInformation_ShouldBeDetected()
        {
            // Act
            _loggingService.LogInfo("Context detection test");
            _loggingService.ForceFlush();

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            var content = File.ReadAllText(_tempLogFile);
            
            // Vérifier que le contexte est détecté (Non-UI dans les tests)
            Assert.That(content, Does.Contain("[Non-UI]"));
            Assert.That(content, Does.Contain("Context detection test"));
        }

        [Test]
        public void TimestampFormat_ShouldBeConsistent()
        {
            // Act
            _loggingService.LogInfo("Timestamp format test");
            _loggingService.ForceFlush();

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            var content = File.ReadAllText(_tempLogFile);
            
            // Vérifier le format de timestamp [YYYY-MM-DD HH:mm:ss.fff]
            Assert.That(content, Does.Match(@"\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\]"));
            Assert.That(content, Does.Contain("Timestamp format test"));
        }

        [Test]
        public void RefactoredArchitecture_ShouldHandleHighVolume()
        {
            // Act - Générer un volume élevé de logs
            for (int i = 0; i < 50; i++)
            {
                _loggingService.LogDebug($"High volume test {i}");
            }
            
            // Forcer un flush pour s'assurer que tout est écrit
            _loggingService.ForceFlush();

            // Assert
            Assert.That(File.Exists(_tempLogFile), Is.True);
            var content = File.ReadAllText(_tempLogFile);
            
            // Vérifier que tous les logs sont présents
            Assert.That(content, Does.Contain("High volume test 0"));
            Assert.That(content, Does.Contain("High volume test 25"));
            Assert.That(content, Does.Contain("High volume test 49"));
            
            // Vérifier que les informations d'appelant sont présentes même en volume élevé
            Assert.That(content, Does.Contain("WriteToLogRefactoredValidationTests.cs"));
            Assert.That(content, Does.Contain("RefactoredArchitecture_ShouldHandleHighVolume"));
        }

        [Test]
        public void DisposeBehavior_ShouldFlushPendingEntries()
        {
            // Arrange - Créer un service temporaire avec DI
            var tempLogFile = Path.GetTempFileName();
            var services = new ServiceCollection();
            var loggingConfig = new LoggingConfiguration
            {
                LogFilePath = tempLogFile,
                ConsoleOutputEnabled = false,
                DebugOutputEnabled = false,
                MinimumLevel = "DEBUG",
                MaxBufferSize = 50
            };

            services.AddSingleton<ILoggingConfiguration>(loggingConfig);
            services.AddSingleton<ILogEntryFactory, LogEntryFactory>();
            services.AddSingleton<ILogTarget, DebugLogTarget>();
            services.AddSingleton<ILogTarget>(provider =>
            {
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                return new ConsoleLogTarget(config.ConsoleOutputEnabled);
            });
            services.AddSingleton<ILogTarget>(provider =>
            {
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                return new FileLogTarget(config.LogFilePath);
            });

            services.AddSingleton<ILoggingService>(provider =>
            {
                var factory = provider.GetRequiredService<ILogEntryFactory>();
                var targets = provider.GetServices<ILogTarget>();
                var config = provider.GetRequiredService<ILoggingConfiguration>();

                return new LoggingService(factory, targets, config);
            });

            using var tempServiceProvider = services.BuildServiceProvider();
            var tempService = (LoggingService)tempServiceProvider.GetRequiredService<ILoggingService>();

            // Act
            tempService.LogDebug("Entry before dispose");
            tempService.Dispose(); // Devrait flusher les entrées en attente

            // Assert
            Assert.That(File.Exists(tempLogFile), Is.True);
            var content = File.ReadAllText(tempLogFile);
            Assert.That(content, Does.Contain("Entry before dispose"));

            // Cleanup
            try { File.Delete(tempLogFile); } catch { }
        }
    }
}
