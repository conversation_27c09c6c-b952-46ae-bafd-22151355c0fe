using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ClipboardPlus.Core.Services;
using CommunityToolkit.Mvvm.Input;

namespace ClipboardPlus.UI.ViewModels
{
    /// <summary>
    /// ViewModel pour la fenêtre de nettoyage avancé de l'historique
    /// Permet de supprimer les éléments non épinglés plus anciens qu'une période donnée
    /// </summary>
    public class AdvancedCleanupViewModel : ViewModelBase
    {
        #region Champs privés

        private readonly IClipboardHistoryManager _historyManager;
        private readonly ILoggingService? _loggingService;
        private int _timeValue = 24;
        private string _timeUnit = "Heures";
        private string _previewMessage = "Calcul en cours...";
        private bool _isExecuting = false;

        #endregion

        #region Propriétés publiques

        /// <summary>
        /// Valeur numérique pour la période (ex: 24)
        /// </summary>
        public int TimeValue
        {
            get => _timeValue;
            set
            {
                if (SetProperty(ref _timeValue, value))
                {
                    UpdatePreview();
                }
            }
        }

        /// <summary>
        /// Unité de temps sélectionnée (Heures, Jours, Semaines, Mois)
        /// </summary>
        public string TimeUnit
        {
            get => _timeUnit;
            set
            {
                if (SetProperty(ref _timeUnit, value))
                {
                    UpdatePreview();
                }
            }
        }

        /// <summary>
        /// Collection des unités de temps disponibles
        /// </summary>
        public ObservableCollection<string> TimeUnits { get; }

        /// <summary>
        /// Message d'aperçu indiquant combien d'éléments seront supprimés
        /// </summary>
        public string PreviewMessage
        {
            get => _previewMessage;
            private set => SetProperty(ref _previewMessage, value);
        }

        /// <summary>
        /// Indique si une opération de nettoyage est en cours
        /// </summary>
        public bool IsExecuting
        {
            get => _isExecuting;
            private set => SetProperty(ref _isExecuting, value);
        }

        /// <summary>
        /// Commande pour exécuter le nettoyage
        /// </summary>
        public IAsyncRelayCommand ExecuteCleanupCommand { get; }

        /// <summary>
        /// Commande pour annuler et fermer la fenêtre
        /// </summary>
        public IRelayCommand CancelCommand { get; }

        #endregion

        #region Constructeur

        /// <summary>
        /// Initialise une nouvelle instance du ViewModel de nettoyage avancé
        /// </summary>
        /// <param name="historyManager">Service de gestion de l'historique</param>
        /// <param name="loggingService">Service de logging (peut être null)</param>
        public AdvancedCleanupViewModel(IClipboardHistoryManager historyManager, ILoggingService? loggingService)
        {
            _historyManager = historyManager ?? throw new ArgumentNullException(nameof(historyManager));
            _loggingService = loggingService; // Peut être null

            // Initialiser les unités de temps
            TimeUnits = new ObservableCollection<string>
            {
                "Heures",
                "Jours",
                "Semaines",
                "Mois"
            };

            // Initialiser les commandes
            ExecuteCleanupCommand = new AsyncRelayCommand(ExecuteCleanupAsync, CanExecuteCleanup);
            CancelCommand = new RelayCommand(Cancel);

            // Calculer l'aperçu initial
            UpdatePreview();

            _loggingService?.LogInfo("AdvancedCleanupViewModel initialisé");
        }

        #endregion

        #region Méthodes privées

        /// <summary>
        /// Met à jour le message d'aperçu en calculant le nombre d'éléments qui seront supprimés
        /// </summary>
        private void UpdatePreview()
        {
            try
            {
                var timeSpan = GetTimeSpanFromInput();
                var count = CalculateItemsToDelete(timeSpan);

                if (count == 0)
                {
                    PreviewMessage = "Aucun élément ne sera supprimé avec ces critères.";
                }
                else if (count == 1)
                {
                    PreviewMessage = "Cette action supprimera 1 élément.";
                }
                else
                {
                    PreviewMessage = $"Cette action supprimera {count} éléments.";
                }

                _loggingService?.LogInfo($"Aperçu mis à jour: {count} éléments à supprimer pour {TimeValue} {TimeUnit}");
            }
            catch (Exception ex)
            {
                PreviewMessage = "Erreur lors du calcul de l'aperçu.";
                _loggingService?.LogError($"Erreur lors du calcul de l'aperçu: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Convertit la valeur et l'unité saisies en TimeSpan
        /// </summary>
        /// <returns>TimeSpan correspondant à la période saisie</returns>
        private TimeSpan GetTimeSpanFromInput()
        {
            return TimeUnit switch
            {
                "Heures" => TimeSpan.FromHours(TimeValue),
                "Jours" => TimeSpan.FromDays(TimeValue),
                "Semaines" => TimeSpan.FromDays(TimeValue * 7),
                "Mois" => TimeSpan.FromDays(TimeValue * 30), // Approximation
                _ => TimeSpan.FromHours(TimeValue) // Valeur par défaut
            };
        }

        /// <summary>
        /// Calcule le nombre d'éléments qui seront supprimés avec la période donnée
        /// </summary>
        /// <param name="olderThan">Période avant laquelle supprimer</param>
        /// <returns>Nombre d'éléments qui seront supprimés</returns>
        private int CalculateItemsToDelete(TimeSpan olderThan)
        {
            var cutoffDate = DateTime.Now - olderThan;
            return _historyManager.HistoryItems
                .Count(item => !item.IsPinned && item.Timestamp < cutoffDate);
        }

        /// <summary>
        /// Détermine si la commande de nettoyage peut être exécutée
        /// </summary>
        /// <returns>True si la commande peut être exécutée</returns>
        private bool CanExecuteCleanup()
        {
            return !IsExecuting && TimeValue > 0;
        }

        /// <summary>
        /// Exécute le nettoyage de l'historique
        /// </summary>
        private async Task ExecuteCleanupAsync()
        {
            try
            {
                IsExecuting = true;
                _loggingService?.LogInfo($"Début du nettoyage avancé: {TimeValue} {TimeUnit}");

                var timeSpan = GetTimeSpanFromInput();
                var deletedCount = await _historyManager.ClearItemsOlderThanAsync(timeSpan);

                _loggingService?.LogInfo($"Nettoyage terminé: {deletedCount} éléments supprimés");

                // Mettre à jour l'aperçu après suppression
                UpdatePreview();

                // Déclencher l'événement de fermeture de fenêtre
                _loggingService?.LogInfo($"Déclenchement de l'événement OnCleanupCompleted avec deletedCount={deletedCount}");
                OnCleanupCompleted?.Invoke(deletedCount);
                _loggingService?.LogInfo($"Événement OnCleanupCompleted déclenché");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors du nettoyage: {ex.Message}", ex);
                OnCleanupError?.Invoke(ex.Message);
            }
            finally
            {
                IsExecuting = false;
            }
        }

        /// <summary>
        /// Annule l'opération et ferme la fenêtre
        /// </summary>
        private void Cancel()
        {
            _loggingService?.LogInfo("Nettoyage avancé annulé par l'utilisateur");
            OnCancelled?.Invoke();
        }

        #endregion

        #region Événements

        /// <summary>
        /// Événement déclenché quand le nettoyage est terminé avec succès
        /// </summary>
        public event Action<int>? OnCleanupCompleted;

        /// <summary>
        /// Événement déclenché en cas d'erreur pendant le nettoyage
        /// </summary>
        public event Action<string>? OnCleanupError;

        /// <summary>
        /// Événement déclenché quand l'utilisateur annule
        /// </summary>
        public event Action? OnCancelled;

        #endregion
    }
}
