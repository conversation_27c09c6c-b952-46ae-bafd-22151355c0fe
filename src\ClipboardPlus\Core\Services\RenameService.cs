using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service métier responsable de la logique de renommage des éléments du presse-papiers.
    /// Cette implémentation encapsule la logique métier pure sans dépendances UI.
    /// </summary>
    public class RenameService : IRenameService
    {
        private readonly IClipboardHistoryManager _clipboardHistoryManager;
        private readonly ILoggingService? _logger;

        /// <summary>
        /// Initialise une nouvelle instance du service de renommage.
        /// </summary>
        /// <param name="clipboardHistoryManager">Gestionnaire de l'historique du presse-papiers</param>
        /// <param name="logger">Logger optionnel pour les diagnostics</param>
        public RenameService(
            IClipboardHistoryManager clipboardHistoryManager,
            ILoggingService? logger = null)
        {
            _clipboardHistoryManager = clipboardHistoryManager ?? throw new ArgumentNullException(nameof(clipboardHistoryManager));
            _logger = logger;
        }

        /// <summary>
        /// Renomme un élément du presse-papiers de manière asynchrone.
        /// </summary>
        /// <param name="item">L'élément à renommer</param>
        /// <param name="newName">Le nouveau nom à attribuer</param>
        /// <returns>Le résultat de l'opération de renommage</returns>
        public async Task<RenameResult> RenameItemAsync(ClipboardItem item, string newName)
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);

            try
            {
                // Validation des paramètres
                if (item == null)
                {
                    const string error = "L'élément à renommer ne peut pas être null";
                    _logger?.LogWarning($"[{operationId}] {error}");
                    return RenameResult.CreateFailure(error);
                }

                // Mémoriser l'ancien nom pour le résultat
                var oldName = item.CustomName;

                _logger?.LogDebug($"[{operationId}] Début du renommage: '{oldName ?? "(null)"}' → '{newName ?? "(null)"}' (ID: {item.Id})");

                // Mise à jour de l'élément
                item.CustomName = newName;

                // DIAGNOSTIC CRITIQUE : Logger l'état de visibilité AVANT persistance
                _logger?.LogInfo($"[{operationId}] AVANT UpdateItemAsync - Item ID: {item.Id}, CustomName: '{item.CustomName}', IsTitleVisible: {item.IsTitleVisible}");

                // Persistance en base de données
                await _clipboardHistoryManager.UpdateItemAsync(item);

                // DIAGNOSTIC CRITIQUE : Logger l'état de visibilité APRÈS persistance
                _logger?.LogInfo($"[{operationId}] APRÈS UpdateItemAsync - Item ID: {item.Id}, CustomName: '{item.CustomName}', IsTitleVisible: {item.IsTitleVisible}");

                _logger?.LogInfo($"[{operationId}] Renommage réussi: '{oldName ?? "(null)"}' → '{newName ?? "(null)"}' (ID: {item.Id})");

                return RenameResult.CreateSuccess(item, oldName, newName);
            }
            catch (Exception ex)
            {
                var errorMessage = $"Erreur lors du renommage: {ex.Message}";
                _logger?.LogError($"[{operationId}] {errorMessage}", ex);

                return RenameResult.CreateFailure(errorMessage, item?.CustomName, newName);
            }
        }
    }
}
