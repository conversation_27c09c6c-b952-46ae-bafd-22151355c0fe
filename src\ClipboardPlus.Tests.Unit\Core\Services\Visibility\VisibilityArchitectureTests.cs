using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.Visibility
{
    /// <summary>
    /// Tests pour valider la nouvelle architecture SOLID de visibilité
    /// </summary>
    [TestFixture]
    public class VisibilityArchitectureTests
    {
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private TitleVisibilityRule _titleRule = null!;
        private TimestampVisibilityRule _timestampRule = null!;
        private VisibilityStateManager _visibilityManager = null!;

        [SetUp]
        public void SetUp()
        {
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockLoggingService = new Mock<ILoggingService>();
            _titleRule = new TitleVisibilityRule(_mockLoggingService.Object);
            _timestampRule = new TimestampVisibilityRule(_mockLoggingService.Object);

            // Configuration par défaut
            _mockSettingsManager.Setup(s => s.HideItemTitle).Returns(false);
            _mockSettingsManager.Setup(s => s.HideTimestamp).Returns(false);

            _visibilityManager = new VisibilityStateManager(
                _titleRule,
                _timestampRule,
                _mockSettingsManager.Object,
                _mockLoggingService.Object
            );
        }

        [Test]
        public void TitleVisibilityRule_ShouldShowTitle_WhenGlobalVisibleAndTitleExists()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = "Test Title" };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "Le titre devrait être visible quand global activé et titre existe");
        }

        [Test]
        public void TitleVisibilityRule_ShouldHideTitle_WhenGlobalHidden()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = false };
            var item = new ClipboardItem { CustomName = "Test Title" };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "Le titre devrait être masqué quand global désactivé, même avec CustomName");
        }

        [Test]
        public void TitleVisibilityRule_ShouldHideTitle_WhenNoCustomName()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTitleVisibility = true };
            var item = new ClipboardItem { CustomName = null };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "Le titre devrait être masqué quand pas de nom personnalisé");
        }

        [Test]
        public void TitleVisibilityRule_ShouldHideTitle_WhenRenamedButGlobalHidden()
        {
            // Arrange - Simule le cas de renommage avec option "cacher titre" activée
            var context = new VisibilityContext { GlobalTitleVisibility = false }; // Utilisateur a choisi de cacher
            var item = new ClipboardItem { CustomName = "Nouveau nom après renommage" };

            // Act
            var result = _titleRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsFalse(result, "Le titre doit rester caché après renommage si l'utilisateur a choisi de cacher les titres");
        }

        [Test]
        public void TimestampVisibilityRule_ShouldFollowGlobalSetting()
        {
            // Arrange
            var context = new VisibilityContext { GlobalTimestampVisibility = true };
            var item = new ClipboardItem();

            // Act
            var result = _timestampRule.ShouldBeVisible(item, context);

            // Assert
            Assert.IsTrue(result, "L'horodatage devrait suivre le paramètre global");
        }

        [Test]
        public void VisibilityStateManager_ShouldInitializeCorrectly()
        {
            // Assert
            Assert.IsTrue(_visibilityManager.GlobalTitleVisibility, "Visibilité globale des titres devrait être initialisée");
            Assert.IsTrue(_visibilityManager.GlobalTimestampVisibility, "Visibilité globale des horodatages devrait être initialisée");
        }

        [Test]
        public void VisibilityStateManager_ShouldTriggerEvent_WhenVisibilityChanges()
        {
            // Arrange
            bool eventTriggered = false;
            VisibilityChangedEventArgs? eventArgs = null;
            
            _visibilityManager.VisibilityChanged += (sender, args) =>
            {
                eventTriggered = true;
                eventArgs = args;
            };

            // Act
            _visibilityManager.UpdateGlobalTitleVisibility(false);

            // Assert
            Assert.IsTrue(eventTriggered, "L'événement devrait être déclenché");
            Assert.IsNotNull(eventArgs, "Les arguments d'événement ne devraient pas être null");
            Assert.AreEqual(VisibilityType.Title, eventArgs!.Type, "Le type d'événement devrait être Title");
            Assert.IsFalse(eventArgs.IsVisible, "La visibilité devrait être False");
        }

        [Test]
        public void VisibilityStateManager_ShouldNotTriggerEvent_WhenValueUnchanged()
        {
            // Arrange
            bool eventTriggered = false;
            _visibilityManager.VisibilityChanged += (sender, args) => eventTriggered = true;

            // Act - Définir la même valeur
            _visibilityManager.UpdateGlobalTitleVisibility(true); // Déjà true par défaut

            // Assert
            Assert.IsFalse(eventTriggered, "L'événement ne devrait pas être déclenché si la valeur ne change pas");
        }

        [Test]
        public void VisibilityStateManager_ShouldRespectSOLIDLogic()
        {
            // Arrange
            var itemWithTitle = new ClipboardItem { CustomName = "Mon Titre" };
            var itemWithoutTitle = new ClipboardItem { CustomName = null };

            // Test 1: Global autorisé + titre présent = VISIBLE
            _mockSettingsManager.Setup(s => s.HideItemTitle).Returns(false);
            _visibilityManager.UpdateTitleVisibilityFromSettings(false);
            bool shouldShow1 = _visibilityManager.ShouldShowTitle(itemWithTitle);
            itemWithTitle.IsTitleVisible = shouldShow1;
            Assert.IsTrue(itemWithTitle.IsTitleVisible, "Titre devrait être visible quand global autorisé et titre présent");

            // Test 2: Global autorisé + pas de titre = MASQUÉ
            bool shouldShow2 = _visibilityManager.ShouldShowTitle(itemWithoutTitle);
            itemWithoutTitle.IsTitleVisible = shouldShow2;
            Assert.IsFalse(itemWithoutTitle.IsTitleVisible, "Titre devrait être masqué quand pas de titre même si global autorisé");

            // Test 3: Global masqué + titre présent = MASQUÉ
            _mockSettingsManager.Setup(s => s.HideItemTitle).Returns(true);
            _visibilityManager.UpdateTitleVisibilityFromSettings(true);
            bool shouldShow3 = _visibilityManager.ShouldShowTitle(itemWithTitle);
            itemWithTitle.IsTitleVisible = shouldShow3;
            Assert.IsFalse(itemWithTitle.IsTitleVisible, "Titre devrait être masqué quand global désactivé même avec titre");

            // Test 4: Global masqué + pas de titre = MASQUÉ
            bool shouldShow4 = _visibilityManager.ShouldShowTitle(itemWithoutTitle);
            itemWithoutTitle.IsTitleVisible = shouldShow4;
            Assert.IsFalse(itemWithoutTitle.IsTitleVisible, "Titre devrait être masqué quand global désactivé et pas de titre");
        }

        [Test]
        public void VisibilityStateManager_ShouldUpdateSettingsManager()
        {
            // Act
            _visibilityManager.UpdateGlobalTitleVisibility(false);

            // Assert
            _mockSettingsManager.VerifySet(s => s.HideItemTitle = true, Times.Once, 
                "SettingsManager devrait être mis à jour avec la valeur inversée");
        }
    }
}
