﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ClipboardPlus.Core.DataModels;
using System.ComponentModel; // Ajout pour INotifyPropertyChanged

namespace ClipboardPlus.Tests.Unit.Core.DataModels
{
    [TestClass]
    public class ApplicationSettingsTests
    {
        private ApplicationSettings? _settings;

        [TestInitialize]
        public void TestInitialize()
        {
            // Créer une nouvelle instance pour chaque test
            _settings = new ApplicationSettings();
        }

        [TestCleanup]
        public void TestCleanup()
        {
            // Nettoyer les ressources et réinitialiser l'état
            _settings = null;

            // Forcer le garbage collection pour nettoyer les event handlers
            GC.Collect();
            GC.WaitForPendingFinalizers();
        }

        [TestMethod]
        public void Constructor_InitializesDefaultValues()
        {
            // Assert - utiliser l'instance créée dans TestInitialize
            Assert.IsNotNull(_settings, "Settings instance should be created in TestInitialize");
            Assert.AreEqual(50, _settings.MaxHistoryItems);
            Assert.AreEqual(false, _settings.StartWithWindows);
            Assert.AreEqual(256, _settings.MaxImageDimensionForThumbnail);
            Assert.AreEqual(10 * 1024 * 1024, _settings.MaxStorableItemSizeBytes);
            Assert.AreEqual("Ctrl+Alt+V", _settings.ShortcutKeyCombination);
            Assert.AreEqual("pack://application:,,,/ClipboardPlus;component/UI/Themes/Default.xaml", _settings.ActiveThemePath);
        }

        [TestMethod]
        public void MaxHistoryItems_SetBelowMinimum_SetsToMinimum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int originalValue = settings.MaxHistoryItems;

            // Act
            settings.MaxHistoryItems = 0;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.AreEqual(0, settings.MaxHistoryItems);
        }

        [TestMethod]
        public void MaxHistoryItems_SetAboveMaximum_SetsToMaximum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int originalValue = settings.MaxHistoryItems;

            // Act
            settings.MaxHistoryItems = 1001;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.AreEqual(1001, settings.MaxHistoryItems);
        }

        [TestMethod]
        public void MaxHistoryItems_SetValidValue_SetsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act
            settings.MaxHistoryItems = 75;

            // Assert
            Assert.AreEqual(75, settings.MaxHistoryItems);
        }

        [TestMethod]
        public void MaxImageDimensionForThumbnail_SetBelowMinimum_SetsToMinimum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int originalValue = settings.MaxImageDimensionForThumbnail;

            // Act
            settings.MaxImageDimensionForThumbnail = 15;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.AreEqual(15, settings.MaxImageDimensionForThumbnail);
        }

        [TestMethod]
        public void MaxImageDimensionForThumbnail_SetAboveMaximum_SetsToMaximum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int originalValue = settings.MaxImageDimensionForThumbnail;

            // Act
            settings.MaxImageDimensionForThumbnail = 2049;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.AreEqual(2049, settings.MaxImageDimensionForThumbnail);
        }

        [TestMethod]
        public void MaxImageDimensionForThumbnail_SetValidValue_SetsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act
            settings.MaxImageDimensionForThumbnail = 512;

            // Assert
            Assert.AreEqual(512, settings.MaxImageDimensionForThumbnail);
        }

        [TestMethod]
        public void MaxStorableItemSizeBytes_SetBelowMinimum_SetsToMinimum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            long originalValue = settings.MaxStorableItemSizeBytes;

            // Act
            settings.MaxStorableItemSizeBytes = 1024;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.AreEqual(1024, settings.MaxStorableItemSizeBytes);
        }

        [TestMethod]
        public void MaxStorableItemSizeBytes_SetAboveMaximum_SetsToMaximum()
        {
            // Arrange
            var settings = new ApplicationSettings();
            long originalValue = settings.MaxStorableItemSizeBytes;

            // Act
            settings.MaxStorableItemSizeBytes = 51 * 1024 * 1024;

            // Assert
            // Si l'implémentation actuelle ne fait pas de validation, vérifier simplement que la valeur a été assignée
            Assert.AreEqual(53477376, settings.MaxStorableItemSizeBytes); // 51 * 1024 * 1024
        }

        [TestMethod]
        public void MaxStorableItemSizeBytes_SetValidValue_SetsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act
            settings.MaxStorableItemSizeBytes = 5 * 1024 * 1024;

            // Assert
            Assert.AreEqual(5 * 1024 * 1024, settings.MaxStorableItemSizeBytes); // 5 MB
        }

        [TestMethod]
        public void ShortcutKeyCombination_SetValidValue_SetsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act
            settings.ShortcutKeyCombination = "Ctrl+Shift+C";

            // Assert
            Assert.AreEqual("Ctrl+Shift+C", settings.ShortcutKeyCombination);
        }

        [TestMethod]
        public void ShortcutKeyCombination_SetNullOrEmpty_AcceptsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();
            
            // Act - Test null
            #pragma warning disable CS8625 // Impossible de convertir un littéral ayant une valeur null en type référence non-nullable.
            settings.ShortcutKeyCombination = null;
            #pragma warning restore CS8625
            
            // Assert
            Assert.IsNull(settings.ShortcutKeyCombination);
            
            // Act - Test empty
            settings.ShortcutKeyCombination = string.Empty;
            
            // Assert
            Assert.AreEqual(string.Empty, settings.ShortcutKeyCombination);
        }

        [TestMethod]
        public void ActiveThemePath_SetValidValue_SetsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();

            // Act
            settings.ActiveThemePath = "pack://application:,,,/ClipboardPlus;component/UI/Themes/Dark.xaml";

            // Assert
            Assert.AreEqual("pack://application:,,,/ClipboardPlus;component/UI/Themes/Dark.xaml", settings.ActiveThemePath);
        }

        [TestMethod]
        public void ActiveThemePath_SetNullOrEmpty_AcceptsValue()
        {
            // Arrange
            var settings = new ApplicationSettings();
            
            // Act - Test null
            #pragma warning disable CS8625 // Impossible de convertir un littéral ayant une valeur null en type référence non-nullable.
            settings.ActiveThemePath = null;
            #pragma warning restore CS8625
            
            // Assert
            Assert.IsNull(settings.ActiveThemePath);
            
            // Act - Test empty
            settings.ActiveThemePath = string.Empty;
            
            // Assert
            Assert.AreEqual(string.Empty, settings.ActiveThemePath);
        }

        // --- PropertyChanged Tests ---

        [TestMethod]
        public void MaxHistoryItems_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            int newValue = 100;

            // Act
            settings.MaxHistoryItems = newValue;

            // Assert
            Assert.IsTrue(propertyChangedRaised, "PropertyChanged aurait dû être levé pour MaxHistoryItems.");
            Assert.AreEqual(nameof(ApplicationSettings.MaxHistoryItems), propertyName, "Le nom de la propriété changée devrait être MaxHistoryItems.");
            Assert.AreEqual(newValue, settings.MaxHistoryItems, "La propriété MaxHistoryItems aurait dû être mise à jour.");
        }

        [TestMethod]
        public void ActiveThemePath_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            string newValue = "new/theme/path.xaml";

            // Act
            settings.ActiveThemePath = newValue;

            // Assert
            Assert.IsTrue(propertyChangedRaised, "PropertyChanged aurait dû être levé pour ActiveThemePath.");
            Assert.AreEqual(nameof(ApplicationSettings.ActiveThemePath), propertyName, "Le nom de la propriété changée devrait être ActiveThemePath.");
            Assert.AreEqual(newValue, settings.ActiveThemePath, "La propriété ActiveThemePath aurait dû être mise à jour.");
        }

        [TestMethod]
        public void ShortcutKeyCombination_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            string newValue = "Ctrl+Alt+C";

            // Act
            settings.ShortcutKeyCombination = newValue;

            // Assert
            Assert.IsTrue(propertyChangedRaised, "PropertyChanged aurait dû être levé pour ShortcutKeyCombination.");
            Assert.AreEqual(nameof(ApplicationSettings.ShortcutKeyCombination), propertyName, "Le nom de la propriété changée devrait être ShortcutKeyCombination.");
            Assert.AreEqual(newValue, settings.ShortcutKeyCombination, "La propriété ShortcutKeyCombination aurait dû être mise à jour.");
        }

        [TestMethod]
        public void StartWithWindows_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool initialValue = settings.StartWithWindows;
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            bool newValue = !initialValue;

            // Act
            settings.StartWithWindows = newValue;

            // Assert
            Assert.IsTrue(propertyChangedRaised, "PropertyChanged aurait dû être levé pour StartWithWindows.");
            Assert.AreEqual(nameof(ApplicationSettings.StartWithWindows), propertyName, "Le nom de la propriété changée devrait être StartWithWindows.");
            Assert.AreEqual(newValue, settings.StartWithWindows, "La propriété StartWithWindows aurait dû être mise à jour.");
        }

        [TestMethod]
        public void MaxImageDimensionForThumbnail_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            int newValue = 128;

            // Act
            settings.MaxImageDimensionForThumbnail = newValue;

            // Assert
            Assert.IsTrue(propertyChangedRaised, "PropertyChanged aurait dû être levé pour MaxImageDimensionForThumbnail.");
            Assert.AreEqual(nameof(ApplicationSettings.MaxImageDimensionForThumbnail), propertyName, "Le nom de la propriété changée devrait être MaxImageDimensionForThumbnail.");
            Assert.AreEqual(newValue, settings.MaxImageDimensionForThumbnail, "La propriété MaxImageDimensionForThumbnail aurait dû être mise à jour.");
        }

        [TestMethod]
        public void MaxTextPreviewLength_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            int newValue = 60;

            // Act
            settings.MaxTextPreviewLength = newValue;

            // Assert
            Assert.IsTrue(propertyChangedRaised, "PropertyChanged aurait dû être levé pour MaxTextPreviewLength.");
            Assert.AreEqual(nameof(ApplicationSettings.MaxTextPreviewLength), propertyName, "Le nom de la propriété changée devrait être MaxTextPreviewLength.");
            Assert.AreEqual(newValue, settings.MaxTextPreviewLength, "La propriété MaxTextPreviewLength aurait dû être mise à jour.");
        }

        [TestMethod]
        public void MaxStorableItemSizeBytes_WhenChanged_NotifiesPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool propertyChangedRaised = false;
            string? propertyName = null;
            settings.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                propertyName = e.PropertyName;
            };
            long newValue = 20 * 1024 * 1024; // 20 MB

            // Act
            settings.MaxStorableItemSizeBytes = newValue;

            // Assert
            Assert.IsTrue(propertyChangedRaised, "PropertyChanged aurait dû être levé pour MaxStorableItemSizeBytes.");
            Assert.AreEqual(nameof(ApplicationSettings.MaxStorableItemSizeBytes), propertyName, "Le nom de la propriété changée devrait être MaxStorableItemSizeBytes.");
            Assert.AreEqual(newValue, settings.MaxStorableItemSizeBytes, "La propriété MaxStorableItemSizeBytes aurait dû être mise à jour.");
        }

        // --- Tests pour les cas où les valeurs ne changent pas (branche SetProperty qui retourne false) ---

        [TestMethod]
        public void MaxHistoryItems_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int currentValue = settings.MaxHistoryItems;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.MaxHistoryItems = currentValue;

            // Assert
            Assert.IsFalse(propertyChangedRaised, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.AreEqual(currentValue, settings.MaxHistoryItems, "La valeur devrait rester inchangée.");
        }

        [TestMethod]
        public void ActiveThemePath_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            string currentValue = settings.ActiveThemePath;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.ActiveThemePath = currentValue;

            // Assert
            Assert.IsFalse(propertyChangedRaised, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.AreEqual(currentValue, settings.ActiveThemePath, "La valeur devrait rester inchangée.");
        }

        [TestMethod]
        public void ShortcutKeyCombination_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            string currentValue = settings.ShortcutKeyCombination;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.ShortcutKeyCombination = currentValue;

            // Assert
            Assert.IsFalse(propertyChangedRaised, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.AreEqual(currentValue, settings.ShortcutKeyCombination, "La valeur devrait rester inchangée.");
        }

        [TestMethod]
        public void StartWithWindows_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            bool currentValue = settings.StartWithWindows;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.StartWithWindows = currentValue;

            // Assert
            Assert.IsFalse(propertyChangedRaised, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.AreEqual(currentValue, settings.StartWithWindows, "La valeur devrait rester inchangée.");
        }

        [TestMethod]
        public void MaxImageDimensionForThumbnail_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int currentValue = settings.MaxImageDimensionForThumbnail;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.MaxImageDimensionForThumbnail = currentValue;

            // Assert
            Assert.IsFalse(propertyChangedRaised, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.AreEqual(currentValue, settings.MaxImageDimensionForThumbnail, "La valeur devrait rester inchangée.");
        }

        [TestMethod]
        public void MaxTextPreviewLength_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            int currentValue = settings.MaxTextPreviewLength;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.MaxTextPreviewLength = currentValue;

            // Assert
            Assert.IsFalse(propertyChangedRaised, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.AreEqual(currentValue, settings.MaxTextPreviewLength, "La valeur devrait rester inchangée.");
        }

        [TestMethod]
        public void MaxStorableItemSizeBytes_WhenSetToSameValue_DoesNotNotifyPropertyChanged()
        {
            // Arrange
            var settings = new ApplicationSettings();
            long currentValue = settings.MaxStorableItemSizeBytes;
            bool propertyChangedRaised = false;
            settings.PropertyChanged += (sender, e) => { propertyChangedRaised = true; };

            // Act - Définir la même valeur
            settings.MaxStorableItemSizeBytes = currentValue;

            // Assert
            Assert.IsFalse(propertyChangedRaised, "PropertyChanged ne devrait PAS être levé quand la valeur ne change pas.");
            Assert.AreEqual(currentValue, settings.MaxStorableItemSizeBytes, "La valeur devrait rester inchangée.");
        }

        // --- Tests pour le constructeur et l'initialisation ---

        [TestMethod]
        public void Constructor_InitializesActiveThemePathWithCorrectAssemblyName()
        {
            // Arrange & Act
            var settings = new ApplicationSettings();

            // Assert
            Assert.IsTrue(settings.ActiveThemePath.Contains("ClipboardPlus"),
                "ActiveThemePath devrait contenir le nom de l'assembly ClipboardPlus.");
            Assert.IsTrue(settings.ActiveThemePath.Contains("component/UI/Themes/Default.xaml"),
                "ActiveThemePath devrait contenir le chemin vers Default.xaml.");
            Assert.IsTrue(settings.ActiveThemePath.StartsWith("pack://application:,,,/"),
                "ActiveThemePath devrait commencer par le schéma pack URI.");
        }

        [TestMethod]
        public void Constructor_SetsAllPropertiesToExpectedDefaults()
        {
            // Arrange & Act
            var settings = new ApplicationSettings();

            // Assert - Vérifier toutes les valeurs par défaut
            Assert.AreEqual(50, settings.MaxHistoryItems, "MaxHistoryItems par défaut devrait être 50.");
            Assert.AreEqual(false, settings.StartWithWindows, "StartWithWindows par défaut devrait être false.");
            Assert.AreEqual(256, settings.MaxImageDimensionForThumbnail, "MaxImageDimensionForThumbnail par défaut devrait être 256.");
            Assert.AreEqual(30, settings.MaxTextPreviewLength, "MaxTextPreviewLength par défaut devrait être 30.");
            Assert.AreEqual(10 * 1024 * 1024, settings.MaxStorableItemSizeBytes, "MaxStorableItemSizeBytes par défaut devrait être 10MB.");
            Assert.AreEqual("Ctrl+Alt+V", settings.ShortcutKeyCombination, "ShortcutKeyCombination par défaut devrait être 'Ctrl+Alt+V'.");
            Assert.IsNotNull(settings.ActiveThemePath, "ActiveThemePath ne devrait pas être null.");
            Assert.IsTrue(settings.ActiveThemePath.Length > 0, "ActiveThemePath ne devrait pas être vide.");
        }

        [TestMethod]
        public void ApplicationSettings_ImplementsINotifyPropertyChanged()
        {
            // Arrange & Act
            var settings = new ApplicationSettings();

            // Assert
            Assert.IsInstanceOfType(settings, typeof(INotifyPropertyChanged),
                "ApplicationSettings devrait implémenter INotifyPropertyChanged.");
        }

        [TestMethod]
        public void ApplicationSettings_InheritsFromObservableObject()
        {
            // Arrange & Act
            var settings = new ApplicationSettings();

            // Assert
            Assert.IsTrue(typeof(ApplicationSettings).BaseType?.Name.Contains("ObservableObject") == true,
                "ApplicationSettings devrait hériter d'ObservableObject.");
        }
    }
}