using System;
using System.Collections.Generic;
using System.Globalization;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation du service de formatage des tailles de fichiers.
    /// Extrait la logique de FormatFileSize() du ContentPreviewViewModel.
    /// </summary>
    public class FileSizeFormatter : IFileSizeFormatter
    {
        private readonly ILoggingService? _loggingService;
        
        // Unités supportées avec leurs facteurs de conversion
        private static readonly Dictionary<string, long> UnitFactors = new()
        {
            { "B", 1L },
            { "KB", 1024L },
            { "MB", 1024L * 1024L },
            { "GB", 1024L * 1024L * 1024L },
            { "TB", 1024L * 1024L * 1024L * 1024L }
        };

        private static readonly string[] Units = { "B", "KB", "MB", "GB", "TB" };

        /// <summary>
        /// Initialise une nouvelle instance de FileSizeFormatter.
        /// </summary>
        /// <param name="loggingService">Service de logging optionnel</param>
        public FileSizeFormatter(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <inheritdoc />
        public string FormatSize(long bytes)
        {
            return FormatSize(bytes, 1);
        }

        /// <inheritdoc />
        public string FormatSize(long bytes, int decimalPlaces)
        {
            try
            {
                _loggingService?.LogInfo($"FileSizeFormatter.FormatSize - Formatage de {bytes} octets avec {decimalPlaces} décimales");

                if (bytes < 0)
                {
                    return "0 B";
                }

                if (bytes == 0)
                {
                    return "0 B";
                }

                // Trouve l'unité appropriée
                int unitIndex = 0;
                double size = bytes;

                while (size >= 1024 && unitIndex < Units.Length - 1)
                {
                    size /= 1024;
                    unitIndex++;
                }

                string unit = Units[unitIndex];
                string formattedSize = size.ToString($"F{decimalPlaces}", CultureInfo.InvariantCulture);
                string result = $"{formattedSize} {unit}";

                _loggingService?.LogInfo($"FileSizeFormatter.FormatSize - Résultat: {result}");

                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"FileSizeFormatter.FormatSize - Erreur lors du formatage de {bytes}: {ex.Message}", ex);
                return $"{bytes} B"; // Fallback
            }
        }

        /// <inheritdoc />
        public string GetAppropriateUnit(long bytes)
        {
            try
            {
                if (bytes < 0)
                {
                    return "B";
                }

                int unitIndex = 0;
                long size = bytes;

                while (size >= 1024 && unitIndex < Units.Length - 1)
                {
                    size /= 1024;
                    unitIndex++;
                }

                string unit = Units[unitIndex];
                
                _loggingService?.LogInfo($"FileSizeFormatter.GetAppropriateUnit - Unité appropriée pour {bytes} octets: {unit}");
                
                return unit;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"FileSizeFormatter.GetAppropriateUnit - Erreur lors de la détermination de l'unité pour {bytes}: {ex.Message}", ex);
                return "B"; // Fallback
            }
        }

        /// <inheritdoc />
        public double ConvertToUnit(long bytes, string unit)
        {
            ArgumentNullException.ThrowIfNull(unit);

            if (!UnitFactors.TryGetValue(unit.ToUpperInvariant(), out long factor))
            {
                throw new ArgumentException($"Unité non supportée: {unit}", nameof(unit));
            }

            try
            {
                double result = (double)bytes / factor;
                
                _loggingService?.LogInfo($"FileSizeFormatter.ConvertToUnit - Conversion de {bytes} octets vers {unit}: {result}");
                
                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"FileSizeFormatter.ConvertToUnit - Erreur lors de la conversion de {bytes} vers {unit}: {ex.Message}", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public bool IsSupportedUnit(string unit)
        {
            if (string.IsNullOrWhiteSpace(unit))
            {
                return false;
            }

            bool isSupported = UnitFactors.ContainsKey(unit.ToUpperInvariant());
            
            _loggingService?.LogInfo($"FileSizeFormatter.IsSupportedUnit - Unité {unit} supportée: {isSupported}");
            
            return isSupported;
        }

        /// <inheritdoc />
        public IEnumerable<string> GetSupportedUnits()
        {
            _loggingService?.LogInfo("FileSizeFormatter.GetSupportedUnits - Retour des unités supportées");
            
            return Units;
        }
    }
}
