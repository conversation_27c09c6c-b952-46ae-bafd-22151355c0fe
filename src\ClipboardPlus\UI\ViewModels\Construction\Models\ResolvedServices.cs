using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.Services.Deletion;

namespace ClipboardPlus.UI.ViewModels.Construction.Models
{
    /// <summary>
    /// DTO contenant tous les services résolus pour le ViewModel.
    /// Ce record encapsule tous les services optionnels et leurs dépendances
    /// pour simplifier la signature du constructeur et respecter le principe DIP.
    /// </summary>
    /// <param name="DeletionLogger">Logger pour les opérations de suppression (peut être null)</param>
    /// <param name="HealthService">Service de santé des collections (peut être null)</param>
    /// <param name="VisibilityManager">Gestionnaire de visibilité SOLID (peut être null)</param>
    /// <param name="NewItemOrchestrator">Orchestrateur de création d'éléments (peut être null)</param>
    /// <param name="TestDetector">Détecteur d'environnement de test (peut être null)</param>
    /// <param name="DeletionService">Service de suppression résolu via ServiceProvider (peut être null)</param>
    /// <param name="DeletionUIValidator">Validateur UI de suppression résolu via ServiceProvider (peut être null)</param>
    /// <param name="DeletionUIHandler">Gestionnaire UI de suppression résolu via ServiceProvider (peut être null)</param>
    /// <param name="DeletionUINotificationService">Service de notifications UI de suppression résolu via ServiceProvider (peut être null)</param>
    public record ResolvedServices(
        IDeletionResultLogger? DeletionLogger,
        ICollectionHealthService? HealthService,
        IVisibilityStateManager? VisibilityManager,
        INewItemCreationOrchestrator? NewItemOrchestrator,
        ITestEnvironmentDetector? TestDetector,
        IDeletionService? DeletionService,
        IDeletionUIValidator? DeletionUIValidator,
        IDeletionUIHandler? DeletionUIHandler,
        IDeletionUINotificationService? DeletionUINotificationService);
}
