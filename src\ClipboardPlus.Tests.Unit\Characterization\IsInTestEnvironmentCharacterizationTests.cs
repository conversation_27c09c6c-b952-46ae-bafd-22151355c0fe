using System;
using System.Diagnostics;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Tests.Unit.Helpers;
using ClipboardPlus.UI.ViewModels;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Characterization
{
    /// <summary>
    /// Tests de caractérisation pour IsInTestEnvironment() - Capture du comportement actuel
    /// Ces tests documentent le comportement existant avant refactoring
    /// </summary>
    [TestFixture]
    [Category("Characterization")]
    [Category("IsInTestEnvironment")]
    public class IsInTestEnvironmentCharacterizationTests
    {
        private ClipboardHistoryViewModel _viewModel = null!;

        [SetUp]
        public void SetUp()
        {
            // Utilisation du helper existant pour créer le ViewModel
            _viewModel = ClipboardHistoryViewModelTestHelper.CreateViewModel();
        }

        [TearDown]
        public void TearDown()
        {
            _viewModel?.Dispose();
        }

        [Test]
        [Description("Caractérisation : IsInTestEnvironment retourne true dans un contexte de test NUnit")]
        public void IsInTestEnvironment_InNUnitContext_ReturnsTrue()
        {
            // Act
            var result = _viewModel.IsInTestEnvironment();

            // Assert
            Assert.That(result, Is.True, "IsInTestEnvironment devrait retourner true dans un contexte de test NUnit");
        }

        [Test]
        [Description("Caractérisation : IsInTestEnvironment est cohérent entre plusieurs appels")]
        public void IsInTestEnvironment_MultipleCallsInSameContext_ReturnsConsistentResult()
        {
            // Act
            var result1 = _viewModel.IsInTestEnvironment();
            var result2 = _viewModel.IsInTestEnvironment();
            var result3 = _viewModel.IsInTestEnvironment();

            // Assert
            Assert.That(result1, Is.EqualTo(result2), "Les appels multiples devraient retourner le même résultat");
            Assert.That(result2, Is.EqualTo(result3), "Les appels multiples devraient retourner le même résultat");
        }

        [Test]
        [Description("Caractérisation : IsInTestEnvironment avec Debugger.IsAttached")]
        public void IsInTestEnvironment_WithDebuggerAttached_BehaviorDocumented()
        {
            // Arrange
            var debuggerAttached = Debugger.IsAttached;

            // Act
            var result = _viewModel.IsInTestEnvironment();

            // Assert & Documentation
            TestContext.WriteLine($"Debugger.IsAttached: {debuggerAttached}");
            TestContext.WriteLine($"IsInTestEnvironment result: {result}");
            
            // Dans un contexte de test, on s'attend à true
            Assert.That(result, Is.True, "Dans un contexte de test NUnit, devrait retourner true");
        }

        [Test]
        [Description("Caractérisation : Performance baseline de IsInTestEnvironment")]
        public void IsInTestEnvironment_PerformanceBaseline()
        {
            // Arrange
            const int iterations = 100;

            // Act
            var stopwatch = Stopwatch.StartNew();
            for (int i = 0; i < iterations; i++)
            {
                _viewModel.IsInTestEnvironment();
            }
            stopwatch.Stop();

            // Assert & Documentation
            var averageMs = stopwatch.ElapsedMilliseconds / (double)iterations;
            TestContext.WriteLine($"Performance baseline: {averageMs:F2}ms par appel sur {iterations} itérations");
            
            // Baseline documentée - ne devrait pas être trop lent
            Assert.That(averageMs, Is.LessThan(10.0), "IsInTestEnvironment ne devrait pas être trop lent");
        }

        [Test]
        [Description("Caractérisation : IsInTestEnvironment avec différents ViewModels")]
        public void IsInTestEnvironment_WithDifferentViewModelInstances_ConsistentBehavior()
        {
            // Arrange
            using var viewModel2 = ClipboardHistoryViewModelTestHelper.CreateViewModel();
            using var viewModel3 = ClipboardHistoryViewModelTestHelper.CreateViewModel();

            // Act
            var result1 = _viewModel.IsInTestEnvironment();
            var result2 = viewModel2.IsInTestEnvironment();
            var result3 = viewModel3.IsInTestEnvironment();

            // Assert
            Assert.That(result1, Is.EqualTo(result2), "Différentes instances devraient avoir le même comportement");
            Assert.That(result2, Is.EqualTo(result3), "Différentes instances devraient avoir le même comportement");
        }
    }
}
