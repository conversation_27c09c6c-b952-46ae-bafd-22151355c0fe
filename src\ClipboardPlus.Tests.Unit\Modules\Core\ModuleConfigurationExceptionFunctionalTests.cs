using NUnit.Framework;
using ClipboardPlus.Modules.Core;
using System;
using ModuleState = ClipboardPlus.Modules.Core.ModuleState;

namespace ClipboardPlus.Tests.Unit.Modules.Core
{
    /// <summary>
    /// Tests fonctionnels pour ModuleConfigurationException - vérifient le comportement métier
    /// dans des scénarios d'usage réels de gestion d'erreurs de configuration
    /// </summary>
    [TestFixture]
    public class ModuleConfigurationExceptionFunctionalTests
    {
        #region Scénarios métier de configuration invalide

        [Test]
        public void ModuleConfigurationException_InvalidDatabaseConnectionString_ShouldCaptureConfigurationError()
        {
            // Arrange - Scénario : Chaîne de connexion base de données invalide
            var moduleName = "PersistenceModule";
            var moduleState = ModuleState.Initializing;
            var configKey = "DatabaseConnectionString";
            var message = "Database connection string is malformed: missing server parameter";

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message);

            // Assert - Vérifier que l'erreur de configuration DB est capturée
            Assert.That(exception.ModuleName, Is.EqualTo("PersistenceModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Initializing));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("DatabaseConnectionString"));
            Assert.That(exception.Message, Is.EqualTo(message));
            Assert.That(exception.InnerException, Is.Null);
            
            // Vérifier l'héritage de ModuleException
            Assert.That(exception, Is.InstanceOf<ModuleException>());
            Assert.That(exception, Is.InstanceOf<Exception>());
        }

        [Test]
        public void ModuleConfigurationException_InvalidClipboardRetentionPolicy_ShouldPreventDataLoss()
        {
            // Arrange - Scénario : Politique de rétention invalide risquant perte de données
            var moduleName = "HistoryModule";
            var moduleState = ModuleState.Running;
            var configKey = "ClipboardRetentionDays";
            var message = "Clipboard retention policy cannot be negative (-30 days). This would cause immediate data deletion.";
            var innerException = new ArgumentOutOfRangeException("retentionDays", -30, "Retention days must be positive");

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message, innerException);

            // Assert - Vérifier que la politique invalide est détectée
            Assert.That(exception.ModuleName, Is.EqualTo("HistoryModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Running));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("ClipboardRetentionDays"));
            Assert.That(exception.Message, Does.Contain("immediate data deletion"));
            Assert.That(exception.InnerException, Is.InstanceOf<ArgumentOutOfRangeException>());
            
            // Vérifier que l'exception interne contient les détails
            var innerArg = exception.InnerException as ArgumentOutOfRangeException;
            Assert.That(innerArg?.ParamName, Is.EqualTo("retentionDays"));
            Assert.That(innerArg?.ActualValue, Is.EqualTo(-30));
        }

        [Test]
        public void ModuleConfigurationException_InvalidMemoryLimits_ShouldPreventSystemInstability()
        {
            // Arrange - Scénario : Limites mémoire invalides risquant instabilité système
            var moduleName = "DataProcessingModule";
            var moduleState = ModuleState.Starting;
            var configKey = "MaxMemoryUsageMB";
            var message = "Memory limit configuration exceeds system capacity (16GB requested, 8GB available). This could cause system instability.";

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message);

            // Assert - Vérifier que les limites mémoire invalides sont détectées
            Assert.That(exception.ModuleName, Is.EqualTo("DataProcessingModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Starting));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("MaxMemoryUsageMB"));
            Assert.That(exception.Message, Does.Contain("system instability"));
            Assert.That(exception.Message, Does.Contain("16GB requested, 8GB available"));
        }

        [Test]
        public void ModuleConfigurationException_InvalidSecuritySettings_ShouldPreventSecurityBreach()
        {
            // Arrange - Scénario : Paramètres de sécurité invalides risquant faille
            var moduleName = "SecurityModule";
            var moduleState = ModuleState.Initialized;
            var configKey = "EncryptionKeyLength";
            var message = "Encryption key length is too weak (64 bits). Minimum required is 256 bits for clipboard data security.";
            var innerException = new ArgumentException("Key length must be at least 256 bits for AES encryption");

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message, innerException);

            // Assert - Vérifier que les paramètres de sécurité faibles sont détectés
            Assert.That(exception.ModuleName, Is.EqualTo("SecurityModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Initialized));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("EncryptionKeyLength"));
            Assert.That(exception.Message, Does.Contain("too weak"));
            Assert.That(exception.Message, Does.Contain("256 bits"));
            Assert.That(exception.InnerException, Is.InstanceOf<ArgumentException>());
        }

        #endregion

        #region Scénarios de validation de configuration

        [Test]
        public void ModuleConfigurationException_MissingRequiredConfiguration_ShouldPreventModuleStart()
        {
            // Arrange - Scénario : Configuration requise manquante empêchant démarrage
            var moduleName = "NetworkSyncModule";
            var moduleState = ModuleState.Created;
            var configKey = "CloudServiceEndpoint";
            var message = "Required configuration 'CloudServiceEndpoint' is missing. Module cannot start without cloud service URL.";

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message);

            // Assert - Vérifier que la configuration manquante empêche le démarrage
            Assert.That(exception.ModuleName, Is.EqualTo("NetworkSyncModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Created));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("CloudServiceEndpoint"));
            Assert.That(exception.Message, Does.Contain("cannot start"));
            Assert.That(exception.Message, Does.Contain("missing"));
        }

        [Test]
        public void ModuleConfigurationException_ConflictingConfigurations_ShouldDetectInconsistency()
        {
            // Arrange - Scénario : Configurations conflictuelles détectées
            var moduleName = "UIModule";
            var moduleState = ModuleState.Running;
            var configKey = "AutoSaveInterval";
            var message = "Configuration conflict detected: AutoSaveInterval (30s) is shorter than DatabaseCommitInterval (60s). This will cause performance issues.";

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message);

            // Assert - Vérifier que le conflit de configuration est détecté
            Assert.That(exception.ModuleName, Is.EqualTo("UIModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Running));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("AutoSaveInterval"));
            Assert.That(exception.Message, Does.Contain("conflict detected"));
            Assert.That(exception.Message, Does.Contain("performance issues"));
        }

        [Test]
        public void ModuleConfigurationException_InvalidFilePathConfiguration_ShouldPreventFileSystemErrors()
        {
            // Arrange - Scénario : Chemin de fichier invalide dans configuration
            var moduleName = "LoggingModule";
            var moduleState = ModuleState.Initializing;
            var configKey = "LogFilePath";
            var message = "Log file path contains invalid characters: 'C:\\logs\\app<>log.txt'. This will cause file system errors.";
            var innerException = new ArgumentException("Path contains invalid characters: <>", "logPath");

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message, innerException);

            // Assert - Vérifier que le chemin invalide est détecté
            Assert.That(exception.ModuleName, Is.EqualTo("LoggingModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Initializing));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("LogFilePath"));
            Assert.That(exception.Message, Does.Contain("invalid characters"));
            Assert.That(exception.Message, Does.Contain("file system errors"));
            Assert.That(exception.InnerException, Is.InstanceOf<ArgumentException>());
        }

        #endregion

        #region Scénarios de récupération de configuration

        [Test]
        public void ModuleConfigurationException_CorruptedConfigurationFile_ShouldEnableConfigRecovery()
        {
            // Arrange - Scénario : Fichier de configuration corrompu nécessitant récupération
            var moduleName = "ConfigurationModule";
            var moduleState = ModuleState.Error;
            var configKey = "UserPreferences";
            var message = "Configuration file is corrupted and cannot be parsed. Backup configuration will be used.";
            var innerException = new FormatException("Invalid JSON format in configuration file");

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message, innerException);

            // Assert - Vérifier que la corruption permet la récupération
            Assert.That(exception.ModuleName, Is.EqualTo("ConfigurationModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Error));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("UserPreferences"));
            Assert.That(exception.Message, Does.Contain("corrupted"));
            Assert.That(exception.Message, Does.Contain("Backup configuration"));
            Assert.That(exception.InnerException, Is.InstanceOf<FormatException>());
        }

        [Test]
        public void ModuleConfigurationException_VersionMismatchConfiguration_ShouldTriggerMigration()
        {
            // Arrange - Scénario : Version de configuration incompatible nécessitant migration
            var moduleName = "MigrationModule";
            var moduleState = ModuleState.Initializing;
            var configKey = "ConfigurationVersion";
            var message = "Configuration version mismatch: found v1.2, expected v2.0. Automatic migration required.";

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message);

            // Assert - Vérifier que la migration est déclenchée
            Assert.That(exception.ModuleName, Is.EqualTo("MigrationModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Initializing));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("ConfigurationVersion"));
            Assert.That(exception.Message, Does.Contain("version mismatch"));
            Assert.That(exception.Message, Does.Contain("migration required"));
        }

        #endregion

        #region Scénarios de diagnostic de configuration

        [Test]
        public void ModuleConfigurationException_PerformanceImpactingConfiguration_ShouldWarnAboutPerformance()
        {
            // Arrange - Scénario : Configuration impactant les performances
            var moduleName = "PerformanceModule";
            var moduleState = ModuleState.Running;
            var configKey = "ClipboardScanInterval";
            var message = "Clipboard scan interval is set too low (10ms). This may cause high CPU usage and impact system performance.";

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message);

            // Assert - Vérifier que l'impact performance est signalé
            Assert.That(exception.ModuleName, Is.EqualTo("PerformanceModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Running));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("ClipboardScanInterval"));
            Assert.That(exception.Message, Does.Contain("too low"));
            Assert.That(exception.Message, Does.Contain("high CPU usage"));
            Assert.That(exception.Message, Does.Contain("impact system performance"));
        }

        [Test]
        public void ModuleConfigurationException_ResourceLimitConfiguration_ShouldPreventResourceExhaustion()
        {
            // Arrange - Scénario : Limites de ressources mal configurées
            var moduleName = "ResourceManagementModule";
            var moduleState = ModuleState.Starting;
            var configKey = "MaxConcurrentOperations";
            var message = "Maximum concurrent operations is set too high (1000). This exceeds system thread pool capacity and may cause resource exhaustion.";

            // Act
            var exception = new ModuleConfigurationException(moduleName, moduleState, configKey, message);

            // Assert - Vérifier que l'épuisement des ressources est prévenu
            Assert.That(exception.ModuleName, Is.EqualTo("ResourceManagementModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Starting));
            Assert.That(exception.ConfigurationKey, Is.EqualTo("MaxConcurrentOperations"));
            Assert.That(exception.Message, Does.Contain("too high"));
            Assert.That(exception.Message, Does.Contain("resource exhaustion"));
        }

        #endregion
    }
}
