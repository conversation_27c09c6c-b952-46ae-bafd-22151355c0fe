using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Modules.Core;

namespace ClipboardPlus.Modules.Creation
{
    /// <summary>
    /// Interface pour le module de création de nouveaux éléments.
    /// 
    /// Ce module gère le processus de création de nouveaux éléments dans l'historique,
    /// incluant la validation, la préparation et la finalisation.
    /// </summary>
    public interface ICreationModule : ClipboardPlus.Modules.Core.IModule
    {
        /// <summary>
        /// Contenu du nouvel élément en cours de création.
        /// </summary>
        string? NewItemContent { get; set; }

        /// <summary>
        /// Indique si un élément est actuellement en cours de création.
        /// </summary>
        bool IsCreatingNewItem { get; }

        /// <summary>
        /// Indique si le contenu actuel est valide pour créer un élément.
        /// </summary>
        bool IsContentValid { get; }

        /// <summary>
        /// État actuel du processus de création.
        /// </summary>
        CreationState CurrentState { get; }

        /// <summary>
        /// Métadonnées du nouvel élément en cours de création.
        /// </summary>
        CreationMetadata? CreationMetadata { get; }

        /// <summary>
        /// Événement déclenché lorsque l'état de création change.
        /// </summary>
        new event EventHandler<CreationStateChangedEventArgs> StateChanged;

        /// <summary>
        /// Événement déclenché lorsque le contenu change.
        /// </summary>
        event EventHandler<CreationContentChangedEventArgs> ContentChanged;

        /// <summary>
        /// Événement déclenché lorsqu'un élément est créé avec succès.
        /// </summary>
        event EventHandler<ItemCreatedEventArgs> ItemCreated;

        /// <summary>
        /// Événement déclenché lorsque la création est annulée.
        /// </summary>
        event EventHandler<CreationCancelledEventArgs> CreationCancelled;

        /// <summary>
        /// Démarre le processus de création d'un nouvel élément.
        /// </summary>
        /// <param name="initialContent">Contenu initial (optionnel)</param>
        /// <returns>Task représentant l'opération de démarrage</returns>
        Task StartCreationAsync(string? initialContent = null);

        /// <summary>
        /// Met à jour le contenu du nouvel élément.
        /// </summary>
        /// <param name="content">Nouveau contenu</param>
        /// <returns>Task représentant l'opération de mise à jour</returns>
        Task UpdateContentAsync(string? content);

        /// <summary>
        /// Valide le contenu actuel.
        /// </summary>
        /// <returns>Résultat de la validation</returns>
        Task<CreationValidationResult> ValidateContentAsync();

        /// <summary>
        /// Finalise et sauvegarde le nouvel élément.
        /// </summary>
        /// <returns>Task représentant l'opération de finalisation</returns>
        Task<ClipboardItem> FinalizeAndSaveAsync();

        /// <summary>
        /// Annule le processus de création.
        /// </summary>
        /// <param name="reason">Raison de l'annulation</param>
        /// <returns>Task représentant l'opération d'annulation</returns>
        Task CancelCreationAsync(string? reason = null);

        /// <summary>
        /// Réinitialise le module de création.
        /// </summary>
        /// <returns>Task représentant l'opération de réinitialisation</returns>
        Task ResetCreationAsync();

        /// <summary>
        /// Vérifie si la création peut être finalisée.
        /// </summary>
        /// <returns>True si la création peut être finalisée</returns>
        bool CanFinalize();

        /// <summary>
        /// Vérifie si la création peut être annulée.
        /// </summary>
        /// <returns>True si la création peut être annulée</returns>
        bool CanCancel();

        /// <summary>
        /// Obtient les statistiques de création.
        /// </summary>
        /// <returns>Statistiques de création</returns>
        CreationStatistics GetStatistics();
    }

    /// <summary>
    /// États du processus de création.
    /// </summary>
    public enum CreationState
    {
        /// <summary>
        /// Aucune création en cours.
        /// </summary>
        Idle,

        /// <summary>
        /// Création initialisée.
        /// </summary>
        Initialized,

        /// <summary>
        /// Contenu en cours de saisie.
        /// </summary>
        EditingContent,

        /// <summary>
        /// Validation en cours.
        /// </summary>
        Validating,

        /// <summary>
        /// Contenu valide, prêt à finaliser.
        /// </summary>
        ReadyToFinalize,

        /// <summary>
        /// Finalisation en cours.
        /// </summary>
        Finalizing,

        /// <summary>
        /// Création terminée avec succès.
        /// </summary>
        Completed,

        /// <summary>
        /// Annulation en cours.
        /// </summary>
        Cancelling,

        /// <summary>
        /// Création annulée.
        /// </summary>
        Cancelled,

        /// <summary>
        /// Erreur pendant la création.
        /// </summary>
        Error
    }

    /// <summary>
    /// Métadonnées de création d'un élément.
    /// </summary>
    public class CreationMetadata
    {
        /// <summary>
        /// Horodatage de début de création.
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Source de la création (manuelle, automatique, etc.).
        /// </summary>
        public string Source { get; set; } = "Manual";

        /// <summary>
        /// Taille du contenu en caractères.
        /// </summary>
        public int ContentLength { get; set; }

        /// <summary>
        /// Type de contenu détecté.
        /// </summary>
        public string? ContentType { get; set; }

        /// <summary>
        /// Propriétés additionnelles.
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// Résultat de validation de création.
    /// </summary>
    public class CreationValidationResult
    {
        /// <summary>
        /// Indique si la validation a réussi.
        /// </summary>
        public bool IsValid { get; }

        /// <summary>
        /// Messages de validation.
        /// </summary>
        public IReadOnlyList<string> Messages { get; }

        /// <summary>
        /// Erreurs de validation.
        /// </summary>
        public IReadOnlyList<string> Errors { get; }

        /// <summary>
        /// Avertissements de validation.
        /// </summary>
        public IReadOnlyList<string> Warnings { get; }

        public CreationValidationResult(bool isValid, IEnumerable<string>? messages = null, 
            IEnumerable<string>? errors = null, IEnumerable<string>? warnings = null)
        {
            IsValid = isValid;
            Messages = (messages ?? Enumerable.Empty<string>()).ToList().AsReadOnly();
            Errors = (errors ?? Enumerable.Empty<string>()).ToList().AsReadOnly();
            Warnings = (warnings ?? Enumerable.Empty<string>()).ToList().AsReadOnly();
        }

        /// <summary>
        /// Crée un résultat de validation réussi.
        /// </summary>
        public static CreationValidationResult Success(string? message = null) =>
            new CreationValidationResult(true, message != null ? new[] { message } : null);

        /// <summary>
        /// Crée un résultat de validation échoué.
        /// </summary>
        public static CreationValidationResult Failure(IEnumerable<string> errors) =>
            new CreationValidationResult(false, errors: errors);

        /// <summary>
        /// Crée un résultat de validation échoué avec un seul message d'erreur.
        /// </summary>
        public static CreationValidationResult Failure(string error) =>
            new CreationValidationResult(false, errors: new[] { error });
    }

    /// <summary>
    /// Statistiques de création.
    /// </summary>
    public class CreationStatistics
    {
        /// <summary>
        /// Nombre total de créations tentées.
        /// </summary>
        public int TotalAttempts { get; set; }

        /// <summary>
        /// Nombre de créations réussies.
        /// </summary>
        public int SuccessfulCreations { get; set; }

        /// <summary>
        /// Nombre de créations annulées.
        /// </summary>
        public int CancelledCreations { get; set; }

        /// <summary>
        /// Nombre de créations échouées.
        /// </summary>
        public int FailedCreations { get; set; }

        /// <summary>
        /// Temps moyen de création.
        /// </summary>
        public TimeSpan AverageCreationTime { get; set; }

        /// <summary>
        /// Dernière création.
        /// </summary>
        public DateTime? LastCreationTime { get; set; }

        /// <summary>
        /// Taux de succès en pourcentage.
        /// </summary>
        public double SuccessRate => TotalAttempts > 0 ? (double)SuccessfulCreations / TotalAttempts * 100 : 0;
    }

    /// <summary>
    /// Arguments d'événement pour les changements d'état de création.
    /// </summary>
    public class CreationStateChangedEventArgs : EventArgs
    {
        public CreationState PreviousState { get; }
        public CreationState NewState { get; }
        public string? Reason { get; }

        public CreationStateChangedEventArgs(CreationState previousState, CreationState newState, string? reason = null)
        {
            PreviousState = previousState;
            NewState = newState;
            Reason = reason;
        }
    }

    /// <summary>
    /// Arguments d'événement pour les changements de contenu.
    /// </summary>
    public class CreationContentChangedEventArgs : EventArgs
    {
        public string? PreviousContent { get; }
        public string? NewContent { get; }
        public bool IsValid { get; }

        public CreationContentChangedEventArgs(string? previousContent, string? newContent, bool isValid)
        {
            PreviousContent = previousContent;
            NewContent = newContent;
            IsValid = isValid;
        }
    }

    /// <summary>
    /// Arguments d'événement pour un élément créé.
    /// </summary>
    public class ItemCreatedEventArgs : EventArgs
    {
        public ClipboardItem CreatedItem { get; }
        public CreationMetadata Metadata { get; }
        public TimeSpan CreationTime { get; }

        public ItemCreatedEventArgs(ClipboardItem createdItem, CreationMetadata metadata, TimeSpan creationTime)
        {
            CreatedItem = createdItem;
            Metadata = metadata;
            CreationTime = creationTime;
        }
    }

    /// <summary>
    /// Arguments d'événement pour une création annulée.
    /// </summary>
    public class CreationCancelledEventArgs : EventArgs
    {
        public string? Reason { get; }
        public string? PartialContent { get; }
        public TimeSpan ElapsedTime { get; }

        public CreationCancelledEventArgs(string? reason, string? partialContent, TimeSpan elapsedTime)
        {
            Reason = reason;
            PartialContent = partialContent;
            ElapsedTime = elapsedTime;
        }
    }
}
