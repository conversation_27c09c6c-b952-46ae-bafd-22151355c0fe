using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Implémentation de la gestion des notifications d'événements de suppression
    /// Responsabilité : Déclencher les événements et notifications appropriés
    /// </summary>
    public class DeletionNotificationService : IDeletionNotificationService
    {
        private readonly ILoggingService _loggingService;
        private readonly Action? _historyChangedCallback;

        /// <summary>
        /// Constructeur avec injection de dépendances
        /// </summary>
        /// <param name="loggingService">Service de logging</param>
        /// <param name="historyChangedCallback">Callback pour l'événement HistoryChanged (optionnel)</param>
        public DeletionNotificationService(ILoggingService loggingService, Action? historyChangedCallback = null)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _historyChangedCallback = historyChangedCallback;
        }

        /// <summary>
        /// Déclenche les notifications appropriées basées sur le résultat global
        /// </summary>
        /// <param name="globalResult">Résultat global de l'opération de suppression</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Résultat des notifications</returns>
        public async Task<NotificationResult> NotifyAsync(GlobalDeletionResult globalResult, string operationId)
        {
            var messages = new List<string>();
            var errors = new List<string>();
            bool historyChangedTriggered = false;
            bool detailsLogged = false;

            try
            {
                _loggingService.LogDebug($"[{operationId}] Début des notifications");

                // Log des détails
                try
                {
                    await LogDeletionDetailsAsync(globalResult, operationId);
                    detailsLogged = true;
                    messages.Add("Détails loggés avec succès");
                }
                catch (Exception ex)
                {
                    errors.Add($"Erreur lors du logging des détails: {ex.Message}");
                    _loggingService.LogError($"[{operationId}] Erreur logging détails: {ex.Message}", ex);
                }

                // Déclenchement de l'événement HistoryChanged
                try
                {
                    historyChangedTriggered = await TriggerHistoryChangedEventAsync(globalResult.ShouldNotify, operationId);
                    if (historyChangedTriggered)
                    {
                        messages.Add("Événement HistoryChanged déclenché");
                    }
                    else
                    {
                        messages.Add("Événement HistoryChanged non déclenché (non requis)");
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"Erreur lors du déclenchement HistoryChanged: {ex.Message}");
                    _loggingService.LogError($"[{operationId}] Erreur HistoryChanged: {ex.Message}", ex);
                }

                _loggingService.LogDebug($"[{operationId}] Notifications terminées");

                if (errors.Count == 0)
                {
                    return NotificationResult.CreateSuccess(historyChangedTriggered, detailsLogged, messages.ToArray());
                }
                else
                {
                    return NotificationResult.CreateFailure(errors.ToArray(), messages.ToArray());
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Erreur générale dans les notifications: {ex.Message}");
                _loggingService.LogError($"[{operationId}] Erreur générale notifications: {ex.Message}", ex);
                return NotificationResult.CreateFailure(errors.ToArray(), messages.ToArray());
            }
        }

        /// <summary>
        /// Déclenche l'événement HistoryChanged si nécessaire
        /// </summary>
        /// <param name="shouldNotify">Indique si la notification doit être déclenchée</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>True si l'événement a été déclenché</returns>
        public Task<bool> TriggerHistoryChangedEventAsync(bool shouldNotify, string operationId)
        {
            if (!shouldNotify)
            {
                _loggingService.LogDebug($"[{operationId}] HistoryChanged non déclenché (shouldNotify = false)");
                return Task.FromResult(false);
            }

            if (_historyChangedCallback == null)
            {
                _loggingService.LogDebug($"[{operationId}] HistoryChanged non déclenché (callback null)");
                return Task.FromResult(false);
            }

            try
            {
                _historyChangedCallback.Invoke();
                _loggingService.LogInfo($"[{operationId}] Événement HistoryChanged déclenché avec succès");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"[{operationId}] Erreur lors du déclenchement HistoryChanged: {ex.Message}", ex);
                return Task.FromException<bool>(ex);
            }
        }

        /// <summary>
        /// Log les détails de l'opération de suppression
        /// </summary>
        /// <param name="globalResult">Résultat global de l'opération</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Task représentant l'opération de logging</returns>
        public async Task LogDeletionDetailsAsync(GlobalDeletionResult globalResult, string operationId)
        {
            if (globalResult.Success)
            {
                _loggingService.LogInfo($"[{operationId}] === DÉTAILS SUPPRESSION ===");
                _loggingService.LogInfo($"[{operationId}] Résultat global: SUCCÈS");
                _loggingService.LogInfo($"[{operationId}] Message: {globalResult.Message}");

                // Détails mémoire
                _loggingService.LogInfo($"[{operationId}] Mémoire - Succès: {globalResult.MemoryResult.Success}, Existait: {globalResult.MemoryResult.ExistedInMemory}");
                if (!string.IsNullOrEmpty(globalResult.MemoryResult.Message))
                {
                    _loggingService.LogInfo($"[{operationId}] Mémoire - Message: {globalResult.MemoryResult.Message}");
                }

                // Détails BDD
                _loggingService.LogInfo($"[{operationId}] BDD - Succès: {globalResult.DatabaseResult.Success}, Tentatives: {globalResult.DatabaseResult.AttemptsCount}");
                _loggingService.LogInfo($"[{operationId}] BDD - Erreurs: {globalResult.DatabaseResult.HadErrors}, Durée: {globalResult.DatabaseResult.TotalElapsed.TotalMilliseconds}ms");

                // Cohérence
                _loggingService.LogInfo($"[{operationId}] Cohérence: {(globalResult.Consistency.IsConsistent ? "OK" : "PROBLÈME")} - {globalResult.Consistency.Message}");

                _loggingService.LogInfo($"[{operationId}] Notification requise: {globalResult.ShouldNotify}");
                _loggingService.LogInfo($"[{operationId}] === FIN DÉTAILS ===");
            }
            else
            {
                _loggingService.LogWarning($"[{operationId}] === DÉTAILS SUPPRESSION ===");
                _loggingService.LogWarning($"[{operationId}] Résultat global: ÉCHEC");
                _loggingService.LogWarning($"[{operationId}] Message: {globalResult.Message}");

                // Détails mémoire
                _loggingService.LogWarning($"[{operationId}] Mémoire - Succès: {globalResult.MemoryResult.Success}, Existait: {globalResult.MemoryResult.ExistedInMemory}");
                if (!string.IsNullOrEmpty(globalResult.MemoryResult.Message))
                {
                    _loggingService.LogWarning($"[{operationId}] Mémoire - Message: {globalResult.MemoryResult.Message}");
                }

                // Détails BDD
                _loggingService.LogWarning($"[{operationId}] BDD - Succès: {globalResult.DatabaseResult.Success}, Tentatives: {globalResult.DatabaseResult.AttemptsCount}");
                _loggingService.LogWarning($"[{operationId}] BDD - Erreurs: {globalResult.DatabaseResult.HadErrors}, Durée: {globalResult.DatabaseResult.TotalElapsed.TotalMilliseconds}ms");

                // Cohérence
                _loggingService.LogWarning($"[{operationId}] Cohérence: {(globalResult.Consistency.IsConsistent ? "OK" : "PROBLÈME")} - {globalResult.Consistency.Message}");

                _loggingService.LogWarning($"[{operationId}] Notification requise: {globalResult.ShouldNotify}");
                _loggingService.LogWarning($"[{operationId}] === FIN DÉTAILS ===");
            }

            await Task.CompletedTask;
        }
    }
}
