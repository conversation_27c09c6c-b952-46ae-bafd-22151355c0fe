using System;
using System.Diagnostics;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation de l'orchestrateur principal pour le traitement des changements d'historique.
    /// 
    /// Cette classe coordonne tous les services spécialisés pour reproduire fidèlement
    /// la logique de ClipboardHistoryManager_HistoryChanged tout en la rendant modulaire et testable.
    /// </summary>
    public class HistoryChangeOrchestrator : IHistoryChangeOrchestrator
    {
        private readonly IHistoryChangeValidator _validator;
        private readonly IHistoryChangeThreadingService _threadingService;
        private readonly IHistorySynchronizationService _synchronizationService;
        private readonly IHistoryMaintenanceService _maintenanceService;
        private readonly ILoggingService _loggingService;
        private readonly HistoryChangeStatistics _statistics;

        /// <summary>
        /// Initialise une nouvelle instance de l'orchestrateur.
        /// </summary>
        public HistoryChangeOrchestrator(
            IHistoryChangeValidator validator,
            IHistoryChangeThreadingService threadingService,
            IHistorySynchronizationService synchronizationService,
            IHistoryMaintenanceService maintenanceService,
            ILoggingService loggingService)
        {
            _validator = validator ?? throw new ArgumentNullException(nameof(validator));
            _threadingService = threadingService ?? throw new ArgumentNullException(nameof(threadingService));
            _synchronizationService = synchronizationService ?? throw new ArgumentNullException(nameof(synchronizationService));
            _maintenanceService = maintenanceService ?? throw new ArgumentNullException(nameof(maintenanceService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _statistics = new HistoryChangeStatistics();
        }

        /// <summary>
        /// Traite un événement de changement d'historique en coordonnant tous les services.
        /// 
        /// Cette méthode reproduit exactement la logique de ClipboardHistoryManager_HistoryChanged :
        /// 1. Génération d'un ID d'événement
        /// 2. Validation des conditions préalables
        /// 3. Gestion spéciale pour les tests
        /// 4. Assurance d'exécution sur le thread UI
        /// 5. Synchronisation des collections
        /// 6. Maintenance périodique
        /// </summary>
        public async Task<HistoryChangeResult> HandleHistoryChangedAsync(HistoryChangedEventArgs args)
        {
            var stopwatch = Stopwatch.StartNew();
            string eventId = GenerateEventId();
            
            // Mettre à jour l'ID d'événement dans les contextes
            args.EventId = eventId;
            args.SyncContext.EventId = eventId;

            try
            {
                _loggingService.LogInfo($"[DÉBUT] HistoryChanged [{eventId}] - ThreadID: {Environment.CurrentManagedThreadId}");
                _statistics.TotalEvents++;

                // Étape 1: Validation des conditions préalables
                var validationResult = _validator.ValidateHistoryChange(args.Context);
                if (!validationResult.IsValid)
                {
                    _loggingService.LogInfo($"HistoryChanged [{eventId}]: Ignoré - {validationResult.Reason}");
                    _statistics.IgnoredEvents++;
                    return HistoryChangeResult.Ignored(eventId, validationResult.Reason!);
                }

                // Étape 2: Gestion spéciale pour les tests (reproduire la logique originale)
                if (args.Context.IsInTestEnvironment)
                {
                    _loggingService.LogInfo($"HistoryChanged [{eventId}]: Mode test détecté, rechargement direct");
                    await args.LoadHistoryAction();
                    _statistics.SuccessfulEvents++;
                    return HistoryChangeResult.Succeeded(eventId, "Traitement en mode test terminé");
                }

                // Étape 3: Assurer l'exécution sur le thread UI
                bool canExecuteDirectly = _threadingService.EnsureUIThread(
                    () => ExecuteSynchronizationLogic(args, eventId),
                    eventId);

                if (!canExecuteDirectly)
                {
                    // L'action a été redirigée vers le thread UI
                    _loggingService.LogInfo($"HistoryChanged [{eventId}]: Redirigé vers le thread UI");
                    _statistics.SuccessfulEvents++;
                    return HistoryChangeResult.Succeeded(eventId, "Redirigé vers le thread UI");
                }

                // Étape 4: Synchronisation des collections
                var syncResult = await _synchronizationService.SynchronizeIfNeededAsync(args.SyncContext);

                if (syncResult.RequiresFullReload)
                {
                    _loggingService.LogInfo($"HistoryChanged [{eventId}]: Rechargement complet nécessaire");
                    await args.LoadHistoryAction();
                }
                else if (syncResult.RequiresRetry)
                {
                    _loggingService.LogInfo($"HistoryChanged [{eventId}]: Nouvelle tentative de synchronisation nécessaire");
                    // La prochaine tentative sera gérée automatiquement par le prochain événement HistoryChanged
                    // ou par le mécanisme de retry du synchronizer
                }

                // Étape 5: Maintenance périodique
                bool maintenanceTriggered = await _maintenanceService.TriggerMaintenanceIfNeededAsync(eventId);
                if (maintenanceTriggered)
                {
                    _loggingService.LogInfo($"HistoryChanged [{eventId}]: Maintenance périodique déclenchée");
                }

                // Succès
                stopwatch.Stop();
                UpdateStatistics(stopwatch.ElapsedMilliseconds);
                _statistics.SuccessfulEvents++;
                
                _loggingService.LogInfo($"[FIN] HistoryChanged [{eventId}]: Traitement terminé avec succès en {stopwatch.ElapsedMilliseconds}ms");
                return HistoryChangeResult.Succeeded(eventId, $"Traitement terminé en {stopwatch.ElapsedMilliseconds}ms");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                UpdateStatistics(stopwatch.ElapsedMilliseconds);
                _statistics.FailedEvents++;
                _statistics.LastError = ex;
                _statistics.LastErrorTimestamp = DateTime.Now;

                _loggingService.LogError($"HistoryChanged [{eventId}]: Exception: {ex.Message}", ex);
                
                // Appeler le gestionnaire d'erreur fourni (reproduire la logique originale)
                try
                {
                    args.ErrorHandler(ex, eventId);
                }
                catch (Exception handlerEx)
                {
                    _loggingService.LogError($"HistoryChanged [{eventId}]: Erreur dans le gestionnaire d'erreur: {handlerEx.Message}", handlerEx);
                }

                return HistoryChangeResult.Failed(eventId, ex.Message, ex);
            }
        }

        /// <summary>
        /// Obtient les statistiques de traitement des événements.
        /// </summary>
        public HistoryChangeStatistics GetStatistics()
        {
            return _statistics;
        }

        /// <summary>
        /// Remet à zéro les statistiques de traitement.
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.TotalEvents = 0;
            _statistics.SuccessfulEvents = 0;
            _statistics.IgnoredEvents = 0;
            _statistics.FailedEvents = 0;
            _statistics.AverageProcessingTimeMs = 0;
            _statistics.LastError = null;
            _statistics.LastErrorTimestamp = null;
        }

        /// <summary>
        /// Exécute la logique de synchronisation sans récursion.
        /// Cette méthode est appelée par EnsureUIThread pour éviter les boucles infinies.
        /// </summary>
        private void ExecuteSynchronizationLogic(HistoryChangedEventArgs args, string eventId)
        {
            try
            {
                // Exécuter la synchronisation de manière synchrone sur le thread UI
                var syncResult = _synchronizationService.SynchronizeIfNeededAsync(args.SyncContext).GetAwaiter().GetResult();

                if (syncResult.RequiresFullReload)
                {
                    _loggingService.LogInfo($"HistoryChanged [{eventId}]: Rechargement complet nécessaire");
                    args.LoadHistoryAction().GetAwaiter().GetResult();
                }

                // Maintenance périodique
                bool maintenanceTriggered = _maintenanceService.TriggerMaintenanceIfNeededAsync(eventId).GetAwaiter().GetResult();
                if (maintenanceTriggered)
                {
                    _loggingService.LogInfo($"HistoryChanged [{eventId}]: Maintenance périodique déclenchée");
                }

                _loggingService.LogInfo($"[FIN] HistoryChanged [{eventId}]: Synchronisation terminée avec succès");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"ExecuteSynchronizationLogic [{eventId}]: Exception: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Génère un identifiant unique pour l'événement (reproduire la logique originale).
        /// </summary>
        private string GenerateEventId() => Guid.NewGuid().ToString("N").Substring(0, 6);

        /// <summary>
        /// Met à jour les statistiques de temps de traitement.
        /// </summary>
        private void UpdateStatistics(long processingTimeMs)
        {
            if (_statistics.TotalEvents > 0)
            {
                _statistics.AverageProcessingTimeMs = 
                    (_statistics.AverageProcessingTimeMs * (_statistics.TotalEvents - 1) + processingTimeMs) / _statistics.TotalEvents;
            }
            else
            {
                _statistics.AverageProcessingTimeMs = processingTimeMs;
            }
        }
    }
}
