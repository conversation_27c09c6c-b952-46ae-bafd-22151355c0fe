using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Interfaces
{
    /// <summary>
    /// Interface pour la notification d'événements liés à l'historique.
    /// Responsabilité unique : Notifier les changements d'historique.
    /// </summary>
    public interface IEventNotifier
    {
        /// <summary>
        /// Notifie qu'un changement a eu lieu dans l'historique.
        /// </summary>
        /// <param name="item">L'élément qui a causé le changement</param>
        /// <param name="changeType">Type de changement effectué</param>
        void NotifyHistoryChanged(ClipboardItem item, HistoryChangeType changeType);
    }

    /// <summary>
    /// Types de changements possibles dans l'historique.
    /// </summary>
    public enum HistoryChangeType
    {
        /// <summary>
        /// Un nouvel élément a été ajouté.
        /// </summary>
        ItemAdded,

        /// <summary>
        /// Un élément existant a été mis à jour.
        /// </summary>
        ItemUpdated,

        /// <summary>
        /// Un élément a été déplacé en première position.
        /// </summary>
        ItemMovedToTop,

        /// <summary>
        /// Des éléments ont été supprimés pour respecter la limite.
        /// </summary>
        ItemsRemoved
    }
}
