#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.LogDeletionResult
{
    [TestFixture]
    public class LogDeletionResultBaselineCollectorTests
    {
        private Mock<ILoggingService>? _loggingServiceMock;
        private LogDeletionResultBaselineCollector? _collector;

        [SetUp]
        public void SetUp()
        {
            _loggingServiceMock = new Mock<ILoggingService>();
            _collector = new LogDeletionResultBaselineCollector(_loggingServiceMock.Object);
        }

        [Test]
        public void RecordExecution_ShouldAddExecutionToQueue()
        {
            var execution = new LogDeletionResultExecution { ExecutionId = Guid.NewGuid() };
            _collector!.RecordExecution(execution);
            var baseline = _collector.GetBaseline();
            Assert.That(baseline.TotalExecutions, Is.EqualTo(1));
        }

        [Test]
        public void RecordExecution_ShouldIgnoreNullExecution()
        {
            _collector!.RecordExecution(null);
            var baseline = _collector.GetBaseline();
            Assert.That(baseline.TotalExecutions, Is.EqualTo(0));
            _loggingServiceMock!.Verify(l => l.LogWarning(It.IsAny<string>()), Times.Once);
        }

        [Test]
        public void GetBaseline_ShouldReturnEmptyMetricsWhenNoExecutions()
        {
            var baseline = _collector!.GetBaseline();
            Assert.That(baseline.TotalExecutions, Is.EqualTo(0));
            Assert.That(baseline.HasError, Is.False);
        }

        [Test]
        public void GetBaseline_ShouldCalculateMetricsCorrectly()
        {
            var executions = new List<LogDeletionResultExecution>
            {
                new LogDeletionResultExecution { IsSuccessful = true, ExecutionTimeMs = 10, MemoryUsedBytes = 100, Success = true, ThreadId = 1 },
                new LogDeletionResultExecution { IsSuccessful = true, ExecutionTimeMs = 20, MemoryUsedBytes = 200, Success = true, ThreadId = 2 },
                new LogDeletionResultExecution { IsSuccessful = false, ErrorMessage = "Error 1", ThreadId = 1 }
            };

            foreach (var execution in executions)
            {
                _collector!.RecordExecution(execution);
            }

            var baseline = _collector!.GetBaseline();

            Assert.That(baseline.TotalExecutions, Is.EqualTo(3));
            Assert.That(baseline.SuccessfulExecutions, Is.EqualTo(2));
            Assert.That(baseline.FailedExecutions, Is.EqualTo(1));
            Assert.That(baseline.AverageExecutionTimeMs, Is.EqualTo(15));
            Assert.That(baseline.MinExecutionTimeMs, Is.EqualTo(10));
            Assert.That(baseline.MaxExecutionTimeMs, Is.EqualTo(20));
            Assert.That(baseline.MedianExecutionTimeMs, Is.EqualTo(15));
            Assert.That(baseline.AverageMemoryUsageBytes, Is.EqualTo(150));
            Assert.That(baseline.MaxMemoryUsageBytes, Is.EqualTo(200));
            Assert.That(baseline.SuccessfulDeletions, Is.EqualTo(2));
            Assert.That(baseline.FailedDeletions, Is.EqualTo(0));
            Assert.That(baseline.UniqueThreadsUsed, Is.EqualTo(2));
            Assert.That(baseline.CommonErrors.Count, Is.EqualTo(1));
            Assert.That(baseline.CommonErrors.First().Key, Is.EqualTo("Error 1"));
        }

        [Test]
        public void ResetMetrics_ShouldClearExecutionsAndResetStartTime()
        {
            _collector!.RecordExecution(new LogDeletionResultExecution());
            var initialBaseline = _collector.GetBaseline();
            var initialStartTime = initialBaseline.CollectionStartTime;

            Thread.Sleep(10); // Ensure time passes
            _collector.ResetMetrics();

            var newBaseline = _collector.GetBaseline();
            Assert.That(newBaseline.TotalExecutions, Is.EqualTo(0));
            Assert.That(newBaseline.CollectionStartTime, Is.GreaterThan(initialStartTime));
            _loggingServiceMock!.Verify(l => l.LogInfo(It.Is<string>(s => s.Contains("réinitialisées"))), Times.Once);
        }
    }
}