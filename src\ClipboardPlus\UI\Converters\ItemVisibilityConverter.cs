using System;
using System.Globalization;
using System.Windows;

using System.Windows.Data;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.Services;

using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.UI.Converters
{
    /// <summary>
    /// Convertisseur de visibilité PROFESSIONNEL pour les éléments du presse-papiers
    ///
    /// ARCHITECTURE SOLID COMPLÈTE :
    /// - Single Responsibility : Conversion de visibilité uniquement
    /// - Open/Closed : Extensible via VisibilityType enum
    /// - Liskov Substitution : Implémente IValueConverter correctement
    /// - Interface Segregation : Utilise IVisibilityStateManager spécialisé
    /// - Dependency Inversion : Dépend d'abstractions (IVisibilityStateManager)
    ///
    /// ARCHITECTURE MVVM RESPECTÉE :
    /// - View : Ce convertisseur (logique de présentation)
    /// - ViewModel : Résolution via IVisibilityStateManager
    /// - Model : ClipboardItem (données pures)
    /// NOUVEAU : Réactif aux événements VisibilityChanged pour respecter MVVM
    /// </summary>
    public class ItemVisibilityConverter : IValueConverter, IDisposable
    {
        /// <summary>
        /// Type de visibilité à évaluer (Title ou Timestamp)
        /// </summary>
        public VisibilityType VisibilityType { get; set; }

        /// <summary>
        /// Gestionnaire de visibilité pour écouter les changements (MVVM)
        /// </summary>
        private IVisibilityStateManager? _visibilityManager;

        /// <summary>
        /// Service de logging pour les événements critiques
        /// </summary>
        private ILoggingService? _loggingService;

        /// <summary>
        /// Événement pour notifier WPF qu'une reconversion est nécessaire
        /// </summary>
        public static event EventHandler? ConversionInvalidated;

        /// <summary>
        /// Constructeur PROFESSIONNEL avec diagnostic complet
        /// Respecte les principes SOLID dès l'instanciation
        /// NOUVEAU : S'abonne aux événements VisibilityChanged pour MVVM
        /// </summary>
        public ItemVisibilityConverter()
        {
            // Résolution du service de logging pour les événements critiques
            try
            {
                if (System.Windows.Application.Current is App appInstance && appInstance.Services != null)
                {
                    _loggingService = appInstance.Services.GetService<ILoggingService>();
                }
            }
            catch (Exception ex)
            {
                // Fallback silencieux si le logging n'est pas disponible
                System.Diagnostics.Debug.WriteLine($"ItemVisibilityConverter: Impossible de résoudre ILoggingService - {ex.Message}");
            }

            // ABONNEMENT AUX ÉVÉNEMENTS MVVM (SOLID/MVVM)
            SubscribeToVisibilityEvents();


        }

        /// <summary>
        /// Convertit un élément ClipboardItem en Visibility selon le type configuré
        /// IMPLÉMENTATION PROFESSIONNELLE SOLID/MVVM
        ///
        /// Respecte tous les principes SOLID :
        /// - SRP : Conversion de visibilité uniquement
        /// - OCP : Extensible via VisibilityType
        /// - LSP : Implémentation correcte d'IValueConverter
        /// - ISP : Utilise IVisibilityStateManager spécialisé
        /// - DIP : Dépend d'abstractions (IVisibilityStateManager)
        /// </summary>
        /// <param name="value">ClipboardItem à évaluer</param>
        /// <param name="targetType">Type cible (Visibility)</param>
        /// <param name="parameter">Paramètre optionnel (non utilisé dans cette implémentation SOLID)</param>
        /// <param name="culture">Culture pour la conversion</param>
        /// <returns>Visibility.Visible ou Visibility.Collapsed selon les règles métier</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var operationId = Guid.NewGuid().ToString().Substring(0, 8);
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            var threadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
            var instanceId = this.GetHashCode();





            // VALIDATION PROFESSIONNELLE : Respecte le principe de défense en profondeur
            if (value is not ClipboardItem item)
            {
                _loggingService?.LogWarning($"ItemVisibilityConverter: Valeur invalide reçue - Type attendu: ClipboardItem, Reçu: {value?.GetType().Name ?? "null"}");
                return Visibility.Collapsed;
            }


            // RÉSOLUTION PROFESSIONNELLE SIMPLIFIÉE : Utilisation directe de IVisibilityStateManager
            // Respecte le Dependency Inversion Principle et évite la duplication
            IVisibilityStateManager? visibilityManager = null;

            // MÉTHODE PROFESSIONNELLE : Résolution via services DI (Dependency Injection)
            try
            {
                if (System.Windows.Application.Current is App appInstance && appInstance.Services != null)
                {
                    // Résolution directe du VisibilityStateManager (architecture SOLID pure)
                    visibilityManager = appInstance.Services.GetService<IVisibilityStateManager>();
                    if (visibilityManager == null)
                    {
                        _loggingService?.LogWarning("ItemVisibilityConverter: IVisibilityStateManager non trouvé dans le conteneur DI");
                    }
                }
                else
                {
                    _loggingService?.LogWarning("ItemVisibilityConverter: Services DI non disponibles");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("ItemVisibilityConverter: Exception lors de la résolution DI", ex);
            }

            // RÉSOLUTION FINALE PROFESSIONNELLE SIMPLIFIÉE
            if (visibilityManager == null)
            {
                _loggingService?.LogInfo($"ItemVisibilityConverter: Utilisation du fallback professionnel pour {VisibilityType}");
                return GetProfessionalFallbackVisibility(item, operationId);
            }


            // APPLICATION DE LA LOGIQUE MÉTIER PROFESSIONNELLE
            // Respecte l'Open/Closed Principle : extensible via VisibilityType
            // Utilise directement IVisibilityStateManager (architecture SOLID pure)
            bool shouldShow = VisibilityType switch
            {
                VisibilityType.Title => visibilityManager.ShouldShowTitle(item),
                VisibilityType.Timestamp => visibilityManager.ShouldShowTimestamp(item),
                _ => false // Défaut sécurisé
            };

            var result = shouldShow ? Visibility.Visible : Visibility.Collapsed;


            return result;
        }

        /// <summary>
        /// Conversion inverse non supportée
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("ItemVisibilityConverter ne supporte pas la conversion inverse");
        }

        /// <summary>
        /// Logique de fallback PROFESSIONNELLE si aucun résolveur n'est disponible
        /// Utilise les services directement avec architecture SOLID
        /// Respecte le principe de défense en profondeur
        /// </summary>
        /// <param name="item">Élément à évaluer</param>
        /// <param name="operationId">ID de l'opération pour le diagnostic</param>
        /// <returns>Visibilité basée sur les services système</returns>
        private Visibility GetProfessionalFallbackVisibility(ClipboardItem item, string operationId)
        {

            try
            {
                // FALLBACK PROFESSIONNEL : Utilisation directe des services système
                if (System.Windows.Application.Current is App appInstance && appInstance.Services != null)
                {

                    var settingsManager = appInstance.Services.GetService<ISettingsManager>();
                    if (settingsManager != null)
                    {
                        var result = VisibilityType switch
                        {
                            VisibilityType.Title => !settingsManager.HideItemTitle && !string.IsNullOrWhiteSpace(item.CustomName)
                                ? Visibility.Visible
                                : Visibility.Collapsed,
                            VisibilityType.Timestamp => !settingsManager.HideTimestamp
                                ? Visibility.Visible
                                : Visibility.Collapsed,
                            _ => Visibility.Collapsed
                        };

                        return result;
                    }
                    else
                    {
                        _loggingService?.LogError("ItemVisibilityConverter: ISettingsManager non trouvé dans le fallback");
                    }
                }
                else
                {
                    _loggingService?.LogError("ItemVisibilityConverter: Services DI non disponibles dans le fallback");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("ItemVisibilityConverter: Exception dans le fallback", ex);
            }

            // FALLBACK ULTIME SÉCURISÉ : Masquer par défaut (principe de sécurité)
            _loggingService?.LogWarning("ItemVisibilityConverter: Utilisation du fallback ultime - masquage par défaut");
            return Visibility.Collapsed;
        }

        /// <summary>
        /// S'abonne aux événements de visibilité (MVVM)
        /// Respecte le Dependency Inversion Principle
        /// </summary>
        private void SubscribeToVisibilityEvents()
        {
            try
            {
                if (System.Windows.Application.Current is App appInstance && appInstance.Services != null)
                {
                    _visibilityManager = appInstance.Services.GetService<IVisibilityStateManager>();
                    if (_visibilityManager != null)
                    {
                        _visibilityManager.VisibilityChanged += OnVisibilityChanged;
                        _loggingService?.LogDebug("ItemVisibilityConverter: Abonné aux événements VisibilityChanged");
                    }
                    else
                    {
                        _loggingService?.LogWarning("ItemVisibilityConverter: IVisibilityStateManager non trouvé pour l'abonnement aux événements");
                    }
                }
                else
                {
                    _loggingService?.LogWarning("ItemVisibilityConverter: Services DI non disponibles pour l'abonnement aux événements");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("ItemVisibilityConverter: Exception lors de l'abonnement aux événements", ex);
            }
        }

        /// <summary>
        /// Gestionnaire d'événements pour les changements de visibilité (MVVM)
        /// </summary>
        private void OnVisibilityChanged(object? sender, VisibilityChangedEventArgs e)
        {
            // Ne réagir qu'aux changements concernant notre type de visibilité
            if (e.Type == VisibilityType)
            {
                _loggingService?.LogDebug($"ItemVisibilityConverter: Événement de visibilité reçu - Type: {e.Type}, Visible: {e.IsVisible}");

                // Déclencher l'événement statique pour notifier WPF
                ConversionInvalidated?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// Dispose pattern pour se désabonner des événements
        /// </summary>
        public void Dispose()
        {
            if (_visibilityManager != null)
            {
                _visibilityManager.VisibilityChanged -= OnVisibilityChanged;
                _loggingService?.LogDebug("ItemVisibilityConverter: Désabonné des événements VisibilityChanged");
            }
        }


    }
}
