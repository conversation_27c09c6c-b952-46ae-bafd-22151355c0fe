using System;
using System.Windows;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Implémentation de la validation du thread UI.
    /// Responsabilité unique : vérifier si l'exécution se fait dans le thread UI approprié.
    /// </summary>
    public class ThreadValidator : IThreadValidator
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de ThreadValidator.
        /// </summary>
        /// <param name="loggingService">Service de logging pour enregistrer les validations.</param>
        public ThreadValidator(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <inheritdoc />
        public bool IsInUIThread()
        {
            try
            {
                // Vérifier si nous sommes dans un thread UI via le Dispatcher WPF
                bool isInUiThread = System.Windows.Application.Current?.Dispatcher?.CheckAccess() ?? false;
                
                _loggingService.LogInfo($"ThreadValidator: Vérification du thread UI - Résultat: {isInUiThread}");
                
                return isInUiThread;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"ThreadValidator: Erreur lors de la vérification du thread UI: {ex.Message}", ex);
                // En cas d'erreur, considérer que nous ne sommes pas dans le thread UI par sécurité
                return false;
            }
        }

        /// <inheritdoc />
        public void ValidateUIThread()
        {
            try
            {
                bool isInUiThread = IsInUIThread();
                
                _loggingService.LogInfo($"ThreadValidator: Exécution dans le thread UI: {isInUiThread}");
                
                if (!isInUiThread)
                {
                    _loggingService.LogWarning("ThreadValidator: ATTENTION - L'exécution n'est PAS dans le thread UI!");
                    _loggingService.LogWarning("ThreadValidator: Cela peut causer des problèmes avec les composants UI comme NotifyIcon.");
                }
                else
                {
                    _loggingService.LogInfo("ThreadValidator: Validation réussie - Exécution dans le thread UI approprié.");
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"ThreadValidator: Erreur lors de la validation du thread UI: {ex.Message}", ex);
                throw; // Remonter l'exception car c'est une validation critique
            }
        }
    }
}
