using System;
using System.Threading.Tasks;
using System.Windows.Threading;
using ClipboardPlus.Core.Services.Shortcuts.Implementations;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services.Shortcuts.Implementations
{
    [TestFixture]
    public class DispatcherProviderTests
    {
        private DispatcherProvider _dispatcherProvider = null!;

        [SetUp]
        public void SetUp()
        {
            _dispatcherProvider = new DispatcherProvider();
        }

        [Test]
        public void Constructor_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new DispatcherProvider());
        }

        [Test]
        public void IsDispatcherAvailable_InTestEnvironment_ReturnsFalse()
        {
            // Act
            var result = _dispatcherProvider.IsDispatcherAvailable();

            // Assert
            Assert.That(result, Is.False, "Dans l'environnement de test, le Dispatcher WPF n'est généralement pas disponible");
        }

        [Test]
        public void CheckAccess_InTestEnvironment_HandlesGracefully()
        {
            // Act & Assert
            // Dans l'environnement de test, CheckAccess peut retourner true ou false selon l'implémentation
            Assert.DoesNotThrow(() => _dispatcherProvider.CheckAccess());
        }

        [Test]
        public void InvokeAsync_WithAction_InTestEnvironment_HandlesGracefully()
        {
            // Arrange
            Action testAction = () => { /* Test action */ };

            // Act & Assert
            // Dans l'environnement de test, cela devrait soit exécuter directement soit gérer l'absence de Dispatcher
            Assert.DoesNotThrowAsync(async () => await _dispatcherProvider.InvokeAsync(testAction));
        }

        [Test]
        public void InvokeAsync_WithFunc_InTestEnvironment_HandlesGracefully()
        {
            // Arrange
            Func<string> testFunc = () => "test result";

            // Act & Assert
            // Dans l'environnement de test, cela devrait soit exécuter directement soit gérer l'absence de Dispatcher
            Assert.DoesNotThrowAsync(async () => await _dispatcherProvider.InvokeAsync(testFunc));
        }

        [Test]
        public void InvokeAsync_WithNullAction_ThrowsNullReferenceException()
        {
            // Act & Assert
            // L'implémentation actuelle lance NullReferenceException, pas ArgumentNullException
            Assert.ThrowsAsync<NullReferenceException>(
                async () => await _dispatcherProvider.InvokeAsync((Action)null!));
        }

        [Test]
        public void InvokeAsync_WithNullFunc_ThrowsNullReferenceException()
        {
            // Act & Assert
            // L'implémentation actuelle lance NullReferenceException, pas ArgumentNullException
            Assert.ThrowsAsync<NullReferenceException>(
                async () => await _dispatcherProvider.InvokeAsync((Func<string>)null!));
        }

        [Test]
        public async Task InvokeAsync_WithException_PropagatesException()
        {
            // Arrange
            var expectedException = new InvalidOperationException("Test exception");
            Action throwingAction = () => throw expectedException;

            // Act & Assert
            var actualException = Assert.ThrowsAsync<InvalidOperationException>(
                async () => await _dispatcherProvider.InvokeAsync(throwingAction));

            Assert.That(actualException!.Message, Is.EqualTo("Test exception"));
        }

        [Test]
        public async Task InvokeAsync_WithFuncException_PropagatesException()
        {
            // Arrange
            var expectedException = new InvalidOperationException("Test func exception");
            Func<string> throwingFunc = () => throw expectedException;

            // Act & Assert
            var actualException = Assert.ThrowsAsync<InvalidOperationException>(
                async () => await _dispatcherProvider.InvokeAsync(throwingFunc));

            Assert.That(actualException!.Message, Is.EqualTo("Test func exception"));
        }

        [Test]
        public async Task InvokeAsync_WithFuncReturningValue_ReturnsCorrectValue()
        {
            // Arrange
            const string expectedValue = "expected result";
            Func<string> testFunc = () => expectedValue;

            // Act
            var result = await _dispatcherProvider.InvokeAsync(testFunc);

            // Assert
            Assert.That(result, Is.EqualTo(expectedValue));
        }

        [Test]
        public async Task InvokeAsync_WithComplexFunc_ReturnsCorrectValue()
        {
            // Arrange
            Func<int> complexFunc = () => 
            {
                var sum = 0;
                for (int i = 1; i <= 5; i++)
                {
                    sum += i;
                }
                return sum;
            };

            // Act
            var result = await _dispatcherProvider.InvokeAsync(complexFunc);

            // Assert
            Assert.That(result, Is.EqualTo(15)); // 1+2+3+4+5 = 15
        }
    }
}
