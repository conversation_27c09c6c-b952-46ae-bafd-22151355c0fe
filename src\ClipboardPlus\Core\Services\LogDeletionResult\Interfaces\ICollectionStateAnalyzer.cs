using System.Collections.Generic;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Interfaces
{
    /// <summary>
    /// Interface pour l'analyse de l'état des collections de l'historique du presse-papiers.
    /// Fournit des informations détaillées sur l'état des collections pour le diagnostic.
    /// </summary>
    public interface ICollectionStateAnalyzer
    {
        /// <summary>
        /// Analyse l'état actuel des collections du ViewModel.
        /// </summary>
        /// <param name="viewModel">ViewModel à analyser</param>
        /// <returns>Informations détaillées sur l'état des collections</returns>
        CollectionStateInfo AnalyzeCollectionState(ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Compare deux collections d'éléments pour détecter les différences.
        /// </summary>
        /// <param name="collection1">Première collection</param>
        /// <param name="collection2">Deuxième collection</param>
        /// <returns>Résultat de la comparaison</returns>
        CollectionComparisonResult CompareCollections(
            IEnumerable<ClipboardItem> collection1, 
            IEnumerable<ClipboardItem> collection2);

        /// <summary>
        /// Recherche un élément dans les collections du ViewModel.
        /// </summary>
        /// <param name="itemId">ID de l'élément à rechercher</param>
        /// <param name="viewModel">ViewModel dans lequel rechercher</param>
        /// <returns>Résultat de la recherche</returns>
        ItemSearchResult FindItemInCollections(long itemId, ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Analyse la cohérence entre le ViewModel et le gestionnaire d'historique.
        /// </summary>
        /// <param name="viewModel">ViewModel à analyser</param>
        /// <returns>Résultat de l'analyse de cohérence</returns>
        ConsistencyAnalysisResult AnalyzeConsistency(ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Obtient un échantillon des premiers éléments de la collection.
        /// </summary>
        /// <param name="viewModel">ViewModel source</param>
        /// <param name="count">Nombre d'éléments à récupérer (défaut: 10)</param>
        /// <returns>Liste des premiers éléments</returns>
        List<ClipboardItem> GetCollectionSample(ClipboardHistoryViewModel viewModel, int count = 10);

        /// <summary>
        /// Calcule des statistiques sur la collection.
        /// </summary>
        /// <param name="viewModel">ViewModel à analyser</param>
        /// <returns>Statistiques de la collection</returns>
        CollectionStatistics CalculateStatistics(ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Détecte les anomalies dans la collection (éléments null, doublons, etc.).
        /// </summary>
        /// <param name="viewModel">ViewModel à analyser</param>
        /// <returns>Liste des anomalies détectées</returns>
        List<CollectionAnomaly> DetectAnomalies(ClipboardHistoryViewModel viewModel);
    }
}
