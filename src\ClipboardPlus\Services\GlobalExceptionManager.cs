using System;
using System.Threading.Tasks;
using System.Windows.Threading;
using ClipboardPlus.Core.Services;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Gestionnaire global des exceptions non gérées de l'application.
    /// Centralise la logique de journalisation et de notification des exceptions.
    /// </summary>
    public class GlobalExceptionManager : IGlobalExceptionManager
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly Dispatcher _dispatcher;
        private bool _isInitialized;

        /// <summary>
        /// Initialise une nouvelle instance de la classe <see cref="GlobalExceptionManager"/>.
        /// </summary>
        /// <param name="serviceProvider">Le fournisseur de services pour résoudre les dépendances.</param>
        /// <param name="dispatcher">Le dispatcher de l'UI thread.</param>
        public GlobalExceptionManager(IServiceProvider serviceProvider, Dispatcher dispatcher)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _dispatcher = dispatcher ?? throw new ArgumentNullException(nameof(dispatcher));
            _isInitialized = false;
        }

        /// <summary>
        /// Initialise les gestionnaires d'exceptions globaux.
        /// </summary>
        public void Initialize()
        {
            if (_isInitialized)
                return;

            // Configurer la gestion des exceptions non gérées
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            _dispatcher.UnhandledException += Dispatcher_UnhandledException;
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

            _isInitialized = true;
            
            var loggingService = _serviceProvider.GetService<ILoggingService>();
            loggingService?.LogInfo("GlobalExceptionManager: Gestionnaires d'exceptions globaux initialisés");
        }

        /// <summary>
        /// Gère les exceptions non gérées du domaine d'application.
        /// </summary>
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            LogUnhandledException("AppDomain", exception, e.IsTerminating);
        }

        /// <summary>
        /// Gère les exceptions non gérées du thread UI.
        /// </summary>
        private void Dispatcher_UnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            LogUnhandledException("UI", e.Exception, false);
            e.Handled = true; // Empêcher le plantage de l'application
        }

        /// <summary>
        /// Gère les exceptions non observées des tâches asynchrones.
        /// </summary>
        /// <remarks>Correction de l'avertissement CS8622 en rendant le paramètre sender explicitement nullable</remarks>
        private void TaskScheduler_UnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            LogUnhandledException("Task", e.Exception, false);
            e.SetObserved(); // Marquer l'exception comme observée pour éviter le plantage
        }

        /// <summary>
        /// Journalise une exception non gérée avec des informations contextuelles.
        /// </summary>
        /// <param name="source">La source de l'exception (UI, AppDomain, Task, etc.)</param>
        /// <param name="exception">L'exception qui s'est produite</param>
        /// <param name="isTerminating">Indique si l'exception entraîne la terminaison de l'application</param>
        /// <remarks>Correction de l'avertissement CS8604 en rendant le paramètre exception non-nullable</remarks>
        public void LogUnhandledException(string source, Exception? exception, bool isTerminating)
        {
            if (exception == null)
            {
                // Journaliser une erreur sur l'absence d'exception mais continuer le traitement
                try
                {
                    var loggingService = _serviceProvider.GetService<ILoggingService>();
                    loggingService?.LogWarning($"LogUnhandledException appelé avec une exception null depuis {source}");
                }
                catch
                {
                    // Ignorer les erreurs lors de la journalisation
                }
                return;
            }

            // Formater le message d'erreur
            var errorMessage = $"Exception non gérée ({source})" + (isTerminating ? " [TERMINANT]" : "");
            
            // Message détaillé pour le journal
            var detailedErrorMessage = $"{errorMessage}\n" +
                $"Type: {exception.GetType().FullName}\n" +
                $"Message: {exception.Message}\n" +
                $"StackTrace: {exception.StackTrace}";

            try
            {
                // Obtenir le service de journalisation
                var loggingService = _serviceProvider.GetService<ILoggingService>();
                var userNotificationService = _serviceProvider.GetService<IUserNotificationService>();
                    
                // Journaliser l'erreur
                loggingService?.LogCritical(detailedErrorMessage, exception);

                // Afficher un message à l'utilisateur si l'application est encore ouverte
                if (!isTerminating && userNotificationService != null && loggingService != null)
                {
                    _dispatcher.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            userNotificationService.ShowError(
                                "Erreur",
                                $"Une erreur s'est produite dans l'application.\n\n" +
                                $"Type d'erreur: {exception.GetType().Name}\n" +
                                $"Message: {exception.Message}\n\n" +
                                $"Détails enregistrés dans le journal: {loggingService.GetLogFilePath()}");
                        }
                        catch
                        {
                            // Ignorer les erreurs lors de l'affichage de la boîte de dialogue
                        }
                    }));
                }
            }
            catch (Exception logEx)
            {
                // En cas d'échec de la journalisation, essayer d'écrire dans le journal d'événements Windows
                try
                {
                    // Dernier recours si le service de journalisation échoue
                    System.Diagnostics.EventLog.WriteEntry(
                        "Application",
                        $"ERREUR CRITIQUE: {errorMessage}\n{exception}\n\nErreur de journalisation: {logEx.Message}",
                        System.Diagnostics.EventLogEntryType.Error);
                }
                catch
                {
                    // Vraiment rien de plus que nous puissions faire ici
                }
            }
        }
    }
}
