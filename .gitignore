# Ignorer les dossiers de build
bin/
obj/

# Ignorer les fichiers temporaires de Visual Studio
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates
*.userprefs
*.usertasks

# NuGet Packages (sauf si jamais vous voulez les inclure)
packages/
*.nupkg
# Le fichier packages.config est inclu
!packages/repositories.config

# Résultats des tests
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*
*.TestResults.xml
TestResults/
coverage.cobertura.xml
coverage-report/

# Base de données SQLite et fichiers journaux
*.db
*.db-journal
*.sqlite
*.sqlite-journal

# Fichiers de build
msbuild.log
msbuild.err
msbuild.wrn

# Fichiers d'OS
.DS_Store
Thumbs.db
[Dd]esktop.ini

# Visual Studio code
.vscode/
*.code-workspace

# ReSharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# Dossier de résultats de la build
[Dd]ebug/
[Rr]elease/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Dossier spécifique au projet
releases/ 