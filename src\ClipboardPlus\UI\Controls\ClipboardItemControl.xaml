<UserControl x:Class="ClipboardPlus.UI.Controls.ClipboardItemControl"
             x:Name="RootUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:ClipboardPlus.UI.Converters"
             xmlns:local="clr-namespace:ClipboardPlus.UI.Controls"

             xmlns:datamodels="clr-namespace:ClipboardPlus.Core.DataModels"
             xmlns:sys="clr-namespace:System;assembly=mscorlib"
             xmlns:app="clr-namespace:System.Windows;assembly=PresentationFramework"
             xmlns:helpers="clr-namespace:ClipboardPlus.UI.Helpers"
             mc:Ignorable="d"
             d:DesignHeight="80"
             d:DesignWidth="300">
    <UserControl.Resources>
        <Style x:Key="ContentPreviewStyle" TargetType="FrameworkElement">
            <Setter Property="Visibility" Value="Collapsed"/>
        </Style>
        <!-- === SYSTÈME SOLID UNIQUE === -->
        <!-- CONVERTISSEURS SUPPRIMÉS : Utilisation directe des propriétés IsTitleVisible/IsTimestampVisible (MVVM pur) -->

        <converters:BooleanAndToVisibilityConverter x:Key="BooleanAndToVisibilityConverter"/>
    </UserControl.Resources>
    
    <Border Background="White"
            CornerRadius="0"
            BorderThickness="1"
            BorderBrush="#DDDDDD"
            Padding="8">
        
        <Border.ContextMenu>
            <ContextMenu DataContext="{Binding PlacementTarget.DataContext, RelativeSource={RelativeSource Self}}">
                <MenuItem Header="Renommer"
                          Command="{Binding DataContext.DemarrerRenommageCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          CommandParameter="{Binding}"
                          ToolTip="Renommer cet élément"/>
                <MenuItem Header="Épingler/Désépingler"
                          Command="{Binding DataContext.BasculerEpinglageCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          CommandParameter="{Binding}"
                          ToolTip="Épingler ou désépingler cet élément"/>
                <MenuItem Header="Prévisualiser"
                          Command="{Binding DataContext.AfficherPreviewCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          CommandParameter="{Binding}"
                          ToolTip="Prévisualiser le contenu"/>
                <Separator/>
                <MenuItem Header="Supprimer"
                          Foreground="Red"
                          Command="{Binding DataContext.SupprimerElementCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          CommandParameter="{Binding}"
                          ToolTip="Supprimer cet élément"/>
                <MenuItem Header="Supprimer tout"
                          Foreground="DarkRed"
                          Command="{Binding DataContext.SupprimerToutCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          ToolTip="Supprimer tous les éléments (sauf les épinglés)"/>
                <MenuItem Header="Suppression avancé..."
                          Command="{Binding DataContext.OpenAdvancedCleanupCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          ToolTip="Ouvrir les options de suppression avancé"/>
            </ContextMenu>
        </Border.ContextMenu>
        
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- Contenu de l'élément (Maintenant en Grid.Column="0") -->
            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                <!-- Mode édition - TextBox visible uniquement pendant le renommage -->
                <TextBox x:Name="EditNameTextBox"
                         Text="{Binding CustomName, Mode=OneWay}"
                         Visibility="Collapsed"
                         Margin="0,0,0,2"
                         FontWeight="SemiBold"
                         Padding="2"
                         KeyDown="EditNameTextBox_KeyDown"
                         LostFocus="EditNameTextBox_LostFocus"/>
                
                <!-- Mode affichage - TextBlock pour le titre === SYSTÈME SOLID MVVM PUR === -->
                <TextBlock x:Name="DisplayNameTextBlock"
                           Text="{Binding CustomName}"
                           FontWeight="SemiBold"
                           TextTrimming="CharacterEllipsis"
                           Margin="0,0,0,0"
                           Tag="Titre"
                           Visibility="{Binding IsTitleVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                
                <!-- Aperçu du contenu TEXTUEL -->
                <TextBlock Text="{Binding TextPreview}"
                           TextWrapping="Wrap"
                           TextTrimming="CharacterEllipsis"
                           Opacity="0.7"
                           MaxHeight="40"
                           Margin="0,0,0,1">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource ContentPreviewStyle}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding DataType}" Value="{x:Static datamodels:ClipboardDataType.Text}">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding DataType}" Value="{x:Static datamodels:ClipboardDataType.Html}">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding DataType}" Value="{x:Static datamodels:ClipboardDataType.Rtf}">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                                 <DataTrigger Binding="{Binding DataType}" Value="{x:Static datamodels:ClipboardDataType.FilePath}">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding DataType}" Value="{x:Static datamodels:ClipboardDataType.Other}">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>

                <!-- Aperçu de l'IMAGE -->
                <Image Source="{Binding ThumbnailSource}"
                       Stretch="Uniform"
                       MaxHeight="40"
                       Margin="0,0,0,1">
                    <Image.Style>
                        <Style TargetType="Image" BasedOn="{StaticResource ContentPreviewStyle}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding DataType}" Value="{x:Static datamodels:ClipboardDataType.Image}">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Image.Style>
                </Image>
                
                <!-- Date de création formatée -->
                <!-- === SYSTÈME SOLID MVVM PUR === -->
                <TextBlock Text="{Binding Timestamp, StringFormat='{}{0:dd/MM/yyyy HH:mm}'}"
                           FontSize="10"
                           Opacity="0.6"
                           HorizontalAlignment="Right"
                           Margin="0,0,0,0"
                           Visibility="{Binding IsTimestampVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>
            
            <!-- Indicateur d'épinglage simple en Grid.Column="0" -->
            <TextBlock Grid.Column="0"
                       Text="&#xE840;"
                       FontFamily="Segoe MDL2 Assets"
                       FontSize="14"
                       Foreground="#666666"
                       VerticalAlignment="Top"
                       HorizontalAlignment="Right"
                       Margin="0,2,2,0"
                       ToolTip="Élément épinglé">
                <TextBlock.Visibility>
                    <MultiBinding Converter="{StaticResource BooleanAndToVisibilityConverter}">
                        <Binding Path="IsPinned"/>
                        <Binding Path="IsTitleVisible"/>
                    </MultiBinding>
                </TextBlock.Visibility>
            </TextBlock>
        </Grid>
    </Border>
</UserControl> 