using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Interop;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Shortcuts.Interfaces;
using ClipboardPlus.Core.Services.Shortcuts.Models;
using ClipboardPlus.Core.Services.Shortcuts.Strategies;

namespace ClipboardPlus.Core.Services.Shortcuts.Implementations
{
    /// <summary>
    /// Implémentation principale de IShortcutInitializer.
    /// Orchestre l'initialisation des raccourcis globaux en utilisant différentes stratégies.
    /// </summary>
    public class ShortcutInitializer : IShortcutInitializer
    {
        private readonly IList<IHandleProvisionStrategy> _strategies;
        private readonly IWindowsHotkeyApi _hotkeyApi;
        private readonly ILoggingService _loggingService;
        
        private HwndSource? _source;
        private IntPtr _windowHandle = IntPtr.Zero;
        private KeyCombination? _currentShortcut;
        private IHandleProvisionStrategy? _activeStrategy;

        public ShortcutInitializer(
            IEnumerable<IHandleProvisionStrategy> strategies,
            IWindowsHotkeyApi hotkeyApi,
            ILoggingService loggingService)
        {
            _strategies = strategies?.ToList() ?? throw new ArgumentNullException(nameof(strategies));
            _hotkeyApi = hotkeyApi ?? throw new ArgumentNullException(nameof(hotkeyApi));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));

            if (!_strategies.Any())
            {
                throw new ArgumentException("Au moins une stratégie doit être fournie.", nameof(strategies));
            }
        }

        /// <inheritdoc />
        public event EventHandler<ShortcutActivatedEventArgs>? ShortcutActivated;

        /// <inheritdoc />
        public async Task<InitializationResult> InitializeAsync(InitializationContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }

            _loggingService.LogInfo($"Début de l'initialisation des raccourcis avec {context.DefaultShortcut}");

            try
            {
                // Étape 1 : Obtenir un handle de fenêtre via les stratégies
                var handleResult = await TryGetWindowHandleAsync(context);
                if (!handleResult.success)
                {
                    return InitializationResult.Failure(
                        "Impossible d'obtenir un handle de fenêtre valide", 
                        handleResult.exception);
                }

                _windowHandle = handleResult.handle;
                _activeStrategy = handleResult.strategy;

                // Étape 2 : Créer le HwndSource pour les hooks
                var sourceResult = TryCreateHwndSource();
                if (!sourceResult.success)
                {
                    return InitializationResult.Failure(
                        "Impossible de créer le HwndSource", 
                        sourceResult.exception);
                }

                // Étape 3 : Enregistrer le raccourci
                var registrationResult = TryRegisterShortcut(context.DefaultShortcut);
                if (!registrationResult.success)
                {
                    return InitializationResult.Failure(
                        "Impossible d'enregistrer le raccourci", 
                        registrationResult.exception);
                }

                _currentShortcut = context.DefaultShortcut;

                _loggingService.LogInfo($"Initialisation réussie avec la stratégie {_activeStrategy!.StrategyName}");
                
                return InitializationResult.Success(
                    context.DefaultShortcut, 
                    _windowHandle, 
                    _activeStrategy.StrategyName);
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Erreur lors de l'initialisation des raccourcis", ex);
                return InitializationResult.Failure("Erreur inattendue lors de l'initialisation", ex);
            }
        }

        /// <summary>
        /// Tente d'obtenir un handle de fenêtre en utilisant les stratégies disponibles.
        /// </summary>
        private async Task<(bool success, IntPtr handle, IHandleProvisionStrategy? strategy, Exception? exception)>
            TryGetWindowHandleAsync(InitializationContext context)
        {
            foreach (var strategy in _strategies)
            {
                try
                {
                    _loggingService.LogInfo($"Tentative avec la stratégie {strategy.StrategyName}");

                    if (!strategy.CanProvideHandle())
                    {
                        _loggingService.LogInfo($"Stratégie {strategy.StrategyName} non applicable");
                        continue;
                    }

                    var handle = await strategy.ProvideHandleAsync(context);
                    if (handle != IntPtr.Zero)
                    {
                        _loggingService.LogInfo($"Handle obtenu avec la stratégie {strategy.StrategyName}");
                        return (true, handle, strategy, null);
                    }
                }
                catch (Exception ex)
                {
                    _loggingService.LogWarning($"Échec de la stratégie {strategy.StrategyName}: {ex.Message}");
                    continue;
                }
            }

            return (false, IntPtr.Zero, null, new InvalidOperationException("Aucune stratégie n'a pu fournir un handle"));
        }

        /// <summary>
        /// Tente de créer le HwndSource pour les hooks Windows.
        /// </summary>
        private (bool success, Exception? exception) TryCreateHwndSource()
        {
            try
            {
                if (_windowHandle == IntPtr.Zero)
                {
                    return (false, new InvalidOperationException("Handle de fenêtre invalide"));
                }

                _source = HwndSource.FromHwnd(_windowHandle);
                if (_source == null)
                {
                    return (false, new InvalidOperationException("Impossible de créer HwndSource"));
                }

                _source.AddHook(WndProc);
                return (true, null);
            }
            catch (Exception ex)
            {
                return (false, ex);
            }
        }

        /// <summary>
        /// Tente d'enregistrer le raccourci global.
        /// </summary>
        private (bool success, Exception? exception) TryRegisterShortcut(KeyCombination shortcut)
        {
            try
            {
                // Utiliser la même logique que TryRegisterShortcut de GlobalShortcutService
                var success = _hotkeyApi.RegisterHotKey(
                    _windowHandle,
                    1, // ID du hotkey
                    (uint)shortcut.Modifiers,
                    (uint)shortcut.Key);

                if (!success)
                {
                    return (false, new InvalidOperationException("Échec de l'enregistrement du raccourci"));
                }

                return (true, null);
            }
            catch (Exception ex)
            {
                return (false, ex);
            }
        }

        /// <summary>
        /// Procédure de fenêtre pour traiter les messages Windows.
        /// </summary>
        private IntPtr WndProc(IntPtr hwnd, int msg, IntPtr wParam, IntPtr lParam, ref bool handled)
        {
            // Même logique que dans GlobalShortcutService
            const int WM_HOTKEY = 0x0312;

            if (msg == WM_HOTKEY && _currentShortcut != null)
            {
                ShortcutActivated?.Invoke(this, new ShortcutActivatedEventArgs(_currentShortcut));
                handled = true;
            }

            return IntPtr.Zero;
        }

        /// <summary>
        /// Nettoie les ressources utilisées.
        /// </summary>
        public void Dispose()
        {
            try
            {
                // Désenregistrer le raccourci
                if (_windowHandle != IntPtr.Zero)
                {
                    _hotkeyApi.UnregisterHotKey(_windowHandle, 1);
                }

                // Nettoyer le HwndSource
                _source?.RemoveHook(WndProc);
                _source?.Dispose();

                // Nettoyer la stratégie active
                _activeStrategy?.Cleanup();
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"Erreur lors du nettoyage: {ex.Message}");
            }
            finally
            {
                _source = null;
                _windowHandle = IntPtr.Zero;
                _currentShortcut = null;
                _activeStrategy = null;
            }
        }
    }
}
