using System;
using System.IO;
using NUnit.Framework;
using ClipboardPlus.Services;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Services
{
    /// <summary>
    /// Tests unitaires pour FileSystemService.
    /// Valide l'abstraction des opérations de système de fichiers.
    /// </summary>
    [TestFixture]
    public class FileSystemServiceTests
    {
        private FileSystemService _service;
        private string _tempDirectory;
        private string _tempFile;

        [SetUp]
        public void Setup()
        {
            _service = new FileSystemService();
            
            // Créer un répertoire temporaire pour les tests
            _tempDirectory = Path.Combine(Path.GetTempPath(), "FileSystemServiceTests_" + Guid.NewGuid().ToString("N")[..8]);
            Directory.CreateDirectory(_tempDirectory);
            
            // Créer un fichier temporaire pour les tests
            _tempFile = Path.Combine(_tempDirectory, "test.txt");
            File.WriteAllText(_tempFile, "Test content");
        }

        [TearDown]
        public void TearDown()
        {
            // Nettoyer les fichiers temporaires
            if (Directory.Exists(_tempDirectory))
            {
                Directory.Delete(_tempDirectory, true);
            }
        }

        #region FileExists Tests

        [Test]
        public void FileExists_WithExistingFile_ReturnsTrue()
        {
            // Act
            bool result = _service.FileExists(_tempFile);

            // Assert
            Assert.IsTrue(result, "FileExists doit retourner true pour un fichier existant");
        }

        [Test]
        public void FileExists_WithNonExistentFile_ReturnsFalse()
        {
            // Arrange
            string nonExistentFile = Path.Combine(_tempDirectory, "nonexistent.txt");

            // Act
            bool result = _service.FileExists(nonExistentFile);

            // Assert
            Assert.IsFalse(result, "FileExists doit retourner false pour un fichier inexistant");
        }

        [Test]
        public void FileExists_WithNullPath_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _service.FileExists(null!));
        }

        [Test]
        public void FileExists_WithEmptyPath_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _service.FileExists(""));
            Assert.Throws<ArgumentException>(() => _service.FileExists("   "));
        }

        #endregion

        #region DirectoryExists Tests

        [Test]
        public void DirectoryExists_WithExistingDirectory_ReturnsTrue()
        {
            // Act
            bool result = _service.DirectoryExists(_tempDirectory);

            // Assert
            Assert.IsTrue(result, "DirectoryExists doit retourner true pour un répertoire existant");
        }

        [Test]
        public void DirectoryExists_WithNonExistentDirectory_ReturnsFalse()
        {
            // Arrange
            string nonExistentDir = Path.Combine(_tempDirectory, "nonexistent");

            // Act
            bool result = _service.DirectoryExists(nonExistentDir);

            // Assert
            Assert.IsFalse(result, "DirectoryExists doit retourner false pour un répertoire inexistant");
        }

        [Test]
        public void DirectoryExists_WithNullPath_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _service.DirectoryExists(null!));
        }

        #endregion

        #region GetFileInfo Tests

        [Test]
        public void GetFileInfo_WithExistingFile_ReturnsFileInfo()
        {
            // Act
            FileInfo? result = _service.GetFileInfo(_tempFile);

            // Assert
            Assert.IsNotNull(result, "GetFileInfo doit retourner FileInfo pour un fichier existant");
            Assert.AreEqual(_tempFile, result!.FullName);
            Assert.IsTrue(result.Length > 0, "Le fichier doit avoir une taille > 0");
        }

        [Test]
        public void GetFileInfo_WithNonExistentFile_ReturnsNull()
        {
            // Arrange
            string nonExistentFile = Path.Combine(_tempDirectory, "nonexistent.txt");

            // Act
            FileInfo? result = _service.GetFileInfo(nonExistentFile);

            // Assert
            Assert.IsNull(result, "GetFileInfo doit retourner null pour un fichier inexistant");
        }

        [Test]
        public void GetFileInfo_WithNullPath_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _service.GetFileInfo(null!));
        }

        #endregion

        #region GetFileSize Tests

        [Test]
        public void GetFileSize_WithExistingFile_ReturnsSize()
        {
            // Act
            long? result = _service.GetFileSize(_tempFile);

            // Assert
            Assert.IsNotNull(result, "GetFileSize doit retourner une taille pour un fichier existant");
            Assert.IsTrue(result > 0, "La taille doit être > 0");
        }

        [Test]
        public void GetFileSize_WithNonExistentFile_ReturnsNull()
        {
            // Arrange
            string nonExistentFile = Path.Combine(_tempDirectory, "nonexistent.txt");

            // Act
            long? result = _service.GetFileSize(nonExistentFile);

            // Assert
            Assert.IsNull(result, "GetFileSize doit retourner null pour un fichier inexistant");
        }

        #endregion

        #region GetLastWriteTime Tests

        [Test]
        public void GetLastWriteTime_WithExistingFile_ReturnsDateTime()
        {
            // Act
            DateTime? result = _service.GetLastWriteTime(_tempFile);

            // Assert
            Assert.IsNotNull(result, "GetLastWriteTime doit retourner une date pour un fichier existant");
            Assert.IsTrue(result <= DateTime.Now, "La date de modification doit être <= maintenant");
        }

        [Test]
        public void GetLastWriteTime_WithNonExistentFile_ReturnsNull()
        {
            // Arrange
            string nonExistentFile = Path.Combine(_tempDirectory, "nonexistent.txt");

            // Act
            DateTime? result = _service.GetLastWriteTime(nonExistentFile);

            // Assert
            Assert.IsNull(result, "GetLastWriteTime doit retourner null pour un fichier inexistant");
        }

        #endregion

        #region IsValidPath Tests

        [Test]
        public void IsValidPath_WithValidPath_ReturnsTrue()
        {
            // Arrange
            string validPath = @"C:\Valid\Path\File.txt";

            // Act
            bool result = _service.IsValidPath(validPath);

            // Assert
            Assert.IsTrue(result, "IsValidPath doit retourner true pour un chemin valide");
        }

        [Test]
        public void IsValidPath_WithInvalidCharacters_ReturnsFalse()
        {
            // Arrange
            string invalidPath = @"C:\Invalid<>Path\File.txt";

            // Act
            bool result = _service.IsValidPath(invalidPath);

            // Assert
            Assert.IsFalse(result, "IsValidPath doit retourner false pour un chemin avec caractères invalides");
        }

        [Test]
        public void IsValidPath_WithNullOrEmpty_ReturnsFalse()
        {
            // Act & Assert
            Assert.IsFalse(_service.IsValidPath(null), "IsValidPath doit retourner false pour null");
            Assert.IsFalse(_service.IsValidPath(""), "IsValidPath doit retourner false pour chaîne vide");
            Assert.IsFalse(_service.IsValidPath("   "), "IsValidPath doit retourner false pour espaces");
        }

        [Test]
        public void IsValidPath_WithTooLongPath_ReturnsFalse()
        {
            // Arrange
            string tooLongPath = @"C:\" + new string('a', 300) + @"\file.txt";

            // Act
            bool result = _service.IsValidPath(tooLongPath);

            // Assert
            Assert.IsFalse(result, "IsValidPath doit retourner false pour un chemin trop long");
        }

        #endregion
    }
}
