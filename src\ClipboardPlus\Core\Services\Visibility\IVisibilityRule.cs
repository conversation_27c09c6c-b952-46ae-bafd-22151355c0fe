namespace ClipboardPlus.Core.Services.Visibility
{
    /// <summary>
    /// Contrat pour les règles de visibilité extensibles
    /// Respecte le principe Open/Closed : ouvert à l'extension, fermé à la modification
    /// Utilise le Strategy Pattern pour permettre différentes implémentations
    /// </summary>
    /// <typeparam name="T">Type d'élément pour lequel la visibilité est évaluée</typeparam>
    public interface IVisibilityRule<in T>
    {
        /// <summary>
        /// Détermine si un élément doit être visible selon cette règle
        /// </summary>
        /// <param name="item">Élément à évaluer</param>
        /// <param name="context">Contexte global de visibilité</param>
        /// <returns>True si l'élément doit être visible, False sinon</returns>
        bool ShouldBeVisible(T item, VisibilityContext context);
    }
}
