using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests d'intégration simples pour la fonctionnalité de nettoyage par date.
    /// </summary>
    [TestFixture]
    public class ClipboardHistoryManagerDateCleanupIntegrationTests
    {
        [Test]
        public void ClearItemsOlderThanAsync_BasicFunctionality_ShouldWork()
        {
            // Arrange - Créer des éléments de test avec des dates spécifiques
            var items = new List<ClipboardItem>
            {
                new ClipboardItem
                {
                    Id = 1,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Ancien élément",
                    Timestamp = DateTime.Now.AddDays(-5), // Ancien
                    IsPinned = false,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Ancien élément")
                },
                new ClipboardItem
                {
                    Id = 2,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Élément récent",
                    Timestamp = DateTime.Now.AddHours(-1), // Récent
                    IsPinned = false,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Élément récent")
                },
                new ClipboardItem
                {
                    Id = 3,
                    DataType = ClipboardDataType.Text,
                    TextPreview = "Ancien épinglé",
                    Timestamp = DateTime.Now.AddDays(-10), // Très ancien mais épinglé
                    IsPinned = true,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Ancien épinglé")
                }
            };

            // Vérifier la logique de filtrage directement
            var cutoffDate = DateTime.Now - TimeSpan.FromDays(2);
            var itemsToRemove = items
                .Where(i => !i.IsPinned && i.Timestamp < cutoffDate)
                .ToList();

            // Assert
            Assert.AreEqual(1, itemsToRemove.Count, "Devrait identifier exactement 1 élément à supprimer");
            Assert.AreEqual(1, itemsToRemove[0].Id, "L'élément ID=1 devrait être identifié pour suppression");
            
            // Vérifier que les éléments préservés sont corrects
            var preservedItems = items.Except(itemsToRemove).ToList();
            Assert.AreEqual(2, preservedItems.Count, "Devrait préserver 2 éléments");
            Assert.IsTrue(preservedItems.Any(i => i.Id == 2), "L'élément récent ID=2 devrait être préservé");
            Assert.IsTrue(preservedItems.Any(i => i.Id == 3), "L'élément épinglé ID=3 devrait être préservé");
        }

        [Test]
        public void TimeSpanCalculation_ShouldWorkCorrectly()
        {
            // Test des calculs de TimeSpan pour s'assurer que notre logique est correcte
            var now = DateTime.Now;
            var twoDaysAgo = now.AddDays(-2);
            var oneDayAgo = now.AddDays(-1);
            var oneHourAgo = now.AddHours(-1);

            var cutoffDate = now - TimeSpan.FromDays(1.5);

            // Vérifications
            Assert.IsTrue(twoDaysAgo < cutoffDate, "2 jours en arrière devrait être plus ancien que la coupure de 1.5 jour");
            Assert.IsFalse(oneDayAgo < cutoffDate, "1 jour en arrière devrait être plus récent que la coupure de 1.5 jour");
            Assert.IsFalse(oneHourAgo < cutoffDate, "1 heure en arrière devrait être plus récent que la coupure de 1.5 jour");
        }

        [Test]
        public void FilterLogic_WithVariousTimeSpans_ShouldWorkCorrectly()
        {
            // Arrange - Créer des éléments avec des timestamps précis
            var baseTime = new DateTime(2024, 1, 15, 12, 0, 0); // Date fixe pour la reproductibilité
            var items = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, Timestamp = baseTime.AddDays(-10), IsPinned = false }, // Très ancien
                new ClipboardItem { Id = 2, Timestamp = baseTime.AddDays(-3), IsPinned = false },  // Ancien
                new ClipboardItem { Id = 3, Timestamp = baseTime.AddDays(-1), IsPinned = false },  // Récent
                new ClipboardItem { Id = 4, Timestamp = baseTime.AddHours(-6), IsPinned = false }, // Très récent
                new ClipboardItem { Id = 5, Timestamp = baseTime.AddDays(-5), IsPinned = true }    // Ancien mais épinglé
            };

            // Test avec différentes périodes
            var testCases = new[]
            {
                new { Days = 7.0, ExpectedRemoved = new[] { 1 }, Description = "7 jours - devrait supprimer les très anciens (sauf épinglés)" },
                new { Days = 2.0, ExpectedRemoved = new[] { 1, 2 }, Description = "2 jours - devrait supprimer les anciens (sauf épinglés)" },
                new { Days = 0.5, ExpectedRemoved = new[] { 1, 2, 3 }, Description = "12 heures - devrait supprimer tout sauf le très récent (et épinglés)" }
            };

            foreach (var testCase in testCases)
            {
                var cutoffDate = baseTime - TimeSpan.FromDays(testCase.Days);
                var itemsToRemove = items
                    .Where(i => !i.IsPinned && i.Timestamp < cutoffDate)
                    .Select(i => i.Id)
                    .ToArray();

                Assert.AreEqual(testCase.ExpectedRemoved.Length, itemsToRemove.Length, 
                    $"{testCase.Description} - Nombre d'éléments à supprimer incorrect");
                
                foreach (var expectedId in testCase.ExpectedRemoved)
                {
                    Assert.Contains(expectedId, itemsToRemove, 
                        $"{testCase.Description} - L'élément ID={expectedId} devrait être supprimé");
                }
            }
        }
    }
}
