using System;
using System.Collections.Generic;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Models
{
    /// <summary>
    /// Informations détaillées sur l'état des collections.
    /// </summary>
    public class CollectionStateInfo
    {
        /// <summary>
        /// Identifiant unique de l'analyse.
        /// </summary>
        public Guid AnalysisId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Nombre d'éléments dans la collection du ViewModel.
        /// </summary>
        public int ViewModelItemCount { get; set; }

        /// <summary>
        /// Nombre d'éléments dans le gestionnaire d'historique.
        /// </summary>
        public int ManagerItemCount { get; set; }

        /// <summary>
        /// Indique si les collections sont synchronisées.
        /// </summary>
        public bool CollectionsInSync { get; set; }

        /// <summary>
        /// Échantillon des premiers éléments de la collection.
        /// </summary>
        public List<ClipboardItem> FirstTenItems { get; set; } = new();

        /// <summary>
        /// Nombre d'éléments null détectés.
        /// </summary>
        public int NullItemCount { get; set; }

        /// <summary>
        /// Nombre de doublons détectés.
        /// </summary>
        public int DuplicateCount { get; set; }

        /// <summary>
        /// Statistiques de la collection.
        /// </summary>
        public CollectionStatistics Statistics { get; set; } = new();

        /// <summary>
        /// Anomalies détectées.
        /// </summary>
        public List<CollectionAnomaly> Anomalies { get; set; } = new();

        /// <summary>
        /// Timestamp de l'analyse.
        /// </summary>
        public DateTime AnalysisTimestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Durée de l'analyse.
        /// </summary>
        public TimeSpan AnalysisDuration { get; set; }

        /// <summary>
        /// Indique si l'analyse a réussi.
        /// </summary>
        public bool AnalysisSuccessful { get; set; } = true;

        /// <summary>
        /// Message d'erreur si l'analyse a échoué.
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Résultat de comparaison entre deux collections.
    /// </summary>
    public class CollectionComparisonResult
    {
        /// <summary>
        /// Identifiant unique de la comparaison.
        /// </summary>
        public Guid ComparisonId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Indique si les collections sont identiques.
        /// </summary>
        public bool AreIdentical { get; set; }

        /// <summary>
        /// Nombre d'éléments dans la première collection.
        /// </summary>
        public int Collection1Count { get; set; }

        /// <summary>
        /// Nombre d'éléments dans la deuxième collection.
        /// </summary>
        public int Collection2Count { get; set; }

        /// <summary>
        /// Éléments présents uniquement dans la première collection.
        /// </summary>
        public List<ClipboardItem> OnlyInCollection1 { get; set; } = new();

        /// <summary>
        /// Éléments présents uniquement dans la deuxième collection.
        /// </summary>
        public List<ClipboardItem> OnlyInCollection2 { get; set; } = new();

        /// <summary>
        /// Éléments communs aux deux collections.
        /// </summary>
        public List<ClipboardItem> CommonItems { get; set; } = new();

        /// <summary>
        /// Différences détectées.
        /// </summary>
        public List<CollectionDifference> Differences { get; set; } = new();

        /// <summary>
        /// Timestamp de la comparaison.
        /// </summary>
        public DateTime ComparisonTimestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Durée de la comparaison.
        /// </summary>
        public TimeSpan ComparisonDuration { get; set; }
    }

    /// <summary>
    /// Résultat de recherche d'un élément dans les collections.
    /// </summary>
    public class ItemSearchResult
    {
        /// <summary>
        /// ID de l'élément recherché.
        /// </summary>
        public long SearchedItemId { get; set; }

        /// <summary>
        /// Indique si l'élément a été trouvé.
        /// </summary>
        public bool Found { get; set; }

        /// <summary>
        /// Élément trouvé dans la collection du ViewModel.
        /// </summary>
        public ClipboardItem? FoundInViewModel { get; set; }

        /// <summary>
        /// Élément trouvé dans le gestionnaire d'historique.
        /// </summary>
        public ClipboardItem? FoundInManager { get; set; }

        /// <summary>
        /// Indique si l'élément a été trouvé par référence.
        /// </summary>
        public bool FoundByReference { get; set; }

        /// <summary>
        /// Indique si l'élément a été trouvé par ID.
        /// </summary>
        public bool FoundById { get; set; }

        /// <summary>
        /// Indique si les instances trouvées sont identiques.
        /// </summary>
        public bool InstancesMatch { get; set; }

        /// <summary>
        /// Positions de l'élément dans les collections.
        /// </summary>
        public List<ItemPosition> Positions { get; set; } = new();

        /// <summary>
        /// Timestamp de la recherche.
        /// </summary>
        public DateTime SearchTimestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Résultat d'analyse de cohérence.
    /// </summary>
    public class ConsistencyAnalysisResult
    {
        /// <summary>
        /// Identifiant unique de l'analyse.
        /// </summary>
        public Guid AnalysisId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Indique si la cohérence est maintenue.
        /// </summary>
        public bool IsConsistent { get; set; }

        /// <summary>
        /// Score de cohérence (0-100).
        /// </summary>
        public double ConsistencyScore { get; set; }

        /// <summary>
        /// Problèmes de cohérence détectés.
        /// </summary>
        public List<ConsistencyIssue> Issues { get; set; } = new();

        /// <summary>
        /// Recommandations pour améliorer la cohérence.
        /// </summary>
        public List<string> Recommendations { get; set; } = new();

        /// <summary>
        /// Timestamp de l'analyse.
        /// </summary>
        public DateTime AnalysisTimestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Statistiques d'une collection.
    /// </summary>
    public class CollectionStatistics
    {
        /// <summary>
        /// Nombre total d'éléments.
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// Nombre d'éléments épinglés.
        /// </summary>
        public int PinnedItems { get; set; }

        /// <summary>
        /// Répartition par type de données.
        /// </summary>
        public Dictionary<ClipboardDataType, int> TypeDistribution { get; set; } = new();

        /// <summary>
        /// Taille moyenne des éléments (en octets).
        /// </summary>
        public double AverageItemSize { get; set; }

        /// <summary>
        /// Taille totale de la collection (en octets).
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// Élément le plus ancien.
        /// </summary>
        public DateTime? OldestItemTimestamp { get; set; }

        /// <summary>
        /// Élément le plus récent.
        /// </summary>
        public DateTime? NewestItemTimestamp { get; set; }

        /// <summary>
        /// Nombre d'éléments avec nom personnalisé.
        /// </summary>
        public int ItemsWithCustomName { get; set; }

        /// <summary>
        /// Nombre d'éléments sans aperçu.
        /// </summary>
        public int ItemsWithoutPreview { get; set; }
    }

    /// <summary>
    /// Différence entre deux collections.
    /// </summary>
    public class CollectionDifference
    {
        /// <summary>
        /// Type de différence.
        /// </summary>
        public DifferenceType Type { get; set; }

        /// <summary>
        /// Description de la différence.
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Élément concerné.
        /// </summary>
        public ClipboardItem? AffectedItem { get; set; }

        /// <summary>
        /// Position dans la première collection.
        /// </summary>
        public int? PositionInCollection1 { get; set; }

        /// <summary>
        /// Position dans la deuxième collection.
        /// </summary>
        public int? PositionInCollection2 { get; set; }
    }

    /// <summary>
    /// Position d'un élément dans une collection.
    /// </summary>
    public class ItemPosition
    {
        /// <summary>
        /// Nom de la collection.
        /// </summary>
        public string CollectionName { get; set; } = string.Empty;

        /// <summary>
        /// Index dans la collection.
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// Indique si la position est valide.
        /// </summary>
        public bool IsValid { get; set; }
    }

    /// <summary>
    /// Types de différences entre collections.
    /// </summary>
    public enum DifferenceType
    {
        ItemAdded,
        ItemRemoved,
        ItemModified,
        PositionChanged,
        PropertyChanged
    }
}
