using System;
using System.Threading.Tasks;
using System.Windows;
using ClipboardPlus.Core.Services.Shortcuts.Interfaces;
using ClipboardPlus.Core.Services.Shortcuts.Models;

namespace ClipboardPlus.Core.Services.Shortcuts.Strategies
{
    /// <summary>
    /// Stratégie qui crée une fenêtre fantôme pour l'enregistrement de raccourcis.
    /// Utilisée quand la fenêtre principale n'est pas disponible.
    /// </summary>
    public class HiddenWindowStrategy : IHandleProvisionStrategy
    {
        private readonly IDispatcherProvider _dispatcherProvider;
        private readonly IWindowFactory _windowFactory;
        private Window? _hiddenWindow;

        public HiddenWindowStrategy(
            IDispatcherProvider dispatcherProvider,
            IWindowFactory windowFactory)
        {
            _dispatcherProvider = dispatcherProvider ?? throw new ArgumentNullException(nameof(dispatcherProvider));
            _windowFactory = windowFactory ?? throw new ArgumentNullException(nameof(windowFactory));
        }

        /// <inheritdoc />
        public string StrategyName => "HiddenWindow";

        /// <inheritdoc />
        public bool CanProvideHandle()
        {
            // Cette stratégie peut toujours être utilisée comme fallback
            return _dispatcherProvider.IsDispatcherAvailable();
        }

        /// <inheritdoc />
        public async Task<IntPtr> ProvideHandleAsync(InitializationContext context)
        {
            if (!CanProvideHandle())
            {
                return IntPtr.Zero;
            }

            try
            {
                return await _dispatcherProvider.InvokeAsync(() =>
                {
                    // Créer la fenêtre fantôme
                    _hiddenWindow = _windowFactory.CreateHiddenWindow();
                    
                    // L'afficher puis la cacher pour obtenir un handle valide
                    _windowFactory.ShowAndHideWindow(_hiddenWindow);
                    
                    // Obtenir le handle
                    return _windowFactory.GetWindowHandle(_hiddenWindow);
                });
            }
            catch (Exception)
            {
                // En cas d'erreur, nettoyer et retourner IntPtr.Zero
                Cleanup();
                return IntPtr.Zero;
            }
        }

        /// <inheritdoc />
        public void Cleanup()
        {
            if (_hiddenWindow != null)
            {
                try
                {
                    if (_dispatcherProvider.CheckAccess())
                    {
                        _hiddenWindow.Close();
                    }
                    else
                    {
                        _dispatcherProvider.InvokeAsync(() => _hiddenWindow.Close());
                    }
                }
                catch
                {
                    // Ignorer les erreurs de nettoyage
                }
                finally
                {
                    _hiddenWindow = null;
                }
            }
        }
    }
}
