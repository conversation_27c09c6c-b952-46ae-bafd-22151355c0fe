using System;
using System.Windows;
using System.Diagnostics;
using ClipboardPlus.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using WpfApplication = System.Windows.Application;
using WpfMessageBox = System.Windows.MessageBox;
using WpfMessageBoxButton = System.Windows.MessageBoxButton;
using WpfMessageBoxImage = System.Windows.MessageBoxImage;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.UI.Helpers
{
    /// <summary>
    /// Classe utilitaire pour afficher des messages d'erreur avec journalisation intégrée.
    /// </summary>
    public static class ErrorMessageHelper
    {
        /// <summary>
        /// Affiche un message d'erreur à l'utilisateur avec journalisation.
        /// </summary>
        /// <param name="message">Le message d'erreur à afficher.</param>
        /// <param name="title">Le titre de la boîte de dialogue d'erreur.</param>
        /// <param name="exception">L'exception associée (facultatif).</param>
        /// <param name="context">Le contexte dans lequel l'erreur s'est produite.</param>
        /// <param name="viewModel">Le ViewModel associé pour gérer l'état des opérations (facultatif).</param>
        public static void ShowError(string message, string title, Exception? exception = null, string context = "", object? viewModel = null)
        {
            var loggingService = GetLoggingService();
            
            try
            {
                // Journaliser l'erreur
                loggingService?.LogError($"[ErrorMessageHelper] {context}: {message}", exception);
                Debug.WriteLine($"[ErrorMessageHelper] {context}: {message}");
                if (exception != null)
                {
                    Debug.WriteLine($"[ErrorMessageHelper] Exception: {exception.GetType().Name}: {exception.Message}");
                    Debug.WriteLine($"[ErrorMessageHelper] StackTrace: {exception.StackTrace}");
                }
                
                // Vérifier si nous sommes dans un contexte UI valide
                bool validContext = WpfApplication.Current != null && WpfApplication.Current.Dispatcher != null;
                if (!validContext)
                {
                    loggingService?.LogError("[ErrorMessageHelper] Impossible d'afficher le message d'erreur: contexte UI invalide");
                    Debug.WriteLine("[ErrorMessageHelper] Impossible d'afficher le message d'erreur: contexte UI invalide");
                    return;
                }
                
                // Construire le message complet
                string fullMessage = message;
                if (exception != null)
                {
                    fullMessage = $"{message}\n\nDétails: {exception.Message}";
                    
                    // Ajouter des informations supplémentaires pour les développeurs en mode debug
                    #if DEBUG
                    fullMessage += $"\n\nType: {exception.GetType().Name}";
                    fullMessage += $"\n\nStackTrace: {exception.StackTrace}";
                    #endif
                }
                
                // Afficher le message sur le thread UI
                ShowErrorOnUIThread(fullMessage, title, loggingService, viewModel);
            }
            catch (Exception ex)
            {
                // Si même l'affichage de l'erreur échoue, au moins nous avons journalisé
                loggingService?.LogError($"[ErrorMessageHelper] Erreur lors de l'affichage du message d'erreur: {ex.Message}", ex);
                Debug.WriteLine($"[ErrorMessageHelper] Erreur lors de l'affichage du message d'erreur: {ex.Message}");
                Debug.WriteLine($"[ErrorMessageHelper] StackTrace: {ex.StackTrace}");
                
                // Réinitialiser l'état du ViewModel si nécessaire
                ResetViewModelState(viewModel);
            }
        }
        
        /// <summary>
        /// Affiche un message d'erreur sur le thread UI.
        /// </summary>
        private static void ShowErrorOnUIThread(string message, string title, ILoggingService? loggingService, object? viewModel)
        {
            try
            {
                // S'assurer que nous sommes sur le thread UI
                if (WpfApplication.Current.Dispatcher.CheckAccess())
                {
                    WpfMessageBox.Show(message, title, WpfMessageBoxButton.OK, WpfMessageBoxImage.Error);
                    loggingService?.LogInfo("[ErrorMessageHelper] Message d'erreur affiché avec succès");
                }
                else
                {
                    loggingService?.LogInfo("[ErrorMessageHelper] Redirection vers le thread UI via Dispatcher.Invoke");
                    WpfApplication.Current.Dispatcher.Invoke(() => 
                    {
                        WpfMessageBox.Show(message, title, WpfMessageBoxButton.OK, WpfMessageBoxImage.Error);
                        loggingService?.LogInfo("[ErrorMessageHelper] Message d'erreur affiché avec succès via Dispatcher");
                    });
                }
            }
            catch (Exception ex)
            {
                loggingService?.LogError($"[ErrorMessageHelper] Erreur lors de l'affichage sur le thread UI: {ex.Message}", ex);
                Debug.WriteLine($"[ErrorMessageHelper] Erreur lors de l'affichage sur le thread UI: {ex.Message}");
                
                // Réinitialiser l'état du ViewModel si nécessaire
                ResetViewModelState(viewModel);
            }
        }
        
        /// <summary>
        /// Réinitialise l'état du ViewModel si nécessaire.
        /// </summary>
        private static void ResetViewModelState(object? viewModel)
        {
            if (viewModel is ClipboardHistoryViewModel clipboardViewModel)
            {
                try
                {
                    // Utiliser une méthode d'extension ou une méthode publique du ViewModel
                    // au lieu d'accéder directement à la propriété privée
                    if (WpfApplication.Current?.Dispatcher != null)
                    {
                        if (WpfApplication.Current.Dispatcher.CheckAccess())
                        {
                            // Utiliser OnPropertyChanged pour notifier le changement
                            // au lieu d'accéder directement à la propriété
                            clipboardViewModel.ResetOperationState();
                        }
                        else
                        {
                            WpfApplication.Current.Dispatcher.BeginInvoke(new Action(() =>
                            {
                                clipboardViewModel.ResetOperationState();
                            }));
                        }
                    }
                }
                catch (Exception)
                {
                    // Ignorer les erreurs ici pour éviter les boucles infinies
                }
            }
        }
        
        /// <summary>
        /// Récupère le service de journalisation de manière sécurisée.
        /// </summary>
        private static ILoggingService? GetLoggingService()
        {
            try
            {
                if (WpfApplication.Current is App app && app.Services != null)
                {
                    return app.Services.GetService<ILoggingService>();
                }
            }
            catch (Exception ex)
            {
                // Éviter une récursion infinie si le service de journalisation cause des problèmes
                Debug.WriteLine($"[ErrorMessageHelper] Impossible d'obtenir le service de journalisation: {ex.Message}");
            }
            
            return null;
        }
    }
} 