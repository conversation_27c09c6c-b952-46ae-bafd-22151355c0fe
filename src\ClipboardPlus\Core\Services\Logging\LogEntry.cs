using System;

namespace ClipboardPlus.Core.Services.Logging
{
    /// <summary>
    /// Représente une entrée de log structurée avec toutes les informations nécessaires.
    /// Utilise un record pour l'immutabilité et la performance.
    /// </summary>
    public record LogEntry(
        string Level,
        string Message,
        DateTime Timestamp,
        string ThreadId,
        string Context,
        string CallerInfo)
    {
        /// <summary>
        /// Formate l'entrée de log selon le format standard de WriteToLog.
        /// </summary>
        /// <returns>Les lignes formatées prêtes pour l'écriture</returns>
        public string[] ToFormattedLines()
        {
            var timestampStr = Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var header = $"[{timestampStr}] [{Level}] [{Context}] [Thread:{ThreadId}] [{CallerInfo}]";
            
            // Gérer les messages multi-lignes avec indentation
            var messageLines = Message.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None);
            
            if (messageLines.Length == 1)
            {
                // Message simple sur une ligne
                return new[] { $"{header} {Message}" };
            }
            else
            {
                // Message multi-lignes avec indentation
                var result = new string[messageLines.Length];
                result[0] = $"{header} {messageLines[0]}";
                
                for (int i = 1; i < messageLines.Length; i++)
                {
                    result[i] = $"    {messageLines[i]}";
                }
                
                return result;
            }
        }
        
        /// <summary>
        /// Détermine si ce niveau de log nécessite un flush immédiat.
        /// </summary>
        public bool RequiresImmediateFlush => Level switch
        {
            "AVERTISSEMENT" => true,
            "ERREUR" => true,
            "CRITIQUE" => true,
            "SUPPRESSION" => true,
            _ => false
        };
        
        /// <summary>
        /// Détermine si ce niveau doit être affiché en console même si la console est désactivée.
        /// </summary>
        public bool ForceConsoleOutput => Level switch
        {
            "ERREUR" => true,
            "CRITIQUE" => true,
            "SUPPRESSION" => true,
            _ => false
        };
        
        /// <summary>
        /// Obtient la couleur console appropriée pour ce niveau de log.
        /// </summary>
        public ConsoleColor GetConsoleColor() => Level switch
        {
            "DEBUG" => ConsoleColor.Gray,
            "INFO" => ConsoleColor.White,
            "AVERTISSEMENT" => ConsoleColor.Yellow,
            "ERREUR" => ConsoleColor.Red,
            "CRITIQUE" => ConsoleColor.DarkRed,
            "SUPPRESSION" => ConsoleColor.Magenta,
            _ => ConsoleColor.White
        };
    }
}
