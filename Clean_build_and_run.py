import subprocess
import sys
import os
import time
import argparse
from pathlib import Path

# --- Configuration des chemins (de manière robuste) ---
# Le 'project_root' est le répertoire où se trouve ce script.
# Tous les autres chemins sont construits à partir de cette base.
project_root = Path(__file__).parent.resolve()
log_dir = project_root / "logs"
project_path = project_root / "src" / "ClipboardPlus" / "ClipboardPlus.csproj"

# Vérifier si le chemin du projet existe pour éviter les erreurs cryptiques
if not project_path.is_file():
    print(f"❌ Erreur : Fichier projet introuvable à l'emplacement '{project_path}'")
    sys.exit(1)

# --- Analyse des arguments de ligne de commande ---
parser = argparse.ArgumentParser(description="ClipboardPlus - Script de build et run optimisé")
parser.add_argument("--fast", action="store_true", help="Mode rapide : utilise la build incrémentale de dotnet (pas de 'clean')")
parser.add_argument("--measure", action="store_true", help="Mesurer le temps de démarrage de l'application")
parser.add_argument("--no-logs-clean", action="store_true", help="Ne pas nettoyer les logs au démarrage")
args = parser.parse_args()


# --- Fonctions utilitaires ---

def run_command(command, measure_time=False, capture_output=False):
    """Exécute une commande, mesure le temps et gère les erreurs."""
    print(f"\n▶️ Exécution : {' '.join(map(str, command))}")
    start_time = time.time()
    
    result = subprocess.run(command, capture_output=capture_output, text=True)
    
    duration = time.time() - start_time
    if measure_time:
        print(f"⏱️ Temps d'exécution : {duration:.2f} secondes")

    if result.returncode != 0:
        print(f"❌ Échec de la commande : {' '.join(map(str, command))}")
        if capture_output:
            print("--- STDOUT ---")
            print(result.stdout)
            print("--- STDERR ---")
            print(result.stderr)
        sys.exit(result.returncode)
    
    return result

def kill_existing_processes():
    """Tue tous les processus ClipboardPlus.exe existants (Windows uniquement)."""
    if os.name != 'nt':
        print("ℹ️ La fermeture des processus n'est supportée que sur Windows.")
        return
        
    print("🔄 Recherche et fermeture des processus ClipboardPlus.exe existants...")
    # On ne vérifie pas le code de retour car la commande échoue si aucun processus n'est trouvé, ce qui est normal.
    subprocess.run(["taskkill", "/F", "/IM", "ClipboardPlus.exe"], capture_output=True, check=False)
    print("✅ Processus existants fermés (s'il y en avait).")

def measure_startup():
    """Lance l'application et mesure son temps de démarrage."""
    print("\n📊 Mesure du temps de démarrage...")
    
    # Utiliser --no-build car la build a déjà été faite juste avant
    command = ["dotnet", "run", "--project", str(project_path), "--no-build"]
    
    start_time = time.time()
    # Lancer le processus en arrière-plan
    process = subprocess.Popen(command)

    app_started = False
    timeout = 30  # secondes
    
    # Sonde pour vérifier si le processus est apparu
    for _ in range(timeout * 2): # sonde toutes les 0.5s
        time.sleep(0.5)
        # tasklist est une commande Windows
        if os.name == 'nt':
            result = subprocess.run(["tasklist", "/FI", "IMAGENAME eq ClipboardPlus.exe"],
                                    capture_output=True, text=True)
            if "ClipboardPlus.exe" in result.stdout:
                duration = time.time() - start_time
                print(f"✅ Application démarrée en {duration:.2f} secondes.")
                app_started = True
                break
    
    if not app_started:
        print(f"❌ Timeout ({timeout}s) : L'application n'a pas pu démarrer.")
        process.terminate() # Tuer le processus 'dotnet run'
    else:
        # Attendre un peu pour que la fenêtre s'initialise visuellement et tuer le processus
        time.sleep(2)
        kill_existing_processes()


# --- Logique principale du script ---

# 1. Nettoyage des logs (sauf si --no-logs-clean)
if not args.no_logs_clean:
    if log_dir.exists():
        for f in log_dir.glob('*'):
            if f.is_file():
                f.unlink()
        print("🧹 Fichiers dans le dossier 'logs' supprimés.")
    else:
        print("ℹ️ Dossier 'logs' introuvable, création...")
        log_dir.mkdir()
else:
    print("ℹ️ Nettoyage des logs ignoré (--no-logs-clean).")

# 2. Tuer les processus existants avant de faire quoi que ce soit
kill_existing_processes()

# 3. Logique de build et run
if args.fast:
    print("\n🚀 Mode rapide activé (build incrémentale)")
    # 'dotnet build' est déjà incrémental et rapide s'il n'y a rien à faire.
    run_command(["dotnet", "build", project_path], measure_time=True)
else:
    print("\n🔨 Mode complet (clean + build)")
    run_command(["dotnet", "clean", project_path], measure_time=True)
    run_command(["dotnet", "build", project_path], measure_time=True)

# 4. Lancer l'application ou mesurer son démarrage
if args.measure:
    measure_startup()
else:
    # Lancer l'application normalement, sans re-builder
    run_command(["dotnet", "run", "--project", str(project_path), "--no-build"])

print("\n✅ Script terminé.")