using System;
using System.Text;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Services.ContentHandlers
{
    /// <summary>
    /// Classe de base abstraite pour les handlers de contenu.
    /// Fournit la logique commune et les validations de base.
    /// Respecte le principe DRY (Don't Repeat Yourself).
    /// </summary>
    public abstract class BaseContentHandler : IContentHandler
    {
        protected readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de BaseContentHandler.
        /// </summary>
        /// <param name="loggingService">Service de logging optionnel</param>
        protected BaseContentHandler(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <inheritdoc />
        public abstract ClipboardDataType SupportedDataType { get; }

        /// <inheritdoc />
        public virtual bool CanHandle(ClipboardItem item)
        {
            if (item == null)
            {
                return false;
            }

            bool canHandle = item.DataType == SupportedDataType;
            
            _loggingService?.LogInfo($"{GetType().Name}.CanHandle - Peut traiter l'élément ID {item.Id} (type {item.DataType}): {canHandle}");
            
            return canHandle;
        }

        /// <inheritdoc />
        public object HandleContent(ClipboardItem item)
        {
            ArgumentNullException.ThrowIfNull(item);

            if (!CanHandle(item))
            {
                throw new ArgumentException($"Ce handler ne peut pas traiter le type {item.DataType}. Type supporté: {SupportedDataType}", nameof(item));
            }

            try
            {
                _loggingService?.LogInfo($"{GetType().Name}.HandleContent - Début du traitement de l'élément ID {item.Id}");

                var result = HandleContentInternal(item);

                _loggingService?.LogInfo($"{GetType().Name}.HandleContent - Traitement terminé avec succès pour l'élément ID {item.Id}");

                return result;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"{GetType().Name}.HandleContent - Erreur lors du traitement de l'élément ID {item.Id}: {ex.Message}", ex);
                
                // En cas d'erreur, retourner le TextPreview ou le message par défaut
                return GetFallbackContent(item, ex);
            }
        }

        /// <inheritdoc />
        public abstract string GetDefaultUnavailableMessage();

        /// <summary>
        /// Traite le contenu de l'élément. Méthode à implémenter par les classes dérivées.
        /// </summary>
        /// <param name="item">L'élément à traiter</param>
        /// <returns>Le contenu formaté</returns>
        protected abstract object HandleContentInternal(ClipboardItem item);

        /// <summary>
        /// Obtient le contenu de secours en cas d'erreur.
        /// </summary>
        /// <param name="item">L'élément en cours de traitement</param>
        /// <param name="exception">L'exception qui s'est produite</param>
        /// <returns>Le contenu de secours</returns>
        protected virtual object GetFallbackContent(ClipboardItem item, Exception exception)
        {
            // Utiliser TextPreview si disponible, sinon le message par défaut
            string fallback = item.TextPreview ?? GetDefaultUnavailableMessage();
            
            _loggingService?.LogInfo($"{GetType().Name}.GetFallbackContent - Utilisation du contenu de secours: '{fallback}'");
            
            return fallback;
        }

        /// <summary>
        /// Décode les données brutes en texte UTF-8.
        /// Méthode utilitaire commune à plusieurs handlers.
        /// </summary>
        /// <param name="rawData">Les données brutes à décoder</param>
        /// <returns>Le texte décodé, ou null si les données sont null/vides</returns>
        protected string? DecodeRawDataAsText(byte[]? rawData)
        {
            if (rawData == null || rawData.Length == 0)
            {
                _loggingService?.LogWarning($"{GetType().Name}.DecodeRawDataAsText - Données brutes null ou vides");
                return null;
            }

            try
            {
                string text = Encoding.UTF8.GetString(rawData);
                
                _loggingService?.LogInfo($"{GetType().Name}.DecodeRawDataAsText - Texte décodé avec succès, longueur: {text.Length} caractères");
                
                return text;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"{GetType().Name}.DecodeRawDataAsText - Erreur lors du décodage: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// Valide qu'un élément a des données utilisables (RawData ou TextPreview).
        /// </summary>
        /// <param name="item">L'élément à valider</param>
        /// <returns>True si l'élément a des données utilisables</returns>
        protected bool HasUsableData(ClipboardItem item)
        {
            bool hasRawData = item.RawData != null && item.RawData.Length > 0;
            bool hasTextPreview = !string.IsNullOrEmpty(item.TextPreview);
            
            bool hasData = hasRawData || hasTextPreview;
            
            _loggingService?.LogInfo($"{GetType().Name}.HasUsableData - Élément ID {item.Id} a des données utilisables: {hasData} (RawData: {hasRawData}, TextPreview: {hasTextPreview})");
            
            return hasData;
        }
    }
}
