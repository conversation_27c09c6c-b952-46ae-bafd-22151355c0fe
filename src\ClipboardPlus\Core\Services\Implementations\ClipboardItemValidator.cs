using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Implementations
{
    /// <summary>
    /// Service de validation des éléments du presse-papiers selon les règles métier.
    /// Responsabilité unique : Valider les éléments selon les règles de l'application.
    /// </summary>
    public class ClipboardItemValidator : IClipboardItemValidator
    {
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du validateur d'éléments.
        /// </summary>
        /// <param name="loggingService">Service de logging pour traçabilité</param>
        public ClipboardItemValidator(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Valide un élément du presse-papiers selon les règles métier.
        /// </summary>
        /// <param name="item">L'élément à valider</param>
        /// <param name="maxSizeBytes">Taille maximale autorisée en octets</param>
        /// <returns>Résultat de la validation</returns>
        /// <exception cref="ArgumentNullException">Si l'élément est null</exception>
        /// <exception cref="ArgumentException">Si l'élément ne respecte pas les règles de validation</exception>
        public async Task<Interfaces.ValidationResult> ValidateAsync(ClipboardItem item, long maxSizeBytes)
        {
            var operationId = Guid.NewGuid().ToString()[..8];
            _loggingService?.LogInfo($"[{operationId}] ClipboardItemValidator.ValidateAsync - Début validation");

            try
            {
                // Règle 1 : L'élément ne peut pas être null
                var nullValidation = ValidateNotNull(item, operationId);
                if (!nullValidation.IsValid)
                {
                    return nullValidation;
                }

                // Règle 2 : Validation de la taille des données
                var sizeValidation = ValidateSize(item, maxSizeBytes, operationId);
                if (!sizeValidation.IsValid)
                {
                    return sizeValidation;
                }

                // Règle 3 : Validation du type de données
                var typeValidation = ValidateDataType(item, operationId);
                if (!typeValidation.IsValid)
                {
                    return typeValidation;
                }

                // Règle 4 : Validation de la cohérence des données
                var consistencyValidation = ValidateDataConsistency(item, operationId);
                if (!consistencyValidation.IsValid)
                {
                    return consistencyValidation;
                }

                _loggingService?.LogInfo($"[{operationId}] ClipboardItemValidator.ValidateAsync - Validation réussie");
                return Interfaces.ValidationResult.Success();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] ClipboardItemValidator.ValidateAsync - Erreur inattendue: {ex.Message}", ex);
                return Interfaces.ValidationResult.Failure("Erreur inattendue lors de la validation", "VALIDATION_ERROR");
            }
        }

        /// <summary>
        /// Valide que l'élément n'est pas null.
        /// </summary>
        private Interfaces.ValidationResult ValidateNotNull(ClipboardItem? item, string operationId)
        {
            if (item == null)
            {
                _loggingService?.LogError($"[{operationId}] Validation échouée : élément null fourni");
                return Interfaces.ValidationResult.Failure("L'élément fourni est null.", "NULL_ITEM");
            }

            return Interfaces.ValidationResult.Success();
        }

        /// <summary>
        /// Valide la taille des données brutes de l'élément.
        /// </summary>
        private Interfaces.ValidationResult ValidateSize(ClipboardItem item, long maxSizeBytes, string operationId)
        {
            if (item.RawData != null && item.RawData.Length > maxSizeBytes)
            {
                var errorMessage = $"La taille de l'élément ({item.RawData.Length} octets) dépasse la taille maximale autorisée ({maxSizeBytes} octets).";
                _loggingService?.LogWarning($"[{operationId}] Validation échouée : {errorMessage}");
                return Interfaces.ValidationResult.Failure(errorMessage, "SIZE_EXCEEDED");
            }

            _loggingService?.LogDebug($"[{operationId}] Validation taille OK : {item.RawData?.Length ?? 0} octets <= {maxSizeBytes} octets");
            return Interfaces.ValidationResult.Success();
        }

        /// <summary>
        /// Valide que le type de données est supporté.
        /// </summary>
        private Interfaces.ValidationResult ValidateDataType(ClipboardItem item, string operationId)
        {
            // Vérifier que le type de données est défini et valide
            if (!Enum.IsDefined(typeof(ClipboardDataType), item.DataType))
            {
                var errorMessage = $"Type de données non supporté : {item.DataType}";
                _loggingService?.LogWarning($"[{operationId}] Validation échouée : {errorMessage}");
                return Interfaces.ValidationResult.Failure(errorMessage, "INVALID_DATA_TYPE");
            }

            _loggingService?.LogDebug($"[{operationId}] Validation type OK : {item.DataType}");
            return Interfaces.ValidationResult.Success();
        }

        /// <summary>
        /// Valide la cohérence entre le type de données et le contenu.
        /// </summary>
        private Interfaces.ValidationResult ValidateDataConsistency(ClipboardItem item, string operationId)
        {
            // Pour les éléments de type Text, s'assurer qu'il y a des données ou un aperçu
            if (item.DataType == ClipboardDataType.Text)
            {
                if (item.RawData == null && string.IsNullOrEmpty(item.TextPreview))
                {
                    var errorMessage = "Un élément de type Text doit avoir des données brutes ou un aperçu textuel";
                    _loggingService?.LogWarning($"[{operationId}] Validation échouée : {errorMessage}");
                    return Interfaces.ValidationResult.Failure(errorMessage, "INCONSISTENT_TEXT_DATA");
                }
            }

            // Pour les éléments non-Text, s'assurer qu'il y a des données brutes
            if (item.DataType != ClipboardDataType.Text && item.RawData == null)
            {
                var errorMessage = $"Un élément de type {item.DataType} doit avoir des données brutes";
                _loggingService?.LogWarning($"[{operationId}] Validation échouée : {errorMessage}");
                return Interfaces.ValidationResult.Failure(errorMessage, "MISSING_RAW_DATA");
            }

            // Validation du timestamp (ne peut pas être dans le futur)
            if (item.Timestamp > DateTime.Now.AddMinutes(1)) // Tolérance d'1 minute pour les décalages d'horloge
            {
                var errorMessage = "Le timestamp de l'élément ne peut pas être dans le futur";
                _loggingService?.LogWarning($"[{operationId}] Validation échouée : {errorMessage}");
                return Interfaces.ValidationResult.Failure(errorMessage, "FUTURE_TIMESTAMP");
            }

            _loggingService?.LogDebug($"[{operationId}] Validation cohérence OK");
            return Interfaces.ValidationResult.Success();
        }
    }
}
