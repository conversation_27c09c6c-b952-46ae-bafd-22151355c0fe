<Window x:Class="ClipboardPlus.UI.Windows.AdvancedCleanupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Nettoyage Avancé de l'Historique"
        Height="370" Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        ShowInTaskbar="False"
        WindowStyle="SingleBorderWindow"
        Loaded="Window_Loaded">
    
    <Window.Resources>
        <!-- Convertisseurs - Utilisation du convertisseur WPF natif -->

        <!-- Style pour les titres de section -->
        <Style x:Key="SectionTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
            <Setter Property="Foreground" Value="#333333"/>
        </Style>
        
        <!-- Style pour les contrôles de saisie -->
        <Style x:Key="InputControlStyle" TargetType="Control">
            <Setter Property="Margin" Value="0,0,8,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Height" Value="28"/>
        </Style>
        

        
        <!-- Style pour le message d'aperçu -->
        <Style x:Key="PreviewMessageStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,4,0,0"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Section Nettoyage par Date -->
        <StackPanel Grid.Row="0">
            <!-- Titre avec icône -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                <TextBlock Text="🕒" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                <TextBlock Text="Nettoyage par Date" Style="{StaticResource SectionTitleStyle}"/>
            </StackPanel>
            
            <!-- Description -->
            <TextBlock Text="Supprimer les éléments (non épinglés) plus anciens que :" 
                       Margin="0,0,0,12" 
                       Foreground="#555555"/>
            
            <!-- Contrôles de saisie -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                <!-- Champ numérique -->
                <TextBox x:Name="TimeValueTextBox"
                         Text="{Binding TimeValue, UpdateSourceTrigger=PropertyChanged}"
                         Width="40"
                         Height="24"
                         Style="{StaticResource InputControlStyle}"
                         HorizontalContentAlignment="Center"
                         ToolTip="Entrez une valeur numérique"/>
                
                <!-- Sélecteur d'unité -->
                <ComboBox x:Name="TimeUnitComboBox"
                          ItemsSource="{Binding TimeUnits}"
                          SelectedItem="{Binding TimeUnit}"
                          Width="120"
                          Height="24"
                          Style="{StaticResource InputControlStyle}"
                          ToolTip="Sélectionnez l'unité de temps"/>
            </StackPanel>
        </StackPanel>
        
        <!-- Séparateur -->
        <Separator Grid.Row="2" Margin="0,0"/>
        
        <!-- Section Aperçu -->
        <StackPanel Grid.Row="4">
            <!-- Titre avec icône -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                <TextBlock Text="🔥" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                <TextBlock Text="Aperçu de l'action" Style="{StaticResource SectionTitleStyle}"/>
            </StackPanel>
            
            <!-- Message d'aperçu -->
            <Border Background="#FFF9E6" 
                    BorderBrush="#FFE4B3" 
                    BorderThickness="1" 
                    CornerRadius="4" 
                    Padding="12">
                <StackPanel>
                    <TextBlock Text="{Binding PreviewMessage}" 
                               Style="{StaticResource PreviewMessageStyle}"
                               FontWeight="SemiBold"/>
                    <TextBlock Text="Cette opération est irréversible." 
                               Style="{StaticResource PreviewMessageStyle}"
                               Foreground="#CC6600"
                               FontStyle="Italic"
                               Margin="0,4,0,0"/>
                </StackPanel>
            </Border>
        </StackPanel>
        
        <!-- Boutons d'action -->
        <StackPanel Grid.Row="6" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,10,0,0">
            
            <!-- Bouton Annuler -->
            <Button x:Name="CancelButton"
                    Content="Annuler"
                    Command="{Binding CancelCommand}"
                    IsCancel="True"
                    ToolTip="Fermer sans effectuer de nettoyage"/>
            
            <!-- Bouton Exécuter -->
            <Button x:Name="ExecuteButton"
                    Content="Exécuter"
                    Command="{Binding ExecuteCleanupCommand}"
                    IsDefault="True"
                    Background="#FF4444"
                    ToolTip="Exécuter le nettoyage selon les critères définis">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
                        <Setter Property="Background" Value="#FF4444"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsExecuting}" Value="True">
                                <Setter Property="Content" Value="En cours..."/>
                                <Setter Property="IsEnabled" Value="False"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </StackPanel>
        
        <!-- Indicateur de progression (visible pendant l'exécution) -->
        <ProgressBar Grid.Row="7"
                     Height="4"
                     Margin="0,10,0,0"
                     IsIndeterminate="True"
                     Visibility="{Binding IsExecuting, Converter={StaticResource BooleanToVisibilityConverter}}"/>
    </Grid>
</Window>
