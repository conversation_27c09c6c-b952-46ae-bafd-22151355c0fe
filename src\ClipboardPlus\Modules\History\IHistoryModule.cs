using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Modules.Core;
using ClipboardPlus.Modules.Core.Events;

namespace ClipboardPlus.Modules.History
{
    /// <summary>
    /// Interface pour le module de gestion de l'historique.
    /// 
    /// Ce module est responsable de la gestion des collections d'historique,
    /// du filtrage, de la synchronisation et des opérations sur les éléments.
    /// </summary>
    public interface IHistoryModule : ClipboardPlus.Modules.Core.IModule
    {
        /// <summary>
        /// Collection observable des éléments d'historique.
        /// </summary>
        ObservableCollection<ClipboardItem> HistoryItems { get; }

        /// <summary>
        /// Collection observable des éléments filtrés.
        /// </summary>
        ObservableCollection<ClipboardItem> FilteredItems { get; }

        /// <summary>
        /// Élément actuellement sélectionné.
        /// </summary>
        ClipboardItem? SelectedItem { get; set; }

        /// <summary>
        /// Filtre de recherche actuel.
        /// </summary>
        string? SearchFilter { get; set; }

        /// <summary>
        /// Indique si le module est en cours de synchronisation.
        /// </summary>
        bool IsSynchronizing { get; }

        /// <summary>
        /// Nombre total d'éléments dans l'historique.
        /// </summary>
        int TotalItemCount { get; }

        /// <summary>
        /// Nombre d'éléments filtrés visibles.
        /// </summary>
        int FilteredItemCount { get; }

        /// <summary>
        /// Événement déclenché lorsque l'historique change.
        /// </summary>
        event EventHandler<HistoryChangedEventArgs> HistoryChanged;

        /// <summary>
        /// Événement déclenché lorsque la sélection change.
        /// </summary>
        event EventHandler<HistorySelectionChangedEventArgs> SelectionChanged;

        /// <summary>
        /// Événement déclenché lorsque le filtre change.
        /// </summary>
        event EventHandler<HistoryFilterChangedEventArgs> FilterChanged;

        /// <summary>
        /// Charge l'historique depuis la source de données.
        /// </summary>
        /// <param name="callContext">Contexte de l'appel pour le debugging</param>
        /// <returns>Task représentant l'opération de chargement</returns>
        Task LoadHistoryAsync(string callContext = "Unknown");

        /// <summary>
        /// Recharge l'historique en forçant une synchronisation.
        /// </summary>
        /// <param name="reason">Raison du rechargement</param>
        /// <returns>Task représentant l'opération de rechargement</returns>
        Task ReloadHistoryAsync(string reason = "Manual reload");

        /// <summary>
        /// Force une synchronisation des collections.
        /// </summary>
        /// <param name="reason">Raison de la synchronisation forcée</param>
        /// <returns>Task représentant l'opération de synchronisation</returns>
        Task ForceSynchronizationAsync(string reason = "Manual sync");

        /// <summary>
        /// Ajoute un nouvel élément à l'historique.
        /// </summary>
        /// <param name="item">Élément à ajouter</param>
        /// <returns>Task représentant l'opération d'ajout</returns>
        Task AddItemAsync(ClipboardItem item);

        /// <summary>
        /// Supprime un élément de l'historique.
        /// </summary>
        /// <param name="item">Élément à supprimer</param>
        /// <returns>Task représentant l'opération de suppression</returns>
        Task RemoveItemAsync(ClipboardItem item);

        /// <summary>
        /// Supprime plusieurs éléments de l'historique.
        /// </summary>
        /// <param name="items">Éléments à supprimer</param>
        /// <returns>Task représentant l'opération de suppression</returns>
        Task RemoveItemsAsync(IEnumerable<ClipboardItem> items);

        /// <summary>
        /// Efface tout l'historique.
        /// </summary>
        /// <returns>Task représentant l'opération d'effacement</returns>
        Task ClearHistoryAsync();

        /// <summary>
        /// Met à jour un élément existant dans l'historique.
        /// </summary>
        /// <param name="item">Élément à mettre à jour</param>
        /// <returns>Task représentant l'opération de mise à jour</returns>
        Task UpdateItemAsync(ClipboardItem item);

        /// <summary>
        /// Applique un filtre de recherche.
        /// </summary>
        /// <param name="filter">Filtre à appliquer</param>
        void ApplyFilter(string? filter);

        /// <summary>
        /// Efface le filtre de recherche.
        /// </summary>
        void ClearFilter();

        /// <summary>
        /// Sélectionne un élément spécifique.
        /// </summary>
        /// <param name="item">Élément à sélectionner</param>
        void SelectItem(ClipboardItem? item);

        /// <summary>
        /// Sélectionne l'élément suivant dans la liste.
        /// </summary>
        void SelectNextItem();

        /// <summary>
        /// Sélectionne l'élément précédent dans la liste.
        /// </summary>
        void SelectPreviousItem();

        /// <summary>
        /// Trouve un élément par son ID.
        /// </summary>
        /// <param name="id">ID de l'élément</param>
        /// <returns>Élément trouvé ou null</returns>
        ClipboardItem? FindItemById(long id);

        /// <summary>
        /// Trouve des éléments correspondant à un critère.
        /// </summary>
        /// <param name="predicate">Critère de recherche</param>
        /// <returns>Éléments correspondants</returns>
        IEnumerable<ClipboardItem> FindItems(Func<ClipboardItem, bool> predicate);

        /// <summary>
        /// Obtient les statistiques de l'historique.
        /// </summary>
        /// <returns>Statistiques de l'historique</returns>
        HistoryStatistics GetStatistics();
    }

    /// <summary>
    /// Arguments d'événement pour les changements d'historique.
    /// </summary>
    public class HistoryChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Type de changement.
        /// </summary>
        public HistoryChangeType ChangeType { get; }

        /// <summary>
        /// Éléments affectés par le changement.
        /// </summary>
        public IReadOnlyList<ClipboardItem> AffectedItems { get; }

        /// <summary>
        /// Contexte du changement.
        /// </summary>
        public string? Context { get; }

        public HistoryChangedEventArgs(HistoryChangeType changeType, IEnumerable<ClipboardItem> affectedItems, string? context = null)
        {
            ChangeType = changeType;
            AffectedItems = affectedItems.ToList().AsReadOnly();
            Context = context;
        }

        public HistoryChangedEventArgs(HistoryChangeType changeType, ClipboardItem affectedItem, string? context = null)
            : this(changeType, new[] { affectedItem }, context)
        {
        }
    }

    /// <summary>
    /// Arguments d'événement pour les changements de sélection.
    /// </summary>
    public class HistorySelectionChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Élément précédemment sélectionné.
        /// </summary>
        public ClipboardItem? PreviousItem { get; }

        /// <summary>
        /// Élément actuellement sélectionné.
        /// </summary>
        public ClipboardItem? CurrentItem { get; }

        public HistorySelectionChangedEventArgs(ClipboardItem? previousItem, ClipboardItem? currentItem)
        {
            PreviousItem = previousItem;
            CurrentItem = currentItem;
        }
    }

    /// <summary>
    /// Arguments d'événement pour les changements de filtre.
    /// </summary>
    public class HistoryFilterChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Filtre précédent.
        /// </summary>
        public string? PreviousFilter { get; }

        /// <summary>
        /// Filtre actuel.
        /// </summary>
        public string? CurrentFilter { get; }

        /// <summary>
        /// Nombre d'éléments correspondant au nouveau filtre.
        /// </summary>
        public int MatchingItemCount { get; }

        public HistoryFilterChangedEventArgs(string? previousFilter, string? currentFilter, int matchingItemCount)
        {
            PreviousFilter = previousFilter;
            CurrentFilter = currentFilter;
            MatchingItemCount = matchingItemCount;
        }
    }

    /// <summary>
    /// Types de changements d'historique.
    /// </summary>
    public enum HistoryChangeType
    {
        /// <summary>
        /// Élément ajouté.
        /// </summary>
        ItemAdded,

        /// <summary>
        /// Élément supprimé.
        /// </summary>
        ItemRemoved,

        /// <summary>
        /// Élément mis à jour.
        /// </summary>
        ItemUpdated,

        /// <summary>
        /// Historique effacé.
        /// </summary>
        HistoryCleared,

        /// <summary>
        /// Historique rechargé.
        /// </summary>
        HistoryReloaded,

        /// <summary>
        /// Collections synchronisées.
        /// </summary>
        CollectionsSynchronized
    }

    /// <summary>
    /// Statistiques de l'historique.
    /// </summary>
    public class HistoryStatistics
    {
        /// <summary>
        /// Nombre total d'éléments.
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// Nombre d'éléments filtrés.
        /// </summary>
        public int FilteredItems { get; set; }

        /// <summary>
        /// Taille totale en octets.
        /// </summary>
        public long TotalSizeBytes { get; set; }

        /// <summary>
        /// Date du dernier ajout.
        /// </summary>
        public DateTime? LastAddedDate { get; set; }

        /// <summary>
        /// Date de la dernière synchronisation.
        /// </summary>
        public DateTime? LastSyncDate { get; set; }

        /// <summary>
        /// Nombre de synchronisations effectuées.
        /// </summary>
        public int SyncCount { get; set; }

        /// <summary>
        /// Filtre actuel.
        /// </summary>
        public string? CurrentFilter { get; set; }
    }
}
