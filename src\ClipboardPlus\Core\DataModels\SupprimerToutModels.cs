using System;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.DataModels
{
    /// <summary>
    /// Requête pour l'opération de suppression de tous les éléments.
    /// </summary>
    public record SupprimerToutRequest(
        string OperationId,
        ClipboardHistoryViewModel ViewModel,
        bool PreservePinned = true
    )
    {
        /// <summary>
        /// Valide la requête et retourne les erreurs éventuelles.
        /// </summary>
        public SupprimerToutValidationResult Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(OperationId))
                errors.Add("OperationId ne peut pas être vide");

            if (ViewModel == null)
                errors.Add("ViewModel ne peut pas être null");

            return new SupprimerToutValidationResult(errors.Count == 0, errors);
        }
    }

    /// <summary>
    /// Résultat de l'analyse des éléments à supprimer.
    /// </summary>
    public record SupprimerToutAnalysis(
        int TotalItems,
        int PinnedItems,
        int ItemsToDelete,
        bool HasItemsToDelete
    )
    {
        /// <summary>
        /// Crée une analyse à partir d'une collection d'éléments.
        /// </summary>
        public static SupprimerToutAnalysis FromItems(IEnumerable<ClipboardItem> items, bool preservePinned = true)
        {
            var itemsList = items?.ToList() ?? new List<ClipboardItem>();
            var totalItems = itemsList.Count;
            var pinnedItems = preservePinned ? itemsList.Count(item => item.IsPinned) : 0;
            var itemsToDelete = totalItems - pinnedItems;

            return new SupprimerToutAnalysis(
                TotalItems: totalItems,
                PinnedItems: pinnedItems,
                ItemsToDelete: itemsToDelete,
                HasItemsToDelete: itemsToDelete > 0
            );
        }

        /// <summary>
        /// Génère le message de confirmation pour l'utilisateur.
        /// </summary>
        public string GenerateConfirmationMessage()
        {
            if (!HasItemsToDelete)
                return "Aucun élément à supprimer. Tous les éléments sont épinglés.";

            return $"Êtes-vous sûr de vouloir supprimer {ItemsToDelete} élément(s) ?\n\n" +
                   $"• {ItemsToDelete} élément(s) seront supprimés\n" +
                   $"• {PinnedItems} élément(s) épinglés seront conservés\n\n" +
                   "Cette action est irréversible.";
        }
    }

    /// <summary>
    /// Résultat de l'opération de suppression.
    /// </summary>
    public record SupprimerToutResult(
        bool Success,
        string OperationId,
        int ItemsDeleted,
        string? ErrorMessage = null,
        Exception? Exception = null
    )
    {
        /// <summary>
        /// Crée un résultat de succès.
        /// </summary>
        public static SupprimerToutResult CreateSuccess(string operationId, int itemsDeleted)
        {
            return new SupprimerToutResult(
                Success: true,
                OperationId: operationId,
                ItemsDeleted: itemsDeleted
            );
        }

        /// <summary>
        /// Crée un résultat d'échec.
        /// </summary>
        public static SupprimerToutResult CreateFailure(string operationId, string errorMessage, Exception? exception = null)
        {
            return new SupprimerToutResult(
                Success: false,
                OperationId: operationId,
                ItemsDeleted: 0,
                ErrorMessage: errorMessage,
                Exception: exception
            );
        }
    }

    /// <summary>
    /// Résultat de validation pour les opérations SupprimerTout.
    /// </summary>
    public record SupprimerToutValidationResult(
        bool IsValid,
        IReadOnlyList<string> Errors
    )
    {
        /// <summary>
        /// Résultat de validation réussie.
        /// </summary>
        public static SupprimerToutValidationResult Success => new(true, Array.Empty<string>());

        /// <summary>
        /// Crée un résultat d'échec avec une seule erreur.
        /// </summary>
        public static SupprimerToutValidationResult Failure(string error) => new(false, new[] { error });

        /// <summary>
        /// Crée un résultat d'échec avec plusieurs erreurs.
        /// </summary>
        public static SupprimerToutValidationResult Failure(IEnumerable<string> errors) => new(false, errors.ToList());

        /// <summary>
        /// Message d'erreur combiné.
        /// </summary>
        public string ErrorMessage => string.Join("; ", Errors);
    }

    /// <summary>
    /// Résultat d'exécution d'une opération.
    /// </summary>
    public record ExecutionResult(
        bool Success,
        string? ErrorMessage = null,
        Exception? Exception = null
    )
    {
        /// <summary>
        /// Résultat de succès.
        /// </summary>
        public static ExecutionResult CreateSuccess() => new(true);

        /// <summary>
        /// Crée un résultat d'échec.
        /// </summary>
        public static ExecutionResult Failure(string errorMessage, Exception? exception = null)
        {
            return new ExecutionResult(false, errorMessage, exception);
        }
    }
}
