using System;
using System.Collections.Generic;

namespace ClipboardPlus.Core.DataModels.Settings
{
    /// <summary>
    /// Données des paramètres de base de l'application.
    /// </summary>
    public record BasicSettingsData(
        int MaxHistoryItems,
        bool StartWithWindows,
        string ShortcutKeyCombination,
        int MaxImageDimensionForThumbnail,
        long MaxStorableItemSizeBytes
    );

    /// <summary>
    /// Données des paramètres avancés de l'application.
    /// </summary>
    public record AdvancedSettingsData(
        bool HideTimestamp,
        bool HideItemTitle,
        ThemeInfo? SelectedTheme
    );

    /// <summary>
    /// Paramètres de visibilité pour les notifications.
    /// </summary>
    public record VisibilitySettings(
        bool HideTimestamp,
        bool HideItemTitle
    );

    /// <summary>
    /// Données complètes de tous les paramètres.
    /// </summary>
    public record CompleteSettingsData(
        BasicSettingsData BasicSettings,
        AdvancedSettingsData AdvancedSettings
    );

    /// <summary>
    /// Données partielles des paramètres pour sauvegarde sélective.
    /// </summary>
    public record PartialSettingsData(
        Dictionary<string, object> ChangedSettings
    );

    /// <summary>
    /// Résultat de l'application des paramètres.
    /// </summary>
    public record SettingsApplicationResult(
        bool Success,
        string Message,
        Dictionary<string, object> AppliedSettings,
        List<string> Errors
    );

    /// <summary>
    /// Résultat de la persistance des paramètres.
    /// </summary>
    public record PersistenceResult(
        bool Success,
        string Message,
        List<string> SavedSettings,
        List<string> Errors
    );

    /// <summary>
    /// Résultat de validation des paramètres.
    /// </summary>
    public record SettingsValidationResult(
        bool IsValid,
        string Message,
        List<string> ValidationErrors
    );
}
