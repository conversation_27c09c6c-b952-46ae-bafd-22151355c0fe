using System;
using System.Collections.Specialized;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media.Imaging;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Extensions;
using WpfClipboard = System.Windows.Clipboard;
using DataFormats = System.Windows.DataFormats;
using System.Windows.Threading;
using System.Linq;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Implémentation de IClipboardInteractionService pour l'environnement WPF.
    /// Encapsule toutes les interactions avec System.Windows.Clipboard.
    /// </summary>
    public class ClipboardInteractionService : IClipboardInteractionService
    {
        private readonly ILoggingService? _loggingService;
        private readonly System.Windows.Threading.Dispatcher _dispatcher;
        private const int MAX_RETRIES_FOR_CLIPBOARD = 5;
        private const int RETRY_DELAY_MS_FOR_CLIPBOARD = 120;

        public ClipboardInteractionService(ILoggingService? loggingService)
        {
            _loggingService = loggingService;
            
            // Vérifier si Application.Current est null (peut arriver dans les tests unitaires)
            if (System.Windows.Application.Current != null)
            {
                _dispatcher = System.Windows.Application.Current.Dispatcher;
            }
            else
            {
                // Créer un nouveau dispatcher si nous sommes dans un contexte de test
                _dispatcher = Dispatcher.CurrentDispatcher;
                _loggingService?.LogInfo("Application.Current est null, utilisation de Dispatcher.CurrentDispatcher");
            }
        }

        /// <inheritdoc />
        public async Task<ClipboardData?> GetClipboardContentAsync()
        {
            for (int i = 0; i < MAX_RETRIES_FOR_CLIPBOARD; i++)
            {
                try
                {
                    var clipboardData = new ClipboardData();
                    if (WpfClipboard.ContainsText())
                    {
                        clipboardData.Text = WpfClipboard.GetText();
                    }
                    if (WpfClipboard.ContainsImage())
                    {
                        clipboardData.Image = WpfClipboard.GetImage();
                    }
                    if (WpfClipboard.ContainsFileDropList())
                    {
                        clipboardData.FileDropList = WpfClipboard.GetFileDropList();
                    }
                    return clipboardData;
                }
                catch (COMException ex)
                {
                    _loggingService?.LogWarning($"Attempt {i + 1}/{MAX_RETRIES_FOR_CLIPBOARD} to get clipboard content failed: {ex.Message}");
                    if (i < MAX_RETRIES_FOR_CLIPBOARD - 1)
                    {
                        await Task.Delay(RETRY_DELAY_MS_FOR_CLIPBOARD);
                    }
                    else
                    {
                        _loggingService?.LogError("All attempts to get clipboard content failed.", ex);
                        return null;
                    }
                }
            }
            return null;
        }

        /// <inheritdoc />
        public async Task<bool> SetClipboardContentAsync(string text)
        {
            for (int i = 0; i < MAX_RETRIES_FOR_CLIPBOARD; i++)
            {
                try
                {
                    WpfClipboard.SetText(text);
                    return true;
                }
                catch (COMException ex)
                {
                    _loggingService?.LogWarning($"Attempt {i + 1}/{MAX_RETRIES_FOR_CLIPBOARD} to set clipboard content failed: {ex.Message}");
                    if (i < MAX_RETRIES_FOR_CLIPBOARD - 1)
                    {
                        await Task.Delay(RETRY_DELAY_MS_FOR_CLIPBOARD);
                    }
                    else
                    {
                        _loggingService?.LogError("All attempts to set clipboard content failed.", ex);
                        return false;
                    }
                }
            }
            return false;
        }
        
        /// <inheritdoc />
        public bool ContainsText()
        {
            try
            {
                return WpfClipboard.ContainsText();
            }
            catch (COMException ex)
            {
                _loggingService?.LogWarning($"Error checking if clipboard contains text: {ex.Message}");
                return false;
            }
        }
        
        /// <inheritdoc />
        public async Task<string?> GetTextAsync()
        {
            return await _dispatcher.InvokeAsync(() =>
            {
                try
                {
                    if (WpfClipboard.ContainsText())
                    {
                        return WpfClipboard.GetText();
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError("Erreur lors de la récupération du texte du presse-papiers.", ex);
                }
                return null;
            });
        }
        
        /// <inheritdoc />
        public bool ContainsImage()
        {
            try
            {
                return WpfClipboard.ContainsImage();
            }
            catch (COMException ex)
            {
                _loggingService?.LogWarning($"Error checking if clipboard contains image: {ex.Message}");
                return false;
            }
        }
        
        /// <inheritdoc />
        public async Task<BitmapSource?> GetImageAsync()
        {
            return await _dispatcher.InvokeAsync(() =>
            {
                try
                {
                    if (WpfClipboard.ContainsImage())
                    {
                        return WpfClipboard.GetImage();
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError("Erreur lors de la récupération de l'image du presse-papiers.", ex);
                }
                return null;
            });
        }
        
        /// <inheritdoc />
        public bool ContainsFileDropList()
        {
            try
            {
                return WpfClipboard.ContainsFileDropList();
            }
            catch (COMException ex)
            {
                _loggingService?.LogWarning($"Error checking if clipboard contains file drop list: {ex.Message}");
                return false;
            }
        }
        
        /// <inheritdoc />
        public async Task<StringCollection?> GetFileDropListAsync()
        {
            return await _dispatcher.InvokeAsync(() =>
            {
                try
                {
                    if (WpfClipboard.ContainsFileDropList())
                    {
                        return WpfClipboard.GetFileDropList();
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError("Erreur lors de la récupération de la liste de fichiers du presse-papiers.", ex);
                }
                return null;
            });
        }

        public async Task SetClipboardContentAsync(ClipboardItem item)
        {
            if (item == null)
            {
                _loggingService?.LogWarning("Tentative de définir le contenu du presse-papiers avec un élément null.");
                return;
            }

            await _dispatcher.InvokeAsync(() =>
            {
                try
                {
                    var dataObject = item.ToDataObject();
                    WpfClipboard.SetDataObject(dataObject, true);
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"Erreur lors de la définition du contenu du presse-papiers pour l'élément ID {item.Id}.", ex);
                }
            });
        }
    }
} 