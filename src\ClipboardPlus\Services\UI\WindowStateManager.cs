using System.Linq;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.UI;
using WpfApplication = System.Windows.Application;

namespace ClipboardPlus.Services.UI
{
    /// <summary>
    /// Implémentation du service de gestion de l'état des fenêtres.
    /// Responsabilité unique : Gérer l'état, la visibilité et l'activation des fenêtres.
    /// </summary>
    public class WindowStateManager : IWindowStateManager
    {
        private readonly ILoggingService? _loggingService;

        public WindowStateManager(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
        }

        /// <summary>
        /// Tente d'activer une fenêtre existante du type spécifié.
        /// </summary>
        public bool TryActivateExistingWindow<T>() where T : Window
        {
            try
            {
                var existingWindow = FindWindow<T>();
                if (existingWindow != null)
                {
                    EnsureWindowVisible(existingWindow);
                    _loggingService?.LogInfo($"WindowStateManager: Fenêtre {typeof(T).Name} activée avec succès");
                    return true;
                }

                _loggingService?.LogInfo($"WindowStateManager: Aucune fenêtre {typeof(T).Name} trouvée");
                return false;
            }
            catch (System.Exception ex)
            {
                _loggingService?.LogError($"WindowStateManager: Erreur lors de l'activation de {typeof(T).Name}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Recherche une fenêtre du type spécifié.
        /// </summary>
        public T? FindWindow<T>() where T : Window
        {
            try
            {
                return WpfApplication.Current?.Windows.OfType<T>().FirstOrDefault();
            }
            catch (System.Exception ex)
            {
                _loggingService?.LogError($"WindowStateManager: Erreur lors de la recherche de {typeof(T).Name}: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// S'assure qu'une fenêtre est visible (pas minimisée) et l'active.
        /// </summary>
        public void EnsureWindowVisible(Window window)
        {
            try
            {
                if (window.WindowState == WindowState.Minimized)
                {
                    window.WindowState = WindowState.Normal;
                    _loggingService?.LogInfo("WindowStateManager: Fenêtre restaurée depuis l'état minimisé");
                }

                window.Activate();
                window.Focus();
                _loggingService?.LogInfo("WindowStateManager: Fenêtre activée et focus défini");
            }
            catch (System.Exception ex)
            {
                _loggingService?.LogError($"WindowStateManager: Erreur lors de la mise en visibilité: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Recherche une fenêtre par son type exact.
        /// </summary>
        public Window? FindWindowByType(System.Type windowType)
        {
            try
            {
                return WpfApplication.Current?.Windows.Cast<Window>()
                    .FirstOrDefault(w => w.GetType() == windowType);
            }
            catch (System.Exception ex)
            {
                _loggingService?.LogError($"WindowStateManager: Erreur lors de la recherche par type {windowType.Name}: {ex.Message}", ex);
                return null;
            }
        }
    }
}
