using ClipboardPlus.Core.Services;

namespace ClipboardPlus.UI.ViewModels.Construction.Interfaces
{
    /// <summary>
    /// Service de validation des paramètres du constructeur.
    /// Responsabilité : Validation que tous les paramètres obligatoires sont non-null selon le principe SRP.
    /// </summary>
    public interface IParameterValidator
    {
        /// <summary>
        /// Valide que tous les paramètres obligatoires sont non-null.
        /// Cette méthode centralise toute la logique de validation des paramètres
        /// pour respecter le principe de responsabilité unique (SRP).
        /// </summary>
        /// <param name="historyManager">Gestionnaire de l'historique du presse-papiers</param>
        /// <param name="clipboardService">Service d'interaction avec le presse-papiers</param>
        /// <param name="settingsManager">Gestionnaire des paramètres de l'application</param>
        /// <param name="notificationService">Service de notifications utilisateur</param>
        /// <param name="userInteractionService">Service d'interaction utilisateur</param>
        /// <param name="serviceProvider">Fournisseur de services pour l'injection de dépendances</param>
        /// <param name="renameService">Service de renommage des éléments</param>
        /// <exception cref="ArgumentNullException">Si l'un des paramètres obligatoires est null</exception>
        void ValidateRequiredParameters(
            IClipboardHistoryManager historyManager,
            IClipboardInteractionService clipboardService,
            ISettingsManager settingsManager,
            IUserNotificationService notificationService,
            IUserInteractionService userInteractionService,
            IServiceProvider serviceProvider,
            IRenameService renameService);
    }
}
