using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.Implementations;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services;
using Moq;
using NUnit.Framework;
using ValidationResult = ClipboardPlus.Core.Services.Interfaces.ValidationResult;

namespace ClipboardPlus.Tests.Unit.Core.Services.Implementations
{
    [TestFixture]
    public class ClipboardItemOrchestratorTests
    {
        private Mock<IClipboardItemValidator> _mockValidator = null!;
        private Mock<IDuplicateDetector> _mockDuplicateDetector = null!;
        private Mock<IClipboardItemProcessor> _mockProcessor = null!;
        private Mock<IHistoryManager> _mockHistoryManager = null!;
        private Mock<IEventNotifier> _mockEventNotifier = null!;
        private Mock<IOperationLogger> _mockOperationLogger = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IOperationContext> _mockOperationContext = null!;
        private ClipboardItemOrchestrator _orchestrator = null!;

        [SetUp]
        public void SetUp()
        {
            _mockValidator = new Mock<IClipboardItemValidator>();
            _mockDuplicateDetector = new Mock<IDuplicateDetector>();
            _mockProcessor = new Mock<IClipboardItemProcessor>();
            _mockHistoryManager = new Mock<IHistoryManager>();
            _mockEventNotifier = new Mock<IEventNotifier>();
            _mockOperationLogger = new Mock<IOperationLogger>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockOperationContext = new Mock<IOperationContext>();

            // Configuration par défaut du mock OperationLogger
            _mockOperationLogger
                .Setup(x => x.StartOperation(It.IsAny<string>(), It.IsAny<ClipboardItem>()))
                .Returns(_mockOperationContext.Object);

            // Configuration par défaut des settings
            _mockSettingsManager.Setup(x => x.MaxStorableItemSizeBytes).Returns(1024 * 1024); // 1MB
            _mockSettingsManager.Setup(x => x.MaxHistoryItems).Returns(100);

            _orchestrator = new ClipboardItemOrchestrator(
                _mockValidator.Object,
                _mockDuplicateDetector.Object,
                _mockProcessor.Object,
                _mockHistoryManager.Object,
                _mockEventNotifier.Object,
                _mockOperationLogger.Object,
                _mockSettingsManager.Object);
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithNullValidator_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                new ClipboardItemOrchestrator(
                    null!,
                    _mockDuplicateDetector.Object,
                    _mockProcessor.Object,
                    _mockHistoryManager.Object,
                    _mockEventNotifier.Object,
                    _mockOperationLogger.Object,
                    _mockSettingsManager.Object));

            Assert.That(ex.ParamName, Is.EqualTo("validator"));
        }

        [Test]
        public void Constructor_WithNullDuplicateDetector_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                new ClipboardItemOrchestrator(
                    _mockValidator.Object,
                    null!,
                    _mockProcessor.Object,
                    _mockHistoryManager.Object,
                    _mockEventNotifier.Object,
                    _mockOperationLogger.Object,
                    _mockSettingsManager.Object));

            Assert.That(ex.ParamName, Is.EqualTo("duplicateDetector"));
        }

        [Test]
        public void Constructor_WithNullProcessor_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                new ClipboardItemOrchestrator(
                    _mockValidator.Object,
                    _mockDuplicateDetector.Object,
                    null!,
                    _mockHistoryManager.Object,
                    _mockEventNotifier.Object,
                    _mockOperationLogger.Object,
                    _mockSettingsManager.Object));

            Assert.That(ex.ParamName, Is.EqualTo("processor"));
        }

        [Test]
        public void Constructor_WithNullHistoryManager_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                new ClipboardItemOrchestrator(
                    _mockValidator.Object,
                    _mockDuplicateDetector.Object,
                    _mockProcessor.Object,
                    null!,
                    _mockEventNotifier.Object,
                    _mockOperationLogger.Object,
                    _mockSettingsManager.Object));

            Assert.That(ex.ParamName, Is.EqualTo("historyManager"));
        }

        [Test]
        public void Constructor_WithNullEventNotifier_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                new ClipboardItemOrchestrator(
                    _mockValidator.Object,
                    _mockDuplicateDetector.Object,
                    _mockProcessor.Object,
                    _mockHistoryManager.Object,
                    null!,
                    _mockOperationLogger.Object,
                    _mockSettingsManager.Object));

            Assert.That(ex.ParamName, Is.EqualTo("eventNotifier"));
        }

        [Test]
        public void Constructor_WithNullOperationLogger_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                new ClipboardItemOrchestrator(
                    _mockValidator.Object,
                    _mockDuplicateDetector.Object,
                    _mockProcessor.Object,
                    _mockHistoryManager.Object,
                    _mockEventNotifier.Object,
                    null!,
                    _mockSettingsManager.Object));

            Assert.That(ex.ParamName, Is.EqualTo("operationLogger"));
        }

        [Test]
        public void Constructor_WithNullSettingsManager_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                new ClipboardItemOrchestrator(
                    _mockValidator.Object,
                    _mockDuplicateDetector.Object,
                    _mockProcessor.Object,
                    _mockHistoryManager.Object,
                    _mockEventNotifier.Object,
                    _mockOperationLogger.Object,
                    null!));

            Assert.That(ex.ParamName, Is.EqualTo("settingsManager"));
        }

        [Test]
        public void Constructor_WithValidParameters_CreatesInstance()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
                new ClipboardItemOrchestrator(
                    _mockValidator.Object,
                    _mockDuplicateDetector.Object,
                    _mockProcessor.Object,
                    _mockHistoryManager.Object,
                    _mockEventNotifier.Object,
                    _mockOperationLogger.Object,
                    _mockSettingsManager.Object));
        }

        #endregion

        #region AddItemAsync Tests

        [Test]
        public async Task AddItemAsync_WithValidationFailure_ThrowsArgumentException()
        {
            // Arrange
            var item = new ClipboardItem { DataType = ClipboardDataType.Text, TextPreview = "Test" };
            var validationResult = ValidationResult.Failure("Validation failed");

            _mockValidator.Setup(x => x.ValidateAsync(item, It.IsAny<long>())).ReturnsAsync(validationResult);

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(async () => await _orchestrator.AddItemAsync(item));
            Assert.That(ex.Message, Does.Contain("Validation failed"));
            Assert.That(ex.ParamName, Is.EqualTo("item"));

            // Verify operation logging
            _mockOperationLogger.Verify(x => x.StartOperation("AddItemAsync", item), Times.Once);
            _mockOperationContext.Verify(x => x.LogError("Validation échouée: Validation failed", It.IsAny<Exception>()), Times.Once);
            _mockOperationContext.Verify(x => x.Fail("Validation échouée: Validation failed"), Times.Once);
        }

        [Test]
        public async Task AddItemAsync_WithOversizedItem_ThrowsArgumentException()
        {
            // Arrange
            var oversizedData = new byte[2 * 1024 * 1024]; // 2MB
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                RawData = oversizedData
            };
            var validationResult = ValidationResult.Failure("La taille de l'élément dépasse la taille maximale autorisée");

            _mockValidator.Setup(x => x.ValidateAsync(item, It.IsAny<long>())).ReturnsAsync(validationResult);

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(async () => await _orchestrator.AddItemAsync(item));
            Assert.That(ex.Message, Does.Contain("dépasse la taille maximale autorisée"));
            Assert.That(ex.ParamName, Is.EqualTo("item"));

            // Verify operation logging
            _mockOperationContext.Verify(x => x.LogError(It.Is<string>(s => s.Contains("Validation échouée")), It.IsAny<Exception>()), Times.Once);
            _mockOperationContext.Verify(x => x.Fail(It.Is<string>(s => s.Contains("Validation échouée"))), Times.Once);
        }

        [Test]
        public async Task AddItemAsync_WithDuplicateFound_UpdatesExistingItem()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                Timestamp = DateTime.Now
            };
            var existingItem = new ClipboardItem
            {
                Id = 123,
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                Timestamp = DateTime.Now.AddMinutes(-1)
            };
            var historyItems = new List<ClipboardItem> { existingItem };

            _mockValidator.Setup(x => x.ValidateAsync(item, It.IsAny<long>())).ReturnsAsync(ValidationResult.Success());
            _mockHistoryManager.Setup(x => x.GetHistoryItems()).Returns(historyItems);
            _mockDuplicateDetector.Setup(x => x.FindDuplicateAsync(item, historyItems)).ReturnsAsync(existingItem);
            _mockProcessor.Setup(x => x.ProcessExistingItemAsync(existingItem, item.Timestamp)).ReturnsAsync(123);

            // Act
            var result = await _orchestrator.AddItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(123));

            // Verify the workflow
            _mockValidator.Verify(x => x.ValidateAsync(item, It.IsAny<long>()), Times.Once);
            _mockHistoryManager.Verify(x => x.GetHistoryItems(), Times.Once);
            _mockDuplicateDetector.Verify(x => x.FindDuplicateAsync(item, historyItems), Times.Once);
            _mockProcessor.Verify(x => x.ProcessExistingItemAsync(existingItem, item.Timestamp), Times.Once);
            _mockEventNotifier.Verify(x => x.NotifyHistoryChanged(existingItem, HistoryChangeType.ItemUpdated), Times.Once);
            _mockOperationContext.Verify(x => x.Complete(It.IsAny<long>()), Times.Once);

            // Verify that new item insertion was NOT called
            _mockProcessor.Verify(x => x.ProcessNewItemAsync(It.IsAny<ClipboardItem>()), Times.Never);
            _mockHistoryManager.Verify(x => x.AddToHistory(It.IsAny<ClipboardItem>()), Times.Never);
        }

        [Test]
        public async Task AddItemAsync_WithDuplicateUpdateFailure_ReturnsZero()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                Timestamp = DateTime.Now
            };
            var existingItem = new ClipboardItem
            {
                Id = 123,
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                Timestamp = DateTime.Now.AddMinutes(-1)
            };
            var historyItems = new List<ClipboardItem> { existingItem };

            _mockValidator.Setup(x => x.ValidateAsync(item, It.IsAny<long>())).ReturnsAsync(ValidationResult.Success());
            _mockHistoryManager.Setup(x => x.GetHistoryItems()).Returns(historyItems);
            _mockDuplicateDetector.Setup(x => x.FindDuplicateAsync(item, historyItems)).ReturnsAsync(existingItem);
            _mockProcessor.Setup(x => x.ProcessExistingItemAsync(existingItem, item.Timestamp)).ReturnsAsync(0);

            // Act
            var result = await _orchestrator.AddItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(0));

            // Verify operation logging
            _mockOperationContext.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains("Échec de la mise à jour du doublon"))), Times.Once);
            _mockOperationContext.Verify(x => x.Complete(0), Times.Once);
        }

        [Test]
        public async Task AddItemAsync_WithDuplicateUpdateException_ReturnsZero()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                Timestamp = DateTime.Now
            };
            var existingItem = new ClipboardItem
            {
                Id = 123,
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                Timestamp = DateTime.Now.AddMinutes(-1)
            };
            var historyItems = new List<ClipboardItem> { existingItem };
            var exception = new Exception("Database error");

            _mockValidator.Setup(x => x.ValidateAsync(item, It.IsAny<long>())).ReturnsAsync(ValidationResult.Success());
            _mockHistoryManager.Setup(x => x.GetHistoryItems()).Returns(historyItems);
            _mockDuplicateDetector.Setup(x => x.FindDuplicateAsync(item, historyItems)).ReturnsAsync(existingItem);
            _mockProcessor.Setup(x => x.ProcessExistingItemAsync(existingItem, item.Timestamp)).ThrowsAsync(exception);

            // Act
            var result = await _orchestrator.AddItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(0));

            // Verify operation logging
            _mockOperationContext.Verify(x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de la mise à jour du doublon")), exception), Times.Once);
            _mockOperationContext.Verify(x => x.Fail(It.Is<string>(s => s.Contains("Erreur lors de la mise à jour du doublon"))), Times.Once);
        }

        [Test]
        public async Task AddItemAsync_WithNewItem_InsertsAndAddsToHistory()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                Timestamp = DateTime.Now
            };
            var historyItems = new List<ClipboardItem>();

            _mockValidator.Setup(x => x.ValidateAsync(item, It.IsAny<long>())).ReturnsAsync(ValidationResult.Success());
            _mockHistoryManager.Setup(x => x.GetHistoryItems()).Returns(historyItems);
            _mockDuplicateDetector.Setup(x => x.FindDuplicateAsync(item, historyItems)).ReturnsAsync((ClipboardItem?)null);
            _mockProcessor.Setup(x => x.ProcessNewItemAsync(item)).ReturnsAsync(456);
            _mockHistoryManager.Setup(x => x.EnforceMaxHistoryItemsAsync(100)).ReturnsAsync(0);

            // Act
            var result = await _orchestrator.AddItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(456));
            Assert.That(item.Id, Is.EqualTo(456)); // Verify ID was set

            // Verify the complete workflow
            _mockValidator.Verify(x => x.ValidateAsync(item, It.IsAny<long>()), Times.Once);
            _mockHistoryManager.Verify(x => x.GetHistoryItems(), Times.Once);
            _mockDuplicateDetector.Verify(x => x.FindDuplicateAsync(item, historyItems), Times.Once);
            _mockProcessor.Verify(x => x.ProcessNewItemAsync(item), Times.Once);
            _mockHistoryManager.Verify(x => x.AddToHistory(item), Times.Once);
            _mockHistoryManager.Verify(x => x.EnforceMaxHistoryItemsAsync(100), Times.Once);
            _mockEventNotifier.Verify(x => x.NotifyHistoryChanged(item, HistoryChangeType.ItemAdded), Times.Once);
            _mockOperationContext.Verify(x => x.Complete(It.IsAny<long>()), Times.Once);
        }

        [Test]
        public async Task AddItemAsync_WithNewItemAndHistoryEnforcement_NotifiesItemsRemoved()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                Timestamp = DateTime.Now
            };
            var historyItems = new List<ClipboardItem>();

            _mockValidator.Setup(x => x.ValidateAsync(item, It.IsAny<long>())).ReturnsAsync(ValidationResult.Success());
            _mockHistoryManager.Setup(x => x.GetHistoryItems()).Returns(historyItems);
            _mockDuplicateDetector.Setup(x => x.FindDuplicateAsync(item, historyItems)).ReturnsAsync((ClipboardItem?)null);
            _mockProcessor.Setup(x => x.ProcessNewItemAsync(item)).ReturnsAsync(456);
            _mockHistoryManager.Setup(x => x.EnforceMaxHistoryItemsAsync(100)).ReturnsAsync(3); // 3 items removed

            // Act
            var result = await _orchestrator.AddItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(456));

            // Verify notifications for both added and removed items
            _mockEventNotifier.Verify(x => x.NotifyHistoryChanged(item, HistoryChangeType.ItemsRemoved), Times.Once);
            _mockEventNotifier.Verify(x => x.NotifyHistoryChanged(item, HistoryChangeType.ItemAdded), Times.Once);
            _mockOperationContext.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains("3 élément(s) supprimé(s)"))), Times.Once);
        }

        [Test]
        public async Task AddItemAsync_WithInsertionFailure_ReturnsZero()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                Timestamp = DateTime.Now
            };
            var historyItems = new List<ClipboardItem>();

            _mockValidator.Setup(x => x.ValidateAsync(item, It.IsAny<long>())).ReturnsAsync(ValidationResult.Success());
            _mockHistoryManager.Setup(x => x.GetHistoryItems()).Returns(historyItems);
            _mockDuplicateDetector.Setup(x => x.FindDuplicateAsync(item, historyItems)).ReturnsAsync((ClipboardItem?)null);
            _mockProcessor.Setup(x => x.ProcessNewItemAsync(item)).ReturnsAsync(0); // Insertion failed

            // Act
            var result = await _orchestrator.AddItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(0));

            // Verify that history operations were NOT called
            _mockHistoryManager.Verify(x => x.AddToHistory(It.IsAny<ClipboardItem>()), Times.Never);
            _mockHistoryManager.Verify(x => x.EnforceMaxHistoryItemsAsync(It.IsAny<int>()), Times.Never);
            _mockEventNotifier.Verify(x => x.NotifyHistoryChanged(It.IsAny<ClipboardItem>(), It.IsAny<HistoryChangeType>()), Times.Never);

            // Verify operation logging
            _mockOperationContext.Verify(x => x.LogError(It.Is<string>(s => s.Contains("Échec de l'insertion en base de données")), It.IsAny<Exception>()), Times.Once);
            _mockOperationContext.Verify(x => x.Fail(It.Is<string>(s => s.Contains("Échec de l'insertion en base de données"))), Times.Once);
        }

        [Test]
        public async Task AddItemAsync_WithUnexpectedException_ReturnsZero()
        {
            // Arrange
            var item = new ClipboardItem
            {
                DataType = ClipboardDataType.Text,
                TextPreview = "Test",
                Timestamp = DateTime.Now
            };
            var historyItems = new List<ClipboardItem>();
            var exception = new InvalidOperationException("Unexpected error");

            _mockValidator.Setup(x => x.ValidateAsync(item, It.IsAny<long>())).ReturnsAsync(ValidationResult.Success());
            _mockHistoryManager.Setup(x => x.GetHistoryItems()).Returns(historyItems);
            _mockDuplicateDetector.Setup(x => x.FindDuplicateAsync(item, historyItems)).ThrowsAsync(exception);

            // Act
            var result = await _orchestrator.AddItemAsync(item);

            // Assert
            Assert.That(result, Is.EqualTo(0));

            // Verify operation logging
            _mockOperationContext.Verify(x => x.LogError(It.Is<string>(s => s.Contains("Erreur inattendue lors de l'ajout de l'élément")), exception), Times.Once);
            _mockOperationContext.Verify(x => x.Fail(It.Is<string>(s => s.Contains("Erreur inattendue lors de l'ajout de l'élément"))), Times.Once);
        }

        #endregion
    }
}
