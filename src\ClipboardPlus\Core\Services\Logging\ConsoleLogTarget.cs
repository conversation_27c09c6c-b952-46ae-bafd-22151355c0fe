using System;

namespace ClipboardPlus.Core.Services.Logging
{
    /// <summary>
    /// Cible de log pour la console avec gestion des couleurs.
    /// Respecte les règles de visibilité selon le niveau et la configuration.
    /// </summary>
    public class ConsoleLogTarget : ILogTarget
    {
        private readonly bool _consoleEnabled;
        private static readonly object _consoleLock = new object();
        
        /// <summary>
        /// Initialise une nouvelle instance de ConsoleLogTarget.
        /// </summary>
        /// <param name="consoleEnabled">Indique si la console est activée globalement</param>
        public ConsoleLogTarget(bool consoleEnabled)
        {
            _consoleEnabled = consoleEnabled;
        }
        
        /// <summary>
        /// Écrit une entrée de log vers la console avec couleurs appropriées.
        /// </summary>
        /// <param name="entry">L'entrée de log à écrire</param>
        public void Write(LogEntry entry)
        {
            // Vérifier si on doit afficher cette entrée
            if (!ShouldDisplay(entry))
            {
                return;
            }
            
            try
            {
                // Thread-safe console color management
                lock (_consoleLock)
                {
                    var originalColor = Console.ForegroundColor;

                    // Appliquer la couleur appropriée
                    Console.ForegroundColor = entry.GetConsoleColor();

                    // Afficher seulement la première ligne formatée (header + première ligne du message)
                    var formattedLines = entry.ToFormattedLines();
                    if (formattedLines.Length > 0)
                    {
                        Console.WriteLine(formattedLines[0]);
                    }

                    // Restaurer la couleur originale
                    Console.ForegroundColor = originalColor;
                }
            }
            catch
            {
                // En cas d'erreur, afficher sans couleur
                try
                {
                    var formattedLines = entry.ToFormattedLines();
                    if (formattedLines.Length > 0)
                    {
                        Console.WriteLine(formattedLines[0]);
                    }
                }
                catch
                {
                    // Ignorer les erreurs de console
                }
            }
        }
        
        /// <summary>
        /// Force l'écriture immédiate (pas nécessaire pour la console).
        /// </summary>
        public void Flush()
        {
            // La console n'a pas de buffer, rien à faire
        }
        
        /// <summary>
        /// Détermine si une entrée doit être affichée en console.
        /// </summary>
        /// <param name="entry">L'entrée de log</param>
        /// <returns>True si l'entrée doit être affichée</returns>
        private bool ShouldDisplay(LogEntry entry)
        {
            // Certains niveaux forcent l'affichage même si la console est désactivée
            if (entry.ForceConsoleOutput)
            {
                return true;
            }
            
            // Sinon, respecter la configuration globale
            return _consoleEnabled;
        }
    }
}
