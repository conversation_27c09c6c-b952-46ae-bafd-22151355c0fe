using System.Collections.Generic;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour la factory de handlers de contenu.
    /// Implémente le pattern Factory pour créer les handlers appropriés selon le type de données.
    /// Respecte le principe d'inversion de dépendance (DIP) en dépendant d'abstractions.
    /// </summary>
    public interface IContentHandlerFactory
    {
        /// <summary>
        /// Crée un handler approprié pour le type de données spécifié.
        /// </summary>
        /// <param name="dataType">Le type de données à traiter</param>
        /// <returns>Le handler approprié, ou null si aucun handler n'est disponible</returns>
        IContentHandler? CreateHandler(ClipboardDataType dataType);

        /// <summary>
        /// Crée un handler approprié pour l'élément spécifié.
        /// </summary>
        /// <param name="item">L'élément du presse-papiers</param>
        /// <returns>Le handler approprié, ou null si aucun handler n'est disponible</returns>
        IContentHandler? CreateHandler(ClipboardItem item);

        /// <summary>
        /// Obtient tous les handlers disponibles.
        /// </summary>
        /// <returns>Énumération de tous les handlers enregistrés</returns>
        IEnumerable<IContentHandler> GetAllHandlers();

        /// <summary>
        /// Vérifie si un handler est disponible pour le type de données spécifié.
        /// </summary>
        /// <param name="dataType">Le type de données à vérifier</param>
        /// <returns>True si un handler est disponible, false sinon</returns>
        bool HasHandler(ClipboardDataType dataType);

        /// <summary>
        /// Obtient la liste des types de données supportés par la factory.
        /// </summary>
        /// <returns>Énumération des types supportés</returns>
        IEnumerable<ClipboardDataType> GetSupportedDataTypes();
    }
}
