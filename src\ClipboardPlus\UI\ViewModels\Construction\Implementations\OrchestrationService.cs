using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Diagnostics;
using ClipboardPlus.UI.Services;
using ClipboardPlus.UI.ViewModels.Construction.Interfaces;

namespace ClipboardPlus.UI.ViewModels.Construction.Implementations
{
    /// <summary>
    /// Implémentation du service d'orchestration des composants complexes du ViewModel.
    /// Responsabilité unique : Création et configuration des orchestrateurs complexes selon le principe SRP.
    /// </summary>
    public class OrchestrationService : IOrchestrationService
    {
        /// <summary>
        /// Crée et configure le HistoryCollectionSynchronizer.
        /// Cette méthode extrait la logique complexe de création du synchronizer
        /// qui était précédemment dans le constructeur (lignes 270-277).
        ///
        /// NOTE: Cette implémentation utilise les méthodes publiques du ViewModel.
        /// Certaines propriétés du ViewModel ne sont pas accessibles publiquement par design.
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel</param>
        /// <param name="historyManager">Gestionnaire de l'historique</param>
        /// <param name="loggingService">Service de logging (peut être null)</param>
        /// <param name="deletionDiagnostic">Service de diagnostic de suppression (peut être null)</param>
        /// <returns>Instance configurée du HistoryCollectionSynchronizer</returns>
        /// <exception cref="ArgumentNullException">Si le ViewModel ou historyManager est null</exception>
        public HistoryCollectionSynchronizer CreateHistoryCollectionSynchronizer(
            ClipboardHistoryViewModel viewModel,
            IClipboardHistoryManager historyManager,
            ILoggingService? loggingService,
            IDeletionDiagnostic? deletionDiagnostic)
        {
            if (viewModel == null) throw new ArgumentNullException(nameof(viewModel));
            if (historyManager == null) throw new ArgumentNullException(nameof(historyManager));

            // Le HistoryCollectionSynchronizer est créé dans le constructeur du ViewModel
            // Cette méthode délègue à la méthode publique pour préserver l'encapsulation

            // Création simplifiée du HistoryCollectionSynchronizer
            return new HistoryCollectionSynchronizer(
                viewModel,
                viewModel.HistoryItems,
                historyManager,
                loggingService,
                deletionDiagnostic,
                null // HistoryChangeOrchestrator n'est pas accessible publiquement
            );
        }

        /// <summary>
        /// Initialise les services optionnels.
        /// Cette méthode extrait l'appel à InitializeMigrationServices()
        /// qui était précédemment dans le constructeur (ligne 289).
        ///
        /// IMPLÉMENTATION COMPLÈTE : Délègue à la méthode publique du ViewModel
        /// pour préserver la logique exacte d'initialisation des services optionnels.
        ///
        /// Logique extraite :
        /// - Récupération de IHistoryChangeOrchestrator depuis le conteneur de services
        /// - Récupération de IFeatureFlagService depuis le conteneur de services
        /// - Logging approprié selon la disponibilité des services
        /// - Gestion des exceptions avec logging d'erreur
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel à configurer</param>
        /// <exception cref="ArgumentNullException">Si le ViewModel est null</exception>
        public void InitializeMigrationServices(ClipboardHistoryViewModel viewModel)
        {
            if (viewModel == null) throw new ArgumentNullException(nameof(viewModel));

            // Déléguer à la méthode publique du ViewModel qui préserve la logique exacte
            // de la ligne 289 du constructeur original
            viewModel.InitializeMigrationServicesPublic();
        }

        /// <summary>
        /// Initialise l'orchestrateur de changements d'historique.
        /// Cette méthode extrait l'initialisation de l'orchestrateur de changements
        /// qui était précédemment dans le constructeur (lignes 292-295).
        ///
        /// IMPLÉMENTATION COMPLÈTE : Délègue à la méthode publique du ViewModel
        /// pour préserver la condition et logique exactes des lignes 292-295.
        ///
        /// Logique extraite :
        /// - Condition : if (_historyChangeOrchestrator != null && _featureFlagService != null)
        /// - Appel : InitializeHistoryChangeOrchestrator(_historyChangeOrchestrator, _featureFlagService)
        /// - Initialisation de l'orchestrateur dans le synchronizer AVANT le premier chargement
        /// - Assignment des services et logging approprié
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel à configurer</param>
        /// <exception cref="ArgumentNullException">Si le ViewModel est null</exception>
        public void InitializeHistoryChangeOrchestrator(ClipboardHistoryViewModel viewModel)
        {
            if (viewModel == null) throw new ArgumentNullException(nameof(viewModel));

            // Déléguer à la méthode publique du ViewModel qui préserve la condition et logique exactes
            // des lignes 292-295 du constructeur original
            viewModel.InitializeHistoryChangeOrchestratorPublic();
        }

        /// <summary>
        /// Initialise le service de santé des collections.
        /// Cette méthode extrait l'initialisation du service de santé
        /// qui était précédemment dans le constructeur (ligne 298).
        ///
        /// IMPLÉMENTATION COMPLÈTE : Délègue à la méthode publique du ViewModel
        /// pour préserver la logique exacte d'initialisation du service de santé des collections.
        ///
        /// Logique extraite :
        /// - Enregistrement du ViewModel pour surveillance si le service est disponible
        /// - Abonnement aux événements de santé (HealthChanged)
        /// - Démarrage de la surveillance automatique
        /// - Logging approprié selon la disponibilité du service
        /// - Gestion des exceptions avec logging d'erreur
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel à configurer</param>
        /// <exception cref="ArgumentNullException">Si le ViewModel est null</exception>
        public void InitializeCollectionHealthService(ClipboardHistoryViewModel viewModel)
        {
            if (viewModel == null) throw new ArgumentNullException(nameof(viewModel));

            // Déléguer à la méthode publique du ViewModel qui préserve la logique exacte
            // de la ligne 298 du constructeur original
            viewModel.InitializeCollectionHealthServicePublic();
        }

        /// <summary>
        /// Déclenche le chargement asynchrone de l'historique.
        /// Cette méthode extrait l'appel à LoadHistoryAsync()
        /// qui était précédemment dans le constructeur (ligne 295).
        /// </summary>
        /// <param name="viewModel">Instance du ViewModel</param>
        /// <returns>Task représentant l'opération asynchrone</returns>
        /// <exception cref="ArgumentNullException">Si le ViewModel est null</exception>
        public Task TriggerHistoryLoadAsync(ClipboardHistoryViewModel viewModel)
        {
            if (viewModel == null) throw new ArgumentNullException(nameof(viewModel));

            // Déclenchement du chargement asynchrone - extraction directe de la ligne 295
            return viewModel.LoadHistoryAsync();
        }
    }
}
