using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Settings;
using ClipboardPlus.Core.DataModels.Settings;

namespace ClipboardPlus.Services.Settings
{
    /// <summary>
    /// Implémentation de l'orchestrateur de persistance des paramètres.
    /// Coordonne la sauvegarde de tous les types de paramètres.
    /// </summary>
    public class SettingsPersistenceOrchestrator : ISettingsPersistenceOrchestrator
    {
        private readonly ISettingsManager _settingsManager;
        private readonly ILoggingService _loggingService;

        public SettingsPersistenceOrchestrator(
            ISettingsManager settingsManager,
            ILoggingService loggingService)
        {
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        public async Task<PersistenceResult> SaveAllSettingsAsync(CompleteSettingsData settings)
        {
            _loggingService?.LogInfo("[SettingsPersistenceOrchestrator] SaveAllSettingsAsync DÉBUT");

            var savedSettings = new List<string>();
            var errors = new List<string>();

            try
            {
                // Sauvegarder tous les paramètres via le SettingsManager
                await _settingsManager.SaveSettingsToPersistenceAsync();
                
                _loggingService?.LogInfo("[SettingsPersistenceOrchestrator] Paramètres sauvegardés en persistance.");

                // Enregistrer les paramètres sauvegardés pour le rapport
                savedSettings.Add("MaxHistoryItems");
                savedSettings.Add("StartWithWindows");
                savedSettings.Add("ShortcutKeyCombination");
                savedSettings.Add("MaxImageDimensionForThumbnail");
                savedSettings.Add("MaxStorableItemSizeBytes");
                savedSettings.Add("HideTimestamp");
                savedSettings.Add("HideItemTitle");
                
                if (settings.AdvancedSettings.SelectedTheme != null)
                {
                    savedSettings.Add("ActiveThemePath");
                }

                return new PersistenceResult(
                    Success: true,
                    Message: "Tous les paramètres ont été sauvegardés avec succès",
                    SavedSettings: savedSettings,
                    Errors: errors
                );
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[SettingsPersistenceOrchestrator] SaveAllSettingsAsync ERREUR", ex);
                errors.Add($"Erreur lors de la sauvegarde: {ex.Message}");

                return new PersistenceResult(
                    Success: false,
                    Message: "Échec de la sauvegarde des paramètres",
                    SavedSettings: savedSettings,
                    Errors: errors
                );
            }
        }

        public async Task<PersistenceResult> SaveSpecificSettingsAsync(PartialSettingsData settings)
        {
            _loggingService?.LogInfo("[SettingsPersistenceOrchestrator] SaveSpecificSettingsAsync DÉBUT");

            var savedSettings = new List<string>();
            var errors = new List<string>();

            try
            {
                // Pour cette implémentation, nous sauvegardons toujours tous les paramètres
                // car le SettingsManager ne supporte pas la sauvegarde sélective
                await _settingsManager.SaveSettingsToPersistenceAsync();
                
                _loggingService?.LogInfo("[SettingsPersistenceOrchestrator] Paramètres spécifiques sauvegardés en persistance.");

                // Enregistrer les paramètres qui étaient censés être sauvegardés
                foreach (var setting in settings.ChangedSettings)
                {
                    savedSettings.Add(setting.Key);
                }

                return new PersistenceResult(
                    Success: true,
                    Message: $"Paramètres spécifiques sauvegardés: {savedSettings.Count} éléments",
                    SavedSettings: savedSettings,
                    Errors: errors
                );
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("[SettingsPersistenceOrchestrator] SaveSpecificSettingsAsync ERREUR", ex);
                errors.Add($"Erreur lors de la sauvegarde spécifique: {ex.Message}");

                return new PersistenceResult(
                    Success: false,
                    Message: "Échec de la sauvegarde des paramètres spécifiques",
                    SavedSettings: savedSettings,
                    Errors: errors
                );
            }
        }
    }
}
