// ============================================================================
// INTERFACE COMMAND VIEWMODEL MANAGER - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Interface pour la gestion des commandes dans le ViewModel
// 📋 RESPONSABILITÉ : Gestion des 8 commandes principales du ViewModel
// 🏗️ ARCHITECTURE : Délégation vers CommandModule existant
//
// ============================================================================

using System;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;
using CommunityToolkit.Mvvm.Input;

namespace ClipboardPlus.UI.ViewModels.Managers.Interfaces
{
    /// <summary>
    /// Interface pour le manager de gestion des commandes dans le ViewModel.
    /// 
    /// Ce manager est responsable de :
    /// - La gestion des 8 commandes principales du ViewModel
    /// - L'initialisation et la configuration des commandes
    /// - La délégation vers le CommandModule
    /// - La gestion des états CanExecute
    /// </summary>
    public interface ICommandViewModelManager : IDisposable
    {
        #region Commandes Principales (8 commandes)

        /// <summary>
        /// Commande pour coller l'élément sélectionné dans l'application active.
        /// </summary>
        IRelayCommand PasteSelectedItemCommand { get; }

        /// <summary>
        /// Commande pour basculer l'état d'épinglage de l'élément sélectionné.
        /// </summary>
        IAsyncRelayCommand<ClipboardItem> BasculerEpinglageCommand { get; }

        /// <summary>
        /// Commande pour supprimer l'élément sélectionné.
        /// </summary>
        IRelayCommand<ClipboardItem> SupprimerElementCommand { get; }

        /// <summary>
        /// Commande pour supprimer l'élément sélectionné (version 2).
        /// </summary>
        IRelayCommand<ClipboardItem> SupprimerElementCommand_V2 { get; }

        /// <summary>
        /// Commande pour supprimer tous les éléments non épinglés.
        /// Respecte la règle métier : préserve les éléments épinglés.
        /// </summary>
        IAsyncRelayCommand SupprimerToutCommand { get; }

        /// <summary>
        /// Commande pour afficher la prévisualisation de l'élément sélectionné.
        /// </summary>
        IRelayCommand<ClipboardItem> AfficherPreviewCommand { get; }

        /// <summary>
        /// Commande pour ouvrir la fenêtre de nettoyage avancé.
        /// </summary>
        IAsyncRelayCommand OpenAdvancedCleanupCommand { get; }

        /// <summary>
        /// Commande pour ouvrir la fenêtre des paramètres.
        /// </summary>
        IAsyncRelayCommand OpenSettingsCommand { get; }

        #endregion

        #region Propriétés de Contexte

        /// <summary>
        /// Élément actuellement sélectionné pour les commandes.
        /// </summary>
        ClipboardItem? SelectedItem { get; set; }

        /// <summary>
        /// Indique si les commandes sont actuellement disponibles.
        /// </summary>
        bool AreCommandsEnabled { get; set; }

        /// <summary>
        /// Nombre total d'éléments dans l'historique (pour SupprimerToutCommand).
        /// </summary>
        int TotalItemCount { get; set; }

        /// <summary>
        /// Nombre d'éléments épinglés (pour SupprimerToutCommand).
        /// </summary>
        int PinnedItemCount { get; set; }

        #endregion

        #region Événements

        /// <summary>
        /// Événement déclenché avant l'exécution d'une commande.
        /// </summary>
        event EventHandler<CommandExecutingEventArgs>? CommandExecuting;

        /// <summary>
        /// Événement déclenché après l'exécution réussie d'une commande.
        /// </summary>
        event EventHandler<CommandExecutedEventArgs>? CommandExecuted;

        /// <summary>
        /// Événement déclenché lorsqu'une commande échoue.
        /// </summary>
        event EventHandler<CommandFailedEventArgs>? CommandFailed;

        #endregion

        #region Méthodes de Gestion des Commandes

        /// <summary>
        /// Initialise toutes les commandes avec leurs implémentations.
        /// </summary>
        void InitializeCommands();

        /// <summary>
        /// Invalide toutes les commandes pour forcer une réévaluation de CanExecute.
        /// </summary>
        void InvalidateAllCommands();

        /// <summary>
        /// Invalide une commande spécifique.
        /// </summary>
        /// <param name="commandName">Nom de la commande à invalider</param>
        void InvalidateCommand(string commandName);

        /// <summary>
        /// Met à jour le contexte des commandes (élément sélectionné, compteurs, etc.).
        /// </summary>
        /// <param name="selectedItem">Élément sélectionné</param>
        /// <param name="totalCount">Nombre total d'éléments</param>
        /// <param name="pinnedCount">Nombre d'éléments épinglés</param>
        void UpdateCommandContext(ClipboardItem? selectedItem, int totalCount, int pinnedCount);

        #endregion

        #region Méthodes d'Exécution Directe

        /// <summary>
        /// Exécute la commande de collage si possible.
        /// </summary>
        /// <returns>True si la commande a été exécutée</returns>
        bool TryExecutePasteCommand();

        /// <summary>
        /// Exécute la commande de basculement d'épinglage si possible.
        /// </summary>
        /// <returns>True si la commande a été exécutée</returns>
        bool TryExecuteTogglePinCommand();

        /// <summary>
        /// Exécute la commande de suppression si possible.
        /// </summary>
        /// <returns>True si la commande a été exécutée</returns>
        bool TryExecuteDeleteCommand();

        /// <summary>
        /// Exécute la commande de suppression totale si possible.
        /// Respecte la règle métier : préserve les éléments épinglés.
        /// </summary>
        /// <returns>True si la commande a été exécutée</returns>
        bool TryExecuteDeleteAllCommand();

        #endregion

        #region Méthodes de Validation

        /// <summary>
        /// Vérifie si une commande peut être exécutée.
        /// </summary>
        /// <param name="commandName">Nom de la commande</param>
        /// <returns>True si la commande peut être exécutée</returns>
        bool CanExecuteCommand(string commandName);

        /// <summary>
        /// Vérifie si la commande de collage peut être exécutée.
        /// </summary>
        /// <returns>True si un élément est sélectionné</returns>
        bool CanExecutePasteCommand();

        /// <summary>
        /// Vérifie si la commande de basculement d'épinglage peut être exécutée.
        /// </summary>
        /// <returns>True si un élément est sélectionné</returns>
        bool CanExecuteTogglePinCommand();

        /// <summary>
        /// Vérifie si la commande de suppression peut être exécutée.
        /// </summary>
        /// <returns>True si un élément est sélectionné</returns>
        bool CanExecuteDeleteCommand();

        /// <summary>
        /// Vérifie si la commande de suppression totale peut être exécutée.
        /// </summary>
        /// <returns>True s'il y a des éléments non épinglés à supprimer</returns>
        bool CanExecuteDeleteAllCommand();

        #endregion

        #region Méthodes d'Initialisation et Nettoyage

        /// <summary>
        /// Initialise le manager avec les dépendances nécessaires.
        /// </summary>
        void Initialize();

        /// <summary>
        /// Nettoie les ressources et se désabonne des événements.
        /// </summary>
        void Cleanup();

        #endregion
    }

    #region Classes d'Arguments d'Événements

    /// <summary>
    /// Arguments d'événement pour l'exécution d'une commande.
    /// </summary>
    public class CommandExecutingEventArgs : EventArgs
    {
        public string CommandName { get; }
        public object? Parameter { get; }
        public bool Cancel { get; set; }

        public CommandExecutingEventArgs(string commandName, object? parameter = null)
        {
            CommandName = commandName;
            Parameter = parameter;
        }
    }

    /// <summary>
    /// Arguments d'événement pour une commande exécutée avec succès.
    /// </summary>
    public class CommandExecutedEventArgs : EventArgs
    {
        public string CommandName { get; }
        public object? Parameter { get; }
        public object? Result { get; }
        public TimeSpan ExecutionTime { get; }

        public CommandExecutedEventArgs(string commandName, object? parameter, object? result, TimeSpan executionTime)
        {
            CommandName = commandName;
            Parameter = parameter;
            Result = result;
            ExecutionTime = executionTime;
        }
    }

    /// <summary>
    /// Arguments d'événement pour une commande qui a échoué.
    /// </summary>
    public class CommandFailedEventArgs : EventArgs
    {
        public string CommandName { get; }
        public object? Parameter { get; }
        public Exception Exception { get; }
        public TimeSpan ExecutionTime { get; }

        public CommandFailedEventArgs(string commandName, object? parameter, Exception exception, TimeSpan executionTime)
        {
            CommandName = commandName;
            Parameter = parameter;
            Exception = exception;
            ExecutionTime = executionTime;
        }
    }

    #endregion
}
