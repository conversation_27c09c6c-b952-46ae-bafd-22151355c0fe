using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Implémentation du service de notifications pour les opérations de suppression UI.
    /// Encapsule la logique de notification pour réduire la complexité cyclomatique.
    /// </summary>
    public class DeletionUINotificationService : IDeletionUINotificationService
    {
        private readonly IUserNotificationService _userNotificationService;
        private readonly ILoggingService? _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du service de notifications de suppression UI.
        /// </summary>
        /// <param name="userNotificationService">Service de notifications utilisateur</param>
        /// <param name="loggingService">Service de logging optionnel</param>
        public DeletionUINotificationService(
            IUserNotificationService userNotificationService,
            ILoggingService? loggingService = null)
        {
            _userNotificationService = userNotificationService ?? throw new ArgumentNullException(nameof(userNotificationService));
            _loggingService = loggingService;
        }

        /// <summary>
        /// Notifie le succès d'une suppression.
        /// </summary>
        /// <param name="itemName">Nom de l'élément supprimé</param>
        /// <param name="operationId">ID de l'opération pour le tracking</param>
        public void NotifySuccess(string itemName, string operationId)
        {
            try
            {
                var message = $"Élément '{itemName}' supprimé avec succès";
                
                // Note: Pas de notification utilisateur pour les succès (comportement actuel)
                // _userNotificationService.ShowSuccess("Succès", message);
                
                _loggingService?.LogInfo($"[{operationId}] {message}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Erreur lors de la notification de succès : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Notifie l'échec d'une suppression.
        /// </summary>
        /// <param name="itemName">Nom de l'élément qui n'a pas pu être supprimé</param>
        /// <param name="errorMessage">Message d'erreur</param>
        /// <param name="operationId">ID de l'opération pour le tracking</param>
        public void NotifyError(string itemName, string errorMessage, string operationId)
        {
            try
            {
                var userMessage = $"Impossible de supprimer l'élément '{itemName}' : {errorMessage}";
                
                // Notification utilisateur pour les erreurs
                _userNotificationService.ShowError("Erreur", userMessage);
                
                _loggingService?.LogError($"[{operationId}] Échec suppression '{itemName}' : {errorMessage}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Erreur lors de la notification d'échec : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Notifie qu'un rollback UI a été effectué.
        /// </summary>
        /// <param name="itemName">Nom de l'élément restauré</param>
        /// <param name="operationId">ID de l'opération pour le tracking</param>
        public void NotifyRollback(string itemName, string operationId)
        {
            try
            {
                var message = $"Rollback UI effectué pour l'élément '{itemName}'";
                
                _loggingService?.LogInfo($"[{operationId}] {message}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Erreur lors de la notification de rollback : {ex.Message}", ex);
            }
        }
    }
}
