using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Interfaces
{
    /// <summary>
    /// Interface pour le traitement des éléments du presse-papiers.
    /// Responsabilité unique : Traiter les éléments (insertion/mise à jour).
    /// </summary>
    public interface IClipboardItemProcessor
    {
        /// <summary>
        /// Traite un nouvel élément en l'insérant dans la persistance.
        /// </summary>
        /// <param name="item">L'élément à insérer</param>
        /// <returns>L'ID généré pour l'élément</returns>
        Task<long> ProcessNewItemAsync(ClipboardItem item);

        /// <summary>
        /// Traite un élément existant en le mettant à jour.
        /// </summary>
        /// <param name="existingItem">L'élément existant à mettre à jour</param>
        /// <param name="newTimestamp">Nouveau timestamp à appliquer</param>
        /// <returns>L'ID de l'élément mis à jour</returns>
        Task<long> ProcessExistingItemAsync(ClipboardItem existingItem, DateTime newTimestamp);
    }
}
