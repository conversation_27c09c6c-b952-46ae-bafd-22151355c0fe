using System;
using System.Threading.Tasks;

namespace ClipboardPlus.Core.Services.Shortcuts.Interfaces
{
    /// <summary>
    /// Interface pour abstraire l'accès au Dispatcher WPF.
    /// Permet l'injection de dépendance et la testabilité.
    /// </summary>
    public interface IDispatcherProvider
    {
        /// <summary>
        /// Exécute une action de manière asynchrone sur le thread UI.
        /// </summary>
        /// <param name="action">L'action à exécuter.</param>
        /// <returns>Une tâche représentant l'opération asynchrone.</returns>
        Task InvokeAsync(Action action);

        /// <summary>
        /// Exécute une fonction de manière asynchrone sur le thread UI.
        /// </summary>
        /// <typeparam name="T">Le type de retour de la fonction.</typeparam>
        /// <param name="function">La fonction à exécuter.</param>
        /// <returns>Une tâche représentant l'opération asynchrone avec le résultat.</returns>
        Task<T> InvokeAsync<T>(Func<T> function);

        /// <summary>
        /// Indique si le Dispatcher est disponible.
        /// </summary>
        /// <returns>True si le Dispatcher est disponible, false sinon.</returns>
        bool IsDispatcherAvailable();

        /// <summary>
        /// Indique si nous sommes actuellement sur le thread UI.
        /// </summary>
        /// <returns>True si nous sommes sur le thread UI, false sinon.</returns>
        bool CheckAccess();
    }
}
