using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Implémentation de la gestion de la suppression d'éléments en mémoire
    /// Responsabilité : Gérer la suppression des éléments de la collection en mémoire
    /// </summary>
    public class DeletionMemoryService : IDeletionMemoryService
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Constructeur avec injection de dépendances
        /// </summary>
        /// <param name="loggingService">Service de logging</param>
        public DeletionMemoryService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new System.ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Vérifie si un élément existe en mémoire
        /// </summary>
        /// <param name="id">L'ID de l'élément à vérifier</param>
        /// <param name="historyItems">La collection d'éléments en mémoire</param>
        /// <returns>True si l'élément existe, False sinon</returns>
        public bool ExistsInMemory(long id, IEnumerable<ClipboardItem> historyItems)
        {
            if (historyItems == null)
                return false;

            return historyItems.Any(item => item.Id == id);
        }

        /// <summary>
        /// Supprime un élément de la collection en mémoire
        /// </summary>
        /// <param name="id">L'ID de l'élément à supprimer</param>
        /// <param name="historyItems">La collection d'éléments en mémoire</param>
        /// <param name="operationId">L'ID de l'opération pour le logging</param>
        /// <returns>Résultat de la suppression mémoire</returns>
        public Task<MemoryDeletionResult> RemoveFromMemoryAsync(long id, IList<ClipboardItem> historyItems, string operationId)
        {
            _loggingService.LogDebug($"[{operationId}] Tentative de suppression mémoire pour ID: {id}");

            if (historyItems == null)
            {
                var message = "La collection historyItems est null";
                _loggingService.LogWarning($"[{operationId}] {message}");
                return Task.FromResult(MemoryDeletionResult.CreateFailure(message));
            }

            // Rechercher l'élément
            var itemToRemove = historyItems.FirstOrDefault(item => item.Id == id);

            if (itemToRemove == null)
            {
                var message = $"Élément avec ID {id} non trouvé en mémoire";
                _loggingService.LogInfo($"[{operationId}] {message}");
                return Task.FromResult(MemoryDeletionResult.CreateSuccess(false, null, message));
            }

            // Supprimer l'élément
            try
            {
                bool removed = historyItems.Remove(itemToRemove);
                if (removed)
                {
                    var message = $"Élément avec ID {id} supprimé de la mémoire avec succès";
                    _loggingService.LogInfo($"[{operationId}] {message}");
                    return Task.FromResult(MemoryDeletionResult.CreateSuccess(true, itemToRemove, message));
                }
                else
                {
                    var message = $"Échec de la suppression mémoire pour ID {id}";
                    _loggingService.LogWarning($"[{operationId}] {message}");
                    return Task.FromResult(MemoryDeletionResult.CreateFailure(message));
                }
            }
            catch (System.Exception ex)
            {
                var message = $"Exception lors de la suppression mémoire pour ID {id}: {ex.Message}";
                _loggingService.LogError($"[{operationId}] {message}", ex);
                return Task.FromResult(MemoryDeletionResult.CreateFailure(message));
            }
        }

        /// <summary>
        /// Récupère un élément de la collection en mémoire par son ID
        /// </summary>
        /// <param name="id">L'ID de l'élément à récupérer</param>
        /// <param name="historyItems">La collection d'éléments en mémoire</param>
        /// <returns>L'élément trouvé ou null</returns>
        public ClipboardItem? FindItemInMemory(long id, IEnumerable<ClipboardItem> historyItems)
        {
            if (historyItems == null)
                return null;

            return historyItems.FirstOrDefault(item => item.Id == id);
        }
    }
}
