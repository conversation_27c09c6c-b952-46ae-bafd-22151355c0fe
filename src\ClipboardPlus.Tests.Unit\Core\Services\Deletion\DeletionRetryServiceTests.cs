using System;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Deletion;

namespace ClipboardPlus.Tests.Unit.Core.Services.Deletion
{
    /// <summary>
    /// Tests unitaires pour DeletionRetryService basés sur le code source réel
    /// </summary>
    [TestFixture]
    public class DeletionRetryServiceTests
    {
        private Mock<IPersistenceService> _mockPersistenceService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private DeletionRetryService _service = null!;

        [SetUp]
        public void SetUp()
        {
            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockLoggingService = new Mock<ILoggingService>();
            _service = new DeletionRetryService(_mockPersistenceService.Object, _mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullPersistenceService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new DeletionRetryService(null!, _mockLoggingService.Object));
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new DeletionRetryService(_mockPersistenceService.Object, null!));
        }

        [Test]
        public void ConfigureRetry_WithValidParameters_ShouldAcceptValues()
        {
            // Arrange & Act
            _service.ConfigureRetry(5, 200);

            // Assert - Pas d'exception levée
            Assert.Pass("Configuration acceptée sans exception");
        }

        [Test]
        public void ConfigureRetry_WithNegativeMaxRetries_ShouldSetToMinimumOne()
        {
            // Arrange & Act
            _service.ConfigureRetry(-1, 100);

            // Assert - Le comportement sera testé indirectement via les tentatives
            Assert.Pass("Valeurs négatives gérées");
        }

        [Test]
        public void ConfigureRetry_WithTooSmallBaseDelay_ShouldSetToMinimum50()
        {
            // Arrange & Act
            _service.ConfigureRetry(3, 10); // Moins que 50ms minimum

            // Assert - Le comportement sera testé indirectement
            Assert.Pass("Délai trop petit géré");
        }

        [Test]
        public async Task DeleteFromDatabaseWithRetryAsync_WithSuccessOnFirstAttempt_ShouldReturnSuccess()
        {
            // Arrange
            long itemId = 123;
            string operationId = "test-op";
            _mockPersistenceService.Setup(x => x.DeleteClipboardItemAsync(itemId))
                                   .ReturnsAsync(true);

            // Act
            var result = await _service.DeleteFromDatabaseWithRetryAsync(itemId, operationId);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.AttemptsCount, Is.EqualTo(1));
            Assert.That(result.HadErrors, Is.False);
            Assert.That(result.LastException, Is.Null);
            Assert.That(result.LogMessages, Is.Not.Null);
            Assert.That(result.LogMessages.Length, Is.GreaterThan(0));
            Assert.That(result.TotalElapsed.TotalMilliseconds, Is.GreaterThan(0));

            // Verify persistence service was called once
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(itemId), Times.Once);

            // Verify logging calls
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains(operationId) && s.Contains("Début suppression BDD"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("Suppression BDD réussie"))), Times.Once);
        }

        [Test]
        public async Task DeleteFromDatabaseWithRetryAsync_WithFailureOnFirstThenSuccess_ShouldReturnSuccessAfterRetry()
        {
            // Arrange
            long itemId = 456;
            string operationId = "retry-test";
            _mockPersistenceService.SetupSequence(x => x.DeleteClipboardItemAsync(itemId))
                                   .ReturnsAsync(false)  // First attempt fails
                                   .ReturnsAsync(true);  // Second attempt succeeds

            // Act
            var result = await _service.DeleteFromDatabaseWithRetryAsync(itemId, operationId);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.AttemptsCount, Is.EqualTo(2));
            Assert.That(result.HadErrors, Is.True); // Had errors on first attempt
            Assert.That(result.LastException, Is.Null);
            Assert.That(result.LogMessages.Length, Is.GreaterThan(1));

            // Verify persistence service was called twice
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(itemId), Times.Exactly(2));

            // Verify logging calls
            _mockLoggingService.Verify(x => x.LogWarning(It.Is<string>(s => s.Contains(operationId) && s.Contains("échouée"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("réussie"))), Times.Once);
        }

        [Test]
        public async Task DeleteFromDatabaseWithRetryAsync_WithAllAttemptsFailingReturnFalse_ShouldReturnFailure()
        {
            // Arrange
            long itemId = 789;
            string operationId = "fail-test";
            _service.ConfigureRetry(2, 50); // Only 2 attempts for faster test
            _mockPersistenceService.Setup(x => x.DeleteClipboardItemAsync(itemId))
                                   .ReturnsAsync(false); // Always fails

            // Act
            var result = await _service.DeleteFromDatabaseWithRetryAsync(itemId, operationId);

            // Assert
            Assert.That(result.Success, Is.False);
            Assert.That(result.AttemptsCount, Is.EqualTo(2));
            Assert.That(result.HadErrors, Is.True);
            Assert.That(result.LastException, Is.Null); // No exception, just false returns
            Assert.That(result.LogMessages.Length, Is.GreaterThan(2));

            // Verify persistence service was called the configured number of times
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(itemId), Times.Exactly(2));

            // Verify error logging was called
            _mockLoggingService.Verify(x => x.LogError(It.IsAny<string>(), It.IsAny<Exception>()), Times.AtLeastOnce);
        }

        [Test]
        public async Task DeleteFromDatabaseWithRetryAsync_WithExceptionOnFirstThenSuccess_ShouldReturnSuccessAfterRetry()
        {
            // Arrange
            long itemId = 999;
            string operationId = "exception-test";
            var testException = new InvalidOperationException("Test database error");
            
            _mockPersistenceService.SetupSequence(x => x.DeleteClipboardItemAsync(itemId))
                                   .ThrowsAsync(testException)  // First attempt throws
                                   .ReturnsAsync(true);         // Second attempt succeeds

            // Act
            var result = await _service.DeleteFromDatabaseWithRetryAsync(itemId, operationId);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.AttemptsCount, Is.EqualTo(2));
            Assert.That(result.HadErrors, Is.True); // Had errors on first attempt
            Assert.That(result.LastException, Is.Null); // Should be null on success

            // Verify persistence service was called twice
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(itemId), Times.Exactly(2));

            // Verify exception logging was called
            _mockLoggingService.Verify(x => x.LogError(It.IsAny<string>(), It.IsAny<Exception>()), Times.AtLeastOnce);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains(operationId) && s.Contains("réussie"))), Times.Once);
        }

        [Test]
        public async Task DeleteFromDatabaseWithRetryAsync_WithAllAttemptsThrowingExceptions_ShouldReturnFailureWithLastException()
        {
            // Arrange
            long itemId = 111;
            string operationId = "all-exception-test";
            var testException = new InvalidOperationException("Persistent database error");
            
            _service.ConfigureRetry(2, 50); // Only 2 attempts for faster test
            _mockPersistenceService.Setup(x => x.DeleteClipboardItemAsync(itemId))
                                   .ThrowsAsync(testException); // Always throws

            // Act
            var result = await _service.DeleteFromDatabaseWithRetryAsync(itemId, operationId);

            // Assert
            Assert.That(result.Success, Is.False);
            Assert.That(result.AttemptsCount, Is.EqualTo(2));
            Assert.That(result.HadErrors, Is.True);
            Assert.That(result.LastException, Is.EqualTo(testException));

            // Verify persistence service was called the configured number of times
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(itemId), Times.Exactly(2));

            // Verify exception logging for each attempt plus final error
            _mockLoggingService.Verify(x => x.LogError(It.IsAny<string>(), It.IsAny<Exception>()), Times.AtLeast(2));
        }

        [Test]
        public async Task DeleteFromDatabaseWithRetryAsync_ShouldLogCorrectMessages()
        {
            // Arrange
            long itemId = 555;
            string operationId = "logging-test";
            _mockPersistenceService.Setup(x => x.DeleteClipboardItemAsync(itemId))
                                   .ReturnsAsync(true);

            // Act
            await _service.DeleteFromDatabaseWithRetryAsync(itemId, operationId);

            // Assert - Verify specific log messages
            _mockLoggingService.Verify(x => x.LogDebug($"[{operationId}] Début suppression BDD avec retry pour ID: {itemId} (max 3 tentatives)"), Times.Once);
            _mockLoggingService.Verify(x => x.LogDebug(It.Is<string>(s => s.Contains($"[{operationId}] Tentative 1/3"))), Times.Once);
            _mockLoggingService.Verify(x => x.LogInfo(It.Is<string>(s => s.Contains($"[{operationId}] Suppression BDD réussie pour ID {itemId} à la tentative 1"))), Times.Once);
        }

        [Test]
        public async Task DeleteFromDatabaseWithRetryAsync_WithCustomRetryConfiguration_ShouldRespectConfiguration()
        {
            // Arrange
            long itemId = 777;
            string operationId = "config-test";
            _service.ConfigureRetry(1, 100); // Only 1 attempt
            _mockPersistenceService.Setup(x => x.DeleteClipboardItemAsync(itemId))
                                   .ReturnsAsync(false); // Fails

            // Act
            var result = await _service.DeleteFromDatabaseWithRetryAsync(itemId, operationId);

            // Assert
            Assert.That(result.Success, Is.False);
            Assert.That(result.AttemptsCount, Is.EqualTo(1)); // Only 1 attempt as configured

            // Verify persistence service was called only once
            _mockPersistenceService.Verify(x => x.DeleteClipboardItemAsync(itemId), Times.Once);
        }
    }

    /// <summary>
    /// Tests pour les méthodes statiques de DatabaseDeletionResult
    /// </summary>
    [TestFixture]
    public class DatabaseDeletionResultTests
    {
        [Test]
        public void CreateSuccess_ShouldReturnValidSuccessResult()
        {
            // Arrange
            int attempts = 2;
            bool hadErrors = true;
            var elapsed = TimeSpan.FromMilliseconds(500);
            string[] logMessages = { "Message 1", "Message 2" };

            // Act
            var result = DatabaseDeletionResult.CreateSuccess(attempts, hadErrors, elapsed, logMessages);

            // Assert
            Assert.That(result.Success, Is.True);
            Assert.That(result.AttemptsCount, Is.EqualTo(attempts));
            Assert.That(result.HadErrors, Is.EqualTo(hadErrors));
            Assert.That(result.TotalElapsed, Is.EqualTo(elapsed));
            Assert.That(result.LogMessages, Is.EqualTo(logMessages));
            Assert.That(result.LastException, Is.Null);
        }

        [Test]
        public void CreateFailure_ShouldReturnValidFailureResult()
        {
            // Arrange
            int attempts = 3;
            var exception = new InvalidOperationException("Test error");
            var elapsed = TimeSpan.FromMilliseconds(1000);
            string[] logMessages = { "Error 1", "Error 2", "Error 3" };

            // Act
            var result = DatabaseDeletionResult.CreateFailure(attempts, exception, elapsed, logMessages);

            // Assert
            Assert.That(result.Success, Is.False);
            Assert.That(result.AttemptsCount, Is.EqualTo(attempts));
            Assert.That(result.HadErrors, Is.True);
            Assert.That(result.TotalElapsed, Is.EqualTo(elapsed));
            Assert.That(result.LogMessages, Is.EqualTo(logMessages));
            Assert.That(result.LastException, Is.EqualTo(exception));
        }

        [Test]
        public void CreateFailure_WithNullException_ShouldReturnValidFailureResult()
        {
            // Arrange
            int attempts = 2;
            var elapsed = TimeSpan.FromMilliseconds(300);
            string[] logMessages = { "Failed attempt 1", "Failed attempt 2" };

            // Act
            var result = DatabaseDeletionResult.CreateFailure(attempts, null, elapsed, logMessages);

            // Assert
            Assert.That(result.Success, Is.False);
            Assert.That(result.AttemptsCount, Is.EqualTo(attempts));
            Assert.That(result.HadErrors, Is.True);
            Assert.That(result.TotalElapsed, Is.EqualTo(elapsed));
            Assert.That(result.LogMessages, Is.EqualTo(logMessages));
            Assert.That(result.LastException, Is.Null);
        }
    }
}
