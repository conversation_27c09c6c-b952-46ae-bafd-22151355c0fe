using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.DataModels
{
    /// <summary>
    /// Arguments d'événement pour le traitement des changements d'historique.
    /// 
    /// Cette classe encapsule toutes les informations nécessaires pour traiter
    /// un événement de changement d'historique de manière structurée.
    /// </summary>
    public class HistoryChangedEventArgs
    {
        /// <summary>
        /// Contexte de validation contenant l'état actuel du système.
        /// </summary>
        public HistoryChangeContext Context { get; set; } = new HistoryChangeContext();

        /// <summary>
        /// Contexte de synchronisation pour les collections.
        /// </summary>
        public HistorySynchronizationContext SyncContext { get; set; } = new HistorySynchronizationContext();

        /// <summary>
        /// Action à exécuter pour recharger l'historique complet.
        /// </summary>
        public Func<Task> LoadHistoryAction { get; set; } = () => Task.CompletedTask;

        /// <summary>
        /// Gestionnaire d'erreur à appeler en cas d'exception.
        /// </summary>
        public Action<Exception, string> ErrorHandler { get; set; } = (ex, eventId) => { };

        /// <summary>
        /// Identifiant unique de l'événement (généré automatiquement si non fourni).
        /// </summary>
        public string EventId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Résultat du traitement d'un changement d'historique.
    /// </summary>
    public class HistoryChangeResult
    {
        /// <summary>
        /// Indique si le traitement a réussi.
        /// </summary>
        public bool Success { get; private set; }

        /// <summary>
        /// Message descriptif du résultat.
        /// </summary>
        public string Message { get; private set; }

        /// <summary>
        /// Exception capturée si le traitement a échoué.
        /// </summary>
        public Exception? Exception { get; private set; }

        /// <summary>
        /// Identifiant de l'événement traité.
        /// </summary>
        public string EventId { get; private set; }

        /// <summary>
        /// Raison pour laquelle le traitement a été ignoré (si applicable).
        /// </summary>
        public string? IgnoredReason { get; private set; }

        /// <summary>
        /// Indique si le traitement a été ignoré pour une raison valide.
        /// </summary>
        public bool WasIgnored => !string.IsNullOrEmpty(IgnoredReason);

        private HistoryChangeResult(bool success, string message, string eventId, Exception? exception = null, string? ignoredReason = null)
        {
            Success = success;
            Message = message;
            EventId = eventId;
            Exception = exception;
            IgnoredReason = ignoredReason;
        }

        /// <summary>
        /// Crée un résultat de succès.
        /// </summary>
        public static HistoryChangeResult Succeeded(string eventId, string message = "Traitement réussi") =>
            new HistoryChangeResult(true, message, eventId);

        /// <summary>
        /// Crée un résultat d'échec.
        /// </summary>
        public static HistoryChangeResult Failed(string eventId, string message, Exception? exception = null) =>
            new HistoryChangeResult(false, message, eventId, exception);

        /// <summary>
        /// Crée un résultat indiquant que le traitement a été ignoré.
        /// </summary>
        public static HistoryChangeResult Ignored(string eventId, string reason) =>
            new HistoryChangeResult(true, $"Traitement ignoré: {reason}", eventId, ignoredReason: reason);
    }

    /// <summary>
    /// Statistiques de traitement des changements d'historique.
    /// </summary>
    public class HistoryChangeStatistics
    {
        /// <summary>
        /// Nombre total d'événements traités.
        /// </summary>
        public int TotalEvents { get; set; }

        /// <summary>
        /// Nombre d'événements traités avec succès.
        /// </summary>
        public int SuccessfulEvents { get; set; }

        /// <summary>
        /// Nombre d'événements ignorés.
        /// </summary>
        public int IgnoredEvents { get; set; }

        /// <summary>
        /// Nombre d'événements ayant échoué.
        /// </summary>
        public int FailedEvents { get; set; }

        /// <summary>
        /// Temps moyen de traitement en millisecondes.
        /// </summary>
        public double AverageProcessingTimeMs { get; set; }

        /// <summary>
        /// Dernière erreur rencontrée.
        /// </summary>
        public Exception? LastError { get; set; }

        /// <summary>
        /// Horodatage de la dernière erreur.
        /// </summary>
        public DateTime? LastErrorTimestamp { get; set; }

        /// <summary>
        /// Taux de succès en pourcentage.
        /// </summary>
        public double SuccessRate => TotalEvents > 0 ? (double)SuccessfulEvents / TotalEvents * 100 : 0;
    }
}
