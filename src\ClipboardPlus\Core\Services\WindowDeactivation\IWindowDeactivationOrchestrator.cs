using System;
using System.Threading.Tasks;
using System.Windows;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Orchestrateur principal pour la gestion de la désactivation de fenêtres.
    /// Coordonne tous les services pour reproduire le comportement de Window_Deactivated.
    /// </summary>
    public interface IWindowDeactivationOrchestrator
    {
        /// <summary>
        /// Gère la désactivation d'une fenêtre de manière asynchrone.
        /// </summary>
        /// <param name="window">Fenêtre concernée par la désactivation</param>
        /// <param name="e">Arguments de l'événement de désactivation</param>
        /// <returns>Résultat de l'opération de désactivation</returns>
        Task<WindowDeactivationResult> HandleWindowDeactivationAsync(Window window, EventArgs e);

        /// <summary>
        /// Gère la désactivation d'une fenêtre de manière synchrone.
        /// </summary>
        /// <param name="window">Fenêtre concernée par la désactivation</param>
        /// <param name="e">Arguments de l'événement de désactivation</param>
        /// <returns>Résultat de l'opération de désactivation</returns>
        WindowDeactivationResult HandleWindowDeactivation(Window window, EventArgs e);

        /// <summary>
        /// Configure l'orchestrateur avec des paramètres spécifiques.
        /// </summary>
        /// <param name="config">Configuration de l'orchestrateur</param>
        void Configure(WindowDeactivationOrchestratorConfig config);

        /// <summary>
        /// Obtient les métriques de performance de l'orchestrateur.
        /// </summary>
        /// <returns>Métriques de performance</returns>
        WindowDeactivationPerformanceMetrics GetPerformanceMetrics();
    }

    /// <summary>
    /// Résultat d'une opération de désactivation de fenêtre.
    /// </summary>
    public class WindowDeactivationResult
    {
        /// <summary>
        /// Indique si l'opération s'est déroulée avec succès.
        /// </summary>
        public bool IsSuccessful { get; set; } = true;

        /// <summary>
        /// Action qui a été effectuée sur la fenêtre.
        /// </summary>
        public WindowDeactivationAction ActionTaken { get; set; }

        /// <summary>
        /// Raison de l'action prise.
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// Détails supplémentaires sur l'opération.
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// Identifiant unique de l'opération.
        /// </summary>
        public string OperationId { get; set; } = string.Empty;

        /// <summary>
        /// Horodatage de début de l'opération.
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Horodatage de fin de l'opération.
        /// </summary>
        public DateTime EndTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Durée totale de l'opération en millisecondes.
        /// </summary>
        public double TotalDurationMs => (EndTime - StartTime).TotalMilliseconds;

        /// <summary>
        /// Résultat de la validation d'état.
        /// </summary>
        public WindowStateValidationResult? ValidationResult { get; set; }

        /// <summary>
        /// Résultat du diagnostic des fenêtres.
        /// </summary>
        public WindowDiagnosticResult? DiagnosticResult { get; set; }

        /// <summary>
        /// Résultat de la classification des fenêtres.
        /// </summary>
        public WindowClassificationResult? ClassificationResult { get; set; }

        /// <summary>
        /// Décision de visibilité prise.
        /// </summary>
        public WindowVisibilityDecision? VisibilityDecision { get; set; }

        /// <summary>
        /// Exception survenue pendant l'opération (si applicable).
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// Métriques de performance de l'opération.
        /// </summary>
        public WindowDeactivationMetrics? Metrics { get; set; }
    }

    /// <summary>
    /// Actions possibles lors de la désactivation d'une fenêtre.
    /// </summary>
    public enum WindowDeactivationAction
    {
        /// <summary>
        /// Aucune action effectuée.
        /// </summary>
        None,

        /// <summary>
        /// Opération ignorée - fenêtre en cours de fermeture.
        /// </summary>
        IgnoredWindowClosing,

        /// <summary>
        /// Opération ignorée - opération ViewModel en cours.
        /// </summary>
        IgnoredOperationInProgress,

        /// <summary>
        /// Opération ignorée - fenêtre de l'application active.
        /// </summary>
        IgnoredApplicationWindow,

        /// <summary>
        /// Opération ignorée - fenêtre cible toujours active.
        /// </summary>
        IgnoredStillActive,

        /// <summary>
        /// Fenêtre masquée - fenêtre externe active.
        /// </summary>
        WindowHiddenExternalWindow,

        /// <summary>
        /// Fenêtre masquée - aucune fenêtre active.
        /// </summary>
        WindowHiddenNoActiveWindow,

        /// <summary>
        /// Erreur pendant l'opération.
        /// </summary>
        Error
    }

    /// <summary>
    /// Configuration pour l'orchestrateur de désactivation de fenêtres.
    /// </summary>
    public class WindowDeactivationOrchestratorConfig
    {
        /// <summary>
        /// Configuration pour la validation d'état.
        /// </summary>
        public WindowStateValidationContext? ValidationConfig { get; set; }

        /// <summary>
        /// Configuration pour la classification des fenêtres.
        /// </summary>
        public WindowClassificationConfig? ClassificationConfig { get; set; }

        /// <summary>
        /// Configuration pour les décisions de visibilité.
        /// </summary>
        public WindowVisibilityDecisionConfig? DecisionConfig { get; set; }

        /// <summary>
        /// Configuration pour le logging.
        /// </summary>
        public WindowDeactivationLoggingConfig? LoggingConfig { get; set; }

        /// <summary>
        /// Indique si les opérations doivent être exécutées de manière asynchrone.
        /// </summary>
        public bool EnableAsyncOperations { get; set; } = false;

        /// <summary>
        /// Indique si les métriques de performance doivent être collectées.
        /// </summary>
        public bool EnablePerformanceMetrics { get; set; } = true;

        /// <summary>
        /// Timeout pour les opérations asynchrones en millisecondes.
        /// </summary>
        public int AsyncTimeoutMs { get; set; } = 5000;
    }

    /// <summary>
    /// Métriques de performance pour l'orchestrateur.
    /// </summary>
    public class WindowDeactivationPerformanceMetrics
    {
        /// <summary>
        /// Nombre total d'opérations traitées.
        /// </summary>
        public long TotalOperations { get; set; }

        /// <summary>
        /// Nombre d'opérations réussies.
        /// </summary>
        public long SuccessfulOperations { get; set; }

        /// <summary>
        /// Nombre d'opérations échouées.
        /// </summary>
        public long FailedOperations { get; set; }

        /// <summary>
        /// Durée moyenne des opérations en millisecondes.
        /// </summary>
        public double AverageOperationDurationMs { get; set; }

        /// <summary>
        /// Durée minimale d'opération en millisecondes.
        /// </summary>
        public double MinOperationDurationMs { get; set; } = double.MaxValue;

        /// <summary>
        /// Durée maximale d'opération en millisecondes.
        /// </summary>
        public double MaxOperationDurationMs { get; set; }

        /// <summary>
        /// Horodatage de la dernière réinitialisation des métriques.
        /// </summary>
        public DateTime LastResetTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Taux de succès en pourcentage.
        /// </summary>
        public double SuccessRate => TotalOperations > 0 ? (SuccessfulOperations / (double)TotalOperations) * 100 : 0;
    }
}
