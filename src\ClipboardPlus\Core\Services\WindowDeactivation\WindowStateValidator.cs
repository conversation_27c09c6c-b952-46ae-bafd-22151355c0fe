using System;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Implémentation du service de validation des conditions préalables pour la désactivation de fenêtre.
    /// </summary>
    public class WindowStateValidator : IWindowStateValidator
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance du validateur d'état de fenêtre.
        /// </summary>
        /// <param name="loggingService">Service de journalisation</param>
        public WindowStateValidator(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <inheritdoc />
        public bool ShouldIgnoreDeactivation(bool isClosing, bool isOperationInProgress)
        {
            var context = new WindowStateValidationContext
            {
                IsClosing = isClosing,
                IsOperationInProgress = isOperationInProgress,
                WindowName = "Unknown"
            };

            var result = ValidateWindowState(context);
            return result.ShouldIgnore;
        }

        /// <inheritdoc />
        public WindowStateValidationResult ValidateWindowState(WindowStateValidationContext validationContext)
        {
            if (validationContext == null)
            {
                throw new ArgumentNullException(nameof(validationContext));
            }

            var startTime = DateTime.Now;
            
            try
            {
                _loggingService.LogInfo($"🔍 [WindowStateValidator] Validation pour '{validationContext.WindowName}' - IsClosing: {validationContext.IsClosing}, IsOperationInProgress: {validationContext.IsOperationInProgress}");

                // Test 1: Fenêtre en cours de fermeture
                if (validationContext.IsClosing)
                {
                    var result = new WindowStateValidationResult
                    {
                        ShouldIgnore = true,
                        Reason = "Fenêtre en cours de fermeture",
                        ValidationType = WindowStateValidationType.WindowClosing,
                        Details = $"La fenêtre '{validationContext.WindowName}' est marquée comme étant en cours de fermeture (_isClosing = true)"
                    };

                    _loggingService.LogInfo($"✅ [WindowStateValidator] {result.Reason} - Désactivation ignorée");
                    return result;
                }

                // Test 2: Opération ViewModel en cours
                if (validationContext.IsOperationInProgress)
                {
                    var result = new WindowStateValidationResult
                    {
                        ShouldIgnore = true,
                        Reason = "Opération ViewModel en cours",
                        ValidationType = WindowStateValidationType.OperationInProgress,
                        Details = $"Une opération est en cours dans le ViewModel de la fenêtre '{validationContext.WindowName}'"
                    };

                    _loggingService.LogInfo($"✅ [WindowStateValidator] {result.Reason} - Désactivation ignorée");
                    return result;
                }

                // Aucune condition d'arrêt précoce détectée
                var normalResult = new WindowStateValidationResult
                {
                    ShouldIgnore = false,
                    Reason = "Aucune condition d'arrêt précoce",
                    ValidationType = WindowStateValidationType.Normal,
                    Details = $"La fenêtre '{validationContext.WindowName}' peut procéder à l'analyse de désactivation"
                };

                _loggingService.LogInfo($"🚀 [WindowStateValidator] {normalResult.Reason} - Poursuite de l'analyse");
                return normalResult;
            }
            catch (Exception ex)
            {
                var errorResult = new WindowStateValidationResult
                {
                    ShouldIgnore = true, // En cas d'erreur, on ignore par sécurité
                    Reason = "Erreur de validation",
                    ValidationType = WindowStateValidationType.ValidationError,
                    Details = $"Erreur lors de la validation: {ex.Message}"
                };

                _loggingService.LogError($"❌ [WindowStateValidator] Erreur lors de la validation: {ex.Message}", ex);
                return errorResult;
            }
        }
    }
}
