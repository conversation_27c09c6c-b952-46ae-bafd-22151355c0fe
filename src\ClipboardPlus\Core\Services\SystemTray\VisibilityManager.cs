using System;
using System.Windows.Forms;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// Implémentation du gestionnaire de visibilité pour l'icône système.
    /// Responsabilité unique : gérer l'affichage et la visibilité du NotifyIcon.
    /// </summary>
    public class VisibilityManager : IVisibilityManager
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de VisibilityManager.
        /// </summary>
        /// <param name="loggingService">Service de logging pour enregistrer les opérations de visibilité.</param>
        public VisibilityManager(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <inheritdoc />
        public void ShowIcon(NotifyIcon notifyIcon)
        {
            if (notifyIcon == null)
            {
                throw new ArgumentNullException(nameof(notifyIcon));
            }

            try
            {
                _loggingService.LogInfo("VisibilityManager: Affichage de l'icône dans la zone de notification...");

                notifyIcon.Visible = true;

                _loggingService.LogInfo($"VisibilityManager: État de visibilité après affichage: {notifyIcon.Visible}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"VisibilityManager: Erreur lors de l'affichage de l'icône: {ex.Message}", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public void HideIcon(NotifyIcon notifyIcon)
        {
            if (notifyIcon == null)
            {
                throw new ArgumentNullException(nameof(notifyIcon));
            }

            try
            {
                _loggingService.LogInfo("VisibilityManager: Masquage de l'icône de la zone de notification...");

                notifyIcon.Visible = false;

                _loggingService.LogInfo($"VisibilityManager: État de visibilité après masquage: {notifyIcon.Visible}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"VisibilityManager: Erreur lors du masquage de l'icône: {ex.Message}", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public bool IsIconVisible(NotifyIcon notifyIcon)
        {
            if (notifyIcon == null)
            {
                throw new ArgumentNullException(nameof(notifyIcon));
            }

            try
            {
                bool isVisible = notifyIcon.Visible;
                _loggingService.LogInfo($"VisibilityManager: Vérification de la visibilité de l'icône - Résultat: {isVisible}");
                return isVisible;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"VisibilityManager: Erreur lors de la vérification de la visibilité: {ex.Message}", ex);
                throw;
            }
        }

        /// <inheritdoc />
        public bool ConfigureInitialVisibility(NotifyIcon notifyIcon)
        {
            if (notifyIcon == null)
            {
                throw new ArgumentNullException(nameof(notifyIcon));
            }

            try
            {
                _loggingService.LogInfo("VisibilityManager: Configuration de la visibilité initiale...");

                // Configurer la visibilité initiale (false pour éviter les flashs visuels)
                notifyIcon.Visible = false;
                
                // Puis afficher l'icône
                ShowIcon(notifyIcon);
                
                // Vérifier que l'icône est bien visible
                bool isVisible = IsIconVisible(notifyIcon);
                
                _loggingService.LogInfo($"VisibilityManager: Configuration de la visibilité initiale terminée - Résultat: {isVisible}");
                
                return isVisible;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"VisibilityManager: Erreur lors de la configuration de la visibilité initiale: {ex.Message}", ex);
                throw;
            }
        }
    }
}
