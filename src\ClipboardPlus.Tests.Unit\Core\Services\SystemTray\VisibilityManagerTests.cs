using System;
using System.Threading;
using System.Windows.Forms;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;

namespace ClipboardPlus.Tests.Unit.Core.Services.SystemTray
{
    /// <summary>
    /// Tests unitaires pour VisibilityManager.
    /// Vérifie la responsabilité unique : gestion de l'affichage et la visibilité du NotifyIcon.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class VisibilityManagerTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private VisibilityManager _visibilityManager;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _visibilityManager = new VisibilityManager(_mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new VisibilityManager(null));
        }

        [Test]
        public void ShowIcon_WithValidNotifyIcon_MakesIconVisible()
        {
            // Arrange
            var notifyIcon = new NotifyIcon { Visible = false };

            // Act
            _visibilityManager.ShowIcon(notifyIcon);

            // Assert
            Assert.That(notifyIcon.Visible, Is.True, "Icon should be visible after ShowIcon");

            _mockLoggingService.Verify(
                x => x.LogInfo("VisibilityManager: Affichage de l'icône dans la zone de notification..."),
                Times.Once,
                "Should log show operation start");

            _mockLoggingService.Verify(
                x => x.LogInfo("VisibilityManager: État de visibilité après affichage: True"),
                Times.Once,
                "Should log visibility state after showing");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ShowIcon_WithNullNotifyIcon_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _visibilityManager.ShowIcon(null));
            Assert.That(ex.ParamName, Is.EqualTo("notifyIcon"));
        }

        [Test]
        public void HideIcon_WithValidNotifyIcon_MakesIconInvisible()
        {
            // Arrange
            var notifyIcon = new NotifyIcon { Visible = true };

            // Act
            _visibilityManager.HideIcon(notifyIcon);

            // Assert
            Assert.That(notifyIcon.Visible, Is.False, "Icon should be invisible after HideIcon");

            _mockLoggingService.Verify(
                x => x.LogInfo("VisibilityManager: Masquage de l'icône de la zone de notification..."),
                Times.Once,
                "Should log hide operation start");

            _mockLoggingService.Verify(
                x => x.LogInfo("VisibilityManager: État de visibilité après masquage: False"),
                Times.Once,
                "Should log visibility state after hiding");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void HideIcon_WithNullNotifyIcon_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _visibilityManager.HideIcon(null));
            Assert.That(ex.ParamName, Is.EqualTo("notifyIcon"));
        }

        [Test]
        public void IsIconVisible_WithVisibleIcon_ReturnsTrue()
        {
            // Arrange
            var notifyIcon = new NotifyIcon { Visible = true };

            // Act
            bool result = _visibilityManager.IsIconVisible(notifyIcon);

            // Assert
            Assert.That(result, Is.True, "Should return true for visible icon");

            _mockLoggingService.Verify(
                x => x.LogInfo("VisibilityManager: Vérification de la visibilité de l'icône - Résultat: True"),
                Times.Once,
                "Should log visibility check result");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void IsIconVisible_WithInvisibleIcon_ReturnsFalse()
        {
            // Arrange
            var notifyIcon = new NotifyIcon { Visible = false };

            // Act
            bool result = _visibilityManager.IsIconVisible(notifyIcon);

            // Assert
            Assert.That(result, Is.False, "Should return false for invisible icon");

            _mockLoggingService.Verify(
                x => x.LogInfo("VisibilityManager: Vérification de la visibilité de l'icône - Résultat: False"),
                Times.Once,
                "Should log visibility check result");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void IsIconVisible_WithNullNotifyIcon_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _visibilityManager.IsIconVisible(null));
            Assert.That(ex.ParamName, Is.EqualTo("notifyIcon"));
        }

        [Test]
        public void ConfigureInitialVisibility_WithValidIcon_ConfiguresAndShowsIcon()
        {
            // Arrange
            var notifyIcon = new NotifyIcon { Visible = true }; // Commencer avec visible pour tester la configuration

            // Act
            bool result = _visibilityManager.ConfigureInitialVisibility(notifyIcon);

            // Assert
            Assert.That(result, Is.True, "Should return true for successful configuration");
            Assert.That(notifyIcon.Visible, Is.True, "Icon should be visible after configuration");

            _mockLoggingService.Verify(
                x => x.LogInfo("VisibilityManager: Configuration de la visibilité initiale..."),
                Times.Once,
                "Should log configuration start");

            _mockLoggingService.Verify(
                x => x.LogInfo("VisibilityManager: Configuration de la visibilité initiale terminée - Résultat: True"),
                Times.Once,
                "Should log configuration completion");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void ConfigureInitialVisibility_WithNullNotifyIcon_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => _visibilityManager.ConfigureInitialVisibility(null));
            Assert.That(ex.ParamName, Is.EqualTo("notifyIcon"));
        }

        [Test]
        public void ShowIcon_WithException_LogsErrorAndThrows()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo("VisibilityManager: Affichage de l'icône dans la zone de notification..."))
                             .Throws(new InvalidOperationException("Test exception"));

            var manager = new VisibilityManager(mockLoggingService.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => manager.ShowIcon(notifyIcon));

            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de l'affichage de l'icône")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when show operation fails");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void HideIcon_WithException_LogsErrorAndThrows()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo("VisibilityManager: Masquage de l'icône de la zone de notification..."))
                             .Throws(new InvalidOperationException("Test exception"));

            var manager = new VisibilityManager(mockLoggingService.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => manager.HideIcon(notifyIcon));

            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors du masquage de l'icône")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when hide operation fails");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void IsIconVisible_WithException_LogsErrorAndThrows()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo(It.IsAny<string>()))
                             .Throws(new InvalidOperationException("Test exception"));

            var manager = new VisibilityManager(mockLoggingService.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => manager.IsIconVisible(notifyIcon));

            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de la vérification de la visibilité")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when visibility check fails");

            // Cleanup
            notifyIcon.Dispose();
        }

        [Test]
        public void VisibilityManager_IsStateless()
        {
            // Arrange
            var icon1 = new NotifyIcon { Visible = false };
            var icon2 = new NotifyIcon { Visible = true };

            // Act - Opérations sur différentes icônes
            _visibilityManager.ShowIcon(icon1);
            _visibilityManager.HideIcon(icon2);

            bool visible1 = _visibilityManager.IsIconVisible(icon1);
            bool visible2 = _visibilityManager.IsIconVisible(icon2);

            // Assert - Les opérations doivent être indépendantes
            Assert.That(visible1, Is.True, "First icon should be visible");
            Assert.That(visible2, Is.False, "Second icon should be invisible");

            // Cleanup
            icon1.Dispose();
            icon2.Dispose();
        }

        [Test]
        public void ConfigureInitialVisibility_CallsShowIconAndIsIconVisible()
        {
            // Arrange
            var notifyIcon = new NotifyIcon();

            // Act
            _visibilityManager.ConfigureInitialVisibility(notifyIcon);

            // Assert - Vérifier que les méthodes internes ont été appelées via les logs
            _mockLoggingService.Verify(
                x => x.LogInfo("VisibilityManager: Affichage de l'icône dans la zone de notification..."),
                Times.Once,
                "ConfigureInitialVisibility should call ShowIcon internally");

            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Vérification de la visibilité de l'icône"))),
                Times.Once,
                "ConfigureInitialVisibility should call IsIconVisible internally");

            // Cleanup
            notifyIcon.Dispose();
        }

        [TearDown]
        public void TearDown()
        {
            // Nettoyer les ressources si nécessaire
        }
    }
}
