using System;
using System.Collections.Generic;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Implementations;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Services.LogDeletionResult
{
    [TestFixture]
    public class DeletionResultFormatterTests
    {
        private DeletionResultFormatter _formatter = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _formatter = new DeletionResultFormatter(_mockLoggingService.Object);
        }

        #region FormatDeletionResult Tests

        [Test]
        public void FormatDeletionResult_WithSuccessfulContext_ReturnsSuccessFormat()
        {
            // Arrange
            var context = CreateTestDeletionContext(success: true);

            // Act
            var result = _formatter.FormatDeletionResult(context);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Does.Contain("Succès: True"));
            Assert.That(result, Does.Contain("Message: Success"));
            Assert.That(result, Does.Contain(context.OperationId.ToString()));
        }

        [Test]
        public void FormatDeletionResult_WithFailedContext_ReturnsFailureFormat()
        {
            // Arrange
            var context = CreateTestDeletionContext(success: false, message: "Deletion failed");

            // Act
            var result = _formatter.FormatDeletionResult(context);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("Succès: False"));
            Assert.That(result, Does.Contain("Deletion failed"));
        }

        [Test]
        public void FormatDeletionResult_WithNullContext_HandlesGracefully()
        {
            // Act
            var result = _formatter.FormatDeletionResult(null!);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("Erreur"));

            // Verify error was logged - le message d'erreur réel ne contient pas "contexte null"
            _mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("formatage résultat suppression")), It.IsAny<Exception>()),
                Times.Once);
        }

        [Test]
        public void FormatDeletionResult_WithContextWithoutItem_HandlesGracefully()
        {
            // Arrange
            var context = new DeletionResultContext
            {
                Success = true,
                Item = null,
                Message = "No item",
                OperationId = Guid.NewGuid(),
                Timestamp = DateTime.Now
            };

            // Act
            var result = _formatter.FormatDeletionResult(context);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("Succès: True"));
            Assert.That(result, Does.Contain("No item")); // Le message réel est "No item", pas "Aucun élément"
        }

        #endregion

        #region FormatValidationResult Tests

        [Test]
        public void FormatValidationResult_WithValidResult_ReturnsFormattedValidation()
        {
            // Arrange
            var validation = CreateTestComprehensiveValidationResult(isValid: true);

            // Act
            var result = _formatter.FormatValidationResult(validation);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("VÉRIFICATION POST-SUPPRESSION"));
            Assert.That(result, Does.Contain("✅ VALIDE"));
            Assert.That(result, Does.Contain(validation.ValidationId.ToString()));
        }

        [Test]
        public void FormatValidationResult_WithInvalidResult_ReturnsFormattedInvalidation()
        {
            // Arrange
            var validation = CreateTestComprehensiveValidationResult(isValid: false);

            // Act
            var result = _formatter.FormatValidationResult(validation);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("❌ INVALIDE"));
            Assert.That(result, Does.Contain("Problèmes détectés"));
        }

        [Test]
        public void FormatValidationResult_WithNullValidation_HandlesGracefully()
        {
            // Act
            var result = _formatter.FormatValidationResult(null!);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("Erreur"));

            // Verify error was logged - le message d'erreur réel ne contient pas "validation null"
            _mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("formatage validation")), It.IsAny<Exception>()),
                Times.Once);
        }

        #endregion

        #region FormatCollectionState Tests

        [Test]
        public void FormatCollectionState_WithSuccessfulState_ReturnsFormattedState()
        {
            // Arrange
            var state = CreateTestCollectionStateInfo(successful: true);

            // Act
            var result = _formatter.FormatCollectionState(state);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("ÉTAT DES COLLECTIONS"));
            Assert.That(result, Does.Contain("✅ SUCCÈS"));
            Assert.That(result, Does.Contain(state.AnalysisId.ToString()));
        }

        [Test]
        public void FormatCollectionState_WithFailedState_ReturnsFormattedError()
        {
            // Arrange
            var state = CreateTestCollectionStateInfo(successful: false, errorMessage: "Analysis failed");

            // Act
            var result = _formatter.FormatCollectionState(state);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("❌ ÉCHEC"));
            Assert.That(result, Does.Contain("Analysis failed"));
        }

        [Test]
        public void FormatCollectionState_WithNullState_HandlesGracefully()
        {
            // Act
            var result = _formatter.FormatCollectionState(null!);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("Erreur"));

            // Verify error was logged - le message d'erreur réel ne contient pas "état null"
            _mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("formatage état collections")), It.IsAny<Exception>()),
                Times.Once);
        }

        #endregion

        #region FormatItemDetails Tests

        [Test]
        public void FormatItemDetails_WithValidItem_ReturnsFormattedDetails()
        {
            // Arrange
            var item = CreateTestClipboardItem();

            // Act
            var result = _formatter.FormatItemDetails(item);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("DÉTAILS DE L'ÉLÉMENT"));
            Assert.That(result, Does.Contain($"ID: {item.Id}"));
            Assert.That(result, Does.Contain($"Type: {item.DataType}"));
            Assert.That(result, Does.Contain(item.TextPreview));
        }

        [Test]
        public void FormatItemDetails_WithNullItem_ReturnsNullItemMessage()
        {
            // Act
            var result = _formatter.FormatItemDetails(null);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("ATTENTION: L'élément est NULL"));
        }

        [Test]
        public void FormatItemDetails_WithLongPreview_TruncatesPreview()
        {
            // Arrange
            var longContent = new string('A', 150); // Plus de 100 caractères
            var item = CreateTestClipboardItem(textPreview: longContent);

            // Act
            var result = _formatter.FormatItemDetails(item);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("..."));
            Assert.That(result, Does.Not.Contain(longContent)); // Le contenu complet ne doit pas être présent
        }

        #endregion

        #region FormatLogHeader Tests

        [Test]
        public void FormatLogHeader_WithValidContext_ReturnsFormattedHeader()
        {
            // Arrange
            var context = CreateTestDeletionContext(success: true);

            // Act
            var result = _formatter.FormatLogHeader(context);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("RÉSULTAT DE SUPPRESSION"));
            Assert.That(result, Does.Contain(context.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff")));
        }

        [Test]
        public void FormatLogHeader_WithNullContext_ReturnsDefaultHeader()
        {
            // Act
            var result = _formatter.FormatLogHeader(null!);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("RÉSULTAT DE SUPPRESSION"));

            // Verify error was logged
            _mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("en-tête")), It.IsAny<Exception>()),
                Times.Once);
        }

        #endregion

        #region FormatLogFooter Tests

        [Test]
        public void FormatLogFooter_WithValidContext_ReturnsFormattedFooter()
        {
            // Arrange
            var context = CreateTestDeletionContext(success: true);

            // Act
            var result = _formatter.FormatLogFooter(context);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("====="));
        }

        [Test]
        public void FormatLogFooter_WithNullContext_ReturnsDefaultFooter()
        {
            // Act
            var result = _formatter.FormatLogFooter(null!);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("====="));
        }

        #endregion

        #region FormatCompleteLog Tests

        [Test]
        public void FormatCompleteLog_WithAllComponents_ReturnsCompleteLog()
        {
            // Arrange
            var context = CreateTestDeletionContext(success: true);
            var validation = CreateTestComprehensiveValidationResult(isValid: true);
            var collectionState = CreateTestCollectionStateInfo(successful: true);

            // Act
            var result = _formatter.FormatCompleteLog(context, validation, collectionState);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("RÉSULTAT DE SUPPRESSION")); // Header
            Assert.That(result, Does.Contain("VÉRIFICATION POST-SUPPRESSION")); // Validation
            Assert.That(result, Does.Contain("ÉTAT DES COLLECTIONS")); // Collection state
            Assert.That(result, Does.Contain("DÉTAILS DE L'ÉLÉMENT")); // Item details
            Assert.That(result, Does.Contain("=====")); // Footer
        }

        [Test]
        public void FormatCompleteLog_WithNullComponents_HandlesGracefully()
        {
            // Act
            var result = _formatter.FormatCompleteLog(null!, null!, null!);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Does.Contain("Erreur"));

            // Verify error was logged
            _mockLoggingService.Verify(
                x => x.LogError(It.IsAny<string>(), It.IsAny<Exception>()),
                Times.AtLeastOnce);
        }

        #endregion

        #region Helper Methods

        private DeletionResultContext CreateTestDeletionContext(bool success, string? message = null)
        {
            var item = CreateTestClipboardItem();
            return new DeletionResultContext
            {
                Success = success,
                Item = item,
                Message = message ?? (success ? "Success" : "Failed"),
                OperationId = Guid.NewGuid(),
                Timestamp = DateTime.Now
            };
        }

        private ClipboardItem CreateTestClipboardItem(string textPreview = "Test item")
        {
            return new ClipboardItem
            {
                Id = 1,
                TextPreview = textPreview,
                CustomName = "Test Custom Name",
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now,
                IsPinned = false,
                OrderIndex = 0
            };
        }

        private ComprehensiveValidationResult CreateTestComprehensiveValidationResult(bool isValid)
        {
            var result = new ComprehensiveValidationResult
            {
                ValidationId = Guid.NewGuid(),
                IsFullyValid = isValid,
                StartTimestamp = DateTime.Now.AddMilliseconds(-100),
                ValidationDuration = TimeSpan.FromMilliseconds(100)
            };

            if (!isValid)
            {
                result.PostDeletionValidation = new DeletionValidationResult
                {
                    IsValid = false,
                    Issues = new List<ValidationIssue>
                    {
                        new ValidationIssue { Code = "ITEM_STILL_PRESENT", Description = "Test issue" }
                    }
                };
            }

            return result;
        }

        private CollectionStateInfo CreateTestCollectionStateInfo(bool successful, string? errorMessage = null)
        {
            return new CollectionStateInfo
            {
                AnalysisId = Guid.NewGuid(),
                AnalysisSuccessful = successful,
                ErrorMessage = errorMessage,
                ViewModelItemCount = successful ? 5 : 0,
                ManagerItemCount = successful ? 5 : 0,
                CollectionsInSync = successful,
                AnalysisTimestamp = DateTime.Now
            };
        }

        #endregion
    }
}
