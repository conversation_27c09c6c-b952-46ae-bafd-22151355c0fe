using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    /// <summary>
    /// Tests supplémentaires pour le SettingsManager qui ne nécessitent pas de thread STA.
    /// Ces tests se concentrent sur la structure et les comportements internes.
    /// </summary>
    [TestFixture]
    public class SettingsManagerExtendedTests
    {
        private Mock<IPersistenceService> _mockPersistenceService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private SettingsManager _settingsManager = null!;

        [SetUp]
        public void Initialize()
        {
            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockLoggingService = new Mock<ILoggingService>();
            
            _mockPersistenceService.Setup(m => m.GetApplicationSettingsAsync())
                .ReturnsAsync(new Dictionary<string, string>());
            _mockPersistenceService.Setup(m => m.SaveApplicationSettingAsync(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(Task.CompletedTask);

            _settingsManager = new SettingsManager(_mockPersistenceService.Object, _mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullPersistenceService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new SettingsManager(null!, _mockLoggingService.Object));
        }

        [Test]
        public void Constructor_WithNullLoggingService_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new SettingsManager(_mockPersistenceService.Object, null));
        }

        [Test]
        public void SettingsManager_ShouldImplementISettingsManager()
        {
            // Arrange & Act
            Type type = typeof(SettingsManager);
            
            // Assert
            Assert.That(typeof(ISettingsManager).IsAssignableFrom(type), Is.True,
                "SettingsManager devrait implémenter ISettingsManager");
        }

        [Test]
        public void SettingsManager_RequiredFields_ShouldExist()
        {
            // Arrange & Act
            Type type = typeof(SettingsManager);
            
            // Assert
            Assert.That(type.GetField("_persistenceService", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _persistenceService devrait exister");
            Assert.That(type.GetField("_settings", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _settings devrait exister");
            Assert.That(type.GetField("_loggingService", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "Le champ _loggingService devrait exister");
        }

        [Test]
        public void SettingsManager_RequiredMethods_ShouldExist()
        {
            // Arrange & Act
            Type type = typeof(SettingsManager);
            
            // Assert
            Assert.That(type.GetMethod("SaveSettingsToPersistenceAsync"), Is.Not.Null,
                "La méthode SaveSettingsToPersistenceAsync devrait exister");
            Assert.That(type.GetMethod("LoadSettingsAsync"), Is.Not.Null,
                "La méthode LoadSettingsAsync devrait exister");
            Assert.That(type.GetMethod("SaveSettingAsync"), Is.Not.Null,
                "La méthode SaveSettingAsync devrait exister");
            Assert.That(type.GetMethod("GetPropertyName", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode privée GetPropertyName devrait exister");
            Assert.That(type.GetMethod("ConvertToPropertyType", BindingFlags.NonPublic | BindingFlags.Instance), Is.Not.Null,
                "La méthode privée ConvertToPropertyType devrait exister");
        }

        [Test]
        public void SettingsManager_RequiredProperties_ShouldExist()
        {
            // Arrange & Act
            Type type = typeof(SettingsManager);
            
            // Assert
            Assert.That(type.GetProperty("MaxHistoryItems"), Is.Not.Null,
                "La propriété MaxHistoryItems devrait exister");
            Assert.That(type.GetProperty("ActiveThemePath"), Is.Not.Null,
                "La propriété ActiveThemePath devrait exister");
            Assert.That(type.GetProperty("ShortcutKeyCombination"), Is.Not.Null,
                "La propriété ShortcutKeyCombination devrait exister");
            Assert.That(type.GetProperty("StartWithWindows"), Is.Not.Null,
                "La propriété StartWithWindows devrait exister");
            Assert.That(type.GetProperty("MaxImageDimensionForThumbnail"), Is.Not.Null,
                "La propriété MaxImageDimensionForThumbnail devrait exister");
            Assert.That(type.GetProperty("ThumbnailSize"), Is.Not.Null,
                "La propriété ThumbnailSize devrait exister");
            Assert.That(type.GetProperty("MaxStorableItemSizeBytes"), Is.Not.Null,
                "La propriété MaxStorableItemSizeBytes devrait exister");
            Assert.That(type.GetProperty("MaxTextPreviewLength"), Is.Not.Null,
                "La propriété MaxTextPreviewLength devrait exister");
            Assert.That(type.GetProperty("HideTimestamp"), Is.Not.Null,
                "La propriété HideTimestamp devrait exister");
            Assert.That(type.GetProperty("HideItemTitle"), Is.Not.Null,
                "La propriété HideItemTitle devrait exister");
            Assert.That(type.GetProperty("SettingsWindowWidth"), Is.Not.Null,
                "La propriété SettingsWindowWidth devrait exister");
            Assert.That(type.GetProperty("SettingsWindowHeight"), Is.Not.Null,
                "La propriété SettingsWindowHeight devrait exister");
            Assert.That(type.GetProperty("SettingsWindowTop"), Is.Not.Null,
                "La propriété SettingsWindowTop devrait exister");
            Assert.That(type.GetProperty("SettingsWindowLeft"), Is.Not.Null,
                "La propriété SettingsWindowLeft devrait exister");
        }

        [Test]
        public void SettingsManager_RequiredEvents_ShouldExist()
        {
            // Arrange & Act
            Type type = typeof(SettingsManager);
            
            // Assert
            var events = type.GetEvents(BindingFlags.Public | BindingFlags.Instance);
            bool hasSettingChangedEvent = false;
            
            foreach (var eventInfo in events)
            {
                if (eventInfo.Name == "SettingChanged")
                {
                    hasSettingChangedEvent = true;
                    break;
                }
            }
            
            Assert.That(hasSettingChangedEvent, Is.True, "SettingsManager devrait avoir un événement SettingChanged");
        }

        [Test]
        public void ConvertToPropertyType_WithIntValue_ReturnsConvertedValue()
        {
            // Arrange
            var method = typeof(SettingsManager).GetMethod("ConvertToPropertyType", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Act
            var result = method!.Invoke(_settingsManager, new object[] { typeof(int), "123" });
            
            // Assert
            Assert.That(result, Is.EqualTo(123));
        }

        [Test]
        public void ConvertToPropertyType_WithBoolValue_ReturnsConvertedValue()
        {
            // Arrange
            var method = typeof(SettingsManager).GetMethod("ConvertToPropertyType", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Act
            var result = method!.Invoke(_settingsManager, new object[] { typeof(bool), "True" });
            
            // Assert
            Assert.That(result, Is.EqualTo(true));
        }

        [Test]
        public void ConvertToPropertyType_WithDoubleValue_ReturnsConvertedValue()
        {
            // Arrange
            var method = typeof(SettingsManager).GetMethod("ConvertToPropertyType", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Act
            var result = method!.Invoke(_settingsManager, new object[] { typeof(double), "123.45" });
            
            // Assert
            Assert.That(result, Is.EqualTo(123.45));
        }

        [Test]
        public void ConvertToPropertyType_WithStringValue_ReturnsOriginalValue()
        {
            // Arrange
            var method = typeof(SettingsManager).GetMethod("ConvertToPropertyType", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Act
            var result = method!.Invoke(_settingsManager, new object[] { typeof(string), "test" });
            
            // Assert
            Assert.That(result, Is.EqualTo("test"));
        }

        [Test]
        public void ConvertToPropertyType_WithInvalidValue_ReturnsNull()
        {
            // Arrange
            var method = typeof(SettingsManager).GetMethod("ConvertToPropertyType", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Act
            var result = method!.Invoke(_settingsManager, new object[] { typeof(int), "not-an-int" });
            
            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public void ThumbnailSize_ReturnsMaxImageDimensionForThumbnail()
        {
            // Arrange
            int expectedSize = 256;
            _settingsManager.MaxImageDimensionForThumbnail = expectedSize;
            
            // Act
            int actualSize = _settingsManager.ThumbnailSize;
            
            // Assert
            Assert.That(actualSize, Is.EqualTo(expectedSize));
        }

        [Test]
        public void HideTimestamp_WhenLoggingIsEnabled_LogsGetAndSet()
        {
            // Arrange
            
            // Act - Get
            bool value = _settingsManager.HideTimestamp;
            
            // Assert
            _mockLoggingService.Verify(l => l.LogInfo(It.IsAny<string>()), Times.AtLeastOnce);
            
            // Reset
            _mockLoggingService.Invocations.Clear();
            
            // Act - Set
            _settingsManager.HideTimestamp = true;
            
            // Assert
            _mockLoggingService.Verify(l => l.LogInfo(It.IsAny<string>()), Times.AtLeastOnce);
        }

        [Test]
        public void HideItemTitle_WhenLoggingIsEnabled_LogsGetAndSet()
        {
            // Arrange
            
            // Act - Get
            bool value = _settingsManager.HideItemTitle;
            
            // Assert
            _mockLoggingService.Verify(l => l.LogInfo(It.IsAny<string>()), Times.AtLeastOnce);
            
            // Reset
            _mockLoggingService.Invocations.Clear();
            
            // Act - Set
            _settingsManager.HideItemTitle = true;
            
            // Assert
            _mockLoggingService.Verify(l => l.LogInfo(It.IsAny<string>()), Times.AtLeastOnce);
        }

        [Test]
        public void ShortcutKeyCombination_WhenLoggingIsEnabled_LogsGetAndSet()
        {
            // Arrange
            
            // Act - Get
            string value = _settingsManager.ShortcutKeyCombination;
            
            // Assert
            _mockLoggingService.Verify(l => l.LogInfo(It.IsAny<string>()), Times.AtLeastOnce);
            
            // Reset
            _mockLoggingService.Invocations.Clear();
            
            // Act - Set
            _settingsManager.ShortcutKeyCombination = "Ctrl+Alt+X";
            
            // Assert
            _mockLoggingService.Verify(l => l.LogInfo(It.IsAny<string>()), Times.AtLeastOnce);
        }
    }
} 