using System.Collections.ObjectModel;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Core.Services.Deletion
{
    /// <summary>
    /// Service orchestrateur principal pour la suppression d'éléments du presse-papiers.
    /// Coordonne toutes les étapes de la suppression : validation, UI, persistance, notifications.
    /// Complexité cyclomatique cible : 3
    /// </summary>
    public interface IDeletionService
    {
        /// <summary>
        /// Supprime un élément du presse-papiers de manière asynchrone.
        /// Cette méthode orchestre toute la logique de suppression y compris la gestion d'IsOperationInProgress.
        /// </summary>
        /// <param name="item">L'élément à supprimer</param>
        /// <param name="operationId">Identifiant unique de l'opération pour le tracking</param>
        /// <param name="uiCollection">Collection UI pour la suppression et le rollback</param>
        /// <param name="viewModelState">Interface pour gérer l'état IsOperationInProgress du ViewModel</param>
        /// <returns>Le résultat de l'opération de suppression</returns>
        Task<DeletionResult> DeleteItemAsync(ClipboardItem? item, string operationId, ObservableCollection<ClipboardItem> uiCollection, IViewModelOperationState viewModelState);
    }

    /// <summary>
    /// Résultat d'une opération de suppression.
    /// Encapsule toutes les informations nécessaires pour le feedback utilisateur.
    /// </summary>
    public class DeletionResult
    {
        /// <summary>
        /// Indique si la suppression a réussi.
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Nom de l'élément supprimé (pour les notifications).
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// Message d'erreur en cas d'échec.
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// ID de l'élément supprimé.
        /// </summary>
        public long ItemId { get; set; }

        /// <summary>
        /// Identifiant de l'opération pour le tracking.
        /// </summary>
        public string OperationId { get; set; } = string.Empty;

        /// <summary>
        /// Indique si un rollback UI a été effectué.
        /// </summary>
        public bool RollbackPerformed { get; set; }

        /// <summary>
        /// Exception originale en cas d'erreur (pour le diagnostic).
        /// </summary>
        public Exception? OriginalException { get; set; }

        /// <summary>
        /// Crée un résultat de succès.
        /// </summary>
        public static DeletionResult CreateSuccess(ClipboardItem item, string operationId)
        {
            return new DeletionResult
            {
                Success = true,
                ItemName = item.CustomName ?? item.TextPreview ?? "Élément",
                ItemId = item.Id,
                OperationId = operationId
            };
        }

        /// <summary>
        /// Crée un résultat d'échec.
        /// </summary>
        public static DeletionResult CreateFailure(string errorMessage, string operationId, ClipboardItem? item = null, Exception? exception = null, bool rollbackPerformed = false)
        {
            return new DeletionResult
            {
                Success = false,
                ErrorMessage = errorMessage,
                OperationId = operationId,
                ItemName = item?.CustomName ?? item?.TextPreview ?? "Élément",
                ItemId = item?.Id ?? 0,
                RollbackPerformed = rollbackPerformed,
                OriginalException = exception
            };
        }
    }
}
