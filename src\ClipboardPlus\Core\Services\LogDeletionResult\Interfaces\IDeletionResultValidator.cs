using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Interfaces
{
    /// <summary>
    /// Interface pour la validation des résultats de suppression.
    /// Vérifie l'état post-suppression et la cohérence des collections.
    /// </summary>
    public interface IDeletionResultValidator
    {
        /// <summary>
        /// Valide l'état après une suppression d'élément.
        /// </summary>
        /// <param name="item">Élément qui devrait être supprimé</param>
        /// <param name="viewModel">ViewModel contenant les collections</param>
        /// <returns>Résultat de la validation</returns>
        DeletionValidationResult ValidatePostDeletion(ClipboardItem item, ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Valide l'état général des collections.
        /// </summary>
        /// <param name="viewModel">ViewModel à valider</param>
        /// <returns>Résultat de la validation des collections</returns>
        CollectionValidationResult ValidateCollectionState(ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Valide la cohérence entre le ViewModel et le gestionnaire d'historique.
        /// </summary>
        /// <param name="viewModel">ViewModel à analyser</param>
        /// <returns>Résultat de la validation de cohérence</returns>
        ConsistencyValidationResult ValidateConsistency(ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Effectue une validation complète (post-suppression + collections + cohérence).
        /// </summary>
        /// <param name="context">Contexte de suppression</param>
        /// <returns>Résultat de validation complet</returns>
        ComprehensiveValidationResult ValidateComplete(DeletionResultContext context);
    }
}
