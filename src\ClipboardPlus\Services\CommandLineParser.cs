using System;
using System.Windows;
using ClipboardPlus.Core.Services;
using System.Collections.Generic;
using System.Linq;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Interface pour le service d'analyse des arguments de ligne de commande.
    /// </summary>
    public interface ICommandLineParser
    {
        /// <summary>
        /// Traite les arguments de ligne de commande.
        /// </summary>
        /// <param name="args">Arguments de ligne de commande.</param>
        /// <param name="serviceProvider">Le fournisseur de services pour résoudre les dépendances.</param>
        /// <returns>True si l'application doit continuer son exécution, false si elle doit se terminer.</returns>
        bool ProcessCommandLineArguments(string[] args, IServiceProvider serviceProvider);

        /// <summary>
        /// Affiche un message d'aide pour l'application.
        /// </summary>
        void ShowHelpMessage();
    }

    /// <summary>
    /// Implémentation du service d'analyse des arguments de ligne de commande.
    /// </summary>
    public class CommandLineParser : ICommandLineParser
    {
        private const string APP_NAME = "ClipboardPlus";
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initialise une nouvelle instance de la classe <see cref="CommandLineParser"/>.
        /// </summary>
        /// <param name="loggingService">Le service de journalisation.</param>
        public CommandLineParser(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <inheritdoc />
        public bool ProcessCommandLineArguments(string[] args, IServiceProvider serviceProvider)
        {
            if (args == null || args.Length == 0)
            {
                _loggingService.LogInfo("Aucun argument de ligne de commande");
                return true;
            }
            
            _loggingService.LogInfo($"Arguments de ligne de commande: {string.Join(" ", args)}");
            
            foreach (var arg in args)
            {
                switch (arg.ToLowerInvariant())
                {
                    case "--console":
                    case "-c":
                        // Activer l'affichage des logs dans la console
                        _loggingService.LogInfo("Activation de l'affichage des logs dans la console via l'argument de ligne de commande");

                        // MIGRATION 2025-01-13 : Utiliser ILoggingService.EnableConsoleOutput() directement
                        // Plus besoin de cast vers IAdvancedLoggingService (architecture SOLID pure)
                        _loggingService.EnableConsoleOutput(true);
                        _loggingService.LogInfo("Affichage des logs dans la console activé via architecture SOLID");
                        break;
                    
                    case "--help":
                    case "-h":
                    case "/?":
                        // Afficher l'aide
                        ShowHelpMessage();
                        return false; // Indiquer que l'application doit se terminer
                    
                    default:
                        _loggingService.LogWarning($"Argument de ligne de commande non reconnu : {arg}");
                        break;
                }
            }
            
            return true; // Continuer l'exécution
        }

        /// <inheritdoc />
        public void ShowHelpMessage()
        {
            var message = $"{APP_NAME} - Gestionnaire de presse-papiers avancé\n\n" +
                          "Options de ligne de commande :\n" +
                          "  --console, -c : Active l'affichage des logs dans la console\n" +
                          "  --help, -h, /? : Affiche ce message d'aide\n\n" +
                          "Pour plus d'informations, consultez la documentation.";
            
            System.Windows.MessageBox.Show(message, $"{APP_NAME} - Aide", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}