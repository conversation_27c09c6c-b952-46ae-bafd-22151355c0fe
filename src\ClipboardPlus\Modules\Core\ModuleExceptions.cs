using System;

namespace ClipboardPlus.Modules.Core
{
    /// <summary>
    /// Exception de base pour tous les problèmes liés aux modules.
    /// </summary>
    public abstract class ModuleException : Exception
    {
        /// <summary>
        /// Nom du module qui a généré l'exception.
        /// </summary>
        public string ModuleName { get; }

        /// <summary>
        /// État du module au moment de l'exception.
        /// </summary>
        public ModuleState ModuleState { get; }

        protected ModuleException(string moduleName, ModuleState moduleState, string message) 
            : base(message)
        {
            ModuleName = moduleName;
            ModuleState = moduleState;
        }

        protected ModuleException(string moduleName, ModuleState moduleState, string message, Exception innerException) 
            : base(message, innerException)
        {
            ModuleName = moduleName;
            ModuleState = moduleState;
        }
    }

    /// <summary>
    /// Exception levée lors de l'initialisation d'un module.
    /// </summary>
    public class ModuleInitializationException : ModuleException
    {
        public ModuleInitializationException(string moduleName, ModuleState moduleState, string message) 
            : base(moduleName, moduleState, message)
        {
        }

        public ModuleInitializationException(string moduleName, ModuleState moduleState, string message, Exception innerException) 
            : base(moduleName, moduleState, message, innerException)
        {
        }
    }

    /// <summary>
    /// Exception levée lors des opérations d'un module (start, stop, reset).
    /// </summary>
    public class ModuleOperationException : ModuleException
    {
        /// <summary>
        /// Opération qui a échoué.
        /// </summary>
        public string Operation { get; }

        public ModuleOperationException(string moduleName, ModuleState moduleState, string operation, string message) 
            : base(moduleName, moduleState, message)
        {
            Operation = operation;
        }

        public ModuleOperationException(string moduleName, ModuleState moduleState, string operation, string message, Exception innerException) 
            : base(moduleName, moduleState, message, innerException)
        {
            Operation = operation;
        }
    }

    /// <summary>
    /// Exception levée lors d'une violation de dépendance entre modules.
    /// </summary>
    public class ModuleDependencyException : ModuleException
    {
        /// <summary>
        /// Nom du module dépendant manquant.
        /// </summary>
        public string DependencyName { get; }

        public ModuleDependencyException(string moduleName, ModuleState moduleState, string dependencyName, string message) 
            : base(moduleName, moduleState, message)
        {
            DependencyName = dependencyName;
        }

        public ModuleDependencyException(string moduleName, ModuleState moduleState, string dependencyName, string message, Exception innerException) 
            : base(moduleName, moduleState, message, innerException)
        {
            DependencyName = dependencyName;
        }
    }

    /// <summary>
    /// Exception levée lors d'une violation de configuration de module.
    /// </summary>
    public class ModuleConfigurationException : ModuleException
    {
        /// <summary>
        /// Nom de la configuration invalide.
        /// </summary>
        public string ConfigurationKey { get; }

        public ModuleConfigurationException(string moduleName, ModuleState moduleState, string configurationKey, string message) 
            : base(moduleName, moduleState, message)
        {
            ConfigurationKey = configurationKey;
        }

        public ModuleConfigurationException(string moduleName, ModuleState moduleState, string configurationKey, string message, Exception innerException) 
            : base(moduleName, moduleState, message, innerException)
        {
            ConfigurationKey = configurationKey;
        }
    }
}
