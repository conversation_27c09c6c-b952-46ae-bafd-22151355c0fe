using System;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Threading;
using System.Windows.Interop;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.SystemTray
{
    /// <summary>
    /// SOLUTION NATIVE : Service de menu contextuel avec capture des clics extérieurs
    /// </summary>
    public class SimpleContextMenuService
    {
        private readonly ILoggingService _loggingService;
        private Popup? _popup;
        private DispatcherTimer? _autoCloseTimer;

        // API Windows pour capturer les clics extérieurs
        [DllImport("user32.dll")]
        private static extern IntPtr SetCapture(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool ReleaseCapture();

        [DllImport("user32.dll")]
        private static extern IntPtr GetCapture();

        // Removed unused field _captureWindow to eliminate compiler warning

        public SimpleContextMenuService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Affiche un menu contextuel simple à la position spécifiée
        /// </summary>
        public void ShowContextMenu(System.Windows.Point position, Action onHistoryClick, Action onSettingsClick, Action onExitClick)
        {
            try
            {
                _loggingService.LogInfo("🎯 [MENU_TRACE] SimpleContextMenuService: DÉBUT ShowContextMenu NATIVE");
                _loggingService.LogInfo($"🎯 [MENU_TRACE] Position demandée: X={position.X}, Y={position.Y}");

                // Fermer le popup précédent s'il existe
                if (_popup != null)
                {
                    _loggingService.LogInfo("🎯 [MENU_TRACE] Fermeture du popup précédent");
                    _popup.IsOpen = false;
                    _popup = null;
                }

                // Créer le contenu du menu
                _loggingService.LogInfo("🎯 [MENU_TRACE] Création du StackPanel");
                var stackPanel = new StackPanel
                {
                    Background = System.Windows.Media.Brushes.White,
                    MinWidth = 150
                };

                // Ajouter les éléments du menu
                _loggingService.LogInfo("🎯 [MENU_TRACE] Ajout des boutons au menu");
                stackPanel.Children.Add(CreateMenuButton("Afficher l'historique", onHistoryClick));
                stackPanel.Children.Add(CreateMenuButton("Paramètres", onSettingsClick));
                stackPanel.Children.Add(new Separator());
                stackPanel.Children.Add(CreateMenuButton("Quitter", onExitClick));

                // Créer le popup AVEC CAPTURE NATIVE
                _loggingService.LogInfo("🎯 [MENU_TRACE] Création du Popup avec capture native");
                _popup = new Popup
                {
                    Child = stackPanel,
                    StaysOpen = true, // SOLUTION NATIVE : On gère la fermeture manuellement
                    AllowsTransparency = true,
                    PopupAnimation = PopupAnimation.Fade,
                    Placement = PlacementMode.AbsolutePoint,
                    HorizontalOffset = position.X,
                    VerticalOffset = position.Y
                };

                // Logs pour le monitoring DÉTAILLÉ
                _popup.Opened += (sender, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_TRACE] ✅ ÉVÉNEMENT: Popup.Opened déclenché");
                    _loggingService.LogInfo($"🎯 [MENU_TRACE] Popup.IsOpen = {_popup.IsOpen}");
                    _loggingService.LogInfo($"🎯 [MENU_TRACE] Popup.StaysOpen = {_popup.StaysOpen}");
                };

                _popup.Closed += (sender, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_TRACE] ❌ ÉVÉNEMENT: Popup.Closed déclenché");
                    _loggingService.LogInfo("🎯 [MENU_TRACE] ✅ FERMETURE AUTOMATIQUE RÉUSSIE !");

                    // Arrêter le timer de sécurité
                    if (_autoCloseTimer != null)
                    {
                        _autoCloseTimer.Stop();
                        _loggingService.LogInfo("🎯 [MENU_TRACE] ⏰ Timer de sécurité arrêté");
                    }
                };

                // NOUVEAUX ÉVÉNEMENTS POUR TRACER TOUT
                _popup.MouseLeave += (sender, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_TRACE] 🖱️ ÉVÉNEMENT: Popup.MouseLeave");
                };

                _popup.LostFocus += (sender, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_TRACE] 🔍 ÉVÉNEMENT: Popup.LostFocus");
                };

                // AJOUT D'ÉVÉNEMENTS SUPPLÉMENTAIRES POUR DIAGNOSTIC COMPLET
                _popup.MouseEnter += (sender, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_TRACE] 🖱️ ÉVÉNEMENT: Popup.MouseEnter");
                };

                _popup.PreviewMouseDown += (sender, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_TRACE] 🖱️ ÉVÉNEMENT: Popup.PreviewMouseDown");
                };

                _popup.KeyDown += (sender, e) =>
                {
                    _loggingService.LogInfo($"🎯 [MENU_TRACE] ⌨️ ÉVÉNEMENT: Popup.KeyDown - Touche: {e.Key}");
                    if (e.Key == System.Windows.Input.Key.Escape)
                    {
                        _loggingService.LogInfo("🎯 [MENU_TRACE] ⌨️ ÉCHAP détecté - fermeture du popup");
                        _popup.IsOpen = false;
                    }
                };

                // ÉVÉNEMENT POUR DÉTECTER LES CLICS EXTÉRIEURS
                _popup.StaysOpen = false; // Déjà défini mais on le reconfirme
                _loggingService.LogInfo("🎯 [MENU_TRACE] ⚙️ Configuration: StaysOpen=false pour fermeture automatique");

                // TIMER DE SÉCURITÉ pour fermeture automatique après 10 secondes
                _autoCloseTimer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(10)
                };
                _autoCloseTimer.Tick += (sender, e) =>
                {
                    _loggingService.LogInfo("🎯 [MENU_TRACE] ⏰ TIMER: Fermeture automatique après 10 secondes");
                    _autoCloseTimer?.Stop();
                    if (_popup != null && _popup.IsOpen)
                    {
                        _popup.IsOpen = false;
                    }
                };

                // Afficher le popup
                _loggingService.LogInfo("🎯 [MENU_TRACE] Ouverture du popup (IsOpen = true)");
                _popup.IsOpen = true;
                _loggingService.LogInfo($"🎯 [MENU_TRACE] Popup.IsOpen après ouverture = {_popup.IsOpen}");

                // Démarrer le timer de sécurité
                _autoCloseTimer.Start();
                _loggingService.LogInfo("🎯 [MENU_TRACE] ⏰ Timer de sécurité démarré (10 secondes)");

                _loggingService.LogInfo("🎯 [MENU_TRACE] ✅ FIN ShowContextMenu - Menu affiché");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"🎯 [MENU_TRACE] ❌ ERREUR dans ShowContextMenu: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Crée un bouton de menu simple
        /// </summary>
        private System.Windows.Controls.Button CreateMenuButton(string text, Action action)
        {
            var button = new System.Windows.Controls.Button
            {
                Content = text,
                Background = System.Windows.Media.Brushes.Transparent,
                BorderThickness = new Thickness(0),
                HorizontalAlignment = System.Windows.HorizontalAlignment.Stretch,
                HorizontalContentAlignment = System.Windows.HorizontalAlignment.Left,
                Padding = new Thickness(10, 5, 10, 5),
                Cursor = System.Windows.Input.Cursors.Hand
            };

            // Style au survol
            button.MouseEnter += (s, e) =>
            {
                button.Background = System.Windows.Media.Brushes.LightBlue;
            };

            button.MouseLeave += (s, e) =>
            {
                button.Background = System.Windows.Media.Brushes.Transparent;
            };

            // Action au clic AVEC LOGS DÉTAILLÉS
            button.Click += (s, e) =>
            {
                try
                {
                    _loggingService.LogInfo($"🎯 [MENU_TRACE] 🖱️ CLIC sur bouton '{text}'");

                    // Fermer le popup
                    if (_popup != null)
                    {
                        _loggingService.LogInfo("🎯 [MENU_TRACE] Fermeture manuelle du popup avant action");
                        _popup.IsOpen = false;
                        _loggingService.LogInfo("🎯 [MENU_TRACE] Popup fermé manuellement");
                    }

                    // Exécuter l'action
                    _loggingService.LogInfo($"🎯 [MENU_TRACE] Exécution de l'action pour '{text}'");
                    action();
                    _loggingService.LogInfo($"🎯 [MENU_TRACE] ✅ Action '{text}' exécutée avec succès");
                }
                catch (Exception ex)
                {
                    _loggingService.LogError($"🎯 [MENU_TRACE] ❌ ERREUR lors du clic sur '{text}': {ex.Message}", ex);
                }
            };

            return button;
        }
    }
}
