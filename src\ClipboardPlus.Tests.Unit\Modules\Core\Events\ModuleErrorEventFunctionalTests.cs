using NUnit.Framework;
using ClipboardPlus.Modules.Core.Events;
using System;
using System.ComponentModel;

namespace ClipboardPlus.Tests.Unit.Modules.Core.Events
{
    /// <summary>
    /// Tests fonctionnels pour ModuleErrorEvent - vérifient le comportement métier
    /// dans des scénarios d'usage réels de gestion d'erreurs inter-modules
    /// </summary>
    [TestFixture]
    public class ModuleErrorEventFunctionalTests
    {
        #region Scénarios métier de gestion d'erreurs critiques

        [Test]
        public void ModuleErrorEvent_DatabaseConnectionFailure_ShouldCaptureInfrastructureError()
        {
            // Arrange - Scénario : Panne de base de données critique
            var sourceModule = "PersistenceModule";
            var dbException = new InvalidOperationException("Database server is unreachable. Connection timeout after 30 seconds.");
            var context = "Attempting to save clipboard item during user operation";

            // Act
            var errorEvent = new ModuleErrorEvent(sourceModule, dbException, context);

            // Assert - Vérifier que l'erreur d'infrastructure est capturée
            Assert.That(errorEvent.SourceModule, Is.EqualTo("PersistenceModule"));
            Assert.That(errorEvent.Exception, Is.EqualTo(dbException));
            Assert.That(errorEvent.ErrorMessage, Is.EqualTo("Database server is unreachable. Connection timeout after 30 seconds."));
            Assert.That(errorEvent.Context, Is.EqualTo(context));
            
            // Vérifier les propriétés héritées de ModuleEventBase
            Assert.That(errorEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(errorEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
            Assert.That(errorEvent.TargetModule, Is.Null); // Broadcast pour erreurs critiques
        }

        [Test]
        public void ModuleErrorEvent_MemoryExhaustionError_ShouldTriggerEmergencyProtocol()
        {
            // Arrange - Scénario : Épuisement mémoire nécessitant protocole d'urgence
            var sourceModule = "DataProcessingModule";
            var memoryException = new OutOfMemoryException("Insufficient memory to process large clipboard data batch");
            var context = "Processing 50,000 clipboard items in batch operation";

            // Act
            var errorEvent = new ModuleErrorEvent(sourceModule, memoryException, context);

            // Assert - Vérifier que l'erreur mémoire déclenche le protocole d'urgence
            Assert.That(errorEvent.SourceModule, Is.EqualTo("DataProcessingModule"));
            Assert.That(errorEvent.Exception, Is.InstanceOf<OutOfMemoryException>());
            Assert.That(errorEvent.ErrorMessage, Does.Contain("Insufficient memory"));
            Assert.That(errorEvent.Context, Does.Contain("50,000 clipboard items"));
            
            // Vérifier que l'événement peut être utilisé pour déclencher cleanup
            Assert.That(errorEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(errorEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void ModuleErrorEvent_SecurityViolationError_ShouldAlertSecuritySystem()
        {
            // Arrange - Scénario : Violation de sécurité nécessitant alerte
            var sourceModule = "SecurityModule";
            var securityException = new UnauthorizedAccessException("Attempted access to restricted clipboard data without proper authentication");
            var context = "User 'guest-user' attempted to access admin-level clipboard history";

            // Act
            var errorEvent = new ModuleErrorEvent(sourceModule, securityException, context);

            // Assert - Vérifier que la violation de sécurité alerte le système
            Assert.That(errorEvent.SourceModule, Is.EqualTo("SecurityModule"));
            Assert.That(errorEvent.Exception, Is.InstanceOf<UnauthorizedAccessException>());
            Assert.That(errorEvent.ErrorMessage, Does.Contain("restricted clipboard data"));
            Assert.That(errorEvent.Context, Does.Contain("guest-user"));
            
            // Vérifier que l'événement peut déclencher des mesures de sécurité
            Assert.That(errorEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(errorEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void ModuleErrorEvent_DataCorruptionError_ShouldInitiateDataRecovery()
        {
            // Arrange - Scénario : Corruption de données nécessitant récupération
            var sourceModule = "DataIntegrityModule";
            var corruptionException = new ArgumentException("Clipboard history database contains corrupted entries. 15 items affected.");
            var context = "Data integrity check during startup validation";

            // Act
            var errorEvent = new ModuleErrorEvent(sourceModule, corruptionException, context);

            // Assert - Vérifier que la corruption initie la récupération
            Assert.That(errorEvent.SourceModule, Is.EqualTo("DataIntegrityModule"));
            Assert.That(errorEvent.Exception, Is.InstanceOf<ArgumentException>());
            Assert.That(errorEvent.ErrorMessage, Does.Contain("corrupted entries"));
            Assert.That(errorEvent.Context, Does.Contain("startup validation"));
            
            // Vérifier que l'événement peut déclencher la récupération automatique
            Assert.That(errorEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(errorEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        #endregion

        #region Scénarios de diagnostic et monitoring

        [Test]
        public void ModuleErrorEvent_PerformanceDegradationError_ShouldTriggerPerformanceAnalysis()
        {
            // Arrange - Scénario : Dégradation performance nécessitant analyse
            var sourceModule = "PerformanceMonitoringModule";
            var performanceException = new TimeoutException("Clipboard operation exceeded maximum allowed time (30s). System performance degraded.");
            var context = "Monitoring clipboard response times during peak usage";

            // Act
            var errorEvent = new ModuleErrorEvent(sourceModule, performanceException, context);

            // Assert - Vérifier que la dégradation déclenche l'analyse
            Assert.That(errorEvent.SourceModule, Is.EqualTo("PerformanceMonitoringModule"));
            Assert.That(errorEvent.Exception, Is.InstanceOf<TimeoutException>());
            Assert.That(errorEvent.ErrorMessage, Does.Contain("exceeded maximum allowed time"));
            Assert.That(errorEvent.Context, Does.Contain("peak usage"));
            
            // Vérifier que l'événement peut déclencher l'optimisation automatique
            Assert.That(errorEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(errorEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void ModuleErrorEvent_ConfigurationError_ShouldTriggerConfigurationValidation()
        {
            // Arrange - Scénario : Erreur de configuration nécessitant validation
            var sourceModule = "ConfigurationModule";
            var configException = new ArgumentException("Invalid clipboard history retention policy: negative value not allowed");
            var context = "Loading user preferences during application startup";

            // Act
            var errorEvent = new ModuleErrorEvent(sourceModule, configException, context);

            // Assert - Vérifier que l'erreur de config déclenche la validation
            Assert.That(errorEvent.SourceModule, Is.EqualTo("ConfigurationModule"));
            Assert.That(errorEvent.Exception, Is.InstanceOf<ArgumentException>());
            Assert.That(errorEvent.ErrorMessage, Does.Contain("retention policy"));
            Assert.That(errorEvent.Context, Does.Contain("user preferences"));
            
            // Vérifier que l'événement peut déclencher la réinitialisation config
            Assert.That(errorEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(errorEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        #endregion

        #region Scénarios de construction d'événements

        [Test]
        public void ModuleErrorEvent_StringMessageConstructor_ShouldCreateGenericException()
        {
            // Arrange - Scénario : Création d'événement avec message string seulement
            var sourceModule = "GenericModule";
            var errorMessage = "Unexpected error occurred during clipboard synchronization";
            var context = "Background synchronization process";

            // Act
            var errorEvent = new ModuleErrorEvent(sourceModule, errorMessage, context);

            // Assert - Vérifier que l'exception générique est créée
            Assert.That(errorEvent.SourceModule, Is.EqualTo("GenericModule"));
            Assert.That(errorEvent.ErrorMessage, Is.EqualTo(errorMessage));
            Assert.That(errorEvent.Context, Is.EqualTo(context));
            Assert.That(errorEvent.Exception, Is.Not.Null);
            Assert.That(errorEvent.Exception.Message, Is.EqualTo(errorMessage));
            Assert.That(errorEvent.Exception, Is.InstanceOf<Exception>());
            
            // Vérifier les propriétés de base
            Assert.That(errorEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(errorEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void ModuleErrorEvent_NullContextHandling_ShouldGracefullyHandleNullContext()
        {
            // Arrange - Scénario : Événement d'erreur sans contexte spécifique
            var sourceModule = "UnknownModule";
            var exception = new InvalidOperationException("Generic module error");
            string? nullContext = null;

            // Act
            var errorEvent = new ModuleErrorEvent(sourceModule, exception, nullContext);

            // Assert - Vérifier que le contexte null est géré
            Assert.That(errorEvent.SourceModule, Is.EqualTo("UnknownModule"));
            Assert.That(errorEvent.Exception, Is.EqualTo(exception));
            Assert.That(errorEvent.ErrorMessage, Is.EqualTo("Generic module error"));
            Assert.That(errorEvent.Context, Is.Null);
            
            // Vérifier que l'événement reste valide
            Assert.That(errorEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(errorEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        #endregion

        #region Scénarios de récupération d'erreur

        [Test]
        public void ModuleErrorEvent_TransientNetworkError_ShouldSupportRetryLogic()
        {
            // Arrange - Scénario : Erreur réseau transitoire avec logique de retry
            var sourceModule = "NetworkSyncModule";
            var networkException = new System.Net.WebException("The remote server returned an error: (503) Service Unavailable");
            var context = "Synchronizing clipboard data with cloud service - retry attempt 2/5";

            // Act
            var errorEvent = new ModuleErrorEvent(sourceModule, networkException, context);

            // Assert - Vérifier que l'erreur transitoire supporte le retry
            Assert.That(errorEvent.SourceModule, Is.EqualTo("NetworkSyncModule"));
            Assert.That(errorEvent.Exception, Is.InstanceOf<System.Net.WebException>());
            Assert.That(errorEvent.ErrorMessage, Does.Contain("Service Unavailable"));
            Assert.That(errorEvent.Context, Does.Contain("retry attempt 2/5"));
            
            // Vérifier que l'événement peut être utilisé pour logique de retry
            Assert.That(errorEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(errorEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void ModuleErrorEvent_CriticalSystemFailure_ShouldTriggerGracefulShutdown()
        {
            // Arrange - Scénario : Panne système critique nécessitant arrêt gracieux
            var sourceModule = "SystemCoreModule";
            var criticalException = new SystemException("Critical system component failure. Application stability compromised.");
            var context = "Core system health check detected fatal error";

            // Act
            var errorEvent = new ModuleErrorEvent(sourceModule, criticalException, context);

            // Assert - Vérifier que la panne critique déclenche l'arrêt gracieux
            Assert.That(errorEvent.SourceModule, Is.EqualTo("SystemCoreModule"));
            Assert.That(errorEvent.Exception, Is.InstanceOf<SystemException>());
            Assert.That(errorEvent.ErrorMessage, Does.Contain("stability compromised"));
            Assert.That(errorEvent.Context, Does.Contain("fatal error"));
            
            // Vérifier que l'événement peut déclencher l'arrêt d'urgence
            Assert.That(errorEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(errorEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        #endregion
    }
}
