using System;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour le service d'écoute du presse-papier.
    /// Cette interface unifie les fonctionnalités précédemment réparties entre IClipboardMonitorService et IAdvancedClipboardService.
    /// </summary>
    public interface IClipboardListenerService : IDisposable
    {
        /// <summary>
        /// Événement déclenché lorsque le contenu du presse-papier a changé
        /// </summary>
        event EventHandler? ClipboardContentChanged;
        
        /// <summary>
        /// Indique si le service est actuellement en écoute du presse-papier
        /// </summary>
        bool IsListening { get; }
        
        /// <summary>
        /// Démarre l'écoute du presse-papier
        /// </summary>
        /// <returns>True si l'écoute a démarré avec succès, sinon False</returns>
        bool StartListening();
        
        /// <summary>
        /// Arrête l'écoute du presse-papier
        /// </summary>
        void StopListening();
    }
} 