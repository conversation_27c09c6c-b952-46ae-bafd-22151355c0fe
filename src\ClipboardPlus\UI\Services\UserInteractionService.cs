using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using ClipboardPlus.Core.Services;
using WpfApp = System.Windows.Application;
using WpfMessageBox = System.Windows.MessageBox;

namespace ClipboardPlus.UI.Services
{
    /// <summary>
    /// Implémentation WPF du service d'interaction utilisateur.
    /// Gère l'ouverture de fenêtres, dialogues et notifications.
    /// </summary>
    public class UserInteractionService : IUserInteractionService
    {
        private readonly ILoggingService? _loggingService;

        public UserInteractionService(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
            _loggingService?.LogInfo("[VERIFICATION_TEST_2025] UserInteractionService CONSTRUCTEUR APPELÉ");
        }

        /// <summary>
        /// Ouvre la fenêtre de nettoyage avancé de l'historique.
        /// </summary>
        public async Task OpenAdvancedCleanupWindowAsync()
        {
            _loggingService?.LogInfo("[VERIFICATION_TEST_2025] UserInteractionService.OpenAdvancedCleanupWindowAsync APPELÉE");
            _loggingService?.LogInfo("UserInteractionService: Ouverture de la fenêtre de nettoyage avancé");
            
            try
            {
                await Task.Run(() =>
                {
                    // Exécuter sur le thread UI
                    WpfApp.Current.Dispatcher.Invoke(() =>
                    {
                        // TEMPORAIRE: Fenêtre de nettoyage simplifiée
                        // NOTE: Remplacer par AdvancedCleanupWindow dans une future version
                        var tempWindow = new Window
                        {
                            Title = "Nettoyage Avancé de l'Historique",
                            Width = 500,
                            Height = 300,
                            WindowStartupLocation = WindowStartupLocation.CenterScreen,
                            Content = new TextBlock
                            {
                                Text = "Fenêtre de nettoyage avancé\n(En cours d'implémentation)\n\nService d'interaction utilisateur",
                                HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                                VerticalAlignment = System.Windows.VerticalAlignment.Center,
                                FontSize = 16,
                                TextAlignment = System.Windows.TextAlignment.Center
                            }
                        };
                        
                        tempWindow.Show(); // Non-modal
                        _loggingService?.LogInfo("UserInteractionService: Fenêtre temporaire ouverte avec succès");
                    });
                });
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"UserInteractionService: Erreur lors de l'ouverture de la fenêtre: {ex.Message}", ex);
                
                // Fallback: afficher un message d'erreur
                WpfApp.Current.Dispatcher.Invoke(() =>
                {
                    WpfMessageBox.Show($"Erreur lors de l'ouverture de la fenêtre de nettoyage: {ex.Message}",
                                    "ClipboardPlus - Erreur",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Error);
                });
                
                throw;
            }
        }

        /// <summary>
        /// Affiche une notification temporaire à l'utilisateur.
        /// </summary>
        public void ShowNotification(string message, bool isSuccess = true)
        {
            _loggingService?.LogInfo($"UserInteractionService: Notification - {message} (Success: {isSuccess})");

            // TEMPORAIRE: MessageBox simple en attendant l'implémentation des notifications toast
            // NOTE: Implémenter un système de notification toast dans une future version
            WpfApp.Current.Dispatcher.Invoke(() =>
            {
                var icon = isSuccess ? MessageBoxImage.Information : MessageBoxImage.Warning;
                var title = isSuccess ? "ClipboardPlus - Information" : "ClipboardPlus - Attention";

                WpfMessageBox.Show(message, title, MessageBoxButton.OK, icon);
            });
        }

        /// <summary>
        /// Affiche un dialogue de confirmation.
        /// </summary>
        public async Task<bool> ShowConfirmationAsync(string message, string title = "Confirmation")
        {
            _loggingService?.LogInfo($"UserInteractionService: Demande de confirmation - {message}");
            
            return await Task.Run(() =>
            {
                bool result = false;
                WpfApp.Current.Dispatcher.Invoke(() =>
                {
                    var dialogResult = WpfMessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
                    result = dialogResult == MessageBoxResult.Yes;
                });
                
                _loggingService?.LogInfo($"UserInteractionService: Résultat de confirmation - {result}");
                return result;
            });
        }
    }
}
