using System;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.NewItem.Implementations;
using ClipboardPlus.Core.Services.NewItem.Models;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.NewItem
{
    /// <summary>
    /// Tests unitaires pour NewItemValidationService.
    /// Vérifie le respect du principe SRP : responsabilité unique de validation.
    /// </summary>
    [TestFixture]
    [Category("NewItem")]
    [Category("Validation")]
    public class NewItemValidationServiceTests
    {
        private NewItemValidationService _service = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _service = new NewItemValidationService(_mockLoggingService.Object);
        }

        #region Tests de Validation Réussie

        [Test]
        [Description("Valide qu'une création d'élément est autorisée quand aucune opération n'est en cours")]
        public void ValidateCanCreateNewItem_WhenNoOperationInProgress_ReturnsSuccess()
        {
            // Arrange
            bool isOperationInProgress = false;
            bool isItemCreationActive = false;

            // Act
            var result = _service.ValidateCanCreateNewItem(isOperationInProgress, isItemCreationActive);

            // Assert
            Assert.That(result.IsValid, Is.True, "La validation devrait réussir quand aucune opération n'est en cours");
            Assert.That(result.ErrorMessage, Is.Empty, "Aucun message d'erreur ne devrait être présent");
            
            // Vérifier le logging
            _mockLoggingService.Verify(
                x => x.LogInfo("Validation réussie: Création d'élément autorisée"),
                Times.Once,
                "Un log d'information devrait être généré pour une validation réussie");
        }

        #endregion

        #region Tests de Validation Échouée

        [Test]
        [Description("Valide qu'une création d'élément est refusée quand une opération est en cours")]
        public void ValidateCanCreateNewItem_WhenOperationInProgress_ReturnsFailure()
        {
            // Arrange
            bool isOperationInProgress = true;
            bool isItemCreationActive = false;

            // Act
            var result = _service.ValidateCanCreateNewItem(isOperationInProgress, isItemCreationActive);

            // Assert
            Assert.That(result.IsValid, Is.False, "La validation devrait échouer quand une opération est en cours");
            Assert.That(result.ErrorMessage, Is.EqualTo("Une autre opération est en cours. Veuillez patienter."),
                "Le message d'erreur devrait indiquer qu'une opération est en cours");
            
            // Vérifier le logging
            _mockLoggingService.Verify(
                x => x.LogWarning("Validation échouée: Une autre opération est en cours"),
                Times.Once,
                "Un log d'avertissement devrait être généré");
        }

        [Test]
        [Description("Valide qu'une création d'élément est refusée quand la création est déjà active")]
        public void ValidateCanCreateNewItem_WhenItemCreationActive_ReturnsFailure()
        {
            // Arrange
            bool isOperationInProgress = false;
            bool isItemCreationActive = true;

            // Act
            var result = _service.ValidateCanCreateNewItem(isOperationInProgress, isItemCreationActive);

            // Assert
            Assert.That(result.IsValid, Is.False, "La validation devrait échouer quand la création est déjà active");
            Assert.That(result.ErrorMessage, Is.EqualTo("La création d'élément est déjà active."),
                "Le message d'erreur devrait indiquer que la création est déjà active");
            
            // Vérifier le logging
            _mockLoggingService.Verify(
                x => x.LogWarning("Validation échouée: La création d'élément est déjà active"),
                Times.Once,
                "Un log d'avertissement devrait être généré");
        }

        [Test]
        [Description("Valide qu'une création d'élément est refusée quand les deux conditions sont vraies")]
        public void ValidateCanCreateNewItem_WhenBothConditionsTrue_ReturnsFailureForOperation()
        {
            // Arrange
            bool isOperationInProgress = true;
            bool isItemCreationActive = true;

            // Act
            var result = _service.ValidateCanCreateNewItem(isOperationInProgress, isItemCreationActive);

            // Assert
            Assert.That(result.IsValid, Is.False, "La validation devrait échouer");
            Assert.That(result.ErrorMessage, Is.EqualTo("Une autre opération est en cours. Veuillez patienter."),
                "Le message d'erreur devrait prioriser l'opération en cours");
            
            // Vérifier que seul le premier log est généré (priorité à l'opération en cours)
            _mockLoggingService.Verify(
                x => x.LogWarning("Validation échouée: Une autre opération est en cours"),
                Times.Once,
                "Seul le log pour l'opération en cours devrait être généré");
            
            _mockLoggingService.Verify(
                x => x.LogWarning("Validation échouée: La création d'élément est déjà active"),
                Times.Never,
                "Le log pour la création active ne devrait pas être généré");
        }

        #endregion

        #region Tests de Robustesse

        [Test]
        [Description("Valide que le service fonctionne sans service de logging")]
        public void ValidateCanCreateNewItem_WithoutLoggingService_WorksCorrectly()
        {
            // Arrange
            var serviceWithoutLogging = new NewItemValidationService(null);

            // Act & Assert - Ne devrait pas lever d'exception
            Assert.DoesNotThrow(() =>
            {
                var result = serviceWithoutLogging.ValidateCanCreateNewItem(false, false);
                Assert.That(result.IsValid, Is.True);
            }, "Le service devrait fonctionner sans service de logging");
        }

        [Test]
        [Description("Valide que le service peut être créé sans paramètres")]
        public void Constructor_WithoutParameters_CreatesValidInstance()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var service = new NewItemValidationService();
                var result = service.ValidateCanCreateNewItem(false, false);
                Assert.That(result.IsValid, Is.True);
            }, "Le service devrait pouvoir être créé sans paramètres");
        }

        #endregion

        #region Tests de Couverture Complète

        [TestCase(false, false, true, "Aucune restriction")]
        [TestCase(true, false, false, "Opération en cours")]
        [TestCase(false, true, false, "Création active")]
        [TestCase(true, true, false, "Les deux restrictions")]
        [Description("Valide tous les cas possibles de validation")]
        public void ValidateCanCreateNewItem_AllCombinations_ReturnsExpectedResult(
            bool isOperationInProgress, 
            bool isItemCreationActive, 
            bool expectedIsValid, 
            string scenario)
        {
            // Act
            var result = _service.ValidateCanCreateNewItem(isOperationInProgress, isItemCreationActive);

            // Assert
            Assert.That(result.IsValid, Is.EqualTo(expectedIsValid), 
                $"Scénario '{scenario}': IsValid devrait être {expectedIsValid}");
            
            if (expectedIsValid)
            {
                Assert.That(result.ErrorMessage, Is.Empty, 
                    $"Scénario '{scenario}': Aucun message d'erreur attendu");
            }
            else
            {
                Assert.That(result.ErrorMessage, Is.Not.Empty, 
                    $"Scénario '{scenario}': Un message d'erreur est attendu");
            }
        }

        #endregion
    }
}
