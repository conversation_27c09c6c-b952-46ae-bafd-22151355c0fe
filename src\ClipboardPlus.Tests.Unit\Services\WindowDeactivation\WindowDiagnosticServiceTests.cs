using System;
using System.Linq;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.WindowDeactivation;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.WindowDeactivation
{
    /// <summary>
    /// Tests unitaires pour WindowDiagnosticService.
    /// </summary>
    [TestFixture]
    public class WindowDiagnosticServiceTests
    {
        private Mock<ILoggingService>? _mockLoggingService;
        private WindowDiagnosticService? _diagnosticService;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _diagnosticService = new WindowDiagnosticService(_mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new WindowDiagnosticService(null!));
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldReturnValidResult()
        {
            // Act
            var result = _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.AllWindows, Is.Not.Null);
            Assert.That(result.Timestamp, Is.Not.EqualTo(default(DateTime)));
            Assert.That(result.DiagnosticDurationMs, Is.GreaterThanOrEqualTo(0));
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldLogDiagnosticStart()
        {
            // Act
            _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains("Début du diagnostic des fenêtres système"))), Times.Once);
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldSetTimestamp()
        {
            // Arrange
            var beforeAnalysis = DateTime.Now.AddSeconds(-1);
            var afterAnalysis = DateTime.Now.AddSeconds(1);

            // Act
            var result = _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert
            Assert.That(result.Timestamp, Is.GreaterThan(beforeAnalysis));
            Assert.That(result.Timestamp, Is.LessThan(afterAnalysis));
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldMeasureDuration()
        {
            // Act
            var result = _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert
            Assert.That(result.DiagnosticDurationMs, Is.GreaterThanOrEqualTo(0));
            Assert.That(result.DiagnosticDurationMs, Is.LessThan(10000)); // Moins de 10 secondes
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldSetSuccessfulToTrue()
        {
            // Act
            var result = _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert
            Assert.That(result.IsSuccessful, Is.True);
            Assert.That(result.ErrorMessage, Is.Null);
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldLogCompletionWithMetrics()
        {
            // Act
            var result = _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s =>
                s.Contains("DIAGNOSTIC") &&
                s.Contains("fenêtres") &&
                s.Contains("ms"))), Times.Once);
        }

        [Test]
        public void GetWindowInfo_WithNullWindow_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                _diagnosticService!.GetWindowInfo(null!));
        }

        [Test]
        public void GetWindowInfo_WithValidWindow_ShouldReturnWindowInfo()
        {
            // Arrange - Créer une fenêtre test (cela peut nécessiter STA thread)
            // Pour les tests unitaires, on peut mocker ou utiliser une fenêtre simple
            
            // Act & Assert - Test que la méthode existe et peut être appelée
            Assert.DoesNotThrow(() => 
            {
                // Cette partie nécessiterait une fenêtre réelle ou un mock plus sophistiqué
                // Pour l'instant, on teste juste que la méthode ne lève pas d'exception avec null
                try
                {
                    _diagnosticService!.GetWindowInfo(null!);
                }
                catch (ArgumentNullException)
                {
                    // Expected exception
                }
            });
        }

        [Test]
        public void AnalyzeCurrentWindowStateAsync_ShouldReturnSameResultAsSync()
        {
            // Act
            var syncResult = _diagnosticService!.AnalyzeCurrentWindowState();
            var asyncResult = _diagnosticService.AnalyzeCurrentWindowStateAsync().Result;

            // Assert
            Assert.That(asyncResult, Is.Not.Null);
            Assert.That(asyncResult.IsSuccessful, Is.EqualTo(syncResult.IsSuccessful));
            Assert.That(asyncResult.AllWindows, Is.Not.Null);
            Assert.That(asyncResult.DiagnosticDurationMs, Is.GreaterThanOrEqualTo(0));
        }

        [Test]
        public void AnalyzeCurrentWindowStateAsync_ShouldCompleteWithinReasonableTime()
        {
            // Arrange
            var timeout = TimeSpan.FromSeconds(5);

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var task = _diagnosticService!.AnalyzeCurrentWindowStateAsync();
                var completed = task.Wait(timeout);
                Assert.That(completed, Is.True, "L'analyse asynchrone devrait se terminer dans un délai raisonnable");
            });
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldHandleEmptyWindowList()
        {
            // Act
            var result = _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert
            Assert.That(result.AllWindows, Is.Not.Null);
            Assert.That(result.TotalWindowCount, Is.EqualTo(result.AllWindows.Count));
            Assert.That(result.IsSuccessful, Is.True);
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldSetActiveWindowCorrectly()
        {
            // Act
            var result = _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert
            if (result.ActiveWindow != null)
            {
                Assert.That(result.ActiveWindowInfo, Is.Not.Null);
                Assert.That(result.ActiveWindowInfo!.IsActive, Is.True);
            }
            else
            {
                Assert.That(result.ActiveWindowInfo, Is.Null);
            }
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldLogWarningForProblematicWindows()
        {
            // Act
            _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert - Vérifier qu'aucune erreur critique n'est loggée
            _mockLoggingService!.Verify(x => x.LogError(It.IsAny<string>(), It.IsAny<Exception>()), Times.Never);
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldProvideConsistentResults()
        {
            // Act - Exécuter plusieurs fois
            var result1 = _diagnosticService!.AnalyzeCurrentWindowState();
            var result2 = _diagnosticService.AnalyzeCurrentWindowState();

            // Assert - Les résultats devraient être cohérents
            Assert.That(result1.IsSuccessful, Is.EqualTo(result2.IsSuccessful));
            Assert.That(result1.AllWindows, Is.Not.Null);
            Assert.That(result2.AllWindows, Is.Not.Null);
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldHandleExceptionsGracefully()
        {
            // Act & Assert - Le service ne devrait pas lever d'exceptions non gérées
            Assert.DoesNotThrow(() =>
            {
                var result = _diagnosticService!.AnalyzeCurrentWindowState();
                Assert.That(result, Is.Not.Null);
            });
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldProvideValidWindowCount()
        {
            // Act
            var result = _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert
            Assert.That(result.TotalWindowCount, Is.GreaterThanOrEqualTo(0));
            Assert.That(result.TotalWindowCount, Is.EqualTo(result.AllWindows.Count));
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldLogDetailedInformation()
        {
            // Act
            _diagnosticService!.AnalyzeCurrentWindowState();

            // Assert - Vérifier que les logs appropriés sont émis
            _mockLoggingService!.Verify(x => x.LogInfo(It.IsAny<string>()), Times.AtLeastOnce);
        }

        [Test]
        public void Service_ShouldHandleMultipleConcurrentCalls()
        {
            // Act - Simuler des appels concurrents
            var tasks = Enumerable.Range(0, 5)
                .Select(_ => System.Threading.Tasks.Task.Run(() => 
                    _diagnosticService!.AnalyzeCurrentWindowState()))
                .ToArray();

            // Assert
            Assert.DoesNotThrow(() =>
            {
                System.Threading.Tasks.Task.WaitAll(tasks, TimeSpan.FromSeconds(10));
                
                foreach (var task in tasks)
                {
                    Assert.That(task.Result, Is.Not.Null);
                    Assert.That(task.Result.IsSuccessful, Is.True);
                }
            });
        }

        [Test]
        public void AnalyzeCurrentWindowState_ShouldProvideTimestampInRecentPast()
        {
            // Arrange
            var beforeCall = DateTime.Now;

            // Act
            var result = _diagnosticService!.AnalyzeCurrentWindowState();

            // Arrange
            var afterCall = DateTime.Now;

            // Assert
            Assert.That(result.Timestamp, Is.GreaterThanOrEqualTo(beforeCall));
            Assert.That(result.Timestamp, Is.LessThanOrEqualTo(afterCall));
        }
    }
}
