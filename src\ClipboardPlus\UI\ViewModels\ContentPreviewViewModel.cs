using System;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.Helpers;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Extensions;
using WpfApplication = System.Windows.Application;

namespace ClipboardPlus.UI.ViewModels
{
    /// <summary>
    /// ViewModel pour la prévisualisation du contenu d'un élément du presse-papiers.
    /// </summary>
    public class ContentPreviewViewModel : ViewModelBase
    {
        private ClipboardItem? _itemToDisplay;
        private object? _formattedPreviewContent;
        private readonly ILoggingService? _loggingService;
        private readonly IContentPreviewLoader _contentPreviewLoader;

        /// <summary>
        /// Élément à afficher.
        /// </summary>
        public ClipboardItem? ItemToDisplay
        {
            get => _itemToDisplay;
            private set => SetProperty(ref _itemToDisplay, value);
        }

        /// <summary>
        /// Contenu formaté pour l'affichage.
        /// </summary>
        public object? FormattedPreviewContent
        {
            get => _formattedPreviewContent;
            private set => SetProperty(ref _formattedPreviewContent, value);
        }

        /// <summary>
        /// Constructeur avec injection de dépendances.
        /// </summary>
        /// <param name="contentPreviewLoader">Service de chargement de contenu</param>
        public ContentPreviewViewModel(IContentPreviewLoader contentPreviewLoader)
        {
            _contentPreviewLoader = contentPreviewLoader ?? throw new ArgumentNullException(nameof(contentPreviewLoader));

            // Récupérer le service de logging
            _loggingService = GetLoggingService();

            _loggingService?.LogInfo($"[DÉBUT] ContentPreviewViewModel.Constructeur - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");

            try
            {
                // Initialiser les propriétés avec des valeurs par défaut
                // pour éviter les problèmes de liaison null
                _itemToDisplay = null;
                _formattedPreviewContent = "Contenu non disponible";

                _loggingService?.LogInfo("ContentPreviewViewModel.Constructeur - Propriétés initialisées avec des valeurs par défaut");
                _loggingService?.LogInfo("ContentPreviewViewModel.Constructeur - ContentPreviewLoader injecté avec succès");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ContentPreviewViewModel.Constructeur - EXCEPTION: {ex.Message}", ex);
            }

            _loggingService?.LogInfo($"[FIN] ContentPreviewViewModel.Constructeur - Heure: {DateTime.Now:HH:mm:ss.fff}");
        }

        /// <summary>
        /// Constructeur par défaut pour compatibilité (utilise Service Locator).
        /// </summary>
        public ContentPreviewViewModel() : this(GetContentPreviewLoader())
        {
        }

        /// <summary>
        /// Charge le contenu d'un élément pour la prévisualisation.
        /// Utilise la nouvelle architecture SOLID avec ContentPreviewLoader.
        /// </summary>
        /// <param name="item">Élément à prévisualiser.</param>
        public void LoadItemContent(ClipboardItem item)
        {
            _loggingService?.LogInfo($"[DÉBUT] LoadItemContent - ThreadID: {Environment.CurrentManagedThreadId}, Heure: {DateTime.Now:HH:mm:ss.fff}");

            try
            {
                if (item == null)
                {
                    _loggingService?.LogError("LoadItemContent - ERREUR: item est null");
                    ErrorMessageHelper.ShowError(
                        "Impossible de charger le contenu : élément non spécifié.",
                        "ClipboardPlus - Erreur de prévisualisation",
                        new ArgumentNullException(nameof(item)),
                        "LoadItemContent",
                        this);

                    throw new ArgumentNullException(nameof(item));
                }

                _loggingService?.LogInfo($"LoadItemContent - Chargement de l'élément ID: {item.Id}, Type: {item.DataType}, Nom: '{item.CustomName ?? "(sans nom)"}'");

                // Vérification de la taille des données
                int rawDataSize = item.RawData?.Length ?? 0;
                _loggingService?.LogInfo($"LoadItemContent - Taille des données brutes: {rawDataSize} octets");

                // Définir l'élément à afficher
                _loggingService?.LogInfo("LoadItemContent - Définition de ItemToDisplay");
                ItemToDisplay = item;

                // ===== NOUVELLE ARCHITECTURE SOLID =====
                // Déléguer le traitement à ContentPreviewLoader
                _loggingService?.LogInfo("LoadItemContent - Délégation au ContentPreviewLoader (nouvelle architecture)");
                var content = _contentPreviewLoader.LoadPreviewContent(item);

                // Définir le contenu formaté
                FormattedPreviewContent = content;

                _loggingService?.LogInfo($"LoadItemContent - Contenu chargé avec succès via nouvelle architecture, type: {FormattedPreviewContent?.GetType().Name ?? "null"}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"LoadItemContent - EXCEPTION: {ex.Message}", ex);

                // Utiliser ErrorMessageHelper pour afficher l'erreur
                _loggingService?.LogInfo("LoadItemContent - Appel à ErrorMessageHelper.ShowError pour exception");
                ErrorMessageHelper.ShowError(
                    "Erreur lors du chargement du contenu pour la prévisualisation.",
                    "ClipboardPlus - Erreur de prévisualisation",
                    ex,
                    "LoadItemContent",
                    this);

                // En cas d'erreur, afficher un message
                _loggingService?.LogInfo("LoadItemContent - Définition d'un message d'erreur pour FormattedPreviewContent");
                FormattedPreviewContent = $"Erreur lors du chargement de la prévisualisation: {ex.Message}";

                // Assurer que ItemToDisplay est défini même en cas d'erreur pour éviter des problèmes de liaison
                if (ItemToDisplay == null && item != null)
                {
                    _loggingService?.LogInfo("LoadItemContent - Définition de ItemToDisplay malgré l'erreur pour éviter des problèmes de liaison");
                    ItemToDisplay = item;
                }
            }
            finally
            {
                _loggingService?.LogInfo($"[FIN] LoadItemContent - Heure: {DateTime.Now:HH:mm:ss.fff}");
            }
        }







        
        /// <summary>
        /// Obtient le service de journalisation de l'application de manière sécurisée
        /// </summary>
        private ILoggingService? GetLoggingService()
        {
            try
            {
                return WpfApplication.Current?.Services()?.GetService(typeof(ILoggingService)) as ILoggingService;
            }
            catch (Exception)
            {
                // Ne pas journaliser ici pour éviter une récursion infinie
                return null;
            }
        }

        /// <summary>
        /// Obtient le service de chargement de contenu de l'application de manière sécurisée
        /// </summary>
        private static IContentPreviewLoader GetContentPreviewLoader()
        {
            try
            {
                var service = WpfApplication.Current?.Services()?.GetService(typeof(IContentPreviewLoader)) as IContentPreviewLoader;
                if (service == null)
                {
                    throw new InvalidOperationException("IContentPreviewLoader n'est pas enregistré dans le conteneur DI");
                }
                return service;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Impossible d'obtenir IContentPreviewLoader: {ex.Message}", ex);
            }
        }
    }
} 
