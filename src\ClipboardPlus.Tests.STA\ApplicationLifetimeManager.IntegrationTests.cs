using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using ClipboardPlus.Core.DataModels;
using System.Threading;
using System;

namespace ClipboardPlus.Tests.STA
{
    [TestFixture]
    [Apartment(System.Threading.ApartmentState.STA)]
    [NonParallelizable]
    public class ApplicationLifetimeManagerTests
    {
        private ServiceProvider _serviceProvider = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IGlobalShortcutService> _mockShortcutService = null!;
        private Mock<IClipboardListenerService> _mockClipboardListener = null!;
        private Mock<IClipboardHistoryManager> _mockClipboardHistoryManager = null!;
        private Mock<ISystemTrayService> _mockSystemTrayService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;

        [SetUp]
        public void Setup()
        {
            var services = new ServiceCollection();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockShortcutService = new Mock<IGlobalShortcutService>();
            _mockClipboardListener = new Mock<IClipboardListenerService>();
            _mockClipboardHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockSystemTrayService = new Mock<ISystemTrayService>();
            _mockLoggingService = new Mock<ILoggingService>();

            // Simuler les paramètres
            _mockSettingsManager.Setup(s => s.ShortcutKeyCombination).Returns("Ctrl+Alt+V");
            _mockSettingsManager.Setup(s => s.LoadSettingsAsync()).Returns(Task.FromResult(true));

            // Configuration des mocks pour l'initialisation
            _mockShortcutService.Setup(s => s.InitializeAsync(It.IsAny<KeyCombination>())).Returns(Task.FromResult(true));
            _mockClipboardListener.Setup(s => s.StartListening()).Returns(true);
            _mockClipboardListener.Setup(c => c.IsListening).Returns(true);

            // Configuration des mocks pour la fermeture
            _mockClipboardListener.Setup(s => s.StopListening());
            _mockShortcutService.Setup(s => s.UnregisterShortcut());
            _mockClipboardHistoryManager.Setup(m => m.PurgeOrphanedItemsAsync()).Returns(Task.FromResult(0));
            
            // Enregistrer les services (réels et mocks)
            services.AddSingleton<IApplicationLifetimeManager, ApplicationLifetimeManager>();
            services.AddSingleton(_mockLoggingService.Object);
            services.AddSingleton(_mockSettingsManager.Object);
            services.AddSingleton(_mockShortcutService.Object);
            services.AddSingleton(_mockClipboardListener.Object);
            services.AddSingleton(_mockClipboardHistoryManager.Object);
            services.AddSingleton(_mockSystemTrayService.Object);

            _serviceProvider = services.BuildServiceProvider();
        }

        [Test]
        public async Task InitializeServices_Should_Execute_All_Startup_Steps()
        {
            // Arrange
            var lifetimeManager = _serviceProvider.GetRequiredService<IApplicationLifetimeManager>();

            // Act
            await lifetimeManager.InitializeServices(_serviceProvider, (s, e) => { }, (s, e) => { });

            // Assert
            _mockSettingsManager.Verify(s => s.LoadSettingsAsync(), Times.Once, "Les paramètres auraient dû être chargés.");
            _mockShortcutService.Verify(s => s.InitializeAsync(It.IsAny<KeyCombination>()), Times.Once, "Le service de raccourcis aurait dû être initialisé.");
            _mockClipboardListener.Verify(s => s.StartListening(), Times.AtLeastOnce, "L'écoute du presse-papiers aurait dû être tentée au moins une fois.");
        }

        [Test]
        public async Task ApplicationLifecycle_Should_Execute_All_Shutdown_Steps()
        {
            // Arrange
            var lifetimeManager = _serviceProvider.GetRequiredService<IApplicationLifetimeManager>();
            var mutex = new Mutex(true, "TestMutex");
            
            // Act
            // 1. Initialiser les services
            await lifetimeManager.InitializeServices(_serviceProvider, (s, e) => { }, (s, e) => { });
            
            // 2. Simuler l'utilisation de l'application
            await Task.Delay(10);
            
            // 3. Fermer l'application
            lifetimeManager.Shutdown(_serviceProvider, _mockSystemTrayService.Object, mutex, true);
            
            // Assert - Vérifications que chaque méthode est appelée
            _mockClipboardListener.Verify(s => s.StopListening(), Times.Once, "Le service d'écoute aurait dû être arrêté.");
            _mockShortcutService.Verify(s => s.UnregisterShortcut(), Times.Once, "Les raccourcis auraient dû être désenregistrés.");
            _mockClipboardHistoryManager.Verify(m => m.PurgeOrphanedItemsAsync(), Times.Once, "La purge des éléments orphelins aurait dû être effectuée.");
            
            // Pas besoin de nettoyer le mutex, il est déjà libéré par la méthode Shutdown
        }
    }
} 