using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels.Settings;

namespace ClipboardPlus.Core.Services.Settings
{
    /// <summary>
    /// Service responsable de l'application des paramètres utilisateur.
    /// Sépare la logique d'application des paramètres de base et avancés.
    /// </summary>
    public interface ISettingsApplicationService
    {
        /// <summary>
        /// Applique les paramètres de base de l'application.
        /// </summary>
        /// <param name="settings">Données des paramètres de base à appliquer</param>
        /// <returns>Résultat de l'application des paramètres</returns>
        Task<SettingsApplicationResult> ApplyBasicSettingsAsync(BasicSettingsData settings);

        /// <summary>
        /// Applique les paramètres avancés de l'application.
        /// </summary>
        /// <param name="settings">Données des paramètres avancés à appliquer</param>
        /// <returns>Résultat de l'application des paramètres</returns>
        Task<SettingsApplicationResult> ApplyAdvancedSettingsAsync(AdvancedSettingsData settings);
    }
}
