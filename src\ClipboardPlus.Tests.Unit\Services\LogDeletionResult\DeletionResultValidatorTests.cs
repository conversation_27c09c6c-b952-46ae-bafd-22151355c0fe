using System;
using System.Collections.Generic;
using System.Linq;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Implementations;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.Tests.Unit.Helpers;

namespace ClipboardPlus.Tests.Unit.Services.LogDeletionResult
{
    [TestFixture]
    public class DeletionResultValidatorTests
    {
        private DeletionResultValidator _validator = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<ICollectionStateAnalyzer> _mockCollectionAnalyzer = null!;
        private ClipboardHistoryViewModel _viewModel = null!;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockCollectionAnalyzer = new Mock<ICollectionStateAnalyzer>();
            _validator = new DeletionResultValidator(_mockLoggingService.Object, _mockCollectionAnalyzer.Object);
            _viewModel = ClipboardHistoryViewModelTestHelper.CreateViewModel();
        }

        [TearDown]
        public void TearDown()
        {
            _viewModel?.Dispose();
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithNullLoggingService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new DeletionResultValidator(null!, _mockCollectionAnalyzer.Object));
        }

        [Test]
        public void Constructor_WithNullCollectionAnalyzer_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new DeletionResultValidator(_mockLoggingService.Object, null!));
        }

        [Test]
        public void Constructor_WithValidParameters_CreatesInstance()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
                new DeletionResultValidator(_mockLoggingService.Object, _mockCollectionAnalyzer.Object));
        }

        #endregion

        // SUPPRIMÉ : Tests ValidatePostDeletion - méthode supprimée

        // SUPPRIMÉ : Test ValidatePostDeletion_WithDeletedItem_ReturnsValidResult

        // SUPPRIMÉ : Test ValidatePostDeletion_WithItemStillPresent_ReturnsInvalidResult

        // SUPPRIMÉ : Test ValidatePostDeletion_WithNullItem_HandlesGracefully
        // SUPPRIMÉ : Test ValidatePostDeletion_WithNullViewModel_HandlesGracefully
        // SUPPRIMÉ : Test ValidatePostDeletion_WithSimilarButDifferentItem_ReturnsValidResult

        // SUPPRIMÉ : Tests ValidateCollectionState - méthode supprimée

        // SUPPRIMÉ : Test ValidateCollectionState_WithValidState_ReturnsValidResult

        // SUPPRIMÉ : Test ValidateCollectionState_WithDesyncedCollections_ReturnsInvalidResult

        // SUPPRIMÉ : Test ValidateCollectionState_WithNullItems_DetectsAnomalies

        // SUPPRIMÉ : Test ValidateCollectionState_WithNullViewModel_HandlesGracefully

        // SUPPRIMÉ : Commentaires sur ValidateConsistency - méthode supprimée

        #region ValidateComplete Tests

        [Test]
        public void ValidateComplete_WithValidContext_ReturnsComprehensiveResult()
        {
            // Arrange
            var item = CreateTestClipboardItem("Test item");
            var context = new DeletionResultContext
            {
                Success = true,
                Item = item,
                ViewModel = _viewModel,
                OperationId = Guid.NewGuid()
            };

            var mockCollectionState = CreateMockCollectionStateInfo(true, 0, 0);
            _mockCollectionAnalyzer.Setup(x => x.AnalyzeCollectionState(_viewModel))
                .Returns(mockCollectionState);

            // SUPPRIMÉ : Commentaire ValidateConsistency - méthode supprimée

            // Act
            var result = _validator.ValidateComplete(context);

            // Assert - CORRIGÉ : Les méthodes de validation sont maintenant implémentées
            Assert.That(result, Is.Not.Null);
            Assert.That(result.ValidationId, Is.Not.EqualTo(Guid.Empty));
            // Les validations spécifiques sont maintenant fonctionnelles
            Assert.That(result.PostDeletionValidation, Is.Not.Null);
            Assert.That(result.PostDeletionValidation!.IsValid, Is.True);
            Assert.That(result.CollectionValidation, Is.Not.Null);
            Assert.That(result.CollectionValidation!.IsValid, Is.True);
            Assert.That(result.ConsistencyValidation, Is.Not.Null);
            Assert.That(result.ConsistencyValidation!.IsConsistent, Is.True);
            Assert.That(result.ValidationDuration, Is.GreaterThan(TimeSpan.Zero));
        }

        [Test]
        public void ValidateComplete_WithNullContext_HandlesGracefully()
        {
            // Act
            var result = _validator.ValidateComplete(null!);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.IsFullyValid, Is.False);
            Assert.That(result.ValidationId, Is.Not.EqualTo(Guid.Empty));
        }

        [Test]
        public void ValidateComplete_WithContextWithoutItem_SkipsPostDeletionValidation()
        {
            // Arrange
            var context = new DeletionResultContext
            {
                Success = true,
                Item = null,
                ViewModel = _viewModel,
                OperationId = Guid.NewGuid()
            };

            var mockCollectionState = CreateMockCollectionStateInfo(true, 0, 0);
            _mockCollectionAnalyzer.Setup(x => x.AnalyzeCollectionState(_viewModel))
                .Returns(mockCollectionState);

            // SUPPRIMÉ : Commentaire ValidateConsistency - méthode supprimée

            // Act
            var result = _validator.ValidateComplete(context);

            // Assert - CORRIGÉ : Les méthodes de validation sont maintenant implémentées
            Assert.That(result, Is.Not.Null);
            Assert.That(result.PostDeletionValidation, Is.Null); // Pas d'item fourni, donc pas de validation post-suppression
            Assert.That(result.CollectionValidation, Is.Not.Null); // Méthode maintenant implémentée
            Assert.That(result.CollectionValidation!.IsValid, Is.True);
            Assert.That(result.ConsistencyValidation, Is.Not.Null); // Méthode maintenant implémentée
            Assert.That(result.ConsistencyValidation!.IsConsistent, Is.True);
        }

        [Test]
        public void ValidateComplete_WithContextWithoutViewModel_SkipsCollectionValidations()
        {
            // Arrange
            var item = CreateTestClipboardItem("Test item");
            var context = new DeletionResultContext
            {
                Success = true,
                Item = item,
                ViewModel = null,
                OperationId = Guid.NewGuid()
            };

            // Act
            var result = _validator.ValidateComplete(context);

            // Assert - CORRIGÉ : Les méthodes gèrent maintenant les cas null
            Assert.That(result, Is.Not.Null);
            Assert.That(result.PostDeletionValidation, Is.Null); // Pas de ViewModel fourni
            Assert.That(result.CollectionValidation, Is.Null); // Pas de ViewModel fourni
            Assert.That(result.ConsistencyValidation, Is.Null); // Pas de ViewModel fourni
        }

        #endregion

        #region Helper Methods

        private List<ClipboardItem> CreateTestClipboardItems(int count)
        {
            var items = new List<ClipboardItem>();
            for (int i = 0; i < count; i++)
            {
                items.Add(CreateTestClipboardItem($"Test item {i + 1}"));
            }
            return items;
        }

        private ClipboardItem CreateTestClipboardItem(string content)
        {
            return new ClipboardItem
            {
                Id = DateTime.Now.Ticks + Random.Shared.Next(),
                TextPreview = content,
                CustomName = $"Custom {content}",
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes(content),
                Timestamp = DateTime.Now,
                IsPinned = false,
                OrderIndex = 0
            };
        }

        private CollectionStateInfo CreateMockCollectionStateInfo(bool successful, int viewModelCount, int managerCount)
        {
            return new CollectionStateInfo
            {
                AnalysisSuccessful = successful,
                ViewModelItemCount = viewModelCount,
                ManagerItemCount = managerCount,
                CollectionsInSync = viewModelCount == managerCount,
                NullItemCount = 0,
                DuplicateCount = 0,
                Anomalies = new List<CollectionAnomaly>(),
                AnalysisId = Guid.NewGuid(),
                AnalysisTimestamp = DateTime.Now
            };
        }

        // Note: ConsistencyValidationResult n'est plus utilisé dans cette version

        #endregion
    }
}
