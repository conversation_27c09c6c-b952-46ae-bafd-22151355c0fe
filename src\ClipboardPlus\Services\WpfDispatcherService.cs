using System;
using System.Threading.Tasks;
using System.Windows.Threading;
using ClipboardPlus.Core.Services.Interfaces;
using WpfApplication = System.Windows.Application;

namespace ClipboardPlus.Services
{
    /// <summary>
    /// Implémentation par défaut utilisant le dispatcher WPF
    /// </summary>
    public class WpfDispatcherService : IDispatcherService
    {
        private readonly Dispatcher _dispatcher;
        
        /// <summary>
        /// Crée une nouvelle instance du service dispatcher WPF
        /// </summary>
        public WpfDispatcherService()
        {
            _dispatcher = WpfApplication.Current?.Dispatcher 
                ?? throw new InvalidOperationException("Le dispatcher WPF n'est pas disponible");
        }
        
        /// <summary>
        /// Crée une nouvelle instance du service dispatcher avec un dispatcher spécifié
        /// </summary>
        /// <param name="dispatcher">Le dispatcher à utiliser</param>
        public WpfDispatcherService(Dispatcher dispatcher)
        {
            _dispatcher = dispatcher ?? throw new ArgumentNullException(nameof(dispatcher));
        }
        
        /// <inheritdoc/>
        public void Invoke(Action action)
        {
            if (action == null) throw new ArgumentNullException(nameof(action));
            
            if (_dispatcher.CheckAccess())
            {
                action();
            }
            else
            {
                _dispatcher.Invoke(action);
            }
        }
        
        /// <inheritdoc/>
        public T Invoke<T>(Func<T> func)
        {
            if (func == null) throw new ArgumentNullException(nameof(func));
            
            if (_dispatcher.CheckAccess())
            {
                return func();
            }
            else
            {
                return _dispatcher.Invoke(func);
            }
        }
        
        /// <inheritdoc/>
        public Task InvokeAsync(Action action)
        {
            if (action == null) throw new ArgumentNullException(nameof(action));
            
            if (_dispatcher.CheckAccess())
            {
                action();
                return Task.CompletedTask;
            }
            else
            {
                return _dispatcher.InvokeAsync(action).Task;
            }
        }
        
        /// <inheritdoc/>
        public Task<T> InvokeAsync<T>(Func<T> func)
        {
            if (func == null) throw new ArgumentNullException(nameof(func));
            
            if (_dispatcher.CheckAccess())
            {
                return Task.FromResult(func());
            }
            else
            {
                return _dispatcher.InvokeAsync(func).Task;
            }
        }
        
        /// <inheritdoc/>
        public bool CheckAccess()
        {
            return _dispatcher.CheckAccess();
        }

        /// <inheritdoc/>
        public Task InvokeAsync(Func<Task> taskFunc)
        {
            if (taskFunc == null) throw new ArgumentNullException(nameof(taskFunc));

            if (_dispatcher.CheckAccess())
            {
                return taskFunc();
            }
            else
            {
                return _dispatcher.InvokeAsync(taskFunc).Task.Unwrap();
            }
        }

        /// <inheritdoc/>
        public Task<T> InvokeAsync<T>(Func<Task<T>> taskFunc)
        {
            if (taskFunc == null) throw new ArgumentNullException(nameof(taskFunc));

            if (_dispatcher.CheckAccess())
            {
                return taskFunc();
            }
            else
            {
                return _dispatcher.InvokeAsync(taskFunc).Task.Unwrap();
            }
        }
    }
}