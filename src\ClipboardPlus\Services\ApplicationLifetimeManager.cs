using ClipboardPlus.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using System.Windows.Forms;

namespace ClipboardPlus.Services
{
    public class ApplicationLifetimeManager : IApplicationLifetimeManager
    {
        private readonly IServiceProvider? _serviceProvider;
        private IClipboardListenerService? _clipboardListenerService;
        private readonly ILoggingService? _loggingService;
        private readonly object _processingLock = new object();
        private int _isProcessing = 0; // 0 = non, 1 = oui

        public ApplicationLifetimeManager(IServiceProvider? serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _loggingService = serviceProvider?.GetService<ILoggingService>();
        }

        public async Task<ISystemTrayService?> InitializeServices(IServiceProvider services, EventHandler shortcutHandler, EventHandler clipboardHandler)
        {
            var logger = InitializeLogging(services);

            ISystemTrayService? systemTrayService = null;

            try
            {
                var settingsManager = await InitializeSettingsAsync(services, logger);

                ConfigureStartupWithWindows(services, settingsManager, logger);

                // ✅ CORRECTION CRITIQUE: Initialiser les modules avant les autres services
                await InitializeModulesAsync(services, logger);

                systemTrayService = await InitializeShortcutServiceAsync(services, settingsManager, shortcutHandler, logger);

                await InitializeClipboardListenerAsync(services, clipboardHandler, logger);

                systemTrayService = InitializeSystemTrayService(services, logger);

                logger?.LogInfo("InitializeServices: Tous les services ont été initialisés");
            }
            catch (Exception ex)
            {
                logger?.LogCritical($"InitializeServices: Exception non gérée lors de l'initialisation des services: {ex.Message}", ex);
                throw;
            }
            return systemTrayService;
        }

        /// <summary>
        /// Initialise et démarre tous les modules de l'application.
        /// </summary>
        private async Task InitializeModulesAsync(IServiceProvider services, ILoggingService? logger)
        {
            logger?.LogInfo("InitializeModulesAsync: Début de l'initialisation des modules");

            try
            {
                // Récupérer tous les modules
                var historyModule = services.GetService<ClipboardPlus.Modules.History.IHistoryModule>();
                var commandModule = services.GetService<ClipboardPlus.Modules.Commands.ICommandModule>();
                var creationModule = services.GetService<ClipboardPlus.Modules.Creation.ICreationModule>();

                // Initialiser les modules dans l'ordre de dépendance
                if (historyModule != null)
                {
                    logger?.LogInfo("InitializeModulesAsync: Initialisation du HistoryModule");
                    await historyModule.InitializeAsync();
                    await historyModule.StartAsync();
                    logger?.LogInfo("InitializeModulesAsync: HistoryModule initialisé et démarré");
                }
                else
                {
                    logger?.LogWarning("InitializeModulesAsync: HistoryModule non trouvé");
                }

                if (commandModule != null)
                {
                    logger?.LogInfo("InitializeModulesAsync: Initialisation du CommandModule");
                    await commandModule.InitializeAsync();
                    await commandModule.StartAsync();
                    logger?.LogInfo("InitializeModulesAsync: CommandModule initialisé et démarré");
                }
                else
                {
                    logger?.LogWarning("InitializeModulesAsync: CommandModule non trouvé");
                }

                if (creationModule != null)
                {
                    logger?.LogInfo("InitializeModulesAsync: Initialisation du CreationModule");
                    await creationModule.InitializeAsync();
                    await creationModule.StartAsync();
                    logger?.LogInfo("InitializeModulesAsync: CreationModule initialisé et démarré");
                }
                else
                {
                    logger?.LogWarning("InitializeModulesAsync: CreationModule non trouvé");
                }

                logger?.LogInfo("InitializeModulesAsync: Tous les modules ont été initialisés et démarrés");
            }
            catch (Exception ex)
            {
                logger?.LogError($"InitializeModulesAsync: Erreur lors de l'initialisation des modules: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Initialise le service de la barre d'état système
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <param name="logger">Le service de journalisation</param>
        /// <returns>Le service de barre d'état système initialisé, ou null si non disponible</returns>
        private ISystemTrayService? InitializeSystemTrayService(IServiceProvider services, ILoggingService? logger)
        {
            var systemTrayService = services.GetService<ISystemTrayService>();
                            if (systemTrayService == null)
                            {
                                logger?.LogWarning("InitializeServices: Service de barre d'état système non disponible");
                            }
            else
            {
                logger?.LogInfo("InitializeServices: Initialisation de l'icône dans la barre d'état système");
                try
                {
                    systemTrayService.Initialize();
                    logger?.LogInfo("InitializeServices: Barre d'état système initialisée avec succès");
                    
                    logger?.LogInfo("InitializeServices: Affichage de la notification de démarrage");
                    systemTrayService.ShowNotification(
                        "ClipboardPlus", 
                        "Application démarrée. Utilisez Win+V pour afficher l'historique du presse-papiers.",
                        ToolTipIcon.Info
                    );
                        }
                        catch (Exception ex)
                        {
                    logger?.LogError($"InitializeServices: Erreur lors de l'initialisation de la barre d'état système: {ex.Message}", ex);
                }
            }
            return systemTrayService;
        }

        /// <summary>
        /// Initialise le service d'écoute du presse-papier
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <param name="clipboardHandler">Le gestionnaire d'événements pour les changements du presse-papier</param>
        /// <param name="logger">Le service de journalisation</param>
        private async Task InitializeClipboardListenerAsync(
            IServiceProvider services, 
            EventHandler clipboardHandler, 
            ILoggingService? logger)
        {
                // Initialiser le service d'écoute du presse-papier
                _clipboardListenerService = services.GetService<IClipboardListenerService>();
                if (_clipboardListenerService == null)
                {
                    logger?.LogError("InitializeServices: Service d'écoute du presse-papier non disponible - CRITIQUE !");
                    // Ancien service ClipboardMonitorService supprimé - plus de fallback disponible
                }
                else
                {
                    logger?.LogInfo("InitializeServices: Démarrage du nouveau service d'écoute du presse-papier");
                    
                    var clipboardHistoryManager = services.GetService<IClipboardHistoryManager>();
                    if (clipboardHistoryManager == null)
                    {
                        logger?.LogWarning("InitializeServices: Gestionnaire d'historique du presse-papier non disponible");
                    }
                    else
                    {
                        logger?.LogInfo("InitializeServices: Connexion de l'événement ClipboardContentChanged (nouveau service)");
                        _clipboardListenerService.ClipboardContentChanged -= clipboardHandler; // Déconnexion préalable pour éviter les doublons
                        _clipboardListenerService.ClipboardContentChanged += clipboardHandler;
                        logger?.LogInfo("InitializeServices: Événement ClipboardContentChanged connecté avec succès (nouveau service)");
                    }
                    
                    // Essayer de démarrer le service jusqu'à 3 fois en cas d'échec
                    const int maxRetries = 3;
                    const int retryDelayMs = 1000;
                    bool startSuccess = false;
                    int retryCount = 0;
                    
                    while (!startSuccess && retryCount < maxRetries)
                    {
                        if (retryCount > 0)
                        {
                            logger?.LogInfo($"InitializeServices: Tentative {retryCount + 1}/{maxRetries} de démarrage du service d'écoute");
                            await Task.Delay(retryDelayMs);
                        }
                        
                        startSuccess = _clipboardListenerService.StartListening();
                        retryCount++;
                    }
                    
                    if (startSuccess)
                    {
                        logger?.LogInfo("InitializeServices: Service d'écoute du presse-papier démarré avec succès");
                    }
                    else
                    {
                        logger?.LogError($"InitializeServices: Échec du démarrage du service d'écoute du presse-papier après {maxRetries} tentatives");
                    }
                }
        }

        public void Shutdown(IServiceProvider? services, ISystemTrayService? systemTrayService, Mutex? appMutex, bool ownsMutex)
        {
            if (services == null) return;
            var logger = LogShutdownStart(services);
            
            try
            {
                // 1. Arrêter les services qui interagissent avec le système
                ShutdownClipboardListener(services, logger);
                ShutdownGlobalShortcutService(services, logger);

                // 1.5. Arrêter les modules
                ShutdownModules(services, logger);

                // 2. Effectuer les opérations de données finales (asynchrone mais attendu)
                CleanupDatabaseAsync(services, logger).GetAwaiter().GetResult();
                
                // 3. Libérer les ressources UI
                DisposeSystemTrayService(systemTrayService, logger);
                
                // 4. Libérer les ressources internes de l'application
                DisposeApplicationResources(services, appMutex, ownsMutex, logger);
                
                // 5. Finaliser proprement
                FinalizeShutdown(logger);
            }
            catch (Exception ex)
            {
                logger?.LogCritical($"OnExit: Exception non gérée lors de la fermeture: {ex.Message}", ex);
            }
            finally
            {
                logger?.LogInfo("OnExit: Fermeture de l'application terminée");
                logger?.ForceFlush();
            }
        }

        /// <summary>
        /// Journalise le début du processus de fermeture et l'état initial du processus.
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <returns>Le service de journalisation</returns>
        private ILoggingService? LogShutdownStart(IServiceProvider services)
        {
            var logger = services.GetService<ILoggingService>();
            logger?.LogInfo("OnExit: Début de la fermeture de l'application");
            
            var process = Process.GetCurrentProcess();
            logger?.LogInfo($"OnExit: État du processus - ID: {process.Id}, Threads: {process.Threads.Count}, Handles: {process.HandleCount}");
            
            return logger;
        }

        /// <summary>
        /// Initialise le service de journalisation
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <returns>Le service de journalisation initialisé</returns>
        private ILoggingService? InitializeLogging(IServiceProvider services)
        {
            var logger = services.GetService<ILoggingService>();
            logger?.LogInfo("InitializeServices: Début de l'initialisation des services");
            return logger;
        }

        /// <summary>
        /// Initialise et charge les paramètres de l'application
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <param name="logger">Le service de journalisation</param>
        /// <returns>Le gestionnaire de paramètres initialisé, ou null si non disponible</returns>
        private async Task<ISettingsManager?> InitializeSettingsAsync(IServiceProvider services, ILoggingService? logger)
        {
            var settingsManager = services.GetService<ISettingsManager>();
            if (settingsManager == null)
            {
                logger?.LogWarning("InitializeServices: Gestionnaire de paramètres non disponible");
            }
            else
            {
                logger?.LogInfo("InitializeServices: Chargement des paramètres...");
                
                await settingsManager.LoadSettingsAsync();
                
                logger?.LogInfo("InitializeServices: Paramètres chargés avec succès");
            }
            return settingsManager;
        }

        /// <summary>
        /// Configure le démarrage automatique avec Windows selon les paramètres
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <param name="settingsManager">Le gestionnaire de paramètres</param>
        /// <param name="logger">Le service de journalisation</param>
        private void ConfigureStartupWithWindows(IServiceProvider services, ISettingsManager? settingsManager, ILoggingService? logger)
        {
            var startupManager = services.GetService<ISystemStartupManager>();
            if (startupManager != null && settingsManager != null)
            {
                startupManager.SyncStartupWithSettings(settingsManager.StartWithWindows);
                logger?.LogInfo($"InitializeServices: Démarrage automatique synchronisé: {settingsManager.StartWithWindows}");
            }
        }

        /// <summary>
        /// Initialise le service de raccourcis clavier
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <param name="settingsManager">Le gestionnaire de paramètres</param>
        /// <param name="shortcutHandler">Le gestionnaire d'événements pour les raccourcis</param>
        /// <param name="logger">Le service de journalisation</param>
        /// <returns>Le service de barre d'état système, qui peut être null si non disponible</returns>
        private async Task<ISystemTrayService?> InitializeShortcutServiceAsync(
            IServiceProvider services, 
            ISettingsManager? settingsManager, 
            EventHandler shortcutHandler, 
            ILoggingService? logger)
        {
            ISystemTrayService? systemTrayService = null;
            
            var shortcutService = services.GetService<IGlobalShortcutService>();
            if (shortcutService == null)
            {
                logger?.LogWarning("InitializeServices: Service de raccourcis clavier non disponible");
            }
            else if (settingsManager != null)
            {
                var shortcutString = settingsManager.ShortcutKeyCombination;
                logger?.LogInfo($"InitializeServices: Raccourci configuré: {shortcutString}");

                if (KeyCombination.TryParse(shortcutString, out KeyCombination keyCombination))
                {
                    logger?.LogInfo("InitializeServices: Initialisation du raccourci...");
                    try
                    {
                systemTrayService = services.GetService<ISystemTrayService>();
                if (systemTrayService == null)
                {
                    logger?.LogWarning("InitializeServices: Service de barre d'état système non disponible");
                }
                        
                        logger?.LogInfo("InitializeServices: Connexion de l'événement ShortcutActivated");
                        shortcutService.ShortcutActivated -= shortcutHandler; 
                        shortcutService.ShortcutActivated += shortcutHandler;
                        logger?.LogInfo("InitializeServices: Événement ShortcutActivated connecté avec succès");

                        logger?.LogInfo("InitializeServices: Appel de InitializeAsync sur le service de raccourcis...");

                        // REFACTORISATION TERMINÉE : InitializeAsync simplifiée et optimisée
                        // La méthode délègue maintenant à l'architecture interne SOLID
                        try
                        {
                            await shortcutService.InitializeAsync(keyCombination);
                            logger?.LogInfo("InitializeServices: Raccourci initialisé avec succès");
                        }
                        catch (Exception ex)
                        {
                            logger?.LogError($"InitializeServices: Erreur lors de l'initialisation du raccourci: {ex.Message}", ex);
                        }
                        
                        logger?.LogInfo("InitializeServices: Initialisation du raccourci terminée");
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError($"InitializeServices: Erreur lors de l'initialisation du raccourci: {ex.Message}", ex);
                    }
                }
                else
                {
                    logger?.LogError($"InitializeServices: Format de raccourci invalide: {shortcutString}");
            }
            }
            
            return systemTrayService;
        }

        /// <summary>
        /// Arrête et dispose tous les modules de l'application.
        /// </summary>
        private void ShutdownModules(IServiceProvider services, ILoggingService? logger)
        {
            logger?.LogInfo("ShutdownModules: Début de l'arrêt des modules");

            try
            {
                // Récupérer tous les modules
                var historyModule = services.GetService<ClipboardPlus.Modules.History.IHistoryModule>();
                var commandModule = services.GetService<ClipboardPlus.Modules.Commands.ICommandModule>();
                var creationModule = services.GetService<ClipboardPlus.Modules.Creation.ICreationModule>();

                // Arrêter les modules dans l'ordre inverse de démarrage
                if (creationModule != null)
                {
                    logger?.LogInfo("ShutdownModules: Arrêt du CreationModule");
                    try
                    {
                        creationModule.StopAsync().GetAwaiter().GetResult();
                        creationModule.Dispose();
                        logger?.LogInfo("ShutdownModules: CreationModule arrêté et disposé");
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError($"ShutdownModules: Erreur lors de l'arrêt du CreationModule: {ex.Message}", ex);
                    }
                }

                if (commandModule != null)
                {
                    logger?.LogInfo("ShutdownModules: Arrêt du CommandModule");
                    try
                    {
                        commandModule.StopAsync().GetAwaiter().GetResult();
                        commandModule.Dispose();
                        logger?.LogInfo("ShutdownModules: CommandModule arrêté et disposé");
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError($"ShutdownModules: Erreur lors de l'arrêt du CommandModule: {ex.Message}", ex);
                    }
                }

                if (historyModule != null)
                {
                    logger?.LogInfo("ShutdownModules: Arrêt du HistoryModule");
                    try
                    {
                        historyModule.StopAsync().GetAwaiter().GetResult();
                        historyModule.Dispose();
                        logger?.LogInfo("ShutdownModules: HistoryModule arrêté et disposé");
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError($"ShutdownModules: Erreur lors de l'arrêt du HistoryModule: {ex.Message}", ex);
                    }
                }

                logger?.LogInfo("ShutdownModules: Tous les modules ont été arrêtés");
            }
            catch (Exception ex)
            {
                logger?.LogError($"ShutdownModules: Erreur générale lors de l'arrêt des modules: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Arrête et libère les services d'écoute du presse-papiers (nouveau et legacy).
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <param name="logger">Le service de journalisation</param>
        private void ShutdownClipboardListener(IServiceProvider services, ILoggingService? logger)
        {
                // Arrêter le nouveau service d'écoute du presse-papier
                if (_clipboardListenerService != null)
                {
                    logger?.LogInfo("OnExit: Arrêt du service d'écoute du presse-papier (instance mémorisée)");
                    try
                    {
                        if (_clipboardListenerService.IsListening)
                        {
                            _clipboardListenerService.StopListening();
                            logger?.LogInfo("OnExit: Service d'écoute du presse-papier arrêté avec succès");
                        }
                        else
                        {
                            logger?.LogInfo("OnExit: Service d'écoute du presse-papier déjà arrêté");
                        }
                        
                        if (_clipboardListenerService is IDisposable disposableListener)
                        {
                            disposableListener.Dispose();
                            logger?.LogInfo("OnExit: Ressources du service d'écoute du presse-papier libérées");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError($"OnExit: Erreur lors de l'arrêt du service d'écoute du presse-papier (instance mémorisée): {ex.Message}", ex);
                    }
                }
                else
                {
                    // Si l'instance mémorisée n'est pas disponible, essayer de récupérer le service depuis le conteneur
                    var clipboardListener = services.GetService<IClipboardListenerService>();
                    if (clipboardListener != null)
                    {
                        logger?.LogInfo("OnExit: Arrêt du service d'écoute du presse-papier (instance du conteneur)");
                        try
                        {
                            if (clipboardListener.IsListening)
                            {
                                clipboardListener.StopListening();
                                logger?.LogInfo("OnExit: Service d'écoute du presse-papier arrêté avec succès");
                            }
                            else
                            {
                                logger?.LogInfo("OnExit: Service d'écoute du presse-papier déjà arrêté");
                            }
                            
                            if (clipboardListener is IDisposable disposableListener)
                            {
                                disposableListener.Dispose();
                                logger?.LogInfo("OnExit: Ressources du service d'écoute du presse-papier libérées");
                            }
                        }
                        catch (Exception ex)
                        {
                            logger?.LogError($"OnExit: Erreur lors de l'arrêt du service d'écoute du presse-papier (instance du conteneur): {ex.Message}", ex);
                        }
                    }
                }
                
                // Ancien service ClipboardMonitorService supprimé - plus de nettoyage nécessaire
        }

        /// <summary>
        /// Arrête le service de raccourcis clavier globaux.
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <param name="logger">Le service de journalisation</param>
        private void ShutdownGlobalShortcutService(IServiceProvider services, ILoggingService? logger)
        {
                var shortcutService = services.GetService<IGlobalShortcutService>();
                if (shortcutService != null)
                {
                    logger?.LogInfo("OnExit: Arrêt du service de raccourcis clavier");
                    try
                    {
                        shortcutService.UnregisterShortcut();
                        logger?.LogInfo("OnExit: Service de raccourcis clavier arrêté");
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError($"OnExit: Erreur lors de l'arrêt du service de raccourcis clavier: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Nettoie la base de données en purgeant les éléments orphelins avec un timeout.
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <param name="logger">Le service de journalisation</param>
        /// <returns>Une tâche représentant l'opération asynchrone</returns>
        private async Task CleanupDatabaseAsync(IServiceProvider services, ILoggingService? logger)
        {
                try
                {
                    var clipboardManager = services.GetService<IClipboardHistoryManager>();
                    if (clipboardManager != null)
                    {
                        logger?.LogInfo("OnExit: Nettoyage de la base de données");
                    
                    // Utilisation de Task.WhenAny pour un timeout non bloquant
                    var purgeTask = clipboardManager.PurgeOrphanedItemsAsync();
                        
                    if (await Task.WhenAny(purgeTask, Task.Delay(5000)) == purgeTask)
                        {
                        var deletedCount = await purgeTask;
                            logger?.LogInfo($"OnExit: {deletedCount} élément(s) orphelin(s) supprimé(s) de la base de données");
                        }
                        else
                        {
                        logger?.LogWarning("OnExit: Timeout (5s) dépassé lors du nettoyage de la base de données");
                        }
                    }
                    else
                    {
                        logger?.LogWarning("OnExit: Service ClipboardHistoryManager non disponible, impossible de nettoyer la base de données");
                    }
                }
                catch (Exception ex)
                {
                    logger?.LogError($"OnExit: Erreur lors du nettoyage de la base de données: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Libère les ressources du service de la barre d'état système.
        /// </summary>
        /// <param name="systemTrayService">Le service de la barre d'état système à libérer</param>
        /// <param name="logger">Le service de journalisation</param>
        private void DisposeSystemTrayService(ISystemTrayService? systemTrayService, ILoggingService? logger)
        {
                if (systemTrayService != null)
                {
                    logger?.LogInfo("OnExit: Libération du service de la zone de notification");
                    try
                    {
                        systemTrayService.Dispose();
                        logger?.LogInfo("OnExit: Service de la zone de notification libéré");
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError($"OnExit: Erreur lors de la libération du service de la zone de notification: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Libère les ressources de l'application comme le conteneur de services et le Mutex.
        /// </summary>
        /// <param name="services">Le fournisseur de services</param>
        /// <param name="appMutex">Le mutex d'instance unique</param>
        /// <param name="ownsMutex">Indique si l'application possède le mutex</param>
        /// <param name="logger">Le service de journalisation</param>
        private void DisposeApplicationResources(IServiceProvider services, Mutex? appMutex, bool ownsMutex, ILoggingService? logger)
        {
                if (services is IDisposable disposableServices)
                {
                    logger?.LogInfo("OnExit: Libération du fournisseur de services");
                    try
                    {
                        disposableServices.Dispose();
                        logger?.LogInfo("OnExit: Fournisseur de services libéré");
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError($"OnExit: Erreur lors de la libération du fournisseur de services: {ex.Message}", ex);
                    }
                }
                
                if (ownsMutex && appMutex != null)
                {
                    logger?.LogInfo("OnExit: Libération du mutex de l'application");
                    try
                    {
                        appMutex.ReleaseMutex();
                        appMutex.Dispose();
                        logger?.LogInfo("OnExit: Mutex de l'application libéré");
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError($"OnExit: Erreur lors de la libération du mutex: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Finalise la fermeture en appelant le Garbage Collector et en journalisant l'état final.
        /// </summary>
        /// <param name="logger">Le service de journalisation</param>
        private void FinalizeShutdown(ILoggingService? logger)
        {
                logger?.LogInfo("OnExit: Exécution du garbage collector");
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
            var process = Process.GetCurrentProcess();
                logger?.LogInfo($"OnExit: État final du processus - ID: {process.Id}, Threads: {process.Threads.Count}, Handles: {process.HandleCount}");
        }

        public async Task ProcessClipboardContentAsync()
        {
            // Utiliser un verrou pour éviter le traitement simultané du presse-papiers
            if (Interlocked.CompareExchange(ref _isProcessing, 1, 0) == 1)
            {
                _loggingService?.LogInfo("ProcessClipboardContentAsync: Traitement déjà en cours, ignoré");
                return;
            }
            
            try
            {
                // Vérifier si le service est disponible
                if (_serviceProvider == null)
                {
                    _loggingService?.LogError("ProcessClipboardContentAsync: ServiceProvider est null");
                    return;
                }
                
                var clipboardProcessor = _serviceProvider.GetService<IClipboardProcessorService>();
                
                if (clipboardProcessor == null)
                {
                    _loggingService?.LogWarning("ProcessClipboardContentAsync: Service de traitement du presse-papiers non disponible");
                    return;
                }
                
                _loggingService?.LogInfo("ProcessClipboardContentAsync: Début du traitement du contenu du presse-papiers");
                
                try
                {
                    await clipboardProcessor.ProcessCurrentClipboardContentAsync();
                    _loggingService?.LogInfo("ProcessClipboardContentAsync: Fin du traitement du contenu du presse-papiers");
                }
                catch (Exception processorEx)
                {
                    _loggingService?.LogError($"ProcessClipboardContentAsync: Erreur lors du traitement du contenu: {processorEx.Message}", processorEx);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"ProcessClipboardContentAsync: Erreur lors du traitement du contenu du presse-papiers: {ex.Message}", ex);
            }
            finally
            {
                // Réinitialiser le drapeau de traitement
                Interlocked.Exchange(ref _isProcessing, 0);
            }
        }
    }
} 