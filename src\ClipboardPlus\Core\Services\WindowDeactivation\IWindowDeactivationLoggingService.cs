using System;
using System.Collections.Generic;
using System.Windows;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Service de logging spécialisé pour la désactivation de fenêtres.
    /// Extrait de la méthode Window_Deactivated pour améliorer la testabilité et la maintenabilité.
    /// </summary>
    public interface IWindowDeactivationLoggingService
    {
        /// <summary>
        /// Journalise le début d'une opération de désactivation de fenêtre.
        /// </summary>
        /// <param name="window">Fenêtre concernée</param>
        /// <param name="operationId">Identifiant unique de l'opération</param>
        void LogDeactivationStart(Window window, string? operationId = null);

        /// <summary>
        /// Journalise les résultats du diagnostic des fenêtres.
        /// </summary>
        /// <param name="diagnostic">Résultats du diagnostic</param>
        /// <param name="operationId">Identifiant de l'opération</param>
        void LogDiagnosticResults(WindowDiagnosticResult diagnostic, string? operationId = null);

        /// <summary>
        /// Journalise les résultats de la classification des fenêtres.
        /// </summary>
        /// <param name="classification">Résultats de la classification</param>
        /// <param name="operationId">Identifiant de l'opération</param>
        void LogClassificationResults(WindowClassificationResult classification, string? operationId = null);

        /// <summary>
        /// Journalise une décision de visibilité.
        /// </summary>
        /// <param name="decision">Décision de visibilité</param>
        /// <param name="operationId">Identifiant de l'opération</param>
        void LogVisibilityDecision(WindowVisibilityDecision decision, string? operationId = null);

        /// <summary>
        /// Journalise la fin d'une opération de désactivation de fenêtre.
        /// </summary>
        /// <param name="window">Fenêtre concernée</param>
        /// <param name="operationId">Identifiant de l'opération</param>
        void LogDeactivationEnd(Window window, string? operationId = null);

        /// <summary>
        /// Journalise une erreur survenue pendant la désactivation.
        /// </summary>
        /// <param name="operationId">Identifiant de l'opération</param>
        /// <param name="exception">Exception survenue</param>
        /// <param name="context">Contexte supplémentaire</param>
        void LogError(string operationId, Exception exception, string? context = null);

        /// <summary>
        /// Journalise les résultats de validation d'état.
        /// </summary>
        /// <param name="validationResult">Résultat de la validation</param>
        /// <param name="operationId">Identifiant de l'opération</param>
        void LogValidationResult(WindowStateValidationResult validationResult, string? operationId = null);

        /// <summary>
        /// Journalise des métriques de performance.
        /// </summary>
        /// <param name="metrics">Métriques à journaliser</param>
        /// <param name="operationId">Identifiant de l'opération</param>
        void LogPerformanceMetrics(WindowDeactivationMetrics metrics, string? operationId = null);

        /// <summary>
        /// Configure le niveau de verbosité du logging.
        /// </summary>
        /// <param name="config">Configuration du logging</param>
        void ConfigureLogging(WindowDeactivationLoggingConfig config);
    }

    /// <summary>
    /// Métriques de performance pour la désactivation de fenêtres.
    /// </summary>
    public class WindowDeactivationMetrics
    {
        /// <summary>
        /// Durée totale de l'opération en millisecondes.
        /// </summary>
        public double TotalDurationMs { get; set; }

        /// <summary>
        /// Durée du diagnostic en millisecondes.
        /// </summary>
        public double DiagnosticDurationMs { get; set; }

        /// <summary>
        /// Durée de la classification en millisecondes.
        /// </summary>
        public double ClassificationDurationMs { get; set; }

        /// <summary>
        /// Durée de la prise de décision en millisecondes.
        /// </summary>
        public double DecisionDurationMs { get; set; }

        /// <summary>
        /// Nombre de fenêtres analysées.
        /// </summary>
        public int WindowsAnalyzed { get; set; }

        /// <summary>
        /// Utilisation mémoire en octets.
        /// </summary>
        public long MemoryUsageBytes { get; set; }

        /// <summary>
        /// Horodatage des métriques.
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Identifiant de l'opération.
        /// </summary>
        public string OperationId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Configuration pour le logging de désactivation de fenêtres.
    /// </summary>
    public class WindowDeactivationLoggingConfig
    {
        /// <summary>
        /// Niveau de verbosité du logging.
        /// </summary>
        public WindowDeactivationLoggingLevel LoggingLevel { get; set; } = WindowDeactivationLoggingLevel.Normal;

        /// <summary>
        /// Indique si les métriques de performance doivent être loggées.
        /// </summary>
        public bool EnablePerformanceLogging { get; set; } = false;

        /// <summary>
        /// Indique si les détails des fenêtres doivent être loggés.
        /// </summary>
        public bool EnableWindowDetailsLogging { get; set; } = true;

        /// <summary>
        /// Indique si les emojis doivent être utilisés dans les logs.
        /// </summary>
        public bool EnableEmojiLogging { get; set; } = true;

        /// <summary>
        /// Indique si les identifiants d'opération doivent être inclus.
        /// </summary>
        public bool IncludeOperationIds { get; set; } = true;

        /// <summary>
        /// Préfixe pour tous les messages de log.
        /// </summary>
        public string LogPrefix { get; set; } = "[WindowDeactivation]";

        /// <summary>
        /// Format des horodatages dans les logs.
        /// </summary>
        public string TimestampFormat { get; set; } = "HH:mm:ss.fff";
    }

    /// <summary>
    /// Niveaux de logging pour la désactivation de fenêtres.
    /// </summary>
    public enum WindowDeactivationLoggingLevel
    {
        /// <summary>
        /// Logging minimal - erreurs seulement.
        /// </summary>
        Minimal,

        /// <summary>
        /// Logging normal - informations principales.
        /// </summary>
        Normal,

        /// <summary>
        /// Logging détaillé - toutes les informations.
        /// </summary>
        Detailed,

        /// <summary>
        /// Logging de débogage - informations très détaillées.
        /// </summary>
        Debug
    }

    /// <summary>
    /// Contexte de logging pour une opération de désactivation.
    /// </summary>
    public class WindowDeactivationLoggingContext
    {
        /// <summary>
        /// Identifiant unique de l'opération.
        /// </summary>
        public string OperationId { get; set; } = string.Empty;

        /// <summary>
        /// Nom de la fenêtre concernée.
        /// </summary>
        public string WindowName { get; set; } = string.Empty;

        /// <summary>
        /// Horodatage de début de l'opération.
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Propriétés supplémentaires pour le contexte.
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Indique si l'opération est en mode de débogage.
        /// </summary>
        public bool IsDebugMode { get; set; }
    }
}
