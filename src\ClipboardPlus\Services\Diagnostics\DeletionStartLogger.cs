using System;
using System.IO;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Diagnostics;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Services.Diagnostics
{
    /// <summary>
    /// Orchestrateur principal pour la journalisation du début des suppressions
    /// Remplace la méthode LogDeletionStart monolithique par une architecture SOLID
    /// </summary>
    public class DeletionStartLogger : IDeletionStartLogger
    {
        private readonly IDiagnosticDataCollector _dataCollector;
        private readonly IDiagnosticFormatter _formatter;
        private readonly IViewModelAnalyzer _viewModelAnalyzer;
        private readonly ICommandValidator _commandValidator;
        private readonly ILoggingService _loggingService;

        public DeletionStartLogger(
            IDiagnosticDataCollector dataCollector,
            IDiagnosticFormatter formatter,
            IViewModelAnalyzer viewModelAnalyzer,
            ICommandValidator commandValidator,
            ILoggingService loggingService)
        {
            _dataCollector = dataCollector ?? throw new ArgumentNullException(nameof(dataCollector));
            _formatter = formatter ?? throw new ArgumentNullException(nameof(formatter));
            _viewModelAnalyzer = viewModelAnalyzer ?? throw new ArgumentNullException(nameof(viewModelAnalyzer));
            _commandValidator = commandValidator ?? throw new ArgumentNullException(nameof(commandValidator));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        public void LogDeletionStart(ClipboardItem? item, ClipboardHistoryViewModel viewModel)
        {
            LogDeletionStart(item, viewModel, "Suppression d'élément individuel");
        }

        public void LogDeletionStart(ClipboardItem? item, ClipboardHistoryViewModel viewModel, string message)
        {
            try
            {
                // Phase 1: Collecte des données via les services spécialisés
                var itemData = _dataCollector.CollectItemData(item);
                var viewModelData = _dataCollector.CollectViewModelData(viewModel);
                var systemData = _dataCollector.CollectSystemData();

                // Phase 2: Analyses avancées
                var viewModelAnalysis = _viewModelAnalyzer.AnalyzeState(viewModel);
                var collectionHealth = _viewModelAnalyzer.AnalyzeCollectionHealth(viewModel);
                var commandValidation = _commandValidator.ValidateCommands(viewModel, item);

                // Phase 3: Enrichissement des données avec les analyses
                EnrichDataWithAnalysis(viewModelData, viewModelAnalysis, collectionHealth, commandValidation);

                // Phase 4: Formatage du rapport
                var header = _formatter.FormatDiagnosticHeader(message, DateTime.Now);
                var itemInfo = _formatter.FormatItemInformation(itemData);
                var viewModelInfo = _formatter.FormatViewModelInformation(viewModelData);
                var systemInfo = _formatter.FormatSystemInformation(systemData);

                var fullReport = _formatter.AssembleDiagnosticReport(header, itemInfo, viewModelInfo, systemInfo);

                // Phase 5: Écriture du rapport
                WriteReportToFile(fullReport);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Erreur dans LogDeletionStart: {ex.Message}", ex);
                WriteErrorToFile($"ERREUR dans LogDeletionStart: {ex.Message}\n{ex.StackTrace}");
            }
        }

        private void EnrichDataWithAnalysis(DiagnosticData viewModelData, 
            ViewModelAnalysisResult analysis, 
            CollectionHealthInfo health, 
            CommandValidationResult commands)
        {
            // Enrichissement avec l'analyse du ViewModel
            viewModelData.Properties["AnalysisIssuesCount"] = analysis.Issues.Count;
            viewModelData.Properties["HasAnalysisIssues"] = analysis.Issues.Count > 0;

            // Enrichissement avec la santé des collections
            viewModelData.Properties["CollectionHasDuplicates"] = health.HasDuplicates;
            viewModelData.Properties["CollectionHealthWarningsCount"] = health.HealthWarnings.Count;

            // Enrichissement avec la validation des commandes
            viewModelData.Properties["CommandValidationErrorsCount"] = commands.ValidationErrors.Count;
            viewModelData.Properties["CommandValidationWarningsCount"] = commands.ValidationWarnings.Count;

            // Ajout des problèmes détectés aux avertissements
            viewModelData.Warnings.AddRange(analysis.Issues);
            viewModelData.Warnings.AddRange(health.HealthWarnings);
            viewModelData.Warnings.AddRange(commands.ValidationWarnings);
            viewModelData.Errors.AddRange(commands.ValidationErrors);
        }

        private void WriteReportToFile(string report)
        {
            try
            {
                // Utiliser le même répertoire que LoggingService (racine du projet)
                var logDirectory = GetProjectLogDirectory();
                Directory.CreateDirectory(logDirectory);

                var logFileName = $"deletion_diagnostic_{DateTime.Now:yyyyMMdd}.log";
                var logFilePath = Path.Combine(logDirectory, logFileName);

                File.AppendAllText(logFilePath, report);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Erreur écriture fichier diagnostic: {ex.Message}", ex);
                WriteErrorToFile($"ERREUR WriteReportToFile: {ex.Message}\n{ex.StackTrace}");
            }
        }

        private void WriteErrorToFile(string errorMessage)
        {
            try
            {
                // Utiliser le même répertoire que LoggingService (racine du projet)
                var logDirectory = GetProjectLogDirectory();
                Directory.CreateDirectory(logDirectory);

                var logFileName = $"deletion_diagnostic_{DateTime.Now:yyyyMMdd}.log";
                var logFilePath = Path.Combine(logDirectory, logFileName);

                var errorReport = $"\n===== ERREUR DIAGNOSTIC - {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} =====\n{errorMessage}\n=====\n\n";
                File.AppendAllText(logFilePath, errorReport);
            }
            catch
            {
                // Échec silencieux pour éviter les boucles d'erreur
            }
        }

        /// <summary>
        /// Obtient le répertoire de logs du projet (COPIE EXACTE de LoggingService.GetProjectRootDirectory)
        /// </summary>
        private string GetProjectLogDirectory()
        {
            try
            {
                var projectRoot = GetProjectRootDirectory();
                var logDirectory = Path.Combine(projectRoot, "logs");
                _loggingService.LogInfo($"[DIAG] GetProjectLogDirectory - ProjectRoot: {projectRoot}, LogDirectory: {logDirectory}");
                return logDirectory;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"[DIAG] Erreur GetProjectLogDirectory: {ex.Message}", ex);
                // Fallback vers le répertoire courant
                var fallback = Path.Combine(Directory.GetCurrentDirectory(), "logs");
                _loggingService.LogInfo($"[DIAG] Utilisation fallback: {fallback}");
                return fallback;
            }
        }

        /// <summary>
        /// COPIE EXACTE de LoggingService.GetProjectRootDirectory()
        /// </summary>
        private string GetProjectRootDirectory()
        {
            try
            {
                // Commencer par le répertoire de l'assembly en cours
                var currentDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);

                if (string.IsNullOrEmpty(currentDirectory))
                {
                    currentDirectory = Directory.GetCurrentDirectory();
                }

                // Remonter jusqu'à trouver le fichier .sln ou le dossier src
                var directory = new DirectoryInfo(currentDirectory);
                while (directory != null && directory.Parent != null)
                {
                    // Vérifier si on trouve un fichier .sln
                    if (directory.GetFiles("*.sln").Length > 0)
                    {
                        return directory.FullName;
                    }

                    // Vérifier si on trouve un dossier src
                    if (directory.GetDirectories("src").Length > 0)
                    {
                        return directory.FullName;
                    }

                    directory = directory.Parent;
                }

                // Si on ne trouve pas le projet, utiliser le répertoire courant
                return Directory.GetCurrentDirectory();
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"[DIAG] Erreur GetProjectRootDirectory: {ex.Message}", ex);
                // Fallback vers le répertoire courant
                return Directory.GetCurrentDirectory();
            }
        }


    }
}
