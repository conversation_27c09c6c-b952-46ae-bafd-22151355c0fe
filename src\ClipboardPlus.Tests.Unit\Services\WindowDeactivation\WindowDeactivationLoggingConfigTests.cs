using System;
using ClipboardPlus.Core.Services.WindowDeactivation;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.WindowDeactivation
{
    /// <summary>
    /// Tests unitaires pour WindowDeactivationLoggingConfig.
    /// </summary>
    [TestFixture]
    public class WindowDeactivationLoggingConfigTests
    {
        private WindowDeactivationLoggingConfig? _config;

        [SetUp]
        public void SetUp()
        {
            _config = new WindowDeactivationLoggingConfig();
        }

        [Test]
        public void Constructor_ShouldInitializeWithDefaultValues()
        {
            // Act
            var config = new WindowDeactivationLoggingConfig();

            // Assert
            Assert.That(config.LoggingLevel, Is.EqualTo(WindowDeactivationLoggingLevel.Normal));
            Assert.That(config.EnablePerformanceLogging, Is.False);
            Assert.That(config.EnableWindowDetailsLogging, Is.True);
            Assert.That(config.EnableEmojiLogging, Is.True);
            Assert.That(config.IncludeOperationIds, Is.True);
            Assert.That(config.LogPrefix, Is.EqualTo("[WindowDeactivation]"));
            Assert.That(config.TimestampFormat, Is.EqualTo("HH:mm:ss.fff"));
        }

        [Test]
        public void LoggingLevel_ShouldBeSettable()
        {
            // Test tous les niveaux de logging
            var levels = new[]
            {
                WindowDeactivationLoggingLevel.Minimal,
                WindowDeactivationLoggingLevel.Normal,
                WindowDeactivationLoggingLevel.Detailed,
                WindowDeactivationLoggingLevel.Debug
            };

            foreach (var level in levels)
            {
                // Act
                _config!.LoggingLevel = level;

                // Assert
                Assert.That(_config.LoggingLevel, Is.EqualTo(level));
            }
        }

        [Test]
        public void EnablePerformanceLogging_ShouldBeSettable()
        {
            // Act
            _config!.EnablePerformanceLogging = true;

            // Assert
            Assert.That(_config.EnablePerformanceLogging, Is.True);

            // Act
            _config.EnablePerformanceLogging = false;

            // Assert
            Assert.That(_config.EnablePerformanceLogging, Is.False);
        }

        [Test]
        public void EnableWindowDetailsLogging_ShouldBeSettable()
        {
            // Act
            _config!.EnableWindowDetailsLogging = false;

            // Assert
            Assert.That(_config.EnableWindowDetailsLogging, Is.False);

            // Act
            _config.EnableWindowDetailsLogging = true;

            // Assert
            Assert.That(_config.EnableWindowDetailsLogging, Is.True);
        }

        [Test]
        public void EnableEmojiLogging_ShouldBeSettable()
        {
            // Act
            _config!.EnableEmojiLogging = false;

            // Assert
            Assert.That(_config.EnableEmojiLogging, Is.False);

            // Act
            _config.EnableEmojiLogging = true;

            // Assert
            Assert.That(_config.EnableEmojiLogging, Is.True);
        }

        [Test]
        public void IncludeOperationIds_ShouldBeSettable()
        {
            // Act
            _config!.IncludeOperationIds = false;

            // Assert
            Assert.That(_config.IncludeOperationIds, Is.False);

            // Act
            _config.IncludeOperationIds = true;

            // Assert
            Assert.That(_config.IncludeOperationIds, Is.True);
        }

        [Test]
        public void LogPrefix_ShouldBeSettable()
        {
            // Arrange
            var customPrefix = "[CustomPrefix]";

            // Act
            _config!.LogPrefix = customPrefix;

            // Assert
            Assert.That(_config.LogPrefix, Is.EqualTo(customPrefix));
        }

        [Test]
        public void LogPrefix_ShouldHandleEmptyString()
        {
            // Act
            _config!.LogPrefix = string.Empty;

            // Assert
            Assert.That(_config.LogPrefix, Is.EqualTo(string.Empty));
        }

        [Test]
        public void LogPrefix_ShouldHandleNullValue()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _config!.LogPrefix = null!);
        }

        [Test]
        public void TimestampFormat_ShouldBeSettable()
        {
            // Arrange
            var customFormat = "yyyy-MM-dd HH:mm:ss";

            // Act
            _config!.TimestampFormat = customFormat;

            // Assert
            Assert.That(_config.TimestampFormat, Is.EqualTo(customFormat));
        }

        [Test]
        public void TimestampFormat_ShouldHandleVariousFormats()
        {
            // Arrange
            var formats = new[]
            {
                "HH:mm:ss",
                "HH:mm:ss.fff",
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd HH:mm:ss.fff",
                "dd/MM/yyyy HH:mm:ss"
            };

            foreach (var format in formats)
            {
                // Act
                _config!.TimestampFormat = format;

                // Assert
                Assert.That(_config.TimestampFormat, Is.EqualTo(format));
            }
        }

        [Test]
        public void Config_ShouldSupportMinimalLoggingConfiguration()
        {
            // Act
            _config!.LoggingLevel = WindowDeactivationLoggingLevel.Minimal;
            _config.EnablePerformanceLogging = false;
            _config.EnableWindowDetailsLogging = false;
            _config.EnableEmojiLogging = false;
            _config.IncludeOperationIds = false;

            // Assert
            Assert.That(_config.LoggingLevel, Is.EqualTo(WindowDeactivationLoggingLevel.Minimal));
            Assert.That(_config.EnablePerformanceLogging, Is.False);
            Assert.That(_config.EnableWindowDetailsLogging, Is.False);
            Assert.That(_config.EnableEmojiLogging, Is.False);
            Assert.That(_config.IncludeOperationIds, Is.False);
        }

        [Test]
        public void Config_ShouldSupportDebugLoggingConfiguration()
        {
            // Act
            _config!.LoggingLevel = WindowDeactivationLoggingLevel.Debug;
            _config.EnablePerformanceLogging = true;
            _config.EnableWindowDetailsLogging = true;
            _config.EnableEmojiLogging = true;
            _config.IncludeOperationIds = true;

            // Assert
            Assert.That(_config.LoggingLevel, Is.EqualTo(WindowDeactivationLoggingLevel.Debug));
            Assert.That(_config.EnablePerformanceLogging, Is.True);
            Assert.That(_config.EnableWindowDetailsLogging, Is.True);
            Assert.That(_config.EnableEmojiLogging, Is.True);
            Assert.That(_config.IncludeOperationIds, Is.True);
        }

        [Test]
        public void Config_ShouldSupportCloning()
        {
            // Arrange
            _config!.LoggingLevel = WindowDeactivationLoggingLevel.Detailed;
            _config.EnablePerformanceLogging = true;
            _config.EnableWindowDetailsLogging = false;
            _config.EnableEmojiLogging = false;
            _config.IncludeOperationIds = false;
            _config.LogPrefix = "[Custom]";
            _config.TimestampFormat = "yyyy-MM-dd HH:mm:ss";

            // Act
            var clonedConfig = new WindowDeactivationLoggingConfig
            {
                LoggingLevel = _config.LoggingLevel,
                EnablePerformanceLogging = _config.EnablePerformanceLogging,
                EnableWindowDetailsLogging = _config.EnableWindowDetailsLogging,
                EnableEmojiLogging = _config.EnableEmojiLogging,
                IncludeOperationIds = _config.IncludeOperationIds,
                LogPrefix = _config.LogPrefix,
                TimestampFormat = _config.TimestampFormat
            };

            // Assert
            Assert.That(clonedConfig.LoggingLevel, Is.EqualTo(_config.LoggingLevel));
            Assert.That(clonedConfig.EnablePerformanceLogging, Is.EqualTo(_config.EnablePerformanceLogging));
            Assert.That(clonedConfig.EnableWindowDetailsLogging, Is.EqualTo(_config.EnableWindowDetailsLogging));
            Assert.That(clonedConfig.EnableEmojiLogging, Is.EqualTo(_config.EnableEmojiLogging));
            Assert.That(clonedConfig.IncludeOperationIds, Is.EqualTo(_config.IncludeOperationIds));
            Assert.That(clonedConfig.LogPrefix, Is.EqualTo(_config.LogPrefix));
            Assert.That(clonedConfig.TimestampFormat, Is.EqualTo(_config.TimestampFormat));
        }

        [Test]
        public void Config_ShouldHandleProductionConfiguration()
        {
            // Act - Configuration typique pour la production
            _config!.LoggingLevel = WindowDeactivationLoggingLevel.Normal;
            _config.EnablePerformanceLogging = false;
            _config.EnableWindowDetailsLogging = false;
            _config.EnableEmojiLogging = false;
            _config.IncludeOperationIds = true;
            _config.LogPrefix = "[WinDeact]";

            // Assert
            Assert.That(_config.LoggingLevel, Is.EqualTo(WindowDeactivationLoggingLevel.Normal));
            Assert.That(_config.EnablePerformanceLogging, Is.False);
            Assert.That(_config.EnableWindowDetailsLogging, Is.False);
            Assert.That(_config.EnableEmojiLogging, Is.False);
            Assert.That(_config.IncludeOperationIds, Is.True);
            Assert.That(_config.LogPrefix, Is.EqualTo("[WinDeact]"));
        }

        [Test]
        public void Config_ShouldHandleDevelopmentConfiguration()
        {
            // Act - Configuration typique pour le développement
            _config!.LoggingLevel = WindowDeactivationLoggingLevel.Debug;
            _config.EnablePerformanceLogging = true;
            _config.EnableWindowDetailsLogging = true;
            _config.EnableEmojiLogging = true;
            _config.IncludeOperationIds = true;
            _config.TimestampFormat = "HH:mm:ss.fff";

            // Assert
            Assert.That(_config.LoggingLevel, Is.EqualTo(WindowDeactivationLoggingLevel.Debug));
            Assert.That(_config.EnablePerformanceLogging, Is.True);
            Assert.That(_config.EnableWindowDetailsLogging, Is.True);
            Assert.That(_config.EnableEmojiLogging, Is.True);
            Assert.That(_config.IncludeOperationIds, Is.True);
            Assert.That(_config.TimestampFormat, Is.EqualTo("HH:mm:ss.fff"));
        }
    }
}
