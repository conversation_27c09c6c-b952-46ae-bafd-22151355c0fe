using NUnit.Framework;
using ClipboardPlus.Modules.History;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Modules.Core.Events;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ClipboardPlus.Tests.Unit.Modules.History
{
    /// <summary>
    /// Test fonctionnel pour HistoryModuleEvent - vérifie le comportement métier
    /// dans un scénario d'usage réel de gestion des événements d'historique
    /// </summary>
    [TestFixture]
    public class HistoryModuleEventFunctionalTests
    {
        private List<ClipboardItem> _testItems;

        [SetUp]
        public void SetUp()
        {
            _testItems = new List<ClipboardItem>
            {
                new ClipboardItem
                {
                    Id = 1,
                    TextPreview = "Premier élément",
                    RawData = System.Text.Encoding.UTF8.GetBytes("Premier élément"),
                    DataType = ClipboardDataType.Text,
                    Timestamp = DateTime.UtcNow.AddMinutes(-10),
                    IsPinned = false
                },
                new ClipboardItem
                {
                    Id = 2,
                    TextPreview = "Deuxième élément",
                    RawData = System.Text.Encoding.UTF8.GetBytes("Deuxième élément"),
                    DataType = ClipboardDataType.Text,
                    Timestamp = DateTime.UtcNow.AddMinutes(-5),
                    IsPinned = true
                }
            };
        }

        [Test]
        public void HistoryModuleEvent_CompleteEventWorkflow_ShouldTrackHistoryOperationsCorrectly()
        {
            // Arrange - Scénario : Workflow complet de gestion d'événements d'historique
            // Un utilisateur effectue diverses opérations sur l'historique et chaque opération génère un événement

            var sourceModule = "HistoryModule";
            var events = new List<HistoryModuleEvent>();

            // Phase 1: Ajout d'un nouvel élément (utilisateur copie quelque chose)
            var newItem = new ClipboardItem
            {
                Id = 3,
                TextPreview = "Nouvel élément copié",
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.UtcNow
            };

            var addEvent = new HistoryModuleEvent(
                sourceModule, 
                HistoryChangeType.ItemAdded, 
                new[] { newItem }, 
                "User copied new content"
            );
            events.Add(addEvent);

            // Vérifier l'événement d'ajout
            Assert.That(addEvent.SourceModule, Is.EqualTo("HistoryModule"));
            Assert.That(addEvent.ChangeType, Is.EqualTo(HistoryChangeType.ItemAdded));
            Assert.That(addEvent.AffectedItems.Count, Is.EqualTo(1));
            Assert.That(addEvent.AffectedItems[0].TextPreview, Is.EqualTo("Nouvel élément copié"));
            Assert.That(addEvent.Context, Is.EqualTo("User copied new content"));
            Assert.That(addEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(addEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
            Assert.That(addEvent.TargetModule, Is.Null); // Broadcast par défaut

            // Phase 2: Mise à jour d'un élément (utilisateur modifie un élément)
            var updatedItem = _testItems[0];
            updatedItem.TextPreview = "Premier élément modifié";

            var updateEvent = new HistoryModuleEvent(
                sourceModule,
                HistoryChangeType.ItemUpdated,
                new[] { updatedItem },
                "User renamed item"
            );
            events.Add(updateEvent);

            // Vérifier l'événement de mise à jour
            Assert.That(updateEvent.ChangeType, Is.EqualTo(HistoryChangeType.ItemUpdated));
            Assert.That(updateEvent.AffectedItems[0].TextPreview, Is.EqualTo("Premier élément modifié"));
            Assert.That(updateEvent.Context, Is.EqualTo("User renamed item"));

            // Phase 3: Suppression d'un élément (utilisateur supprime un élément)
            var itemToRemove = _testItems[1];

            var removeEvent = new HistoryModuleEvent(
                sourceModule,
                HistoryChangeType.ItemRemoved,
                new[] { itemToRemove },
                "User deleted item"
            );
            events.Add(removeEvent);

            // Vérifier l'événement de suppression
            Assert.That(removeEvent.ChangeType, Is.EqualTo(HistoryChangeType.ItemRemoved));
            Assert.That(removeEvent.AffectedItems[0].Id, Is.EqualTo(2));
            Assert.That(removeEvent.Context, Is.EqualTo("User deleted item"));

            // Phase 4: Rechargement de l'historique (utilisateur clique sur "Actualiser")
            var reloadEvent = new HistoryModuleEvent(
                sourceModule,
                HistoryChangeType.HistoryReloaded,
                _testItems,
                "User requested refresh"
            );
            events.Add(reloadEvent);

            // Vérifier l'événement de rechargement
            Assert.That(reloadEvent.ChangeType, Is.EqualTo(HistoryChangeType.HistoryReloaded));
            Assert.That(reloadEvent.AffectedItems.Count, Is.EqualTo(2));
            Assert.That(reloadEvent.Context, Is.EqualTo("User requested refresh"));

            // Phase 5: Nettoyage de l'historique (utilisateur clique sur "Supprimer tout")
            var clearEvent = new HistoryModuleEvent(
                sourceModule,
                HistoryChangeType.HistoryCleared,
                _testItems.Where(i => !i.IsPinned), // Seuls les éléments non-épinglés sont supprimés
                "User cleared history"
            );
            events.Add(clearEvent);

            // Vérifier l'événement de nettoyage
            Assert.That(clearEvent.ChangeType, Is.EqualTo(HistoryChangeType.HistoryCleared));
            Assert.That(clearEvent.AffectedItems.Count, Is.EqualTo(1)); // Seul l'élément non-épinglé
            Assert.That(clearEvent.AffectedItems.All(i => !i.IsPinned), Is.True);
            Assert.That(clearEvent.Context, Is.EqualTo("User cleared history"));

            // Phase 6: Synchronisation des collections (opération système)
            var syncEvent = new HistoryModuleEvent(
                sourceModule,
                HistoryChangeType.CollectionsSynchronized,
                new List<ClipboardItem>(), // Pas d'éléments spécifiques pour la sync
                "Automatic synchronization"
            );
            events.Add(syncEvent);

            // Vérifier l'événement de synchronisation
            Assert.That(syncEvent.ChangeType, Is.EqualTo(HistoryChangeType.CollectionsSynchronized));
            Assert.That(syncEvent.AffectedItems.Count, Is.EqualTo(0));
            Assert.That(syncEvent.Context, Is.EqualTo("Automatic synchronization"));

            // Vérifications globales du workflow
            Assert.That(events.Count, Is.EqualTo(6));
            
            // Vérifier que tous les événements ont des propriétés de base valides
            foreach (var evt in events)
            {
                Assert.That(evt.SourceModule, Is.EqualTo("HistoryModule"));
                Assert.That(evt.EventId, Is.Not.EqualTo(Guid.Empty));
                Assert.That(evt.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
                Assert.That(evt.AffectedItems, Is.Not.Null);
                Assert.That(evt, Is.InstanceOf<ModuleEventBase>());
            }

            // Vérifier que les événements sont dans l'ordre chronologique
            for (int i = 1; i < events.Count; i++)
            {
                Assert.That(events[i].Timestamp, Is.GreaterThanOrEqualTo(events[i-1].Timestamp));
            }

            // Vérifier que chaque type de changement d'historique est représenté
            var changeTypes = events.Select(e => e.ChangeType).ToHashSet();
            Assert.That(changeTypes.Contains(HistoryChangeType.ItemAdded), Is.True);
            Assert.That(changeTypes.Contains(HistoryChangeType.ItemUpdated), Is.True);
            Assert.That(changeTypes.Contains(HistoryChangeType.ItemRemoved), Is.True);
            Assert.That(changeTypes.Contains(HistoryChangeType.HistoryReloaded), Is.True);
            Assert.That(changeTypes.Contains(HistoryChangeType.HistoryCleared), Is.True);
            Assert.That(changeTypes.Contains(HistoryChangeType.CollectionsSynchronized), Is.True);

            // Vérifier que les contextes sont informatifs
            Assert.That(events.All(e => !string.IsNullOrEmpty(e.Context)), Is.True);
        }
    }
}
