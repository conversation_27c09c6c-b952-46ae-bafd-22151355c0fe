using System;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.SupprimerTout
{
    /// <summary>
    /// Orchestrateur principal pour les opérations de suppression de tous les éléments.
    /// Coordonne tous les composants modulaires : validation, analyse, UI, et exécution.
    /// Remplace la méthode SupprimerTout originale avec une architecture modulaire et testable.
    /// </summary>
    public class SupprimerToutOrchestrator
    {
        private readonly ILoggingService? _loggingService;
        private readonly SupprimerToutValidator? _validator;
        private readonly SupprimerToutAnalyzer? _analyzer;
        private readonly SupprimerToutUIHandler? _uiHandler;
        private readonly SupprimerToutExecutor? _executor;

        /// <summary>
        /// Initialise une nouvelle instance de l'orchestrateur.
        /// </summary>
        /// <param name="loggingService">Service de logging pour tracer les opérations.</param>
        /// <param name="validator">Validateur pour vérifier les requêtes.</param>
        /// <param name="analyzer">Analyseur pour analyser les éléments à supprimer.</param>
        /// <param name="uiHandler">Gestionnaire UI pour les confirmations utilisateur.</param>
        /// <param name="executor">Exécuteur pour effectuer les suppressions.</param>
        public SupprimerToutOrchestrator(
            ILoggingService? loggingService = null,
            SupprimerToutValidator? validator = null,
            SupprimerToutAnalyzer? analyzer = null,
            SupprimerToutUIHandler? uiHandler = null,
            SupprimerToutExecutor? executor = null)
        {
            _loggingService = loggingService;
            _validator = validator;
            _analyzer = analyzer;
            _uiHandler = uiHandler;
            _executor = executor;
        }

        /// <summary>
        /// Exécute le workflow complet de suppression de tous les éléments.
        /// Coordonne la validation, l'analyse, la confirmation UI, et l'exécution.
        /// </summary>
        /// <param name="request">Requête de suppression contenant les paramètres.</param>
        /// <returns>Résultat de l'opération complète.</returns>
        public async Task<SupprimerToutResult> ExecuteAsync(SupprimerToutRequest request)
        {
            _loggingService?.LogInfo("[VERIFICATION_TEST_2025] SupprimerToutOrchestrator.ExecuteAsync APPELÉE");
            try
            {
                // CORRECTION: Vérification null de la requête
                if (request == null)
                {
                    var errorMsg = "Requête null - impossible de continuer";
                    _loggingService?.LogError($"[unknown] {errorMsg}");
                    return SupprimerToutResult.CreateFailure("unknown", errorMsg);
                }

                _loggingService?.LogInfo($"Début de l'orchestration pour l'opération {request.OperationId}");

                // CORRECTION: Vérification préalable des composants critiques
                if (!ValidateComponents())
                {
                    var errorMsg = "Composants de l'orchestrateur non disponibles";
                    _loggingService?.LogError($"[{request.OperationId}] {errorMsg}");
                    return SupprimerToutResult.CreateFailure(request.OperationId, errorMsg);
                }

                // Étape 1 : Validation de la requête
                var validationResult = await ValidateRequestAsync(request);
                if (!validationResult.IsValid)
                {
                    _loggingService?.LogWarning($"[{request.OperationId}] Validation échouée: {validationResult.ErrorMessage}");
                    return SupprimerToutResult.CreateFailure(request.OperationId, validationResult.ErrorMessage ?? "Validation failed");
                }

                _loggingService?.LogInfo($"[{request.OperationId}] Validation réussie");

                // Étape 2 : Analyse des éléments
                var analysis = await AnalyzeItemsAsync(request);
                if (analysis == null || !analysis.HasItemsToDelete)
                {
                    var message = "Aucun élément à supprimer selon l'analyse";
                    _loggingService?.LogInfo($"[{request.OperationId}] {message}");
                    return SupprimerToutResult.CreateFailure(request.OperationId, message);
                }

                _loggingService?.LogInfo($"[{request.OperationId}] Analyse réussie - {analysis.ItemsToDelete} élément(s) à supprimer");

                // Étape 3 : Confirmation utilisateur
                var userConfirmed = await ConfirmWithUserAsync(analysis, request.ViewModel, request.OperationId);
                if (!userConfirmed)
                {
                    var message = "Opération annulée par l'utilisateur";
                    _loggingService?.LogInfo($"[{request.OperationId}] {message}");
                    return SupprimerToutResult.CreateFailure(request.OperationId, message);
                }

                _loggingService?.LogInfo($"[{request.OperationId}] Confirmation utilisateur obtenue");

                // Étape 4 : Exécution de la suppression
                var executionResult = await ExecuteDeletionAsync(analysis, request.OperationId);
                if (!executionResult.Success)
                {
                    _loggingService?.LogError($"[{request.OperationId}] Exécution échouée: {executionResult.ErrorMessage}");
                    return executionResult;
                }

                _loggingService?.LogInfo($"[{request.OperationId}] Orchestration réussie - {executionResult.ItemsDeleted} élément(s) supprimé(s)");
                return executionResult;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{request?.OperationId}] Erreur lors de l'orchestration: {ex.Message}");
                return SupprimerToutResult.CreateFailure(request?.OperationId ?? "unknown", ex.Message, ex);
            }
        }

        /// <summary>
        /// Valide que tous les composants critiques sont disponibles.
        /// </summary>
        /// <returns>True si tous les composants sont disponibles, false sinon.</returns>
        private bool ValidateComponents()
        {
            var hasValidator = _validator != null;
            var hasAnalyzer = _analyzer != null;
            var hasExecutor = _executor != null;
            var hasUIHandler = _uiHandler != null;

            _loggingService?.LogInfo($"Validation des composants - Validator: {hasValidator}, Analyzer: {hasAnalyzer}, Executor: {hasExecutor}, UIHandler: {hasUIHandler}");

            return hasValidator && hasAnalyzer && hasExecutor && hasUIHandler;
        }

        /// <summary>
        /// Valide la requête de suppression.
        /// </summary>
        /// <param name="request">Requête à valider.</param>
        /// <returns>Résultat de la validation.</returns>
        private Task<SupprimerToutValidationResult> ValidateRequestAsync(SupprimerToutRequest? request)
        {
            try
            {
                if (request == null)
                {
                    _loggingService?.LogError("ERREUR CRITIQUE: Requête null");
                    return Task.FromResult(SupprimerToutValidationResult.Failure("Requête null"));
                }

                if (_validator == null)
                {
                    _loggingService?.LogError("ERREUR CRITIQUE: Validateur non disponible");
                    return Task.FromResult(SupprimerToutValidationResult.Failure("Validateur non disponible"));
                }

                var result = _validator.ValidateRequest(request);
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Erreur lors de la validation: {ex.Message}");
                return Task.FromResult(SupprimerToutValidationResult.Failure(ex.Message));
            }
        }

        /// <summary>
        /// Analyse les éléments à supprimer.
        /// </summary>
        /// <param name="request">Requête contenant les paramètres d'analyse.</param>
        /// <returns>Analyse des éléments.</returns>
        private Task<SupprimerToutAnalysis?> AnalyzeItemsAsync(SupprimerToutRequest request)
        {
            try
            {
                if (request == null)
                {
                    _loggingService?.LogError("ERREUR CRITIQUE: Requête null pour l'analyse");
                    return Task.FromResult<SupprimerToutAnalysis?>(null);
                }

                if (_analyzer == null)
                {
                    _loggingService?.LogWarning($"[{request.OperationId}] Analyseur non disponible");
                    return Task.FromResult<SupprimerToutAnalysis?>(null);
                }

                // Récupérer les éléments depuis le ViewModel
                _loggingService?.LogInfo($"[{request.OperationId}] Diagnostic ViewModel - ViewModel null: {request.ViewModel == null}");

                if (request.ViewModel != null)
                {
                    _loggingService?.LogInfo($"[{request.OperationId}] Diagnostic ViewModel - HistoryItems null: {request.ViewModel.HistoryItems == null}");
                    if (request.ViewModel.HistoryItems != null)
                    {
                        _loggingService?.LogInfo($"[{request.OperationId}] Diagnostic ViewModel - Nombre d'éléments: {request.ViewModel.HistoryItems.Count}");
                    }
                }

                var items = request.ViewModel?.HistoryItems?.ToList();
                if (items == null || !items.Any())
                {
                    _loggingService?.LogInfo($"[{request.OperationId}] Aucun élément trouvé dans le ViewModel");
                    return Task.FromResult<SupprimerToutAnalysis?>(new SupprimerToutAnalysis(0, 0, 0, false));
                }

                _loggingService?.LogInfo($"[{request.OperationId}] Début de l'analyse de {items.Count} élément(s)");
                var analysis = _analyzer.AnalyzeItems(items, request.PreservePinned, request.OperationId);
                _loggingService?.LogInfo($"[{request.OperationId}] Analyse terminée - {analysis.ItemsToDelete} élément(s) à supprimer");

                return Task.FromResult<SupprimerToutAnalysis?>(analysis);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{request.OperationId}] Erreur lors de l'analyse: {ex.Message}");
                return Task.FromResult<SupprimerToutAnalysis?>(null);
            }
        }

        /// <summary>
        /// Demande confirmation à l'utilisateur.
        /// </summary>
        /// <param name="analysis">Analyse des éléments à supprimer.</param>
        /// <param name="viewModel">ViewModel pour rechercher la fenêtre parente.</param>
        /// <param name="operationId">ID de l'opération.</param>
        /// <returns>True si l'utilisateur confirme, false sinon.</returns>
        private async Task<bool> ConfirmWithUserAsync(SupprimerToutAnalysis analysis, ClipboardHistoryViewModel? viewModel, string operationId)
        {
            try
            {
                if (_uiHandler == null)
                {
                    _loggingService?.LogWarning($"[{operationId}] Gestionnaire UI non disponible - confirmation automatique");
                    return true; // Auto-confirm if UI handler is not available
                }

                return await _uiHandler.ConfirmDeletionAsync(analysis, viewModel, operationId);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Erreur lors de la confirmation: {ex.Message}");
                return false; // Fail safe - don't confirm on error
            }
        }

        /// <summary>
        /// Exécute la suppression des éléments.
        /// </summary>
        /// <param name="analysis">Analyse des éléments à supprimer.</param>
        /// <param name="operationId">ID de l'opération.</param>
        /// <returns>Résultat de l'exécution.</returns>
        private async Task<SupprimerToutResult> ExecuteDeletionAsync(SupprimerToutAnalysis analysis, string operationId)
        {
            try
            {
                if (_executor == null)
                {
                    var errorMsg = "Exécuteur non disponible";
                    _loggingService?.LogError($"[{operationId}] {errorMsg}");
                    return SupprimerToutResult.CreateFailure(operationId, errorMsg);
                }

                return await _executor.ExecuteAsync(analysis, operationId);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Erreur lors de l'exécution: {ex.Message}");
                return SupprimerToutResult.CreateFailure(operationId, ex.Message, ex);
            }
        }



        /// <summary>
        /// Effectue un nettoyage d'urgence en cas d'échec critique.
        /// </summary>
        /// <param name="operationId">ID de l'opération.</param>
        /// <returns>Résultat du nettoyage d'urgence.</returns>
        public async Task<SupprimerToutResult> EmergencyCleanupAsync(string operationId)
        {
            try
            {
                _loggingService?.LogWarning($"[{operationId}] Début du nettoyage d'urgence par l'orchestrateur");

                if (_executor == null)
                {
                    return SupprimerToutResult.CreateFailure(operationId, "Exécuteur non disponible pour le nettoyage d'urgence");
                }

                var cleanupResult = await _executor.EmergencyCleanupAsync(operationId);

                if (cleanupResult.Success)
                {
                    _loggingService?.LogInfo($"[{operationId}] Nettoyage d'urgence réussi");
                    return SupprimerToutResult.CreateSuccess(operationId, 0); // Unknown count for emergency cleanup
                }
                else
                {
                    _loggingService?.LogError($"[{operationId}] Nettoyage d'urgence échoué");
                    return SupprimerToutResult.CreateFailure(operationId, cleanupResult.ErrorMessage ?? "Nettoyage d'urgence échoué");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Erreur lors du nettoyage d'urgence: {ex.Message}");
                return SupprimerToutResult.CreateFailure(operationId, ex.Message, ex);
            }
        }


    }
}
