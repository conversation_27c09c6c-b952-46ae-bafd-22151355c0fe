using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.LogDeletionResult.Implementations
{
    /// <summary>
    /// Implémentation de l'analyseur d'état des collections.
    /// Fournit des analyses détaillées sur l'état des collections de l'historique.
    /// </summary>
    public class CollectionStateAnalyzer : ICollectionStateAnalyzer
    {
        private readonly ILoggingService _loggingService;

        public CollectionStateAnalyzer(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Analyse l'état actuel des collections du ViewModel.
        /// </summary>
        public CollectionStateInfo AnalyzeCollectionState(ClipboardHistoryViewModel viewModel)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CollectionStateInfo();

            try
            {
                _loggingService.LogDebug("🔍 [ANALYZER] Début analyse état des collections");

                if (viewModel == null)
                {
                    result.AnalysisSuccessful = false;
                    result.ErrorMessage = "ViewModel est null";
                    return result;
                }

                // Analyser la collection du ViewModel
                AnalyzeViewModelCollection(viewModel, result);

                // Analyser la collection du gestionnaire d'historique
                AnalyzeManagerCollection(viewModel, result);

                // Vérifier la synchronisation
                result.CollectionsInSync = result.ViewModelItemCount == result.ManagerItemCount;

                // Obtenir un échantillon des premiers éléments
                result.FirstTenItems = GetCollectionSample(viewModel, 10);

                // Calculer les statistiques
                result.Statistics = CalculateStatistics(viewModel);

                // Détecter les anomalies
                result.Anomalies = DetectAnomalies(viewModel);

                // Compter les éléments null et doublons
                result.NullItemCount = CountNullItems(viewModel);
                result.DuplicateCount = CountDuplicates(viewModel);

                result.AnalysisSuccessful = true;
                _loggingService.LogDebug($"✅ [ANALYZER] Analyse terminée - Items VM: {result.ViewModelItemCount}, Manager: {result.ManagerItemCount}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [ANALYZER] Erreur lors de l'analyse: {ex.Message}", ex);
                result.AnalysisSuccessful = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.AnalysisDuration = stopwatch.Elapsed;
                _loggingService.LogDebug($"⏱️ [ANALYZER] Analyse durée: {stopwatch.ElapsedMilliseconds}ms");
            }

            return result;
        }

        /// <summary>
        /// Compare deux collections d'éléments.
        /// </summary>
        public CollectionComparisonResult CompareCollections(
            IEnumerable<ClipboardItem> collection1,
            IEnumerable<ClipboardItem> collection2)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CollectionComparisonResult();

            try
            {
                _loggingService.LogDebug("🔍 [ANALYZER] Début comparaison collections");

                var list1 = collection1?.ToList() ?? new List<ClipboardItem>();
                var list2 = collection2?.ToList() ?? new List<ClipboardItem>();

                result.Collection1Count = list1.Count;
                result.Collection2Count = list2.Count;

                // Éléments communs
                result.CommonItems = list1.Intersect(list2).ToList();

                // Éléments uniquement dans la première collection
                result.OnlyInCollection1 = list1.Except(list2).ToList();

                // Éléments uniquement dans la deuxième collection
                result.OnlyInCollection2 = list2.Except(list1).ToList();

                // Les collections sont identiques si elles ont la même taille et aucune différence
                result.AreIdentical = result.Collection1Count == result.Collection2Count &&
                                     result.OnlyInCollection1.Count == 0 &&
                                     result.OnlyInCollection2.Count == 0;

                // Analyser les différences détaillées
                result.Differences = AnalyzeDifferences(list1, list2);

                _loggingService.LogDebug($"✅ [ANALYZER] Comparaison terminée - Identiques: {result.AreIdentical}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [ANALYZER] Erreur lors de la comparaison: {ex.Message}", ex);
            }
            finally
            {
                stopwatch.Stop();
                result.ComparisonDuration = stopwatch.Elapsed;
                _loggingService.LogDebug($"⏱️ [ANALYZER] Comparaison durée: {stopwatch.ElapsedMilliseconds}ms");
            }

            return result;
        }

        /// <summary>
        /// Recherche un élément dans les collections.
        /// </summary>
        public ItemSearchResult FindItemInCollections(long itemId, ClipboardHistoryViewModel viewModel)
        {
            var result = new ItemSearchResult { SearchedItemId = itemId };

            try
            {
                _loggingService.LogDebug($"[ANALYZER] Recherche élément ID: {itemId}");

                if (viewModel == null)
                {
                    return result;
                }

                // Rechercher dans la collection du ViewModel
                result.FoundInViewModel = viewModel.HistoryItems?.FirstOrDefault(i => i?.Id == itemId);
                result.FoundById = result.FoundInViewModel != null;

                // Rechercher dans le gestionnaire d'historique
                var historyManager = GetHistoryManager(viewModel);
                if (historyManager != null)
                {
                    result.FoundInManager = historyManager.HistoryItems?.FirstOrDefault(i => i?.Id == itemId);
                }

                // Vérifier si trouvé par référence
                if (result.FoundInViewModel != null && result.FoundInManager != null)
                {
                    result.FoundByReference = ReferenceEquals(result.FoundInViewModel, result.FoundInManager);
                    result.InstancesMatch = result.FoundByReference;
                }

                result.Found = result.FoundInViewModel != null || result.FoundInManager != null;

                // Déterminer les positions
                if (result.FoundInViewModel != null && viewModel.HistoryItems != null)
                {
                    var index = viewModel.HistoryItems.ToList().IndexOf(result.FoundInViewModel);
                    result.Positions.Add(new ItemPosition
                    {
                        CollectionName = "ViewModel",
                        Index = index,
                        IsValid = index >= 0
                    });
                }

                _loggingService.LogDebug($"✅ [ANALYZER] Recherche terminée - Trouvé: {result.Found}");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [ANALYZER] Erreur lors de la recherche: {ex.Message}", ex);
            }

            return result;
        }

        /// <summary>
        /// Analyse la cohérence entre les composants.
        /// </summary>
        public ConsistencyAnalysisResult AnalyzeConsistency(ClipboardHistoryViewModel viewModel)
        {
            var result = new ConsistencyAnalysisResult();

            try
            {
                _loggingService.LogDebug("🔍 [ANALYZER] Début analyse cohérence");

                if (viewModel == null)
                {
                    result.Issues.Add(new ConsistencyIssue
                    {
                        Type = ConsistencyIssueType.AccessibilityProblem,
                        Description = "ViewModel est null",
                        Impact = ConsistencyImpact.Critical
                    });
                    result.IsConsistent = false;
                    result.ConsistencyScore = 0;
                    return result;
                }

                var issues = new List<ConsistencyIssue>();

                // Vérifier l'accessibilité du gestionnaire
                var historyManager = GetHistoryManager(viewModel);
                if (historyManager == null)
                {
                    issues.Add(new ConsistencyIssue
                    {
                        Type = ConsistencyIssueType.AccessibilityProblem,
                        Description = "Gestionnaire d'historique inaccessible",
                        Impact = ConsistencyImpact.High,
                        AffectedComponents = { "HistoryManager" }
                    });
                }

                // Vérifier la synchronisation des collections
                if (historyManager != null)
                {
                    var vmCount = viewModel.HistoryItems?.Count ?? 0;
                    var managerCount = historyManager.HistoryItems?.Count ?? 0;

                    if (vmCount != managerCount)
                    {
                        issues.Add(new ConsistencyIssue
                        {
                            Type = ConsistencyIssueType.CountMismatch,
                            Description = $"Nombre d'éléments différent: ViewModel={vmCount}, Manager={managerCount}",
                            Impact = ConsistencyImpact.Medium,
                            AffectedComponents = { "ViewModel", "HistoryManager" }
                        });
                    }
                }

                // Vérifier l'état du ViewModel
                if (viewModel.HistoryItems == null)
                {
                    issues.Add(new ConsistencyIssue
                    {
                        Type = ConsistencyIssueType.StateInconsistency,
                        Description = "Collection HistoryItems du ViewModel est null",
                        Impact = ConsistencyImpact.High,
                        AffectedComponents = { "ViewModel" }
                    });
                }

                result.Issues = issues;
                result.IsConsistent = issues.Count == 0;
                result.ConsistencyScore = CalculateConsistencyScore(issues);

                // Générer des recommandations
                result.Recommendations = GenerateRecommendations(issues);

                _loggingService.LogDebug($"✅ [ANALYZER] Analyse cohérence terminée - Score: {result.ConsistencyScore:F1}%");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"❌ [ANALYZER] Erreur lors de l'analyse de cohérence: {ex.Message}", ex);
                result.IsConsistent = false;
                result.ConsistencyScore = 0;
            }

            return result;
        }

        /// <summary>
        /// Obtient un échantillon des premiers éléments.
        /// </summary>
        public List<ClipboardItem> GetCollectionSample(ClipboardHistoryViewModel viewModel, int count = 10)
        {
            try
            {
                if (viewModel?.HistoryItems == null) return new List<ClipboardItem>();

                return viewModel.HistoryItems
                    .Where(i => i != null)
                    .Take(count)
                    .ToList()!;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [ANALYZER] Erreur lors de l'obtention de l'échantillon: {ex.Message}");
                return new List<ClipboardItem>();
            }
        }

        /// <summary>
        /// Calcule des statistiques sur la collection.
        /// </summary>
        public CollectionStatistics CalculateStatistics(ClipboardHistoryViewModel viewModel)
        {
            var stats = new CollectionStatistics();

            try
            {
                if (viewModel?.HistoryItems == null) return stats;

                var validItems = viewModel.HistoryItems.Where(i => i != null).ToList();
                stats.TotalItems = validItems.Count;
                stats.PinnedItems = validItems.Count(i => i!.IsPinned);
                stats.ItemsWithCustomName = validItems.Count(i => !string.IsNullOrEmpty(i!.CustomName));
                stats.ItemsWithoutPreview = validItems.Count(i => string.IsNullOrEmpty(i!.TextPreview));

                // Répartition par type
                stats.TypeDistribution = validItems
                    .GroupBy(i => i!.DataType)
                    .ToDictionary(g => g.Key, g => g.Count());

                // Statistiques de taille
                var sizes = validItems.Where(i => i!.RawData != null).Select(i => (long)i!.RawData!.Length).ToList();
                if (sizes.Count > 0)
                {
                    stats.AverageItemSize = sizes.Average();
                    stats.TotalSize = sizes.Sum();
                }

                // Statistiques temporelles
                var timestamps = validItems.Select(i => i!.Timestamp).ToList();
                if (timestamps.Count > 0)
                {
                    stats.OldestItemTimestamp = timestamps.Min();
                    stats.NewestItemTimestamp = timestamps.Max();
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [ANALYZER] Erreur lors du calcul des statistiques: {ex.Message}");
            }

            return stats;
        }

        /// <summary>
        /// Détecte les anomalies dans la collection.
        /// </summary>
        public List<CollectionAnomaly> DetectAnomalies(ClipboardHistoryViewModel viewModel)
        {
            var anomalies = new List<CollectionAnomaly>();

            try
            {
                if (viewModel?.HistoryItems == null) return anomalies;

                // Détecter les éléments null
                var nullCount = viewModel.HistoryItems.Count(i => i == null);
                if (nullCount > 0)
                {
                    anomalies.Add(new CollectionAnomaly
                    {
                        Type = AnomalyType.NullItem,
                        Description = $"{nullCount} éléments null détectés",
                        Count = nullCount
                    });
                }

                // Détecter les doublons d'ID
                var validItems = viewModel.HistoryItems.Where(i => i != null).ToList();
                var duplicateIds = validItems
                    .GroupBy(i => i!.Id)
                    .Where(g => g.Count() > 1)
                    .ToList();

                foreach (var group in duplicateIds)
                {
                    anomalies.Add(new CollectionAnomaly
                    {
                        Type = AnomalyType.DuplicateId,
                        Description = $"ID dupliqué: {group.Key}",
                        Count = group.Count(),
                        AffectedItems = group.ToList()!
                    });
                }

                // Détecter les données corrompues
                var corruptedItems = validItems.Where(i => IsItemCorrupted(i!)).ToList();
                if (corruptedItems.Count > 0)
                {
                    anomalies.Add(new CollectionAnomaly
                    {
                        Type = AnomalyType.CorruptedItem,
                        Description = $"{corruptedItems.Count} éléments corrompus détectés",
                        Count = corruptedItems.Count,
                        AffectedItems = corruptedItems!
                    });
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [ANALYZER] Erreur lors de la détection d'anomalies: {ex.Message}");
            }

            return anomalies;
        }

        #region Méthodes privées

        private void AnalyzeViewModelCollection(ClipboardHistoryViewModel viewModel, CollectionStateInfo result)
        {
            try
            {
                result.ViewModelItemCount = viewModel.HistoryItems?.Count ?? 0;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [ANALYZER] Erreur analyse collection ViewModel: {ex.Message}");
                result.ViewModelItemCount = 0;
            }
        }

        private void AnalyzeManagerCollection(ClipboardHistoryViewModel viewModel, CollectionStateInfo result)
        {
            try
            {
                var historyManager = GetHistoryManager(viewModel);
                result.ManagerItemCount = historyManager?.HistoryItems?.Count ?? 0;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [ANALYZER] Erreur analyse collection Manager: {ex.Message}");
                result.ManagerItemCount = 0;
            }
        }

        private ClipboardHistoryManager? GetHistoryManager(ClipboardHistoryViewModel viewModel)
        {
            try
            {
                var field = viewModel.GetType().GetField("_historyManager",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                return field?.GetValue(viewModel) as ClipboardHistoryManager;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [ANALYZER] Erreur accès gestionnaire d'historique: {ex.Message}");
                return null;
            }
        }

        private int CountNullItems(ClipboardHistoryViewModel viewModel)
        {
            try
            {
                return viewModel.HistoryItems?.Count(i => i == null) ?? 0;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [ANALYZER] Erreur comptage éléments null: {ex.Message}");
                return 0;
            }
        }

        private int CountDuplicates(ClipboardHistoryViewModel viewModel)
        {
            try
            {
                if (viewModel.HistoryItems == null) return 0;

                var validItems = viewModel.HistoryItems.Where(i => i != null).ToList();
                var uniqueIds = validItems.Select(i => i!.Id).Distinct().Count();
                return validItems.Count - uniqueIds;
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [ANALYZER] Erreur comptage doublons: {ex.Message}");
                return 0;
            }
        }

        private List<CollectionDifference> AnalyzeDifferences(List<ClipboardItem> list1, List<ClipboardItem> list2)
        {
            var differences = new List<CollectionDifference>();

            try
            {
                // Analyser les éléments ajoutés
                foreach (var item in list2.Except(list1))
                {
                    differences.Add(new CollectionDifference
                    {
                        Type = DifferenceType.ItemAdded,
                        Description = $"Élément ajouté: ID {item?.Id}",
                        AffectedItem = item,
                        PositionInCollection2 = item != null ? list2.IndexOf(item) : -1
                    });
                }

                // Analyser les éléments supprimés
                foreach (var item in list1.Except(list2))
                {
                    differences.Add(new CollectionDifference
                    {
                        Type = DifferenceType.ItemRemoved,
                        Description = $"Élément supprimé: ID {item?.Id}",
                        AffectedItem = item,
                        PositionInCollection1 = item != null ? list1.IndexOf(item) : -1
                    });
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogWarning($"⚠️ [ANALYZER] Erreur analyse différences: {ex.Message}");
            }

            return differences;
        }

        private double CalculateConsistencyScore(List<ConsistencyIssue> issues)
        {
            if (issues.Count == 0) return 100.0;

            var totalPenalty = issues.Sum(issue => issue.Impact switch
            {
                ConsistencyImpact.Low => 5,
                ConsistencyImpact.Medium => 15,
                ConsistencyImpact.High => 30,
                ConsistencyImpact.Critical => 50,
                _ => 10
            });

            return Math.Max(0, 100 - totalPenalty);
        }

        private List<string> GenerateRecommendations(List<ConsistencyIssue> issues)
        {
            var recommendations = new List<string>();

            foreach (var issue in issues)
            {
                switch (issue.Type)
                {
                    case ConsistencyIssueType.CountMismatch:
                        recommendations.Add("Synchroniser les collections ViewModel et HistoryManager");
                        break;
                    case ConsistencyIssueType.AccessibilityProblem:
                        recommendations.Add("Vérifier l'initialisation du gestionnaire d'historique");
                        break;
                    case ConsistencyIssueType.StateInconsistency:
                        recommendations.Add("Réinitialiser l'état du ViewModel");
                        break;
                }
            }

            return recommendations.Distinct().ToList();
        }

        private bool IsItemCorrupted(ClipboardItem item)
        {
            try
            {
                // Vérifications de base pour détecter la corruption
                if (item.Id <= 0) return true;
                if (item.RawData == null && string.IsNullOrEmpty(item.TextPreview)) return true;
                if (item.Timestamp == default) return true;

                return false;
            }
            catch
            {
                return true;
            }
        }

        #endregion
    }
}
