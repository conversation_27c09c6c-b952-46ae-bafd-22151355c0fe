root = true

[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# C# files
[*.cs]
indent_style = space
indent_size = 4

# XML project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_style = space
indent_size = 2

# XML config files
[*.{xml,props,targets,ruleset,config,nuspec,resx,vsixmanifest,vsct}]
indent_style = space
indent_size = 2

# XAML files
[*.xaml]
indent_style = space
indent_size = 4

# JSON files
[*.json]
indent_style = space
indent_size = 2

# Shell script files
[*.{bat,cmd,ps1,sh}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# C# naming conventions
[*.cs]
# Interfaces should start with I
dotnet_naming_rule.interface_should_begin_with_i.severity = suggestion
dotnet_naming_rule.interface_should_begin_with_i.symbols = interface
dotnet_naming_rule.interface_should_begin_with_i.style = begins_with_i
dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal
dotnet_naming_style.begins_with_i.required_prefix = I
dotnet_naming_style.begins_with_i.capitalization = pascal_case

# Pascal case for public and protected members
dotnet_naming_rule.public_members_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.public_members_should_be_pascal_case.symbols = public_and_protected_members
dotnet_naming_rule.public_members_should_be_pascal_case.style = pascal_case_style
dotnet_naming_symbols.public_and_protected_members.applicable_kinds = property,method,field,event,delegate
dotnet_naming_symbols.public_and_protected_members.applicable_accessibilities = public,protected
dotnet_naming_style.pascal_case_style.capitalization = pascal_case

# Camel case for parameters and local variables
dotnet_naming_rule.local_variables_should_be_camel_case.severity = suggestion
dotnet_naming_rule.local_variables_should_be_camel_case.symbols = local_variables
dotnet_naming_rule.local_variables_should_be_camel_case.style = camel_case_style
dotnet_naming_symbols.local_variables.applicable_kinds = parameter,local
dotnet_naming_style.camel_case_style.capitalization = camel_case 