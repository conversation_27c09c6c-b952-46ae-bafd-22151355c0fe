﻿using System;
using System.Reflection;
using System.Threading;
using System.Windows.Threading;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit
{
    /// <summary>
    /// Classe pour nettoyer les Ã©tats statiques entre les tests et gérer l'état WPF
    /// </summary>
    [TestFixture]
    public static class GlobalTestCleanup
    {
        /// <summary>
        /// Nettoie tous les Ã©tats statiques aprÃ¨s chaque test
        /// </summary>
        [OneTimeTearDown]
        public static void CleanupStaticState()
        {
            try
            {
                // 1. RÃ©initialiser KeyboardSimulator
                ResetKeyboardSimulatorState();

                // 2. RÃ©initialiser ClipboardItemLogger
                ResetClipboardItemLoggerState();

                // 3. Nettoyer l'état WPF
                CleanupWpfState();

                // 4. Forcer le garbage collection
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du nettoyage global: {ex.Message}");
            }
        }
        
        private static void ResetKeyboardSimulatorState()
        {
            try
            {
                var type = Type.GetType("ClipboardPlus.Utils.KeyboardSimulator, ClipboardPlus");
                if (type != null)
                {
                    var lastSimulationTimeField = type.GetField("_lastSimulationTime", BindingFlags.NonPublic | BindingFlags.Static);
                    lastSimulationTimeField?.SetValue(null, DateTime.MinValue);
                    
                    var isSimulationInProgressField = type.GetField("_isSimulationInProgress", BindingFlags.NonPublic | BindingFlags.Static);
                    isSimulationInProgressField?.SetValue(null, false);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la rÃ©initialisation de KeyboardSimulator: {ex.Message}");
            }
        }
        
        private static void ResetClipboardItemLoggerState()
        {
            try
            {
                var type = Type.GetType("ClipboardPlus.UI.Controls.ClipboardItemLogger, ClipboardPlus");
                if (type != null)
                {
                    var initializedField = type.GetField("_initialized", BindingFlags.NonPublic | BindingFlags.Static);
                    initializedField?.SetValue(null, false);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la rÃ©initialisation de ClipboardItemLogger: {ex.Message}");
            }
        }

        private static void CleanupWpfState()
        {
            try
            {
                // Nettoyer les dispatchers WPF qui peuvent rester actifs
                var currentDispatcher = Dispatcher.CurrentDispatcher;
                if (currentDispatcher != null)
                {
                    // Forcer le traitement de tous les messages en attente
                    currentDispatcher.Invoke(() => { }, DispatcherPriority.Background);

                    // Nettoyer les timers et les tâches en attente
                    currentDispatcher.BeginInvokeShutdown(DispatcherPriority.Background);
                }

                // Réinitialiser l'état du thread
                if (Thread.CurrentThread.GetApartmentState() == ApartmentState.STA)
                {
                    // Forcer le nettoyage des ressources WPF
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du nettoyage WPF: {ex.Message}");
            }
        }
    }
}
