using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.Services
{
    public class StartupLogic : IStartupLogic
    {
        private readonly IServiceProvider _services;
        private ILoggingService? _logger;
        private readonly ISingleInstanceService _singleInstanceService;
        private readonly ICommandLineParser _commandLineParser;
        private readonly IStartupOrchestrator _startupOrchestrator;

        public StartupLogic(IServiceProvider services)
        {
            try
            {
                _services = services;
                _logger = services.GetRequiredService<ILoggingService>();
                _singleInstanceService = services.GetRequiredService<ISingleInstanceService>();
                _commandLineParser = services.GetRequiredService<ICommandLineParser>();
                _startupOrchestrator = services.GetRequiredService<IStartupOrchestrator>();
                _logger?.LogInfo("StartupLogic: Constructeur terminé sans exception.");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[StartupLogic][CTOR][EXCEPTION] {ex}");
                try { _logger?.LogCritical($"[StartupLogic][CTOR][EXCEPTION] {ex.Message}", ex); } catch { }
                throw;
            }
        }

        public async Task<(StartupStatus Status, ISystemTrayService? TrayService)> ExecuteAsync(string[] commandLineArgs, EventHandler shortcutActivatedHandler, EventHandler clipboardChangedHandler)
        {
            if (!HandleSingleInstanceCheck())
                return (StartupStatus.ShutdownRequested, null);
            if (!ProcessCommandLineArguments(commandLineArgs))
                return (StartupStatus.ShutdownRequested, null);
            var trayService = await OrchestrateApplicationStart(shortcutActivatedHandler, clipboardChangedHandler);
            if (trayService == null)
                return (StartupStatus.CriticalError, null);
            FinalizeStartup(trayService);
            return (StartupStatus.Success, trayService);
        }

        private bool HandleSingleInstanceCheck()
        {
            _logger?.LogInfo("StartupLogic: Vérification de l'instance unique...");
            if (_singleInstanceService != null)
            {
                _logger?.LogInfo($"StartupLogic: ISingleInstanceService résolu. IsFirstInstance = {_singleInstanceService.IsFirstInstance}");
                if (!_singleInstanceService.IsFirstInstance)
                {
                    _logger?.LogInfo("StartupLogic: Une autre instance est déjà en cours d'exécution. Notification et arrêt.");
                    _singleInstanceService.NotifyExistingInstance();
                    return false;
                }
            }
            else
            {
                _logger?.LogWarning("StartupLogic: ISingleInstanceService est NULL.");
            }
            return true;
        }

        private bool ProcessCommandLineArguments(string[] args)
        {
            _logger?.LogInfo("StartupLogic: Traitement des arguments de ligne de commande...");
            if (_commandLineParser != null)
            {
                _logger?.LogInfo("StartupLogic: ICommandLineParser résolu. Traitement des arguments.");
                if (!_commandLineParser.ProcessCommandLineArguments(args, _services))
                {
                    _logger?.LogWarning("StartupLogic: Arrêt demandé par les arguments de ligne de commande.");
                    return false;
                }
            }
            else
            {
                _logger?.LogWarning("StartupLogic: ICommandLineParser est NULL.");
            }
            return true;
        }

        private async Task<ISystemTrayService?> OrchestrateApplicationStart(EventHandler shortcutActivatedHandler, EventHandler clipboardChangedHandler)
        {
            _logger?.LogInfo("StartupLogic: Orchestration du démarrage de l'application...");
            if (_startupOrchestrator == null)
            {
                _logger?.LogCritical("StartupLogic: Le service IStartupOrchestrator n'a pas pu être résolu. Arrêt.");
                return null;
            }

            // Résoudre ApplicationLifetimeManager pour obtenir les gestionnaires d'événements
            _logger?.LogInfo("StartupLogic: Résolution de IApplicationLifetimeManager pour les gestionnaires d'événements...");
            var lifetimeManager = _services.GetService<IApplicationLifetimeManager>();
            if (lifetimeManager == null)
            {
                _logger?.LogCritical("StartupLogic: IApplicationLifetimeManager n'a pas pu être résolu. Arrêt.");
                return null;
            }

            _logger?.LogInfo("StartupLogic: Appel de _startupOrchestrator.StartApplication avec gestionnaires d'événements...");
            var trayService = await _startupOrchestrator.StartApplication(
                _services,
                shortcutActivatedHandler,
                clipboardChangedHandler);
            if (trayService == null)
            {
                _logger?.LogCritical("StartupLogic: _startupOrchestrator.StartApplication a renvoyé NULL. Arrêt.");
                return null;
            }
            _logger?.LogInfo("StartupLogic: _startupOrchestrator.StartApplication a réussi.");
            return trayService;
        }

        private void FinalizeStartup(ISystemTrayService trayService)
        {
            _logger?.LogInfo("StartupLogic: Enregistrement du gestionnaire de message inter-instances.");
            _singleInstanceService?.RegisterWindowMessageHandler(null);
            _logger?.LogInfo("StartupLogic: Création de la fenêtre principale cachée.");
            // Note: La fenêtre principale est créée à la demande via le tray service
            _logger?.LogInfo("StartupLogic: Fenêtre principale cachée affichée.");
        }
    }
} 