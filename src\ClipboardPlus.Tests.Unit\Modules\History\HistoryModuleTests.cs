using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Modules.Core.Events;
using Prism.Events;
using ClipboardPlus.Modules.History;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Modules.History
{
    /// <summary>
    /// Tests unitaires pour le HistoryModule.
    /// 
    /// Ces tests valident le comportement du module d'historique,
    /// incluant la gestion des collections, le filtrage et la synchronisation.
    /// </summary>
    [TestFixture]
    public class HistoryModuleTests
    {
        private Mock<IClipboardHistoryManager> _mockHistoryManager;
        private Mock<ILoggingService> _mockLoggingService;
        private Mock<IEventAggregator> _mockEventAggregator;
        private HistoryModule _historyModule;
        private List<ClipboardItem> _testItems;

        [SetUp]
        public void SetUp()
        {
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockEventAggregator = new Mock<IEventAggregator>();

            // Configuration du mock EventAggregator pour retourner des mocks d'événements
            var mockModuleStateEvent = new Mock<ModuleStateChangedPrismEvent>();
            var mockHistoryEvent = new Mock<HistoryModulePrismEvent>();
            _mockEventAggregator.Setup(ea => ea.GetEvent<ModuleStateChangedPrismEvent>())
                               .Returns(mockModuleStateEvent.Object);
            _mockEventAggregator.Setup(ea => ea.GetEvent<HistoryModulePrismEvent>())
                               .Returns(mockHistoryEvent.Object);

            _historyModule = new HistoryModule(
                _mockHistoryManager.Object,
                _mockLoggingService.Object,
                _mockEventAggregator.Object);

            // Créer des éléments de test
            _testItems = new List<ClipboardItem>
            {
                new ClipboardItem
                {
                    Id = 1,
                    DataType = ClipboardDataType.Text,
                    Timestamp = DateTime.Now.AddMinutes(-10),
                    RawData = System.Text.Encoding.UTF8.GetBytes("Premier élément"),
                    TextPreview = "Premier élément",
                    CustomName = "Premier"
                },
                new ClipboardItem
                {
                    Id = 2,
                    DataType = ClipboardDataType.Text,
                    Timestamp = DateTime.Now.AddMinutes(-5),
                    RawData = System.Text.Encoding.UTF8.GetBytes("Deuxième élément"),
                    TextPreview = "Deuxième élément",
                    CustomName = "Deuxième"
                },
                new ClipboardItem
                {
                    Id = 3,
                    DataType = ClipboardDataType.Text,
                    Timestamp = DateTime.Now,
                    RawData = System.Text.Encoding.UTF8.GetBytes("Troisième élément"),
                    TextPreview = "Troisième élément",
                    CustomName = "Troisième"
                }
            };

            // Configurer le mock du gestionnaire d'historique
            _mockHistoryManager.Setup(m => m.HistoryItems)
                .Returns(_testItems);
        }

        [TearDown]
        public void TearDown()
        {
            _historyModule?.Dispose();
        }

        #region Tests d'initialisation

        [Test]
        public async Task InitializeAsync_ShouldInitializeSuccessfully()
        {
            // Act
            await _historyModule.InitializeAsync();

            // Assert
            Assert.That(_historyModule.State, Is.EqualTo(ClipboardPlus.Modules.Core.ModuleState.Initialized));
            _mockLoggingService.Verify(l => l.LogInfo(It.IsAny<string>()), Times.AtLeastOnce);
        }

        [Test]
        public async Task StartAsync_ShouldStartSuccessfully()
        {
            // Arrange
            await _historyModule.InitializeAsync();

            // Act
            await _historyModule.StartAsync();

            // Assert
            Assert.That(_historyModule.State, Is.EqualTo(ClipboardPlus.Modules.Core.ModuleState.Running));
            _mockEventAggregator.Verify(e => e.GetEvent<ModuleStateChangedPrismEvent>().Publish(It.IsAny<ModuleStateChangedEvent>()), Times.AtLeastOnce);
        }

        #endregion

        #region Tests de chargement d'historique

        [Test]
        public async Task LoadHistoryAsync_ShouldLoadItemsFromManager()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();

            // Act
            await _historyModule.LoadHistoryAsync("Test");

            // Assert
            Assert.That(_historyModule.HistoryItems.Count, Is.EqualTo(3));
            Assert.That(_historyModule.FilteredItems.Count, Is.EqualTo(3));
            Assert.That(_historyModule.TotalItemCount, Is.EqualTo(3));
            Assert.That(_historyModule.FilteredItemCount, Is.EqualTo(3));
        }

        [Test]
        public async Task LoadHistoryAsync_ShouldTriggerHistoryChangedEvent()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            
            var eventTriggered = false;
            _historyModule.HistoryChanged += (sender, args) =>
            {
                eventTriggered = true;
                Assert.That(args.ChangeType, Is.EqualTo(HistoryChangeType.HistoryReloaded));
                Assert.That(args.Context, Is.EqualTo("Test"));
            };

            // Act
            await _historyModule.LoadHistoryAsync("Test");

            // Assert
            Assert.That(eventTriggered, Is.True);
        }

        #endregion

        #region Tests de filtrage

        [Test]
        public async Task ApplyFilter_ShouldFilterItemsCorrectly()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");

            // Act
            _historyModule.ApplyFilter("Premier");

            // Assert
            Assert.That(_historyModule.FilteredItems.Count, Is.EqualTo(1));
            Assert.That(_historyModule.FilteredItems[0].TextPreview, Is.EqualTo("Premier élément"));
            Assert.That(_historyModule.SearchFilter, Is.EqualTo("Premier"));
        }

        [Test]
        public async Task ApplyFilter_ShouldTriggerFilterChangedEvent()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");
            
            var eventTriggered = false;
            _historyModule.FilterChanged += (sender, args) =>
            {
                eventTriggered = true;
                Assert.That(args.CurrentFilter, Is.EqualTo("Premier"));
                Assert.That(args.MatchingItemCount, Is.EqualTo(1));
            };

            // Act
            _historyModule.ApplyFilter("Premier");

            // Assert
            Assert.That(eventTriggered, Is.True);
        }

        [Test]
        public async Task ClearFilter_ShouldShowAllItems()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");
            _historyModule.ApplyFilter("Premier");

            // Act
            _historyModule.ClearFilter();

            // Assert
            Assert.That(_historyModule.FilteredItems.Count, Is.EqualTo(3));
            Assert.That(_historyModule.SearchFilter, Is.Null);
        }

        #endregion

        #region Tests de sélection

        [Test]
        public async Task SelectItem_ShouldUpdateSelectedItem()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");

            // Act
            _historyModule.SelectItem(_testItems[1]);

            // Assert
            Assert.That(_historyModule.SelectedItem, Is.EqualTo(_testItems[1]));
        }

        [Test]
        public async Task SelectItem_ShouldTriggerSelectionChangedEvent()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");
            
            var eventTriggered = false;
            _historyModule.SelectionChanged += (sender, args) =>
            {
                eventTriggered = true;
                Assert.That(args.CurrentItem, Is.EqualTo(_testItems[1]));
                Assert.That(args.PreviousItem, Is.Null);
            };

            // Act
            _historyModule.SelectItem(_testItems[1]);

            // Assert
            Assert.That(eventTriggered, Is.True);
        }

        [Test]
        public async Task SelectNextItem_ShouldSelectCorrectItem()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");
            _historyModule.SelectItem(_testItems[0]);

            // Act
            _historyModule.SelectNextItem();

            // Assert
            Assert.That(_historyModule.SelectedItem, Is.EqualTo(_testItems[1]));
        }

        [Test]
        public async Task SelectPreviousItem_ShouldSelectCorrectItem()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");
            _historyModule.SelectItem(_testItems[1]);

            // Act
            _historyModule.SelectPreviousItem();

            // Assert
            Assert.That(_historyModule.SelectedItem, Is.EqualTo(_testItems[0]));
        }

        #endregion

        #region Tests d'ajout et suppression

        [Test]
        public async Task AddItemAsync_ShouldAddItemToCollection()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");
            
            var newItem = new ClipboardItem
            {
                Id = 4,
                DataType = ClipboardDataType.Text,
                Timestamp = DateTime.Now,
                RawData = System.Text.Encoding.UTF8.GetBytes("Nouvel élément"),
                TextPreview = "Nouvel élément"
            };

            // Act
            await _historyModule.AddItemAsync(newItem);

            // Assert
            Assert.That(_historyModule.HistoryItems.Count, Is.EqualTo(4));
            Assert.That(_historyModule.HistoryItems[0], Is.EqualTo(newItem)); // Ajouté en tête
            _mockHistoryManager.Verify(m => m.AddItemAsync(newItem), Times.Once);
        }

        [Test]
        public async Task RemoveItemAsync_ShouldRemoveItemFromCollection()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");
            var itemToRemove = _testItems[1];

            // Act
            await _historyModule.RemoveItemAsync(itemToRemove);

            // Assert
            Assert.That(_historyModule.HistoryItems.Count, Is.EqualTo(2));
            Assert.That(_historyModule.HistoryItems.Contains(itemToRemove), Is.False);
            _mockHistoryManager.Verify(m => m.DeleteItemAsync(itemToRemove.Id), Times.Once);
        }

        #endregion

        #region Tests de recherche

        [Test]
        public async Task FindItemById_ShouldReturnCorrectItem()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");

            // Act
            var foundItem = _historyModule.FindItemById(2);

            // Assert
            Assert.That(foundItem, Is.Not.Null);
            Assert.That(foundItem.Id, Is.EqualTo(2));
            Assert.That(foundItem.TextPreview, Is.EqualTo("Deuxième élément"));
        }

        [Test]
        public async Task FindItems_ShouldReturnMatchingItems()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");

            // Act
            var foundItems = _historyModule.FindItems(item => item.TextPreview.Contains("élément"));

            // Assert
            Assert.That(foundItems.Count(), Is.EqualTo(3));
        }

        #endregion

        #region Tests de statistiques

        [Test]
        public async Task GetStatistics_ShouldReturnCorrectStatistics()
        {
            // Arrange
            await _historyModule.InitializeAsync();
            await _historyModule.StartAsync();
            await _historyModule.LoadHistoryAsync("Test");
            _historyModule.ApplyFilter("Premier");

            // Act
            var stats = _historyModule.GetStatistics();

            // Assert
            Assert.That(stats.TotalItems, Is.EqualTo(3));
            Assert.That(stats.FilteredItems, Is.EqualTo(1));
            Assert.That(stats.CurrentFilter, Is.EqualTo("Premier"));
            Assert.That(stats.TotalSizeBytes, Is.GreaterThan(0));
        }

        #endregion

        #region Tests d'erreurs

        [Test]
        public void Constructor_WithNullHistoryManager_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new HistoryModule(null, _mockLoggingService.Object, _mockEventAggregator.Object));
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new HistoryModule(_mockHistoryManager.Object, null, _mockEventAggregator.Object));
        }

        [Test]
        public void Constructor_WithNullEventBus_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new HistoryModule(_mockHistoryManager.Object, _mockLoggingService.Object, null));
        }

        #endregion
    }
}
