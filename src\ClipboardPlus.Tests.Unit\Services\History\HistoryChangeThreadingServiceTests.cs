#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.

using System;
using System.Threading;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;
using Moq;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Services.History
{
    /// <summary>
    /// Tests unitaires pour HistoryChangeThreadingService.
    /// </summary>
    [TestFixture]
    public class HistoryChangeThreadingServiceTests
    {
        private Mock<ILoggingService>? _mockLoggingService;
        private HistoryChangeThreadingService? _threadingService;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _threadingService = new HistoryChangeThreadingService(_mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new HistoryChangeThreadingService(null!));
        }

        [Test]
        public void Constructor_WithValidLoggingService_ShouldCreateInstance()
        {
            // Act
            var service = new HistoryChangeThreadingService(_mockLoggingService!.Object);

            // Assert
            Assert.That(service, Is.Not.Null);
        }

        [Test]
        public void EnsureUIThread_WithNullAction_ShouldLogWarningAndReturnFalse()
        {
            // Arrange
            var eventId = "test-event-123";

            // Act
            var result = _threadingService!.EnsureUIThread(null, eventId);

            // Assert
            Assert.That(result, Is.False);
            _mockLoggingService!.Verify(x => x.LogWarning(It.Is<string>(s => 
                s.Contains("Action null fournie à EnsureUIThread") && 
                s.Contains(eventId))), Times.Once);
        }

        [Test]
        public void EnsureUIThread_WithValidAction_ShouldLogInfo()
        {
            // Arrange
            var eventId = "test-event-456";
            Action testAction = () => { /* Action de test */ };

            // Act
            var result = _threadingService!.EnsureUIThread(testAction, eventId);

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s =>
                s.Contains(eventId))), Times.AtLeastOnce);
        }

        [Test]
        public void EnsureUIThread_WhenActionThrowsException_ShouldLogErrorAndRethrow()
        {
            // Arrange
            var eventId = "test-event-error";
            var expectedException = new InvalidOperationException("Test exception");
            Action throwingAction = () => throw expectedException;

            // Act & Assert
            var actualException = Assert.Throws<InvalidOperationException>(() =>
                _threadingService!.EnsureUIThread(throwingAction, eventId));

            Assert.That(actualException, Is.SameAs(expectedException));
            // Vérifier qu'une erreur a été loggée (le message peut varier selon le contexte d'exécution)
            _mockLoggingService!.Verify(x => x.LogError(
                It.Is<string>(s => s.Contains("Erreur lors de la redirection vers le thread UI") && s.Contains(eventId)),
                It.Is<Exception>(ex => ex == expectedException)), Times.Once);
        }

        [Test]
        public void IsOnUIThread_ShouldNotThrowException()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _threadingService!.IsOnUIThread());
        }

        [Test]
        public void IsOnUIThread_WhenApplicationCurrentIsNull_ShouldReturnFalse()
        {
            // Note: Dans un contexte de test unitaire, Application.Current est généralement null
            // Act
            var result = _threadingService!.IsOnUIThread();

            // Assert
            // Le résultat peut être false ou true selon le contexte de test
            Assert.That(result, Is.TypeOf<bool>());
        }

        [Test]
        public void EnsureUIThread_WithNullEventId_ShouldHandleGracefully()
        {
            // Arrange
            Action testAction = () => { /* Action de test */ };

            // Act & Assert
            Assert.DoesNotThrow(() => _threadingService!.EnsureUIThread(testAction, null));
        }

        [Test]
        public void EnsureUIThread_WithEmptyEventId_ShouldHandleGracefully()
        {
            // Arrange
            Action testAction = () => { /* Action de test */ };

            // Act & Assert
            Assert.DoesNotThrow(() => _threadingService!.EnsureUIThread(testAction, ""));
        }

        [Test]
        public void EnsureUIThread_MultipleCallsWithSameEventId_ShouldLogEachCall()
        {
            // Arrange
            var eventId = "repeated-event";
            var callCount = 0;
            Action testAction = () => callCount++;

            // Act
            _threadingService!.EnsureUIThread(testAction, eventId);
            _threadingService!.EnsureUIThread(testAction, eventId);

            // Assert
            _mockLoggingService!.Verify(x => x.LogInfo(It.Is<string>(s => 
                s.Contains(eventId))), Times.AtLeast(2));
        }

        [Test]
        public void EnsureUIThread_WithLongRunningAction_ShouldComplete()
        {
            // Arrange
            var eventId = "long-running-event";
            var completed = false;
            Action longAction = () => 
            {
                Thread.Sleep(10); // Courte pause pour simuler du travail
                completed = true;
            };

            // Act
            var result = _threadingService!.EnsureUIThread(longAction, eventId);

            // Assert
            Assert.That(completed, Is.True);
        }

        [Test]
        public void Service_ShouldHandleConcurrentCalls()
        {
            // Arrange
            var eventId = "concurrent-test";
            var executionCount = 0;
            var lockObject = new object();
            Action concurrentAction = () => 
            {
                lock (lockObject)
                {
                    executionCount++;
                }
            };

            // Act
            var tasks = new[]
            {
                System.Threading.Tasks.Task.Run(() => _threadingService!.EnsureUIThread(concurrentAction, eventId + "-1")),
                System.Threading.Tasks.Task.Run(() => _threadingService!.EnsureUIThread(concurrentAction, eventId + "-2")),
                System.Threading.Tasks.Task.Run(() => _threadingService!.EnsureUIThread(concurrentAction, eventId + "-3"))
            };

            // Assert
            Assert.DoesNotThrow(() => System.Threading.Tasks.Task.WaitAll(tasks, TimeSpan.FromSeconds(5)));
        }

        [Test]
        public void IsOnUIThread_ShouldReturnConsistentResult()
        {
            // Act
            var result1 = _threadingService!.IsOnUIThread();
            var result2 = _threadingService!.IsOnUIThread();

            // Assert
            Assert.That(result1, Is.EqualTo(result2));
        }

        [Test]
        public void EnsureUIThread_WithActionThatModifiesState_ShouldExecuteCorrectly()
        {
            // Arrange
            var eventId = "state-modification-test";
            var testValue = 0;
            Action stateAction = () => testValue = 42;

            // Act
            _threadingService!.EnsureUIThread(stateAction, eventId);

            // Assert
            Assert.That(testValue, Is.EqualTo(42));
        }

        [Test]
        public void Service_ShouldLogAppropriateMessagesForDifferentScenarios()
        {
            // Arrange
            var eventId = "logging-test";
            Action normalAction = () => { };

            // Act
            _threadingService!.EnsureUIThread(normalAction, eventId);

            // Assert - Vérifier qu'au moins un log a été émis
            _mockLoggingService!.Verify(x => x.LogInfo(It.IsAny<string>()), Times.AtLeastOnce);
        }
    }
}
