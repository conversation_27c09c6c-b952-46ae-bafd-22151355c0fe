using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Core.Services.WindowDeactivation
{
    /// <summary>
    /// Implémentation de l'orchestrateur principal pour la gestion de la désactivation de fenêtres.
    /// Reproduit fidèlement le comportement de la méthode Window_Deactivated originale.
    /// </summary>
    public class WindowDeactivationOrchestrator : IWindowDeactivationOrchestrator
    {
        private readonly IWindowStateValidator _stateValidator;
        private readonly IWindowDiagnosticService _diagnosticService;
        private readonly IApplicationWindowClassifier _windowClassifier;
        private readonly IWindowVisibilityDecisionService _decisionService;
        private readonly IWindowDeactivationLoggingService _loggingService;
        private readonly ILoggingService _baseLoggingService;

        private WindowDeactivationOrchestratorConfig _config;
        private readonly WindowDeactivationPerformanceMetrics _performanceMetrics;
        private readonly object _metricsLock = new object();

        /// <summary>
        /// Initialise une nouvelle instance de l'orchestrateur de désactivation de fenêtres.
        /// </summary>
        public WindowDeactivationOrchestrator(
            IWindowStateValidator stateValidator,
            IWindowDiagnosticService diagnosticService,
            IApplicationWindowClassifier windowClassifier,
            IWindowVisibilityDecisionService decisionService,
            IWindowDeactivationLoggingService loggingService,
            ILoggingService baseLoggingService,
            WindowDeactivationOrchestratorConfig? config = null)
        {
            _stateValidator = stateValidator ?? throw new ArgumentNullException(nameof(stateValidator));
            _diagnosticService = diagnosticService ?? throw new ArgumentNullException(nameof(diagnosticService));
            _windowClassifier = windowClassifier ?? throw new ArgumentNullException(nameof(windowClassifier));
            _decisionService = decisionService ?? throw new ArgumentNullException(nameof(decisionService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _baseLoggingService = baseLoggingService ?? throw new ArgumentNullException(nameof(baseLoggingService));

            _config = config ?? new WindowDeactivationOrchestratorConfig();
            _performanceMetrics = new WindowDeactivationPerformanceMetrics();
        }

        /// <inheritdoc />
        public async Task<WindowDeactivationResult> HandleWindowDeactivationAsync(Window window, EventArgs e)
        {
            if (_config.EnableAsyncOperations)
            {
                return await Task.Run(() => HandleWindowDeactivation(window, e));
            }
            else
            {
                return HandleWindowDeactivation(window, e);
            }
        }

        /// <inheritdoc />
        public WindowDeactivationResult HandleWindowDeactivation(Window window, EventArgs e)
        {
            var operationId = GenerateOperationId();
            var stopwatch = Stopwatch.StartNew();
            var result = new WindowDeactivationResult
            {
                OperationId = operationId,
                StartTime = DateTime.Now
            };

            try
            {
                // Étape 1: Logging du début de l'opération
                _loggingService.LogDeactivationStart(window, operationId);

                // Étape 2: Validation des conditions préalables
                var validationResult = ValidateWindowState(window, operationId);
                result.ValidationResult = validationResult;

                if (validationResult.ShouldIgnore)
                {
                    result.ActionTaken = GetActionFromValidationType(validationResult.ValidationType);
                    result.Reason = validationResult.Reason;
                    result.Details = validationResult.Details;

                    _loggingService.LogValidationResult(validationResult, operationId);
                    _loggingService.LogDeactivationEnd(window, operationId);

                    result.EndTime = DateTime.Now;
                    UpdatePerformanceMetrics(result, true);
                    return result;
                }

                // Étape 3: Diagnostic des fenêtres système
                var diagnosticResult = _diagnosticService.AnalyzeCurrentWindowState();
                result.DiagnosticResult = diagnosticResult;
                _loggingService.LogDiagnosticResults(diagnosticResult, operationId);

                if (!diagnosticResult.IsSuccessful)
                {
                    result.IsSuccessful = false;
                    result.ActionTaken = WindowDeactivationAction.Error;
                    result.Reason = "Échec du diagnostic des fenêtres";
                    result.Details = diagnosticResult.ErrorMessage ?? "Erreur inconnue";

                    result.EndTime = DateTime.Now;
                    UpdatePerformanceMetrics(result, false);
                    return result;
                }

                // Étape 4: Classification de la fenêtre active (si présente)
                WindowClassificationResult? classificationResult = null;
                if (diagnosticResult.ActiveWindow != null && diagnosticResult.ActiveWindow != window)
                {
                    classificationResult = _windowClassifier.ClassifyWindow(diagnosticResult.ActiveWindow, window);
                    result.ClassificationResult = classificationResult;
                    _loggingService.LogClassificationResults(classificationResult, operationId);
                }

                // Étape 5: Prise de décision de visibilité
                var decisionContext = new WindowVisibilityDecisionContext
                {
                    Diagnostic = diagnosticResult,
                    Classification = classificationResult,
                    TargetWindow = window,
                    TargetWindowName = window.GetType().Name,
                    IsTargetWindowActive = window.IsActive,
                    Config = _config.DecisionConfig
                };

                var visibilityDecision = _decisionService.EvaluateVisibilityDecision(decisionContext);
                result.VisibilityDecision = visibilityDecision;
                _loggingService.LogVisibilityDecision(visibilityDecision, operationId);

                // Étape 6: Exécution de l'action
                if (visibilityDecision.ShouldHide)
                {
                    ExecuteHideAction(window, visibilityDecision);
                    result.ActionTaken = GetActionFromDecisionType(visibilityDecision.DecisionType);
                }
                else
                {
                    result.ActionTaken = GetIgnoreActionFromDecisionType(visibilityDecision.DecisionType);
                }

                result.Reason = visibilityDecision.Reason;
                result.Details = visibilityDecision.Details;

                // Étape 7: Logging de fin et métriques
                _loggingService.LogDeactivationEnd(window, operationId);

                if (_config.EnablePerformanceMetrics)
                {
                    var metrics = CreateOperationMetrics(result, stopwatch.Elapsed.TotalMilliseconds);
                    result.Metrics = metrics;
                    _loggingService.LogPerformanceMetrics(metrics, operationId);
                }

                result.EndTime = DateTime.Now;
                UpdatePerformanceMetrics(result, true);
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.IsSuccessful = false;
                result.ActionTaken = WindowDeactivationAction.Error;
                result.Reason = "Exception pendant l'opération";
                result.Details = ex.Message;
                result.Exception = ex;
                result.EndTime = DateTime.Now;

                _loggingService.LogError(operationId, ex, "HandleWindowDeactivation");
                _baseLoggingService.LogError($"[WindowDeactivationOrchestrator] Erreur critique dans l'opération {operationId}: {ex.Message}", ex);

                UpdatePerformanceMetrics(result, false);
                return result;
            }
        }

        /// <inheritdoc />
        public void Configure(WindowDeactivationOrchestratorConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));

            // Configurer les services sous-jacents
            if (config.DecisionConfig != null)
            {
                _decisionService.ConfigureDecisionRules(config.DecisionConfig);
            }

            if (config.LoggingConfig != null)
            {
                _loggingService.ConfigureLogging(config.LoggingConfig);
            }

            _baseLoggingService.LogInfo($"[WindowDeactivationOrchestrator] Configuration mise à jour");
        }

        /// <inheritdoc />
        public WindowDeactivationPerformanceMetrics GetPerformanceMetrics()
        {
            lock (_metricsLock)
            {
                return new WindowDeactivationPerformanceMetrics
                {
                    TotalOperations = _performanceMetrics.TotalOperations,
                    SuccessfulOperations = _performanceMetrics.SuccessfulOperations,
                    FailedOperations = _performanceMetrics.FailedOperations,
                    AverageOperationDurationMs = _performanceMetrics.AverageOperationDurationMs,
                    MinOperationDurationMs = _performanceMetrics.MinOperationDurationMs,
                    MaxOperationDurationMs = _performanceMetrics.MaxOperationDurationMs,
                    LastResetTime = _performanceMetrics.LastResetTime
                };
            }
        }

        /// <summary>
        /// Valide l'état de la fenêtre avant de procéder au traitement.
        /// </summary>
        private WindowStateValidationResult ValidateWindowState(Window window, string operationId)
        {
            var isClosing = GetWindowClosingState(window);
            var isOperationInProgress = GetViewModelOperationState(window);

            var context = new WindowStateValidationContext
            {
                IsClosing = isClosing,
                IsOperationInProgress = isOperationInProgress,
                WindowName = window.GetType().Name,
                IsActive = window.IsActive
            };

            return _stateValidator.ValidateWindowState(context);
        }

        /// <summary>
        /// Obtient l'état de fermeture d'une fenêtre via réflexion.
        /// </summary>
        private bool GetWindowClosingState(Window window)
        {
            try
            {
                var isClosingField = window.GetType().GetField("_isClosing",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (isClosingField != null)
                {
                    return (bool)(isClosingField.GetValue(window) ?? false);
                }
            }
            catch (Exception ex)
            {
                _baseLoggingService.LogWarning($"[WindowDeactivationOrchestrator] Impossible d'obtenir l'état _isClosing: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Obtient l'état d'opération du ViewModel associé à la fenêtre.
        /// </summary>
        private bool GetViewModelOperationState(Window window)
        {
            try
            {
                if (window.DataContext is ClipboardHistoryViewModel viewModel)
                {
                    return viewModel.IsOperationInProgress;
                }
            }
            catch (Exception ex)
            {
                _baseLoggingService.LogWarning($"[WindowDeactivationOrchestrator] Impossible d'obtenir l'état du ViewModel: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Exécute l'action de masquage de la fenêtre.
        /// </summary>
        private void ExecuteHideAction(Window window, WindowVisibilityDecision decision)
        {
            try
            {
                switch (decision.RecommendedAction)
                {
                    case WindowVisibilityAction.HideImmediately:
                        window.Hide();
                        break;
                    case WindowVisibilityAction.Minimize:
                        window.WindowState = WindowState.Minimized;
                        break;
                    default:
                        window.Hide(); // Action par défaut
                        break;
                }

                _baseLoggingService.LogInfo($"[WindowDeactivationOrchestrator] Action exécutée: {decision.RecommendedAction} sur {window.GetType().Name}");
            }
            catch (Exception ex)
            {
                _baseLoggingService.LogError($"[WindowDeactivationOrchestrator] Erreur lors du masquage de la fenêtre: {ex.Message}", ex);
                // Ne pas re-lancer l'exception pour éviter de casser l'orchestrateur
                // L'erreur est loggée et l'opération continue
            }
        }

        /// <summary>
        /// Génère un identifiant unique pour l'opération.
        /// </summary>
        private string GenerateOperationId()
        {
            return Guid.NewGuid().ToString("N")[..8];
        }

        /// <summary>
        /// Convertit un type de validation en action de désactivation.
        /// </summary>
        private WindowDeactivationAction GetActionFromValidationType(WindowStateValidationType validationType)
        {
            return validationType switch
            {
                WindowStateValidationType.WindowClosing => WindowDeactivationAction.IgnoredWindowClosing,
                WindowStateValidationType.OperationInProgress => WindowDeactivationAction.IgnoredOperationInProgress,
                WindowStateValidationType.ValidationError => WindowDeactivationAction.Error,
                _ => WindowDeactivationAction.None
            };
        }

        /// <summary>
        /// Convertit un type de décision en action de masquage.
        /// </summary>
        private WindowDeactivationAction GetActionFromDecisionType(WindowVisibilityDecisionType decisionType)
        {
            return decisionType switch
            {
                WindowVisibilityDecisionType.HideExternalWindow => WindowDeactivationAction.WindowHiddenExternalWindow,
                WindowVisibilityDecisionType.HideNoActiveWindow => WindowDeactivationAction.WindowHiddenNoActiveWindow,
                _ => WindowDeactivationAction.None
            };
        }

        /// <summary>
        /// Convertit un type de décision en action d'ignorance.
        /// </summary>
        private WindowDeactivationAction GetIgnoreActionFromDecisionType(WindowVisibilityDecisionType decisionType)
        {
            return decisionType switch
            {
                WindowVisibilityDecisionType.IgnoredApplicationWindow => WindowDeactivationAction.IgnoredApplicationWindow,
                WindowVisibilityDecisionType.IgnoredStillActive => WindowDeactivationAction.IgnoredStillActive,
                _ => WindowDeactivationAction.None
            };
        }

        /// <summary>
        /// Crée les métriques pour une opération spécifique.
        /// </summary>
        private WindowDeactivationMetrics CreateOperationMetrics(WindowDeactivationResult result, double totalDurationMs)
        {
            return new WindowDeactivationMetrics
            {
                TotalDurationMs = totalDurationMs,
                DiagnosticDurationMs = result.DiagnosticResult?.DiagnosticDurationMs ?? 0,
                ClassificationDurationMs = result.ClassificationResult?.ClassificationDurationMs ?? 0,
                DecisionDurationMs = result.VisibilityDecision?.EvaluationDurationMs ?? 0,
                WindowsAnalyzed = result.DiagnosticResult?.TotalWindowCount ?? 0,
                OperationId = result.OperationId,
                Timestamp = result.StartTime
            };
        }

        /// <summary>
        /// Met à jour les métriques de performance globales.
        /// </summary>
        private void UpdatePerformanceMetrics(WindowDeactivationResult result, bool isSuccessful)
        {
            if (!_config.EnablePerformanceMetrics) return;

            lock (_metricsLock)
            {
                _performanceMetrics.TotalOperations++;

                if (isSuccessful)
                {
                    _performanceMetrics.SuccessfulOperations++;
                }
                else
                {
                    _performanceMetrics.FailedOperations++;
                }

                var duration = result.TotalDurationMs;

                // Mise à jour des durées min/max/moyenne
                if (duration < _performanceMetrics.MinOperationDurationMs)
                {
                    _performanceMetrics.MinOperationDurationMs = duration;
                }

                if (duration > _performanceMetrics.MaxOperationDurationMs)
                {
                    _performanceMetrics.MaxOperationDurationMs = duration;
                }

                // Calcul de la moyenne mobile
                var totalOps = _performanceMetrics.TotalOperations;
                _performanceMetrics.AverageOperationDurationMs =
                    ((_performanceMetrics.AverageOperationDurationMs * (totalOps - 1)) + duration) / totalOps;
            }
        }
    }
}
