using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.LogDeletionResult.Interfaces;
using ClipboardPlus.Core.Services.NewItem.Interfaces;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Diagnostics;
using ClipboardPlus.UI.ViewModels.Construction.Interfaces;
using ClipboardPlus.UI.ViewModels.Construction.Models;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.UI.ViewModels.Construction.Implementations
{
    /// <summary>
    /// Implémentation du service de résolution des dépendances via ServiceProvider ou fallback.
    /// Responsabilité unique : Résolution et configuration de tous les services optionnels et complexes selon le principe DIP.
    /// </summary>
    public class ServiceResolver : IServiceResolver
    {
        /// <summary>
        /// Résout les services optionnels avec fallback via ServiceProvider.
        /// Cette méthode centralise la logique de résolution des services optionnels
        /// en utilisant le ServiceProvider ou les instances fournies directement.
        /// Extraction directe des lignes 237-249 du constructeur original.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services pour l'injection de dépendances</param>
        /// <param name="deletionLogger">Logger pour les opérations de suppression (optionnel)</param>
        /// <param name="healthService">Service de santé des collections (optionnel)</param>
        /// <param name="visibilityManager">Gestionnaire de visibilité SOLID (optionnel)</param>
        /// <param name="newItemOrchestrator">Orchestrateur de création d'éléments (optionnel)</param>
        /// <param name="testDetector">Détecteur d'environnement de test (optionnel)</param>
        /// <returns>DTO contenant tous les services résolus</returns>
        public ResolvedServices ResolveOptionalServices(
            IServiceProvider serviceProvider,
            IDeletionResultLogger? deletionLogger,
            ICollectionHealthService? healthService,
            IVisibilityStateManager? visibilityManager,
            INewItemCreationOrchestrator? newItemOrchestrator,
            ITestEnvironmentDetector? testDetector)
        {
            // Résolution des services optionnels avec fallback (lignes 237-243 du constructeur original)
            var resolvedDeletionLogger = deletionLogger ?? serviceProvider?.GetService<IDeletionResultLogger>();
            var resolvedHealthService = healthService ?? serviceProvider?.GetService<ICollectionHealthService>();
            var resolvedVisibilityManager = visibilityManager ?? serviceProvider?.GetService<IVisibilityStateManager>();
            var resolvedNewItemOrchestrator = newItemOrchestrator ?? serviceProvider?.GetService<INewItemCreationOrchestrator>();
            var resolvedTestDetector = testDetector ?? serviceProvider?.GetService<ITestEnvironmentDetector>();

            // Résolution des services pour refactorisation SupprimerElement V2 (lignes 246-249 du constructeur original)
            var deletionService = serviceProvider?.GetService<IDeletionService>();
            var deletionUIValidator = serviceProvider?.GetService<IDeletionUIValidator>();
            var deletionUIHandler = serviceProvider?.GetService<IDeletionUIHandler>();
            var deletionUINotificationService = serviceProvider?.GetService<IDeletionUINotificationService>();

            return new ResolvedServices(
                DeletionLogger: resolvedDeletionLogger,
                HealthService: resolvedHealthService,
                VisibilityManager: resolvedVisibilityManager,
                NewItemOrchestrator: resolvedNewItemOrchestrator,
                TestDetector: resolvedTestDetector,
                DeletionService: deletionService,
                DeletionUIValidator: deletionUIValidator,
                DeletionUIHandler: deletionUIHandler,
                DeletionUINotificationService: deletionUINotificationService
            );
        }



        /// <summary>
        /// Résout les services complexes nécessitant une résolution spéciale.
        /// Cette méthode centralise la résolution de tous les services complexes
        /// (LoggingService, DeletionDiagnostic, PersistenceService) en un seul point,
        /// éliminant la duplication et les dépendances circulaires.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services pour l'injection de dépendances</param>
        /// <returns>DTO contenant les services complexes résolus</returns>
        public ComplexServices ResolveComplexServices(IServiceProvider serviceProvider)
        {
            // Résolution directe d'ILoggingService (équivalent à GetLoggingService())
            var loggingService = ResolveLoggingService_V2(serviceProvider);

            // Résolution directe d'IDeletionDiagnostic (équivalent à GetDeletionDiagnostic())
            var deletionDiagnostic = ResolveDeletionDiagnostic_V2(serviceProvider);

            // Résolution directe d'IPersistenceService (équivalent à GetPersistenceService())
            var persistenceService = ResolvePersistenceService_V2(serviceProvider);

            return new ComplexServices(
                LoggingService: loggingService,
                DeletionDiagnostic: deletionDiagnostic,
                PersistenceService: persistenceService
            );
        }

        /// <summary>
        /// [PHASE 1] Résolution indépendante d'ILoggingService.
        /// Réplique la logique de ClipboardHistoryViewModel.GetLoggingService() sans dépendance au ViewModel.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services</param>
        /// <returns>Instance d'ILoggingService ou null si non résolu</returns>
        private ILoggingService? ResolveLoggingService_V2(IServiceProvider serviceProvider)
        {
            try
            {
                // Tenter de récupérer le service de journalisation à partir du conteneur de services
                return serviceProvider?.GetService<ILoggingService>();
            }
            catch (Exception)
            {
                // En cas d'exception, retourner null (comportement équivalent au ViewModel)
                return null;
            }
        }

        /// <summary>
        /// [PHASE 1] Résolution indépendante d'IDeletionDiagnostic.
        /// Réplique la logique de ClipboardHistoryViewModel.GetDeletionDiagnostic() sans dépendance au ViewModel.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services</param>
        /// <returns>Instance d'IDeletionDiagnostic ou null si non résolu</returns>
        private IDeletionDiagnostic? ResolveDeletionDiagnostic_V2(IServiceProvider serviceProvider)
        {
            try
            {
                // Tenter de récupérer le service de diagnostic de suppression
                return serviceProvider?.GetService<IDeletionDiagnostic>();
            }
            catch (Exception)
            {
                // En cas d'exception, retourner null (comportement équivalent au ViewModel)
                return null;
            }
        }

        /// <summary>
        /// [PHASE 1] Résolution indépendante d'IPersistenceService.
        /// Réplique la logique de ClipboardHistoryViewModel.GetPersistenceService() sans dépendance au ViewModel.
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services</param>
        /// <returns>Instance d'IPersistenceService ou null si non résolu</returns>
        private IPersistenceService? ResolvePersistenceService_V2(IServiceProvider serviceProvider)
        {
            try
            {
                // Tenter de récupérer le service de persistance
                return serviceProvider?.GetService<IPersistenceService>();
            }
            catch (Exception)
            {
                // En cas d'exception, retourner null (comportement équivalent au ViewModel)
                return null;
            }
        }
    }
}
