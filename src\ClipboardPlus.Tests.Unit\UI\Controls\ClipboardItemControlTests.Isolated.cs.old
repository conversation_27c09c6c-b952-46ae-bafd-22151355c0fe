using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.Controls;
using ClipboardPlus.UI.ViewModels;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using ClipboardPlus.Tests.Unit.TestHelpers;
using CommunityToolkit.Mvvm.Input;

namespace ClipboardPlus.Tests.Unit.UI.Controls
{
    /// <summary>
    /// Tests unitaires pour ClipboardItemControl isolés des ressources d'interface utilisateur
    /// </summary>
    [TestClass]
    public class ClipboardItemControlIsolatedTests : UITestBase
    {
        /// <summary>
        /// Attribut personnalisé pour exécuter un test dans un thread STA
        /// </summary>
        public class STATestMethodAttribute : TestMethodAttribute
        {
            public override TestResult[] Execute(ITestMethod testMethod)
            {
                TestResult[]? result = null;
                var thread = new Thread(() =>
                {
                    // Exécution du test dans un thread STA
                    result = base.Execute(testMethod);
                });
                
                thread.SetApartmentState(ApartmentState.STA);
                thread.Start();
                thread.Join();
                
                return result;
            }
        }

        // Utiliser la classe globale MockClipboardHistoryViewModel à la place
        private class OldMockClipboardHistoryViewModel : ITestClipboardHistoryViewModel
        {
            public ObservableCollection<ClipboardItem> HistoryItems { get; } = new ObservableCollection<ClipboardItem>();
            
            private ClipboardItem? _itemEnRenommage;
            public ClipboardItem? ItemEnRenommage
            {
                get => _itemEnRenommage;
                set
                {
                    _itemEnRenommage = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ItemEnRenommage)));
                }
            }
            
            private string _nouveauNom = string.Empty;
            public string NouveauNom
            {
                get => _nouveauNom;
                set
                {
                    _nouveauNom = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(NouveauNom)));
                }
            }
            
            public IRelayCommand<ClipboardItem> DemarrerRenommageCommand { get; } = new TestRelayCommand<ClipboardItem>();
            public IRelayCommand ConfirmerRenommageCommand { get; } = new TestRelayCommand();
            public IRelayCommand AnnulerRenommageCommand { get; } = new TestRelayCommand();
            public IRelayCommand<ClipboardItem> BasculerEpinglageCommand { get; } = new TestRelayCommand<ClipboardItem>();
            public IRelayCommand<ClipboardItem> AfficherPreviewCommand { get; } = new TestRelayCommand<ClipboardItem>();
            public IRelayCommand<ClipboardItem> SupprimerElementCommand { get; } = new TestRelayCommand<ClipboardItem>();
            
            public event PropertyChangedEventHandler? PropertyChanged;
        }

        /// <summary>
        /// Command qui implémente IRelayCommand pour les tests
        /// </summary>
        private class TestRelayCommand : IRelayCommand
        {
            public bool CanExecuteValue { get; set; } = true;
            public bool WasExecuted { get; private set; }
            public object? LastParameter { get; private set; }

            public bool CanExecute(object? parameter) => CanExecuteValue;

            public void Execute(object? parameter)
            {
                WasExecuted = true;
                LastParameter = parameter;
            }
            
            public void NotifyCanExecuteChanged() { }

            public event EventHandler? CanExecuteChanged;
        }

        /// <summary>
        /// Command typée qui implémente IRelayCommand pour les tests
        /// </summary>
        private class TestRelayCommand<T> : IRelayCommand<T>
        {
            public bool CanExecuteValue { get; set; } = true;
            public bool WasExecuted { get; private set; }
            public T? LastParameter { get; private set; }

            public bool CanExecute(object? parameter) => CanExecuteValue;
            
            public bool CanExecute(T? parameter) => CanExecuteValue;

            public void Execute(object? parameter)
            {
                WasExecuted = true;
                if (parameter is T typedParam)
                {
                    LastParameter = typedParam;
                    Execute(typedParam);
                }
            }
            
            public void Execute(T? parameter)
            {
                WasExecuted = true;
                LastParameter = parameter;
            }
            
            public void NotifyCanExecuteChanged() { }

            public event EventHandler? CanExecuteChanged;
        }
        
        /// <summary>
        /// Command asynchrone typée qui implémente IAsyncRelayCommand pour les tests
        /// </summary>
        private class TestAsyncRelayCommand<T> : IAsyncRelayCommand<T>, INotifyPropertyChanged
        {
            public bool CanExecuteValue { get; set; } = true;
            public bool WasExecuted { get; private set; }
            public T? LastParameter { get; private set; }
            
            private bool _isRunning;
            public bool IsRunning 
            { 
                get => _isRunning; 
                private set 
                {
                    _isRunning = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsRunning)));
                }
            }
            
            public bool IsCancellationRequested { get; private set; }
            
            private Task? _task;
            public Task? Task => _task;
            
            public Task? ExecutionTask => _task;
            
            public bool CanBeCanceled => true;

            public bool CanExecute(object? parameter) => CanExecuteValue;
            
            public bool CanExecute(T? parameter) => CanExecuteValue;

            public void Execute(object? parameter)
            {
                WasExecuted = true;
                if (parameter is T typedParam)
                {
                    LastParameter = typedParam;
                    _task = ExecuteAsync(typedParam);
                }
            }
            
            public void Execute(T? parameter)
            {
                WasExecuted = true;
                LastParameter = parameter;
                _task = ExecuteAsync(parameter);
            }
            
            public Task ExecuteAsync(T? parameter)
            {
                WasExecuted = true;
                LastParameter = parameter;
                IsRunning = true;
                _task = Task.CompletedTask;
                IsRunning = false;
                return Task.CompletedTask;
            }
            
            public Task ExecuteAsync(object? parameter)
            {
                WasExecuted = true;
                if (parameter is T typedParam)
                {
                    LastParameter = typedParam;
                }
                IsRunning = true;
                _task = Task.CompletedTask;
                IsRunning = false;
                return Task.CompletedTask;
            }
            
            public void NotifyCanExecuteChanged() { }
            
            public void Cancel() 
            {
                IsCancellationRequested = true;
            }

            public event EventHandler? CanExecuteChanged;
            public event PropertyChangedEventHandler? PropertyChanged;
        }

        /// <summary>
        /// Mock de ILoggingService pour les tests
        /// </summary>
        private class MockLoggingService : ILoggingService
        {
            public List<string> LoggedMessages { get; } = new List<string>();
            public bool IsConsoleOutputEnabled { get; private set; } = false;

            public void LogDebug(string message)
            {
                LoggedMessages.Add($"DEBUG: {message}");
            }

            public void LogInfo(string message)
            {
                LoggedMessages.Add($"INFO: {message}");
            }

            public void LogWarning(string message)
            {
                LoggedMessages.Add($"WARNING: {message}");
            }

            public void LogError(string message, Exception? exception = null)
            {
                var msg = $"ERROR: {message}";
                if (exception != null)
                {
                    msg += $" - {exception.Message}";
                }
                LoggedMessages.Add(msg);
            }

            public void LogCritical(string message, Exception? exception = null)
            {
                var msg = $"CRITICAL: {message}";
                if (exception != null)
                {
                    msg += $" - {exception.Message}";
                }
                LoggedMessages.Add(msg);
            }

            public void LogException(Exception exception, string? context = null)
            {
                LoggedMessages.Add($"EXCEPTION: {context ?? ""} - {exception.Message}");
            }

            public string GetLogFilePath()
            {
                return "test_log_file.txt";
            }

            public void ForceFlush()
            {
                // Ne rien faire dans le mock
            }

            public void EnableConsoleOutput(bool enabled)
            {
                IsConsoleOutputEnabled = enabled;
            }

            public void LogDeletion(string message)
            {
                LoggedMessages.Add($"DELETION: {message}");
            }
        }

        /// <summary>
        /// Helper pour accéder aux champs et méthodes privés
        /// </summary>
        private static class ReflectionHelper
        {
            /// <summary>
            /// Définit la valeur d'un champ privé
            /// </summary>
            public static void SetPrivateField(object instance, string fieldName, object? value)
            {
                var field = instance.GetType().GetField(fieldName, BindingFlags.NonPublic | BindingFlags.Instance);
                if (field != null)
                {
                    field.SetValue(instance, value);
                }
                else
                {
                    throw new ArgumentException($"Le champ '{fieldName}' n'existe pas dans le type {instance.GetType().Name}");
                }
            }

            /// <summary>
            /// Obtient la valeur d'un champ privé
            /// </summary>
            public static T? GetPrivateField<T>(object instance, string fieldName)
            {
                var field = instance.GetType().GetField(fieldName, BindingFlags.NonPublic | BindingFlags.Instance);
                if (field != null)
                {
                    return (T?)field.GetValue(instance);
                }
                else
                {
                    throw new ArgumentException($"Le champ '{fieldName}' n'existe pas dans le type {instance.GetType().Name}");
                }
            }

            /// <summary>
            /// Invoque une méthode privée
            /// </summary>
            public static object? InvokePrivateMethod(object instance, string methodName, params object[] parameters)
            {
                var method = instance.GetType().GetMethod(methodName, BindingFlags.NonPublic | BindingFlags.Instance);
                if (method != null)
                {
                    return method.Invoke(instance, parameters);
                }
                else
                {
                    throw new ArgumentException($"La méthode '{methodName}' n'existe pas dans le type {instance.GetType().Name}");
                }
            }
        }

        [TestInitialize]
        public void Initialize()
        {
            // Initialiser l'environnement de test UI
            InitializeUITestEnvironment();
        }

        /// <summary>
        /// Teste que l'objet CurrentItem est mis à jour lors du changement de DataContext
        /// </summary>
        [STATestMethod]
        public void DataContextChanged_UpdatesCurrentItem()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            var item = new ClipboardItem { Id = 1, CustomName = "Test Item" };
            
            // Act - Appeler directement la méthode de changement de DataContext
            control.DataContext = item;
            
            // Assert - Accéder au champ privé _currentItem via réflexion
            var currentItemField = typeof(ClipboardItemControl).GetField(
                "_currentItem", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(currentItemField, "Le champ _currentItem doit exister");
            
            var currentItem = currentItemField.GetValue(control) as ClipboardItem;
            Assert.IsNotNull(currentItem, "CurrentItem ne devrait pas être null après le changement de DataContext");
            Assert.AreEqual(1, currentItem.Id);
            Assert.AreEqual("Test Item", currentItem.CustomName);
        }
        
        /// <summary>
        /// Teste que RenameMenuItem_Click exécute la commande DemarrerRenommageCommand
        /// </summary>
        [STATestMethod]
        public void RenameMenuItem_Click_ExecutesCommand()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            var item = new ClipboardItem { Id = 1, CustomName = "Test Item" };
            var viewModel = new MockClipboardHistoryViewModel();
            viewModel.HistoryItems.Add(item);
            
            // Configurer l'état interne du contrôle via réflexion
            var currentItemField = typeof(ClipboardItemControl).GetField(
                "_currentItem", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(currentItemField, "Le champ _currentItem doit exister");
            currentItemField.SetValue(control, item);
            
            var viewModelField = typeof(ClipboardItemControl).GetField(
                "_viewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(viewModelField, "Le champ _viewModel doit exister");
            viewModelField.SetValue(control, viewModel);
            
            // Act - Appeler directement la méthode RenameMenuItem_Click via réflexion
            var method = typeof(ClipboardItemControl).GetMethod(
                "RenameMenuItem_Click", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(method, "La méthode RenameMenuItem_Click doit exister");
            method.Invoke(control, new object[] { null!, new RoutedEventArgs() });
            
            // Assert
            Assert.IsTrue(((TestRelayCommand<ClipboardItem>)viewModel.DemarrerRenommageCommand).WasExecuted);
            Assert.AreEqual(item, ((TestRelayCommand<ClipboardItem>)viewModel.DemarrerRenommageCommand).LastParameter);
        }
        
        /// <summary>
        /// Teste que PinMenuItem_Click exécute la commande BasculerEpinglageCommand
        /// </summary>
        [STATestMethod]
        public void PinMenuItem_Click_ExecutesCommand()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            var item = new ClipboardItem { Id = 1, CustomName = "Test Item" };
            var viewModel = new MockClipboardHistoryViewModel();
            viewModel.HistoryItems.Add(item);
            
            // Configurer l'état interne du contrôle via réflexion
            var currentItemField = typeof(ClipboardItemControl).GetField(
                "_currentItem", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(currentItemField, "Le champ _currentItem doit exister");
            currentItemField.SetValue(control, item);
            
            var viewModelField = typeof(ClipboardItemControl).GetField(
                "_viewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(viewModelField, "Le champ _viewModel doit exister");
            viewModelField.SetValue(control, viewModel);
            
            // Act - Appeler directement la méthode PinMenuItem_Click via réflexion
            var method = typeof(ClipboardItemControl).GetMethod(
                "PinMenuItem_Click", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(method, "La méthode PinMenuItem_Click doit exister");
            method.Invoke(control, new object[] { null!, new RoutedEventArgs() });
            
            // Assert
            Assert.IsTrue(((TestAsyncRelayCommand<ClipboardItem>)viewModel.BasculerEpinglageCommand).WasExecuted);
            Assert.AreEqual(item, ((TestAsyncRelayCommand<ClipboardItem>)viewModel.BasculerEpinglageCommand).LastParameter);
        }
        
        /// <summary>
        /// Teste que PreviewMenuItem_Click exécute la commande AfficherPreviewCommand
        /// </summary>
        [STATestMethod]
        public void PreviewMenuItem_Click_ExecutesCommand()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            var item = new ClipboardItem { Id = 1, CustomName = "Test Item" };
            var viewModel = new MockClipboardHistoryViewModel();
            viewModel.HistoryItems.Add(item);
            
            // Configurer l'état interne du contrôle via réflexion
            var currentItemField = typeof(ClipboardItemControl).GetField(
                "_currentItem", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(currentItemField, "Le champ _currentItem doit exister");
            currentItemField.SetValue(control, item);
            
            var viewModelField = typeof(ClipboardItemControl).GetField(
                "_viewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(viewModelField, "Le champ _viewModel doit exister");
            viewModelField.SetValue(control, viewModel);
            
            // Act - Appeler directement la méthode PreviewMenuItem_Click via réflexion
            var method = typeof(ClipboardItemControl).GetMethod(
                "PreviewMenuItem_Click", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(method, "La méthode PreviewMenuItem_Click doit exister");
            method.Invoke(control, new object[] { null!, new RoutedEventArgs() });
            
            // Assert
            Assert.IsTrue(((TestRelayCommand<ClipboardItem>)viewModel.AfficherPreviewCommand).WasExecuted);
            Assert.AreEqual(item, ((TestRelayCommand<ClipboardItem>)viewModel.AfficherPreviewCommand).LastParameter);
        }
        
        /// <summary>
        /// Teste que DeleteMenuItem_Click exécute la commande SupprimerElementCommand
        /// </summary>
        [STATestMethod]
        public void DeleteMenuItem_Click_ExecutesCommand()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            var item = new ClipboardItem { Id = 1, CustomName = "Test Item" };
            var viewModel = new MockClipboardHistoryViewModel();
            viewModel.HistoryItems.Add(item);
            
            // Configurer l'état interne du contrôle via réflexion
            var currentItemField = typeof(ClipboardItemControl).GetField(
                "_currentItem", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(currentItemField, "Le champ _currentItem doit exister");
            currentItemField.SetValue(control, item);
            
            var viewModelField = typeof(ClipboardItemControl).GetField(
                "_viewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(viewModelField, "Le champ _viewModel doit exister");
            viewModelField.SetValue(control, viewModel);
            
            // Act - Appeler directement la méthode DeleteMenuItem_Click via réflexion
            var method = typeof(ClipboardItemControl).GetMethod(
                "DeleteMenuItem_Click", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(method, "La méthode DeleteMenuItem_Click doit exister");
            method.Invoke(control, new object[] { null!, new RoutedEventArgs() });
            
            // Assert
            Assert.IsTrue(((TestRelayCommand<ClipboardItem>)viewModel.SupprimerElementCommand).WasExecuted);
            Assert.AreEqual(item, ((TestRelayCommand<ClipboardItem>)viewModel.SupprimerElementCommand).LastParameter);
        }
        
        /// <summary>
        /// Teste que DeleteMenuItem_Click n'exécute pas la commande quand l'élément est null
        /// </summary>
        [STATestMethod]
        public void DeleteMenuItem_Click_WithNullItem_DoesNotExecuteCommand()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            var viewModel = new MockClipboardHistoryViewModel();
            
            // Configurer l'état interne du contrôle via réflexion
            var currentItemField = typeof(ClipboardItemControl).GetField(
                "_currentItem", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(currentItemField, "Le champ _currentItem doit exister");
            currentItemField.SetValue(control, null);
            
            var viewModelField = typeof(ClipboardItemControl).GetField(
                "_viewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(viewModelField, "Le champ _viewModel doit exister");
            viewModelField.SetValue(control, viewModel);
            
            // Act - Appeler directement la méthode DeleteMenuItem_Click via réflexion
            var method = typeof(ClipboardItemControl).GetMethod(
                "DeleteMenuItem_Click", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(method, "La méthode DeleteMenuItem_Click doit exister");
            method.Invoke(control, new object[] { null!, new RoutedEventArgs() });
            
            // Assert
            Assert.IsFalse(((TestRelayCommand<ClipboardItem>)viewModel.SupprimerElementCommand).WasExecuted);
        }
        
        /// <summary>
        /// Teste que FindViewModel retourne le ViewModel quand il est déjà défini
        /// </summary>
        [STATestMethod]
        public void FindViewModel_ReturnsViewModel_WhenAlreadySet()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            var viewModel = new MockClipboardHistoryViewModel();
            
            // Configurer l'état interne du contrôle via réflexion
            var viewModelField = typeof(ClipboardItemControl).GetField(
                "_viewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(viewModelField, "Le champ _viewModel doit exister");
            viewModelField.SetValue(control, viewModel);
            
            // Act - Appeler directement la méthode FindViewModel via réflexion
            var method = typeof(ClipboardItemControl).GetMethod(
                "FindViewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(method, "La méthode FindViewModel doit exister");
            var result = method.Invoke(control, null);
            
            // Assert
            Assert.IsNotNull(result);
            Assert.AreSame(viewModel, result);
        }
        
        /// <summary>
        /// Teste que FindViewModel retourne null quand la recherche a déjà été tentée
        /// </summary>
        [STATestMethod]
        public void FindViewModel_ReturnsNull_WhenSearchAttempted()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            
            // Configurer l'état interne du contrôle via réflexion
            var viewModelSearchAttemptedField = typeof(ClipboardItemControl).GetField(
                "_viewModelSearchAttempted", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(viewModelSearchAttemptedField, "Le champ _viewModelSearchAttempted doit exister");
            viewModelSearchAttemptedField.SetValue(control, true);
            
            // Act - Appeler directement la méthode FindViewModel via réflexion
            var method = typeof(ClipboardItemControl).GetMethod(
                "FindViewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(method, "La méthode FindViewModel doit exister");
            var result = method.Invoke(control, null);
            
            // Assert
            Assert.IsNull(result);
        }
        
        /// <summary>
        /// Teste que EditNameTextBox_KeyDown avec la touche Enter exécute ConfirmerRenommageCommand
        /// </summary>
        [STATestMethod]
        public void EditNameTextBox_KeyDown_EnterKey_ConfirmsRenaming()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            var viewModel = new MockClipboardHistoryViewModel();
            
            // Configurer l'état interne du contrôle via réflexion
            var viewModelField = typeof(ClipboardItemControl).GetField(
                "_viewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(viewModelField, "Le champ _viewModel doit exister");
            viewModelField.SetValue(control, viewModel);
            
            // Créer un KeyEventArgs simulé
            var keyEventArgs = new KeyEventArgs(
                Keyboard.PrimaryDevice,
                PresentationSource.FromVisual(Application.Current.MainWindow),
                0,
                Key.Enter);
            
            // Act - Appeler directement la méthode EditNameTextBox_KeyDown via réflexion
            var method = typeof(ClipboardItemControl).GetMethod(
                "EditNameTextBox_KeyDown", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(method, "La méthode EditNameTextBox_KeyDown doit exister");
            method.Invoke(control, new object[] { null!, keyEventArgs });
            
            // Assert
            Assert.IsTrue(((TestRelayCommand)viewModel.ConfirmerRenommageCommand).WasExecuted);
        }
        
        /// <summary>
        /// Teste que EditNameTextBox_KeyDown avec la touche Escape exécute AnnulerRenommageCommand
        /// </summary>
        [STATestMethod]
        public void EditNameTextBox_KeyDown_EscapeKey_CancelsRenaming()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            var viewModel = new MockClipboardHistoryViewModel();
            
            // Configurer l'état interne du contrôle via réflexion
            var viewModelField = typeof(ClipboardItemControl).GetField(
                "_viewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(viewModelField, "Le champ _viewModel doit exister");
            viewModelField.SetValue(control, viewModel);
            
            // Créer un KeyEventArgs simulé
            var keyEventArgs = new KeyEventArgs(
                Keyboard.PrimaryDevice,
                PresentationSource.FromVisual(Application.Current.MainWindow),
                0,
                Key.Escape);
            
            // Act - Appeler directement la méthode EditNameTextBox_KeyDown via réflexion
            var method = typeof(ClipboardItemControl).GetMethod(
                "EditNameTextBox_KeyDown", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(method, "La méthode EditNameTextBox_KeyDown doit exister");
            method.Invoke(control, new object[] { null!, keyEventArgs });
            
            // Assert
            Assert.IsTrue(((TestRelayCommand)viewModel.AnnulerRenommageCommand).WasExecuted);
        }
        
        /// <summary>
        /// Teste que EditNameTextBox_LostFocus exécute ConfirmerRenommageCommand
        /// </summary>
        [STATestMethod]
        public void EditNameTextBox_LostFocus_ConfirmsRenaming()
        {
            // Arrange
            ClipboardItemControl control = new ClipboardItemControl();
            var viewModel = new MockClipboardHistoryViewModel();
            
            // Configurer l'état interne du contrôle via réflexion
            var viewModelField = typeof(ClipboardItemControl).GetField(
                "_viewModel", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(viewModelField, "Le champ _viewModel doit exister");
            viewModelField.SetValue(control, viewModel);
            
            // Act - Appeler directement la méthode EditNameTextBox_LostFocus via réflexion
            var method = typeof(ClipboardItemControl).GetMethod(
                "EditNameTextBox_LostFocus", 
                BindingFlags.NonPublic | BindingFlags.Instance);
                
            Assert.IsNotNull(method, "La méthode EditNameTextBox_LostFocus doit exister");
            method.Invoke(control, new object[] { null!, new RoutedEventArgs() });
            
            // Assert
            Assert.IsTrue(((TestRelayCommand)viewModel.ConfirmerRenommageCommand).WasExecuted);
        }
    }
} 