using System;
using System.IO;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    [TestFixture]
    public class LoggingServiceTests
    {
        private LoggingService _loggingService = null!;
        private string _tempLogFile = string.Empty;
        private ServiceProvider _serviceProvider = null!;

        [SetUp]
        public void Initialize()
        {
            _tempLogFile = Path.GetTempFileName();

            // Configuration avec DI
            var services = new ServiceCollection();
            var loggingConfig = new LoggingConfiguration
            {
                LogFilePath = _tempLogFile,
                ConsoleOutputEnabled = false,
                DebugOutputEnabled = false,
                MinimumLevel = "DEBUG",
                MaxBufferSize = 50
            };

            services.AddSingleton<ILoggingConfiguration>(loggingConfig);
            services.AddSingleton<ILogEntryFactory, LogEntryFactory>();
            services.AddSingleton<ILogTarget, DebugLogTarget>();
            services.AddSingleton<ILogTarget>(provider =>
            {
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                return new ConsoleLogTarget(config.ConsoleOutputEnabled);
            });
            services.AddSingleton<ILogTarget>(provider =>
            {
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                return new FileLogTarget(config.LogFilePath);
            });

            services.AddSingleton<ILoggingService>(provider =>
            {
                var factory = provider.GetRequiredService<ILogEntryFactory>();
                var targets = provider.GetServices<ILogTarget>();
                var config = provider.GetRequiredService<ILoggingConfiguration>();

                return new LoggingService(factory, targets, config);
            });

            _serviceProvider = services.BuildServiceProvider();
            _loggingService = (LoggingService)_serviceProvider.GetRequiredService<ILoggingService>();
        }

        [TearDown]
        public void Cleanup()
        {
            _loggingService?.Dispose();
            _serviceProvider?.Dispose();
            if (File.Exists(_tempLogFile))
            {
                try { File.Delete(_tempLogFile); } catch { }
            }
        }

        [Test]
        public void Constructor_WithDI_DoesNotThrow()
        {
            // Act & Assert - Le service est déjà créé dans SetUp
            Assert.That(_loggingService, Is.Not.Null);
        }

        [Test]
        public void LogDebug_DoesNotThrow()
        {
            // Arrange
            string testMessage = "Test de log Debug";

            // Act & Assert
            try
            {
                _loggingService.LogDebug(testMessage);
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"LogDebug ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void LogInfo_DoesNotThrow()
        {
            // Arrange
            string testMessage = "Test de log Info";

            // Act & Assert
            try
            {
                _loggingService.LogInfo(testMessage);
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"LogInfo ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        [Timeout(5000)] // Timeout de 5 secondes pour éviter les blocages
        public void LogWarning_DoesNotThrow()
        {
            // Arrange
            string testMessage = "Test de log Warning";

            // Act & Assert
            try
            {
                _loggingService.LogWarning(testMessage);

                // Forcer un flush pour s'assurer que l'écriture est terminée
                _loggingService.ForceFlush();

                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"LogWarning ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void LogError_WithoutException_DoesNotThrow()
        {
            // Arrange
            string testMessage = "Test de log Error sans exception";

            // Act & Assert
            try
            {
                _loggingService.LogError(testMessage);
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"LogError ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void LogError_WithException_DoesNotThrow()
        {
            // Arrange
            string testMessage = "Test de log Error avec exception";
            var testException = new InvalidOperationException("Test exception");

            // Act & Assert
            try
            {
                _loggingService.LogError(testMessage, testException);
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"LogError avec exception ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void LogCritical_WithoutException_DoesNotThrow()
        {
            // Arrange
            string testMessage = "Test de log Critical sans exception";

            // Act & Assert
            try
            {
                _loggingService.LogCritical(testMessage);
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"LogCritical ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void LogCritical_WithException_DoesNotThrow()
        {
            // Arrange
            string testMessage = "Test de log Critical avec exception";
            var testException = new InvalidOperationException("Test exception");

            // Act & Assert
            try
            {
                _loggingService.LogCritical(testMessage, testException);
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"LogCritical avec exception ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void LogDeletion_DoesNotThrow()
        {
            // Arrange
            string testMessage = "Test de log Deletion";

            // Act & Assert
            try
            {
                _loggingService.LogDeletion(testMessage);
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"LogDeletion ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        [Category("Problematic")]
        public void ForceFlush_DoesNotThrow()
        {
            // Arrange
            _loggingService.LogInfo("Test avant flush");

            // Act & Assert
            try
            {
                _loggingService.ForceFlush();
                _loggingService.LogInfo("Test après flush");
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"ForceFlush ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        [Category("Problematic")]
        public void AllLogMethods_WithLongString_DoesNotThrow()
        {
            // Arrange
            string longString = new string('X', 1000);

            // Act & Assert
            try
            {
                _loggingService.LogError(longString);
                _loggingService.LogCritical(longString);
                _loggingService.LogDeletion(longString);
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"Les méthodes de log avec chaîne longue ne devraient pas lever d'exception, mais ont levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void AllLogMethods_WithNullString_HandlesGracefully()
        {
            // Act & Assert
            try
            {
                // Les méthodes de log doivent gérer null gracieusement
                _loggingService.LogError(null!);
                _loggingService.LogCritical(null!);
                _loggingService.LogDeletion(null!);

                // Si on arrive ici, c'est que les méthodes ont géré null correctement
                Assert.That(true, Is.True);
            }
            catch (ArgumentNullException)
            {
                // C'est acceptable si le service lève ArgumentNullException pour null
                Assert.That(true, Is.True, "ArgumentNullException est acceptable pour les valeurs null");
            }
            catch (Exception ex)
            {
                // Toute autre exception indique un problème dans la gestion de null
                Assert.Fail($"Les méthodes de log devraient gérer null gracieusement ou lever ArgumentNullException, mais ont levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void LogError_WithSpecialCharacters_DoesNotThrow()
        {
            // Arrange
            string specialChars = "!@#$%^&*()_+{}|:<>?[];',./\\`~";

            // Act & Assert
            try
            {
                _loggingService.LogError(specialChars);
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"LogError avec caractères spéciaux ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void EnableConsoleOutput_DoesNotThrow()
        {
            // Act & Assert
            try
            {
                _loggingService.EnableConsoleOutput(true);
                Assert.That(true, Is.True);
            }
            catch (Exception ex)
            {
                Assert.Fail($"EnableConsoleOutput ne devrait pas lever d'exception, mais a levé: {ex.GetType().Name}: {ex.Message}");
            }
        }
    }
}
