using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace ClipboardPlus.UI.Converters
{
    /// <summary>
    /// Convertit une valeur booléenne en couleur.
    /// </summary>
    public class BooleanToColorConverter : IValueConverter
    {
        // Mettre en cache les brushes par défaut
        private static readonly SolidColorBrush DefaultTrueBrush = new SolidColorBrush(Colors.Green);
        private static readonly SolidColorBrush DefaultFalseBrush = new SolidColorBrush(Colors.Transparent);

        static BooleanToColorConverter()
        {
            DefaultTrueBrush.Freeze(); // Geler les brushes pour améliorer les performances
            DefaultFalseBrush.Freeze();
        }

        /// <summary>
        /// Convertit une valeur booléenne en couleur.
        /// </summary>
        /// <param name="value">Valeur à convertir.</param>
        /// <param name="targetType">Type cible.</param>
        /// <param name="parameter">Paramètre optionnel pour spécifier les couleurs.</param>
        /// <param name="culture">Culture à utiliser.</param>
        /// <returns>La couleur correspondant à la valeur booléenne.</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool boolValue = value is bool val && val;
            
            SolidColorBrush trueBrush = DefaultTrueBrush;
            SolidColorBrush falseBrush = DefaultFalseBrush;
            
            // Si un paramètre est fourni sous forme de chaîne "TrueColor,FalseColor"
            if (parameter is string colorParams)
            {
                var colors = colorParams.Split(',');
                if (colors.Length >= 2)
                {
                    try
                    {
                        if (System.Windows.Media.ColorConverter.ConvertFromString(colors[0]) is System.Windows.Media.Color tColor)
                        {
                            trueBrush = new SolidColorBrush(tColor);
                            trueBrush.Freeze(); // Geler les brushes créés dynamiquement aussi
                        }
                        if (System.Windows.Media.ColorConverter.ConvertFromString(colors[1]) is System.Windows.Media.Color fColor)
                        {
                            falseBrush = new SolidColorBrush(fColor);
                            falseBrush.Freeze(); // Geler les brushes créés dynamiquement aussi
                        }
                    }
                    catch (Exception ex) 
                    { 
                        // Log l'erreur si un service de logging était disponible
                        System.Diagnostics.Debug.WriteLine($"BooleanToColorConverter: Erreur lors de l'analyse des paramètres de couleur: {ex.Message}");
                        // Utiliser les couleurs par défaut en cas d'erreur
                        trueBrush = DefaultTrueBrush;
                        falseBrush = DefaultFalseBrush;
                    }
                }
            }
            
            return boolValue ? trueBrush : falseBrush;
        }

        /// <summary>
        /// Convertit une couleur en valeur booléenne.
        /// </summary>
        /// <param name="value">Valeur à convertir.</param>
        /// <param name="targetType">Type cible.</param>
        /// <param name="parameter">Paramètre optionnel.</param>
        /// <param name="culture">Culture à utiliser.</param>
        /// <returns>True si la couleur correspond à la couleur "true", False sinon.</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Cette conversion inverse n'est généralement pas utilisée
            throw new NotImplementedException();
        }
    }
} 