using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using ClipboardPlus.Core.Services;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.UI.Views;
using ClipboardPlus.UI.Windows;
using Microsoft.Extensions.DependencyInjection;

namespace ClipboardPlus.Core.Services.Windows
{
    /// <summary>
    /// Implémentation du service responsable de l'affichage et de la gestion de la fenêtre d'historique du presse-papiers.
    /// Cette classe encapsule la logique d'ouverture de la fenêtre d'historique qui était précédemment
    /// dans SystemTrayService.ShowHistoryWindow().
    /// </summary>
    public class HistoryWindowService : IHistoryWindowService
    {
        private readonly ILoggingService? _loggingService;
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// Initialise une nouvelle instance de HistoryWindowService.
        /// </summary>
        /// <param name="loggingService">Service de logging pour tracer les opérations.</param>
        /// <param name="serviceProvider">Fournisseur de services pour résoudre les dépendances (comme ClipboardHistoryViewModel).</param>
        public HistoryWindowService(ILoggingService? loggingService, IServiceProvider serviceProvider)
        {
            _loggingService = loggingService;
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        /// <summary>
        /// Affiche la fenêtre d'historique du presse-papiers.
        /// Cette méthode gère l'activation de la fenêtre existante ou la création d'une nouvelle instance.
        /// </summary>
        /// <returns>Une tâche représentant l'opération asynchrone d'affichage de la fenêtre.</returns>
        public Task ShowHistoryWindowAsync()
        {
            try
            {
                _loggingService?.LogInfo("HistoryWindowService: Début de l'affichage de la fenêtre d'historique");
                
                // Vérifier si la fenêtre d'historique existe déjà
                var existingWindow = System.Windows.Application.Current.Windows.OfType<ClipboardHistoryWindow>().FirstOrDefault();
                
                if (existingWindow != null)
                {
                    // Activer la fenêtre existante
                    _loggingService?.LogInfo("HistoryWindowService: Activation de la fenêtre existante");

                    // Rendre la fenêtre visible si elle était cachée
                    existingWindow.Show();
                    
                    // S'assurer que la fenêtre n'est pas minimisée
                    if (existingWindow.WindowState == WindowState.Minimized)
                    {
                        existingWindow.WindowState = WindowState.Normal;
                    }

                    // Activer la fenêtre et la mettre au premier plan
                    existingWindow.Activate();
                    existingWindow.Focus();
                    
                    _loggingService?.LogInfo("HistoryWindowService: Fenêtre existante activée");
                }
                else
                {
                    // Créer et afficher une nouvelle fenêtre d'historique
                    _loggingService?.LogInfo("HistoryWindowService: Création d'une nouvelle fenêtre d'historique");
                    
                    var clipboardHistoryViewModel = _serviceProvider.GetService(typeof(ClipboardHistoryViewModel)) as ClipboardHistoryViewModel;
                    if (clipboardHistoryViewModel == null)
                    {
                        _loggingService?.LogError("HistoryWindowService: Impossible d'obtenir ClipboardHistoryViewModel du conteneur de services");
                        return Task.CompletedTask;
                    }
                    
                    var historyWindow = new ClipboardHistoryWindow(clipboardHistoryViewModel);
                    
                    _loggingService?.LogInfo("HistoryWindowService: Affichage de la nouvelle fenêtre");
                    historyWindow.Show();
                    historyWindow.Activate();
                    
                    _loggingService?.LogInfo("HistoryWindowService: Nouvelle fenêtre affichée et activée");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"HistoryWindowService: Erreur lors de l'affichage de la fenêtre d'historique: {ex.Message}", ex);
            }

            return Task.CompletedTask;
        }
    }
}
