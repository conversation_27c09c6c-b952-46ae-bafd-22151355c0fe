#!/usr/bin/env powershell
# Script d'analyse de chevauchement de couverture
# Identifie quelles lignes sont couvertes par tests normaux vs STA vs les deux

Write-Host "=== ANALYSE DE CHEVAUCHEMENT DE COUVERTURE ===" -ForegroundColor Cyan
Write-Host ""

# Chemins des rapports
$normalCoverageFile = ".\CodeCoverage\coverage-normal.cobertura.xml"
$staCoverageFile = ".\CodeCoverage\coverage-sta.cobertura.xml"
$combinedCoverageFile = ".\CodeCoverage\coverage.cobertura.xml"

# Verifier que les fichiers existent
if (!(Test-Path $normalCoverageFile)) {
    Write-Host "ERREUR: Rapport normal introuvable: $normalCoverageFile" -ForegroundColor Red
    Write-Host "Executez d'abord: .\run-all-tests-with-coverage.ps1" -ForegroundColor Yellow
    exit 1
}

if (!(Test-Path $staCoverageFile)) {
    Write-Host "ERREUR: Rapport STA introuvable: $staCoverageFile" -ForegroundColor Red
    Write-Host "Executez d'abord: .\run-all-tests-with-coverage.ps1" -ForegroundColor Yellow
    exit 1
}

if (!(Test-Path $combinedCoverageFile)) {
    Write-Host "ERREUR: Rapport combine introuvable: $combinedCoverageFile" -ForegroundColor Red
    Write-Host "Executez d'abord: .\run-all-tests-with-coverage.ps1" -ForegroundColor Yellow
    exit 1
}

Write-Host "Chargement des rapports XML..." -ForegroundColor Yellow

try {
    [xml]$normalXml = Get-Content $normalCoverageFile
    [xml]$staXml = Get-Content $staCoverageFile
    [xml]$combinedXml = Get-Content $combinedCoverageFile
    
    Write-Host "Rapports charges avec succes" -ForegroundColor Green
} catch {
    Write-Host "ERREUR lors du chargement des rapports: $_" -ForegroundColor Red
    exit 1
}

# Statistiques globales
$normalLineRate = [math]::Round([double]$normalXml.coverage.'line-rate' * 100, 2)
$staLineRate = [math]::Round([double]$staXml.coverage.'line-rate' * 100, 2)
$combinedLineRate = [math]::Round([double]$combinedXml.coverage.'line-rate' * 100, 2)

$normalLinesCovered = [int]$normalXml.coverage.'lines-covered'
$staLinesCovered = [int]$staXml.coverage.'lines-covered'
$combinedLinesCovered = [int]$combinedXml.coverage.'lines-covered'
$totalLines = [int]$combinedXml.coverage.'lines-valid'

Write-Host ""
Write-Host "=== STATISTIQUES GLOBALES ===" -ForegroundColor Cyan
Write-Host "Tests normaux    : $normalLineRate% ($normalLinesCovered lignes)" -ForegroundColor White
Write-Host "Tests STA        : $staLineRate% ($staLinesCovered lignes)" -ForegroundColor White
Write-Host "Combine          : $combinedLineRate% ($combinedLinesCovered lignes)" -ForegroundColor White
Write-Host "Total du projet  : $totalLines lignes" -ForegroundColor Gray

# Calculer le chevauchement
$theoreticalTotal = $normalLinesCovered + $staLinesCovered
$actualTotal = $combinedLinesCovered
$overlap = $theoreticalTotal - $actualTotal

Write-Host ""
Write-Host "=== ANALYSE DE CHEVAUCHEMENT ===" -ForegroundColor Cyan
Write-Host "Lignes couvertes par tests normaux uniquement : $($normalLinesCovered - $overlap)" -ForegroundColor Green
Write-Host "Lignes couvertes par tests STA uniquement      : $($staLinesCovered - $overlap)" -ForegroundColor Blue
Write-Host "Lignes couvertes par LES DEUX types de tests   : $overlap" -ForegroundColor Yellow
Write-Host "Total unique (verification)                    : $combinedLinesCovered" -ForegroundColor White

# Pourcentages
$normalOnlyPercent = [math]::Round((($normalLinesCovered - $overlap) / $totalLines) * 100, 2)
$staOnlyPercent = [math]::Round((($staLinesCovered - $overlap) / $totalLines) * 100, 2)
$overlapPercent = [math]::Round(($overlap / $totalLines) * 100, 2)

Write-Host ""
Write-Host "=== REPARTITION EN POURCENTAGES ===" -ForegroundColor Cyan
Write-Host "Couverture normale uniquement : $normalOnlyPercent%" -ForegroundColor Green
Write-Host "Couverture STA uniquement     : $staOnlyPercent%" -ForegroundColor Blue
Write-Host "Couverture chevauchante       : $overlapPercent%" -ForegroundColor Yellow
Write-Host "Total (verification)          : $combinedLineRate%" -ForegroundColor White

# Analyse par classe (top 10 des classes avec le plus de lignes)
Write-Host ""
Write-Host "=== ANALYSE PAR CLASSE (TOP 10) ===" -ForegroundColor Cyan

$classAnalysis = @()

# Parcourir les classes dans le rapport combine
foreach ($package in $combinedXml.coverage.packages.package) {
    foreach ($class in $package.classes.class) {
        $className = $class.name
        $classLinesCovered = [int]$class.'lines-covered'
        $classLinesValid = [int]$class.'lines-valid'
        $classLineRate = if ($classLinesValid -gt 0) { [math]::Round(($classLinesCovered / $classLinesValid) * 100, 2) } else { 0 }
        
        if ($classLinesValid -gt 0) {
            $classAnalysis += [PSCustomObject]@{
                ClassName = $className
                LinesCovered = $classLinesCovered
                LinesValid = $classLinesValid
                LineRate = $classLineRate
            }
        }
    }
}

# Trier par nombre de lignes valides (plus grandes classes)
$topClasses = $classAnalysis | Sort-Object LinesValid -Descending | Select-Object -First 10

foreach ($class in $topClasses) {
    $displayName = $class.ClassName
    if ($displayName.Length -gt 50) {
        $displayName = "..." + $displayName.Substring($displayName.Length - 47)
    }
    
    Write-Host ("  {0,-50} : {1,6}% ({2,4}/{3,4} lignes)" -f $displayName, $class.LineRate, $class.LinesCovered, $class.LinesValid) -ForegroundColor White
}

# Recommandations
Write-Host ""
Write-Host "=== RECOMMANDATIONS ===" -ForegroundColor Cyan

if ($overlap -gt 100) {
    Write-Host "✓ Bon chevauchement ($overlap lignes) - Les deux types de tests se completent bien" -ForegroundColor Green
} else {
    Write-Host "⚠ Faible chevauchement ($overlap lignes) - Considerez d'ajouter plus de tests STA" -ForegroundColor Yellow
}

if ($staOnlyPercent -lt 2) {
    Write-Host "⚠ Peu de couverture STA unique ($staOnlyPercent%) - Potentiel d'expansion des tests STA" -ForegroundColor Yellow
} else {
    Write-Host "✓ Bonne couverture STA unique ($staOnlyPercent%) - Les tests STA apportent une valeur ajoutee" -ForegroundColor Green
}

$efficiency = [math]::Round(($combinedLineRate / ($normalLineRate + $staLineRate)) * 100, 1)
Write-Host "Efficacite de la combinaison: $efficiency% (plus c'est proche de 100%, moins il y a de redondance)" -ForegroundColor Gray

Write-Host ""
Write-Host "=== PROCHAINES ETAPES SUGGEREES ===" -ForegroundColor Cyan
Write-Host "1. Examiner les classes avec 0% de couverture pour identifier les candidats aux tests STA" -ForegroundColor White
Write-Host "2. Analyser les classes avec couverture partielle pour optimiser les tests existants" -ForegroundColor White
Write-Host "3. Considerer d'ajouter des tests STA pour les classes UI non couvertes" -ForegroundColor White

Write-Host ""
Write-Host "=== ANALYSE TERMINEE ===" -ForegroundColor Cyan
