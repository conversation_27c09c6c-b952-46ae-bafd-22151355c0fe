using System;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.ViewModels;

namespace ClipboardPlus.Diagnostics
{
    /// <summary>
    /// Interface pour le diagnostic des opérations de suppression d'éléments
    /// </summary>
    public interface IDeletionDiagnostic
    {


        /// <summary>
        /// Nouvelle implémentation SOLID pour la journalisation du début des suppressions
        /// Remplace LogDeletionStart avec une architecture modulaire et testable
        /// </summary>
        /// <param name="item">L'élément à supprimer</param>
        /// <param name="viewModel">Le ViewModel actuel</param>
        void LogDeletionStart_V2(ClipboardItem? item, ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Nouvelle implémentation SOLID pour la journalisation du début des suppressions avec message personnalisé
        /// Remplace LogDeletionStart avec une architecture modulaire et testable
        /// </summary>
        /// <param name="item">L'élément à supprimer (peut être null pour les opérations en lot)</param>
        /// <param name="viewModel">Le ViewModel actuel</param>
        /// <param name="message">Message personnalisé décrivant l'opération</param>
        void LogDeletionStart_V2(ClipboardItem? item, ClipboardHistoryViewModel viewModel, string message);

        /// <summary>
        /// Journalise le résultat d'une tentative de suppression
        /// </summary>
        /// <param name="success">Indique si la suppression a réussi</param>
        /// <param name="item">L'élément concerné</param>
        /// <param name="message">Message optionnel</param>
        void LogDeletionResult(bool success, ClipboardItem? item, string? message = null);

        /// <summary>
        /// Journalise le résultat d'une tentative de suppression avec ViewModel
        /// </summary>
        /// <param name="success">Indique si la suppression a réussi</param>
        /// <param name="item">L'élément concerné</param>
        /// <param name="message">Message optionnel</param>
        /// <param name="viewModel">ViewModel pour validation avancée</param>
        void LogDeletionResult(bool success, ClipboardItem? item, string? message, ClipboardHistoryViewModel viewModel);

        /// <summary>
        /// Journalise une exception survenue pendant la suppression
        /// </summary>
        /// <param name="ex">L'exception</param>
        /// <param name="context">Le contexte dans lequel l'exception s'est produite</param>
        void LogDeletionException(Exception ex, string context);

        /// <summary>
        /// Journalise l'état de la collection après une suppression
        /// </summary>
        /// <param name="viewModel">Le ViewModel contenant la collection</param>
        /// <param name="context">Le contexte de la journalisation</param>
        void LogCollectionState(ClipboardHistoryViewModel viewModel, string context);
    }
}