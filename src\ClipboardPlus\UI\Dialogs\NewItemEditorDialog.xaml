<Window x:Class="ClipboardPlus.UI.Dialogs.NewItemEditorDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ClipboardPlus.UI.Dialogs"
        xmlns:converters="clr-namespace:ClipboardPlus.UI.Converters"
        mc:Ignorable="d"
        Title="Créer un nouvel élément" 
        Width="450" MinWidth="350"
        SizeToContent="Height" MinHeight="250"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResizeWithGrip">
    
    <Window.Resources>
        <converters:IntToBooleanConverter x:Key="IntToBooleanConverter"/>
    </Window.Resources>
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- En-tête -->
        <TextBlock Grid.Row="0" 
                   Text="Ajouter un nouvel élément au presse-papiers"
                   FontSize="16" 
                   FontWeight="SemiBold"
                   Margin="0,0,0,10"/>
        
        <!-- Zone d'édition du contenu -->
        <Border Grid.Row="1" 
                BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}" 
                BorderThickness="1" 
                Margin="0,5">
            <TextBox x:Name="ContentTextBox"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     VerticalScrollBarVisibility="Auto"
                     HorizontalScrollBarVisibility="Auto"
                     Padding="8"
                     MinLines="5"
                     MaxLines="20"
                     Text="{Binding NewItemTextContent, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                     FontFamily="Consolas"/>
        </Border>
        
        <!-- Boutons d'action -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,10,0,0">
            <Button x:Name="SaveButton"
                    Content="Enregistrer"
                    Width="80"
                    Height="22"
                    Margin="5,0"
                    Command="{Binding FinalizeAndSaveNewItemCommand}"
                    IsEnabled="{Binding ElementName=ContentTextBox, Path=Text.Length, Converter={StaticResource IntToBooleanConverter}}"
                    IsDefault="True"/>

            <Button x:Name="CancelButton"
                    Content="Annuler"
                    Width="80"
                    Height="22"
                    Margin="5,0"
                    Command="{Binding DiscardNewItemCreationCommand}"
                    IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window> 