using System.Runtime.CompilerServices;
using CommunityToolkit.Mvvm.ComponentModel;

namespace ClipboardPlus.UI.ViewModels
{
    /// <summary>
    /// Classe de base pour tous les ViewModels de l'application.
    /// </summary>
    public abstract class ViewModelBase : ObservableObject
    {
        /// <summary>
        /// Définit la valeur d'un champ et déclenche l'événement PropertyChanged si la valeur a changé.
        /// </summary>
        /// <typeparam name="T">Type de la propriété.</typeparam>
        /// <param name="field">Référence au champ à définir.</param>
        /// <param name="value">Nouvelle valeur.</param>
        /// <param name="propertyName">Nom de la propriété (détecté automatiquement).</param>
        /// <returns>True si la valeur a changé, sinon false.</returns>
        protected new bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
} 