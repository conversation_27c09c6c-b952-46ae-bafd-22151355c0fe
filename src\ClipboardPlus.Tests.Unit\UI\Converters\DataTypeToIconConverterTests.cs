using System;
using System.Globalization;
using System.Reflection;
using NUnit.Framework;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.UI.Converters;

namespace ClipboardPlus.Tests.Unit.UI.Converters
{
    [TestFixture]
    public class DataTypeToIconConverterTests
    {
        private DataTypeToIconConverter _converter = null!;

        [SetUp]
        public void Initialize()
        {
            _converter = new DataTypeToIconConverter();
        }

        [Test]
        public void Convert_WithTextDataType_ReturnsCorrectIcon()
        {
            // Arrange
            object value = ClipboardDataType.Text;
            Type targetType = typeof(string);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            var result = _converter.Convert(value, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.InstanceOf<string>(), "Le résultat devrait être une chaîne de caractères");
            Assert.That(result, Is.EqualTo("\uE8C4"), "L'icône pour le type Text devrait être correcte");
        }

        [Test]
        public void Convert_WithRtfDataType_ReturnsCorrectIcon()
        {
            // Arrange
            object value = ClipboardDataType.Rtf;
            Type targetType = typeof(string);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            var result = _converter.Convert(value, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.InstanceOf<string>(), "Le résultat devrait être une chaîne de caractères");
            Assert.That(result, Is.EqualTo("\uE8E5"), "L'icône pour le type Rtf devrait être correcte");
        }

        [Test]
        public void Convert_WithHtmlDataType_ReturnsCorrectIcon()
        {
            // Arrange
            object value = ClipboardDataType.Html;
            Type targetType = typeof(string);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            var result = _converter.Convert(value, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.InstanceOf<string>(), "Le résultat devrait être une chaîne de caractères");
            Assert.That(result, Is.EqualTo("\uE8CD"), "L'icône pour le type Html devrait être correcte");
        }

        [Test]
        public void Convert_WithImageDataType_ReturnsCorrectIcon()
        {
            // Arrange
            object value = ClipboardDataType.Image;
            Type targetType = typeof(string);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            var result = _converter.Convert(value, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.InstanceOf<string>(), "Le résultat devrait être une chaîne de caractères");
            Assert.That(result, Is.EqualTo("\uEB9F"), "L'icône pour le type Image devrait être correcte");
        }

        [Test]
        public void Convert_WithFilePathDataType_ReturnsCorrectIcon()
        {
            // Arrange
            object value = ClipboardDataType.FilePath;
            Type targetType = typeof(string);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            var result = _converter.Convert(value, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.InstanceOf<string>(), "Le résultat devrait être une chaîne de caractères");
            Assert.That(result, Is.EqualTo("\uEC50"), "L'icône pour le type FilePath devrait être correcte");
        }

        [Test]
        public void Convert_WithNullValue_ReturnsNull()
        {
            // Arrange
            object? value = null;
            Type targetType = typeof(string);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            var result = _converter.Convert(value!, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Null, "Le résultat devrait être null pour une valeur null");
        }

        [Test]
        public void Convert_WithDifferentTargetType_StillReturnsString()
        {
            // Arrange
            object value = ClipboardDataType.Text;
            Type targetType = typeof(object); // Un type cible différent
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            var result = _converter.Convert(value, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.InstanceOf<string>(), "Le résultat devrait toujours être une chaîne de caractères");
        }

        [Test]
        public void Convert_WithParameter_IgnoresParameter()
        {
            // Arrange
            object value = ClipboardDataType.Text;
            Type targetType = typeof(string);
            object parameter = "Un paramètre quelconque";
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            var result = _converter.Convert(value, targetType, parameter, culture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.EqualTo("\uE8C4"), "Le paramètre ne devrait pas affecter le résultat");
        }

        [Test]
        public void Convert_WithDifferentCulture_ReturnsTheSameResult()
        {
            // Arrange
            object value = ClipboardDataType.Text;
            Type targetType = typeof(string);
            object? parameter = null;

            // Act - Essayer plusieurs cultures différentes
            var resultInvariant = _converter.Convert(value, targetType, parameter!, CultureInfo.InvariantCulture);
            var resultFrench = _converter.Convert(value, targetType, parameter!, new CultureInfo("fr-FR"));
            var resultEnglish = _converter.Convert(value, targetType, parameter!, new CultureInfo("en-US"));

            // Assert - Les résultats devraient être identiques quelle que soit la culture
            Assert.That(resultFrench, Is.EqualTo(resultInvariant), "La culture ne devrait pas affecter le résultat");
            Assert.That(resultEnglish, Is.EqualTo(resultInvariant), "La culture ne devrait pas affecter le résultat");
        }

        [Test]
        public void Convert_WithUnknownDataType_ReturnsDefaultIcon()
        {
            // Arrange
            // Pour simuler un type de données inconnu, nous allons créer une valeur d'énumération
            // qui n'est pas définie explicitement dans l'énumération ClipboardDataType
            ClipboardDataType unknownType = (ClipboardDataType)999;
            Type targetType = typeof(string);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            var result = _converter.Convert(unknownType, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Not.Null, "Le résultat ne devrait pas être null");
            Assert.That(result, Is.EqualTo("\uEA92"), "L'icône par défaut devrait être utilisée pour un type inconnu");
        }

        [Test]
        public void GetIconCharacter_ReturnsCorrectValuesForAllDataTypes()
        {
            // Arrange
            var method = typeof(DataTypeToIconConverter).GetMethod("GetIconCharacter",
                BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.That(method, Is.Not.Null, "La méthode GetIconCharacter n'a pas été trouvée");

            // Act & Assert - Vérifier que tous les types d'énumération ont une icône correspondante
            if (method != null)
            {
                foreach (ClipboardDataType dataType in Enum.GetValues(typeof(ClipboardDataType)))
                {
                    var result = method.Invoke(_converter, new object[] { dataType });
                    Assert.That(result, Is.Not.Null, $"GetIconCharacter doit retourner une valeur pour {dataType}");
                    Assert.That(result, Is.InstanceOf<string>(), "Le résultat doit être une chaîne de caractères");
                    if (result is string stringResult)
                    {
                        Assert.That(stringResult.Length > 0, Is.True, "L'icône ne doit pas être une chaîne vide");
                    }
                }
            }
        }

        [Test]
        public void Convert_WithInvalidValue_ReturnsNull()
        {
            // Arrange
            object value = "Not a ClipboardDataType";
            Type targetType = typeof(string);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act
            var result = _converter.Convert(value, targetType, parameter!, culture);

            // Assert
            Assert.That(result, Is.Null, "Le résultat devrait être null pour une valeur non valide");
        }
        
        [Test]
        public void ConvertBack_ThrowsNotImplementedException()
        {
            // Arrange
            object value = "\uE8C4"; // Icône de texte
            Type targetType = typeof(ClipboardDataType);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act & Assert
            Assert.Throws<NotImplementedException>(() => _converter.ConvertBack(value, targetType, parameter!, culture));
        }

        [Test]
        public void ExceptionMessage_ForConvertBack_IsCorrect()
        {
            // Arrange
            object value = "\uE8C4";
            Type targetType = typeof(ClipboardDataType);
            object? parameter = null;
            CultureInfo culture = CultureInfo.InvariantCulture;

            // Act & Assert
            try
            {
                _converter.ConvertBack(value, targetType, parameter!, culture);
                Assert.Fail("Une exception aurait dû être levée");
            }
            catch (NotImplementedException ex)
            {
                Assert.That(ex.Message, Is.EqualTo("La conversion inverse n'est pas prise en charge."),
                    "Le message d'exception devrait indiquer que la conversion inverse n'est pas prise en charge");
            }
        }
    }
} 