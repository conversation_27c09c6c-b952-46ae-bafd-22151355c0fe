using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ClipboardPlus.Modules.Core
{
    /// <summary>
    /// Classe de base abstraite pour tous les modules.
    /// 
    /// Cette classe fournit une implémentation de base du cycle de vie des modules
    /// et des mécanismes de gestion d'état communs.
    /// </summary>
    public abstract class ModuleBase : IModule
    {
        private readonly object _stateLock = new object();
        private ModuleState _state = ModuleState.Created;
        private bool _disposed = false;

        /// <inheritdoc />
        public abstract string ModuleName { get; }

        /// <inheritdoc />
        public abstract Version ModuleVersion { get; }

        /// <inheritdoc />
        public ModuleState State
        {
            get
            {
                lock (_stateLock)
                {
                    return _state;
                }
            }
            private set
            {
                ModuleState previousState;
                lock (_stateLock)
                {
                    previousState = _state;
                    _state = value;
                }

                if (previousState != value)
                {
                    OnStateChanged(new ModuleStateChangedEventArgs(previousState, value));
                }
            }
        }

        /// <inheritdoc />
        public bool IsInitialized => State == ModuleState.Initialized || State == ModuleState.Running;

        /// <inheritdoc />
        public bool IsRunning => State == ModuleState.Running;

        /// <inheritdoc />
        public event EventHandler<ModuleStateChangedEventArgs>? StateChanged;

        /// <summary>
        /// Déclenche l'événement StateChanged.
        /// </summary>
        protected virtual void OnStateChanged(ModuleStateChangedEventArgs e)
        {
            StateChanged?.Invoke(this, e);
        }

        /// <inheritdoc />
        public async Task InitializeAsync()
        {
            ThrowIfDisposed();
            
            if (State != ModuleState.Created)
            {
                throw new ModuleOperationException(ModuleName, State, "Initialize", 
                    $"Module can only be initialized from Created state, current state: {State}");
            }

            State = ModuleState.Initializing;

            try
            {
                await OnInitializeAsync();
                State = ModuleState.Initialized;
            }
            catch (Exception ex)
            {
                State = ModuleState.Error;
                throw new ModuleInitializationException(ModuleName, State, 
                    $"Failed to initialize module {ModuleName}", ex);
            }
        }

        /// <inheritdoc />
        public async Task StartAsync()
        {
            ThrowIfDisposed();
            
            if (State != ModuleState.Initialized)
            {
                throw new ModuleOperationException(ModuleName, State, "Start", 
                    $"Module must be initialized before starting, current state: {State}");
            }

            State = ModuleState.Starting;

            try
            {
                await OnStartAsync();
                State = ModuleState.Running;
            }
            catch (Exception ex)
            {
                State = ModuleState.Error;
                throw new ModuleOperationException(ModuleName, State, "Start", 
                    $"Failed to start module {ModuleName}", ex);
            }
        }

        /// <inheritdoc />
        public async Task StopAsync()
        {
            ThrowIfDisposed();
            
            if (State != ModuleState.Running)
            {
                throw new ModuleOperationException(ModuleName, State, "Stop", 
                    $"Module must be running to be stopped, current state: {State}");
            }

            State = ModuleState.Stopping;

            try
            {
                await OnStopAsync();
                State = ModuleState.Stopped;
            }
            catch (Exception ex)
            {
                State = ModuleState.Error;
                throw new ModuleOperationException(ModuleName, State, "Stop", 
                    $"Failed to stop module {ModuleName}", ex);
            }
        }

        /// <inheritdoc />
        public async Task ResetAsync()
        {
            ThrowIfDisposed();
            
            if (State == ModuleState.Running)
            {
                await StopAsync();
            }

            try
            {
                await OnResetAsync();
                State = ModuleState.Initialized;
            }
            catch (Exception ex)
            {
                State = ModuleState.Error;
                throw new ModuleOperationException(ModuleName, State, "Reset", 
                    $"Failed to reset module {ModuleName}", ex);
            }
        }

        /// <inheritdoc />
        public virtual ModuleHealthResult CheckHealth()
        {
            ThrowIfDisposed();

            try
            {
                var details = new Dictionary<string, object>
                {
                    ["State"] = State.ToString(),
                    ["IsInitialized"] = IsInitialized,
                    ["IsRunning"] = IsRunning,
                    ["ModuleName"] = ModuleName,
                    ["ModuleVersion"] = ModuleVersion.ToString()
                };

                var customHealth = OnCheckHealth();
                if (customHealth != null)
                {
                    foreach (var kvp in customHealth)
                    {
                        details[kvp.Key] = kvp.Value;
                    }
                }

                bool isHealthy = State == ModuleState.Running || State == ModuleState.Initialized;
                string message = isHealthy ? "Module is healthy" : $"Module is in {State} state";

                return new ModuleHealthResult(isHealthy, message, details);
            }
            catch (Exception ex)
            {
                return ModuleHealthResult.Unhealthy($"Health check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Méthode abstraite pour l'initialisation spécifique du module.
        /// </summary>
        protected abstract Task OnInitializeAsync();

        /// <summary>
        /// Méthode abstraite pour le démarrage spécifique du module.
        /// </summary>
        protected abstract Task OnStartAsync();

        /// <summary>
        /// Méthode abstraite pour l'arrêt spécifique du module.
        /// </summary>
        protected abstract Task OnStopAsync();

        /// <summary>
        /// Méthode virtuelle pour la réinitialisation spécifique du module.
        /// Par défaut, ne fait rien.
        /// </summary>
        protected virtual Task OnResetAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// Méthode virtuelle pour la vérification de santé spécifique du module.
        /// Retourne des détails additionnels sur l'état de santé.
        /// </summary>
        protected virtual Dictionary<string, object>? OnCheckHealth()
        {
            return null;
        }

        /// <summary>
        /// Vérifie si le module a été disposé et lève une exception si c'est le cas.
        /// </summary>
        protected void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(ModuleName);
            }
        }

        /// <inheritdoc />
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Libère les ressources du module.
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                State = ModuleState.Disposing;

                try
                {
                    OnDispose();
                }
                catch (Exception ex)
                {
                    // Selon les bonnes pratiques .NET, Dispose() ne doit pas lancer d'exception
                    // On log l'erreur mais on ne la propage pas
                    System.Diagnostics.Debug.WriteLine($"Exception during dispose of module {ModuleName}: {ex.Message}");
                }
                finally
                {
                    _disposed = true;
                    State = ModuleState.Disposed;
                }
            }
        }

        /// <summary>
        /// Méthode virtuelle pour le nettoyage spécifique du module.
        /// </summary>
        protected virtual void OnDispose()
        {
            // Par défaut, ne fait rien
        }
    }
}
