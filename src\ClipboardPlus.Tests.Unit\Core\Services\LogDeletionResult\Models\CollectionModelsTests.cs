using System;
using System.Collections.Generic;
using System.Linq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.LogDeletionResult.Models;
using NUnit.Framework;

namespace ClipboardPlus.Tests.Unit.Core.Services.LogDeletionResult.Models
{
    [TestFixture]
    public class CollectionComparisonResultTests
    {
        [Test]
        public void Constructor_InitializesWithDefaults()
        {
            // Act
            var result = new CollectionComparisonResult();

            // Assert
            Assert.IsNotNull(result.ComparisonId);
            Assert.AreNotEqual(Guid.Empty, result.ComparisonId);
            Assert.IsFalse(result.AreIdentical);
            Assert.AreEqual(0, result.Collection1Count);
            Assert.AreEqual(0, result.Collection2Count);
            Assert.IsNotNull(result.OnlyInCollection1);
            Assert.IsNotNull(result.OnlyInCollection2);
            Assert.IsNotNull(result.CommonItems);
            Assert.IsNotNull(result.Differences);
            Assert.IsTrue((DateTime.Now - result.ComparisonTimestamp).TotalSeconds < 1);
        }

        [Test]
        public void Properties_CanBeSetAndRetrieved()
        {
            // Arrange
            var result = new CollectionComparisonResult();
            var testId = Guid.NewGuid();
            var testTimestamp = DateTime.Now.AddMinutes(-5);
            var testItem1 = new ClipboardItem { Id = 1, TextPreview = "Test1" };
            var testItem2 = new ClipboardItem { Id = 2, TextPreview = "Test2" };
            var testDifference = new CollectionDifference
            {
                Type = DifferenceType.ItemAdded,
                Description = "Test difference"
            };

            // Act
            result.ComparisonId = testId;
            result.AreIdentical = true;
            result.Collection1Count = 5;
            result.Collection2Count = 3;
            result.ComparisonTimestamp = testTimestamp;
            result.OnlyInCollection1.Add(testItem1);
            result.OnlyInCollection2.Add(testItem2);
            result.CommonItems.Add(testItem1);
            result.Differences.Add(testDifference);

            // Assert
            Assert.AreEqual(testId, result.ComparisonId);
            Assert.IsTrue(result.AreIdentical);
            Assert.AreEqual(5, result.Collection1Count);
            Assert.AreEqual(3, result.Collection2Count);
            Assert.AreEqual(testTimestamp, result.ComparisonTimestamp);
            Assert.AreEqual(1, result.OnlyInCollection1.Count);
            Assert.AreEqual(testItem1, result.OnlyInCollection1[0]);
            Assert.AreEqual(1, result.OnlyInCollection2.Count);
            Assert.AreEqual(testItem2, result.OnlyInCollection2[0]);
            Assert.AreEqual(1, result.CommonItems.Count);
            Assert.AreEqual(testItem1, result.CommonItems[0]);
            Assert.AreEqual(1, result.Differences.Count);
            Assert.AreEqual(testDifference, result.Differences[0]);
        }

        [Test]
        public void Collections_CanBeModified()
        {
            // Arrange
            var result = new CollectionComparisonResult();
            var items = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, TextPreview = "Item1" },
                new ClipboardItem { Id = 2, TextPreview = "Item2" },
                new ClipboardItem { Id = 3, TextPreview = "Item3" }
            };

            // Act
            result.OnlyInCollection1.AddRange(items);
            result.OnlyInCollection2.AddRange(items.Take(2));
            result.CommonItems.AddRange(items.Skip(1));

            // Assert
            Assert.AreEqual(3, result.OnlyInCollection1.Count);
            Assert.AreEqual(2, result.OnlyInCollection2.Count);
            Assert.AreEqual(2, result.CommonItems.Count);
        }

        [Test]
        public void ComparisonId_IsUniqueForEachInstance()
        {
            // Act
            var result1 = new CollectionComparisonResult();
            var result2 = new CollectionComparisonResult();

            // Assert
            Assert.AreNotEqual(result1.ComparisonId, result2.ComparisonId);
        }
    }

    [TestFixture]
    public class CollectionDifferenceTests
    {
        [Test]
        public void Constructor_InitializesWithDefaults()
        {
            // Act
            var difference = new CollectionDifference();

            // Assert
            Assert.AreEqual(DifferenceType.ItemAdded, difference.Type); // Valeur par défaut de l'enum
            Assert.AreEqual(string.Empty, difference.Description);
            Assert.IsNull(difference.AffectedItem);
            Assert.IsNull(difference.PositionInCollection1);
            Assert.IsNull(difference.PositionInCollection2);
        }

        [Test]
        public void Properties_CanBeSetAndRetrieved()
        {
            // Arrange
            var difference = new CollectionDifference();
            var testItem = new ClipboardItem { Id = 1, TextPreview = "Test item" };

            // Act
            difference.Type = DifferenceType.ItemRemoved;
            difference.Description = "Item was removed";
            difference.AffectedItem = testItem;
            difference.PositionInCollection1 = 5;
            difference.PositionInCollection2 = null;

            // Assert
            Assert.AreEqual(DifferenceType.ItemRemoved, difference.Type);
            Assert.AreEqual("Item was removed", difference.Description);
            Assert.AreEqual(testItem, difference.AffectedItem);
            Assert.AreEqual(5, difference.PositionInCollection1);
            Assert.IsNull(difference.PositionInCollection2);
        }

        [Test]
        public void Type_CanBeSetToAllEnumValues()
        {
            // Arrange
            var difference = new CollectionDifference();

            // Act & Assert
            foreach (DifferenceType type in Enum.GetValues<DifferenceType>())
            {
                difference.Type = type;
                Assert.AreEqual(type, difference.Type);
            }
        }

        [Test]
        public void AffectedItem_CanBeNull()
        {
            // Arrange
            var difference = new CollectionDifference();

            // Act
            difference.AffectedItem = null;

            // Assert
            Assert.IsNull(difference.AffectedItem);
        }

        [Test]
        public void Positions_CanBeNullOrPositive()
        {
            // Arrange
            var difference = new CollectionDifference();

            // Act & Assert - Null values
            difference.PositionInCollection1 = null;
            difference.PositionInCollection2 = null;
            Assert.IsNull(difference.PositionInCollection1);
            Assert.IsNull(difference.PositionInCollection2);

            // Act & Assert - Positive values
            difference.PositionInCollection1 = 0;
            difference.PositionInCollection2 = 10;
            Assert.AreEqual(0, difference.PositionInCollection1);
            Assert.AreEqual(10, difference.PositionInCollection2);

            // Act & Assert - Negative values (should be allowed)
            difference.PositionInCollection1 = -1;
            difference.PositionInCollection2 = -5;
            Assert.AreEqual(-1, difference.PositionInCollection1);
            Assert.AreEqual(-5, difference.PositionInCollection2);
        }

        [Test]
        public void Description_CanBeEmptyOrWhitespace()
        {
            // Arrange
            var difference = new CollectionDifference();

            // Act & Assert - Empty string
            difference.Description = string.Empty;
            Assert.AreEqual(string.Empty, difference.Description);

            // Act & Assert - Whitespace
            difference.Description = "   ";
            Assert.AreEqual("   ", difference.Description);

            // Act & Assert - Null (should not throw)
            Assert.DoesNotThrow(() => difference.Description = null!);
        }
    }

    [TestFixture]
    public class DifferenceTypeTests
    {
        [Test]
        public void DifferenceType_HasExpectedValues()
        {
            // Act
            var values = Enum.GetValues<DifferenceType>();

            // Assert
            Assert.Contains(DifferenceType.ItemAdded, values);
            Assert.Contains(DifferenceType.ItemRemoved, values);
            Assert.Contains(DifferenceType.ItemModified, values);
            Assert.Contains(DifferenceType.PositionChanged, values);
            Assert.Contains(DifferenceType.PropertyChanged, values);
        }

        [Test]
        public void DifferenceType_CanBeConvertedToString()
        {
            // Act & Assert
            Assert.AreEqual("ItemAdded", DifferenceType.ItemAdded.ToString());
            Assert.AreEqual("ItemRemoved", DifferenceType.ItemRemoved.ToString());
            Assert.AreEqual("ItemModified", DifferenceType.ItemModified.ToString());
            Assert.AreEqual("PositionChanged", DifferenceType.PositionChanged.ToString());
            Assert.AreEqual("PropertyChanged", DifferenceType.PropertyChanged.ToString());
        }

        [Test]
        public void DifferenceType_CanBeParsedFromString()
        {
            // Act & Assert
            Assert.AreEqual(DifferenceType.ItemAdded, Enum.Parse<DifferenceType>("ItemAdded"));
            Assert.AreEqual(DifferenceType.ItemRemoved, Enum.Parse<DifferenceType>("ItemRemoved"));
            Assert.AreEqual(DifferenceType.ItemModified, Enum.Parse<DifferenceType>("ItemModified"));
            Assert.AreEqual(DifferenceType.PositionChanged, Enum.Parse<DifferenceType>("PositionChanged"));
            Assert.AreEqual(DifferenceType.PropertyChanged, Enum.Parse<DifferenceType>("PropertyChanged"));
        }
    }

    [TestFixture]
    public class CollectionComparisonResultIntegrationTests
    {
        [Test]
        public void CompleteScenario_WithRealData_WorksCorrectly()
        {
            // Arrange
            var result = new CollectionComparisonResult();
            var item1 = new ClipboardItem { Id = 1, TextPreview = "Common item" };
            var item2 = new ClipboardItem { Id = 2, TextPreview = "Only in collection 1" };
            var item3 = new ClipboardItem { Id = 3, TextPreview = "Only in collection 2" };

            var difference1 = new CollectionDifference
            {
                Type = DifferenceType.ItemAdded,
                Description = "Item added to collection 2",
                AffectedItem = item3,
                PositionInCollection2 = 1
            };

            var difference2 = new CollectionDifference
            {
                Type = DifferenceType.ItemRemoved,
                Description = "Item removed from collection 2",
                AffectedItem = item2,
                PositionInCollection1 = 0
            };

            // Act
            result.AreIdentical = false;
            result.Collection1Count = 2;
            result.Collection2Count = 2;
            result.CommonItems.Add(item1);
            result.OnlyInCollection1.Add(item2);
            result.OnlyInCollection2.Add(item3);
            result.Differences.AddRange(new[] { difference1, difference2 });

            // Assert
            Assert.IsFalse(result.AreIdentical);
            Assert.AreEqual(2, result.Collection1Count);
            Assert.AreEqual(2, result.Collection2Count);
            Assert.AreEqual(1, result.CommonItems.Count);
            Assert.AreEqual(1, result.OnlyInCollection1.Count);
            Assert.AreEqual(1, result.OnlyInCollection2.Count);
            Assert.AreEqual(2, result.Differences.Count);

            // Vérifier les différences
            var addedDiff = result.Differences.First(d => d.Type == DifferenceType.ItemAdded);
            var removedDiff = result.Differences.First(d => d.Type == DifferenceType.ItemRemoved);

            Assert.AreEqual(item3, addedDiff.AffectedItem);
            Assert.AreEqual(item2, removedDiff.AffectedItem);
        }
    }
}
