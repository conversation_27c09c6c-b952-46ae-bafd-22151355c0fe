using System;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Service de gestion des feature flags pour contrôler les migrations progressives.
    /// 
    /// Ce service permet d'activer/désactiver des fonctionnalités de manière contrôlée
    /// pour faciliter les migrations et les tests A/B.
    /// </summary>
    public interface IFeatureFlagService
    {
        /// <summary>
        /// Vérifie si une fonctionnalité est activée.
        /// </summary>
        /// <param name="featureName">Nom de la fonctionnalité</param>
        /// <returns>True si la fonctionnalité est activée</returns>
        bool IsFeatureEnabled(string featureName);

        /// <summary>
        /// Active ou désactive une fonctionnalité.
        /// </summary>
        /// <param name="featureName">Nom de la fonctionnalité</param>
        /// <param name="enabled">État souhaité</param>
        void SetFeatureEnabled(string featureName, bool enabled);

        /// <summary>
        /// Obtient le pourcentage de rollout pour une fonctionnalité.
        /// </summary>
        /// <param name="featureName">Nom de la fonctionnalité</param>
        /// <returns>Pourcentage de rollout (0-100)</returns>
        int GetRolloutPercentage(string featureName);

        /// <summary>
        /// Définit le pourcentage de rollout pour une fonctionnalité.
        /// </summary>
        /// <param name="featureName">Nom de la fonctionnalité</param>
        /// <param name="percentage">Pourcentage de rollout (0-100)</param>
        void SetRolloutPercentage(string featureName, int percentage);

        /// <summary>
        /// Vérifie si l'utilisateur actuel fait partie du rollout.
        /// </summary>
        /// <param name="featureName">Nom de la fonctionnalité</param>
        /// <param name="userId">Identifiant utilisateur (optionnel)</param>
        /// <returns>True si l'utilisateur fait partie du rollout</returns>
        bool IsUserInRollout(string featureName, string? userId = null);
    }

    /// <summary>
    /// Constantes pour les feature flags du refactoring HistoryChanged.
    /// </summary>
    public static class HistoryChangedFeatureFlags
    {
        /// <summary>
        /// Feature flag principal pour activer la nouvelle méthode HistoryChanged.
        /// </summary>
        public const string USE_REFACTORED_HISTORY_CHANGED = "UseRefactoredHistoryChanged";

        /// <summary>
        /// Feature flag pour activer le monitoring comparatif.
        /// </summary>
        public const string ENABLE_HISTORY_CHANGED_MONITORING = "EnableHistoryChangedMonitoring";

        /// <summary>
        /// Feature flag pour activer les logs détaillés de migration.
        /// </summary>
        public const string ENABLE_MIGRATION_LOGGING = "EnableMigrationLogging";


    }
}
