using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.DataModels;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    [TestFixture]
    public class SettingsManagerTests
    {
        private Mock<IPersistenceService> _mockPersistenceService = new Mock<IPersistenceService>();
        private SettingsManager _settingsManager = null!;
        private Dictionary<string, string> _testSettings = new Dictionary<string, string>();

        [SetUp]
        public void Initialize()
        {
            _testSettings = new Dictionary<string, string>
            {
                { nameof(ApplicationSettings.MaxHistoryItems), "75" },
                { nameof(ApplicationSettings.StartWithWindows), "True" },
                { nameof(ApplicationSettings.ShortcutKeyCombination), "Ctrl+Alt+V" },
                { nameof(ApplicationSettings.MaxImageDimensionForThumbnail), "512" },
                { nameof(ApplicationSettings.MaxStorableItemSizeBytes), "5242880" }, // 5 MB
                { nameof(ApplicationSettings.ActiveThemePath), "pack://application:,,,/ClipboardPlus;component/UI/Themes/Dark.xaml" }
            };

            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockPersistenceService.Setup(m => m.GetApplicationSettingsAsync())
                .ReturnsAsync(_testSettings);
            _mockPersistenceService.Setup(m => m.SaveApplicationSettingAsync(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(Task.CompletedTask);

            _settingsManager = new SettingsManager(_mockPersistenceService.Object, null);
        }

        [Test]
        public async Task LoadSettingsAsync_LoadsSettingsFromPersistenceService()
        {
            // Act
            await _settingsManager.LoadSettingsAsync();

            // Assert
            _mockPersistenceService.Verify(m => m.GetApplicationSettingsAsync(), Times.Once);
            Assert.That(_settingsManager.MaxHistoryItems, Is.EqualTo(75));
            Assert.That(_settingsManager.StartWithWindows, Is.EqualTo(true));
            Assert.That(_settingsManager.ShortcutKeyCombination, Is.EqualTo("Ctrl+Alt+V"));
            Assert.That(_settingsManager.MaxImageDimensionForThumbnail, Is.EqualTo(512));
            Assert.That(_settingsManager.MaxStorableItemSizeBytes, Is.EqualTo(5242880));
            Assert.That(_settingsManager.ActiveThemePath, Is.EqualTo("pack://application:,,,/ClipboardPlus;component/UI/Themes/Dark.xaml"));
        }

        [Test]
        public async Task LoadSettingsAsync_WhenPersistenceServiceReturnsNull_UsesDefaultSettings()
        {
            // Arrange
            _mockPersistenceService.Setup(m => m.GetApplicationSettingsAsync())
                .ReturnsAsync(new Dictionary<string, string>()); // Simule aucune donnée persistée

            var defaultSettings = new ApplicationSettings(); // Pour obtenir toutes les valeurs par défaut
            string expectedDefaultThemePath = defaultSettings.ActiveThemePath; // Capture la valeur par défaut construite

            // Act
            await _settingsManager.LoadSettingsAsync();

            // Assert
            Assert.That(_settingsManager.MaxHistoryItems, Is.EqualTo(defaultSettings.MaxHistoryItems));
            Assert.That(_settingsManager.StartWithWindows, Is.EqualTo(defaultSettings.StartWithWindows));
            Assert.That(_settingsManager.ShortcutKeyCombination, Is.EqualTo(defaultSettings.ShortcutKeyCombination));
            Assert.That(_settingsManager.MaxImageDimensionForThumbnail, Is.EqualTo(defaultSettings.MaxImageDimensionForThumbnail));
            Assert.That(_settingsManager.MaxStorableItemSizeBytes, Is.EqualTo(defaultSettings.MaxStorableItemSizeBytes));
            Assert.That(_settingsManager.ActiveThemePath, Is.EqualTo(expectedDefaultThemePath));
            Assert.That(_settingsManager.MaxTextPreviewLength, Is.EqualTo(defaultSettings.MaxTextPreviewLength));
        }

        [Test]
        public async Task SaveSettingAsync_SavesSettingToPersistenceService()
        {
            // Act
            await _settingsManager.SaveSettingAsync(s => s.MaxHistoryItems, 100);

            // Assert
            _mockPersistenceService.Verify(m => m.SaveApplicationSettingAsync(nameof(ApplicationSettings.MaxHistoryItems), "100"), Times.Once);
        }

        [Test]
        public async Task SaveSettingAsync_ForMaxHistoryItems_UpdatesProperty()
        {
            // Act
            await _settingsManager.SaveSettingAsync(s => s.MaxHistoryItems, 100);

            // Assert
            Assert.That(_settingsManager.MaxHistoryItems, Is.EqualTo(100));
        }

        [Test]
        public async Task SaveSettingAsync_ForStartWithWindows_UpdatesProperty()
        {
            // Act
            await _settingsManager.SaveSettingAsync(s => s.StartWithWindows, true);

            // Assert
            Assert.That(_settingsManager.StartWithWindows, Is.True);
        }

        [Test]
        public async Task SaveSettingAsync_ForShortcutKeyCombination_UpdatesProperty()
        {
            // Act
            await _settingsManager.SaveSettingAsync(s => s.ShortcutKeyCombination, "Ctrl+Shift+C");

            // Assert
            Assert.That(_settingsManager.ShortcutKeyCombination, Is.EqualTo("Ctrl+Shift+C"));
        }

        [Test]
        public async Task SaveSettingAsync_ForMaxImageDimensionForThumbnail_UpdatesProperty()
        {
            // Act
            await _settingsManager.SaveSettingAsync(s => s.MaxImageDimensionForThumbnail, 256);

            // Assert
            Assert.That(_settingsManager.MaxImageDimensionForThumbnail, Is.EqualTo(256));
        }

        [Test]
        public async Task SaveSettingAsync_ForMaxStorableItemSizeBytes_UpdatesProperty()
        {
            // Act
            await _settingsManager.SaveSettingAsync(s => s.MaxStorableItemSizeBytes, 10485760); // 10 MB

            // Assert
            Assert.That(_settingsManager.MaxStorableItemSizeBytes, Is.EqualTo(10485760));
        }

        [Test]
        public async Task SaveSettingAsync_ForActiveThemePath_UpdatesProperty()
        {
            // Act
            await _settingsManager.SaveSettingAsync(s => s.ActiveThemePath, "pack://application:,,,/ClipboardPlus;component/UI/Themes/Light.xaml");

            // Assert
            Assert.That(_settingsManager.ActiveThemePath, Is.EqualTo("pack://application:,,,/ClipboardPlus;component/UI/Themes/Light.xaml"));
        }

        [Test]
        public async Task LoadSettingsAsync_WhenSpecificSettingIsMissing_UsesDefaultForMissingAndLoadsOthers()
        {
            // Arrange
            var partialSettings = new Dictionary<string, string>
            {
                // MaxHistoryItems est manquant
                { nameof(ApplicationSettings.StartWithWindows), "True" },
                { nameof(ApplicationSettings.ShortcutKeyCombination), "Ctrl+Alt+S" }
                // MaxImageDimensionForThumbnail est manquant
                // MaxStorableItemSizeBytes est manquant
                // ActiveThemePath est manquant
                // MaxTextPreviewLength est manquant
            };
            _mockPersistenceService.Setup(m => m.GetApplicationSettingsAsync())
                .ReturnsAsync(partialSettings);

            var settings = new ApplicationSettings(); // Pour obtenir les valeurs par défaut exactes
            string defaultThemePath = settings.ActiveThemePath; // Capture la valeur par défaut construite

            // Act
            await _settingsManager.LoadSettingsAsync();

            // Assert
            _mockPersistenceService.Verify(m => m.GetApplicationSettingsAsync(), Times.Once);

            // Vérifier la valeur manquante qui devrait être par défaut
            Assert.That(_settingsManager.MaxHistoryItems, Is.EqualTo(settings.MaxHistoryItems), "MaxHistoryItems devrait être la valeur par défaut.");

            // Vérifier les valeurs chargées
            Assert.That(_settingsManager.StartWithWindows, Is.EqualTo(true), "StartWithWindows devrait être la valeur chargée.");
            Assert.That(_settingsManager.ShortcutKeyCombination, Is.EqualTo("Ctrl+Alt+S"), "ShortcutKeyCombination devrait être la valeur chargée.");

            // Vérifier les autres valeurs manquantes qui devraient être par défaut
            Assert.That(_settingsManager.MaxImageDimensionForThumbnail, Is.EqualTo(settings.MaxImageDimensionForThumbnail), "MaxImageDimensionForThumbnail devrait être la valeur par défaut.");
            Assert.That(_settingsManager.MaxStorableItemSizeBytes, Is.EqualTo(settings.MaxStorableItemSizeBytes), "MaxStorableItemSizeBytes devrait être la valeur par défaut.");
            Assert.That(_settingsManager.ActiveThemePath, Is.EqualTo(defaultThemePath), "ActiveThemePath devrait être la valeur par défaut.");
            Assert.That(_settingsManager.MaxTextPreviewLength, Is.EqualTo(settings.MaxTextPreviewLength), "MaxTextPreviewLength devrait être la valeur par défaut.");
        }

        [Test]
        public async Task LoadSettingsAsync_WithMalformedValue_UsesDefaultForMalformedAndLoadsOthers()
        {
            // Arrange
            var malformedSettings = new Dictionary<string, string>
            {
                { nameof(ApplicationSettings.MaxHistoryItems), "not-an-int" }, // Valeur malformée
                { nameof(ApplicationSettings.StartWithWindows), "True" },
                { nameof(ApplicationSettings.ShortcutKeyCombination), "Ctrl+Alt+X" }
            };
            _mockPersistenceService.Setup(m => m.GetApplicationSettingsAsync())
                .ReturnsAsync(malformedSettings);

            var defaultSettings = new ApplicationSettings(); // Pour les valeurs par défaut

            // Act
            await _settingsManager.LoadSettingsAsync();

            // Assert
            _mockPersistenceService.Verify(m => m.GetApplicationSettingsAsync(), Times.Once);

            // Vérifier que la valeur malformée a conduit à l'utilisation de la valeur par défaut
            Assert.That(_settingsManager.MaxHistoryItems, Is.EqualTo(defaultSettings.MaxHistoryItems), "MaxHistoryItems devrait être la valeur par défaut à cause de la malformation.");

            // Vérifier que les autres valeurs valides sont chargées
            Assert.That(_settingsManager.StartWithWindows, Is.EqualTo(true), "StartWithWindows devrait être la valeur chargée.");
            Assert.That(_settingsManager.ShortcutKeyCombination, Is.EqualTo("Ctrl+Alt+X"), "ShortcutKeyCombination devrait être la valeur chargée.");

            // Vérifier que les autres propriétés non fournies utilisent également leurs valeurs par défaut
            Assert.That(_settingsManager.MaxImageDimensionForThumbnail, Is.EqualTo(defaultSettings.MaxImageDimensionForThumbnail));
            Assert.That(_settingsManager.ActiveThemePath, Is.EqualTo(defaultSettings.ActiveThemePath));
        }

        [Test]
        public void PropertySetters_InvokesSettingChangedEvent()
        {
            // Arrange
            // _settingsManager est initialisé avec les valeurs par défaut de ApplicationSettings
            // (MaxHistoryItems=50, StartWithWindows=false, ActiveThemePath=default_path, etc.)

            bool eventRaised = false;
            string? changedPropertyName = null;
            Action<string> eventHandler = (propertyName) =>
            {
                eventRaised = true;
                changedPropertyName = propertyName;
            };
            _settingsManager.SettingChanged += eventHandler;

            // --- Test MaxHistoryItems ---
            eventRaised = false; changedPropertyName = null;
            int newMaxHistoryItems = _settingsManager.MaxHistoryItems + 50; // Assurer un changement
            _settingsManager.MaxHistoryItems = newMaxHistoryItems;
            Assert.That(eventRaised, Is.True, "SettingChanged devrait être levé pour MaxHistoryItems.");
            Assert.That(changedPropertyName, Is.EqualTo(nameof(ApplicationSettings.MaxHistoryItems)));
            Assert.That(_settingsManager.MaxHistoryItems, Is.EqualTo(newMaxHistoryItems));

            // --- Test StartWithWindows ---
            eventRaised = false; changedPropertyName = null;
            bool newStartWithWindows = !_settingsManager.StartWithWindows; // Assurer un changement
            _settingsManager.StartWithWindows = newStartWithWindows;
            Assert.That(eventRaised, Is.True, "SettingChanged devrait être levé pour StartWithWindows.");
            Assert.That(changedPropertyName, Is.EqualTo(nameof(ApplicationSettings.StartWithWindows)));
            Assert.That(_settingsManager.StartWithWindows, Is.EqualTo(newStartWithWindows));

            // --- Test ActiveThemePath ---
            eventRaised = false; changedPropertyName = null;
            string newThemePath = _settingsManager.ActiveThemePath + "_changed"; // Assurer un changement
            _settingsManager.ActiveThemePath = newThemePath;
            Assert.That(eventRaised, Is.True, "SettingChanged devrait être levé pour ActiveThemePath.");
            Assert.That(changedPropertyName, Is.EqualTo(nameof(ApplicationSettings.ActiveThemePath)));
            Assert.That(_settingsManager.ActiveThemePath, Is.EqualTo(newThemePath));

            // --- Test ShortcutKeyCombination ---
            eventRaised = false; changedPropertyName = null;
            string newShortcut = _settingsManager.ShortcutKeyCombination + "+S"; // Assurer un changement
            _settingsManager.ShortcutKeyCombination = newShortcut;
            Assert.That(eventRaised, Is.True, "SettingChanged devrait être levé pour ShortcutKeyCombination.");
            Assert.That(changedPropertyName, Is.EqualTo(nameof(ApplicationSettings.ShortcutKeyCombination)));
            Assert.That(_settingsManager.ShortcutKeyCombination, Is.EqualTo(newShortcut));

            // --- Test MaxImageDimensionForThumbnail ---
            eventRaised = false; changedPropertyName = null;
            int newMaxDimension = _settingsManager.MaxImageDimensionForThumbnail + 100; // Assurer un changement
            _settingsManager.MaxImageDimensionForThumbnail = newMaxDimension;
            Assert.That(eventRaised, Is.True, "SettingChanged devrait être levé pour MaxImageDimensionForThumbnail.");
            Assert.That(changedPropertyName, Is.EqualTo(nameof(ApplicationSettings.MaxImageDimensionForThumbnail)));
            Assert.That(_settingsManager.MaxImageDimensionForThumbnail, Is.EqualTo(newMaxDimension));

            // --- Test MaxStorableItemSizeBytes ---
            eventRaised = false; changedPropertyName = null;
            long newMaxSize = _settingsManager.MaxStorableItemSizeBytes + 1024;
            _settingsManager.MaxStorableItemSizeBytes = newMaxSize;
            Assert.That(eventRaised, Is.True, "SettingChanged devrait être levé pour MaxStorableItemSizeBytes.");
            Assert.That(changedPropertyName, Is.EqualTo(nameof(ApplicationSettings.MaxStorableItemSizeBytes)));
            Assert.That(_settingsManager.MaxStorableItemSizeBytes, Is.EqualTo(newMaxSize));

            // --- Test MaxTextPreviewLength ---
            eventRaised = false; changedPropertyName = null;
            int newMaxTextPreviewLength = _settingsManager.MaxTextPreviewLength + 50;
            _settingsManager.MaxTextPreviewLength = newMaxTextPreviewLength;
            Assert.That(eventRaised, Is.True, "SettingChanged devrait être levé pour MaxTextPreviewLength.");
            Assert.That(changedPropertyName, Is.EqualTo(nameof(ApplicationSettings.MaxTextPreviewLength)));
            Assert.That(_settingsManager.MaxTextPreviewLength, Is.EqualTo(newMaxTextPreviewLength));

            // --- Test HideTimestamp ---
            eventRaised = false; changedPropertyName = null;
            bool newHideTimestamp = !_settingsManager.HideTimestamp;
            _settingsManager.HideTimestamp = newHideTimestamp;
            Assert.That(eventRaised, Is.True, "SettingChanged devrait être levé pour HideTimestamp.");
            Assert.That(changedPropertyName, Is.EqualTo(nameof(ApplicationSettings.HideTimestamp)));
            Assert.That(_settingsManager.HideTimestamp, Is.EqualTo(newHideTimestamp));

            // Détacher l'événement pour ne pas affecter d'autres tests
            _settingsManager.SettingChanged -= eventHandler;
        }

        [Test]
        public async Task SaveSettingAsync_InvokesSettingChangedEvent()
        {
            // Arrange
            bool eventRaised = false;
            string? changedPropertyName = null;
            _settingsManager.SettingChanged += (propertyName) =>
            {
                eventRaised = true;
                changedPropertyName = propertyName;
            };
            int newValue = 150;

            // Act
            await _settingsManager.SaveSettingAsync(s => s.MaxHistoryItems, newValue);

            // Assert
            Assert.That(eventRaised, Is.True, "SettingChanged devrait être levé par SaveSettingAsync.");
            Assert.That(changedPropertyName, Is.EqualTo(nameof(ApplicationSettings.MaxHistoryItems)), "Le nom de la propriété changée devrait correspondre.");
            Assert.That(_settingsManager.MaxHistoryItems, Is.EqualTo(newValue), "La valeur de la propriété devrait être mise à jour.");
        }

        [Test]
        public async Task SaveSettingAsync_ForMaxTextPreviewLength_UpdatesProperty()
        {
            // Act
            await _settingsManager.SaveSettingAsync(s => s.MaxTextPreviewLength, 200);

            // Assert
            Assert.That(_settingsManager.MaxTextPreviewLength, Is.EqualTo(200));
        }

        [Test]
        public async Task SaveSettingAsync_ForHideTimestamp_UpdatesProperty()
        {
            // Act
            await _settingsManager.SaveSettingAsync(s => s.HideTimestamp, true);

            // Assert
            Assert.That(_settingsManager.HideTimestamp, Is.True);
        }
    }
} 