using NUnit.Framework;
using ClipboardPlus.Modules.Commands;
using System;

namespace ClipboardPlus.Tests.Unit.Modules.Commands
{
    /// <summary>
    /// Tests fonctionnels pour CommandFailedEventArgs - vérifient le comportement métier
    /// dans des scénarios d'usage réels de gestion d'erreurs de commandes
    /// </summary>
    [TestFixture]
    public class CommandFailedEventArgsFunctionalTests
    {
        private ICommandContext _testContext;

        [SetUp]
        public void SetUp()
        {
            _testContext = new CommandContext();
        }

        #region Scénarios métier de gestion d'erreurs

        [Test]
        public void CommandFailedEventArgs_DatabaseConnectionError_ShouldCaptureBusinessContext()
        {
            // Arrange - Scénario : Échec de commande due à une erreur de base de données
            var commandName = "SaveClipboardItem";
            var parameter = new { ItemId = "item-123", Content = "Important data" };
            var dbException = new InvalidOperationException("Database connection timeout");
            var executionTime = TimeSpan.FromSeconds(5.2);

            // Act
            var eventArgs = new CommandFailedEventArgs(commandName, parameter, _testContext, dbException, executionTime);

            // Assert - Vérifier que toutes les informations métier sont capturées
            Assert.That(eventArgs.CommandName, Is.EqualTo("SaveClipboardItem"));
            Assert.That(eventArgs.Parameter, Is.EqualTo(parameter));
            Assert.That(eventArgs.Context, Is.EqualTo(_testContext));
            Assert.That(eventArgs.Exception, Is.EqualTo(dbException));
            Assert.That(eventArgs.ExecutionTime, Is.EqualTo(executionTime));
            
            // Vérifier que l'exception contient les détails métier
            Assert.That(eventArgs.Exception.Message, Does.Contain("Database connection timeout"));
        }

        [Test]
        public void CommandFailedEventArgs_UserPermissionDenied_ShouldPreserveSecurityContext()
        {
            // Arrange - Scénario : Échec de commande due à des permissions insuffisantes
            var commandName = "DeleteHistoryItem";
            var parameter = new { ItemId = "sensitive-item-456", UserId = "user-789" };
            var securityException = new UnauthorizedAccessException("User does not have delete permissions");
            var executionTime = TimeSpan.FromMilliseconds(150);

            _testContext.SetProperty("UserId", "user-789");
            _testContext.SetProperty("UserRole", "ReadOnly");

            // Act
            var eventArgs = new CommandFailedEventArgs(commandName, parameter, _testContext, securityException, executionTime);

            // Assert - Vérifier que le contexte de sécurité est préservé
            Assert.That(eventArgs.CommandName, Is.EqualTo("DeleteHistoryItem"));
            Assert.That(eventArgs.Context.GetProperty<string>("UserId"), Is.EqualTo("user-789"));
            Assert.That(eventArgs.Context.GetProperty<string>("UserRole"), Is.EqualTo("ReadOnly"));
            Assert.That(eventArgs.Exception, Is.InstanceOf<UnauthorizedAccessException>());
            Assert.That(eventArgs.ExecutionTime.TotalMilliseconds, Is.EqualTo(150));
        }

        [Test]
        public void CommandFailedEventArgs_ValidationError_ShouldCaptureInputData()
        {
            // Arrange - Scénario : Échec de commande due à des données invalides
            var commandName = "CreateNewItem";
            var invalidParameter = new { Content = "", Type = "Unknown", Size = -1 };
            var validationException = new ArgumentException("Content cannot be empty and size must be positive");
            var executionTime = TimeSpan.FromMilliseconds(50);

            // Act
            var eventArgs = new CommandFailedEventArgs(commandName, invalidParameter, _testContext, validationException, executionTime);

            // Assert - Vérifier que les données d'entrée invalides sont capturées
            Assert.That(eventArgs.CommandName, Is.EqualTo("CreateNewItem"));
            Assert.That(eventArgs.Parameter, Is.EqualTo(invalidParameter));
            Assert.That(eventArgs.Exception, Is.InstanceOf<ArgumentException>());
            Assert.That(eventArgs.Exception.Message, Does.Contain("Content cannot be empty"));
            Assert.That(eventArgs.ExecutionTime.TotalMilliseconds, Is.LessThan(100)); // Échec rapide pour validation
        }

        [Test]
        public void CommandFailedEventArgs_NetworkTimeout_ShouldTrackPerformanceMetrics()
        {
            // Arrange - Scénario : Échec de commande due à un timeout réseau
            var commandName = "SyncWithCloud";
            var parameter = new { SyncType = "Full", LastSync = DateTime.UtcNow.AddDays(-1) };
            var timeoutException = new TimeoutException("Network operation timed out after 30 seconds");
            var executionTime = TimeSpan.FromSeconds(30.5);

            _testContext.SetProperty("NetworkLatency", 2500);
            _testContext.SetProperty("RetryCount", 3);

            // Act
            var eventArgs = new CommandFailedEventArgs(commandName, parameter, _testContext, timeoutException, executionTime);

            // Assert - Vérifier que les métriques de performance sont trackées
            Assert.That(eventArgs.CommandName, Is.EqualTo("SyncWithCloud"));
            Assert.That(eventArgs.ExecutionTime.TotalSeconds, Is.GreaterThan(30));
            Assert.That(eventArgs.Context.GetProperty<int>("NetworkLatency"), Is.EqualTo(2500));
            Assert.That(eventArgs.Context.GetProperty<int>("RetryCount"), Is.EqualTo(3));
            Assert.That(eventArgs.Exception, Is.InstanceOf<TimeoutException>());
        }

        #endregion

        #region Scénarios de diagnostic et monitoring

        [Test]
        public void CommandFailedEventArgs_CriticalSystemError_ShouldEnableEmergencyDiagnostics()
        {
            // Arrange - Scénario : Erreur système critique nécessitant un diagnostic d'urgence
            var commandName = "EmergencyBackup";
            var parameter = new { BackupType = "Critical", Priority = "High" };
            var systemException = new OutOfMemoryException("Insufficient memory for backup operation");
            var executionTime = TimeSpan.FromMinutes(2.5);

            _testContext.SetProperty("SystemMemoryMB", 512);
            _testContext.SetProperty("RequiredMemoryMB", 2048);
            _testContext.SetProperty("CriticalOperation", true);

            // Act
            var eventArgs = new CommandFailedEventArgs(commandName, parameter, _testContext, systemException, executionTime);

            // Assert - Vérifier que le diagnostic d'urgence peut être activé
            Assert.That(eventArgs.CommandName, Is.EqualTo("EmergencyBackup"));
            Assert.That(eventArgs.Exception, Is.InstanceOf<OutOfMemoryException>());
            Assert.That(eventArgs.Context.GetProperty<bool>("CriticalOperation"), Is.True);
            Assert.That(eventArgs.Context.GetProperty<int>("SystemMemoryMB"), Is.LessThan(eventArgs.Context.GetProperty<int>("RequiredMemoryMB")));
            Assert.That(eventArgs.ExecutionTime.TotalMinutes, Is.GreaterThan(2));
        }

        [Test]
        public void CommandFailedEventArgs_ConcurrentModification_ShouldTrackConflictResolution()
        {
            // Arrange - Scénario : Échec due à une modification concurrente
            var commandName = "UpdateClipboardItem";
            var parameter = new { ItemId = "item-999", Version = 5, NewContent = "Updated content" };
            var conflictException = new InvalidOperationException("Item was modified by another process (current version: 7)");
            var executionTime = TimeSpan.FromMilliseconds(200);

            _testContext.SetProperty("ExpectedVersion", 5);
            _testContext.SetProperty("ActualVersion", 7);
            _testContext.SetProperty("ConflictDetected", true);

            // Act
            var eventArgs = new CommandFailedEventArgs(commandName, parameter, _testContext, conflictException, executionTime);

            // Assert - Vérifier que les informations de conflit sont capturées
            Assert.That(eventArgs.CommandName, Is.EqualTo("UpdateClipboardItem"));
            Assert.That(eventArgs.Context.GetProperty<int>("ExpectedVersion"), Is.EqualTo(5));
            Assert.That(eventArgs.Context.GetProperty<int>("ActualVersion"), Is.EqualTo(7));
            Assert.That(eventArgs.Context.GetProperty<bool>("ConflictDetected"), Is.True);
            Assert.That(eventArgs.Exception.Message, Does.Contain("current version: 7"));
        }

        #endregion

        #region Scénarios de récupération d'erreur

        [Test]
        public void CommandFailedEventArgs_TransientError_ShouldSupportRetryLogic()
        {
            // Arrange - Scénario : Erreur transitoire qui peut être retentée
            var commandName = "RefreshClipboardHistory";
            var parameter = new { PageSize = 50, PageNumber = 1 };
            var transientException = new InvalidOperationException("Service temporarily unavailable");
            var executionTime = TimeSpan.FromSeconds(1.2);

            _testContext.SetProperty("RetryAttempt", 2);
            _testContext.SetProperty("MaxRetries", 5);
            _testContext.SetProperty("BackoffDelayMs", 1000);

            // Act
            var eventArgs = new CommandFailedEventArgs(commandName, parameter, _testContext, transientException, executionTime);

            // Assert - Vérifier que les informations de retry sont disponibles
            Assert.That(eventArgs.CommandName, Is.EqualTo("RefreshClipboardHistory"));
            Assert.That(eventArgs.Context.GetProperty<int>("RetryAttempt"), Is.LessThan(eventArgs.Context.GetProperty<int>("MaxRetries")));
            Assert.That(eventArgs.Context.GetProperty<int>("BackoffDelayMs"), Is.GreaterThan(0));
            Assert.That(eventArgs.Exception.Message, Does.Contain("temporarily unavailable"));
        }

        [Test]
        public void CommandFailedEventArgs_NullParameterHandling_ShouldGracefullyHandleNullInputs()
        {
            // Arrange - Scénario : Commande avec paramètre null (cas métier valide)
            var commandName = "ClearAllHistory";
            object? nullParameter = null;
            var exception = new InvalidOperationException("Cannot clear history: operation cancelled by user");
            var executionTime = TimeSpan.FromMilliseconds(10);

            // Act
            var eventArgs = new CommandFailedEventArgs(commandName, nullParameter, _testContext, exception, executionTime);

            // Assert - Vérifier que les paramètres null sont gérés correctement
            Assert.That(eventArgs.CommandName, Is.EqualTo("ClearAllHistory"));
            Assert.That(eventArgs.Parameter, Is.Null);
            Assert.That(eventArgs.Context, Is.Not.Null);
            Assert.That(eventArgs.Exception, Is.Not.Null);
            Assert.That(eventArgs.ExecutionTime, Is.EqualTo(TimeSpan.FromMilliseconds(10)));
        }

        #endregion
    }
}
