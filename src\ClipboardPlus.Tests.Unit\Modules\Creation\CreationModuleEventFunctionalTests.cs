using NUnit.Framework;
using ClipboardPlus.Modules.Creation;
using ClipboardPlus.Modules.Core.Events;
using System;

namespace ClipboardPlus.Tests.Unit.Modules.Creation
{
    /// <summary>
    /// Tests fonctionnels pour CreationModuleEvent - vérifient le comportement métier
    /// dans des scénarios d'usage réels de gestion des changements d'état de création
    /// </summary>
    [TestFixture]
    public class CreationModuleEventFunctionalTests
    {
        #region Scénarios métier de transitions d'état de création

        [Test]
        public void CreationModuleEvent_UserInitiatesCreation_ShouldCaptureCreationStart()
        {
            // Arrange - Scénario : Utilisateur démarre la création d'un nouvel élément
            var sourceModule = "CreationModule";
            var currentState = CreationState.EditingContent;
            var previousState = CreationState.Idle;
            var reason = "User clicked 'New Item' button";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, reason);

            // Assert - Vérifier que le démarrage de création est capturé
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.EditingContent));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.Idle));
            Assert.That(moduleEvent.Reason, Is.EqualTo("User clicked 'New Item' button"));
            
            // Vérifier les propriétés héritées de ModuleEventBase
            Assert.That(moduleEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(moduleEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
            Assert.That(moduleEvent.TargetModule, Is.Null); // Broadcast par défaut
        }

        [Test]
        public void CreationModuleEvent_ContentValidationSuccess_ShouldTrackValidationProgress()
        {
            // Arrange - Scénario : Validation du contenu réussie
            var sourceModule = "CreationModule";
            var currentState = CreationState.ReadyToFinalize;
            var previousState = CreationState.EditingContent;
            var reason = "Content validation passed: 250 characters, text format detected";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, reason);

            // Assert - Vérifier que la progression de validation est trackée
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.ReadyToFinalize));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.EditingContent));
            Assert.That(moduleEvent.Reason, Does.Contain("validation passed"));
            Assert.That(moduleEvent.Reason, Does.Contain("250 characters"));
        }

        [Test]
        public void CreationModuleEvent_CreationFinalization_ShouldCaptureCompletionMetrics()
        {
            // Arrange - Scénario : Finalisation réussie de la création
            var sourceModule = "CreationModule";
            var currentState = CreationState.Completed;
            var previousState = CreationState.Finalizing;
            var reason = "Item created successfully in 2.3 seconds with ID: 12345";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, reason);

            // Assert - Vérifier que les métriques de completion sont capturées
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.Completed));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.Finalizing));
            Assert.That(moduleEvent.Reason, Does.Contain("successfully"));
            Assert.That(moduleEvent.Reason, Does.Contain("2.3 seconds"));
            Assert.That(moduleEvent.Reason, Does.Contain("ID: 12345"));
        }

        [Test]
        public void CreationModuleEvent_CreationCancellation_ShouldTrackCancellationReason()
        {
            // Arrange - Scénario : Annulation de création par l'utilisateur
            var sourceModule = "CreationModule";
            var currentState = CreationState.Idle;
            var previousState = CreationState.EditingContent;
            var reason = "User cancelled creation after 45 seconds of editing";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, reason);

            // Assert - Vérifier que la raison d'annulation est trackée
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.Idle));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.EditingContent));
            Assert.That(moduleEvent.Reason, Does.Contain("cancelled"));
            Assert.That(moduleEvent.Reason, Does.Contain("45 seconds"));
        }

        #endregion

        #region Scénarios d'erreurs et récupération

        [Test]
        public void CreationModuleEvent_ValidationFailure_ShouldCaptureValidationErrors()
        {
            // Arrange - Scénario : Échec de validation du contenu
            var sourceModule = "CreationModule";
            var currentState = CreationState.Error;
            var previousState = CreationState.EditingContent;
            var reason = "Validation failed: Content too large (5MB exceeds 1MB limit)";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, reason);

            // Assert - Vérifier que les erreurs de validation sont capturées
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.Error));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.EditingContent));
            Assert.That(moduleEvent.Reason, Does.Contain("Validation failed"));
            Assert.That(moduleEvent.Reason, Does.Contain("5MB exceeds 1MB limit"));
        }

        [Test]
        public void CreationModuleEvent_DatabaseSaveFailure_ShouldTrackPersistenceErrors()
        {
            // Arrange - Scénario : Échec de sauvegarde en base de données
            var sourceModule = "CreationModule";
            var currentState = CreationState.Error;
            var previousState = CreationState.Finalizing;
            var reason = "Database save failed: Connection timeout after 30 seconds";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, reason);

            // Assert - Vérifier que les erreurs de persistance sont trackées
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.Error));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.Finalizing));
            Assert.That(moduleEvent.Reason, Does.Contain("Database save failed"));
            Assert.That(moduleEvent.Reason, Does.Contain("timeout"));
        }

        [Test]
        public void CreationModuleEvent_ErrorRecovery_ShouldTrackRecoveryAttempts()
        {
            // Arrange - Scénario : Récupération après erreur
            var sourceModule = "CreationModule";
            var currentState = CreationState.EditingContent;
            var previousState = CreationState.Error;
            var reason = "Recovery successful: User corrected content and resumed editing";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, reason);

            // Assert - Vérifier que les tentatives de récupération sont trackées
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.EditingContent));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.Error));
            Assert.That(moduleEvent.Reason, Does.Contain("Recovery successful"));
            Assert.That(moduleEvent.Reason, Does.Contain("resumed editing"));
        }

        #endregion

        #region Scénarios de performance et monitoring

        [Test]
        public void CreationModuleEvent_LongRunningCreation_ShouldTrackPerformanceMetrics()
        {
            // Arrange - Scénario : Création longue nécessitant monitoring
            var sourceModule = "CreationModule";
            var currentState = CreationState.Finalizing;
            var previousState = CreationState.ReadyToFinalize;
            var reason = "Large content processing: 2.5MB image data, estimated completion in 15 seconds";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, reason);

            // Assert - Vérifier que les métriques de performance sont trackées
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.Finalizing));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.ReadyToFinalize));
            Assert.That(moduleEvent.Reason, Does.Contain("2.5MB"));
            Assert.That(moduleEvent.Reason, Does.Contain("15 seconds"));
        }

        [Test]
        public void CreationModuleEvent_HighFrequencyCreation_ShouldSupportRapidStateChanges()
        {
            // Arrange - Scénario : Création rapide avec changements d'état fréquents
            var sourceModule = "CreationModule";
            var currentState = CreationState.Completed;
            var previousState = CreationState.Initialized;
            var reason = "Fast creation completed in 150ms: simple text content";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, reason);

            // Assert - Vérifier que les changements d'état rapides sont supportés
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.Completed));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.Initialized));
            Assert.That(moduleEvent.Reason, Does.Contain("150ms"));
            Assert.That(moduleEvent.Reason, Does.Contain("simple text"));
        }

        #endregion

        #region Scénarios de gestion des raisons null

        [Test]
        public void CreationModuleEvent_NullReason_ShouldHandleGracefully()
        {
            // Arrange - Scénario : Changement d'état sans raison spécifique
            var sourceModule = "CreationModule";
            var currentState = CreationState.Initialized;
            var previousState = CreationState.Idle;
            string? nullReason = null;

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, nullReason);

            // Assert - Vérifier que les raisons null sont gérées
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.Initialized));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.Idle));
            Assert.That(moduleEvent.Reason, Is.Null);
            
            // Vérifier que l'événement reste valide
            Assert.That(moduleEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(moduleEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void CreationModuleEvent_EmptyReason_ShouldPreserveEmptyString()
        {
            // Arrange - Scénario : Changement d'état avec raison vide
            var sourceModule = "CreationModule";
            var currentState = CreationState.EditingContent;
            var previousState = CreationState.Initialized;
            var emptyReason = "";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, emptyReason);

            // Assert - Vérifier que les chaînes vides sont préservées
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.EditingContent));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.Initialized));
            Assert.That(moduleEvent.Reason, Is.EqualTo(""));
        }

        #endregion

        #region Scénarios de workflow de création complet

        [Test]
        public void CreationModuleEvent_CompleteCreationWorkflow_ShouldSupportAllStateTransitions()
        {
            // Arrange - Scénario : Workflow complet de création
            var sourceModule = "CreationModule";
            
            // Test de plusieurs transitions typiques
            var transitions = new[]
            {
                (CreationState.Initialized, CreationState.Idle, "Module initialized"),
                (CreationState.EditingContent, CreationState.Initialized, "User started editing"),
                (CreationState.ReadyToFinalize, CreationState.EditingContent, "Content validated"),
                (CreationState.Finalizing, CreationState.ReadyToFinalize, "User confirmed creation"),
                (CreationState.Completed, CreationState.Finalizing, "Creation finalized successfully")
            };

            foreach (var (current, previous, reason) in transitions)
            {
                // Act
                var moduleEvent = new CreationModuleEvent(sourceModule, current, previous, reason);

                // Assert - Vérifier que chaque transition est correctement capturée
                Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
                Assert.That(moduleEvent.CurrentState, Is.EqualTo(current));
                Assert.That(moduleEvent.PreviousState, Is.EqualTo(previous));
                Assert.That(moduleEvent.Reason, Is.EqualTo(reason));
                Assert.That(moduleEvent.EventId, Is.Not.EqualTo(Guid.Empty));
                Assert.That(moduleEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
            }
        }

        [Test]
        public void CreationModuleEvent_InheritanceFromModuleEventBase_ShouldSupportEventBusIntegration()
        {
            // Arrange - Scénario : Intégration avec le bus d'événements
            var sourceModule = "CreationModule";
            var currentState = CreationState.Completed;
            var previousState = CreationState.Finalizing;
            var reason = "Integration test for event bus";

            // Act
            var moduleEvent = new CreationModuleEvent(sourceModule, currentState, previousState, reason);

            // Assert - Vérifier que l'héritage de ModuleEventBase fonctionne
            Assert.That(moduleEvent, Is.InstanceOf<ModuleEventBase>());
            Assert.That(moduleEvent.SourceModule, Is.EqualTo("CreationModule"));
            Assert.That(moduleEvent.TargetModule, Is.Null); // Par défaut null pour broadcast
            Assert.That(moduleEvent.EventId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(moduleEvent.Timestamp, Is.LessThanOrEqualTo(DateTime.UtcNow));
            
            // Vérifier les propriétés spécifiques à CreationModuleEvent
            Assert.That(moduleEvent.CurrentState, Is.EqualTo(CreationState.Completed));
            Assert.That(moduleEvent.PreviousState, Is.EqualTo(CreationState.Finalizing));
            Assert.That(moduleEvent.Reason, Is.EqualTo("Integration test for event bus"));
        }

        #endregion
    }
}
