using System;
using System.Threading;
using System.Windows;
using Moq;
using NUnit.Framework;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.SystemTray;

namespace ClipboardPlus.Tests.Unit.Core.Services.SystemTray
{
    /// <summary>
    /// Tests unitaires pour ThreadValidator.
    /// Vérifie la responsabilité unique : validation du thread UI.
    /// </summary>
    [TestFixture]
    [Apartment(ApartmentState.STA)]
    public class ThreadValidatorTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private ThreadValidator _threadValidator;
        private Application _wpfApplication;

        [OneTimeSetUp]
        public void OneTimeSetUp()
        {
            // Créer une application WPF pour les tests STA
            if (Application.Current == null)
            {
                _wpfApplication = new Application();
            }
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            _wpfApplication?.Shutdown();
        }

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _threadValidator = new ThreadValidator(_mockLoggingService.Object);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ThreadValidator(null));
        }

        [Test]
        public void IsInUIThread_WhenInUIThread_ReturnsTrue()
        {
            // Act
            bool result = _threadValidator.IsInUIThread();

            // Assert
            Assert.That(result, Is.True, "Should return true when in UI thread");
            
            // Vérifier que le logging a été appelé
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Vérification du thread UI") && s.Contains("True"))),
                Times.Once,
                "Should log UI thread verification result");
        }

        [Test]
        public void IsInUIThread_LogsCorrectMessage()
        {
            // Act
            _threadValidator.IsInUIThread();

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.StartsWith("ThreadValidator: Vérification du thread UI"))),
                Times.Once,
                "Should log thread verification message");
        }

        [Test]
        public void ValidateUIThread_WhenInUIThread_LogsSuccessMessage()
        {
            // Act
            _threadValidator.ValidateUIThread();

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo("ThreadValidator: Validation réussie - Exécution dans le thread UI approprié."),
                Times.Once,
                "Should log successful validation message");
        }

        [Test]
        public void ValidateUIThread_WhenInUIThread_LogsExecutionStatus()
        {
            // Act
            _threadValidator.ValidateUIThread();

            // Assert
            _mockLoggingService.Verify(
                x => x.LogInfo(It.Is<string>(s => s.Contains("Exécution dans le thread UI: True"))),
                Times.Once,
                "Should log execution status");
        }

        [Test]
        public void ValidateUIThread_DoesNotThrowException()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _threadValidator.ValidateUIThread(),
                "ValidateUIThread should not throw exception in normal conditions");
        }

        [Test]
        public void IsInUIThread_WithException_ReturnsFalseAndLogsError()
        {
            // Arrange - Créer un validator avec un mock qui lève une exception lors du logging
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo(It.IsAny<string>()))
                             .Throws(new InvalidOperationException("Test exception"));

            var validator = new ThreadValidator(mockLoggingService.Object);

            // Act
            bool result = validator.IsInUIThread();

            // Assert
            Assert.That(result, Is.False, "Should return false when exception occurs");
            
            // Vérifier que l'erreur a été loggée
            mockLoggingService.Verify(
                x => x.LogError(It.Is<string>(s => s.Contains("Erreur lors de la vérification du thread UI")), 
                               It.IsAny<Exception>()),
                Times.Once,
                "Should log error when exception occurs");
        }

        [Test]
        public void ValidateUIThread_WithException_ThrowsException()
        {
            // Arrange - Créer un validator avec un mock qui lève une exception
            var mockLoggingService = new Mock<ILoggingService>();
            mockLoggingService.Setup(x => x.LogInfo(It.IsAny<string>()))
                             .Throws(new InvalidOperationException("Test exception"));

            var validator = new ThreadValidator(mockLoggingService.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => validator.ValidateUIThread(),
                "ValidateUIThread should throw exception when logging fails");
        }

        [Test]
        public void ValidateUIThread_CallsIsInUIThread()
        {
            // Arrange - Créer un spy pour vérifier les appels internes
            var callCount = 0;
            _mockLoggingService.Setup(x => x.LogInfo(It.Is<string>(s => s.Contains("Vérification du thread UI"))))
                              .Callback(() => callCount++);

            // Act
            _threadValidator.ValidateUIThread();

            // Assert
            Assert.That(callCount, Is.EqualTo(1), "ValidateUIThread should call IsInUIThread internally");
        }

        [Test]
        public void ThreadValidator_IsStateless()
        {
            // Act - Appeler plusieurs fois les méthodes
            bool result1 = _threadValidator.IsInUIThread();
            bool result2 = _threadValidator.IsInUIThread();
            
            _threadValidator.ValidateUIThread();
            _threadValidator.ValidateUIThread();

            // Assert - Les résultats doivent être cohérents
            Assert.That(result1, Is.EqualTo(result2), "ThreadValidator should be stateless and return consistent results");
        }
    }
}
