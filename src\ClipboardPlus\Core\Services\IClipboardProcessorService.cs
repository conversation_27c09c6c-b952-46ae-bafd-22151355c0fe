using ClipboardPlus.Core.DataModels;
using System.Collections.Specialized;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;

namespace ClipboardPlus.Core.Services
{
    public interface IClipboardProcessorService
    {
        Task<ClipboardItem?> CreateItemFromTextAsync(string text);

        Task<ClipboardItem?> CreateItemFromImageAsync(BitmapSource imageSource, int thumbnailMaxDimension);

        Task<ClipboardItem?> CreateItemFromFileDropListAsync(StringCollection files);
        
        /// <summary>
        /// Traite le contenu actuel du presse-papiers et l'ajoute à l'historique si nécessaire.
        /// </summary>
        Task ProcessCurrentClipboardContentAsync();
    }
} 