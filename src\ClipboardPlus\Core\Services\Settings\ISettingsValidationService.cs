using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.DataModels.Settings;

namespace ClipboardPlus.Core.Services.Settings
{
    /// <summary>
    /// Service responsable de la validation des changements de paramètres.
    /// Centralise toute la logique de validation avant application.
    /// </summary>
    public interface ISettingsValidationService
    {
        /// <summary>
        /// Valide un changement de raccourci clavier.
        /// </summary>
        /// <param name="currentShortcut">Raccourci actuel</param>
        /// <param name="newShortcut">Nouveau raccourci proposé</param>
        /// <returns>Résultat de la validation</returns>
        SettingsValidationResult ValidateShortcutChange(string currentShortcut, string newShortcut);

        /// <summary>
        /// Valide un changement de paramètre de démarrage automatique.
        /// </summary>
        /// <param name="currentStartup">État actuel du démarrage automatique</param>
        /// <param name="newStartup">Nouvel état proposé</param>
        /// <returns>Résultat de la validation</returns>
        SettingsValidationResult ValidateStartupChange(bool currentStartup, bool newStartup);

        /// <summary>
        /// Valide un changement de thème.
        /// </summary>
        /// <param name="currentTheme">Thème actuel</param>
        /// <param name="newTheme">Nouveau thème proposé</param>
        /// <returns>Résultat de la validation</returns>
        SettingsValidationResult ValidateThemeChange(ThemeInfo? currentTheme, ThemeInfo? newTheme);

        /// <summary>
        /// Valide tous les changements de paramètres de manière globale.
        /// </summary>
        /// <param name="currentSettings">Paramètres actuels</param>
        /// <param name="newSettings">Nouveaux paramètres proposés</param>
        /// <returns>Résultat de la validation globale</returns>
        Task<SettingsValidationResult> ValidateAllChangesAsync(CompleteSettingsData currentSettings, CompleteSettingsData newSettings);
    }
}
