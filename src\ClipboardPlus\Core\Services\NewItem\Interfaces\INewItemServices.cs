using System;
using System.Threading.Tasks;
using System.Windows;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services.NewItem.Models;

namespace ClipboardPlus.Core.Services.NewItem.Interfaces
{
    /// <summary>
    /// Service de validation pour la création de nouveaux éléments.
    /// Responsabilité unique : Valider les conditions préalables à la création d'éléments.
    /// </summary>
    public interface INewItemValidationService
    {
        /// <summary>
        /// Valide si un nouvel élément peut être créé dans l'état actuel.
        /// </summary>
        /// <param name="isOperationInProgress">Indique si une opération est déjà en cours</param>
        /// <param name="isItemCreationActive">Indique si la création d'élément est déjà active</param>
        /// <returns>Résultat de validation avec succès/échec et message d'erreur</returns>
        NewItemValidationResult ValidateCanCreateNewItem(bool isOperationInProgress, bool isItemCreationActive);
    }

    /// <summary>
    /// Service de configuration des dialogues.
    /// Responsabilité unique : Configurer les dialogues pour l'affichage optimal.
    /// </summary>
    public interface IDialogConfigurationService
    {
        /// <summary>
        /// Configure un dialogue de création de nouvel élément.
        /// </summary>
        /// <param name="dialog">Le dialogue à configurer</param>
        /// <param name="dataContext">Le contexte de données à associer</param>
        void ConfigureNewItemDialog(object dialog, object dataContext);

        /// <summary>
        /// Trouve la fenêtre propriétaire appropriée pour centrer le dialogue.
        /// </summary>
        /// <returns>La fenêtre propriétaire ou null si aucune trouvée</returns>
        Window? FindOwnerWindow();
    }

    /// <summary>
    /// Service de gestion du mode test.
    /// Responsabilité unique : Détecter et gérer le mode test.
    /// </summary>
    public interface ITestModeHandler
    {
        /// <summary>
        /// Détermine si l'application est en mode test.
        /// </summary>
        /// <returns>True si en mode test, false sinon</returns>
        bool IsInTestMode();

        /// <summary>
        /// Gère le comportement spécifique au mode test.
        /// </summary>
        /// <param name="stateManager">Gestionnaire d'état pour appliquer les modifications</param>
        void HandleTestMode(IOperationStateManager stateManager);
    }

    /// <summary>
    /// Service de gestion d'état des opérations.
    /// Responsabilité unique : Gérer l'état des opérations et propriétés liées.
    /// </summary>
    public interface IOperationStateManager
    {
        /// <summary>
        /// Indique si une opération est en cours.
        /// </summary>
        bool IsOperationInProgress { get; set; }

        /// <summary>
        /// Indique si la création d'élément est active.
        /// </summary>
        bool IsItemCreationActive { get; set; }

        /// <summary>
        /// Contenu textuel du nouvel élément.
        /// </summary>
        string NewItemTextContent { get; set; }

        /// <summary>
        /// Rafraîchit les commandes liées à la création d'éléments.
        /// </summary>
        void RefreshItemCreationCommands();
    }

    /// <summary>
    /// Service d'affichage de dialogues.
    /// Responsabilité unique : Gérer l'affichage des dialogues modaux.
    /// </summary>
    public interface IDialogService
    {
        /// <summary>
        /// Affiche un dialogue modal.
        /// </summary>
        /// <param name="dialog">Le dialogue à afficher</param>
        /// <returns>Le résultat du dialogue (true/false/null)</returns>
        bool? ShowDialog(object dialog);
    }

    /// <summary>
    /// Service de gestion d'erreurs.
    /// Responsabilité unique : Gérer l'affichage et le logging des erreurs.
    /// </summary>
    public interface IErrorHandlingService
    {
        /// <summary>
        /// Gère une erreur en l'affichant à l'utilisateur et en la loggant.
        /// </summary>
        /// <param name="message">Message d'erreur à afficher</param>
        /// <param name="title">Titre de la boîte de dialogue d'erreur</param>
        /// <param name="exception">Exception associée (optionnelle)</param>
        /// <param name="context">Contexte de l'erreur pour le logging</param>
        /// <param name="viewModel">ViewModel associé (optionnel)</param>
        void HandleError(string message, string title, Exception? exception = null, string context = "", object? viewModel = null);
    }

    /// <summary>
    /// Orchestrateur principal pour la création de nouveaux éléments.
    /// Responsabilité unique : Coordonner le processus complet de création d'éléments.
    /// </summary>
    public interface INewItemCreationOrchestrator
    {
        /// <summary>
        /// Prépare la création d'un nouvel élément en orchestrant tous les services nécessaires.
        /// </summary>
        void PrepareNewItem();
    }


}
