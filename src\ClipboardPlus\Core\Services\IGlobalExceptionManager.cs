using System;

namespace ClipboardPlus.Core.Services
{
    /// <summary>
    /// Interface pour un service de gestion globale des exceptions non gérées dans l'application.
    /// </summary>
    public interface IGlobalExceptionManager
    {
        /// <summary>
        /// Initialise les gestionnaires d'exceptions globaux.
        /// </summary>
        void Initialize();

        /// <summary>
        /// Journalise une exception non gérée avec des informations contextuelles.
        /// </summary>
        /// <param name="source">La source de l'exception (UI, AppDomain, Task, etc.)</param>
        /// <param name="exception">L'exception qui s'est produite</param>
        /// <param name="isTerminating">Indique si l'exception entraîne la terminaison de l'application</param>
        void LogUnhandledException(string source, Exception exception, bool isTerminating);
    }
}
