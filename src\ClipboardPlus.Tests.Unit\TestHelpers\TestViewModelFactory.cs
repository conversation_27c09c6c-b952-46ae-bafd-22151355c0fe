// ============================================================================
// FACTORY DE TEST POUR HARNAIS DE SÉCURITÉ - PHASE 6C
// ============================================================================
//
// 🛡️ OBJECTIF : Créer des instances de ClipboardHistoryViewModel pour les tests
// 🎯 USAGE : Harnais de sécurité Phase 6C - Extraction des Managers
//
// ============================================================================

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Modules.History;
using ClipboardPlus.Modules.Commands;
using ClipboardPlus.Modules.Creation;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.UI.ViewModels.Construction;
using Microsoft.Extensions.DependencyInjection;
using Moq;

namespace ClipboardPlus.Tests.Unit.TestHelpers
{
    /// <summary>
    /// Factory pour créer des instances de ClipboardHistoryViewModel configurées pour les tests.
    /// </summary>
    public class TestViewModelFactory : IDisposable
    {
        private readonly Mock<IClipboardHistoryManager> _mockHistoryManager;
        private readonly Mock<IClipboardInteractionService> _mockClipboardService;
        private readonly Mock<ISettingsManager> _mockSettingsManager;
        private readonly Mock<IUserNotificationService> _mockNotificationService;
        private readonly Mock<IUserInteractionService> _mockUserInteractionService;
        private readonly Mock<IRenameService> _mockRenameService;
        private readonly Mock<IHistoryModule> _mockHistoryModule;
        private readonly Mock<ICommandModule> _mockCommandModule;
        private readonly Mock<ICreationModule> _mockCreationModule;
        private readonly Mock<IServiceProvider> _mockServiceProvider;
        private readonly Mock<ILoggingService> _mockLoggingService;
        private readonly Mock<IVisibilityStateManager> _mockVisibilityStateManager;

        private readonly ObservableCollection<ClipboardItem> _testHistoryItems;
        private readonly ObservableCollection<ClipboardItem> _testFilteredItems;

        public TestViewModelFactory()
        {
            // Initialiser les collections de test
            _testHistoryItems = new ObservableCollection<ClipboardItem>();
            _testFilteredItems = new ObservableCollection<ClipboardItem>();

            // Créer les mocks des services
            _mockHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockClipboardService = new Mock<IClipboardInteractionService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockNotificationService = new Mock<IUserNotificationService>();
            _mockUserInteractionService = new Mock<IUserInteractionService>();
            _mockRenameService = new Mock<IRenameService>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockVisibilityStateManager = new Mock<IVisibilityStateManager>();

            // Créer les mocks des modules
            _mockHistoryModule = new Mock<IHistoryModule>();
            _mockCommandModule = new Mock<ICommandModule>();
            _mockCreationModule = new Mock<ICreationModule>();

            SetupDefaultBehaviors();
        }

        /// <summary>
        /// Crée une instance de ClipboardHistoryViewModel configurée pour les tests.
        /// </summary>
        public ClipboardHistoryViewModel CreateClipboardHistoryViewModel()
        {
            var dependencies = new ViewModelDependencies(
                _mockHistoryManager.Object,
                _mockClipboardService.Object,
                _mockSettingsManager.Object,
                _mockNotificationService.Object,
                _mockUserInteractionService.Object,
                _mockRenameService.Object,
                _mockHistoryModule.Object,
                _mockCommandModule.Object,
                _mockCreationModule.Object,
                _mockServiceProvider.Object
            );

            var optionalDependencies = new OptionalServicesDependencies(
                DeletionResultLogger: null, // IDeletionResultLogger
                CollectionHealthService: null, // ICollectionHealthService
                VisibilityStateManager: _mockVisibilityStateManager.Object, // IVisibilityStateManager
                NewItemCreationOrchestrator: null, // INewItemCreationOrchestrator
                TestEnvironmentDetector: null, // ITestEnvironmentDetector
                SettingsWindowService: null  // ISettingsWindowService
            );

            return new ClipboardHistoryViewModel(dependencies, optionalDependencies);
        }

        /// <summary>
        /// Configure le HistoryManager pour retourner des éléments de test.
        /// </summary>
        public void SetupHistoryManagerWithItems(IEnumerable<ClipboardItem> items)
        {
            _testHistoryItems.Clear();
            _testFilteredItems.Clear();

            foreach (var item in items)
            {
                _testHistoryItems.Add(item);
                _testFilteredItems.Add(item);
            }

            // Configurer le mock HistoryModule
            _mockHistoryModule.Setup(m => m.HistoryItems).Returns(_testHistoryItems);
            _mockHistoryModule.Setup(m => m.FilteredItems).Returns(_testFilteredItems);
            _mockHistoryModule.Setup(m => m.TotalItemCount).Returns(_testHistoryItems.Count);
            _mockHistoryModule.Setup(m => m.FilteredItemCount).Returns(_testFilteredItems.Count);

            // Configurer le mock HistoryManager
            _mockHistoryManager.Setup(m => m.HistoryItems)
                .Returns(items.ToList());
        }

        /// <summary>
        /// Configure le HistoryManager pour lever une exception.
        /// </summary>
        public void SetupHistoryManagerToThrowException()
        {
            _mockHistoryManager.Setup(m => m.HistoryItems)
                .Throws(new InvalidOperationException("Test exception"));

            _mockHistoryModule.Setup(m => m.LoadHistoryAsync(It.IsAny<string>()))
                .ThrowsAsync(new InvalidOperationException("Test exception"));
        }

        /// <summary>
        /// Configure les comportements par défaut des mocks.
        /// </summary>
        private void SetupDefaultBehaviors()
        {
            // HistoryManager par défaut
            _mockHistoryManager.Setup(m => m.HistoryItems)
                .Returns(new List<ClipboardItem>());

            // HistoryModule par défaut
            _mockHistoryModule.Setup(m => m.HistoryItems).Returns(_testHistoryItems);
            _mockHistoryModule.Setup(m => m.FilteredItems).Returns(_testFilteredItems);
            _mockHistoryModule.Setup(m => m.TotalItemCount).Returns(0);
            _mockHistoryModule.Setup(m => m.FilteredItemCount).Returns(0);
            _mockHistoryModule.Setup(m => m.IsSynchronizing).Returns(false);

            // CommandModule par défaut
            _mockCommandModule.Setup(m => m.CommandContext).Returns(Mock.Of<ICommandContext>());

            // CreationModule par défaut
            _mockCreationModule.Setup(m => m.IsCreatingNewItem).Returns(false);

            // SettingsManager par défaut
            _mockSettingsManager.Setup(m => m.MaxHistoryItems).Returns(50);
            _mockSettingsManager.Setup(m => m.StartWithWindows).Returns(false);
            _mockSettingsManager.Setup(m => m.ShortcutKeyCombination).Returns("Ctrl+Shift+V");

            // ServiceProvider par défaut
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(ILoggingService)))
                .Returns(_mockLoggingService.Object);

            // LoggingService par défaut (ne fait rien)
            _mockLoggingService.Setup(l => l.LogInfo(It.IsAny<string>()));
            _mockLoggingService.Setup(l => l.LogWarning(It.IsAny<string>()));
            _mockLoggingService.Setup(l => l.LogError(It.IsAny<string>(), It.IsAny<Exception>()));
        }

        /// <summary>
        /// Libère les ressources utilisées par la factory.
        /// </summary>
        public void Dispose()
        {
            _testHistoryItems?.Clear();
            _testFilteredItems?.Clear();
        }
    }
}
