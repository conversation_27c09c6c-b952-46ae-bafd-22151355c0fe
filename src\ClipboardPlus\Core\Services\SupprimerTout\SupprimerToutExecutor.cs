using System;
using System.Linq;
using System.Threading.Tasks;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Core.Services.SupprimerTout
{
    /// <summary>
    /// Exécuteur pour les opérations de suppression de tous les éléments.
    /// Extrait de la méthode SupprimerTout originale pour séparer les responsabilités.
    /// Gère l'exécution effective de la suppression et l'interaction avec ClipboardHistoryManager.
    /// </summary>
    public class SupprimerToutExecutor
    {
        private readonly ILoggingService? _loggingService;
        private readonly IClipboardHistoryManager? _clipboardHistoryManager;

        /// <summary>
        /// Initialise une nouvelle instance de l'exécuteur.
        /// </summary>
        /// <param name="loggingService">Service de logging pour tracer les exécutions.</param>
        /// <param name="clipboardHistoryManager">Gestionnaire d'historique pour effectuer les suppressions.</param>
        public SupprimerToutExecutor(ILoggingService? loggingService = null, IClipboardHistoryManager? clipboardHistoryManager = null)
        {
            _loggingService = loggingService;
            _clipboardHistoryManager = clipboardHistoryManager;
        }

        /// <summary>
        /// Exécute la suppression des éléments selon l'analyse fournie.
        /// </summary>
        /// <param name="analysis">Analyse des éléments à supprimer.</param>
        /// <param name="operationId">ID de l'opération pour le logging.</param>
        /// <returns>Résultat de l'exécution.</returns>
        public async Task<SupprimerToutResult> ExecuteAsync(SupprimerToutAnalysis? analysis, string operationId)
        {
            try
            {
                _loggingService?.LogInfo($"Début de l'exécution de la suppression pour l'opération {operationId}");

                // Validation de base
                if (analysis == null)
                {
                    var errorMsg = "Analyse null - impossible d'exécuter la suppression";
                    _loggingService?.LogError($"[{operationId}] {errorMsg}");
                    return SupprimerToutResult.CreateFailure(operationId, errorMsg);
                }

                if (!ValidateExecution(analysis))
                {
                    var errorMsg = "Aucun élément à supprimer selon l'analyse";
                    _loggingService?.LogWarning($"[{operationId}] {errorMsg}");
                    return SupprimerToutResult.CreateFailure(operationId, errorMsg);
                }

                _loggingService?.LogInfo($"[{operationId}] Validation réussie - {analysis.ItemsToDelete} élément(s) à supprimer");

                // Exécution de la suppression
                var clearResult = await ClearHistoryAsync(analysis.PinnedItems > 0, operationId);

                if (!clearResult.Success)
                {
                    _loggingService?.LogError($"[{operationId}] Échec du nettoyage: {clearResult.ErrorMessage}");
                    return SupprimerToutResult.CreateFailure(operationId, clearResult.ErrorMessage ?? "Échec du nettoyage");
                }

                // Vérification post-exécution
                var actualItemsDeleted = await VerifyDeletionAsync(analysis, operationId);

                _loggingService?.LogInfo($"[{operationId}] Exécution réussie - {actualItemsDeleted} élément(s) supprimé(s)");
                return SupprimerToutResult.CreateSuccess(operationId, actualItemsDeleted);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Erreur lors de l'exécution: {ex.Message}");
                return SupprimerToutResult.CreateFailure(operationId, ex.Message, ex);
            }
        }

        /// <summary>
        /// Effectue le nettoyage de l'historique.
        /// </summary>
        /// <param name="preservePinned">Indique si les éléments épinglés doivent être préservés.</param>
        /// <param name="operationId">ID de l'opération pour le logging.</param>
        /// <returns>Résultat de l'exécution du nettoyage.</returns>
        public async Task<ExecutionResult> ClearHistoryAsync(bool preservePinned, string operationId)
        {
            try
            {
                _loggingService?.LogInfo($"[{operationId}] Début du nettoyage de l'historique (préserver épinglés: {preservePinned})");

                if (_clipboardHistoryManager == null)
                {
                    var errorMsg = "ClipboardHistoryManager non disponible";
                    _loggingService?.LogError($"[{operationId}] {errorMsg}");
                    return ExecutionResult.Failure(errorMsg);
                }

                await _clipboardHistoryManager.ClearHistoryAsync(preservePinned);
                _loggingService?.LogInfo($"[{operationId}] Nettoyage de l'historique réussi");
                return ExecutionResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Erreur lors du nettoyage: {ex.Message}");
                return ExecutionResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// Valide si l'exécution peut avoir lieu selon l'analyse.
        /// </summary>
        /// <param name="analysis">Analyse à valider.</param>
        /// <returns>True si l'exécution peut avoir lieu, false sinon.</returns>
        public bool ValidateExecution(SupprimerToutAnalysis? analysis)
        {
            if (analysis == null)
            {
                _loggingService?.LogWarning("Validation d'exécution échouée - analyse null");
                return false;
            }

            if (!analysis.HasItemsToDelete)
            {
                _loggingService?.LogInfo($"Validation d'exécution échouée - aucun élément à supprimer (total: {analysis.TotalItems}, épinglés: {analysis.PinnedItems})");
                return false;
            }

            _loggingService?.LogInfo($"Validation d'exécution réussie - {analysis.ItemsToDelete} élément(s) à supprimer");
            return true;
        }

        /// <summary>
        /// Vérifie le résultat de la suppression en comptant les éléments restants.
        /// </summary>
        /// <param name="analysis">Analyse originale.</param>
        /// <param name="operationId">ID de l'opération.</param>
        /// <returns>Nombre d'éléments effectivement supprimés.</returns>
        private Task<int> VerifyDeletionAsync(SupprimerToutAnalysis analysis, string operationId)
        {
            try
            {
                if (_clipboardHistoryManager == null)
                {
                    _loggingService?.LogWarning($"[{operationId}] Impossible de vérifier la suppression - ClipboardHistoryManager non disponible");
                    return Task.FromResult(analysis.ItemsToDelete); // Assume success
                }

                var remainingItems = _clipboardHistoryManager.HistoryItems;
                var remainingCount = remainingItems?.Count ?? 0;

                // Calcul des éléments supprimés
                var expectedRemaining = analysis.PinnedItems;
                var actualDeleted = analysis.TotalItems - remainingCount;

                _loggingService?.LogInfo($"[{operationId}] Vérification post-suppression - " +
                                       $"Éléments restants: {remainingCount}, " +
                                       $"Attendu: {expectedRemaining}, " +
                                       $"Supprimés: {actualDeleted}");

                return Task.FromResult(Math.Max(0, actualDeleted));
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"[{operationId}] Erreur lors de la vérification: {ex.Message}");
                return Task.FromResult(analysis.ItemsToDelete); // Assume success if verification fails
            }
        }



        /// <summary>
        /// Effectue un nettoyage d'urgence en cas d'échec partiel.
        /// </summary>
        /// <param name="operationId">ID de l'opération.</param>
        /// <returns>Résultat du nettoyage d'urgence.</returns>
        public async Task<ExecutionResult> EmergencyCleanupAsync(string operationId)
        {
            try
            {
                _loggingService?.LogWarning($"[{operationId}] Début du nettoyage d'urgence");

                if (_clipboardHistoryManager == null)
                {
                    return ExecutionResult.Failure("ClipboardHistoryManager non disponible pour le nettoyage d'urgence");
                }

                // Tentative de nettoyage complet en cas d'urgence
                await _clipboardHistoryManager.ClearHistoryAsync(false);
                _loggingService?.LogInfo($"[{operationId}] Nettoyage d'urgence réussi");
                return ExecutionResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"[{operationId}] Erreur lors du nettoyage d'urgence: {ex.Message}");
                return ExecutionResult.Failure(ex.Message);
            }
        }
    }
}
