using NUnit.Framework;
using ClipboardPlus.Modules.Core;
using System;
using ModuleState = ClipboardPlus.Modules.Core.ModuleState;

namespace ClipboardPlus.Tests.Unit.Modules.Core
{
    /// <summary>
    /// Tests fonctionnels pour ModuleDependencyException - vérifient le comportement métier
    /// dans des scénarios d'usage réels de gestion des dépendances entre modules
    /// </summary>
    [TestFixture]
    public class ModuleDependencyExceptionFunctionalTests
    {
        #region Scénarios métier de dépendances manquantes

        [Test]
        public void ModuleDependencyException_MissingDatabaseModule_ShouldPreventDataOperations()
        {
            // Arrange - Scénario : Module de base de données manquant empêchant opérations
            var moduleName = "HistoryModule";
            var moduleState = ModuleState.Initializing;
            var dependencyName = "DatabaseModule";
            var message = "Cannot initialize HistoryModule: DatabaseModule is required for clipboard data persistence but is not available.";

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message);

            // Assert - Vérifier que la dépendance DB manquante empêche les opérations
            Assert.That(exception.ModuleName, Is.EqualTo("HistoryModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Initializing));
            Assert.That(exception.DependencyName, Is.EqualTo("DatabaseModule"));
            Assert.That(exception.Message, Does.Contain("data persistence"));
            Assert.That(exception.Message, Does.Contain("not available"));
            Assert.That(exception.InnerException, Is.Null);
            
            // Vérifier l'héritage de ModuleException
            Assert.That(exception, Is.InstanceOf<ModuleException>());
            Assert.That(exception, Is.InstanceOf<Exception>());
        }

        [Test]
        public void ModuleDependencyException_MissingSecurityModule_ShouldPreventUnsafeOperations()
        {
            // Arrange - Scénario : Module de sécurité manquant empêchant opérations non sécurisées
            var moduleName = "NetworkSyncModule";
            var moduleState = ModuleState.Starting;
            var dependencyName = "SecurityModule";
            var message = "NetworkSyncModule cannot start without SecurityModule. Clipboard data encryption is mandatory for network operations.";
            var innerException = new InvalidOperationException("Security module initialization failed");

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message, innerException);

            // Assert - Vérifier que la sécurité manquante empêche les opérations réseau
            Assert.That(exception.ModuleName, Is.EqualTo("NetworkSyncModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Starting));
            Assert.That(exception.DependencyName, Is.EqualTo("SecurityModule"));
            Assert.That(exception.Message, Does.Contain("encryption is mandatory"));
            Assert.That(exception.Message, Does.Contain("network operations"));
            Assert.That(exception.InnerException, Is.InstanceOf<InvalidOperationException>());
        }

        [Test]
        public void ModuleDependencyException_MissingUIModule_ShouldPreventUserInteraction()
        {
            // Arrange - Scénario : Module UI manquant empêchant interaction utilisateur
            var moduleName = "CommandModule";
            var moduleState = ModuleState.Running;
            var dependencyName = "UIModule";
            var message = "CommandModule requires UIModule for user command execution. User interface components are not available.";

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message);

            // Assert - Vérifier que l'UI manquante empêche l'interaction utilisateur
            Assert.That(exception.ModuleName, Is.EqualTo("CommandModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Running));
            Assert.That(exception.DependencyName, Is.EqualTo("UIModule"));
            Assert.That(exception.Message, Does.Contain("user command execution"));
            Assert.That(exception.Message, Does.Contain("not available"));
        }

        [Test]
        public void ModuleDependencyException_MissingLoggingModule_ShouldPreventDiagnostics()
        {
            // Arrange - Scénario : Module de logging manquant empêchant diagnostics
            var moduleName = "DiagnosticsModule";
            var moduleState = ModuleState.Initialized;
            var dependencyName = "LoggingModule";
            var message = "DiagnosticsModule cannot function without LoggingModule. System monitoring and error tracking will be disabled.";

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message);

            // Assert - Vérifier que le logging manquant empêche les diagnostics
            Assert.That(exception.ModuleName, Is.EqualTo("DiagnosticsModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Initialized));
            Assert.That(exception.DependencyName, Is.EqualTo("LoggingModule"));
            Assert.That(exception.Message, Does.Contain("monitoring"));
            Assert.That(exception.Message, Does.Contain("error tracking"));
            Assert.That(exception.Message, Does.Contain("disabled"));
        }

        #endregion

        #region Scénarios de dépendances circulaires

        [Test]
        public void ModuleDependencyException_CircularDependency_ShouldDetectInfiniteLoop()
        {
            // Arrange - Scénario : Dépendance circulaire détectée
            var moduleName = "ModuleA";
            var moduleState = ModuleState.Initializing;
            var dependencyName = "ModuleB";
            var message = "Circular dependency detected: ModuleA depends on ModuleB, but ModuleB also depends on ModuleA. This creates an infinite initialization loop.";

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message);

            // Assert - Vérifier que la dépendance circulaire est détectée
            Assert.That(exception.ModuleName, Is.EqualTo("ModuleA"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Initializing));
            Assert.That(exception.DependencyName, Is.EqualTo("ModuleB"));
            Assert.That(exception.Message, Does.Contain("Circular dependency"));
            Assert.That(exception.Message, Does.Contain("infinite initialization loop"));
        }

        [Test]
        public void ModuleDependencyException_ComplexCircularDependency_ShouldDetectChainLoop()
        {
            // Arrange - Scénario : Dépendance circulaire complexe (A->B->C->A)
            var moduleName = "ClipboardModule";
            var moduleState = ModuleState.Starting;
            var dependencyName = "HistoryModule";
            var message = "Complex circular dependency chain detected: ClipboardModule -> HistoryModule -> SecurityModule -> ClipboardModule. Dependency resolution failed.";
            var innerException = new StackOverflowException("Dependency resolution stack overflow");

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message, innerException);

            // Assert - Vérifier que la chaîne circulaire complexe est détectée
            Assert.That(exception.ModuleName, Is.EqualTo("ClipboardModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Starting));
            Assert.That(exception.DependencyName, Is.EqualTo("HistoryModule"));
            Assert.That(exception.Message, Does.Contain("Complex circular dependency"));
            Assert.That(exception.Message, Does.Contain("resolution failed"));
            Assert.That(exception.InnerException, Is.InstanceOf<StackOverflowException>());
        }

        #endregion

        #region Scénarios de versions incompatibles

        [Test]
        public void ModuleDependencyException_IncompatibleVersion_ShouldPreventVersionConflict()
        {
            // Arrange - Scénario : Version de dépendance incompatible
            var moduleName = "ModernUIModule";
            var moduleState = ModuleState.Created;
            var dependencyName = "CoreModule";
            var message = "ModernUIModule requires CoreModule v2.0 or higher, but found v1.5. API compatibility cannot be guaranteed.";

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message);

            // Assert - Vérifier que l'incompatibilité de version est détectée
            Assert.That(exception.ModuleName, Is.EqualTo("ModernUIModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Created));
            Assert.That(exception.DependencyName, Is.EqualTo("CoreModule"));
            Assert.That(exception.Message, Does.Contain("v2.0 or higher"));
            Assert.That(exception.Message, Does.Contain("found v1.5"));
            Assert.That(exception.Message, Does.Contain("API compatibility"));
        }

        [Test]
        public void ModuleDependencyException_DeprecatedDependency_ShouldWarnAboutObsolescence()
        {
            // Arrange - Scénario : Dépendance obsolète utilisée
            var moduleName = "LegacyIntegrationModule";
            var moduleState = ModuleState.Running;
            var dependencyName = "OldDataModule";
            var message = "LegacyIntegrationModule depends on deprecated OldDataModule v1.0. This module will be removed in the next major version.";

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message);

            // Assert - Vérifier que l'obsolescence est signalée
            Assert.That(exception.ModuleName, Is.EqualTo("LegacyIntegrationModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Running));
            Assert.That(exception.DependencyName, Is.EqualTo("OldDataModule"));
            Assert.That(exception.Message, Does.Contain("deprecated"));
            Assert.That(exception.Message, Does.Contain("removed in the next major version"));
        }

        #endregion

        #region Scénarios de récupération de dépendances

        [Test]
        public void ModuleDependencyException_OptionalDependencyMissing_ShouldEnableDegradedMode()
        {
            // Arrange - Scénario : Dépendance optionnelle manquante permettant mode dégradé
            var moduleName = "AdvancedFeaturesModule";
            var moduleState = ModuleState.Starting;
            var dependencyName = "CloudSyncModule";
            var message = "Optional dependency CloudSyncModule is not available. AdvancedFeaturesModule will run in offline mode with reduced functionality.";

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message);

            // Assert - Vérifier que le mode dégradé est activé
            Assert.That(exception.ModuleName, Is.EqualTo("AdvancedFeaturesModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Starting));
            Assert.That(exception.DependencyName, Is.EqualTo("CloudSyncModule"));
            Assert.That(exception.Message, Does.Contain("Optional dependency"));
            Assert.That(exception.Message, Does.Contain("offline mode"));
            Assert.That(exception.Message, Does.Contain("reduced functionality"));
        }

        [Test]
        public void ModuleDependencyException_FallbackDependencyAvailable_ShouldUseFallback()
        {
            // Arrange - Scénario : Dépendance principale manquante mais fallback disponible
            var moduleName = "DataStorageModule";
            var moduleState = ModuleState.Initializing;
            var dependencyName = "SQLServerModule";
            var message = "Primary dependency SQLServerModule is unavailable. Falling back to SQLiteModule for local data storage.";

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message);

            // Assert - Vérifier que le fallback est utilisé
            Assert.That(exception.ModuleName, Is.EqualTo("DataStorageModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Initializing));
            Assert.That(exception.DependencyName, Is.EqualTo("SQLServerModule"));
            Assert.That(exception.Message, Does.Contain("Primary dependency"));
            Assert.That(exception.Message, Does.Contain("Falling back to SQLiteModule"));
        }

        #endregion

        #region Scénarios de diagnostic de dépendances

        [Test]
        public void ModuleDependencyException_DependencyLoadFailure_ShouldProvideLoadDiagnostics()
        {
            // Arrange - Scénario : Échec de chargement de dépendance avec diagnostics
            var moduleName = "MainApplicationModule";
            var moduleState = ModuleState.Error;
            var dependencyName = "CriticalServiceModule";
            var message = "Failed to load CriticalServiceModule: assembly not found in expected location. Check installation integrity.";
            var innerException = new System.IO.FileNotFoundException("Could not load file or assembly 'CriticalServiceModule.dll'");

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message, innerException);

            // Assert - Vérifier que les diagnostics de chargement sont fournis
            Assert.That(exception.ModuleName, Is.EqualTo("MainApplicationModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Error));
            Assert.That(exception.DependencyName, Is.EqualTo("CriticalServiceModule"));
            Assert.That(exception.Message, Does.Contain("assembly not found"));
            Assert.That(exception.Message, Does.Contain("installation integrity"));
            Assert.That(exception.InnerException, Is.InstanceOf<System.IO.FileNotFoundException>());
        }

        [Test]
        public void ModuleDependencyException_DependencyInitializationTimeout_ShouldDetectHangingDependency()
        {
            // Arrange - Scénario : Timeout d'initialisation de dépendance
            var moduleName = "TimeoutSensitiveModule";
            var moduleState = ModuleState.Initializing;
            var dependencyName = "SlowInitializationModule";
            var message = "Dependency SlowInitializationModule failed to initialize within timeout period (30 seconds). Module may be hanging or experiencing performance issues.";
            var innerException = new TimeoutException("Module initialization timeout exceeded");

            // Act
            var exception = new ModuleDependencyException(moduleName, moduleState, dependencyName, message, innerException);

            // Assert - Vérifier que le timeout de dépendance est détecté
            Assert.That(exception.ModuleName, Is.EqualTo("TimeoutSensitiveModule"));
            Assert.That(exception.ModuleState, Is.EqualTo(ModuleState.Initializing));
            Assert.That(exception.DependencyName, Is.EqualTo("SlowInitializationModule"));
            Assert.That(exception.Message, Does.Contain("timeout period"));
            Assert.That(exception.Message, Does.Contain("hanging"));
            Assert.That(exception.Message, Does.Contain("performance issues"));
            Assert.That(exception.InnerException, Is.InstanceOf<TimeoutException>());
        }

        #endregion
    }
}
