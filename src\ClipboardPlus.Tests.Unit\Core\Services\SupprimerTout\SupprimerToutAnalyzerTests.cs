using System;
using System.Collections.Generic;
using System.Linq;
using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.SupprimerTout;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.SupprimerTout
{
    /// <summary>
    /// Tests SIMPLES pour SupprimerToutAnalyzer.
    /// Approche progressive et sécurisée.
    /// </summary>
    [TestFixture]
    public class SupprimerToutAnalyzerTests
    {
        private SupprimerToutAnalyzer? _analyzer;
        private Mock<ILoggingService>? _mockLoggingService;

        [SetUp]
        public void SetUp()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _analyzer = new SupprimerToutAnalyzer(_mockLoggingService.Object);
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithLoggingService_ShouldCreateSuccessfully()
        {
            // Act & Assert
            Assert.That(_analyzer, Is.Not.Null);
        }

        [Test]
        public void Constructor_WithNullLoggingService_ShouldNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => new SupprimerToutAnalyzer(null));
        }

        #endregion

        #region AnalyzeItems Tests

        [Test]
        public void AnalyzeItems_WithNullCollection_ShouldReturnEmptyAnalysis()
        {
            // Act
            var result = _analyzer!.AnalyzeItems(null!);

            // Assert
            Assert.That(result.TotalItems, Is.EqualTo(0));
            Assert.That(result.PinnedItems, Is.EqualTo(0));
            Assert.That(result.ItemsToDelete, Is.EqualTo(0));
            Assert.That(result.HasItemsToDelete, Is.False);
        }

        [Test]
        public void AnalyzeItems_WithEmptyCollection_ShouldReturnEmptyAnalysis()
        {
            // Arrange
            var items = new List<ClipboardItem>();

            // Act
            var result = _analyzer!.AnalyzeItems(items);

            // Assert
            Assert.That(result.TotalItems, Is.EqualTo(0));
            Assert.That(result.PinnedItems, Is.EqualTo(0));
            Assert.That(result.ItemsToDelete, Is.EqualTo(0));
            Assert.That(result.HasItemsToDelete, Is.False);
        }

        [Test]
        public void AnalyzeItems_WithMixedItems_ShouldCalculateCorrectly()
        {
            // Arrange
            var items = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, IsPinned = false },
                new ClipboardItem { Id = 2, IsPinned = true },
                new ClipboardItem { Id = 3, IsPinned = false }
            };

            // Act
            var result = _analyzer!.AnalyzeItems(items, preservePinned: true);

            // Assert
            Assert.That(result.TotalItems, Is.EqualTo(3));
            Assert.That(result.PinnedItems, Is.EqualTo(1));
            Assert.That(result.ItemsToDelete, Is.EqualTo(2));
            Assert.That(result.HasItemsToDelete, Is.True);
        }

        [Test]
        public void AnalyzeItems_WithPreservePinnedFalse_ShouldDeleteAllItems()
        {
            // Arrange
            var items = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, IsPinned = false },
                new ClipboardItem { Id = 2, IsPinned = true }
            };

            // Act
            var result = _analyzer!.AnalyzeItems(items, preservePinned: false);

            // Assert
            Assert.That(result.TotalItems, Is.EqualTo(2));
            Assert.That(result.PinnedItems, Is.EqualTo(0));
            Assert.That(result.ItemsToDelete, Is.EqualTo(2));
            Assert.That(result.HasItemsToDelete, Is.True);
        }

        #endregion

        #region HasItemsToDelete Tests

        [Test]
        public void HasItemsToDelete_WithNullCollection_ShouldReturnFalse()
        {
            // Act
            var result = _analyzer!.HasItemsToDelete(null!);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void HasItemsToDelete_WithEmptyCollection_ShouldReturnFalse()
        {
            // Arrange
            var items = new List<ClipboardItem>();

            // Act
            var result = _analyzer!.HasItemsToDelete(items);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void HasItemsToDelete_WithUnpinnedItems_ShouldReturnTrue()
        {
            // Arrange
            var items = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, IsPinned = false }
            };

            // Act
            var result = _analyzer!.HasItemsToDelete(items);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void HasItemsToDelete_WithOnlyPinnedItems_ShouldReturnFalse()
        {
            // Arrange
            var items = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, IsPinned = true }
            };

            // Act
            var result = _analyzer!.HasItemsToDelete(items, preservePinned: true);

            // Assert
            Assert.That(result, Is.False);
        }

        #endregion

        // SUPPRIMÉ : Tests de ValidateAnalysis - méthode de validation supprimée
        // Les tests de validation ont été supprimés car ValidateAnalysis était du code mort

        // SUPPRIMÉ : Tests de GenerateAnalysisReport - méthode de debugging supprimée
        // Les tests de rapport ont été supprimés car GenerateAnalysisReport était du code mort

        #region Performance Tests

        [Test]
        public void AnalyzeItems_ShouldCompleteQuickly()
        {
            // Arrange
            var items = Enumerable.Range(1, 1000)
                .Select(i => new ClipboardItem { Id = i, IsPinned = i % 3 == 0 })
                .ToList();

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var result = _analyzer!.AnalyzeItems(items);

            // Assert
            stopwatch.Stop();
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(100),
                "L'analyse de 1000 éléments devrait être rapide");

            // Vérifier que le résultat est correct
            Assert.That(result.TotalItems, Is.EqualTo(1000));
        }

        [Test]
        public void HasItemsToDelete_ShouldCompleteQuickly()
        {
            // Arrange
            var items = Enumerable.Range(1, 1000)
                .Select(i => new ClipboardItem { Id = i, IsPinned = true })
                .ToList();

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var result = _analyzer!.HasItemsToDelete(items);

            // Assert
            stopwatch.Stop();
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(50),
                "La vérification rapide devrait être très rapide");
            Assert.That(result, Is.False); // Tous épinglés, rien à supprimer
        }

        #endregion

        #region Integration Tests

        [Test]
        public void Analyzer_Integration_ShouldWorkWithModels()
        {
            // Arrange
            var items = new List<ClipboardItem>
            {
                new ClipboardItem { Id = 1, IsPinned = false },
                new ClipboardItem { Id = 2, IsPinned = true },
                new ClipboardItem { Id = 3, IsPinned = false }
            };

            // Act
            var analysis = _analyzer!.AnalyzeItems(items, preservePinned: true, "test123");
            var hasItems = _analyzer!.HasItemsToDelete(items);
            // SUPPRIMÉ : ValidateAnalysis - méthode de validation supprimée
            // SUPPRIMÉ : GenerateAnalysisReport - méthode de debugging supprimée

            // Assert
            Assert.That(analysis, Is.Not.Null);
            Assert.That(hasItems, Is.True);
            // SUPPRIMÉ : Test de validation - méthode supprimée
            // SUPPRIMÉ : Test du rapport - méthode supprimée

            TestContext.WriteLine("✅ Analyseur fonctionne avec les modèles");
        }

        #endregion
    }
}
