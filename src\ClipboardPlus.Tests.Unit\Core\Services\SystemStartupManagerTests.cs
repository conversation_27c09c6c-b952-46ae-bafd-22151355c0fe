using NUnit.Framework;
using ClipboardPlus.Core.Services;
using Moq;
using System;
using System.Reflection;

namespace ClipboardPlus.Tests.Unit.Core.Services
{
    [TestFixture]
    public class SystemStartupManagerTests
    {
        // Note : Les tests pour SystemStartupManager sont limités car il interagit directement
        // avec le Registre Windows et WpfApplication.Current, qui sont difficiles à mocker
        // sans modifier le code source pour l'injection de dépendances.
        // Ces tests se concentrent sur la robustesse et le comportement dans des conditions limitées.

        [Test]
        public void Constructor_DoesNotThrow_WhenLoggingServiceIsUnavailable()
        {
            // Arrange & Act
            // Dans un environnement de test standard, WpfApplication.Current est souvent null,
            // donc _loggingService sera null dans SystemStartupManager.
            // Ce test vérifie que le constructeur ne lève pas d'exception dans ce cas.
            try
            {
                var manager = new SystemStartupManager();
                Assert.That(manager, Is.Not.Null, "Le constructeur devrait réussir.");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Le constructeur ne devrait pas lever d'exception, mais a levé {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void IsStartupEnabled_DoesNotThrow_DuringExecution()
        {
            // Arrange
            var manager = new SystemStartupManager();

            // Act & Assert
            // Ce test vérifie que la méthode IsStartupEnabled peut être appelée sans lever d'exception,
            // indépendamment du fait que la clé de registre existe ou non.
            try
            {
                bool result = manager.IsStartupEnabled();
                // Aucune assertion sur la valeur de 'result' car elle dépend de l'état de la machine.
                // Le simple fait que la méthode s'exécute sans exception est le test ici.
            }
            catch (Exception ex)
            {
                Assert.Fail($"IsStartupEnabled ne devrait pas lever d'exception, mais a levé {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void SetStartupEnabled_DoesNotThrow_DuringExecution()
        {
            // Arrange
            var manager = new SystemStartupManager();

            // Act & Assert pour enable=true
            // Ce test vérifie que SetStartupEnabled(true) peut être appelée sans lever d'exception.
            try
            {
                bool resultEnable = manager.SetStartupEnabled(true);
                // Aucune assertion sur la valeur de 'resultEnable'.
            }
            catch (Exception ex)
            {
                Assert.Fail($"SetStartupEnabled(true) ne devrait pas lever d'exception, mais a levé {ex.GetType().Name}: {ex.Message}");
            }

            // Act & Assert pour enable=false
            // Ce test vérifie que SetStartupEnabled(false) peut être appelée sans lever d'exception.
            try
            {
                bool resultDisable = manager.SetStartupEnabled(false);
                // Aucune assertion sur la valeur de 'resultDisable'.
            }
            catch (Exception ex)
            {
                Assert.Fail($"SetStartupEnabled(false) ne devrait pas lever d'exception, mais a levé {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void SyncStartupWithSettings_DoesNotThrow_WhenInternalCallsAreHandledGracefully()
        {
            // Arrange
            var manager = new SystemStartupManager();

            // Act & Assert
            // Ce test vérifie que SyncStartupWithSettings ne lève pas d'exception,
            // en supposant que IsStartupEnabled et SetStartupEnabled gèrent leurs propres erreurs.
            try
            {
                manager.SyncStartupWithSettings(true);  // Tente d'activer
                manager.SyncStartupWithSettings(false); // Tente de désactiver
            }
            catch (Exception ex)
            {
                Assert.Fail($"SyncStartupWithSettings ne devrait pas lever d'exception, mais a levé {ex.GetType().Name}: {ex.Message}");
            }
        }

        // Helper pour obtenir le chemin d'exécutable attendu (ne sera pas utilisé pour SetStartupEnabled directement dans ces tests
        // car on ne mocke pas le registre, mais utile pour comprendre la logique interne).
        private string GetExpectedExePath()
        {
            var exePath = Assembly.GetEntryAssembly()?.Location;
            if (string.IsNullOrEmpty(exePath))
            {
                return string.Empty; // ou gérer l'erreur comme le fait le code source
            }
            if (exePath.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
            {
                exePath = System.IO.Path.ChangeExtension(exePath, ".exe");
            }
            return exePath;
        }
    }
} 