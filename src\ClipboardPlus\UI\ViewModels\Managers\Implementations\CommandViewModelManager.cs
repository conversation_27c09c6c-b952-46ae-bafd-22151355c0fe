// RÉACTIVÉ : Implémentation de la logique de wrapping pour résoudre les problèmes de types
// ============================================================================
// COMMAND VIEWMODEL MANAGER IMPLEMENTATION - PHASE 6C
// ============================================================================
//
// 🎯 OBJECTIF : Implémentation concrète de la gestion des commandes
// 📋 RESPONSABILITÉ : Délégation vers CommandModule + gestion des 8 commandes
// 🏗️ ARCHITECTURE : Réutilisation du CommandModule existant
//
// ============================================================================

using System;
using System.Diagnostics;
using System.Windows.Input;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Modules.Commands;
using CommunityToolkit.Mvvm.Input;
using ClipboardPlus.UI.ViewModels.Managers.Interfaces;
using CommunityToolkit.Mvvm.ComponentModel;
using ViewModelCommandExecutingEventArgs = ClipboardPlus.UI.ViewModels.Managers.Interfaces.CommandExecutingEventArgs;
using ViewModelCommandExecutedEventArgs = ClipboardPlus.UI.ViewModels.Managers.Interfaces.CommandExecutedEventArgs;
using ViewModelCommandFailedEventArgs = ClipboardPlus.UI.ViewModels.Managers.Interfaces.CommandFailedEventArgs;

namespace ClipboardPlus.UI.ViewModels.Managers.Implementations
{
    /// <summary>
    /// Implémentation concrète du manager de gestion des commandes.
    /// 
    /// Cette classe délègue les opérations vers le CommandModule existant
    /// et gère les 8 commandes principales du ViewModel.
    /// </summary>
    public class CommandViewModelManager : ObservableObject, ICommandViewModelManager
    {
        #region Champs Privés

        private readonly ICommandModule _commandModule;
        private ClipboardItem? _selectedItem;
        private bool _areCommandsEnabled = true;
        private int _totalItemCount;
        private int _pinnedItemCount;
        private bool _isDisposed;

        #endregion

        #region Constructeur

        /// <summary>
        /// Initialise une nouvelle instance du CommandViewModelManager.
        /// </summary>
        /// <param name="commandModule">Module de commandes à utiliser</param>
        public CommandViewModelManager(ICommandModule commandModule)
        {
            _commandModule = commandModule ?? throw new ArgumentNullException(nameof(commandModule));
            
            // Initialiser les commandes
            InitializeCommands();
        }

        #endregion

        #region Commandes Principales (ICommandViewModelManager)

        /// <summary>
        /// Commande pour coller l'élément sélectionné dans l'application active.
        /// </summary>
        public IRelayCommand PasteSelectedItemCommand { get; private set; } = null!;

        /// <summary>
        /// Commande pour basculer l'état d'épinglage de l'élément sélectionné.
        /// </summary>
        public IAsyncRelayCommand<ClipboardItem> BasculerEpinglageCommand { get; private set; } = null!;

        /// <summary>
        /// Commande pour supprimer l'élément sélectionné.
        /// </summary>
        public IRelayCommand<ClipboardItem> SupprimerElementCommand { get; private set; } = null!;

        /// <summary>
        /// Commande pour supprimer l'élément sélectionné (version 2).
        /// </summary>
        public IRelayCommand<ClipboardItem> SupprimerElementCommand_V2 { get; private set; } = null!;

        /// <summary>
        /// Commande pour supprimer tous les éléments non épinglés.
        /// </summary>
        public IAsyncRelayCommand SupprimerToutCommand { get; private set; } = null!;

        /// <summary>
        /// Commande pour afficher la prévisualisation de l'élément sélectionné.
        /// </summary>
        public IRelayCommand<ClipboardItem> AfficherPreviewCommand { get; private set; } = null!;

        /// <summary>
        /// Commande pour ouvrir la fenêtre de nettoyage avancé.
        /// </summary>
        public IAsyncRelayCommand OpenAdvancedCleanupCommand { get; private set; } = null!;

        /// <summary>
        /// Commande pour ouvrir la fenêtre des paramètres.
        /// </summary>
        public IAsyncRelayCommand OpenSettingsCommand { get; private set; } = null!;

        #endregion

        #region Propriétés de Contexte (ICommandViewModelManager)

        /// <summary>
        /// Élément actuellement sélectionné pour les commandes.
        /// </summary>
        public ClipboardItem? SelectedItem
        {
            get => _selectedItem;
            set
            {
                if (SetProperty(ref _selectedItem, value))
                {
                    InvalidateAllCommands();
                }
            }
        }

        /// <summary>
        /// Indique si les commandes sont actuellement disponibles.
        /// </summary>
        public bool AreCommandsEnabled
        {
            get => _areCommandsEnabled;
            set
            {
                if (SetProperty(ref _areCommandsEnabled, value))
                {
                    InvalidateAllCommands();
                }
            }
        }

        /// <summary>
        /// Nombre total d'éléments dans l'historique.
        /// </summary>
        public int TotalItemCount
        {
            get => _totalItemCount;
            set
            {
                if (SetProperty(ref _totalItemCount, value))
                {
                    InvalidateAllCommands();
                }
            }
        }

        /// <summary>
        /// Nombre d'éléments épinglés.
        /// </summary>
        public int PinnedItemCount
        {
            get => _pinnedItemCount;
            set
            {
                if (SetProperty(ref _pinnedItemCount, value))
                {
                    InvalidateAllCommands();
                }
            }
        }

        #endregion

        #region Événements (ICommandViewModelManager)

        /// <summary>
        /// Événement déclenché avant l'exécution d'une commande.
        /// </summary>
        public event EventHandler<ViewModelCommandExecutingEventArgs>? CommandExecuting;

        /// <summary>
        /// Événement déclenché après l'exécution réussie d'une commande.
        /// </summary>
        public event EventHandler<ViewModelCommandExecutedEventArgs>? CommandExecuted;

        /// <summary>
        /// Événement déclenché lorsqu'une commande échoue.
        /// </summary>
        public event EventHandler<ViewModelCommandFailedEventArgs>? CommandFailed;

        #endregion

        #region Méthodes de Gestion des Commandes (ICommandViewModelManager)

        /// <summary>
        /// Initialise toutes les commandes avec leurs implémentations.
        /// WRAPPING PATTERN : Crée des wrappers typés forts qui délèguent vers le CommandModule.
        /// </summary>
        public void InitializeCommands()
        {
            // === WRAPPING PATTERN : Délégation vers CommandModule ===

            // 1. PasteSelectedItemCommand : IRelayCommand (synchrone)
            PasteSelectedItemCommand = new RelayCommand(
                () => ExecuteCommandSafely(nameof(PasteSelectedItemCommand), null, () =>
                    _commandModule.PasteSelectedItemCommand.Execute(null)),
                () => _areCommandsEnabled && _commandModule.PasteSelectedItemCommand.CanExecute(null));

            // 2. BasculerEpinglageCommand : IAsyncRelayCommand<ClipboardItem> (asynchrone avec paramètre)
            BasculerEpinglageCommand = new AsyncRelayCommand<ClipboardItem>(
                async (item) => await ExecuteCommandSafelyAsync(nameof(BasculerEpinglageCommand), item, async () =>
                {
                    // Le CommandModule.TogglePinCommand est AsyncRelayCommand<ClipboardItem>
                    if (_commandModule.TogglePinCommand.CanExecute(item))
                    {
                        _commandModule.TogglePinCommand.Execute(item);
                        // Note: L'exécution ICommand est synchrone, mais déclenche une opération async dans le module
                    }
                }),
                (item) => _areCommandsEnabled && item != null && _commandModule.TogglePinCommand.CanExecute(item));

            // 3. SupprimerElementCommand : IRelayCommand<ClipboardItem> (synchrone avec paramètre)
            SupprimerElementCommand = new RelayCommand<ClipboardItem>(
                (item) => ExecuteCommandSafely(nameof(SupprimerElementCommand), item, () =>
                    _commandModule.DeleteSelectedItemCommand.Execute(null)), // Le module utilise l'élément sélectionné du contexte
                (item) => _areCommandsEnabled && item != null && _commandModule.DeleteSelectedItemCommand.CanExecute(null));

            // 4. SupprimerElementCommand_V2 : IRelayCommand<ClipboardItem> (version 2, même délégation)
            SupprimerElementCommand_V2 = new RelayCommand<ClipboardItem>(
                (item) => ExecuteCommandSafely(nameof(SupprimerElementCommand_V2), item, () =>
                    _commandModule.DeleteSelectedItemCommand.Execute(null)), // Même commande sous-jacente
                (item) => _areCommandsEnabled && item != null && _commandModule.DeleteSelectedItemCommand.CanExecute(null));

            // 5. SupprimerToutCommand : IAsyncRelayCommand (asynchrone sans paramètre)
            SupprimerToutCommand = new AsyncRelayCommand(
                async () => await ExecuteCommandSafelyAsync(nameof(SupprimerToutCommand), null, async () =>
                {
                    if (_commandModule.ClearHistoryCommand.CanExecute(null))
                    {
                        _commandModule.ClearHistoryCommand.Execute(null);
                    }
                }),
                () => _areCommandsEnabled && _commandModule.ClearHistoryCommand.CanExecute(null));

            // 6. AfficherPreviewCommand : IRelayCommand<ClipboardItem> (fonctionnalité non implémentée dans le module)
            AfficherPreviewCommand = new RelayCommand<ClipboardItem>(
                (item) => ExecuteCommandSafely(nameof(AfficherPreviewCommand), item, () =>
                {
                    // Cette fonctionnalité n'existe pas encore dans le CommandModule
                    // Pour l'instant, on déclenche juste l'événement CommandExecuted
                }),
                (item) => _areCommandsEnabled && item != null);

            // 7. OpenAdvancedCleanupCommand : IAsyncRelayCommand (fonctionnalité non implémentée dans le module)
            OpenAdvancedCleanupCommand = new AsyncRelayCommand(
                async () => await ExecuteCommandSafelyAsync(nameof(OpenAdvancedCleanupCommand), null, async () =>
                {
                    // Cette fonctionnalité n'existe pas encore dans le CommandModule
                    // Pour l'instant, on déclenche juste l'événement CommandExecuted
                }),
                () => _areCommandsEnabled);

            // 8. OpenSettingsCommand : IAsyncRelayCommand (fonctionnalité non implémentée dans le module)
            OpenSettingsCommand = new AsyncRelayCommand(
                async () => await ExecuteCommandSafelyAsync(nameof(OpenSettingsCommand), null, async () =>
                {
                    // Cette fonctionnalité n'existe pas encore dans le CommandModule
                    // Pour l'instant, on déclenche juste l'événement CommandExecuted
                }),
                () => _areCommandsEnabled);
        }

        /// <summary>
        /// Invalide toutes les commandes pour forcer une réévaluation de CanExecute.
        /// </summary>
        public void InvalidateAllCommands()
        {
            if (_isDisposed) return;

            // Invalider toutes les commandes avec les bons types
            (PasteSelectedItemCommand as IRelayCommand)?.NotifyCanExecuteChanged();
            (BasculerEpinglageCommand as IRelayCommand)?.NotifyCanExecuteChanged();
            (SupprimerElementCommand as IRelayCommand)?.NotifyCanExecuteChanged();
            (SupprimerElementCommand_V2 as IRelayCommand)?.NotifyCanExecuteChanged();
            (SupprimerToutCommand as IRelayCommand)?.NotifyCanExecuteChanged();
            (AfficherPreviewCommand as IRelayCommand)?.NotifyCanExecuteChanged();
            (OpenAdvancedCleanupCommand as IRelayCommand)?.NotifyCanExecuteChanged();
            (OpenSettingsCommand as IRelayCommand)?.NotifyCanExecuteChanged();
        }

        /// <summary>
        /// Invalide une commande spécifique.
        /// </summary>
        /// <param name="commandName">Nom de la commande à invalider</param>
        public void InvalidateCommand(string commandName)
        {
            if (_isDisposed) return;

            var command = commandName switch
            {
                nameof(PasteSelectedItemCommand) => PasteSelectedItemCommand as IRelayCommand,
                nameof(BasculerEpinglageCommand) => BasculerEpinglageCommand as IRelayCommand,
                nameof(SupprimerElementCommand) => SupprimerElementCommand as IRelayCommand,
                nameof(SupprimerElementCommand_V2) => SupprimerElementCommand_V2 as IRelayCommand,
                nameof(SupprimerToutCommand) => SupprimerToutCommand as IRelayCommand,
                nameof(AfficherPreviewCommand) => AfficherPreviewCommand as IRelayCommand,
                nameof(OpenAdvancedCleanupCommand) => OpenAdvancedCleanupCommand as IRelayCommand,
                nameof(OpenSettingsCommand) => OpenSettingsCommand as IRelayCommand,
                _ => null
            };

            command?.NotifyCanExecuteChanged();
        }

        /// <summary>
        /// Met à jour le contexte des commandes.
        /// </summary>
        /// <param name="selectedItem">Élément sélectionné</param>
        /// <param name="totalCount">Nombre total d'éléments</param>
        /// <param name="pinnedCount">Nombre d'éléments épinglés</param>
        public void UpdateCommandContext(ClipboardItem? selectedItem, int totalCount, int pinnedCount)
        {
            SelectedItem = selectedItem;
            TotalItemCount = totalCount;
            PinnedItemCount = pinnedCount;
        }

        #endregion

        #region Méthodes d'Exécution des Commandes - SUPPRIMÉES

        // === ANCIENNES MÉTHODES D'EXÉCUTION SUPPRIMÉES ===
        // Les commandes utilisent maintenant le WRAPPING PATTERN avec délégation directe
        // dans InitializeCommands(). Plus besoin de méthodes d'exécution séparées.

        #endregion

        #region Méthodes de Validation (ICommandViewModelManager)

        public bool CanExecuteCommand(string commandName) => commandName switch
        {
            nameof(PasteSelectedItemCommand) => CanExecutePasteCommand(),
            nameof(BasculerEpinglageCommand) => CanExecuteTogglePinCommand(),
            nameof(SupprimerElementCommand) => CanExecuteDeleteCommand(),
            nameof(SupprimerElementCommand_V2) => CanExecuteDeleteCommand(),
            nameof(SupprimerToutCommand) => CanExecuteDeleteAllCommand(),
            nameof(AfficherPreviewCommand) => CanExecutePasteCommand(),
            nameof(OpenAdvancedCleanupCommand) => _areCommandsEnabled,
            nameof(OpenSettingsCommand) => _areCommandsEnabled,
            _ => false
        };

        public bool CanExecutePasteCommand() => _areCommandsEnabled && _selectedItem != null;
        public bool CanExecuteTogglePinCommand() => _areCommandsEnabled && _selectedItem != null;
        public bool CanExecuteDeleteCommand() => _areCommandsEnabled && _selectedItem != null;
        public bool CanExecuteDeleteAllCommand() => _areCommandsEnabled && _totalItemCount > _pinnedItemCount;

        public bool TryExecutePasteCommand()
        {
            if (CanExecutePasteCommand())
            {
                PasteSelectedItemCommand.Execute(null);
                return true;
            }
            return false;
        }

        public bool TryExecuteTogglePinCommand()
        {
            if (CanExecuteTogglePinCommand() && _selectedItem != null)
            {
                BasculerEpinglageCommand.Execute(_selectedItem);
                return true;
            }
            return false;
        }

        public bool TryExecuteDeleteCommand()
        {
            if (CanExecuteDeleteCommand() && _selectedItem != null)
            {
                SupprimerElementCommand.Execute(_selectedItem);
                return true;
            }
            return false;
        }

        public bool TryExecuteDeleteAllCommand()
        {
            if (CanExecuteDeleteAllCommand())
            {
                SupprimerToutCommand.Execute(null);
                return true;
            }
            return false;
        }

        #endregion

        #region Méthodes Utilitaires

        /// <summary>
        /// Exécute une commande de manière sécurisée avec gestion d'erreurs et événements.
        /// </summary>
        private void ExecuteCommandSafely(string commandName, object? parameter, Action action)
        {
            if (_isDisposed) return;

            var stopwatch = Stopwatch.StartNew();
            var executingArgs = new ViewModelCommandExecutingEventArgs(commandName, parameter);

            try
            {
                // Événement avant exécution
                CommandExecuting?.Invoke(this, executingArgs);
                if (executingArgs.Cancel) return;

                // Exécuter la commande
                action();

                // Événement après exécution réussie
                stopwatch.Stop();
                CommandExecuted?.Invoke(this, new ViewModelCommandExecutedEventArgs(
                    commandName, parameter, null, stopwatch.Elapsed));
            }
            catch (Exception ex)
            {
                // Événement en cas d'erreur
                stopwatch.Stop();
                CommandFailed?.Invoke(this, new ViewModelCommandFailedEventArgs(
                    commandName, parameter, ex, stopwatch.Elapsed));
            }
        }

        /// <summary>
        /// Exécute une commande asynchrone de manière sécurisée avec gestion d'erreurs et événements.
        /// </summary>
        private async Task ExecuteCommandSafelyAsync(string commandName, object? parameter, Func<Task> action)
        {
            if (_isDisposed) return;

            var stopwatch = Stopwatch.StartNew();
            var executingArgs = new ViewModelCommandExecutingEventArgs(commandName, parameter);

            try
            {
                // Événement avant exécution
                CommandExecuting?.Invoke(this, executingArgs);
                if (executingArgs.Cancel) return;

                // Exécuter la commande asynchrone
                await action();

                // Événement après exécution réussie
                stopwatch.Stop();
                CommandExecuted?.Invoke(this, new ViewModelCommandExecutedEventArgs(
                    commandName, parameter, null, stopwatch.Elapsed));
            }
            catch (Exception ex)
            {
                // Événement en cas d'erreur
                stopwatch.Stop();
                CommandFailed?.Invoke(this, new ViewModelCommandFailedEventArgs(
                    commandName, parameter, ex, stopwatch.Elapsed));
            }
        }

        #endregion

        #region Méthodes d'Initialisation et Nettoyage (ICommandViewModelManager)

        public void Initialize()
        {
            // Les commandes sont déjà initialisées dans le constructeur
            // Cette méthode peut être utilisée pour une réinitialisation si nécessaire
            if (_isDisposed) return;
        }

        public void Cleanup()
        {
            if (_isDisposed) return;

            _selectedItem = null;
            _areCommandsEnabled = false;
            _totalItemCount = 0;
            _pinnedItemCount = 0;
        }

        public void Dispose()
        {
            if (_isDisposed) return;

            Cleanup();
            _isDisposed = true;
        }

        #endregion
    }
}
