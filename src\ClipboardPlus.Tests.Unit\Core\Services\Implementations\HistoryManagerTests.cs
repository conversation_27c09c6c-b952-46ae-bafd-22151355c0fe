using NUnit.Framework;
using Moq;
using ClipboardPlus.Core.Services.Implementations;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Unit.Core.Services.Implementations
{
    [TestFixture]
    public class HistoryManagerTests
    {
        private Mock<IPersistenceService> _mockPersistenceService = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private HistoryManager _historyManager = null!;

        [SetUp]
        public void SetUp()
        {
            _mockPersistenceService = new Mock<IPersistenceService>();
            _mockLoggingService = new Mock<ILoggingService>();
            _historyManager = new HistoryManager(_mockPersistenceService.Object, _mockLoggingService.Object);
        }

        #region Constructor Tests

        [Test]
        public void Constructor_WithValidParameters_CreatesInstance()
        {
            // Act & Assert
            Assert.That(_historyManager, Is.Not.Null);
            Assert.That(_historyManager.GetHistoryItems(), Is.Not.Null);
            Assert.That(_historyManager.GetHistoryItems().Count, Is.EqualTo(0));
        }

        [Test]
        public void Constructor_WithNullPersistenceService_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                new HistoryManager(null!, _mockLoggingService.Object));

            Assert.That(ex.ParamName, Is.EqualTo("persistenceService"));
        }

        [Test]
        public void Constructor_WithNullLoggingService_CreatesInstance()
        {
            // Act & Assert
            var manager = new HistoryManager(_mockPersistenceService.Object, null);
            Assert.That(manager, Is.Not.Null);
        }

        #endregion

        #region AddToHistory Tests

        [Test]
        public void AddToHistory_WithValidItem_AddsToFirstPosition()
        {
            // Arrange
            var item = new ClipboardItem
            {
                Id = 1,
                DataType = ClipboardDataType.Text,
                RawData = System.Text.Encoding.UTF8.GetBytes("Test content"),
                Timestamp = DateTime.Now
            };

            // Act
            _historyManager.AddToHistory(item);

            // Assert
            var historyItems = _historyManager.GetHistoryItems();
            Assert.That(historyItems.Count, Is.EqualTo(1));
            Assert.That(historyItems[0], Is.EqualTo(item));
        }

        [Test]
        public void AddToHistory_WithMultipleItems_AddsInCorrectOrder()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 1"), Timestamp = DateTime.Now.AddMinutes(-2) };
            var item2 = new ClipboardItem { Id = 2, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 2"), Timestamp = DateTime.Now.AddMinutes(-1) };
            var item3 = new ClipboardItem { Id = 3, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 3"), Timestamp = DateTime.Now };

            // Act
            _historyManager.AddToHistory(item1);
            _historyManager.AddToHistory(item2);
            _historyManager.AddToHistory(item3);

            // Assert
            var historyItems = _historyManager.GetHistoryItems();
            Assert.That(historyItems.Count, Is.EqualTo(3));
            Assert.That(historyItems[0], Is.EqualTo(item3)); // Le plus récent en premier
            Assert.That(historyItems[1], Is.EqualTo(item2));
            Assert.That(historyItems[2], Is.EqualTo(item1)); // Le plus ancien en dernier
        }

        [Test]
        public void AddToHistory_WithNullItem_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                _historyManager.AddToHistory(null!));

            Assert.That(ex.ParamName, Is.EqualTo("item"));
        }

        #endregion

        #region MoveToTop Tests

        [Test]
        public void MoveToTop_WithExistingItem_MovesToFirstPosition()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 1"), Timestamp = DateTime.Now.AddMinutes(-2) };
            var item2 = new ClipboardItem { Id = 2, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 2"), Timestamp = DateTime.Now.AddMinutes(-1) };
            var item3 = new ClipboardItem { Id = 3, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 3"), Timestamp = DateTime.Now };

            _historyManager.AddToHistory(item1);
            _historyManager.AddToHistory(item2);
            _historyManager.AddToHistory(item3);

            // Act - Déplacer item1 (qui est en position 2) vers le haut
            _historyManager.MoveToTop(item1);

            // Assert
            var historyItems = _historyManager.GetHistoryItems();
            Assert.That(historyItems.Count, Is.EqualTo(3));
            Assert.That(historyItems[0], Is.EqualTo(item1)); // item1 maintenant en premier
            Assert.That(historyItems[1], Is.EqualTo(item3));
            Assert.That(historyItems[2], Is.EqualTo(item2));
        }

        [Test]
        public void MoveToTop_WithItemAlreadyAtTop_DoesNothing()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 1"), Timestamp = DateTime.Now };
            var item2 = new ClipboardItem { Id = 2, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 2"), Timestamp = DateTime.Now.AddMinutes(-1) };

            _historyManager.AddToHistory(item2);
            _historyManager.AddToHistory(item1); // item1 est déjà en première position

            // Act
            _historyManager.MoveToTop(item1);

            // Assert
            var historyItems = _historyManager.GetHistoryItems();
            Assert.That(historyItems.Count, Is.EqualTo(2));
            Assert.That(historyItems[0], Is.EqualTo(item1)); // Toujours en première position
            Assert.That(historyItems[1], Is.EqualTo(item2));
        }

        [Test]
        public void MoveToTop_WithNonExistentItem_DoesNothing()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 1"), Timestamp = DateTime.Now };
            var item2 = new ClipboardItem { Id = 2, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 2"), Timestamp = DateTime.Now.AddMinutes(-1) };
            var nonExistentItem = new ClipboardItem { Id = 999, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Non-existent"), Timestamp = DateTime.Now };

            _historyManager.AddToHistory(item1);
            _historyManager.AddToHistory(item2);

            // Act
            _historyManager.MoveToTop(nonExistentItem);

            // Assert
            var historyItems = _historyManager.GetHistoryItems();
            Assert.That(historyItems.Count, Is.EqualTo(2));
            Assert.That(historyItems[0], Is.EqualTo(item2));
            Assert.That(historyItems[1], Is.EqualTo(item1));
        }

        [Test]
        public void MoveToTop_WithNullItem_ThrowsArgumentNullException()
        {
            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() =>
                _historyManager.MoveToTop(null!));

            Assert.That(ex.ParamName, Is.EqualTo("item"));
        }

        #endregion

        #region EnforceMaxHistoryItemsAsync Tests

        [Test]
        public async Task EnforceMaxHistoryItemsAsync_WithItemsUnderLimit_ReturnsZero()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 1"), Timestamp = DateTime.Now, IsPinned = false };
            var item2 = new ClipboardItem { Id = 2, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 2"), Timestamp = DateTime.Now.AddMinutes(-1), IsPinned = false };

            _historyManager.AddToHistory(item1);
            _historyManager.AddToHistory(item2);

            // Act
            var removedCount = await _historyManager.EnforceMaxHistoryItemsAsync(5);

            // Assert
            Assert.That(removedCount, Is.EqualTo(0));
            Assert.That(_historyManager.GetHistoryItems().Count, Is.EqualTo(2));
        }

        [Test]
        public async Task EnforceMaxHistoryItemsAsync_WithItemsOverLimit_RemovesOldestUnpinned()
        {
            // Arrange
            var oldestItem = new ClipboardItem { Id = 1, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Oldest"), Timestamp = DateTime.Now.AddMinutes(-10), IsPinned = false };
            var middleItem = new ClipboardItem { Id = 2, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Middle"), Timestamp = DateTime.Now.AddMinutes(-5), IsPinned = false };
            var newestItem = new ClipboardItem { Id = 3, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Newest"), Timestamp = DateTime.Now, IsPinned = false };

            _historyManager.AddToHistory(oldestItem);
            _historyManager.AddToHistory(middleItem);
            _historyManager.AddToHistory(newestItem);

            _mockPersistenceService.Setup(p => p.DeleteClipboardItemAsync(It.IsAny<long>()))
                .Returns(Task.FromResult(true));

            // Act
            var removedCount = await _historyManager.EnforceMaxHistoryItemsAsync(2);

            // Assert
            Assert.That(removedCount, Is.EqualTo(1));
            Assert.That(_historyManager.GetHistoryItems().Count, Is.EqualTo(2));
            
            var remainingItems = _historyManager.GetHistoryItems();
            Assert.That(remainingItems.Any(i => i.Id == 1), Is.False); // L'élément le plus ancien a été supprimé
            Assert.That(remainingItems.Any(i => i.Id == 2), Is.True);
            Assert.That(remainingItems.Any(i => i.Id == 3), Is.True);

            _mockPersistenceService.Verify(p => p.DeleteClipboardItemAsync(1), Times.Once);
        }

        [Test]
        public async Task EnforceMaxHistoryItemsAsync_WithPinnedItems_PreservesPinnedItems()
        {
            // Arrange
            var oldestPinned = new ClipboardItem { Id = 1, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Oldest Pinned"), Timestamp = DateTime.Now.AddMinutes(-10), IsPinned = true };
            var oldestUnpinned = new ClipboardItem { Id = 2, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Oldest Unpinned"), Timestamp = DateTime.Now.AddMinutes(-8), IsPinned = false };
            var newestItem = new ClipboardItem { Id = 3, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Newest"), Timestamp = DateTime.Now, IsPinned = false };

            _historyManager.AddToHistory(oldestPinned);
            _historyManager.AddToHistory(oldestUnpinned);
            _historyManager.AddToHistory(newestItem);

            _mockPersistenceService.Setup(p => p.DeleteClipboardItemAsync(It.IsAny<long>()))
                .Returns(Task.FromResult(true));

            // Act
            var removedCount = await _historyManager.EnforceMaxHistoryItemsAsync(2);

            // Assert
            Assert.That(removedCount, Is.EqualTo(1));
            Assert.That(_historyManager.GetHistoryItems().Count, Is.EqualTo(2));
            
            var remainingItems = _historyManager.GetHistoryItems();
            Assert.That(remainingItems.Any(i => i.Id == 1), Is.True); // L'élément épinglé est préservé
            Assert.That(remainingItems.Any(i => i.Id == 2), Is.False); // L'élément non épinglé le plus ancien a été supprimé
            Assert.That(remainingItems.Any(i => i.Id == 3), Is.True);

            _mockPersistenceService.Verify(p => p.DeleteClipboardItemAsync(2), Times.Once);
        }

        [Test]
        public async Task EnforceMaxHistoryItemsAsync_WithAllPinnedItems_ReturnsZero()
        {
            // Arrange
            var pinnedItem1 = new ClipboardItem { Id = 1, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Pinned 1"), Timestamp = DateTime.Now.AddMinutes(-10), IsPinned = true };
            var pinnedItem2 = new ClipboardItem { Id = 2, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Pinned 2"), Timestamp = DateTime.Now.AddMinutes(-5), IsPinned = true };
            var pinnedItem3 = new ClipboardItem { Id = 3, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Pinned 3"), Timestamp = DateTime.Now, IsPinned = true };

            _historyManager.AddToHistory(pinnedItem1);
            _historyManager.AddToHistory(pinnedItem2);
            _historyManager.AddToHistory(pinnedItem3);

            // Act
            var removedCount = await _historyManager.EnforceMaxHistoryItemsAsync(1);

            // Assert
            Assert.That(removedCount, Is.EqualTo(0));
            Assert.That(_historyManager.GetHistoryItems().Count, Is.EqualTo(3)); // Tous les éléments épinglés sont préservés

            _mockPersistenceService.Verify(p => p.DeleteClipboardItemAsync(It.IsAny<long>()), Times.Never);
        }

        [Test]
        public void EnforceMaxHistoryItemsAsync_WithNegativeMaxItems_ThrowsArgumentException()
        {
            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _historyManager.EnforceMaxHistoryItemsAsync(-1));

            Assert.That(ex!.ParamName, Is.EqualTo("maxItems"));
            Assert.That(ex!.Message, Does.Contain("négatif"));
        }

        [Test]
        public async Task EnforceMaxHistoryItemsAsync_WithDatabaseError_ContinuesWithOtherItems()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 1"), Timestamp = DateTime.Now.AddMinutes(-10), IsPinned = false };
            var item2 = new ClipboardItem { Id = 2, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 2"), Timestamp = DateTime.Now.AddMinutes(-8), IsPinned = false };
            var item3 = new ClipboardItem { Id = 3, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 3"), Timestamp = DateTime.Now, IsPinned = false };

            _historyManager.AddToHistory(item1);
            _historyManager.AddToHistory(item2);
            _historyManager.AddToHistory(item3);

            // Simuler une erreur pour le premier élément seulement
            _mockPersistenceService.Setup(p => p.DeleteClipboardItemAsync(1))
                .ThrowsAsync(new Exception("Database error"));
            _mockPersistenceService.Setup(p => p.DeleteClipboardItemAsync(2))
                .Returns(Task.FromResult(true));

            // Act
            var removedCount = await _historyManager.EnforceMaxHistoryItemsAsync(1);

            // Assert
            Assert.That(removedCount, Is.EqualTo(1)); // Un seul élément supprimé avec succès
            Assert.That(_historyManager.GetHistoryItems().Count, Is.EqualTo(2)); // item1 reste en mémoire à cause de l'erreur, item2 supprimé

            _mockPersistenceService.Verify(p => p.DeleteClipboardItemAsync(1), Times.Once);
            _mockPersistenceService.Verify(p => p.DeleteClipboardItemAsync(2), Times.Once);
        }

        #endregion

        #region GetHistoryItems Tests

        [Test]
        public void GetHistoryItems_WithEmptyHistory_ReturnsEmptyReadOnlyList()
        {
            // Act
            var historyItems = _historyManager.GetHistoryItems();

            // Assert
            Assert.That(historyItems, Is.Not.Null);
            Assert.That(historyItems.Count, Is.EqualTo(0));
            Assert.That(historyItems, Is.InstanceOf<IReadOnlyList<ClipboardItem>>());
        }

        [Test]
        public void GetHistoryItems_WithItems_ReturnsReadOnlyList()
        {
            // Arrange
            var item1 = new ClipboardItem { Id = 1, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 1"), Timestamp = DateTime.Now };
            var item2 = new ClipboardItem { Id = 2, DataType = ClipboardDataType.Text, RawData = System.Text.Encoding.UTF8.GetBytes("Item 2"), Timestamp = DateTime.Now.AddMinutes(-1) };

            _historyManager.AddToHistory(item1);
            _historyManager.AddToHistory(item2);

            // Act
            var historyItems = _historyManager.GetHistoryItems();

            // Assert
            Assert.That(historyItems, Is.Not.Null);
            Assert.That(historyItems.Count, Is.EqualTo(2));
            Assert.That(historyItems, Is.InstanceOf<IReadOnlyList<ClipboardItem>>());
            Assert.That(historyItems[0], Is.EqualTo(item2)); // Le plus récent en premier
            Assert.That(historyItems[1], Is.EqualTo(item1));
        }

        #endregion
    }
}
